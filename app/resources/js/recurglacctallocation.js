var radioAllocationIdValue = 'Individual allocation ID';

function accountRunOnLoad() {
    var allocGrpFld = window.editor.view.findComponents('GLACCTALLOCATIONGRP', 'Field')[0];
    var allocIdFld = window.editor.view.findComponents('GLACCTALLOCATION', 'Field')[0];
    var allocRunTypeFld = window.editor.view.findComponents('ALLOCATIONRUNTYPE', 'Field')[0];
    var isPeriodEnd = window.editor.view.findComponents('ISPERIODEND', 'Field')[0];
    //In view mode, allocation ID and grid
    if (view.state == 'showview') {
        var allocationRunType = allocRunTypeFld.getValue();
        hideAllocationType(allocationRunType);
        if (allocationRunType == radioAllocationIdValue) {
            getAllocationGrpMembers(allocIdFld.getValue(), 'MEMBER');
        } else {
            getAllocationGrpMembers(allocGrpFld.getValue(), 'GROUP');
        }
    } else {
        hideAllocationType(allocRunTypeFld.getValue(), allocGrpFld, allocIdFld);
    }

    if (view.state == 'showedit' && isPeriodEnd.getValue() == 'true') {
        window.view.addMessage(MESSAGE_WARNING, GT("IA.BY_SUBMITTING_THIS_START_DATE_MAY_CHANGE_AS_YOU_HA"));
    }
}

function hideAllocationType(allocRunType) {

    var allocGrpFld = window.editor.view.findComponents('GLACCTALLOCATIONGRP', 'Field')[0];
    var allocIdFld = window.editor.view.findComponents('GLACCTALLOCATION', 'Field')[0];
    clearGridData();
    if (allocRunType == radioAllocationIdValue) {
        updatedFldProperty(allocGrpFld, 'required', false);
        updatedFldProperty(allocGrpFld, 'hidden', true);
        updatedFldProperty(allocIdFld, 'required', true);
        updatedFldProperty(allocIdFld, 'hidden', false);
        allocGrpFld.setValue('');
    } else {
        updatedFldProperty(allocGrpFld, 'required', true);
        updatedFldProperty(allocGrpFld, 'hidden', false);
        updatedFldProperty(allocIdFld, 'required', false);
        updatedFldProperty(allocIdFld, 'hidden', true);
        allocIdFld.setValue('');
    }
}

function updatedFldProperty(allocIdFld, property, value) {
    allocIdFld.updateProperty(property, value);
    allocIdFld.redraw();
}

function getAllocationGrpMembers(fieldMeta, type) {
    clearGridData();
    if (typeof fieldMeta == 'string') {
        allocation = fieldMeta.toString();
    } else {
        var allocation = fieldMeta.getValue();
    }
    if (allocation) {
        var param = {allocation: allocation, type: type};
        var c_SubmitSuccess = getAllocationGroupMembersSuccess();
        var c_SubmitError = getAllocationGroupMembersError();
        window.editor.ajax(true, 'getAllocationGroupMembers', param, c_SubmitSuccess, c_SubmitError);
    }
}

/**
 * AJAX Success call back
 *
 * @param obj
 * @returns {c_SubmitSuccess}
 */
function getAllocationGroupMembersSuccess() {
    function c_SubmitSuccess(value) {

        var errors = value['errors'];
        var messages = value['messages'];

        if (errors) {
            for (var i = 0; i < errors.length; i++) {
                window.view.addMessage(MESSAGE_ERROR, errors[i]);
            }
        } else if (messages) {
            for (var i = 0; i < messages.length; i++) {
                window.view.addMessage(MESSAGE_INFO, messages[i]);
            }
        } else {
            var gridTarget = window.editor.findComponents('GLACCTALLOCATIONMEMBERS', 'Grid');
            gridTarget = gridTarget[0];
            var rowcount = value.length;
            for (var i = 0; i < rowcount; i++) {
                gridTarget.addRowData(i,
                    {
                        MEMBER: value[i]['MEMBER'],
                        SOURCE_TIME_PERIOD: value[i]['SOURCE'],
                        BASIS_TIME_PERIOD: value[i]['BASIS'],
                        POSTING_JOURNAL: value[i]['JOURNALSYMBOL'],
                        POSTED_DATE: value[i]['POSTED_DATE'],
                    }
                );
                gridTarget.redraw();
            }
        }
    }

    return c_SubmitSuccess;
}

/**
 * Overriden this function in allocation as we are not supporting EOM
 * Hide/Show the 'Every' and 'End of Month' fields
 * depending on the value selected for 'Posting Repeat By'
 */
function repeatByChange(obj)
{
    // Gather the field data
    obj.meta.parentComponent.gatherData();

    // Are we in the reversal section ?
    //var isReversal = ( obj.meta.getPath().search('REVJE') != -1 );

    // Show/Hide the fields depending on what section and what option the user select
    var showFieldPath = [];
    var reqFieldPath = [];
    var hideFieldPath = [];
    var value = obj.meta.getValue();

    var everyPath = obj.meta.scheduleFields['REPEATINTERVAL'];

    if ( value != 'None' ) {
        showFieldPath.push(everyPath);
        reqFieldPath.push(everyPath);

        // Change the suffix value to match with the selected value
        var suffixPath = 'SUFFIX_' +  everyPath;
        var suffix = obj.meta.parentComponent.findComponents(suffixPath);
        suffix = suffix[0];
        
        // Get the label for the selected value
        var valueLabel = obj.meta.type.validlabels[obj.meta.type.validvalues.indexOf(value)];
        suffix.setValue(valueLabel);

    } else {
        hideFieldPath.push(everyPath);
    }

    // Show/Hide the fields
    showHideFields(showFieldPath, reqFieldPath, hideFieldPath, true, obj.meta.scheduleFields);
}

/**
 * AJAX Error call back
 *
 * @returns {c_SubmitError}
 */
function getAllocationGroupMembersError() {
    function c_SubmitError(value) {
        var errors = value['errors'];

        if (errors) {
            for (var i = 0; i < errors.length; i++) {
                window.view.addMessage(MESSAGE_ERROR, errors[i]);
            }
        } else {
            window.view.addMessage(MESSAGE_ERROR, GT('IA.ERROR_CORRECT_ANY_ISSUES_THEN_TRY_AGAIN')
            );
        }

    }

    return c_SubmitError;
}

function clearGridData() {
    var gridTarget = window.editor.findComponents('GLACCTALLOCATIONMEMBERS', 'Grid');
    gridTarget = gridTarget[0];
    var gridRowCount = gridTarget.getRowCount();
    for (var i = gridRowCount - 1; i >= 0; i--) {
        gridTarget.deleteRowData(i);
        gridTarget.redraw();
    }
}