/**
 * Hide or show the fields in the Account Information section of the Credit Card Setup section.
 */
function useAccountChanged()
{
    var merchantUseAchAccount = window.editor.view.getField('MERCHANT_USE_ACH_ACCOUNT').getValue();
    var merchantEnvironmentField = window.editor.view.getField('MERCHANT_ENVIRONMENT');
    var merchantAccountTypeField = window.editor.view.getField('MERCHANT_ACCOUNTTYPE');
    var merchantAccountField = window.editor.view.getField('MERCHANT_ACCOUNT');
    var merchantGlAccountField = window.editor.view.getField('MERCHANT_GLACCOUNTNO');
    var merchantAuthNetIdField = window.editor.view.getField('MERCHANT_AUTH_NET_ID');
    var merchantAuthNetTranKeyField = window.editor.view.getField('MERCHANT_AUTH_NET_TRAN_KEY');
    if (merchantUseAchAccount == 'true') {
        merchantEnvironmentField.updateProperty('hidden', true);
        merchantAccountTypeField.updateProperty('hidden', true);
        merchantAccountField.updateProperty('hidden', true);
        merchantGlAccountField.updateProperty('hidden', true);
        merchantAuthNetIdField.updateProperty('hidden', true);
        merchantAuthNetTranKeyField.updateProperty('hidden', true);
    } else {
        merchantEnvironmentField.updateProperty('hidden', false);
        merchantAccountTypeField.updateProperty('hidden', false);
        merchantAccountField.updateProperty('hidden', false);
        merchantGlAccountField.updateProperty('hidden', false);
        var merchantGateway = window.editor.view.getField('MERCHANT_GATEWAY').getValue();
        if ( merchantGateway == 'AuthorizeNet' ) {
            merchantAuthNetIdField.updateProperty('hidden', false);
            merchantAuthNetTranKeyField.updateProperty('hidden', false);
        }
        accountTypeChanged();
    }
    merchantEnvironmentField.parentComponent.redraw();
    enableCardPaymentChange();
    enableAchPaymentChanged();
}

/**
 * Hide or show the fields in the Account Information section of the ACH Setup section.
 */
function achUseAccountChanged()
{
    var achUseMerchantAccount = window.editor.view.getField('ACH_USE_MERCHANT_ACCOUNT').getValue();
    var achEnvironmentField = window.editor.view.getField('ACH_ENVIRONMENT');
    var achAccountTypeField = window.editor.view.getField('ACH_ACCOUNTTYPE');
    var achAccountField = window.editor.view.getField('ACH_ACCOUNT');
    var achGLAccountField = window.editor.view.getField('ACH_GLACCOUNTNO');
    var achAuthNetIdField = window.editor.view.getField('ACH_AUTH_NET_ID');
    var achAuthNetTranKeyField = window.editor.view.getField('ACH_AUTH_NET_TRAN_KEY');
    if ( achUseMerchantAccount == 'true' ) {
        achEnvironmentField.updateProperty('hidden', true, true);
        achAccountTypeField.updateProperty('hidden', true, true);
        achAccountField.updateProperty('hidden', true, true);
        achGLAccountField.updateProperty('hidden', true, true);
        achAuthNetIdField.updateProperty('hidden', true, true);
        achAuthNetTranKeyField.updateProperty('hidden', true, true);
    } else {
        achEnvironmentField.updateProperty('hidden', false, true);
        achAccountTypeField.updateProperty('hidden', false, true);
        achAccountField.updateProperty('hidden', false, true);
        achGLAccountField.updateProperty('hidden', false, true);
        achAuthNetIdField.updateProperty('hidden', false, true);
        achAuthNetTranKeyField.updateProperty('hidden', false, true);
        achAccountTypeChanged();
    }
    achEnvironmentField.parentComponent.redraw();
    enableCardPaymentChange();
    enableAchPaymentChanged();
}

/**
 * Hide or show the 'ACH_USE_MERCHANT_ACCOUNT' and MERCHANT_USE_ACH_ACCOUNT fields
 */
function accountInfoChanged() {
    var merchantGateway = window.editor.view.getField('MERCHANT_GATEWAY').getValue();
    var merchantUseAchAccountField = window.editor.view.getField('MERCHANT_USE_ACH_ACCOUNT');
    var merchantUseAchAccount = merchantUseAchAccountField.getValue();
    var merchantAuthNetId = window.editor.view.getField('MERCHANT_AUTH_NET_ID').getValue();
    var merchantAuthNetTranKey = window.editor.view.getField('MERCHANT_AUTH_NET_TRAN_KEY').getValue();
    var merchantEnvironment = window.editor.view.getField('MERCHANT_ENVIRONMENT').getValue();
    var merchantAccount = window.editor.view.getField('MERCHANT_ACCOUNT').getValue();
    var merchantGlAccountKey = window.editor.view.getField('MERCHANT_GLACCOUNTNO').getValue();
    var achUseMerchantAccountField = window.editor.view.getField('ACH_USE_MERCHANT_ACCOUNT');
    var achUseMerchantAccount = achUseMerchantAccountField.getValue();
    var achAuthNetId = window.editor.view.getField('ACH_AUTH_NET_ID').getValue();
    var achAuthNetTranKey = window.editor.view.getField('ACH_AUTH_NET_TRAN_KEY').getValue();
    var achEnvironment = window.editor.view.getField('ACH_ENVIRONMENT').getValue();
    var achAccount = window.editor.view.getField('ACH_ACCOUNT').getValue();
    var achGlAccountKey = window.editor.view.getField('ACH_GLACCOUNTNO').getValue();
    
    if (merchantGateway == 'AuthorizeNet' && merchantUseAchAccount != 'true' && merchantAuthNetId
        && merchantAuthNetTranKey && merchantEnvironment && (merchantAccount || merchantGlAccountKey)
        ) {
        achUseMerchantAccountField.updateProperty('hidden', false);
    } else {
        achUseMerchantAccountField.updateProperty('hidden', true);
        achUseMerchantAccountField.setValue('false');
    }
    
    if (achUseMerchantAccount != 'true' && achAuthNetId && achAuthNetTranKey && achEnvironment &&
        (achAccount || achGlAccountKey) && merchantGateway == 'AuthorizeNet'
        ) {
        merchantUseAchAccountField.updateProperty('hidden', false);
    } else {
        merchantUseAchAccountField.updateProperty('hidden', true);
        merchantUseAchAccountField.setValue('false');
    }
    merchantUseAchAccountField.parentComponent.redraw();
    useAccountChanged();
    achUseAccountChanged();
}

/**
 * Hide or show fields according to the value of the 'MERCHANT_GATEWAY' field.
 */
function gatewayChanged()
{
    var merchantGateway = window.editor.view.getField('MERCHANT_GATEWAY').getValue();
    var paypalLinkField1 = window.editor.view.getField('PAYPAL_LINK_1');
    var paypalLinkField2 = window.editor.view.getField('PAYPAL_LINK_2');
    var authNetLinkField1 = window.editor.view.getField('AUTH_NET_LINK_1');
    var authNetLinkField2 = window.editor.view.getField('AUTH_NET_LINK_2');
    var paypalCredentialsSection = window.editor.view.findComponentsById('PAYPAL_CREDENTIALS')[0];
    var authNetCredentialsSection = window.editor.view.findComponentsById('AUTH_NET_CREDENTIALS')[0];
    if ( merchantGateway == 'AuthorizeNet' ) {
        paypalLinkField1.updateProperty('hidden', true);
        paypalLinkField2.updateProperty('hidden', true);
        authNetLinkField1.updateProperty('hidden', false);
        authNetLinkField2.updateProperty('hidden', false);
        paypalCredentialsSection.updateProperty('hidden', true);
        authNetCredentialsSection.updateProperty('hidden', false);
    } else {
        paypalLinkField1.updateProperty('hidden', false);
        paypalLinkField2.updateProperty('hidden', false);
        authNetLinkField1.updateProperty('hidden', true);
        authNetLinkField2.updateProperty('hidden', true);
        paypalCredentialsSection.updateProperty('hidden', false);
        authNetCredentialsSection.updateProperty('hidden', true);
    }
    paypalLinkField1.parentComponent.parentComponent.redraw();
    paypalLinkField2.parentComponent.parentComponent.redraw();
    accountInfoChanged();
    enableCardPaymentChange();
    enableAchPaymentChanged();
}

/**
 * Hide or show fields according to the value of the 'ACH_ACCOUNTTYPE' field.
 */
function achAccountTypeChanged() {
    window.setTimeout(function() {
        window.editor.gatherData();
        var achAccountType = window.editor.view.getField('ACH_ACCOUNTTYPE')
            .getValue();
        var achAccountField = window.editor.view.getField('ACH_ACCOUNT');
        var achGLAccountField = window.editor.view.getField('ACH_GLACCOUNTNO');
        if ( achAccountType == 'Bank' ) {
            achAccountField.updateProperty('hidden', false);
            achGLAccountField.updateProperty('hidden', true);
        } else if ( achAccountType == 'Undeposited Funds Account' ) {
            achAccountField.updateProperty('hidden', true);
            achGLAccountField.updateProperty('hidden', false);
        }
        achAccountField.parentComponent.redraw();
        enableCardPaymentChange();
        enableAchPaymentChanged();
    }, 0);
}

/**
 * Hide or show fields according to the value of the 'MERCHANT_ACCOUNTTYPE' field.
 */
function accountTypeChanged() {
    window.setTimeout(function() {
        window.editor.gatherData();
        var merchantAccountType = window.editor.view.getField('MERCHANT_ACCOUNTTYPE')
            .getValue();
        var merchantAccountField = window.editor.view.getField('MERCHANT_ACCOUNT');
        var merchantGlAccountField = window.editor.view.getField('MERCHANT_GLACCOUNTNO');
        if ( merchantAccountType == 'Bank' ) {
            merchantAccountField.updateProperty('hidden', false);
            merchantGlAccountField.updateProperty('hidden', true);
        } else if ( merchantAccountType == 'Undeposited Funds Account' ) {
            merchantAccountField.updateProperty('hidden', true);
            merchantGlAccountField.updateProperty('hidden', false);
        }
        merchantAccountField.parentComponent.redraw();
        enableCardPaymentChange();
        enableAchPaymentChanged();
    }, 0);
}

/**
 * Hide or show fields according to the 'ENABLE_CARD_PAYMENT' value
 */
function enableCardPaymentChange() {
    var paymentProcessorSection = window.editor.view.findComponentsById('PAYMENT_PROCESSOR')[0];
    var accountInformationSection = window.editor.view.findComponentsById('ACCOUNT_INFORMATION')[0];
    var paypalCredentialsSection = window.editor.view.findComponentsById('PAYPAL_CREDENTIALS')[0];
    var authNetCredentialsSection = window.editor.view.findComponentsById('AUTH_NET_CREDENTIALS')[0];
    var enableCardPayment = window.editor.view.getField('ENABLE_CARD_PAYMENT').getValue();
    var merchantUseAchAccount = window.editor.view.getField('MERCHANT_USE_ACH_ACCOUNT').getValue();
    var merchantAccountType = window.editor.view.getField('MERCHANT_ACCOUNTTYPE').getValue();
    var merchantGateway = window.editor.view.getField('MERCHANT_GATEWAY').getValue();
    var merchantAccountField = window.editor.view.getField('MERCHANT_ACCOUNT');
    var merchantGLAccountKeyField = window.editor.view.getField('MERCHANT_GLACCOUNTNO');
    var merchantPartnerField = window.editor.view.getField('MERCHANT_PARTNER');
    var merchantVendorField = window.editor.view.getField('MERCHANT_VENDOR');
    var merchantPasswordField = window.editor.view.getField('MERCHANT_PASSWORD');
    var merchantAuthNetIdField = window.editor.view.getField('MERCHANT_AUTH_NET_ID');
    var merchantAuthNetTranKeyField = window.editor.view.getField('MERCHANT_AUTH_NET_TRAN_KEY');
    
    if ( enableCardPayment == 'true' ) {
        paymentProcessorSection.updateProperty('hidden', false);
        accountInformationSection.updateProperty('hidden', false);
        if (merchantGateway == 'AuthorizeNet') {
            authNetCredentialsSection.updateProperty('hidden', false);
            paypalCredentialsSection.updateProperty('hidden', true);
        }
        else {
            paypalCredentialsSection.updateProperty('hidden', false);
            authNetCredentialsSection.updateProperty('hidden', true);
        }
        if (merchantUseAchAccount != 'true') {
            if ( merchantAccountType == 'Bank' ) {
                merchantAccountField.updateProperty('required', true);
                merchantGLAccountKeyField.updateProperty('required', false);
            } else {
                merchantAccountField.updateProperty('required', false);
                merchantGLAccountKeyField.updateProperty('required', true);
            }

            if ( merchantGateway == 'AuthorizeNet' ) {
                merchantPartnerField.updateProperty('required', false);
                merchantVendorField.updateProperty('required', false);
                merchantPasswordField.updateProperty('required', false);
                merchantAuthNetIdField.updateProperty('required', true);
                merchantAuthNetTranKeyField.updateProperty('required', true);
            } else {
                merchantPartnerField.updateProperty('required', true);
                merchantVendorField.updateProperty('required', true);
                merchantPasswordField.updateProperty('required', true);
                merchantAuthNetIdField.updateProperty('required', false);
                merchantAuthNetTranKeyField.updateProperty('required', false);
            }
        }
    } else {
        paymentProcessorSection.updateProperty('hidden', true);
        accountInformationSection.updateProperty('hidden', true);
        paypalCredentialsSection.updateProperty('hidden', true);
        authNetCredentialsSection.updateProperty('hidden', true);
        merchantAccountField.updateProperty('required', false);
        merchantGLAccountKeyField.updateProperty('required', false);
        merchantPartnerField.updateProperty('required', false);
        merchantVendorField.updateProperty('required', false);
        merchantPasswordField.updateProperty('required', false);
        merchantAuthNetIdField.updateProperty('required', false);
        merchantAuthNetTranKeyField.updateProperty('required', false);
    }
    accountInformationSection.parentComponent.redraw();
}

function enableAchPaymentChanged() {
    
    var enableAchPayment = window.editor.view.getField('ENABLE_ACH_PAYMENT').getValue();
    var achUseMerchantAccount = window.editor.view.getField('ACH_USE_MERCHANT_ACCOUNT').getValue();
    var achAccountType = window.editor.view.getField('ACH_ACCOUNTTYPE').getValue();
    var achAccountField = window.editor.view.getField('ACH_ACCOUNT');
    var achGLAccountKeyField = window.editor.view.getField('ACH_GLACCOUNTNO');
    var achAuthNetIdField = window.editor.view.getField('ACH_AUTH_NET_ID');
    var achAuthNetTranKeyField = window.editor.view.getField('ACH_AUTH_NET_TRAN_KEY');
    var achPaymentProcessorSection = window.editor.view.findComponentsById('ACH_PAYMENT_PROCESSOR')[0];
    var achAccountInformationSection = window.editor.view.findComponentsById('ACH_ACCOUNT_INFORMATION')[0];
    
    if (enableAchPayment == 'true') {
        achPaymentProcessorSection.updateProperty('hidden', false);
        achAccountInformationSection.updateProperty('hidden', false);
        if (achUseMerchantAccount != 'true') {
            if ( achAccountType == 'Bank' ) {
                achAccountField.updateProperty('required', true);
                achGLAccountKeyField.updateProperty('required', false);
            } else {
                achAccountField.updateProperty('required', false);
                achGLAccountKeyField.updateProperty('required', true);
            }
            achAuthNetIdField.updateProperty('required', true);
            achAuthNetTranKeyField.updateProperty('required', true);
        }
    } else {
        achPaymentProcessorSection.updateProperty('hidden', true);
        achAccountInformationSection.updateProperty('hidden', true);
        achAccountField.updateProperty('required', false);
        achGLAccountKeyField.updateProperty('required', false);
        achAuthNetIdField.updateProperty('required', false);
        achAuthNetTranKeyField.updateProperty('required', false);
    }
    achPaymentProcessorSection.parentComponent.redraw();
}

function loadPortal(urlField) {
    window.open(urlField, '');
    return true;
}