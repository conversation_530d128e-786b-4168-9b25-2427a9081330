
var NS4 = (navigator.appName == "Netscape" && parseInt(navigator.appVersion, 10) < 5);

function addOption(theSel, theText, theValue) {
	var newOpt = new Option(theText, theValue);
	var selLength = theSel.length;
	theSel.options[selLength] = newOpt;
}

function deleteOption(theSel, theIndex) { 
	var selLength = theSel.length;
	if(selLength>0) {
		theSel.options[theIndex] = null;
	}
}

function moveOptions(theSelFrom, theSelTo) {
	var selLength = theSelFrom.length;
	var selectedText = new Array();
	var selectedValues = new Array();
	var selectedCount = 0;

	var i;

	// Find the selected Options in reverse order
	// and delete them from the 'from' Select.
	for(i=selLength-1; i>=0; i--) {
		if(theSelFrom.options[i].selected) {
			selectedText[selectedCount] = theSelFrom.options[i].text;
			selectedValues[selectedCount] = theSelFrom.options[i].value;
			deleteOption(theSelFrom, i);
			selectedCount++;
		}
	}

	// Add the selected text/values in reverse order.
	// This will add the Options to the 'to' Select
	// in the same order as they were in the 'from' Select.
	for(i=selectedCount-1; i>=0; i--) {
		addOption(theSelTo, selectedText[i], selectedValues[i]);
	}

	if(NS4) history.go(0);
	
	return false;
}

function moveUp(destination) {
	var opts = destination.options;

	for (var k=1; k<opts.length; k++) {
		if (opts[k].selected) {
			var optId = opts[k].value;
			if (optId && optId.charAt(0) != '@') {
				var tmp = opts[k-1];
				opts[k-1] = new Option(opts[k].text, opts[k].value);
				opts[k] = new Option(tmp.text, tmp.value);
				opts[k-1].selected = true;
				break;
			}
		}
	}

	return false;
}

function moveDown(destination) {
	var opts = destination.options;

	for (var k=0; k<opts.length-1; k++) {
		if (opts[k].selected) {
			var optId = opts[k].value;
			if (optId && optId.charAt(0) != '@') {
				var tmp = opts[k+1];
				opts[k+1] = new Option(opts[k].text, opts[k].value);
				opts[k] = new Option(tmp.text, tmp.value);
				opts[k+1].selected = true;
				break;
			}
		}
	}

	return false;
}

function getSelected() {
	var opts = document.theForm.assignedList.options;
	var tmp = "";
	for (var k=0; k<opts.length; k++) {
		var optId = opts[k].value;
		if (optId && optId.charAt(0) == '@')
			optId = optId.substring(1);
		tmp = tmp + optId+",";
	}
	return tmp;
}
