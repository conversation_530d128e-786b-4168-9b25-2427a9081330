function tsOnLoad(event) {
    updateLastEmailDate();
    timesheetStatusSearch(event);
    window.tsShowViewAllWarningId = -1; // The warning msg id for view all employees (manager field blank)
    var emailMessage = document.getElementById('_obj__EMAIL_TEXT');
    emailMessage.rows = 2;
}

function updateLastEmailDate() {
    var theDate = window.view.value['LASTEMAIL'];
    var email = ReformatDate(theDate, STANDARDFORMAT, USERDATEFORMAT);
    if (email) {
        var lastEmail = window.editor.findComponents('EMAIL_LASTEMAIL');
        emailMessage = GT({
                              'id': 'IA.LAST_EMAIL_SENT',
                              'placeHolders': [{ 'name': 'EMAIL_DATE', 'value': email }]
                          });
        lastEmail[0].setValue(emailMessage);
    }
}

var dateSpanInDays = 31;

function timesheetStatusSearch(event) {
    window.view.clearMessages();

    if (!validateSearch()) {
        return;
    }

    //Validate date range
    if(!validDate()) {
        return;
    }

    window.editor.showLoadingBar();
    window.editor.ajax(true, 'search', null, function(data) {
        var grids = window.editor.findComponents('TIMESHEETSTATUS_ITEMS', 'Grid');
        if (grids && grids.length > 0) {
            grid = grids[0];
        }
        if (grid) {
            if (data instanceof Array) {
                grid.setValue(data);
                grid.redraw( function() {
                    updateSelectedDisplay(0);
                    adjustGridHeaderCells();
                    adjustGridLabel();
                    timesheetStatusSetupCellClasses(this);
                });
                grid['numSelected'] = 0;

            } else {
                grid.setValue([]);
                grid.redraw();
                try {
                    if (data['_errors']) {
                        for (var i = 0; i < data['_errors'].length; i++) {
                            window.view.addMessage(MESSAGE_ERROR, data['_errors'][i]);
                        }
                    }
                    if (data['_warnings']) {
                        for (var i = 0; i < data['_warnings'].length; i++) {
                            window.view.addMessage(MESSAGE_WARNING, data['_warnings'][i]);
                        }
                    }
                } catch(error) {
                }
            }
        }
        window.editor.hideLoadingBar();
    }, function(error) {
        window.editor.hideLoadingBar();
    });
}

function adjustGridHeaderCells() {
    var dateControl = window.editor.findComponents('SEARCH_DATE');
    var dateControlFrom = window.editor.findComponents('SEARCH_DATE_FROM');
    var toDate = dateControl[0].value;
    var fromDate = AddDays(toDate, -(dateSpanInDays-1), STANDARDFORMAT);

    if (dateControlFrom[0].value) {
        fromDate = dateControlFrom[0].value;
    }

    var theDate = CreateDateFromString (fromDate, STANDARDFORMAT);
    var grids = window.editor.findComponents('TIMESHEETSTATUS_ITEMS', 'Grid');
    if (grids && grids.length > 0) {
        grid = grids[0];
    }
    if (grid) {
        var firstDay = timesheetStatusFindFirstDayColumn(grid);
        var lastDay = firstDay + 30;
        var lastHeaderCell = firstDay + dateSpanInDays - 1;
        // find each of the header cells, and set day value
        for (var i=firstDay; i<=lastDay; i++) {
            var id = "_obj__TIMESHEETSTATUS_ITEMS_" + i + "_header_all";
            var cell = document.getElementById(id);
            //Hide cell if exceeds limit
            if (i <= lastHeaderCell) {
                cell.innerHTML = theDate.getDate();
                if (theDate.getDay() == 6 && i != lastDay) {
                    addClass(cell, 'sat');
                } else {
                    removeClass(cell, 'sat');
                }
                theDate.setDate(theDate.getDate()+1);
                //remove hide class for header if any
                removeClass(cell, 'hideCell');
            } else {
                addClass(cell, 'hideCell');
            }
        }
    }
}

function adjustGridLabel() {
    var dateControl = window.editor.findComponents('SEARCH_DATE');
    // display date range in the user's preferred format
    var toDate = ReformatDate(dateControl[0].value, STANDARDFORMAT, USERDATEFORMAT);
    var dateControlFrom = window.editor.findComponents('SEARCH_DATE_FROM');
    var fromDate = AddDays(toDate, -(dateSpanInDays-1), USERDATEFORMAT);
    if (dateControlFrom[0].value) {
        fromDate = ReformatDate(dateControlFrom[0].value, STANDARDFORMAT, USERDATEFORMAT);
    }
    var prevPeriod = GT('IA.GO_BACK_ONE_PERIOD');
    var prevDay = GT('IA.GO_BACK_ONE_DAY');
    var nextDay = GT('IA.GO_FORWARD_ONE_DAY');
    var nextPeriod = GT('IA.GO_FORWARD_ONE_PERIOD');
    var message = '<div class="cmenterDiv">\
    <span href="#none" class="controlIcon tsPrevFast" title="' + prevPeriod + '"  onclick="tsAdjustDate(-1, \'period\');">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>\
        <span href="#none" class="controlIcon tsPrev" title="' + prevDay + '" onclick="tsAdjustDate(-1, \'day\');">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>\
        <span id="GRID_RANGE_LABEL">' + fromDate + " to " + toDate + '</span>\
        <span href="#none" class="controlIcon tsNext" title="'  + nextDay + '" onclick="tsAdjustDate(1, \'day\');">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>\
        <span href="#none" class="controlIcon tsNextFast" title="'  + nextPeriod + '" onclick="tsAdjustDate(1, \'period\');">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>\
    </div>';
    var dl= window.editor.view.getField('STATE_RANGE');
    dl.infoText = message;
    dl.redraw();
    //var rangeLabel = document.getElementById("GRID_RANGE_LABEL");
    //rangeLabel.innerHTML = fromDate + " to " + toDate;
}

function tsAdjustDate(direction, unit) {
    var dateControl = window.editor.findComponents('SEARCH_DATE');
    var days = direction * (unit == 'day' ? 1 : dateSpanInDays);
    var newDate = AddDays(dateControl[0].value, days, STANDARDFORMAT);
    dateControl[0].setValue(newDate);

    var dateControlFrom = window.editor.findComponents('SEARCH_DATE_FROM');
    if (dateControlFrom[0].value) {
        var newDate = AddDays(dateControl[0].value, -(dateSpanInDays-1), STANDARDFORMAT);
        dateControlFrom[0].setValue(newDate);
    }

    adjustGridHeaderCells();
    adjustGridLabel();
    timesheetStatusSearch();
}

function validateSearch() {
    var ok = false;
    // validate search criteria
    if (window.editor.validateData()) {
        ok = true;
    }
    return ok;
}

function gridCheckBoxHandler(gridName, field) {
    if (field) {
        var grid = editor.findComponents(gridName, 'Grid');
        if ( grid && grid[0]) {
            grid = grid[0];
            var start = grid.getPageStartLine();
            var end = grid.getPageEndLine();
            for ( var ix = start; ix < end; ix++ ) {
                var f = grid.findLineComponent('SELECTED', ix);
                f.setValue(field.checked);
            }
            if (field.checked) {
                grid['numSelected'] = end - start;  // don't count header
            } else {
                grid['numSelected'] = 0;
            }
            updateSelectedDisplay(grid['numSelected']);
        }
    }
}

function tsSelectClick(event) {
    if (event.target) {
        var grid = editor.findComponents('TIMESHEETSTATUS_ITEMS', 'Grid');
        if ( grid && grid[0]) {
            grid = grid[0];
            if (!('numSelected' in grid)) {
                grid['numSelected'] = 0;
            }
            if (event.target.checked) {
                grid['numSelected']++;
            } else {
                grid['numSelected']--;
            }
            updateSelectedDisplay(grid['numSelected']);
        }
    }
}

function tsManagerChange(mgrControl) {
    if (!mgrControl.meta || mgrControl.meta.readonly || !mgrControl.meta.currentPickValues ) return;
    var mgrField = mgrControl.meta;
    mgrField.gatherData();
    var mgrId = mgrField.getValue();
    if (!mgrId) {
        // Show the error message if not displayed yet
        if ( window.tsShowViewAllWarningId < 0 ) {
            var msg = GT('IA.TIMESHEET_STATUS_SEARCH_ALL_EMPLOYEES_MESSAGE');
            window.tsShowViewAllWarningId = window.view.addMessage(MESSAGE_WARNING, msg);
        }
    } else if ( window.tsShowViewAllWarningId > 0 ) {
        // Remove it if it is there
        window.view.removeMessage(MESSAGE_WARNING, window.tsShowViewAllWarningId);
        window.tsShowViewAllWarningId = -1;
    }
}

function tsProjectChange(projectControl) {
    if (!projectControl.meta) {
        return;
    }
    var projectField = projectControl.meta;
    projectField.gatherData();
    var projectId = projectField.getValue();
    // if project was blanked, then clear task control
    if (!projectId) {
        var taskControl = window.editor.findComponents('SEARCH_TASKID');
        taskControl[0].setValue('');
    }
}

function tsProjectGroupChange(pickControl) {
    if (!pickControl.meta) {
        return;
    }
    // lookup type of selected line, and store it in hidden field
    var pickField = pickControl.meta;
    if (!pickField) return;
    pickField.gatherData();
    var pickId = pickField.getValue();
    // if project was blanked, then clear task control
    if (!pickId) {
        var taskControl = window.editor.findComponents('SEARCH_TASKID');
        taskControl[0].setValue('');
        return;
    }
    var pickInfo = pickField.findPickerObject(pickId);
    window.view.value['PROJECTNGROUP_TYPE'] = pickInfo ? pickInfo['TYPE'] : '';
}

function tsLocGroupChange(pickControl) {
    if (!pickControl.meta) {
        return;
    }
    // lookup type of selected line, and store it in hidden field
    var pickField = pickControl.meta;
    if (!pickField) return;
    pickField.gatherData();
    var pickId = pickField.getValue();
    var pickInfo = pickField.findPickerObject(pickId);
    window.view.value['LOCNGROUP_TYPE'] = pickInfo ? pickInfo['TYPE'] : '';
}

function tsDeptGroupChange(pickControl) {
    if (!pickControl.meta) {
        return;
    }
    // lookup type of selected line, and store it in hidden field
    var pickField = pickControl.meta;
    if (!pickField) return;
    pickField.gatherData();
    var pickId = pickField.getValue();
    var pickInfo = pickField.findPickerObject(pickId);
    window.view.value['DEPTNGROUP_TYPE'] = pickInfo ? pickInfo['TYPE'] : '';
}

function updateSelectedDisplay(numSelected) {
    var control = editor.findComponents('EMAIL_SELECTED');

    control[0].fullname = GT({
                                 'id': 'IA.TIMESHEET_STATUS_SELECTED_RESOURCES',
                                 'placeHolders': [{ 'name': 'NUMBER_OF_RESOURCES', 'value': numSelected }]
                             });
    control[0].redraw();
}

function timesheetStatusFindFirstDayColumn(grid) {
    // find the index of the first day column - there are at least 2 columns in front
    for ( var ix = 2; ix < grid.getColumnCount(); ix++ ) {
        if (grid.children[ix].children[0].path == 'DAY_01' ) break;
    }
    return ix;
}

function timesheetStatusSetupCellClasses(grid) {
    if ( ! grid ) {
        var grids = window.editor.findComponents('TIMESHEETSTATUS_ITEMS', 'Grid');
        if ( grids && grids.length > 0 ) {
            grid = grids[0];
        }
    }

    if ( grid ) {
        if( grid.isDrawing() ) {
            setTimeout( function() {timesheetStatusSetupCellClasses(grid);grid = null;}, 50);
            return;
        }
        var startIndex = timesheetStatusFindFirstDayColumn(grid);
        var stopIndex = startIndex + dateSpanInDays;

        if (!grid.currentChildren || !grid.value) {
            return;
        }
        for ( var rowIndex = 0; rowIndex < grid.value.length; rowIndex++ ) {
            var rowValue = grid.value[rowIndex];
            if( ! rowValue ) continue;
            var rowChildren = grid.currentChildren[rowIndex];
            for ( var columnIndex = startIndex; columnIndex < grid.getColumnCount(); columnIndex++ ) {
                var child = rowChildren[columnIndex];
                if (child) {
                    var target = child.children[0];
                    timesheetStatusSetCellClass(target, columnIndex - startIndex, columnIndex >= stopIndex);
                }
            }
        }
    }
}

function timesheetStatusSetCellClass(target, dayIndex, hide) {
    var className = 'readonly statusIcon no_grid_min_width';
    var bgClassName = 'center';
    if (!hide) {
        var state = target.parentValue['DAYS'][dayIndex]['STATE'];
        // state can be: 'Submitted', 'Approved', 'Entered', 'Missing' or null
        if (state && state != '') {
            className += ' ts' + state;
            bgClassName += ' tsBg' + state;
        }
    } else {
        bgClassName += ' hideCell';
    }
    // remove any existing class designations
    if (target.className) {
        var classes = target.className.split(' ');
        classes.forEach(function(val, index, theArray){
            target.removeClass(val);
        })
    }
    var classes = className.split(' ');
    classes.forEach(function(val, index, theArray){
        target.updateProperty('className', val, true);
    })
    target.parentComponent.setClass(bgClassName);
}

// may not need this, since timesheetStatus can't be altered
function timesheetStatusPopupClose(target) {
    timesheetSetCellClass(target);
}

function hasClass(el, name) {
    return new RegExp('(\\s|^)'+name+'(\\s|$)').test(el.className);
}

function addClass(el, name) {
    if (!hasClass(el, name)) { el.className += (el.className ? ' ' : '') +name; }
}

function removeClass(el, name) {
    if (hasClass(el, name)) {
        el.className=el.className.replace(new RegExp('(\\s|^)'+name+'(\\s|$)'),' ').replace(/^\s+|\s+$/g, '');
    }
}

function drawOldUI(dayEntries, element) {
    // init innerHTML
    var innerHTML = "<div class='piProjectDialog'><table>";
    // foreach ENTRIES
    for (var i=0; i < dayEntries.length; i++) {
        // format entry info and append to innerHTML
        var newDiv = formatEntryInfo(dayEntries[i]);
        innerHTML += newDiv;
    }
    innerHTML += "</table></div>";
    // locate PROJECT_INFO div and set innerHTML
    var dialog = window.editor.view.findComponents("PROJECT_INFO");
    dialog[0]['default'] = innerHTML;
    editor.showPage('tsProjectInfo', element);
}

function drawNewUI(dayEntries, element) {
    var gridEntries = [];
    var stateMap = {'Submitted':'IA.SUBMITTED', 'Approved':'IA.APPROVED','Partially Approved': 'IA.PARTIALLY_APPROVED', 'Declined':'IA.DECLINED','Draft': 'IA.DRAFT', 'Partially Declined':'IA.PARTIALLY_DECLINED', 'Saved':'IA.SAVED'};
    for (var i=0; i < dayEntries.length; i++) {
        gridEntries.push({
                'PROJ_CUST' : (dayEntries[i].PROJECTID ? dayEntries[i].PROJECTID : dayEntries[i].CUSTOMERID),
                'TASK_ITEM' : (dayEntries[i].TASKNAME ? (dayEntries[i].TASKNAME + ", " + FormatAmtForDisplay(dayEntries[i].HOURS) + " "+ GT('IA.HOURS')) : dayEntries[i].ITEMID),
                'STATE' : GT(stateMap[dayEntries[i].STATE])
            }
        );
    }
    window.editor.view.value['PROJECT_INFORMATION_QX'] = gridEntries;
    editor.showPage('tsProjectInfo_QX', element);
}

function tsGridClick(event) {
    var element = event.target ? event.target : event.srcElement;
    if (element.meta && element.meta.path && element.meta.path.substring(0,4) == 'DAY_') {
        // format content of popup from element meta-data
        // extract day number from path
        var index = parseInt(element.meta.path.substring(4,6), 10) - 1;
        // use as index to in DAYS
        if (element.meta.parentValue.DAYS[index].ENTRIES) {
            if ( typeof PAGE_LAYOUT_TYPE != 'undefined' && PAGE_LAYOUT_TYPE === 'Q' ) {
                drawNewUI(element.meta.parentValue.DAYS[index].ENTRIES, element);
            } else {
                drawOldUI(element.meta.parentValue.DAYS[index].ENTRIES, element);
            }
        }
    }
}

function formatEntryInfo(entry) {
    var stateMap = {'Submitted':'IA.SUBMITTED', 'Approved':'IA.APPROVED','Partially Approved': 'IA.PARTIALLY_APPROVED', 'Declined':'IA.DECLINED','Draft': 'IA.DRAFT', 'Partially Declined':'IA.PARTIALLY_DECLINED', 'Saved':'IA.SAVED'};
    var data = window.view.value;
    var html = "<tr><td><table>";
    html += "<tr><td><span class='piProject'>";
    if (entry.PROJECTID) {
        html += data['PROJECT_LABEL'] + ": " + entry.PROJECTID;
    } else {
        html += data['CUSTOMER_LABEL']  + ": " + entry.CUSTOMERID;
    }
    html +=  "</span></td></tr>";
    html += "<tr><td><span class='piTask'>";
    if (entry.TASKNAME) {
        html += data['TASK_LABEL'] + ": " + entry.TASKNAME + ", " + FormatAmtForDisplay(entry.HOURS) + " " + GT('IA.HOURS');
    } else {
        html += data['ITEM_LABEL'] + ": " + entry.ITEMID;
    }
    html += "</span></td></tr>";
    html += "</table></td>";
    html += "<td class='piStatus'><span class='pi" + entry.STATE.replace(" ", "") + "'>" + GT(stateMap[entry.STATE.replace(" ", "")]) + "</span></td>";
    html += "</tr>";
    return html;
}

function tsSendEmail(event) {
    window.view.clearMessages();
    window.editor.gatherData();
    window.editor.showLoadingBar();
    window.editor.ajax(true, 'sendEmail', null, function(data) {
        // check for error values returned and display
        var today = dateToStandardFormat(new Date());
        window.view.value['LASTEMAIL'] = today;
        updateLastEmailDate();
        try {
            if (data['_errors']) {
                for (var i = 0; i < data['_errors'].length; i++) {
                    window.view.addMessage(MESSAGE_ERROR, data['_errors'][i]);
                }
            }
            if (data['_warnings']) {
                for (var i = 0; i < data['_warnings'].length; i++) {
                    window.view.addMessage(MESSAGE_WARNING, data['_warnings'][i]);
                }
            }
        } catch(error) {
        }
        window.editor.hideLoadingBar();
    }, function(error) {
        window.editor.hideLoadingBar();
    });
}

/**
 * dateSpan - returns the inclusive number of days represented by a date range
 *
 * @param beginDate From date
 * @param endDate To date
 * @returns {number} number of days represented by the range
 */
function dateSpan(beginDate, endDate) {
    var fromDate = new Date(beginDate);
    var toDate = new Date(endDate);
    // use UTC dates to ignore the effects of DST
    var utc1 = Date.UTC(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate());
    var utc2 = Date.UTC(toDate.getFullYear(), toDate.getMonth(), toDate.getDate());
    var span = Math.floor((utc2 - utc1) / (1000 * 3600 * 24)) + 1;

    return span;
}

function dateToStandardFormat(theDate) {
    var d = theDate.getDate();
    var m = theDate.getMonth() + 1;
    var y = theDate.getFullYear();
    return '' + (m<=9 ? '0' + m : m) + '/' + (d <= 9 ? '0' + d : d) + '/' + y;
}

function GridEmployeePicker(meta) {
    this.meta = meta;
}

/**
 * The function will validate the date range
 * @returns {boolean}
 */
function validDate() {
    var dateControl = window.editor.findComponents('SEARCH_DATE');
    var dateControlFrom = window.editor.findComponents('SEARCH_DATE_FROM');
    var toDate = dateControl[0].value;

    if (dateControlFrom[0].value) {
        fromDate = dateControlFrom[0].value;
        dateSpanInDays = dateSpan(fromDate, toDate);
    } else {
        dateSpanInDays = 31;
    }

    // check for valid date range
    if (dateSpanInDays > 31) {
        alert("Invalid date range. Maximum range is 31 days");
        dateControlFrom[0].setValue("");
        return false;
    }

    // check for valid dates
    if (dateSpanInDays < 1) {
        alert("The start date must be before the end date");
        dateControlFrom[0].setValue("");
        return false;
    }

    return true;
}

GridEmployeePicker.inheritsFrom(InputControlPtr);

GridEmployeePicker.prototype.addCustomAssists = function(assists, readOnly) {
    if (window.view.value['RESSCHED_ENABLED'] == 'T') {
        var assist = {
            'func' : function (elem, event) {
                if (this.assist.control.meta.parentValue) {
                    value = this.assist.control.meta.parentValue;
                    empid = encodeURIComponent(value.EMPLOYEEID);
                    name = encodeURIComponent(value.RESOURCE);
                    var url = CustomizeURL('lister.phtml', '&.op=4952&.empid=' + empid + '&.name='+ name);
                    Launch(url, "action" + '_1', 1024, 500, false);
                } else {
                    alert('No schedule to view');
                }
            },
            'text' : 'schedule'
        };
        assists.push(assist);
    }
}

GridEmployeePicker.prototype.drawReadOnly = function() {
    var assists = [];
    this.addCustomAssists(assists, true);
    return InputControlAssisted.prototype.drawReadOnly.call(this, assists, false);
}
