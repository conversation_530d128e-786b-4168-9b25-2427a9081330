/**
 * Created on 24-02-2017.
 */

function ManageApprovalDelegate() {
    url = "editor.phtml?.popup=1&.op=" + APPROVALDELEGATE_OP + "&.sess=" + SESS;
    Launch(url, 'managepolicy', 900, 500);
}

function showManageDelegateBtn(checkbox) {
    var manageDeligates = window.editor.findComponents('MANAGEDELEGATES', 'Row')[0];
    var hiddenState = true;
    if (checkbox.checked) {
        hiddenState = false;
    }
    manageDeligates.updateProperty('hidden', hiddenState);
    manageDeligates.parentComponent.redraw();
}

// Upon clicking on the save button, validate the delegate entries and submit the form.
function validateDelegateEntries() {
    window.view.clearMessages();
    var grids = window.editor.findComponents('APPROVAL_DELEGATE_DETAILS', 'Grid');
    if (grids && grids.length > 0) {
        var defaultDelegate = window.editor.findComponents('DEFAULT_DELEGATE', 'Field')[0];
        var defaultDelegateGroup = window.editor.findComponents('DEFAULT_DELEGATEUSERGROUPID', 'Field')[0];
        if (isEmpty(defaultDelegate.value) && isEmpty(defaultDelegateGroup.value)) {
            window.view.addMessage(MESSAGE_ERROR, "A value for Default delegate is required.");
            return false;
        }
        var grid = grids[0];
        var gridLength = grid.value.length;
        var rows = grid.getValue();
        var approverCurrentLine = "";
        var delegateCurrentLine = "";
        for (var line = 0; line < gridLength; line++) {
            if (!isEmpty(rows[line]['APPROVER']) && isEmpty(rows[line]['DELEGATE'])
                && isEmpty(rows[line]['DELEGATEUSERGROUPID'])) {
                window.view.addMessage(MESSAGE_ERROR, "Select a delegate for line " + (line + 1));
                return false;
            }
            if(rows[line]['DELEGATE'] != null) {
                approverCurrentLine = rows[line]['APPROVER'].split('--')[0];
                delegateCurrentLine = rows[line]['DELEGATE'].split('--')[0];
                if (approverCurrentLine == delegateCurrentLine) {
                    window.view.addMessage(MESSAGE_ERROR, "Select a delegate who is not the same as the approver for line " + (line + 1));
                    return false;
                }
            }
        }
    }
    window.editor.submit(true, 'save', true);
}

function isEmpty(value){
    return (value === undefined || value == null || value.length <= 0) ? true : false;
}

// Upon selecting a default delegate, update the delegate/delegateusergroupid/delegatetype fields
// in the grid which are not overridden by user.
function updateDefaultDelegateInGrid(val,isUserType) {
    var grids = window.editor.findComponents('APPROVAL_DELEGATE_DETAILS', 'Grid');
    if (grids && grids.length > 0) {
        var grid = grids[0];
        var gridLength = grid.value.length;
        var delegateTyep = 'U';
        var userDelegateVal = val.value;
        var groupDelegateVal = "";

        if(!isUserType) {
            groupDelegateVal = val.value;
            userDelegateVal = "";
            delegateTyep = 'UG';
        }
        for (var line = 0; line < gridLength; line++) {
            var defaultDlgteOvrdn = window.editor.findComponents("DEFAULTDELEGATEOVRRDN", 'Field')[line];
            var lineDelegate = grid.findLineComponent('DELEGATE', line, 'Field');
            var lineDelegateGroup = grid.findLineComponent('DELEGATEUSERGROUPID', line, 'Field');
            var lineDelegateType = grid.findLineComponent('DELEGATETYPE', line, 'Field');
            if (defaultDlgteOvrdn.value != true && defaultDlgteOvrdn.value != "true") {

                lineDelegate.setValue(userDelegateVal);
                lineDelegate.updateProperty('hidden', !isUserType);

                lineDelegateGroup.setValue(groupDelegateVal);
                lineDelegateGroup.updateProperty('hidden', isUserType);

                lineDelegateType.setValue(delegateTyep);
                lineDelegateType.updateMetadata();
                lineDelegate.redraw();
            }
        }
    }
}

// Upon selecting a delegate/delegateusergroupid, update the delegateovrrdn field.
function updateDelegateHelperField(val) {
    var lineNo = val.meta.getLineNo();
    var defaultDlgteOvrdn = window.editor.findComponents("DEFAULTDELEGATEOVRRDN", 'Field')[lineNo];
    var defaultDelegateType = window.editor.findComponents("DEFAULT_DELEGATETYPE", 'Field')[0];
    var defaultUserDelegate = window.editor.findComponents("DEFAULT_DELEGATE", 'Field')[0];
    var defaultGroupDelegate = window.editor.findComponents("DEFAULT_DELEGATEUSERGROUPID", 'Field')[0];

    var defaultDelegatedValue = "";
    var selectedDelegate = val.value.split('--')[0];
    if(defaultDelegateType.value == 'U'
        && defaultUserDelegate.value != null) {
        defaultDelegatedValue = defaultUserDelegate.value.split('--')[0];
    }
    else if(defaultDelegateType.value == 'UG' && defaultGroupDelegate.value != null) {
        defaultDelegatedValue = defaultGroupDelegate.value.split('--')[0];
    }

    if (defaultDelegatedValue == selectedDelegate ) {
        defaultDlgteOvrdn.setValue("false");
    }
    else {
        defaultDlgteOvrdn.setValue("true");
    }
}

// Onload initialize the grid fields.
function initGridFields(event) {
    var grids = window.editor.findComponents('APPROVAL_DELEGATE_DETAILS', 'Grid');
    if (grids && grids.length > 0) {
        var grid = grids[0];
        for ( var line = 0; line < grid.value.length; line++ ) {
            var delegateType = grid.findLineComponent('DELEGATETYPE', line, 'Field').value;
            toggleDelegateType(line,delegateType);
        }
    }
}

//On delegatetype change toggle the delegate/delegateusergroupid fields.
function changeDelegateType(val) {
    var lineNo = val.meta.getLineNo();
    toggleDelegateType(lineNo,val.value);
}

//Toggle the delegate/delegateusergroupid fields.
function toggleDelegateType(lineNo,delegateType){
    var delegateGroupId = window.editor.findComponents("DELEGATEUSERGROUPID", 'Field')[lineNo];
    var delegate = window.editor.findComponents("DELEGATE", 'Field')[lineNo];
    var delegateGroupState = false;
    var delegateUserState = false;

    if(delegateType === 'U')
    {
        delegateGroupState = true;
    }else{
        delegateUserState = true;
    }
    delegateGroupId.updateProperty('hidden', delegateGroupState);
    delegate.updateProperty('hidden', delegateUserState);

    delegateGroupId.parentComponent.redraw();
    delegate.parentComponent.redraw();
}

//On default delegatetype change toggle the default default delegate/default delegateusergroupid fields.
function toggleDefaultDelegateType(val){
    var defaultDelegate = window.editor.findComponents("DEFAULT_DELEGATE", 'Field')[0];
    var defaultDelegateGroup = window.editor.findComponents("DEFAULT_DELEGATEUSERGROUPID", 'Field')[0];
    var delegateGroupState = false;
    var delegateUserState = false;

    if(val.value === 'U')
    {
        delegateGroupState = true;
        defaultDelegateGroup.setValue("");
    }else{
        delegateUserState = true;
        defaultDelegate.setValue("");
    }
    defaultDelegate.updateProperty('hidden', delegateUserState);
    defaultDelegateGroup.updateProperty('hidden',delegateGroupState);

    defaultDelegate.parentComponent.redraw();
    defaultDelegateGroup.parentComponent.redraw();
}
