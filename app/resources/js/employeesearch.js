Object.keys = Object.keys || (function () {
    var hasOwnProperty = Object.prototype.hasOwnProperty,
        hasDontEnumBug = !{toString:null}.propertyIsEnumerable("toString"),
        DontEnums = [
            'toString',
            'toLocaleString',
            'valueOf',
            'hasOwnProperty',
            'isPrototypeOf',
            'propertyIsEnumerable',
            'constructor'
        ],
        DontEnumsLength = DontEnums.length;

    return function (o) {
        if (typeof o != "object" && typeof o != "function" || o === null)
            throw new TypeError("Object.keys called on a non-object");

        var result = [];
        for (var name in o) {
            if (hasOwnProperty.call(o, name))
                result.push(name);
        }

        if (hasDontEnumBug) {
            for (var i = 0; i < DontEnumsLength; i++) {
                if (hasOwnProperty.call(o, DontEnums[i]))
                    result.push(DontEnums[i]);
            }
        }

        return result;
    };
})();

function search() {
    window.view.clearMessages();

    if (!validateSearch()) {
        return;
    }

    window.editor.showLoadingBar();
    window.editor.ajax(true, 'search', null, function(data) {
        var grids = window.editor.findComponents('EMPLOYEES');
        if (grids && grids.length > 0) {
            if (data instanceof Array) {
                if (data.length > 0) {
                    var keys = Object.keys(data[0]);
                    for (var i = 0; i < data.length; i++) {
                        var record = data[i];
                        for ( var j = 0; j < keys.length; j++) {
                            if (-1 < keys[j].indexOf('.')) {
                                var nkey = keys[j].replace(/\./g, '_');
                                record[nkey] = record[keys[j]];
                                delete record[keys[j]];
                            }
                        }
                    }
                }
                grids[0].setValue(data);
                grids[0].redraw();
            } else {
                grids[0].setValue([]);
                grids[0].redraw();
                try {
                    if (data['_errors']) {
                        for (var i = 0; i < data['_errors'].length; i++) {
                            window.view.addMessage(MESSAGE_ERROR, data['_errors'][i]);
                        }
                    }
                    if (data['_warnings']) {
                        for (var i = 0; i < data['_warnings'].length; i++) {
                            window.view.addMessage(MESSAGE_WARNING, data['_warnings'][i]);
                        }
                    }
                    
                } catch(error) {
                }
            }
        }
        window.editor.hideLoadingBar();
    }, function(error) {
        window.editor.hideLoadingBar();
    });
}

function validateSearch() {
    var fields = window.editor.findComponents('SEARCH_AVAILABILITY');
    var v = fields[0].value;
    if (!v || v === 'A') {
        return true;
    }

    var ok = true;
    var startDate = null, endDate = null;
    var fields = window.editor.findComponents('SEARCH_AVAILABILITY_FROM');
    if (fields && fields.length > 0) {
        try {
            if (fields[0].value && fields[0].value !== '') {
                startDate = new Date(fields[0].value);
            } else {
                ok = false;
            }
        } catch (e) {
            ok = false;
        }
        if (!ok) {
            window.view.addMessage(MESSAGE_ERROR, 'Please provide a valid availability FROM date');
        }
    }

    fields = window.editor.findComponents('SEARCH_AVAILABILITY_TO');
    if (fields && fields.length > 0) {
        var fieldOk = true;
        try {
            if (fields[0].value && fields[0].value !== '') {
                endDate = new Date(fields[0].value);
            } else {
                fieldOk = ok = false;
            }
        } catch (e) {
            fieldOk = ok = false;
        }
        if (!fieldOk) {
            window.view.addMessage(MESSAGE_ERROR, 'Please provide a valid availability TO date')
        }
    }

    if (ok) {
        if (startDate.getTime() > endDate.getTime()) {
            window.view.addMessage(MESSAGE_ERROR, 'The availability FROM date is after the availability TO date');
            ok = false;
        }
    }
    return ok;
}

function gridCheckBoxHandler(gridName, field) {

    if (field) {
        var grid = editor.findComponents(gridName, 'Grid');
        if ( grid && grid[0]) {
            grid = grid[0];
            var start = grid.getPageStartLine();
            var end = grid.getPageEndLine();
            for( var ix = start; ix < end; ix++ ) {
                var f = grid.findLineComponent('SELECTED', ix);
                f.setValue(field.checked);
            }
        }
    }
}

function esProjectGroupChange(pickControl) {
    if (!pickControl.meta) {
        return;
    }
    // lookup type of selected line, and store it in hidden field
    var pickField = pickControl.meta;
    if (!pickField) return;
    pickField.gatherData();
    var pickId = pickField.getValue();
    var pickInfo = pickField.findPickerObject(pickId);
    window.view.value['PROJECTNGROUP_TYPE'] = pickInfo ? pickInfo['TYPE'] : '';
}

function esLocGroupChange(pickControl) {
    if (!pickControl.meta) {
        return;
    }
    // lookup type of selected line, and store it in hidden field
    var pickField = pickControl.meta;
    if (!pickField) return;
    pickField.gatherData();
    var pickId = pickField.getValue();
    var pickInfo = pickField.findPickerObject(pickId);
    window.view.value['LOCNGROUP_TYPE'] = pickInfo ? pickInfo['TYPE'] : '';
}

function esDeptGroupChange(pickControl) {
    if (!pickControl.meta) {
        return;
    }
    // lookup type of selected line, and store it in hidden field
    var pickField = pickControl.meta;
    if (!pickField) return;
    pickField.gatherData();
    var pickId = pickField.getValue();
    var pickInfo = pickField.findPickerObject(pickId);
    window.view.value['DEPTNGROUP_TYPE'] = pickInfo ? pickInfo['TYPE'] : '';
}

function esEmpGroupChange(pickControl) {
    if (!pickControl.meta) {
        return;
    }
    // lookup type of selected line, and store it in hidden field
    var pickField = pickControl.meta;
    if (!pickField) return;
    pickField.gatherData();
    var pickId = pickField.getValue();
    var pickInfo = pickField.findPickerObject(pickId);
    window.view.value['EMPNGROUP_TYPE'] = pickInfo ? pickInfo['TYPE'] : '';
}

/*
 * This was added to replace "GridEmployeePicker" assisted column with two columns so it would work in ActionUI.
 */
function onClickEmployeeSchedule(employeeScheduleButton) {
    var grid = employeeScheduleButton.meta.getGrid();
    var lineno = employeeScheduleButton.meta.getLineNo();

    var empField = grid.findLineComponent('EMPLOYEEID', lineno, 'Field');
    if (!empField.getValue()) {
        alert('No schedule to view');
        return;
    }
    var values = empField.getValue().split('--');
    if (values.length == 1) {
        values.push(es_getEmployeeName(empField.parentValue));
    }
    var url = CustomizeURL('lister.phtml', '&.op=4952&.empid=' + values[0] + '&.name='+ values[1]);
    Launch(url, "action" + '_1', 1024, 500, false);
}