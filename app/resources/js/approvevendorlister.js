function ApproveVendor(rec, appType, ownerloc) {
    var done = document.getElementById('done').value;
    set('.rec',rec);
    set('.approve',appType);
    set('.action',appType);
    set('.done', done);
    approvalcommentCtrl = document.getElementById('apprcommentdiv');
    //set('.approveurl',approveurl);
    set('.ownerloc', ownerloc);
    approvalHeader = document.getElementById('aphead');
    approvalButton = document.getElementById('submitlink');
    if (appType == 'Approve') {
        approvalHeader.innerHTML = '&nbsp;'+GT('IA.APPROVE_VENDOR');
        approvalButton.innerHTML = '&nbsp;'+GT('IA.APPROVE');
    } else {
        approvalHeader.innerHTML = '&nbsp;'+GT('IA.DECLINE_VENDOR');
        approvalButton.innerHTML = '&nbsp;'+GT('IA.DECLINE');
    }
    document.getElementById('.approvalcomments').value = '';
    if (approvalcommentCtrl) {
        return displayControl(approvalcommentCtrl.style);
    } else {
        return false;
    }
}

function SubmitAll(rec, url) {
    set('.rec', '');
    var docSelected = false;
    var docs = document.getElementsByName('.checks[]');
    for (i = 0; i < docs.length; i++) {
        if (docs[i].checked) {
            docSelected = true;
            break;
        }
    }
    if (docSelected) {
        return setactMethod('POST',url);
    } else {
        if (PAGE_LAYOUT_TYPE && PAGE_LAYOUT_TYPE === 'Q') {
            window.setTimeout(function(){
                QXUtil.hideLoading();
            },0);
        } else {
            YAHOO.loadingPanel.panel.hide();
        }
        return false;
    }
}

function CancelApprovalComments() {
    var apprcommentCtrl = document.getElementById('apprcommentdiv');
    if (apprcommentCtrl) {
        document.getElementById('.approvalcomments').value = '';
        return this.closeControl(apprcommentCtrl.style);
    } else {
        return false;
    }
}

function SaveApprovalWithComments() {

    var sess = document.getElementById('sess').value;
    var pageOp = document.getElementById('pageOp').value;
    var cny = document.getElementById('cny').value;
    var csrfTokenField = document.getElementById('csrfTokenField').value;
    var currScript = document.getElementById('currScript').value;
    var csrfToken = document.getElementById('csrfToken').value;
    var approveLoading = document.getElementById('approveLoading').value;
    var declineLoading = document.getElementById('declineLoading').value;
    
    
    
    approvalcomments = document.getElementById('.approvalcomments');
    
    set('.apprcomment',approvalcomments.value);

    appType = document.ff.elements['.approve'].value;

    if (appType == 'Approve') {
        ypanelstr = approveLoading;
    } else {
        ypanelstr = declineLoading;
    }
    LaunchLoadingPanel(ypanelstr);
    
    addElement(csrfTokenField, csrfToken);
    setactMethod('POST', 'submit.phtml');
    
    return false;
}