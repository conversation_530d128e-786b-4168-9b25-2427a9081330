/*!
 * Bootstrap-select v1.12.4 (http://silviomoreto.github.io/bootstrap-select)
 *
 * Copyright 2013-2017 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
!function(a,b){"function"==typeof define&&define.amd?define(["jquery"],function(a){return b(a)}):"object"==typeof module&&module.exports?module.exports=b(require("jquery")):b(a.jQuery)}(this,function(a){!function(a){a.fn.selectpicker.defaults={noneSelectedText:"មិនមានអ្វីបានជ្រើសរើស",noneResultsText:"មិនមានលទ្ធផល {0}",countSelectedText:function(a,b){return"{0} ធាតុដែលបានជ្រើស"},maxOptionsText:function(a,b){return[1==a?"ឈានដល់ដែនកំណត់ ( {n} ធាតុអតិបរមា)":"អតិបរមាឈានដល់ដែនកំណត់ ( {n} ធាតុ)",1==b?"ដែនកំណត់ក្រុមឈានដល់ ( {n} អតិបរមាធាតុ)":"អតិបរមាក្រុមឈានដល់ដែនកំណត់ ( {n} ធាតុ)"]},selectAllText:"ជ្រើស​យក​ទាំងអស់",deselectAllText:"មិនជ្រើស​យក​ទាំងអស",multipleSeparator:", "}}(a)});