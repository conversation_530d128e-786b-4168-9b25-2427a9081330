var userId = sfUserId;  // From ia container
var sfId = "";
// currently logged in Salesforce id
var toggleGrp = "Account";
// which feed is showing on a toggle feed
var chatterFeed;
// holds the current feed
var gUserInfoDeferred = undefined;
var gGroupInfoDeferred = undefined;
var gGroupMembershipInfoDeferred = undefined;
var gContextInfoDeferred = undefined;


// ensure correct protocol
if (window.location.protocol != "https:") {
    window.location.href = "https:" + window.location.href.substring(window.location.protocol.length);
}

String.prototype.trunc = function (n) {
    var toLong = this.length > n;
    var s_ = toLong ? this.substr(0, n - 1) : this;
    if (toLong)
        s_ = s_.substr(0, s_.lastIndexOf(' '));
    return toLong ? s_ + '  ... (<u>more</u>)' : s_;
};

function getParam(sname, myurl) {

    var params = myurl.substr(myurl.indexOf("?") + 1);
    var sval = null;
    params = params.split("&");

    // split param and value into individual pieces
    for (var i = 0; i < params.length; i++) {
        temp = params[i].split("=");
        if ([temp[0]] == sname) {
            sval = decodeURIComponent(temp[1]);
        }
    }
    return sval;
};

var determineContext = function () {
    var pagetype = getParam('pagetype', location.href);
    var sfid = getParam('sfid', location.href);
    var objecttype = getParam('objecttype', location.href);
    var field = 'Intacct_Id__c';

    if (sfid == null) {
        objecttype = null;
        field = null;
    }

    if (objecttype == 'CollaborationGroup' || objecttype == 'collaborationgroup') {
        objecttype = 'CollaborationGroup';
        field = 'Name';
        if (sfid == 'Key%20Account%20-%20Barney%26%23039%3Bs') {
            field = 'Id';
            sfid = '0F9i0000000H3mNCAS';
        }
    }

    if (objecttype == 'User') {
        field = 'CommunityNickname';
    }

    if (pagetype == 'chatterbug') {
        return undefined;
    }

    return {
        object: objecttype,
        matchUsing: field,
        matchValue: sfid,
        contextResolver: function (data, config) {
            if (data.totalSize > 0) {
                if (data.totalSize > 1) {
                    throw "Multiple records found with given context: " + config.context.matchValue;
                }
                return {
                    "id": data.records[0].Id,
                    "name": data.records[0].Name
                };
            }
            return;
        }
    };
};

var navigate = function (partype, parname, parid) {
    tellHost("navigate*" + partype + "*" + parname + "*" + parid);
};

var removequotes = function (text) {

    var temp = text.replace("&#39;", "\'");
    return temp.replace(/'/g, "\\'");
};

var resetContext = function (contextType, contextValue, contextField) {
    var msg = "";
    if (contextType !== null) {
        if (okContext(contextValue)) {
            var context = {
                object: contextType,
                matchUsing: contextField,
                matchValue: contextValue

            };
            if (chatterFeed) {
                chatterFeed.resetContext(context);
            }
        } else {
            if (chatterFeed) {
                chatterFeed.resetContext();
                msg = "No filters applied - bad record";
            }
        }
    } else {

        if (chatterFeed) {
            chatterFeed.resetContext();
        }

    }

};

function tellHost(message) {

    var target = parent.postMessage ? parent : (parent.document.postMessage ? parent.document : undefined);
    target.postMessage(message, '*');

}

var autoResizePending = false;
var autoresize = function () {
    if ( ! autoResizePending ) {
        autoResizePending = true;
        window.setTimeout(function() {
            autoResizePending = false;
            tellHost('resize');
        }, 1);
    }
};

require(['support/chatterBugScripts', 'main', 'support/followScripts', 'support/groupScripts', 'support/pageSetups',
         'support/lookups', 'jquery', 'backbone.salesforce' ],

    function(renderChatterBug) {

        gUserInfoDeferred = $.Deferred();
        gGroupInfoDeferred = $.Deferred();
        gGroupMembershipInfoDeferred = $.Deferred();
        gContextInfoDeferred = $.Deferred();
        Backbone.Salesforce.sid = gSess;
        Backbone.Salesforce.host = "sfdchatter.phtml?.sess=" + gSess + "&.proxy=";
        Backbone.Salesforce.proxy = { url : "sfdchatter.phtml?.sess=" + gSess + "&.proxy=${path}",
                                      headers : [ { name: 'X-XSRF_TOKEN', value:  gXsrfToken } ] };

        var pageType = getParam("pagetype", location.href).toLowerCase();
        var postControl = getParam("postcontrol", location.href);
        sfId = getParam('sfid', location.href);

        var setupComplete = function() {

            $('#chatter').children().remove();

            var contextId = sfId;
            switch ( pageType ) {
                case "people":
                case "groups":
                case "files":
                case "files:owned":
                case "files:to_me":
                case "chatterbug":
                case "dashboard":
                case "dashboard:what_i_follow":
                case "dashboard:to_me":
                case "dashboard:company":
                    contextId = pageType;
                    break;
            }

            var render = function (/* Sfdc.Chatter.Feed */ feedContext, /* ChatterFeedOnly */ feed) {

                if ( feed && pageType !== "chatterbug" ) {
                    feed.resetContext(feedContext);
                }

                if ( pageType == 'people' || pageType == 'groups' ) {

                    var pageContextId = sfId || userId;
                    if (pageContextId) {
                        gUserInfoDeferred.done(function(userInfo) {
                            renderCurrentUserProfile(userInfo);
                        });
                    }

                } else if ( pageType == 'person' || pageType.indexOf('feed') == 0  ) {

                    var pageContextId = sfId || userId;
                    if (pageContextId) {
                        if ( pageContextId == userId ) {
                            gUserInfoDeferred.done(function(userInfo) {
                                gContextInfoDeferred.resolve(userInfo);
                            });
                        } else {
                            var user = new Backbone.Salesforce.Model({});
                            user.url = 'chatter/users/' + pageContextId;
                            user.fetch({
                                async: true,
                                success: function (user, textStatus, jqXHR) {
                                    gContextInfoDeferred.resolve(user);
                                },
                                url: user.url
                            });
                        }

                    }

                    gContextInfoDeferred.done(function(userInfo) {
                        if ( pageType == 'person' ) {
                            // Person page
                            renderUserProfile(userInfo);
                        } else {
                            // Generic feed pages
                            renderCurrentUserProfile(userInfo);
                        }
                    });

                } else if (pageType == 'chatterbug') {

                    document.getElementsByTagName('body')[0].className += 'chatterbug';
                    restCall("chatter/feeds/news/me/feed-elements?sort=LastModifiedDateDesc", function(feed) {
                        renderChatterBug(feed, gSess);
                    });

                } else if ( pageType == 'group' || pageType == 'dashgroup' ) {

                    if ( sfId ) {
                        var soqlQuery = "SELECT id, name, fullphotourl, ownerId, owner.name, CollaborationType, IsArchived, InformationTitle, InformationBody, Description, IsAutoArchiveDisabled from collaborationgroup where id='" + sfId + "'";
                        fetchRecord(soqlQuery)
                            .done(function(data) {
                                // Duplicate Name to name so that we can use common code in the gContextInfoDeferred with
                                // /chatter/user/... data sources which use leading lower-case attribute names
                                data.set('name', data.get('Name'));
                                renderGroupProfile(data);
                                gGroupInfoDeferred.resolve(data);
                                gContextInfoDeferred.resolve(data);
                            });
                    }
                }

                autoresize();

            };

            Sfdc.Chatter.Feed.create({
                el: '#chatter',
                style: {
                    width: '524px',
                    linkColor: '#015BA7'
                },
                showFeedSelectorAndSort: false,
                showPublishPostControl:   pageType.indexOf('feed') == 0 || pageType == 'person' || pageType == "object" || pageType == "group"
                                       || pageType.indexOf("dashboard") == 0 || pageType == "dashgroup" || postControl,
                showRefreshButton: pageType.indexOf('dashboard') != 0 && pageType != 'dashgroup',
                pageType: pageType,
                sess: gSess,
                contextId: contextId,
                userId: userId,
                nogrouppost: false,

                success: function (/* ChatterFeedOnly */ feed) {
                    render(this, feed);
                },

                error: function (err) {
                    render(this, false);
                }

            });
        };

        if ( pageType != 'chatterbug' ) {
            //get current user metadata
            var user = new Backbone.Salesforce.Model({});
            user.url = 'chatter/users/me';
            user.fetch({
                async: true,
                success: function (data, textStatus, jqXHR) {
                    gUserInfoDeferred.resolve(user);
                },
                url: user.url
            });
        }

        setuppage(setupComplete.bind(this));

    }

);

