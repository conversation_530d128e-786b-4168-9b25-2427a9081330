# Makefile

UNAME := $(shell uname)
ifneq ($(UNAME), Darwin)
RM=/bin/rm
TOUCH=/bin/touch
FIND=/usr/bin/find
else
# Mac defaults work fine.
RM=rm
TOUCH=touch
FIND=find
endif

BUILD_TOOLS_DIR = $(IA_BUILD_TOOLS)
YUICOMPRESS = $(BUILD_TOOLS_DIR)/yui_compress-2.4.8.sh

BUILD_DIR = ../../build
CSS_BUILD_DIR = ../../build/css/
CSS2INC = ../../tools/eng/convertCssToArr.bin

PWD := $(shell pwd)

# find if newer source files exist; using 1 second accuracy
LAST_CSS_TIME_INTEGER = $(shell find $(PWD)/*.css -type f -printf '%T@\n' | sort -n | tail -1 | cut -d '.' -f 1)
LAST_MCSS_TIME = $(shell find $(CSS_BUILD_DIR)*.mcss -type f -printf '%T@\n' 2> /dev/null | sort -n | tail -1 | cut -d '.' -f 1)
LAST_MCSS_TIME_INTEGER = $(shell test -z "$(LAST_MCSS_TIME)" && echo 0 || echo $(LAST_MCSS_TIME))

# find first non existing minified file
FILE_MISSING = $(shell for file in *.css; do { test -e "$(CSS_BUILD_DIR)$${file%.*}.mcss"; } || { echo "f"; break; }; done)

all:
	@test -d $(BUILD_DIR) || mkdir $(BUILD_DIR)
	@test -d $(CSS_BUILD_DIR) || mkdir $(CSS_BUILD_DIR)
	@if test "$(FILE_MISSING)" != "" || test $(LAST_MCSS_TIME_INTEGER) -lt $(LAST_CSS_TIME_INTEGER) ; then \
		echo "Minifying css..."; \
		$(YUICOMPRESS) css '*.css' '.css$$:.mcss'; \
		mv *.mcss $(CSS_BUILD_DIR); \
		$(CSS2INC) $(CSS_BUILD_DIR); \
	fi

# required label, but nothing to do here
makeqrydepend:
	@echo "Nothing to see here.  Move along." >makeqrydepend.tmp
