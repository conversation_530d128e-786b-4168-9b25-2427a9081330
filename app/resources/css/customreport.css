.trigger{
	cursor: pointer;
	cursor: hand;
	margin-top: 0px;
	margin-bottom: 0px;
}
.custlisttitle{
	font-size:12px;
	font-weight:bold;
	font-family: Arial, sans-serif;
	color:#000000;
}
.branch{
	display: block;
	margin-left: 20px;
	/*margin-top: 3px;*/
	/*margin-bottom: 5px;*/
}
.leaf_column_header{
	background:#DDD7C1;
	border-right:0px solid #CCCBC4;
	width:270px;
	padding:1px;
	text-align:center;
	white-space:nowrap;
	font-size:11px;
	font-family: Arial, sans-serif;
	/*font-weight:bold;*/
	color:#000000;
}
.row_label_right{
	background:#E9E8DF;
	border-right:0px solid #CCCBC4;
	text-align:right;
	white-space:nowrap;
	font-size:11px;
	font-family: Arial, sans-serif;
	/*font-weight:bold;*/
	color:#000000;
}
.row_label_left{
	background:#E9E8DF;
	border-right:0px solid #CCCBC4;
	text-align:left;
	white-space:nowrap;
	font-size:11px;
	font-family: Arial, sans-serif;
	/*font-weight:bold;*/
	color:#000000;
}

.node_name {
	background:#DDD7C1;
}

table.column_selection_cell {
	padding:0px 0px 0px 0px;
	margin:0px 0px 0px 0px;
}

div.workarea { padding:0px; float:right; border:1px solid #333333; width:100% }

ul.draglist { 
    --width: 200px; 
    list-style: none;
    margin:0;
    padding:3px;
    /*
       The bottom padding provides the cushion that makes the empty 
       list targetable.  Alternatively, we could leave the padding 
       off by default, adding it when we detect that the list is empty.
    */
    --padding-bottom:20px;
}

ul.draglist li {
    margin: 2px;
    cursor: move; 
}


li.list {
	background-color: #E9E8DF;
    border:0px solid #333333;
	padding:2px;
}

div.listic {
	display:inline;
	width:5%;
	text-align:left;
}

div.listlab {
	display:	inline;		/* Stay on one line */
	position:	absolute;	/* So we can control position */
	right:		30%;		/* 30% from right edge */
}

div.list {
	display:	inline;		/* Stay on one line */
	margin-left:	70%;		/* Align to left side */
}

#user_actions { float:right }

