<?
//=============================================================================
//
//	FILE:			getlist_metdata.2.1.inc
//	AUTHOR:			<PERSON><PERSON>
//	DESCRIPTION:	backend functions for Wrappers to Ledger and Utility APIs
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

global $xmlmapping;

$mailaddress = array(
    'attr' => array(
        'address1' => 'ADDR1',
        'address2' => 'ADDR2',
        'city' => 'CITY',
        'state' => 'STATE',
        'zip' => 'ZIP',
        'country' => 'COUNTRY'
    ),
    'table' => 'mailaddress',
    'fkey' => 'RECORD#'
);

$batchdata = array(
    'key' => 'RECORD#',
    'batchtitle' => 'TITLE',
    'datecreated' => 'CREATED',
    'open' => 'OPEN',
    'status' => 'STATUS',
    'totalamount' => 'TOTAL'
);

$contactfields = array(
    'subelements' => array('mailaddress'),
    'attr' => array(
        'contactname' => 'NAME',
        'printas' => 'PRINTAS',
        'companyname' => 'COMPANYNAME',
        'prefix' => 'MRMS',
        'firstname' => 'FIRSTNAME',
        'lastname' => 'LASTNAME',
        'initial' => 'MI',
        'phone1' => 'PHONE1',
        'phone2' => 'PHONE2',
        'cellphone' => 'CELLPHONE',
        'pager' => 'PAGER',
        'fax' => 'FAX',
        'email1' => 'EMAIL1',
        'email2' => 'EMAIL2',
        'url1' => 'URL1',
        'url2' => 'URL2',
        'status' => 'STATUS'
    ),
    'table' => 'contact',
    'invfkey' => array('MAILADDRKEY'),
    'fkey' => 'RECORD#'
);

$glentry_structure = array(
    'key' => 'GLENTRY.RECORD#',
    'trtype' => 'TR_TYPE',
    'amount' => 'AMOUNT',
    'glaccountno' => 'ACCOUNTNO',
    'document' => 'DOCUMENT',
    'datecreated' => 'ENTRY_DATE',
    'memo' => 'DESCRIPTION',
    'locationid' => 'LOCATION',
    'departmentid' => 'DEPARTMENT',
    //'customerid' => 'CUSTOMERID',
    //'vendorid' => 'VENDORID',
    //'employeeid' => 'EMPLOYEEID',
    //'projectid' => 'PROJECTID',
    //'itemid' => 'ITEMID',
    //'classid' => 'CLASSID',
    'journalid' => 'GLBATCH.JOURNAL',
    'batchno' => 'GLBATCH.BATCHNO',
    'trx_amount' => 'TRX_AMOUNT',
    'exch_rate_date' => 'EXCH_RATE_DATE',
    'exch_rate_type_id' => 'EXCH_RATE_TYPE_ID',
    'exchange_rate' => 'EXCHANGE_RATE',
    'basecurrency' => 'BASECURR',
    'currency' => 'CURRENCY',
    'state' => 'STATE'
);

$recurglentry_structure = array(
    'trtype' => 'RECURGLENTRY.TRTYPE',
);
$locmember_structure = array(
    'locationid' => 'LOCATIONID',
    'name' => 'NAME'
);

$xmlmapping = array(
    'invoice' => array(
        'subelements' => array('lineitem'),
        'internalSubElements' => array('invoiceitems' => 'ITEMS'),
        'subelementEntities' => array('lineitem' => 'arinvoiceitem'),
        'intDataType' => 'struct',
        'aliasList' => array(
            'billtocontactname' => 'BILLTOPAYTO.NAME',
            'shiptocontactname' => 'SHIPTORETURNTO.NAME',
        ),
        'attr' => array(
            'key' => 'PRRECORD.RECORD#',
            'customerid' => 'CUSTOMER.CUSTOMERID',
            'datecreated' => 'PRRECORD.WHENCREATED',
            'dateposted' => 'PRBATCH.CREATED',
            'datedue' => 'PRRECORD.WHENDUE',
            'datepaid' => 'PRRECORD.WHENPAID',
            'termname' => 'TERMS.NAME',
            'batchkey' => 'PRRECORD.PRBATCHKEY',
            'invoiceno' => 'PRRECORD.RECORDID',
            'ponumber' => 'PRRECORD.DOCNUMBER',
            'totalamount' => 'PRRECORD.TOTALENTERED',
            'totalpaid' => 'PRRECORD.TOTALPAID',
            'totaldue' => 'PRRECORD.TOTALDUE',
            'totalselected' => 'PRRECORD.TOTALSELECTED',
            'description' => 'PRRECORD.DESCRIPTION',
            'trx_totalamount' => 'PRRECORD.TRX_TOTALENTERED',
            'trx_totalpaid' => 'PRRECORD.TRX_TOTALPAID',
            'trx_totaldue' => 'PRRECORD.TRX_TOTALDUE',
            'trx_totalselected' => 'PRRECORD.TRX_TOTALSELECTED',
            'basecurr' => 'PRRECORD.BASECURR',
            'currency' => 'PRRECORD.CURRENCY',
            'billto' => 'BILLTOCONTACTNAME',
            'shipto' => 'SHIPTOCONTACTNAME',
            'whenmodified' => 'PRRECORD.WHENMODIFIED',
            'state' => 'PRRECORD.STATE',
            'taxsolutionid' => 'TAXSOLUTION.SOLUTIONID',
            'retainagereleased' => 'PRRECORD.RETAINAGERELEASED'
        ),
        'nonQueryAttr' => array(
            'supdocid' => 'SUPDOCID',
            'externalurl' => 'EXTERNALURL',
        ),
        'markupFunction' => array
        (
            'whenmodified' => array(
                'type' => 'timestamp',
                'column' => 'PRRECORD.WHENMODIFIED',
                'alias' => 'WHENMODIFIED',
            )
        ),
        'permissionkey' => array('ar/activities/arbatch/view', 'ar/lists/arinvoice/view'),
        'intlevel' => array('invoiceitems'),
        'where' => array("prrecord.recordtype='ri'"),
        'invfkey' => array('PRRECORD.RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord',
        'entity' => 'arinvoice'
    ),
    'lineitem' => array(
        'attr' => array(
            'line_num' => 'LINE_NO',
            'accountlabel' => 'ACCOUNTLABELKEY',
            'glaccountno' => 'ACCOUNTKEY',
            'amount' => 'PRENTRY.AMOUNT',
            'memo' => 'DESCRIPTION',
            'locationid' => 'LOCATION#',
            'departmentid' => 'DEPT#',
            'key' => 'PRENTRY.RECORD#',
            'totalpaid' => 'PRENTRY.TOTALPAID',
            'totaldue' => '(PRENTRY.AMOUNT - PRENTRY.TOTALPAID - NVL(PRENTRY.AMOUNTRETAINED, 0)) as TOTALDUE',
            'trx_amount' => 'PRENTRY.TRX_AMOUNT',
            'trx_totalpaid' => 'PRENTRY.TRX_TOTALPAID',
            'trx_totaldue' => '(PRENTRY.TRX_AMOUNT - PRENTRY.TRX_TOTALPAID - NVL(PRENTRY.TRX_AMOUNTRETAINED, 0)) as TRX_TOTALDUE',
            'currency' => 'PRENTRY.CURRENCY',
            //'locationkey' => 'PRENTRY.LOCATION#',
            //'departmentkey' => 'PRENTRY.DEPT#',
            'projectkey' => 'PRENTRY.PROJECTDIMKEY',
            'customerkey' => 'PRENTRY.CUSTOMERDIMKEY',
            'vendorkey' => 'PRENTRY.VENDORDIMKEY',
            'employeekey' => 'PRENTRY.EMPLOYEEDIMKEY',
            'itemkey' => 'PRENTRY.ITEMDIMKEY',
            'classkey' => 'PRENTRY.CLASSDIMKEY',
            'contractkey' => 'PRENTRY.CONTRACTDIMKEY',
            'warehousekey' => 'PRENTRY.WAREHOUSEDIMKEY',
            'taskkey' => 'PRENTRY.TASKDIMKEY',
            'costtypekey' => 'PRENTRY.COSTTYPEDIMKEY',
            'billable' => 'PRENTRY.BILLABLE',
            'offsetglaccountno' => 'OFFSETACCOUNTKEY',
            'retainagepercentage' => 'RETAINAGEPERCENTAGE',
            'amountretained' => 'AMOUNTRETAINED',
            'trx_amountretained' => 'TRX_AMOUNTRETAINED',
            'primarydockey' => 'PRIMARYDOCKEY',
            'primarydoclinekey' => 'PRIMARYDOCLINEKEY',
        ),
        'where' => array("lineitem='T'"),
        'orderby' => "line_no",
        'table' => 'prentry',
        'fkey' => 'recordkey'
    ),
    'project' => array(
        'hasLines' => 1,
        'subelements' => array('projectresources'),
        'internalSubElements' => array('projectresources' => 'PROJECT_RESOURCES'),
        'intlevel' => array('projectresources'),
        'subelementEntities' => array('projectresources' => 'projectresources'),
        'intDataType' => 'struct',
        'fieldmap' => array(
            'key' => 'PROJECTID',
        ),
        'subobjects' => array('contactinfo', 'billto', 'shipto',),
        'permissionkey' => array('ar/lists/project/view'),
        'invfkey' => array('PROJECT.RECORD#'),
        'table' => 'project',
        'entity' => 'project',
        'useEntity' => true
    ),
    'projecttype' => array(
        'useEntity' => true,
        'entity' => 'projecttype',
        'fkey' => 'record#',
    ),
    'projectstatus' => array(
        'useEntity' => true,
        'entity' => 'projectstatus',
        'fkey' => 'record#',
    ),
    'projectresources' => array(
        'useEntity' => true,
        'intDataType' => 'struct',
        'entity' => 'projectresources',
        'fkey' => 'projectkey',
    ),
    'task' => array(
        'hasLines' => 1,
        'subelements' => array('taskresources'),
        'internalSubElements' => array('taskresources' => 'TASK_RESOURCES_INFO'),
        'intlevel' => array('taskresources'),
        'subelementEntities' => array('taskresources' => 'taskresources'),
        'intDataType' => 'struct',
        'fieldmap' => array(
            'key' => 'TASK.RECORD#',
        ),
        'permissionkey' => array('pa/lists/task/view'),
        'invfkey' => array('TASK.RECORD#'),
        'table' => 'task',
        'entity' => 'task',
        'useEntity' => true
    ),
    'taskresources' => array(
        'useEntity' => true,
        'intDataType' => 'struct',
        'entity' => 'taskresources',
        'fkey' => 'taskkey',
    ),
    'class' => array(
        'attr' => array(
            'key' => 'CLASSID',
            'name' => 'NAME',
            'description' => 'DESCRIPTION',
            'parentid' => 'PARENTID',
            'whenmodified' => 'WHENMODIFIED',
            'status' => 'STATUS',
        ),
        'markupFunction' => array
        (
            'whenmodified' => array(
                'type' => 'timestamp',
                'column' => 'WHENMODIFIED',
                'alias' => 'WHENMODIFIED',
            )
        ),
        'permissionkey' => array('co/lists/class/view'),
        'invfkey' => array('RECORD#'),
        'table' => 'class',
        'entity' => 'class',
        'useEntity' => true
    ),
    'timesheet' => array(
        'useEntity' => true,
        'subelements' => array('timesheetitems'),
        'internalSubElements' => array('timesheetitems' => 'TIMESHEETITEMS'),
        'intlevel' => array('timesheetitems'),
        'subelementEntities' => array('timesheetitems' => 'timesheetentry'),
        'intDataType' => 'struct',
        'fieldmap' => array(
            'key' => 'RECORDNO',
        ),
        'key' => 'key',
        'permissionkey' => array('pa/lists/timesheet/view'),
        'entity' => 'timesheet',
        'hasLines' => '1',
    ),
    'timetype' => array(
        'useEntity' => true,
        'entity' => 'timetype',
        'fkey' => 'record#',
    ),
    'earningtype' => array(
        'useEntity' => true,
        'entity' => 'earningtype',
        'fkey' => 'record#',
    ),
    'employeerate' => array(
        'useEntity' => true,
        'entity' => 'employeerate',
        'fkey' => 'record#',
    ),
    'timesheetitem' => array(
        'useEntity' => true,
        'intDataType' => 'struct',
        'orderby' => "lineno",
        'entity' => 'timesheetentry',
        'permissionkey' => array('pa/lists/timesheet/view'),
        'fkey' => 'timesheetkey',
    ),
    'bill' => array(
        'subelements' => array('lineitem'),
        'internalSubElements' => array('billitems' => 'ITEMS'),
        'subelementEntities' => array('lineitem' => 'apbillitem'),
        'intDataType' => 'struct',
        'aliasList' => array(
            'paytocontactname' => 'BILLTOPAYTO.NAME',
            'returntocontactname' => 'SHIPTORETURNTO.NAME',
        ),
        'attr' => array(
            'key' => 'PRRECORD.RECORD#',
            'vendorid' => 'VENDOR.VENDORID',
            'datecreated' => 'PRRECORD.WHENCREATED',
            'dateposted' => 'PRBATCH.CREATED',
            'datedue' => 'PRRECORD.WHENDUE',
            'datepaid' => 'PRRECORD.WHENPAID',
            'termname' => 'TERMS.NAME',
            'batchkey' => 'PRRECORD.PRBATCHKEY',
            'billno' => 'PRRECORD.RECORDID',
            'ponumber' => 'PRRECORD.DOCNUMBER',
            'totalamount' => 'PRRECORD.TOTALENTERED',
            'totalpaid' => 'PRRECORD.TOTALPAID',
            'totaldue' => 'PRRECORD.TOTALDUE',
            'totalselected' => 'PRRECORD.TOTALSELECTED',
            'onhold' => 'PRRECORD.ONHOLD',
            'description' => 'PRRECORD.DESCRIPTION',
            'trx_totalamount' => 'PRRECORD.TRX_TOTALENTERED',
            'trx_totalpaid' => 'PRRECORD.TRX_TOTALPAID',
            'trx_totaldue' => 'PRRECORD.TRX_TOTALDUE',
            'trx_totalselected' => 'PRRECORD.TRX_TOTALSELECTED',
            'basecurr' => 'PRRECORD.BASECURR',
            'currency' => 'PRRECORD.CURRENCY',
            'payto' => 'PAYTOCONTACTNAME',
            'returnto' => 'RETURNTOCONTACTNAME',
            'whenmodified' => "PRRECORD.WHENMODIFIED",
            'state' => 'PRRECORD.STATE',
            'inclusivetax' => 'PRRECORD.INCLUSIVETAX',
            'taxsolutionid' => 'TAXSOLUTION.SOLUTIONID',
            'retainagereleased' => 'PRRECORD.RETAINAGERELEASED'
        ), //Added for those fields which does not have foreign key relationship,
        'nonQueryAttr' => array(
            'supdocid' => 'SUPDOCID',
            'externalurl' => 'EXTERNALURL',
        ),
        'markupFunction' => array
        (
            'whenmodified' => array(
                'type' => 'timestamp',
                'column' => 'PRRECORD.WHENMODIFIED',
                'alias' => 'WHENMODIFIED',
            )
        ),
        'permissionkey' => array('ap/activities/apbatch/view', 'ap/lists/apbill/view'),
        'intlevel' => array('billitems'),
        'where' => array("prrecord.recordtype='pi'"),
        'invfkey' => array('PRRECORD.RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord',
        'entity' => 'apbill'
    ),
    'recurbill' => array
    (
        'subelements' => array('aprecurbillentry'),
        // ITEMS is the name of the key in the get array that has subelements and aprecurbillentry is the tag to be used in xml
        'internalSubElements' => array('aprecurbillentry' => 'ITEMS'),
        'entity' => 'aprecurbill',
        // intDataType is required so that xmlgw_router_2_1::GetEntityData() passes a data array to xmlgw_router_2_1::ConstructElement that isn't flat
        'intDataType' => 'struct',
        // as defined in class/common/security.inc
        'permissionkey' => array('ap/activities/apbatch/view', 'ar/lists/aprecurbill/view'),
        // tells xmlgw_router_2_1 to use the entity manager rather than making a custom query
        'useEntity' => true
    ),
    'recurinvoice' => array
    (
        'subelements' => array('arrecurinvoiceentry'),
        'internalSubElements' => array('arrecurinvoiceentry' => 'ITEMS'),
        'entity' => 'arrecurinvoice',
        'intDataType' => 'struct',
        'permissionkey' => array('ap/activities/arbatch/view', 'ar/lists/arrecurinvoice/view'),
        'useEntity' => true
    ),
    'billbatch' => array(
        'subelements' => array('bill'),
        'subelementfkey' => 'prbatchkey',
        'attr' => $batchdata,
        'permissionkey' => array('ap/activities/apbatch/view'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='pi'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'invoicebatch' => array(
        'subelements' => array('invoice'),
        'attr' => $batchdata,
        'permissionkey' => array('ar/activities/arbatch/view'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='ri'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'apadjustmentbatch' => array(
        'subelements' => array('apadjustment'),
        'attr' => $batchdata,
        'permissionkey' => array('ap/activities/apbatch/view'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='pa'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'aradjustmentbatch' => array(
        'subelements' => array('aradjustment'),
        'attr' => $batchdata,
        'permissionkey' => array('ar/activities/arbatch/view'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='ra'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'arpaymentbatch' => array(
        'subelements' => array('arpayment'),
        'subelementfkey' => 'prbatchkey',
        'attr' => array(
            'key' => 'RECORD#',
            'batchtitle' => 'TITLE',
            'datecreated' => 'CREATED',
            'bankaccountid' => 'ACCOUNTNOKEY',
            'open' => 'OPEN',
            'status' => 'STATUS',
            'totalamount' => 'TOTAL'
        ),
        'permissionkey' => array('ar/activities/arpaymentbatch/view'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='rp'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'apadjustment' => array(
        'subelements' => array('lineitemnoretainage'),
        'subelementslabel' => array('lineitem'),
        'attr' => array(
            'key' => 'PRRECORD.RECORD#',
            'vendorid' => 'PRRECORD.ENTITY',
            'datecreated' => 'PRRECORD.WHENCREATED',
            'dateposted' => 'PRBATCH.CREATED',
            'datepaid' => 'PRRECORD.WHENPAID',
            'batchkey' => 'PRRECORD.PRBATCHKEY',
            'adjustmentno' => 'PRRECORD.RECORDID',
            'billno' => 'PRRECORD.DOCNUMBER',
            'description' => 'PRRECORD.DESCRIPTION',
            'totalamount' => 'PRRECORD.TOTALENTERED',
            'totalpaid' => 'PRRECORD.TOTALPAID',
            'totaldue' => 'PRRECORD.TOTALDUE',
            'totalselected' => 'PRRECORD.TOTALSELECTED',
            'whenmodified' => "PRRECORD.WHENMODIFIED",
            'state' => 'PRRECORD.STATE',
            'basecurr' => 'PRRECORD.BASECURR',
            'currency' => 'PRRECORD.CURRENCY',
            'trx_totalamount' => 'PRRECORD.TRX_TOTALENTERED',
            'trx_totalpaid' => 'PRRECORD.TRX_TOTALPAID',
            'trx_totaldue' => 'PRRECORD.TRX_TOTALDUE',
            'trx_totalselected' => 'PRRECORD.TRX_TOTALSELECTED',
            'inclusivetax' => 'PRRECORD.INCLUSIVETAX',
            'taxsolutionid' => 'TAXSOLUTION.SOLUTIONID',
        ),
        'markupFunction' => array
        (
            'whenmodified' => array(
                'type' => 'timestamp',
                'column' => 'PRRECORD.WHENMODIFIED',
                'alias' => 'WHENMODIFIED',
            )
        ),
        'permissionkey' => array('ap/activities/apbatch/view', 'ap/lists/apadjustment/view'),
        'intlevel' => array('apadjustmentitems'),
        'where' => array("prrecord.recordtype='pa'"),
        'invfkey' => array('PRRECORD.RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord',
        'entity' => 'apadjustment'
    ),
    'aradjustment' => array(
        'subelements' => array('lineitemnoretainage'),
        'subelementslabel' => array('lineitem'),
        'attr' => array(
            'key' => 'PRRECORD.RECORD#',
            'customerid' => 'PRRECORD.ENTITY',
            'datecreated' => 'PRRECORD.WHENCREATED',
            'dateposted' => 'PRBATCH.CREATED',
            'datepaid' => 'PRRECORD.WHENPAID',
            'batchkey' => 'PRRECORD.PRBATCHKEY',
            'adjustmentno' => 'PRRECORD.RECORDID',
            'invoiceno' => 'PRRECORD.DOCNUMBER',
            'description' => 'PRRECORD.DESCRIPTION',
            'totalamount' => 'PRRECORD.TOTALENTERED',
            'totalpaid' => 'PRRECORD.TOTALPAID',
            'totaldue' => 'PRRECORD.TOTALDUE',
            'totalselected' => 'PRRECORD.TOTALSELECTED',
            'whenmodified' => "PRRECORD.WHENMODIFIED",
            'state' => 'PRRECORD.STATE',
            'basecurr' => 'PRRECORD.BASECURR',
            'currency' => 'PRRECORD.CURRENCY',
            'trx_totalamount' => 'PRRECORD.TRX_TOTALENTERED',
            'trx_totalpaid' => 'PRRECORD.TRX_TOTALPAID',
            'trx_totaldue' => 'PRRECORD.TRX_TOTALDUE',
            'trx_totalselected' => 'PRRECORD.TRX_TOTALSELECTED',
            'taxsolutionid' => 'TAXSOLUTION.SOLUTIONID',
        ),
        'markupFunction' => array
        (
            'whenmodified' => array(
                'type' => 'timestamp',
                'column' => 'PRRECORD.WHENMODIFIED',
                'alias' => 'WHENMODIFIED',
            )
        ),
        'permissionkey' => array('ar/activities/arbatch/view', 'ar/lists/aradjustment/view'),
        'intlevel' => array('aradjustmentitems'),
        'where' => array("prrecord.recordtype='ra'"),
        'invfkey' => array('PRRECORD.RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord',
        'entity' => 'aradjustment'
    ),
    'appayment' => array(
        'subelements' => array('appaymentitem'),
        'intlevel' => array('appaymentitems'),
        'attr' => array(
            'key' => 'PRRECORD.RECORD#',
            'vendorid' => 'VENDOR.VENDORID',
            'paymentamount' => 'PRRECORD.TOTALENTERED',
            'refid' => 'PRRECORD.DOCNUMBER',
            'paymentdate' => 'PRRECORD.WHENPAID',
            'paymentmethod' => 'IAPAYMETHOD.NAME',
            'financialentity' => 'PRRECORD.FINANCIALENTITY',
            'transactionstate' => 'PRRECORD.STATE',
            'paymenttrxamount' => 'PRRECORD.TRX_TOTALENTERED',
            'currency' => 'PRRECORD.CURRENCY',
            'whenmodified' => "PRRECORD.WHENMODIFIED",
            'cleared' => 'PRRECORD.CLEARED',
            'cleareddate' => 'PRRECORD.CLRDATE'
            // NOTE: date formatting for this type of list is defined/specified in xmlgw_getlist.2.1.inc  TM 02.08.08
        ),
        'markupFunction' => array
        (
            'whenmodified' => array(
                'type' => 'timestamp',
                'column' => 'PRRECORD.WHENMODIFIED',
                'alias' => 'WHENMODIFIED',
            )
        ),
        'permissionkey' => array('ap/lists/appostedpayment'),
        'where' => array("prrecord.RECORDTYPE IN ('pp', 'pr', 'cp', 'cw', 'ck', 'cx', 'ci')"),
        'invfkey' => array('PRRECORD.RECORD#'),
        'table' => 'prrecord'
    ),
    'appaymentitem' => array(
        'attr' => array(
            'billkey' => 'INVOICEMST.RECORD#',
            'billno' => 'INVOICEMST.RECORDID',
            'lineitemkey' => 'PRENTRYPYMTRECS.PAIDITEMKEY',
            'glaccountno' => 'INVOICEITEM.ACCOUNTKEY',
            'description' => 'INVOICEITEM.DESCRIPTION',
            'amount' => 'PRENTRYPYMTRECS.AMOUNT',
            'departmentid' => 'INVOICEITEM.DEPT#',
            'locationid' => 'INVOICEITEM.LOCATION#',
            'trx_amount' => 'PRENTRYPYMTRECS.TRX_AMOUNT',
            'currency' => 'PRENTRYPYMTRECS.CURRENCY',
            'state' => 'PRENTRYPYMTRECS.STATE',
        ),
        'fkey' => 'PRENTRYPYMTRECS.PAYMENTKEY',
        'where' => array("invoiceitem.recordkey = invoicemst.record#"),
        'table' => 'prentrypymtrecs'
    ),
    'arpayment' => array(
        'subelements' => array('arpaymentitem', 'arpaymentitemdetail', 'lineitemnoretainage'),
        'subelementslabel' => array('arpaymentitem', 'arpaymentitemdetail', 'lineitem'),
        'intlevel' => array('arpaymentitems', 'arpaymentitemdetails', 'lineitemsnoretainage'),
        'intlevellabel' => array('arpaymentitems', 'arpaymentitemdetails', 'lineitems'),
        'attr' => array(
            'customerid' => 'CUSTOMER.CUSTOMERID',
            'paymentamount' => 'PRRECORD.TOTALENTERED',
            'paymentapplied' => 'PRRECORD.TOTALPAID',
            'paymentunapplied' => 'PAYMENTUNAPPLIED',
            'batchkey' => 'PRRECORD.PRBATCHKEY',
            'refid' => 'PRRECORD.DOCNUMBER',
            'state' => 'PRRECORD.STATE',
            'datereceived' => 'PRRECORD.WHENCREATED',
            'key' => 'PRRECORD.RECORD#',
            'batchtitle' => 'PRBATCH.TITLE',
            'paymentmethod' => 'IAPAYMETHOD.NAME',
            'undepglaccountno' => 'GLACCOUNT.ACCT_NO',
            'undepaccountlabel' => 'ACCOUNTLABEL.LABEL',
            'undeposited' => 'UNDEPOSITED',
            'whenmodified' => "PRRECORD.WHENMODIFIED"
        ),
        'markupFunction' => array
        (
            'whenmodified' => array(
                'type' => 'timestamp',
                'column' => 'PRRECORD.WHENMODIFIED',
                'alias' => 'WHENMODIFIED',
            )
        ),
        'aliasList' => array('paymentunapplied' => '(PRRECORD.TOTALENTERED - PRRECORD.TOTALPAID)',
            'undeposited' => "case when (prrecord.parentpayment is null and prbatch.UNDEPFUNDSACCT is not null) then 'true' else 'false' end"),
        'sqlhint' => '/*+ NO_INDEX(PRRECORD IX_PRRECORD_PARENTPAYMENT) */',
        'permissionkey' => array('ar/activities/arpaymentbatch/view'),
        'where' => array("PRRECORD.RECORDTYPE IN ('rp', 'rr')"),
        'invfkey' => array('PRRECORD.RECORD#', 'PRRECORD.RECORD#', 'PRRECORD.RECORD#'),
        'fkey' => 'prbatchkey,paymethodkey, prbatch.undepfundsacct,prbatch.undepfundsaclabel',
        'table' => 'prrecord'
    ),
    'arpaymentitem' => array(
        'attr' => array(
            'invoicekey' => 'RECORDKEY',
            'amount' => 'AMOUNT'
        ),
        'fkey' => 'PRENTRYPYMTRECS.PAYMENTKEY',
        'table' => 'prentrypymtrecs'
    ),
    'arpaymentitemdetail' => array(
        'attr' => array(
            'arpaymentitemkey' => 'PAYITEMKEY',
            'invoicelineitemkey' => 'PAIDITEMKEY',
            'amount' => 'AMOUNT'
        ),
        'fkey' => 'PRENTRYPYMTRECS.PAYMENTKEY',
        'table' => 'prentrypymtrecs'
    ),
    'glbudget' => array(
        'subelements' => array('glbudgetitems'),
        'internalSubElements' => array('glbudgetitems' => 'glbudgetitem'),
        'fieldmap' => array(
            'budgetid' => 'BUDGETID',
            'description' => 'DESCRIPTION',
            'budgetdefault' => 'DEFAULT_BUDGET',
            'whenmodified' => 'MODIFIED',
            'userid' => 'USER',
            'status' => 'STATUS',
            'megadepartmentid' => 'MEGADEPARTMENTID',
            'megalocationid' => 'MEGALOCATIONID',
        ),
        'key' => 'budgetid',
        'table' => 'budgetheader',
        'entity' => 'budgetheader',
        'permissionkey' => array('gl/lists/budgetheader', 'gl/lists/glbook/view'),
        'useEntity' => true,
        'hasLines' => '1'
    ),
    'glbudgetitem' => array(
        'permissionkey' => array('gl/lists/glbudget'),
        'listMethod' => 'GetList',
        'entity' => 'glbudget',
        'useEntity' => true,
    ),
    'customerachinfo' => array(
        'entity' => 'customerachinfo',
        'fieldmap' => array('bankaccounttype' => 'ACCOUNTTYPE'),
        'useEntity' => true,
        'permissionkey' => array('ar/lists/customer'),
    ),
    'customerchargecard' => array(
        'entity' => 'customercreditcard',
        'useEntity' => true,
        'subobjects' => array('mailaddress'),
        'permissionkey' => array('ar/lists/customercreditcard/view'),
    ),
    'customerbankaccount' => array(
        'entity' => 'customerbankaccount',
        'useEntity' => true,
        'subobjects' => array('mailaddress'),
        'permissionkey' => array('ar/lists/customerbankaccount'),
    ),
    'territory' => array(
        'attr' => array(
            'territoryid' => 'TERRITORYID',
            'name' => 'NAME',
            'parentid' => 'PARENT.TERRITORYID',
            'managerid' => 'MANAGER.EMPLOYEEID',
            'status' => 'STATUS'),
        'permissionkey' => 'co/lists/territory/view',
        'entity' => 'territory',
        'useEntity' => true,
        'key' => 'territoryid',
    ),
    'location' => array(
        'subobjects' => array('primary', 'shipto'),
        'permissionkey' => array('co/lists/location/view', 'gl/lists/glbook/view'),
        'entity' => 'location',
        'useEntity' => true,
    ),
    'locationgroup' => array(
        'subelements' => array('members'),
        'internalSubElements' => array('members' => 'MEMBERS'),
        'listMethod' => 'GetListFull',
        'entity' => 'locationgroup',
        'useEntity' => true,
        'permissionkey' => array('co/lists/locationgroup'),
        'hasLines' => '1'
    ),
    'locationentity' => array(
        'entity' => 'locationentity',
        'useEntity' => true,
        'permissionkey' => array('co/lists/locationentity', 'gl/lists/glbook/view'),
    ),
    'supdocfolder' => array(
        'entity' => 'supportingdocumentgrps',
        'useEntity' => true,
        'permissionkey' => array('co/lists/supportingdocumentgrps/view'),
    ),
    'attachment' => array(
        'attr' => array(
            'attachmentname' => 'ATTACHMENTNAME',
            'attachmenttype' => 'ATTACHMENTTYPE',
            'attachmentdata' => 'ATTACHMENTDATA',
        ),
    ),
    'supdoc' => array(
        'table' => 'supdoc',
        'entity' => 'supportingdocuments',
        'useEntity' => true,
        'permissionkey' => array('co/lists/supportingdocuments/view'),
        'subelements' => array('attachments'),
        'internalSubElements' => array('attachments' => 'ATTACHMENTS'),
        'hasLines' => '1',
        'fieldmap' => array(
            'recordno' => 'RECORDNO',
            'supdocid' => 'DOCUMENTID',
            'supdocname' => 'DOCUMENTNAME',
            'folder' => 'GROUPKEY',
            'description' => 'DESCRIPTION',
            'creationdate' => 'CREATED',
            'createdby' => 'CREATEDBY',
        ),
    ),
    'exchangeratetypes' => array(
        'entity' => 'exchangeratetypes',
        'useEntity' => true,
    ),
    'csnhistory' => array(
        'entity' => 'csnhistory',
        'useEntity' => true,
    ),
    'company_info' => array(
        'attr' => array(
            'title' => 'COMPANY.TITLE',
            'name' => 'COMPANY.NAME'),
        'table' => 'company',
        'where' => array("company.record#=" . GetMyCompany()),
        'entity' => 'company',
        'permissionkey' => array('mp/setup/company_info', 'co/setup/company_info'),
    ),
    'trxcurrencies' => array(
        'entity' => 'trxcurrencies',
        'useEntity' => true,
        'permissionkey' => array('co/lists/trxcurrencies'),
    ),
    'contact' => array(
        'key' => 'contactname',
        'entity' => 'contact',
        'useEntity' => true,
        'subobjects' => array('mailaddress'),
        'permissionkey' => array('co/lists/contact', 'mp/lists/contact', 'so/lists/contact', 'po/lists/contact'),
    ),
    'mailaddress' => $mailaddress,
    'department' => array(
        'permissionkey' => array('co/lists/department/view', 'gl/lists/glbook/view'),
        'entity' => 'department',
        'useEntity' => true,
    ),
    'employee' => array(
        'permissionkey' => array('co/lists/employee/view'),
        'key' => 'employeeid',
        'entity' => 'employee',
        'useEntity' => true,
        'subobjects' => array('personalinfo'),
    ),
    'vendor' => array(
        'fieldmap' => array(
            'glaccountno' => 'APACCOUNT',
            'form1099' => 'FORM1099BOX'
        ),
        'subobjects' => array('contactinfo', 'payto', 'returnto', 'contactto1099', 'primary', 'visibility'),
        'useEntity' => true,
        'key' => 'vendorid',
        'permissionkey' => array('ap/lists/vendor/view'),
        'entity' => 'vendor',
    ),
    'vendorvisibility' => array(
        'useEntity' => true,
        'key' => 'vendorid',
        'permissionkey' => array('ap/lists/vendorvisibility/view'),
        'entity' => 'vendorvisibility',
    ),
    'vendorpref' => array(
        'attr' => array(
            'key' => 'VENDORPREF.RECORD#',
            'vendorid' => 'VENDORPREF.VENDORID',
            'property' => 'VENDORPREF.PROPERTY',
            'value' => 'VENDORPREF.VALUE',
            'locationkey' => 'VENDORPREF.LOCATIONKEY'
        ),
        'table' => 'vendorpref',
        'permissionkey' => array('ap/lists/vendor/view'),
    ),
    'employeepref' => array(
        'attr' => array(
            'key' => 'EMPLOYEEPREF.RECORD#',
            'employeeid' => 'EMPLOYEEPREF.EMPLOYEEID',
            'property' => 'EMPLOYEEPREF.PROPERTY',
            'value' => 'EMPLOYEEPREF.VALUE',
            'locationkey' => 'EMPLOYEEPREF.LOCATIONKEY'
        ),
        'table' => 'employeepref',
        'permissionkey' => array('co/lists/employee/view'),
    ),
    'vendorentityaccount' => array(
        'useEntity' => true,
        'key' => 'vendorid',
        'permissionkey' => array('ap/lists/vendor/view'),
        'entity' => 'vendoracctnoloc',
    ),
    'customer' => array(
        'key' => 'customerid',
        'fieldmap' => array('glaccountno' => 'ARACCOUNT', 'contactlist' => 'RECORDNO', 'deliveryoptions' => 'DELIVERY_OPTIONS'),
        'useEntity' => true,
        'subobjects' => array('contactinfo', 'billto', 'shipto', 'primary', 'visibility'),
        'permissionkey' => array('ar/lists/customer/view'),
        'entity' => 'customer',
    ),
    'customervisibility' => array(
        'useEntity' => true,
        'key' => 'customerid',
        'permissionkey' => array('ar/lists/customervisibility/view'),
        'entity' => 'customervisibility',
    ),
    'customerppackage' => array(
        'useEntity' => true,
        'permissionkey' => array('co/setup/package/view'),
        'entity' => 'package',
    ),
    'apaccountlabel' => array(
        'permissionkey' => array('ap/lists/apaccountlabel/view'),
        'entity' => 'apaccountlabel',
        'useEntity' => true,
    ),
    'araccountlabel' => array(
        'permissionkey' => array('ar/lists/araccountlabel/view'),
        'entity' => 'araccountlabel',
        'useEntity' => true,
    ),
    'journal' => array(
        'permissionkey' => array('gl/lists/journal/view'),
        'entity' => 'journal',
        'useEntity' => true,
    ),
    'adjjournal' => array(
        'permissionkey' => array('gl/lists/adjjournal/view'),
        'entity' => 'adjjournal',
        'useEntity' => true,
    ),
    'statjournal' => array(
        'permissionkey' => array('gl/lists/statjournal/view'),
        'entity' => 'statjournal',
        'useEntity' => true,
    ),
    'glaccount' => array(
        'listMethod' => 'GetListFull',
        'useEntity' => true,
        'permissionkey' => array('gl/lists/glaccount/view', 'gl/lists/glbook/view'),
        'entity' => 'glaccount',
        'fieldmap' => array('glaccountno' => 'accountno')
    ),
    'statglaccount' => array(
        'listMethod' => 'GetListFull',
        'useEntity' => true,
        'permissionkey' => array('gl/lists/stataccount/view'),
        'entity' => 'stataccount',
        'fieldmap' => array('glaccountno' => 'accountno')
    ),
    'apterm' => array(
        'useEntity' => true,
        'permissionkey' => array('ap/lists/apterm/view'),
        'entity' => 'apterm'
    ),
    'arterm' => array(
        'useEntity' => true,
        'permissionkey' => array('ar/lists/arterm/view'),
        'entity' => 'arterm'
    ),
    'expensereport' => array(
        'subelements' => array('expense', 'approvalinfo'),
        'attr' => array(
            'key' => 'RECORD#',
            'employeeid' => 'ENTITY',
            'datecreated' => 'WHENCREATED',
            'datepaid' => 'WHENPAID',
            'batchkey' => 'PRBATCHKEY',
            'expensereportno' => 'RECORDID',
            'description' => 'DESCRIPTION',
            'status' => 'STATE',
            'basecurr' => 'BASECURR',
            'currency' => 'CURRENCY',
            'totalamount' => 'TOTALENTERED',
            'totalpaid' => 'TOTALPAID',
            'totaldue' => 'TOTALDUE',
            'trx_totalamount' => 'TRX_TOTALENTERED',
            'trx_totalpaid' => 'TRX_TOTALPAID',
            'trx_totaldue' => 'TRX_TOTALDUE',
        ),
        'nonQueryAttr' => array(
            'supdocid' => 'SUPDOCID',
        ),
        'permissionkey' => array('ee/activities/myexpenses/view'),
        'intlevel' => array('expenses', 'approvalhistory'),
        'where' => array("recordtype='ei'"),
        'invfkey' => array('RECORD#', 'RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord'
    ),
    'expenseadjustments' => array(
        'subelements' => array('expense'),
        'attr' => array(
            'key' => 'RECORD#',
            'employeeid' => 'ENTITY',
            'datecreated' => 'WHENCREATED',
            'datepaid' => 'WHENPAID',
            'batchkey' => 'PRBATCHKEY',
            'expensereportno' => 'RECORDID',
            'description' => 'DESCRIPTION',
            'basecurr' => 'BASECURR',
            'currency' => 'CURRENCY',
            'totalamount' => 'TOTALENTERED',
            'totalpaid' => 'TOTALPAID',
            'totaldue' => 'TOTALDUE',
            'trx_totalamount' => 'TRX_TOTALENTERED',
            'trx_totalpaid' => 'TRX_TOTALPAID',
            'trx_totaldue' => 'TRX_TOTALDUE',
        ),
        'nonQueryAttr' => array(
            'supdocid' => 'SUPDOCID',
        ),
        'permissionkey' => array('ee/lists/expenseadjustments/view'),
        'intlevel' => array('expenses'),
        'where' => array("recordtype='ea'"),
        'invfkey' => array('RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord'
    ),
    'expenseadjustmentreport' => array(
        'subelements' => array('expense'),
        'attr' => array(
            'key' => 'RECORD#',
            'employeeid' => 'ENTITY',
            'datecreated' => 'WHENCREATED',
            'datepaid' => 'WHENPAID',
            'batchkey' => 'PRBATCHKEY',
            'expensereportno' => 'RECORDID',
            'description' => 'DESCRIPTION',
            'basecurr' => 'BASECURR',
            'currency' => 'CURRENCY',
            'totalamount' => 'TOTALENTERED',
            'totalpaid' => 'TOTALPAID',
            'totaldue' => 'TOTALDUE',
            'trx_totalamount' => 'TRX_TOTALENTERED',
            'trx_totalpaid' => 'TRX_TOTALPAID',
            'trx_totaldue' => 'TRX_TOTALDUE',
        ),
        'nonQueryAttr' => array(
            'supdocid' => 'SUPDOCID',
        ),
        'permissionkey' => array('ee/lists/expenseadjustments/view'),
        'intlevel' => array('expenses'),
        'where' => array("recordtype='ea'"),
        'invfkey' => array('RECORD#'),
        'fkey' => 'prbatchkey',
        'table' => 'prrecord'
    ),
    'expensereportbatch' => array(
        'subelements' => array('expensereport'),
        'attr' => $batchdata,
        'permissionkey' => array('ee/activities/eebatch/view'),
        'choiceattr' => array('open'),
        'where' => array("recordtype='ei'"),
        'invfkey' => array('RECORD#'),
        'table' => 'prbatch'
    ),
    'expense' => array(
        'subelements' => array('expenseexchinfo'),
        'attr' => array(
            'line_num' => 'LINE_NO',
            'expensetype' => 'ACCOUNTLABELKEY',
            'glaccountno' => 'ACCOUNTKEY',
            'amount' => 'AMOUNT',
            'trx_amount' => 'TRX_AMOUNT',
            'expensedate' => 'ENTRY_DATE',
            'memo' => 'DESCRIPTION',
            'form1099' => 'FORM1099',
            'locationid' => 'LOCATION#',
            'departmentid' => 'DEPT#',
            //'locationkey' => 'PRENTRY.LOCATION#',
            //'departmentkey' => 'PRENTRY.DEPT#',
            'projectkey' => 'PRENTRY.PROJECTDIMKEY',
            'taskkey' => 'PRENTRY.TASKDIMKEY',
            'costtypekey' => 'PRENTRY.COSTTYPEDIMKEY',
            'customerkey' => 'PRENTRY.CUSTOMERDIMKEY',
            'vendorkey' => 'PRENTRY.VENDORDIMKEY',
            'employeekey' => 'PRENTRY.EMPLOYEEDIMKEY',
            'itemkey' => 'PRENTRY.ITEMDIMKEY',
            'classkey' => 'PRENTRY.CLASSDIMKEY',
            'contractkey' => 'PRENTRY.CONTRACTDIMKEY',
            'warehousekey' => 'PRENTRY.WAREHOUSEDIMKEY',
            'billable' => 'PRENTRY.BILLABLE',
            'exppmttypekey' => 'EXPPMTTYPEKEY',
            'nonreimbursable' => 'NONREIMBURSABLE',
            'quantity' => 'QUANTITY',
            'rate' => 'UNITRATE',
        ),
        'table' => 'prentry',
        'invfkey' => array('RECORD#'),
        'fkey' => 'recordkey'
    ),
    'expenseexchinfo' => array(
        'attr' => array(
            'currency' => 'CURRENCY',
            'amount' => 'AMOUNT',
            'exch_rate_date' => 'EXCH_RATE_DATE',
            'exch_rate_type_id' => 'EXCH_RATE_TYPE_ID',
            'exchange_rate' => 'EXCHANGE_RATE',
            'user_exchrate' => 'USER_EXCHRATE',
        ),
        'table' => 'prentrycurrdetail',
        'fkey' => 'prentrykey'
    ),
    'approvalinfo' => array(
        'attr' => array(
            'comments' => 'COMMENTS',
        ),
        'table' => 'approvalhistory',
        'fkey' => 'record#'
    ),
    'expensetypes' => array(
        'fieldmap' => array(
            'expensetype' => 'accountlabel'
        ),
        'subelements' => array('unitrates'),
        'internalSubElements' => array('unitrates' => 'UNIT_RATES'),
        'intlevel' => array('unitrates'),
        'subelementEntities' => array('unitrates' => 'unitrate'),
        'useEntity' => true,
        'permissionkey' => array('ee/lists/eeaccountlabel/view'),
        'entity' => 'eeaccountlabel',
        'hasLines' => '1',
        'intDataType' => 'struct',
    ),
    'unitrate' => array(
        'useEntity' => true,
        'intDataType' => 'struct',
        'entity' => 'unitrate',
        'fkey' => 'expensetypekey',
        'permissionkey' => array('ee/lists/eeaccountlabel/view'),
    ),
    'expensepaymenttype' => array(
        'fieldmap' => array(
            'key' => 'RECORDNO',
            'expensepaymenttype' => 'expensepaymenttype',
            'name' => 'NAME',
            'description' => 'DESCRIPTION',
            'nonreimbursable' => 'NONREIMBURSABLE',
            'offsetacctno' => 'OFFSETACCTNO',
            'status' => 'STATUS',
        ),
        'useEntity' => true,
        'permissionkey' => array('ee/lists/expensepaymenttype/view'),
        'entity' => 'expensepaymenttype'
    ),
    'reportingperiod' => array(
        'fieldmap' => array(
            'headingtitle' => 'HEADER1',
            'headingtitle2' => 'HEADER2',
            'budgetable' => 'BUDGETING',
            'startdate' => 'START_DATE',
            'enddate' => 'END_DATE'
        ),
        'useEntity' => true,
        'permissionkey' => array('co/lists/reportingperiod/view', 'gl/lists/glbook/view'),
        'entity' => 'glbudgettype'
    ),
    'accountgroup' => array(
        'useEntity' => true,
        'permissionkey' => array('gl/lists/glacctgrp/view', 'gl/lists/glbook/view'),
        'entity' => 'glacctgrp',
        'fieldmap' => array(
            'accountgroupname' => 'NAME',
            'headingtitle' => 'TITLE',
            'normalbalance' => 'NORMAL_BALANCE',
            'memberstype' => 'MEMBERTYPE',
        ),
    ),
    'bankaccount2' => array(
        'subelements' => array('checklayout', 'mailaddress'),
        'attr' => array(
            'bankaccountid' => 'ACCOUNTID',
            'bankaccountno' => 'ACCOUNTNO',
            'glaccountno' => 'GLACCOUNTKEY',
            'bankname' => 'NAME',
            'routingno' => 'ROUTINGNO',
            'branchid' => 'BRANCHID',
            'bankaccounttype' => 'TYPE',
            'phone' => 'PHONE',
            'nextcheck' => 'CHECK_CURRNO',
            'status' => 'STATUS',
            'lastreconciledbalance' => 'RECBAL',
            'lastreconcileddate' => 'RECDATE',
            'currency' => 'CURRENCY'
        ),
        'permissionkey' => array('cm/lists/checkingaccount/view'),
        'invfkey' => array('CHECKLAYOUTKEY', 'MAILADDRKEY'),
        'table' => 'bankaccount'
    ),
    'bankaccount' => array(
        'useEntity' => true,
        'permissionkey' => array('cm/lists/checkingaccount/view'),
        'entity' => 'bankaccount'
    ),
    'checklayout' => array(
        'attr' => array(
            'checkpaperformat' => 'PAPERFORMAT',
            'signaturesrequired' => 'NUM_SIGLINES'
        ),
        'table' => 'checklayout',
        'fkey' => 'RECORD#'
    ),
    'gltransaction' => array(
        'fieldmap' => array(
            'key' => 'RECORDNO',
            'journalid' => 'JOURNAL',
            'batchno' => 'BATCHNO',
            'datecreated' => 'BATCH_DATE',
            'description' => 'BATCH_TITLE',
            'sourceentity' => 'BASELOCATION_NO',
            'datemodified' => 'MODIFIED',
            'state' => 'STATE'
        ),
        'subelements' => array('gltransactionentries'),
        'internalSubElements' => array('gltransactionentries' => 'ENTRIES'),
        'useEntity' => true,
        'table' => 'glbatch',
        'entity' => 'glbatch',
        'listMethod' => 'GetListFull',
        'permissionkey' => array('gl/lists/journal'),
        'hasLines' => '1'
    ),
    'glentry' => array(
        'useEntity' => true,
        'fieldmap' => $glentry_structure,
        'table' => 'glentry',
        'permissionkey' => array('gl/lists/journal'),
        'entity' => 'glentry',
    ),
    'financialentity' => array(
        'useEntity' => true,
        'permissionkey' => array(
            'cm/lists/checkingaccount',
            'cm/lists/creditcard',
            'cm/lists/savingsaccount'
        ),
        'entity' => 'financialentity',
        'fieldmap' => array(
            'finentid' => 'ID',
            'finentacctno' => 'ACCTNO',
            'finentname' => 'NAME',
            'finenttype' => 'TYPE',
            'glaccountno' => 'GLACCOUNTNO',
            'currency' => 'CURRENCY'
        )
    ),
    'stkittransaction' => array(
        'useEntity' => true,
        'entity' => 'stkitdocument',
        'listMethod' => 'GetListFull_OLD',
        'hasLines' => 1,
        'subelements' => array('stkittransitems'),
        'intlevel' => array('stkittransitems'),
        'subelementEntities' => array('stkittransitems' => 'stkitdocumententry'),
        'internalSubElements' => array('stkittransitems' => 'ENTRIES'),
        'permissionkey' => array('inv/lists/stkitdocument'),
        'fieldmap' => array(
            'key' => 'RECORDNO',
            'transactiontype' => 'DOCPARID',
            'transactionid' => 'DOCID',
            'datecreated' => 'WHENCREATED',
            'createdfrom' => 'CREATEDFROM',
            'referenceno' => 'PONUMBER',
            'message' => 'MESSAGE',
            'transactionstate' => 'STATE',
        )
    ),
    'ictransaction' => array(
        'useEntity' => true,
        'entity' => 'invdocument',
        'listMethod' => 'GetListFull_OLD',
        'hasLines' => 1,
        'subelements' => array('ictransitems', 'subtotals'),
        'intlevel' => array('ictransitems'),
        'subelementEntities' => array('ictransitems' => 'invdocumententry'),
        'internalSubElements' => array('ictransitems' => 'ENTRIES', 'subtotals' => 'SUBTOTALS'),
        'permissionkey' => array('inv/lists/invdocument'),
        'fieldmap' => array(
            'key' => 'RECORDNO',
            'transactiontype' => 'DOCPARID',
            'transactionid' => 'DOCID',
            'documentnumber' => 'DOCNO',
            'datecreated' => 'WHENCREATED',
            'createdfrom' => 'CREATEDFROM',
            'referenceno' => 'PONUMBER',
            'message' => 'MESSAGE',
            'transactionstate' => 'STATE',
        )
    ),
    // NOTE: subelement fields for GetListFull_OLD must also be defined in app/source/inventory/DocumentManager.cls  TM 02.13.08
    'sotransaction' => array(
        'useEntity' => true,
        'entity' => 'sodocument',
        'listMethod' => 'GetListFull_OLD',
        'hasLines' => 1,
    //uncomment below lines with GA of PDLC-0531-13
    'hasLinesSubElement' => 1,
    'subelements'    => array ('sotransitems', 'subtotals'),
    'intlevel'        => array('sotransitems'),
    'subelementEntities' => array ('sotransitems' => 'sodocumententry'),
    'internalSubElements'    => array ('sotransitems' => 'ENTRIES', 'subtotals' => 'SUBTOTALS'),
        'lineElements' => array('sotransitem' => 'linesubtotals'),
        'internalLineElements' => array('linesubtotals' => 'SUBTOTALSENTRY'),
        'subobjects' => array('shipto', 'billto'),
        'permissionkey' => array('so/lists/sodocument'),
        'fieldmap' => array(
            'key' => 'RECORDNO',
            'prrecordkey' => 'PRRECORDKEY',
            'totalamount' => 'TOTAL',
            'customerid' => 'CUSTVENDID',
            'datedue' => 'WHENDUE',
            'termname' => 'TERM.NAME',
            'transactionstate' => 'STATE',
            'transactionid' => 'DOCID',
            'documentnumber' => 'DOCNO',
            'datecreated' => 'WHENCREATED',
            'dateposted' => 'WHENPOSTED',
            'retainageheld' => 'RETAINAGEHELD',
            'retainagebilled' => 'RETAINAGEBILLED',
            'retainagebalance' => 'RETAINAGEBALANCE',
            'totallessretainageheld' => 'TOTALLESSRETAINAGEHELD',
            'transactiontype' => 'DOCPARID',
            'referenceno' => 'PONUMBER',
            'shippingmethod' => 'SHIPVIA',
            'shipto' => 'SHIPTO.CONTACTNAME',
            'billto' => 'BILLTO.CONTACTNAME',
            'supdocid' => 'SUPDOCID',
            'vsoepricelist' => 'VSOEPRICELIST',
            'project' => 'PROJECT',
            'changelognumber' => 'CHANGELOGNUMBER',
            'trx_totalpaid' => 'TRX_TOTALPAID',
            'totalpaid' => 'TOTALPAID',
            'trx_totalentered' => 'TRX_TOTALENTERED',
            'totalentered' => 'TOTALENTERED',
            'trx_totaldue' => 'TRX_TOTALDUE',
            'totaldue' => 'TOTALDUE',
            'paymentstatus' => 'PAYMENTSTATUS',
            'exchratedate' => 'EXCHRATEDATE',
            'exchratetype' => 'EXCHRATETYPES.NAME',
            'exchratetypeid' => 'EXCH_RATE_TYPE_ID',
            'exchrate' => 'EXCHRATE',
            'needbydate' => 'NEEDBYDATE',
            'shipbydate' => 'SHIPBYDATE',
            'cancelafterdate' => 'CANCELAFTERDATE',
            'donotshipbeforedate' => 'DONOTSHIPBEFOREDATE',
            'donotshipafterdate' => 'DONOTSHIPAFTERDATE',
            'servicedeliverydate' => 'SERVICEDELIVERYDATE',
            'trackingnumber' => 'TRACKINGNUMBER',
            'shippeddate' => 'SHIPPEDDATE',
            'customerponumber' => 'CUSTOMERPONUMBER',
            'scope' => 'SCOPE',
            'inclusions' => 'INCLUSIONS',
            'exclusions' => 'EXCLUSIONS',
            'terms' => 'TERMS',
            'schedulestartdate' => 'SCHEDULESTARTDATE',
            'actualstartdate' => 'ACTUALSTARTDATE',
            'scheduledcompletiondate' => 'SCHEDULEDCOMPLETIONDATE',
            'revisedcompletiondate' => 'REVISEDCOMPLETIONDATE',
            'substantialcompletiondate' => 'SUBSTANTIALCOMPLETIONDATE',
            'actualcompletiondate' => 'ACTUALCOMPLETIONDATE',
            'noticetoproceed' => 'NOTICETOPROCEED',
            'responsedue' => 'RESPONSEDUE',
            'executedon' => 'EXECUTEDON',
            'scheduleimpact' => 'SCHEDULEIMPACT',
            'internalrefno' => 'INTERNALREFNO',
            'internalinitiatedbykey' => 'INTERNALINITIATEDBYKEY',
            'internalinitiatedby' => 'INTERNALINITIATEDBY',
            'internalinitiatedbyname' => 'INTERNALINITIATEDBYNAME',
            'internalverbalbykey' => 'INTERNALVERBALBYKEY',
            'internalverbalby' => 'INTERNALVERBALBY',
            'internalverbalbyname' => 'INTERNALVERBALBYNAME',
            'internalissuedbykey' => 'INTERNALISSUEDBYKEY',
            'internalissuedby' => 'INTERNALISSUEDBY',
            'internalissuedbyname' => 'INTERNALISSUEDBYNAME',
            'internalissuedon' => 'INTERNALISSUEDON',
            'internalapprovedbykey' => 'INTERNALAPPROVEDBYKEY',
            'internalapprovedby' => 'INTERNALAPPROVEDBY',
            'internalapprovedbyname' => 'INTERNALAPPROVEDBYNAME',
            'internalapprovedon' => 'INTERNALAPPROVEDON',
            'internalsignedbykey' => 'INTERNALSIGNEDBYKEY',
            'internalsignedby' => 'INTERNALSIGNEDBY',
            'internalsignedbyname' => 'INTERNALSIGNEDBYNAME',
            'internalsignedon' => 'INTERNALSIGNEDON',
            'internalsource' => 'INTERNALSOURCE',
            'internalsourcerefno' => 'INTERNALSOURCEREFNO',
            'externalrefno' => 'EXTERNALREFNO',
            'externalverbalbykey' => 'EXTERNALVERBALBYKEY',
            'externalverbalby' => 'EXTERNALVERBALBY',
            'externalapprovedbykey' => 'EXTERNALAPPROVEDBYKEY',
            'externalapprovedby' => 'EXTERNALAPPROVEDBY',
            'externalapprovedon' => 'EXTERNALAPPROVEDON',
            'externalsignedbykey' => 'EXTERNALSIGNEDBYKEY',
            'externalsignedby' => 'EXTERNALSIGNEDBY',
            'externalsignedon' => 'EXTERNALSIGNEDON',
            'performancebondrequired' => 'PERFORMANCEBONDREQUIRED',
            'performancebondreceived' => 'PERFORMANCEBONDRECEIVED',
            'performancebondamount' => 'PERFORMANCEBONDAMOUNT',
            'performancesuretycompanykey' => 'PERFORMANCESURETYCOMPANYKEY',
            'performancesuretycompany' => 'PERFORMANCESURETYCOMPANY',
            'performancesuretycompanyname' => 'PERFORMANCESURETYCOMPANYNAME',
            'paymentbondrequired' => 'PAYMENTBONDREQUIRED',
            'paymentbondreceived' => 'PAYMENTBONDRECEIVED',
            'paymentbondamount' => 'PAYMENTBONDAMOUNT',
            'paymentsuretycompanykey' => 'PAYMENTSURETYCOMPANYKEY',
            'paymentsuretycompany' => 'PAYMENTSURETYCOMPANY',
            'paymentsuretycompanyname' => 'PAYMENTSURETYCOMPANYNAME',
            'taxsolutionid' => 'TAXSOLUTIONID',
            'haschange' => 'HASCHANGE',
            'revisedtotal' => 'REVISEDTOTAL',
            'revisedsubtotal' => 'REVISEDSUBTOTAL',
            'trx_revisedtotal' => 'TRX_REVISEDTOTAL',
            'trx_revisedsubtotal' => 'TRX_REVISEDSUBTOTAL',
            'postedchangestotal' => 'POSTEDCHANGESTOTAL',
            'projectcontractkey' => 'PROJECTCONTRACTKEY',
            'projectcontractid' => 'PROJECTCONTRACTID',
            'pcbexternalrefno' => 'PCBEXTERNALREFNO',
            'pcbdescription' => 'PCBDESCRIPTION',
            'pcbdate' => 'PCBDATE',
            'architectkey' => 'ARCHITECTKEY',
            'billthroughdate' => 'BILLTHROUGHDATE',
            'billapplicationno' => 'BILLAPPLICATIONNO',
            'orgcontractamt' => 'ORGCONTRACTAMT',
            'netchanges' => 'NETCHANGES',
            'revisedcontractamt' => 'REVISEDCONTRACTAMT',
            'totalcompletedtodate' => 'TOTALCOMPLETEDTODATE',
            'retcompletedamt' => 'RETCOMPLETEDAMT',
            'retstoredmaterials' => 'RETSTOREDMATERIALS',
            'totalretainage' => 'TOTALRETAINAGE',
            'totalearnedlessret' => 'TOTALEARNEDLESSRET',
            'lesspriorapplication' => 'LESSPRIORAPPLICATION',
            'currentamtdue' => 'CURRENTAMTDUE',
            'balancetofinish' => 'BALANCETOFINISH',
            'tcapmaddition' => 'TCAPMADDITION',
            'tcapmdeduction' => 'TCAPMDEDUCTION',
            'tcatmaddition' => 'TCATMADDITION',
            'tcatmdeduction' => 'TCATMDEDUCTION',
            'previousretainagebalance' => 'PREVIOUSRETAINAGEBALANCE',
            'totalretainageonthisinvoice' => 'TOTALRETAINAGEONTHISINVOICE',
            'totalnetchangesaddition' => 'TOTALNETCHANGESADDITION',
            'totalnetchangesdeduction' => 'TOTALNETCHANGESDEDUCTION',
            'totalnetchanges' => 'TOTALNETCHANGES',
            'architect' => 'ARCHITECT',
        )
    ),
    'otherreceipts' => array(
        'useEntity' => true,
        'subelements' => array('otherreceiptsentries'),
        'internalSubElements' => array('otherreceiptsentries' => 'ITEMS'),
        'intlevel' => array('otherreceiptsentries'),
        'subelementEntities' => array('otherreceiptsentries' => 'otherreceiptsentry'),
        'intDataType' => 'struct',
        'fieldmap' => array(
            'key' => 'RECORDNO',
            'undepglaccountno' => 'UNDEPACCTNO',
        ),
        'key' => 'key',
        'permissionkey' => array('cm/lists/otherreceipts/view'),
        'entity' => 'otherreceipts',
        'hasLines' => '1',
    ),

    'recursotransaction' => array
    (
        'useEntity' => true,
        'entity' => 'sorecurdocument',
        'listMethod' => 'GetListFull',
        'hasLines' => 1,
        'subelements' => array('recursotransitems', 'subtotals'),
        'internalSubElements' => array('recursotransitems' => 'ENTRIES', 'subtotals' => 'SUBTOTALS'),
        'subobjects' => array('shipto', 'billto'),
        'permissionkey' => array('so/lists/sorecurdocument'),
        'fieldmap' => array(
            'key' => 'RECORDNO',
            'customerid' => 'CUSTVENDID',
            'termname' => 'TERM.NAME',
            'datecreated' => 'WHENCREATED',
            'transactiontype' => 'DOCID',
            'referenceno' => 'PONUMBER',
            'customerponumber' => 'CUSTOMERPONUMBER',
            'shippingmethod' => 'SHIPVIA',
            'shipto' => 'SHIPTO.CONTACTNAME',
            'billto' => 'BILLTO.CONTACTNAME',
            'basecurr' => 'BASECURR',
            'currency' => 'CURRENCY',
            'exchratedate' => 'EXCHRATEDATE',
            'exchratetype' => 'EXCHRATETYPES.NAME',
            'exchratetypeid' => 'EXCH_RATE_TYPE_ID',
            'exchrate' => 'EXCHRATE',
            'docstatus' => 'DOCSTATUS',
        )
    ),
    'potransaction' => array(
        'useEntity' => true,
        'entity' => 'podocument',
        'listMethod' => 'GetListFull_OLD',
        'hasLines' => 1,
        //uncomment below lines with GA of PDLC-0531-13
        'hasLinesSubElement' => 1,
        'subelements' => array('potransitems', 'subtotals'),
        'intlevel' => array('potransitems'),
        'subelementEntities' => array('potransitems' => 'podocumententry'),
        'subobjects' => array('payto', 'returnto', 'deliverto'),
        'internalSubElements' => array('potransitems' => 'ENTRIES', 'subtotals' => 'SUBTOTALS'),
        'lineElements' => array('potransitem' => 'linesubtotals'),
        'internalLineElements' => array('linesubtotals' => 'SUBTOTALSENTRY'),
        'permissionkey' => array('po/lists/podocument'),
        'fieldmap' => array(
            'key' => 'RECORDNO',
            'prrecordkey' => 'PRRECORDKEY',
            'transactiontype' => 'DOCPARID',
            'transactionid' => 'DOCID',
            'documentnumber' => 'DOCNO',
            'datecreated' => 'WHENCREATED',
            'dateposted' => 'WHENPOSTED',
            'createdfrom' => 'CREATEDFROM',
            'vendorid' => 'CUSTVENDID',
            'referenceno' => 'PONUMBER',
            'vendordocno' => 'VENDORDOCNO',
            'termname' => 'TERM.NAME',
            'datedue' => 'WHENDUE',
            'message' => 'MESSAGE',
            'project' => 'PROJECT',
            'changelognumber' => 'CHANGELOGNUMBER',
            'shippingmethod' => 'SHIPVIA',
            'returnto' => 'SHIPTO.CONTACTNAME',
            'payto' => 'BILLTO.CONTACTNAME',
            'deliverto' => 'DELIVERTO.CONTACTNAME',
            'supdocid' => 'SUPDOCID',
            'transactionstate' => 'STATE',
            'trx_totalpaid' => 'TRX_TOTALPAID',
            'totalpaid' => 'TOTALPAID',
            'trx_totalentered' => 'TRX_TOTALENTERED',
            'totalentered' => 'TOTALENTERED',
            'trx_totaldue' => 'TRX_TOTALDUE',
            'totaldue' => 'TOTALDUE',
            'paymentstatus' => 'PAYMENTSTATUS',
            'exchratedate' => 'EXCHRATEDATE',
            'exchratetype' => 'EXCHRATETYPES.NAME',
            'exchratetypeid' => 'EXCH_RATE_TYPE_ID',
            'exchrate' => 'EXCHRATE',
            'needbydate' => 'NEEDBYDATE',
            'donotshipbeforedate' => 'DONOTSHIPBEFOREDATE',
            'donotshipafterdate' => 'DONOTSHIPAFTERDATE',
            'promiseddate' => 'PROMISEDDATE',
            'contractstartdate' => 'CONTRACTSTARTDATE',
            'contractenddate' => 'CONTRACTENDDATE',
            'cancelafterdate' => 'CANCELAFTERDATE',
            'scope' => 'SCOPE',
            'inclusions' => 'INCLUSIONS',
            'exclusions' => 'EXCLUSIONS',
            'terms' => 'TERMS',
            'schedulestartdate' => 'SCHEDULESTARTDATE',
            'actualstartdate' => 'ACTUALSTARTDATE',
            'scheduledcompletiondate' => 'SCHEDULEDCOMPLETIONDATE',
            'revisedcompletiondate' => 'REVISEDCOMPLETIONDATE',
            'substantialcompletiondate' => 'SUBSTANTIALCOMPLETIONDATE',
            'actualcompletiondate' => 'ACTUALCOMPLETIONDATE',
            'noticetoproceed' => 'NOTICETOPROCEED',
            'responsedue' => 'RESPONSEDUE',
            'executedon' => 'EXECUTEDON',
            'scheduleimpact' => 'SCHEDULEIMPACT',
            'internalrefno' => 'INTERNALREFNO',
            'internalinitiatedbykey' => 'INTERNALINITIATEDBYKEY',
            'internalinitiatedby' => 'INTERNALINITIATEDBY',
            'internalinitiatedbyname' => 'INTERNALINITIATEDBYNAME',
            'internalverbalbykey' => 'INTERNALVERBALBYKEY',
            'internalverbalby' => 'INTERNALVERBALBY',
            'internalverbalbyname' => 'INTERNALVERBALBYNAME',
            'internalissuedbykey' => 'INTERNALISSUEDBYKEY',
            'internalissuedby' => 'INTERNALISSUEDBY',
            'internalissuedbyname' => 'INTERNALISSUEDBYNAME',
            'internalissuedon' => 'INTERNALISSUEDON',
            'internalapprovedbykey' => 'INTERNALAPPROVEDBYKEY',
            'internalapprovedby' => 'INTERNALAPPROVEDBY',
            'internalapprovedbyname' => 'INTERNALAPPROVEDBYNAME',
            'internalapprovedon' => 'INTERNALAPPROVEDON',
            'internalsignedbykey' => 'INTERNALSIGNEDBYKEY',
            'internalsignedby' => 'INTERNALSIGNEDBY',
            'internalsignedbyname' => 'INTERNALSIGNEDBYNAME',
            'internalsignedon' => 'INTERNALSIGNEDON',
            'internalsource' => 'INTERNALSOURCE',
            'internalsourcerefno' => 'INTERNALSOURCEREFNO',
            'externalrefno' => 'EXTERNALREFNO',
            'externalverbalbykey' => 'EXTERNALVERBALBYKEY',
            'externalverbalby' => 'EXTERNALVERBALBY',
            'externalapprovedbykey' => 'EXTERNALAPPROVEDBYKEY',
            'externalapprovedby' => 'EXTERNALAPPROVEDBY',
            'externalapprovedon' => 'EXTERNALAPPROVEDON',
            'externalsignedbykey' => 'EXTERNALSIGNEDBYKEY',
            'externalsignedby' => 'EXTERNALSIGNEDBY',
            'externalsignedon' => 'EXTERNALSIGNEDON',
            'performancebondrequired' => 'PERFORMANCEBONDREQUIRED',
            'performancebondreceived' => 'PERFORMANCEBONDRECEIVED',
            'performancebondamount' => 'PERFORMANCEBONDAMOUNT',
            'performancesuretycompanykey' => 'PERFORMANCESURETYCOMPANYKEY',
            'performancesuretycompany' => 'PERFORMANCESURETYCOMPANY',
            'performancesuretycompanyname' => 'PERFORMANCESURETYCOMPANYNAME',
            'paymentbondrequired' => 'PAYMENTBONDREQUIRED',
            'paymentbondreceived' => 'PAYMENTBONDRECEIVED',
            'paymentbondamount' => 'PAYMENTBONDAMOUNT',
            'paymentsuretycompanykey' => 'PAYMENTSURETYCOMPANYKEY',
            'paymentsuretycompany' => 'PAYMENTSURETYCOMPANY',
            'paymentsuretycompanyname' => 'PAYMENTSURETYCOMPANYNAME',
            'haschange' => 'HASCHANGE',
            'revisedtotal' => 'REVISEDTOTAL',
            'revisedsubtotal' => 'REVISEDSUBTOTAL',
            'trx_revisedtotal' => 'TRX_REVISEDTOTAL',
            'trx_revisedsubtotal' => 'TRX_REVISEDSUBTOTAL',
            'taxsolutionid' => 'TAXSOLUTIONID',
            'postedchangestotal' => 'POSTEDCHANGESTOTAL',

        )
    ),
    'warehouse' => array(
        'fieldmap' => array(
            'warehouseid' => 'WAREHOUSEID',
            'name' => 'NAME',
            'parentid' => 'LOC.LOCATIONID',
            'warehouseparentid' => 'PARENTID',
            'managerid' => 'MANAGERID',
            'contactname' => 'CONTACTINFO.CONTACTNAME',
            'shipto' => 'SHIPTO.CONTACTNAME',
            'usedingl' => 'USEDINGL',
            'status' => 'STATUS',
        ),
        'subobjects' => array('contactname', 'shipto'),
        'useEntity' => true,
        'entity' => 'warehouse',
        'permissionkey' => array(
            'po/lists/warehouse',
            'inv/lists/warehouse',
            'so/lists/warehouse'
        ),
    ),
    'productline' => array(
        'fieldmap' => array(
            'productlineid' => 'PRODUCTLINEID',
            'parentid' => 'PARENTLINE',
            'productlinedesc' => 'DESCRIPTION',
            'status' => 'STATUS',
        ),
        'useEntity' => true,
        'permissionkey' => array(
            'po/lists/productline',
            'inv/lists/productline',
            'so/lists/productline'
        ),
        'entity' => 'productline',
    ),
    'ictotal' => array(
        'fieldmap' => array(
            'totalname' => 'NAME',
            'status' => 'STATUS',
        ),
        'useEntity' => true,
        'entity' => 'invtotal',
        'permissionkey' => array('inv/lists/invtotal')
    ),
    'itemglgroup' => array(
        'fieldmap' => array(
            'glgroupname' => 'NAME',
        ),
        'useEntity' => true,
        'entity' => 'itemglgroup',
        'permissionkey' => array(
            'po/lists/itemglgroup',
            'inv/lists/itemglgroup',
            'so/lists/itemglgroup'
        ),
    ),
    'custglgroup' => array(
        'fieldmap' => array(
            'glgroupname' => 'NAME',
        ),
        'useEntity' => true,
        'entity' => 'custglgroup',
        'permissionkey' => array('so/lists/custglgroup'),
    ),
    'vendglgroup' => array(
        'fieldmap' => array(
            'glgroupname' => 'NAME',
        ),
        'useEntity' => true,
        'entity' => 'vendglgroup',
        'permissionkey' => array('po/lists/vendglgroup'),
    ),
    'uom' => array(
        'fieldmap' => array(
            'unit' => 'UNIT',
            'numdecimals' => 'NUMDECIMALS',
            'groupname' => 'GROUPNAME',

        ),
        'useEntity' => true,
        'entity' => 'uomdetail',
        'permissionkey' => array('po/lists/uom', 'so/lists/uom', 'inv/lists/uom'),
    ),
    'sopricelist' => array(
        'fieldmap' => array(
            'pricelistname' => 'NAME',
            'datefrom' => 'DATEFROM',
            'dateto' => 'DATETO',
            'status' => 'STATUS',
        ),
        'useEntity' => true,
        'entity' => 'sopricelist',
        'permissionkey' => array('so/lists/sopricelist', 'inv/lists/invpricelist'),
    ),
    'vsoepricelist' => array(
        'fieldmap' => array(
            'name' => 'NAME',
            'description' => 'DESCRIPTION',
            'createdby' => 'CREATEDBY',
            'status' => 'STATUS',
            'isdefault' => 'ISDEFAULT',
        ),
        'useEntity' => true,
        'entity' => 'vsoepricelist',
        'permissionkey' => array('so/lists/vsoepricelist'),
    ),
    'vsoeitempricelist' => array(
        'hasLines' => 1,
        'subelements' => array('priceentries'),
        'intlevel' => array('priceentries'),
        'subelementEntities' => array('priceentries' => 'vsoeitempricelistentry'),
        'internalSubElements' => array('priceentries' => 'PRICEENTRY'),
        'fieldmap' => array(
            'key' => 'RECORDNO',
            'pricelistid' => 'PRICELISTID',
            'itemid' => 'ITEMID',
            'currency' => 'CURRENCY',
            'valuebase' => 'VALUEBASE',
            'percentbase' => 'PERCENTBASE',
            'percentof' => 'PERCENTOF',
            'status' => 'STATUS',
            'version' => 'VERSION',
        ),
        'useEntity' => true,
        'entity' => 'vsoeitempricelist',
        'permissionkey' => array('so/lists/vsoeitempricelist'),
    ),
    'popricelist' => array(
        'fieldmap' => array(
            'pricelistname' => 'NAME',
            'datefrom' => 'DATEFROM',
            'dateto' => 'DATETO',
            'status' => 'STATUS',
        ),
        'useEntity' => true,
        'entity' => 'popricelist',
        'permissionkey' => array('po/lists/popricelist', 'inv/lists/invpricelist'),
    ),
    'icitem' => array(
        'fieldmap' => array(
            'itemid' => 'ITEMID',
            'itemname' => 'NAME',
            'extdescription' => 'EXTENDED_DESCRIPTION',
            'productlineid' => 'PRODUCTLINEID',
            'glgroupname' => 'GLGROUP',
            'substituteid' => 'SUBSTITUTEID',
            'shipweight' => 'SHIP_WEIGHT',
            'standardunit' => 'UOM.INVUOMDETAIL.UNIT',
            'purchasingunit' => 'UOM.POUOMDETAIL.UNIT',
            'purchasingunitfactor' => 'UOM.POUOMDETAIL.CONVFACTOR',
            'salesunit' => 'UOM.SOUOMDETAIL.UNIT',
            'salesunitfactor' => 'UOM.SOUOMDETAIL.CONVFACTOR',
            'uom' => 'UOMGRP',
            'costmethod' => 'COST_METHOD',
            'standardcost' => 'STANDARD_COST',
            'averagecost' => 'AVERAGE_COST',
            'whenlastsold' => 'WHENLASTSOLD',
            'whenlastreceived' => 'WHENLASTRECEIVED',
            'taxable' => 'TAXABLE',
            'taxgroup' => 'TAXGROUP.NAME',
            'defaultwhse' => 'DEFAULT_WAREHOUSE',
            'note' => 'NOTE',
            'itemtype' => 'ITEMTYPE',
            'enablefulfillment' => 'ENABLEFULFILLMENT',
            'whenmodified' => 'WHENMODIFIED',
            'status' => 'STATUS',
            'upc' => 'UPC',
            'taxcode' => 'TAXCODE',
            'revenue_posting' => 'REVPOSTING',
            'vsoecategory' => 'VSOECATEGORY',
            'vsoedlvrstatus' => 'VSOEDLVRSTATUS',
            'vsoerevdefstatus' => 'VSOEREVDEFSTATUS',
            'enable_bins' => 'ENABLE_BINS',
            'inventory_precision' => 'INV_PRECISION',
            'purchasing_precision' => 'PO_PRECISION',
            'sales_precision' => 'SO_PRECISION',
            'cnbillingtemplate' => 'CNBILLINGTEMPLATE.NAME',
            'cnrevenuetemplate' => 'CNREVENUETEMPLATE.NAME',
            'cnrevenue2template' => 'CNREVENUE2TEMPLATE.NAME',
            'cnexpensetemplate' => 'CNEXPENSETEMPLATE.NAME',
            'cnexpense2template' => 'CNEXPENSE2TEMPLATE.NAME',
            'mrr' => 'MRR',
            'autoprintlabel' => 'AUTOPRINTLABEL',
            'weight_uom' => 'WEIGHT_UOM',
            'net_weight' => 'NET_WEIGHT',
            'lwh_uom' => 'LWH_UOM',
            'length' => 'LENGTH',
            'width' => 'WIDTH',
            'height' => 'HEIGHT',
            'thicknessuom' => 'THICKNESSUOM',
            'thickness' => 'THICKNESS',
            'minimumthickness' => 'MINIMUMTHICKNESS',
            'maximumthickness' => 'MAXIMUMTHICKNESS',
            'areauom' => 'AREAUOM',
            'area' => 'AREA',
            'volumeuom' => 'VOLUMEUOM',
            'volume' => 'VOLUME',
            'durometer' => 'DUROMETER',
            'diameteruom' => 'DIAMETERUOM',
            'innerdiameter' => 'INNERDIAMETER',
            'outerdiameter' => 'OUTERDIAMETER',
            'densityuom' => 'DENSITYUOM',
            'density' => 'DENSITY',
            'upc12' => 'UPC12',
            'ean13' => 'EAN13',
            'safetyitem' => 'SAFETYITEM',
            'restricteditem' => 'RESTRICTEDITEM',
            'compliantitem' => 'COMPLIANTITEM',
            'condition' => 'CONDITION',
            'engineeringalert' => 'ENGINEERINGALERT',
            'specification1' => 'SPECIFICATION1',
            'specification2' => 'SPECIFICATION2',
            'specification3' => 'SPECIFICATION3',
            'engineeringapproval' => 'ENGINEERINGAPPROVAL',
            'quality_controlapproval' => 'QUALITYCONTROLAPPROVAL',
            'salesapproval' => 'SALESAPPROVAL',
            'primarycountryoforigin' => 'PRIMARYCOUNTRYOFORIGIN',
            'brand' => 'BRAND',
            'subbrand' => 'SUBBRAND',
            'category' => 'CATEGORY',
            'subcategory' => 'SUBCATEGORY',
            'catalogref' => 'CATALOGREF',
            'color' => 'COLOR',
            'style' => 'STYLE',
            'size1' => 'SIZE1',
            'size2' => 'SIZE2',
            'giftcard' => 'GIFTCARD',
            'webenabled' => 'WEBENABLED',
            'webname' => 'WEBNAME',
            'webshortdesc' => 'WEBSHORTDESC',
            'weblongdesc' => 'WEBLONGDESC',
        ),
        'useEntity' => true,
        'entity' => 'item',
        'hasLines' => '1',
        'listMethod' => 'GetListFull',
        'subelements' => array('complineitems', 'whslineitems', 'vendlineitems'),
        'intlevel' => array('complineitems', 'whslineitems', 'vendlineitems'),
        'subelementEntities' => array('complineitems' => 'itemcomponent', 'whslineitem' => 'itemwarehouseinfo', 'vendlineitem' => 'itemvendor'),
        'internalSubElements' => array('complineitems' => 'COMPONENT_INFO', 'whslineitems' => 'WAREHOUSE_INFO', 'vendlineitems' => 'VENDOR_INFO'),
        'hasLinesSubElement' => 1,
        'lineElements' => array('whslineitem' => 'whslineitemsstdcostentries'),
        'internalLineElements' => array('whslineitemsstdcostentries' => 'STDCOSTENTRIES'),
        'permissionkey' => array('inv/lists/item', 'so/lists/item', 'po/lists/item'),
    ),
    'ictransactiondef' => array(
        'fieldmap' => array(
            'transdefid' => 'DOCID',
            'transclass' => 'DOCCLASS'
        ),
        'useEntity' => true,
        'entity' => 'invdocumentparams',
        'permissionkey' => array('inv/activities/invdocumentparams'),
    ),
    'sotransactiondef' => array(
        'fieldmap' => array(
            'transdefid' => 'DOCID',
            'transclass' => 'DOCCLASS'
        ),
        'useEntity' => true,
        'entity' => 'sodocumentparams',
        'permissionkey' => array('so/activities/sodocumentparams'),
    ),
    'potransactiondef' => array(
        'fieldmap' => array(
            'transdefid' => 'DOCID',
            'transclass' => 'DOCCLASS'
        ),
        'useEntity' => true,
        'entity' => 'podocumentparams',
        'permissionkey' => array('po/activities/podocumentparams'),
    ),
    'artransactiondef' => array(
        'permissionkey' => array('ar/activities/arbatch'),
    ),
    'araging' => array(
        'subelements' => array('aging'),
        'internalSubElements' => array('aging' => 'aging'),
        'multiSubelement' => array('aging' => true),
        'fieldmap' => array(
            'customerid' => 'CUSTOMERID',
        ),
        'permissionkey' => array('ar/reports/customer_aging'),
    ),

    'aging' => array(
        'subelements' => array('agingdetails'),
        'internalSubElements' => array('agingdetails' => 'AGINGDETAILS'),
        'multiSubelement' => array('agingdetails' => true),
        'fieldmap' => array(
            'agingperiods' => 'AGINGPERIODS',
            'totalamount' => 'TOTALAMOUNT',
        ),
    ),
    'agingdetails' => array(
        'subelements' => array('agingdetail'),
        'internalSubElements' => array('agingdetail' => 'AGINGDETAIL'),
        'multiSubelement' => array('agingdetail' => true),
    ),
    'agingdetail' => array(
        'fieldmap' => array(
            'invoiceno' => 'INVOICENO',
            'totaldue' => 'TOTALDUE',
            'agingdate' => 'AGINGDATE',
            'age' => 'AGE',
        ),
    ),
    'taxdetail' => array(
        'permissionkey' => array('so/lists/sotaxdetail/view'),
        'entity' => 'taxdetail',
        'useEntity' => true,
    ),
    'taxschedule' => array(
        'permissionkey' => array('so/lists/sotaxschedule/view'),
        'entity' => 'taxschedule',
        'useEntity' => true,
    ),
    'taxscheduledetail' => array(
        'permissionkey' => array('so/lists/sotaxschedule/view'),
        'entity' => 'taxscheddetails',
        'useEntity' => true
    ),
    'contacttaxgroup' => array(
        'permissionkey' => 'co/lists/taxgroup',
        'entity' => 'taxgroup',
        'useEntity' => true
    ),
    'itemtaxgroup' => array(
        'permissionkey' => 'inv/lists/itemtaxgroup',
        'entity' => 'itemtaxgroup',
        'useEntity' => true
    ),
    'taxschedulemap' => array(
        'permissionkey' => 'so/lists/sotaxschedmap',
        'entity' => 'taxschedmap',
        'useEntity' => true
    ),
    'revrectemplate' => array(
        'useEntity' => true,
        'entity' => 'revrectemplate',
        'permissionkey' => 'ar/lists/revrectemplate'
    ),
    'revrecschedule' => array(
        'useEntity' => true,
        'entity' => 'revrecschedule',
        'permissionkey' => 'gl/lists/revrecschedule',
        'fieldmap' => array(
            'recordno' => 'RECORDNO',
            'revrectemplatekey' => 'REVRECTEMPLATEKEY',
            'revrectemplateid' => 'REVRECTEMPLATEID',
            'templatepostingmethod' => 'TEMPLATEPOSTINGMETHOD',
            'recmethod' => 'RECMETHOD',
            'completed' => 'COMPLETED',
            'invoiceno' => 'INVOICENO',
            'invoicedate' => 'INVOICEDATE',
            'invoicekey' => 'RECORDKEY',
            'sodocumentid' => 'DOCID',
            'sodocumentdate' => 'DOCUMENTDATE',
            'description' => 'DESCRIPTION',
            'invoiceitemkey' => 'PRENTRYKEY',
            'sodocumententrykey' => 'DOCENTRYKEY',
            'status' => 'STATUS',
            'calculationmethod' => 'PACALCSOURCE',
            'calculationbasedon' => 'PACALCHOURS',
            'projectkey' => 'PROJECTKEY',
            'projectid' => 'PROJECTID',
            'projectname' => 'PROJECTNAME',
            'taskkey' => 'TASKKEY',
            'taskname' => 'TASKNAME',
        )
    ),
    'revrecscheduleentry' => array(
        'useEntity' => true,
        'entity' => 'revrecscheduleentry',
        'permissionkey' => 'gl/lists/revrecschedule'
    ),
    'revrecchangelog' => array(
        'useEntity' => true,
        'entity' => 'revrecchangelog',
        'permissionkey' => 'gl/lists/revrecschedule'
    ),
    'subscription' => array(
        'useEntity' => true,
        'entity' => 'subscription',
        'permissionkey' => array('co/lists/subscriptions'),
    ),
    //  Do not add this until we can return custom fields and also filter on just sotransitems.  TM 02.14.08
    //	'sotransitem' => array(
    //		'useEntity' => true,
    //		'entity' => 'sodocumententry',
    //		'permissionkey' => 'so/lists/sodocument'
    //		),
    'pricelistitem' => array(
        'useEntity' => true,
        'entity' => 'invpricelistentry',
        'permissionkey' => array('po/lists/popricelistentry', 'so/lists/popricelistentry', 'inv/lists/invpricelistentry')
    ),
    'renewalmacro' => array(
        'useEntity' => true,
        'entity' => 'renewalmacro',
        'permissionkey' => 'so/lists/renewalmacro'
    ),
    'smarteventlog' => array(
        'useEntity' => true,
        'entity' => 'smarteventlog',
        //'permissionkey' => array('so/lists/sodocument')
    ),
    'appaymentrequest' => array(
        'useEntity' => true,
        'entity' => 'appaymentrequest',
        'permissionkey' => 'ap/lists/appaymentrequest/view'
    ),
    'recurringgltransaction' => array(
        'useEntity' => true,
        'entity' => 'recurglbatch',
        'permissionkey' => 'gl/lists/recurglbatch/view',
        'subelements' => array('recurglentry'),
        'internalSubElements' => array('recurglentry' => 'ENTRIES'),
        'intDataType' => 'struct',
    ),
    'recurringstatgltrans' => array(
        'useEntity' => true,
        'entity' => 'recurglbatch',
        'permissionkey' => 'gl/lists/recurglbatch/view',
        'subelements' => array('recurglentry'),
        'internalSubElements' => array('recurglentry' => 'ENTRIES'),
        'intDataType' => 'struct',
    ),
    'allocation' => array(
        'useEntity' => true,
        'entity' => 'allocation',
        'listMethod' => 'getAllocationList',
        'permissionkey' => 'co/lists/allocation/view',
        'subelements' => array('allocationentries'),
        'internalSubElements' => array('allocationentries' => 'ALLOCATIONENTRY'),
        'subelementEntities' => array('allocationentries' => 'allocationentry'),
        'intlevel' => array('allocationentries'),
        'intDataType' => 'struct',
        'fkey' => 'allocationkey',
        'fieldmap' => array(
            'recordno' => 'RECORD#',
            'allocationid' => 'ALLOCATIONID',
            'description' => 'DESCRIPTION',
            'allocateby' => "TYPE",
            'docnumber' => 'DOCNUMBER',
            'status' => 'STATUS',
            'supdocid' => 'SUPDOCID',
        ),
        'hasLines' => '1'
    ),
    'entry' => array(
        'attr' => array(
            'line_num' => 'LINE_NO',
            'value' => 'VALUE',
            'departmentid' => 'DEPT#',
            'locationid' => 'LOCATION#',
        ),
        'table' => 'allocationentry',
        'fkey' => 'allocationkey'
    ),
    'cctransaction' => array(
        'entity' => 'cctransaction',
        'permissionkey' => 'cm/lists/cctransaction/view',
        'useEntity' => true,
        'subelements' => array('ccpayitems'),
        'internalSubElements' => array('ccpayitems' => 'ITEMS'),
        'intDataType' => 'struct',
        'hasLines' => '1'
    ),
    'vsoeallocation' => array(
        'useEntity' => true,
        'entity' => 'vsoekitallocation',
        'listMethod' => 'GetVSOEListFull',
        'hasLines' => 1,
        'subelements' => array('lineentryallocation'),
        'intlevel' => array('lineentryallocation'),
        'subelementEntities' => array('lineentryallocation' => 'docentrydetail'),
        'internalSubElements' => array('lineentryallocation' => 'ENTRIES'),
        'permissionkey' => array('so/lists/vsoekitallocation/view'),
    ),
    'lineitemnoretainage' => array(
        'attr' => array(
            'line_num' => 'LINE_NO',
            'accountlabel' => 'ACCOUNTLABELKEY',
            'glaccountno' => 'ACCOUNTKEY',
            'amount' => 'PRENTRY.AMOUNT',
            'memo' => 'DESCRIPTION',
            'locationid' => 'LOCATION#',
            'departmentid' => 'DEPT#',
            'key' => 'PRENTRY.RECORD#',
            'totalpaid' => 'PRENTRY.TOTALPAID',
            'totaldue' => '(PRENTRY.AMOUNT - PRENTRY.TOTALPAID - NVL(PRENTRY.AMOUNTRETAINED, 0)) as TOTALDUE',
            'trx_amount' => 'PRENTRY.TRX_AMOUNT',
            'trx_totalpaid' => 'PRENTRY.TRX_TOTALPAID',
            'trx_totaldue' => '(PRENTRY.TRX_AMOUNT - PRENTRY.TRX_TOTALPAID - NVL(PRENTRY.TRX_AMOUNTRETAINED, 0)) as TRX_TOTALDUE',
            'currency' => 'PRENTRY.CURRENCY',
            'projectkey' => 'PRENTRY.PROJECTDIMKEY',
            'customerkey' => 'PRENTRY.CUSTOMERDIMKEY',
            'vendorkey' => 'PRENTRY.VENDORDIMKEY',
            'employeekey' => 'PRENTRY.EMPLOYEEDIMKEY',
            'itemkey' => 'PRENTRY.ITEMDIMKEY',
            'classkey' => 'PRENTRY.CLASSDIMKEY',
            'contractkey' => 'PRENTRY.CONTRACTDIMKEY',
            'warehousekey' => 'PRENTRY.WAREHOUSEDIMKEY',
            'taskkey' => 'PRENTRY.TASKDIMKEY',
            'costtypekey' => 'PRENTRY.COSTTYPEDIMKEY',
            'billable' => 'PRENTRY.BILLABLE',
            'offsetglaccountno' => 'OFFSETACCOUNTKEY',
        ),
        'where' => array("lineitem='T'"),
        'orderby' => "line_no",
        'table' => 'prentry',
        'fkey' => 'recordkey'
    ),
);

$stkittransitem_structure = array(
    'itemid' => 'ITEMID',
    'itemdesc' => 'ITEMDESC',
    'warehouseid' => 'WAREHOUSE.LOCATION_NO',
    'quantity' => 'UIQTY',
    'unit' => 'UNIT',
    'locationid' => 'LOCATIONID',
    'departmentid' => 'DEPARTMENTID',
);
$ictransitem_structure = array(
    'key' => 'RECORDNO',
    'itemid' => 'ITEMID',
    'itemdesc' => 'ITEMDESC',
    'warehouseid' => 'WAREHOUSE.LOCATION_NO',
    'quantity' => 'UIQTY',
    'unit' => 'UNIT',
    'cost' => 'UIPRICE',
    'totalamount' => 'UIVALUE',
    'locationid' => 'LOCATIONID',
    'departmentid' => 'DEPARTMENTID',
    'projectid' => 'PROJECTID',
    'customerid' => 'CUSTOMERID',
    'vendorid' => 'VENDORID',
    'employeeid' => 'EMPLOYEEID',
    'classid' => 'CLASSID',
    'contractid' => 'CONTRACTID',
    'taskid' => 'TASKID',
    'costtypeid' => 'COSTTYPEID',
);
$complineitem_structure = array(
    'componentkey' => 'COMPONENTKEY',
    'quantity' => 'QUANTITY',
    'revpercent' => 'REVPERCENT',
    'std_uom' => 'UNIT',
    'average_cost' => 'AVERAGE_COST',
    'kcdlvrstatus' => 'KCDLVRSTATUS',
    'kcrevdefstatus' => 'KCREVDEFSTATUS',
);

$whselineitemstdcost_structure = array(
        'effective_start_date' => 'EFFECTIVE_START_DATE',
        'standard_cost' => 'STANDARD_COST',
);

$whslineitem_structure = array(
    'whsid' => 'WAREHOUSEID',
    'cycle' => 'CYCLE',
    'stdcost' => 'STANDARD_COST',
    'lastcost' => 'LAST_COST',
    'avgcost' => 'AVERAGE_COST',
    'reordermethod' => 'REORDER_METHOD',
    'economyorderqty' => 'ECONOMIC_ORDER_QTY',
    'reorder' => 'REORDER_POINT',
    'minorderqty' => 'MIN_ORDER_QTY',
    'maxorderqty' => 'MAX_ORDER_QTY',
    'minstock' => 'MIN_STOCK',
    'maxstock' => 'MAX_STOCK',
    'datelastsold' => 'DATE_LASTSOLD',
    'datelastrecvd' => 'DATE_LASTRECEIVED',
    'defaultsubsection' => 'DEFAULT_SUBSECTION',
    'aisleid' => 'DEFAULT_AISLE',
    'rowkey' => 'DEFAULT_ROW',
    'binid' => 'DEFAULT_BIN',
    'whslineitemsstdcostentries' => array(
       /* 'whslineitemstdcostentry' => $whselineitemstdcost_structure*/
    ),

);

$vendlineitem_structure = array(
    'vendorid' => 'VENDORID',
    'stockno' => 'STOCKNO',
    'leadtime' => 'LEAD_TIME',
    'econorderqty' => 'ECONOMIC_ORDER_QTY',
);

$oetransitem_empty_linesubtotals = array(
    'key' => 'RECORDNO',
    'itemid' => 'ITEMID',
    'itemdesc' => 'ITEMDESC',
    'itemaliasid' => 'ITEMALIASID',
    'warehouseid' => 'WAREHOUSE.LOCATION_NO',
    'quantity' => 'UIQTY',
    'unit' => 'UNIT',
    'linelevelsimpletaxtype' => 'LINELEVELSIMPLETAXTYPE',
    'price' => 'UIPRICE',
    'retailprice' => 'RETAILPRICE',
    'totalamount' => 'UIVALUE',
    //uncomment below lines with GA of PDLC-0531-13
    'overridetaxamount' => 'TAXVALOVERRIDE',
    'deliverto' => array('contactname' => 'DELIVERTO.CONTACTNAME'),
    'taxrate' => 'PERCENTVAL',
    'tax' => 'TAXABSVAL',
    'grossamount' => 'LINETOTAL',
    'locationid' => 'LOCATIONID',
    'departmentid' => 'DEPARTMENTID',
    'memo' => 'MEMO',
    'discsurchargememo' => 'DISCOUNT_MEMO',
    'projectid' => 'PROJECTID',
    'customerid' => 'CUSTOMERID',
    'vendorid' => 'VENDORID',
    'employeeid' => 'EMPLOYEEID',
    'classid' => 'CLASSID',
    'contractid' => 'CONTRACTID',
    'billable' => 'BILLABLE',
    'dropship' => 'DROPSHIP',
    'buytoorder' => 'BUYTOORDER',
    'paymenttaxcapture' => 'PAYMENTTAXCAPTURE',
    'linesubtotals'=>array(),
    'taskid' => 'TASKID',
    'needbydate' => 'NEEDBYDATE',
    'donotshipbeforedate' => 'DONOTSHIPBEFOREDATE',
    'donotshipafterdate' => 'DONOTSHIPAFTERDATE',
    'promiseddate' => 'PROMISEDDATE',
    'dateconfirmed' => 'DATECONFIRMED',
    'cancelafterdate' => 'CANCELAFTERDATE',
    'dateshiptosupplier' => 'DATESHIPTOSUPPLIER',
    'costtypeid' => 'COSTTYPEID',
    'allocation' => 'ALLOCATION',
    'relateddockey' => 'RELATEDDOCKEY',
    'relateddoclinekey' => 'RELATEDDOCLINEKEY',
    'primarydockey' => 'PRIMARYDOCKEY',
    'primarydoclinekey' => 'PRIMARYDOCLINEKEY',
    'revisedunitqty' => 'REVISEDUNITQTY',
    'revisedqty' => 'REVISEDQTY',
    'draftchangeunitqty' => 'DRAFTCHANGEUNITQTY',
    'draftchangeqty' => 'DRAFTCHANGEQTY',
    'revisedunitvalue' => 'REVISEDUNITVALUE',
    'revisedvalue' => 'REVISEDVALUE',
    'trx_revisedvalue' => 'TRX_REVISEDVALUE',
    'revisedprice' => 'REVISEDPRICE',
    'revisedunitprice' => 'REVISEDUNITPRICE',
    'trx_revisedprice' => 'TRX_REVISEDPRICE',
    'conversiontype' => 'CONVERSIONTYPE',
    'draftchangeprice' => 'DRAFTCHANGEPRICE',
    'relateddocno' => 'RELATEDDOCNO',
    'relateddoclineno' => 'RELATEDDOCLINENO',
    'postedqtychanges' => 'POSTEDQTYCHANGES',
    'postedchangeextprice' => 'POSTEDCHANGEEXTPRICE',
    'postedchangeextbaseprice' => 'POSTEDCHANGEEXTBASEPRICE',
    'addedbychange' => 'ADDEDBYCHANGE',
    'draftchangebaseprice' => 'DRAFTCHANGEBASEPRICE',
    'reverseconversion' => 'REVERSECONVERSION',
    'reversepriceconverted' => 'REVERSEPRICECONVERTED',
    'reverseqtyconverted' => 'REVERSEQTYCONVERTED',
    'stdpriceconverted' => 'STDPRICECONVERTED',
    'stdqtyconverted' => 'STDQTYCONVERTED',
    'sourcedocid' => 'SOURCEDOCID',
    'sourcedoclineid' => 'SOURCEDOCLINEID',
    'taxscheduleid' => 'TAXSCHEDULEID'
    //uncomment below lines with GA of PDLC-0531-13

// Removed, does not apply to PO transitems, sotransitems are now separarate TM 02.14.08
//	'revrectemplate' =>  'REVRECTEMPLATE',
//	'revrecstartdate'     =>  'REVRECSTARTDATE',
//  'revrecenddate'       =>  'REVRECENDDATE',
);

$oetransitem_structure=$oetransitem_empty_linesubtotals;
$oetransitem_structure['linesubtotals'] = array ('linesubtotal'	=>	$entrySubtotal_structure ?? null);


$sotransitem_structure = array(
		'recordno'     =>  'RECORDNO',
		'dochdrno'  =>  'DOCHDRNO',
		'docid' => 'DOCUMENT.DOCID',
        'bundlenumber'  =>  'BUNDLENUMBER',
		'itemid'		=>	'ITEMID',
        'itemaliasid' =>	'ITEMALIASID',
		'itemdesc'		=>	'ITEMDESC',
		'line_no' => 'LINE_NO',
		'warehouseid'	=>	'WAREHOUSE.LOCATION_NO',
		'quantity'		=>	'UIQTY',
		'unit'			=>	'UNIT',
		'price'			=>	'UIPRICE',
		'retailprice'   =>  'RETAILPRICE',
		'totalamount'	=>	'UIVALUE',
        //uncomment below lines with GA of PDLC-0531-13
        'taxrate' => 'PERCENTVAL',
        'tax'           =>  'TAXABSVAL',
        'grossamount' => 'LINETOTAL',
		'locationid'	=>  'LOCATIONID',
		'departmentid'	=>	'DEPARTMENTID',
		'memo'			=>	'MEMO',
		'discsurchargememo' => 'DISCOUNT_MEMO',
		'revrectemplate'=>  'REVRECTEMPLATE',
		'revrecstartdate'     =>  'REVRECSTARTDATE',
		'revrecenddate'       =>  'REVRECENDDATE',
		'renewalmacro'        =>  'RENEWALMACRO',
		'currency'  => 'CURRENCY',
		'exchratedate'  => 'EXCHRATEDATE',
		'exchratetype' => 'EXCHRATETYPE',
		'exchrate'  => 'EXCHRATE',
		'trx_price' => 'TRX_PRICE',
		'trx_value' => 'TRX_VALUE',
		'projectid' => 'PROJECTID',
		'customerid' => 'CUSTOMERID',
		'vendorid' => 'VENDORID',
		'employeeid' => 'EMPLOYEEID',
		'classid' => 'CLASSID',
        'contractid' => 'CONTRACTID',        
        'taskno' => 'TASKKEY',
        'billingtemplate'=> 'BILLINGTEMPLATE',
        'sourcedocumentid' => 'SOURCE_DOCID',
        'sourcedocumentkey' => 'SOURCE_DOCKEY',
        'sourcedocumententrytkey' => 'SOURCE_DOCLINEKEY',
        'discountpercent' => 'DISCOUNTPERCENT',
        'multiplier' => 'MULTIPLIER',
        'dropship'  => 'DROPSHIP',
        'buytoorder'  => 'BUYTOORDER',
        'paymenttaxcapture' => 'PAYMENTTAXCAPTURE',
        //uncomment below lines with GA of PDLC-0531-13
        'linesubtotals'		=>	array (
            'linesubtotal'	=>	$entrySubtotal_structure ?? null    // Don't know correct fix yet
        ),
        'taskid' => 'TASKID',
        'costtypeid' => 'COSTTYPEID',
        'needbydate' => 'NEEDBYDATE',
        'shipby' => 'SHIPBY',
        'donotshipbeforedate' => 'DONOTSHIPBEFOREDATE',
        'donotshipafterdate' => 'DONOTSHIPAFTERDATE',
        'datepickticketprinted' => 'DATEPICKTICKETPRINTED',
        'cancelafterdate' => 'CANCELAFTERDATE',
        'shippeddate' => 'SHIPPEDDATE',
        'relateddockey' => 'RELATEDDOCKEY',
        'relateddoclinekey' => 'RELATEDDOCLINEKEY',
        'revisedunitqty' => 'REVISEDUNITQTY',
        'revisedqty' => 'REVISEDQTY',
        'draftchangeunitqty' => 'DRAFTCHANGEUNITQTY',
        'draftchangeqty' => 'DRAFTCHANGEQTY',
        'revisedunitvalue' => 'REVISEDUNITVALUE',
        'revisedvalue' => 'REVISEDVALUE',
        'trx_revisedvalue' => 'TRX_REVISEDVALUE',
        'revisedprice' => 'REVISEDPRICE',
        'revisedunitprice' => 'REVISEDUNITPRICE',
        'trx_revisedprice' => 'TRX_REVISEDPRICE',
        'conversiontype' => 'CONVERSIONTYPE',
        'draftchangeprice' => 'DRAFTCHANGEPRICE',
        'relateddocno' => 'RELATEDDOCNO',
        'relateddoclineno' => 'RELATEDDOCLINENO',
        'postedqtychanges' => 'POSTEDQTYCHANGES',
        'postedchangeextprice' => 'POSTEDCHANGEEXTPRICE',
        'postedchangeextbaseprice' => 'POSTEDCHANGEEXTBASEPRICE',
        'addedbychange' => 'ADDEDBYCHANGE',
        'projectcontractid' => 'PROJECTCONTRACTID',
        'projectcontractkey' => 'PROJECTCONTRACTKEY',
        'projectcontractlineid' => 'PROJECTCONTRACTLINEID',
        'projectcontractlinekey' => 'PROJECTCONTRACTLINEKEY',
        'pcblexternalrefno' => 'PCBLEXTERNALREFNO',
        'pcbldescription' => 'PCBLDESCRIPTION',
        'pcblbillingtype' => 'PCBLBILLINGTYPE',
        'contractlinevalue' => 'CONTRACTLINEVALUE',
        'priorapplicationamt' => 'PRIORAPPLICATIONAMT',
        'completedthisperiod' => 'COMPLETEDTHISPERIOD',
        'storedmaterials' => 'STOREDMATERIALS',
        'totalcompletedtodate' => 'TOTALCOMPLETEDTODATE',
        'percentcompletedtodate' => 'PERCENTCOMPLETEDTODATE',
        'balanceremaining' => 'BALANCEREMAINING',
        'draftchangebaseprice' => 'DRAFTCHANGEBASEPRICE',
        'previousretainagebalance' => 'PREVIOUSRETAINAGEBALANCE',
        'isretainagerelease' => 'ISRETAINAGERELEASE',
        'retainagetobill'  => 'RETAINAGETOBILL',
        'retainagebalance' => 'RETAINAGEBALANCE',
        'issummarized' => 'ISSUMMARIZED',
        'reverseconversion' => 'REVERSECONVERSION',
        'reversepriceconverted' => 'REVERSEPRICECONVERTED',
        'reverseqtyconverted' => 'REVERSEQTYCONVERTED',
        'stdpriceconverted' => 'STDPRICECONVERTED',
        'stdqtyconverted' => 'STDQTYCONVERTED',
        'sourcedocid' => 'SOURCEDOCID',
        'sourcedoclineid' => 'SOURCEDOCLINEID',
        'taxscheduleid' => 'TAXSCHEDULEID'
);

$recursotransitem_structure = array(
    'recordno' => 'RECORDNO',
    'itemid' => 'ITEMID',
    'itemdesc' => 'ITEMDESC',
    'itemaliasid' => 'ITEMALIASID',
    'taxable' => 'OVERRIDETAX',
    'line_no' => 'LINE_NO',
    'warehouseid' => 'WAREHOUSE.LOCATION_NO',
    'quantity' => 'UIQTY',
    'unit' => 'UNIT',
    'price' => 'UIPRICE',
    'retailprice' => 'RETAILPRICE',
    'totalamount' => 'UIVALUE',
    'locationid' => 'LOCATIONID',
    'departmentid' => 'DEPARTMENTID',
    'memo' => 'MEMO',
    'discsurchargememo' => 'DISCOUNT_MEMO',
    'revrectemplate' => 'REVRECTEMPLATE',
    'revrecstartdate' => 'REVRECSTARTDATE',
    'revrecenddate' => 'REVRECENDDATE',
    'trx_price' => 'TRX_PRICE',
    'trx_value' => 'TRX_VALUE',
    'projectid' => 'PROJECTID',
    'customerid' => 'CUSTOMERID',
    'vendorid' => 'VENDORID',
    'employeeid' => 'EMPLOYEEID',
    'classid' => 'CLASSID',
    'contractid' => 'CONTRACTID',     
    'linestatus' => 'LINESTATUS',
    'taskid' => 'TASKID',
    'costtypeid' => 'COSTTYPEID',
);

$priceentry_structure = array(
    'startdate' => 'STARTDATE',
    'enddate' => 'ENDDATE',
    'value' => 'VALUE',
    'markup' => 'MARKUP',
    'markdown' => 'MARKDOWN',
    'bandup' => 'BANDUP',
    'banddown' => 'BANDDOWN',
    'memo' => 'MEMO'
);

$subtotal_structure = array(
    'description' => 'DESCRIPTION',
    'total' => 'TOTAL',
    'absval' => 'ABSVAL',
    'percentval' => 'PERCENTVAL',
    'locationid' => 'LOCATIONID',
    'departmentid' => 'DEPARTMENTID',
    'projectid' => 'PROJECTID',
    'customerid' => 'CUSTOMERID',
    'vendorid' => 'VENDORID',
    'employeeid' => 'EMPLOYEEID',
    'classid' => 'CLASSID',
    'contractid' => 'CONTRACTID',
    'itemid' => 'ITEMID',
    'warehouseid' => 'WAREHOUSEID',
    'taskid' => 'TASKID',
    'costtypeid' => 'COSTTYPEID',
);

$entrySubtotal_structure = array(
    'description' => 'DESCRIPTION',
    'total' => 'TOTAL',
    'absval' => 'ABSVAL',
    'percentval' => 'PERCENTVAL',
    /*'locationid'	=> 'LOCATIONID',
    'departmentid'	=> 'DEPARTMENTID',
    'projectid'     => 'PROJECTID',
    'customerid'    => 'CUSTOMERID',
    'vendorid'      => 'VENDORID',
    'employeeid'    => 'EMPLOYEEID',
    'classid'       => 'CLASSID',
    'itemid'       => 'ITEMID',*/
);

$xmlreturn_structure = array(
    'timesheet' => array(
        'key' => 'RECORDNO',
        'description' => 'DESCRIPTION',
        'employeeid' => 'EMPLOYEEID',
        'begindate' => 'BEGINDATE',
        'enddate' => 'ENDDATE',
        'glpostdate' => 'GLPOSTDATE',
        'supdocid' => 'SUPDOCID',
        'state' => 'STATE',
        'timesheetitems' => array(
            'timesheetitem' => array(
                'key' => 'RECORDNO',
                'lineno' => 'LINENO',
                'timesheetkey' => 'TIMESHEETKEY',
                'customerid' => 'CUSTOMERID',
                'projectid' => 'PROJECTID',
                'vendorid' => 'VENDORID',
                'classid' => 'CLASSID',
                'contractid' => 'CONTRACTID',
                'warehouseid' => 'WAREHOUSEID',
                'taskkey' => 'TASKKEY',
                'itemid' => 'ITEMID',
                'taskname' => 'TASKNAME',
                'timeetype' => 'TIMETYPE',
                'billable' => 'BILLABLE',
                'locationid' => 'LOCATIONID',
                'departmentid' => 'DEPARTMENTID',
                'entrydate' => 'ENTRYDATE',
                'qty' => 'QTY',
                'notes' => 'NOTES',
                'description' => 'DESCRIPTION',
                'state' => 'STATE',
                'taskid' => 'TASKID',
                'costtypeid' => 'COSTTYPEID',
                'extamount' => 'EXTAMOUNT',
                'extemployertaxes' => 'EXTEMPLOYERTAXES',
                'extfringes' => 'EXTFRINGES',
                'extcashfringes' => 'EXTCASHFRINGES'
            ),
        )
    ),
    'timetype' => array(
        'name' => 'NAME',
        'accountno' => 'ACCOUNTNO',
        'offsetaccountno' => 'OFFSETACCOUNTNO',
        'status' => 'STATUS',
        'earningtypename' => 'EARNINGTYPENAME',
    ),
    'earningtype' => array(
        'name' => 'NAME',
        'method' => 'METHOD',
        'billableacctno' => 'BILLABLEACCTNO',
        'nonbillableacctno' => 'NONBILLABLEACCTNO',
        'ratemultiplier' => 'RATEMULTIPLIER',
    ),
    'employeerate' => array(
        'recordno' => 'RECORDNO',
        'employeeid' => 'EMPLOYEEID',
        'ratestartdate' => 'RATESTARTDATE',
        'rateenddate' => 'RATEENDDATE',
        'salaryrate' => 'SALARYRATE',
        'billingrate' => 'BILLINGRATE'
    ),
    'accountgroup' => array(
        'accountgroupname' => 'NAME',
        'headingtitle' => 'TITLE',
        'totaltitle' => 'TOTALTITLE',
        'normalbalance' => 'NORMAL_BALANCE',
        'memberstype' => 'MEMBERTYPE',
        'iskpi' => 'ISKPI',
    ),
    'apaccountlabel' => array(
        'glaccountno' => 'GLACCOUNTNO',
        'accountlabel' => 'ACCOUNTLABEL',
        'description' => 'DESCRIPTION',
        'offsetglaccountno' => 'OFFSETGLACCOUNTNO',
        'status' => 'STATUS'
    ),
    'allocation' => array(
        'recordno' => 'RECORDNO',
        'allocationid' => 'ALLOCATIONID',
        'description' => 'DESCRIPTION',
        'allocateby' => 'TYPE',
        'docnumber' => 'DOCNUMBER',
        'supdocid' => 'SUPDOCID',
        'status' => 'STATUS',
        'allocationentries' => array(
            'entry' => array(
                'line_num' => 'LINE_NO',
                'value' => 'VALUE',
                'departmentid' => 'DEPARTMENTID',
                'locationid' => 'LOCATIONID',
            )
        ),
    ),
    'entry' => array(
        'line_num' => 'LINE_NO',
        'value' => 'VALUE',
        'departmentid' => 'DEPARTMENTID',
        'locationid' => 'LOCATIONID',
    ),
    'apterm' => array(
        'name' => 'NAME',
        'description' => 'DESCRIPTION',
        'status' => 'STATUS',
        'due' => 'VALUE',
        'discount' => 'VALUE',
        'penalty' => 'VALUE',
        'whencreated' => 'WHENCREATED',
        'whenmodified' => 'WHENMODIFIED',
        'createdby' => 'CREATEDBY',
        'modifiedby' => 'MODIFIEDBY',
    ),
    'araccountlabel' => array(
        'glaccountno' => 'GLACCOUNTNO',
        'accountlabel' => 'ACCOUNTLABEL',
        'description' => 'DESCRIPTION',
        'offsetglaccountno' => 'OFFSETGLACCOUNTNO',
        'status' => 'STATUS',
        'taxcode' => 'TAXCODE',
    ),
    'arterm' => array(
        'name' => 'NAME',
        'description' => 'DESCRIPTION',
        'status' => 'STATUS',
        'due' => 'VALUE',
        'discount' => 'VALUE',
        'disccalcon' => 'DISCCALCON',
        'penalty' => 'VALUE',
        'whencreated' => 'WHENCREATED',
        'whenmodified' => 'WHENMODIFIED',
        'createdby' => 'CREATEDBY',
        'modifiedby' => 'MODIFIEDBY',
    ),
    'bankaccount' => array(
        'bankaccountid' => 'BANKACCOUNTID',
        'bankaccountno' => 'BANKACCOUNTNO',
        'glaccountno' => 'GLACCOUNTNO',
        'bankname' => 'BANKNAME',
        'routingno' => 'ROUTINGNO',
        'branchid' => 'BRANCHID',
        'bankaccounttype' => 'BANKACCOUNTTYPE',
        'phone' => 'PHONE',
        'nextcheck' => 'NEXTCHECK',
        'status' => 'STATUS',
        'lastreconciledbalance' => 'LASTRECONCILEDBALANCE',
        'lastreconcileddate' => 'LASTRECONCILEDDATE',
        'mailaddress' => array(
            'address1' => 'MAILADDRESS.ADDRESS1',
            'address2' => 'MAILADDRESS.ADDRESS2',
            'city' => 'MAILADDRESS.CITY',
            'state' => 'MAILADDRESS.STATE',
            'zip' => 'MAILADDRESS.ZIP',
            'country' => 'MAILADDRESS.COUNTRY'
        ),
        'whencreated' => 'WHENCREATED',
        'whenmodified' => 'WHENMODIFIED',
        'createdby' => 'CREATEDBY',
        'modifiedby' => 'MODIFIEDBY',
        'outsourcecheck' => 'OUTSOURCECHECK',
        'fractionalroutingno' => 'FRACTIONALROUTINGNO',
        'nameontheaccount' => 'NAMEONTHEACCOUNT',
    ),

    'contact' => array(
        'contactname' => 'CONTACTNAME',
        'printas' => 'PRINTAS',
        'companyname' => 'COMPANYNAME',
        'prefix' => 'PREFIX',
        'firstname' => 'FIRSTNAME',
        'lastname' => 'LASTNAME',
        'initial' => 'INITIAL',
        'phone1' => 'PHONE1',
        'phone2' => 'PHONE2',
        'cellphone' => 'CELLPHONE',
        'pager' => 'PAGER',
        'fax' => 'FAX',
        'email1' => 'EMAIL1',
        'email2' => 'EMAIL2',
        'url1' => 'URL1',
        'url2' => 'URL2',
        'status' => 'STATUS',
        'mailaddress' => array(
            'address1' => 'MAILADDRESS.ADDRESS1',
            'address2' => 'MAILADDRESS.ADDRESS2',
            'city' => 'MAILADDRESS.CITY',
            'state' => 'MAILADDRESS.STATE',
            'zip' => 'MAILADDRESS.ZIP',
            'country' => 'MAILADDRESS.COUNTRY',
            'latitude' => 'MAILADDRESS.LATITUDE',
            'longitude' => 'MAILADDRESS.LONGITUDE'
        ),
    ),
    'customer' => array(
        'recordno' => 'RECORDNO',
        'customerid' => 'CUSTOMERID',
        'name' => 'NAME',
        'parentid' => 'PARENTID',
        'termname' => 'TERMNAME',
        'custrepid' => 'CUSTREPID',
        'shippingmethod' => 'SHIPPINGMETHOD',
        'custtype' => 'CUSTTYPE',
        'taxid' => 'TAXID',
        'creditlimit' => 'CREDITLIMIT',
        'totaldue' => 'TOTALDUE',
        'territoryid' => 'TERRITORYID',
        'resaleno' => 'RESALENO',
        'deliveryoptions' => 'DELIVERY_OPTIONS',
        'accountlabel' => 'ACCOUNTLABEL',
        'glaccountno' => 'ARACCOUNT',
        'glgroup' => 'GLGROUP',
        'onhold' => 'ONHOLD',
        'comments' => 'COMMENTS',
        'supdocid' => 'SUPDOCID',
        'whenmodified' => 'WHENMODIFIED',
        'status' => 'STATUS',
        'currency' => 'CURRENCY',
        'primary' => array('contactname' => 'CONTACTINFO.CONTACTNAME'),
        'billto' => array('contactname' => 'BILLTO.CONTACTNAME'),
        'shipto' => array('contactname' => 'SHIPTO.CONTACTNAME'),
        'contactinfo' => array(
            'contact' => array(
                'contactname' => 'DISPLAYCONTACT.CONTACTNAME',
                'printas' => 'DISPLAYCONTACT.PRINTAS',
                'companyname' => 'DISPLAYCONTACT.COMPANYNAME',
                'taxable' => 'DISPLAYCONTACT.TAXABLE',
                'taxgroup' => 'DISPLAYCONTACT.TAXGROUP',
                'prefix' => 'DISPLAYCONTACT.PREFIX',
                'firstname' => 'DISPLAYCONTACT.FIRSTNAME',
                'lastname' => 'DISPLAYCONTACT.LASTNAME',
                'initial' => 'DISPLAYCONTACT.INITIAL',
                'phone1' => 'DISPLAYCONTACT.PHONE1',
                'phone2' => 'DISPLAYCONTACT.PHONE2',
                'cellphone' => 'DISPLAYCONTACT.CELLPHONE',
                'pager' => 'DISPLAYCONTACT.PAGER',
                'fax' => 'DISPLAYCONTACT.FAX',
                'email1' => 'DISPLAYCONTACT.EMAIL1',
                'email2' => 'DISPLAYCONTACT.EMAIL2',
                'url1' => 'DISPLAYCONTACT.URL1',
                'url2' => 'DISPLAYCONTACT.URL2',
                'mailaddress' => array(
                    'address1' => 'DISPLAYCONTACT.MAILADDRESS.ADDRESS1',
                    'address2' => 'DISPLAYCONTACT.MAILADDRESS.ADDRESS2',
                    'city' => 'DISPLAYCONTACT.MAILADDRESS.CITY',
                    'state' => 'DISPLAYCONTACT.MAILADDRESS.STATE',
                    'zip' => 'DISPLAYCONTACT.MAILADDRESS.ZIP',
                    'country' => 'DISPLAYCONTACT.MAILADDRESS.COUNTRY',
                    'latitude' => 'DISPLAYCONTACT.MAILADDRESS.LATITUDE',
                    'longitude' => 'DISPLAYCONTACT.MAILADDRESS.LONGITUDE'
                ),
                'taxsolutionid' => 'DISPLAYCONTACT.TAXSOLUTIONID',
                'taxschedule' => 'DISPLAYCONTACT.TAXSCHEDULE',
            ),
        ),
        'vsoepricelist' => 'VSOEPRICELIST',
        'contactlist' => 'RECORDNO',
        'pricelist' => 'PRICELIST',
        'offsetglaccountno' => 'OFFSETGLACCOUNTNO',
        'visibility' => array(
            'visibility_type' => 'OBJECTRESTRICTION',
            'restricted_locs' => 'RESTRICTEDLOCATIONS',
            'restricted_depts' => 'RESTRICTEDDEPARTMENTS',
        ),
        'retainagepercentage' => 'RETAINAGEPERCENTAGE',
    ),
    'customervisibility' => array(
        'customerid' => 'CUSTOMERID',
        'visibility' => array(
            'visibility_type' => 'VISIBILITY',
            'restricted_locs' => 'RESTRICTEDLOCATIONS',
            'restricted_depts' => 'RESTRICTEDDEPARTMENTS',
        ),
    ),
    'customerachinfo' => array(
        'recordno' => 'RECORDNO',
        'customerid' => 'CUSTOMERID',
        'bankname' => 'BANKNAME',
        'bankadd1' => 'BANKADD1',
        'bankadd2' => 'BANKADD2',
        'aba' => 'ABA',
        'accountnumber' => 'ACCOUNTNUMBER',
        'bankaccounttype' => 'ACCOUNTTYPE',
    ),
    'customerchargecard' => array(
        'recordno' => 'RECORDNO',
        'customerid' => 'CUSTOMERID',
        'cardnum' => 'CARDNUM',
        'maskcardnum' => 'CARDNUM',
        'cardid' => 'CARDID',
        'cardtype' => 'CARDTYPE',
        'exp_month' => 'EXP_MONTH',
        'exp_year' => 'EXP_YEAR',
        'description' => 'DESCRIPTION',
        'defaultcard' => 'DEFAULTCARD',
        'usebilltoaddr' => 'USEBILLTOADDR',
        'status' => 'STATUS',
        'mailaddress' => array(
            'address1' => 'MAILADDRESS.ADDRESS1',
            'address2' => 'MAILADDRESS.ADDRESS2',
            'city' => 'MAILADDRESS.CITY',
            'state' => 'MAILADDRESS.STATE',
            'zip' => 'MAILADDRESS.ZIP',
            'country' => 'MAILADDRESS.COUNTRY',
        ),
    ),
    'customerbankaccount' => array(
        'recordno' => 'RECORDNO',
        'customerid' => 'CUSTOMERID',
        'accountnumber' => 'ACCOUNTNUMBER',
        'routingnumber' => 'ROUTINGNUMBER',
        'bankname' => 'BANKNAME',
        'accounttype' => 'ACCOUNTTYPE',
        'accountholder' => 'ACCOUNTHOLDER',
        'description' => 'DESCRIPTION',
        'defaultaccount' => 'DEFAULTACCOUNT',
        'status' => 'STATUS',
        'mailaddress' => array(
            'address1' => 'MAILADDRESS.ADDRESS1',
            'address2' => 'MAILADDRESS.ADDRESS2',
            'city' => 'MAILADDRESS.CITY',
            'state' => 'MAILADDRESS.STATE',
            'zip' => 'MAILADDRESS.ZIP',
            'country' => 'MAILADDRESS.COUNTRY',
        ),
    ),
    'customerppackage' => array(
        'name' => 'NAME',
        'description' => 'DESCRIPTION',
        'author' => 'AUTHOR',
        'intacctid' => 'INTACCTID',
        'signed' => 'SIGNED'
    ),
    'department' => array(
        'departmentid' => 'DEPARTMENTID',
        'title' => 'TITLE',
        'parentid' => 'PARENTID',
        'supervisorid' => 'SUPERVISORID',
        'whenmodified' => 'WHENMODIFIED',
        'status' => 'STATUS'
    ),
    'employee' => array(
        'recordno' => 'RECORDNO',
        'employeeid' => 'EMPLOYEEID',
        'ssn' => 'SSN',
        'title' => 'TITLE',
        'locationid' => 'LOCATIONID',
        'departmentid' => 'DEPARTMENTID',
        'classid' => 'CLASSID',
        'supervisorid' => 'SUPERVISORID',
        'birthdate' => 'BIRTHDATE',
        'startdate' => 'STARTDATE',
        'enddate' => 'ENDDATE',
        'terminationtype' => 'TERMINATIONTYPE',
        'employeetype' => 'EMPLOYEETYPE',
        'gender' => 'GENDER',
        'whenmodified' => 'WHENMODIFIED',
        'status' => 'STATUS',
        'currency' => 'CURRENCY',
        'name1099' => 'NAME1099',
        'form1099type' => 'FORM1099TYPE',
        'form1099box' => 'FORM1099BOX',
        'earningtype' => 'EARNINGTYPENAME',
        'paymentnotify' => 'PAYMENTNOTIFY',
        'mergepaymentreq' => 'MERGEPAYMENTREQ',
        'personalinfo' => array(
            'contact' => array(
                'contactname' => 'PERSONALINFO.CONTACTNAME',
                'printas' => 'PERSONALINFO.PRINTAS',
                'companyname' => 'PERSONALINFO.COMPANYNAME',
                'prefix' => 'PERSONALINFO.PREFIX',
                'firstname' => 'PERSONALINFO.FIRSTNAME',
                'lastname' => 'PERSONALINFO.LASTNAME',
                'initial' => 'PERSONALINFO.INITIAL',
                'phone1' => 'PERSONALINFO.PHONE1',
                'phone2' => 'PERSONALINFO.PHONE2',
                'cellphone' => 'PERSONALINFO.CELLPHONE',
                'pager' => 'PERSONALINFO.PAGER',
                'fax' => 'PERSONALINFO.FAX',
                'email1' => 'PERSONALINFO.EMAIL1',
                'email2' => 'PERSONALINFO.EMAIL2',
                'url1' => 'PERSONALINFO.URL1',
                'url2' => 'PERSONALINFO.URL2',
                'mailaddress' => array(
                    'address1' => 'PERSONALINFO.MAILADDRESS.ADDRESS1',
                    'address2' => 'PERSONALINFO.MAILADDRESS.ADDRESS2',
                    'city' => 'PERSONALINFO.MAILADDRESS.CITY',
                    'state' => 'PERSONALINFO.MAILADDRESS.STATE',
                    'zip' => 'PERSONALINFO.MAILADDRESS.ZIP',
                    'country' => 'PERSONALINFO.MAILADDRESS.COUNTRY',
                ),
            ),
        ),
        'contactlist' => 'RECORDNO',
    ),
    'expensetypes' => array(
        'glaccountno' => 'GLACCOUNTNO',
        'expensetype' => 'ACCOUNTLABEL',
        'description' => 'DESCRIPTION',
        'offsetglaccountno' => 'OFFSETGLACCOUNTNO',
        'status' => 'STATUS',
        'unitcurrency' => 'UNITCURRENCY',
        'unitrates' => array(
            'unitrate' => array(
                'startdate' => 'STARTDATE',
                'rate' => 'RATE',
            )
        ),
    ),
    'expensepaymenttype' => array(
        'key' => 'RECORDNO',
        'name' => 'NAME',
        'description' => 'DESCRIPTION',
        'nonreimbursable' => 'NONREIMBURSABLE',
        'status' => 'STATUS',
        'offsetacctno' => 'OFFSETACCTNO'
    ),
    'glaccount' => array(
        'recordno' => 'RECORDNO',
        'glaccountno' => 'ACCOUNTNO',
        'title' => 'TITLE',
        'normalbalance' => 'NORMALBALANCE',
        'accounttype' => 'ACCOUNTTYPE',
        'closingtype' => 'CLOSINGTYPE',
        'closingaccountno' => 'CLOSINGACCOUNTNO',
        'whenmodified' => 'WHENMODIFIED',
        'status' => 'STATUS',
        'requiredept' => 'REQUIREDEPT',
        'requireloc' => 'REQUIRELOC',
        //'requireproject' 	=> 'REQUIREPROJECT',
        //'requirecustomer' 	=> 'REQUIRECUSTOMER',
        //'requirevendor' 	=> 'REQUIREVENDOR',
        //'requireemployee' 	=> 'REQUIREEMPLOYEE',
        //'requireitem' 	=> 'REQUIREITEM',
        //'requireclass' 	=> 'REQUIRECLASS',
        'taxable' => 'TAXABLE',
        'taxcode' => 'TAXCODE',
        'mrccode' => 'MRCCODE',
        'alternativeaccount' => 'ALTERNATIVEACCOUNT',
        'subledgercontrolon' => 'SUBLEDGERCONTROLON',
    ),
    'statglaccount' => array(
        'glaccountno' => 'ACCOUNTNO',
        'title' => 'TITLE',
        'requiredept' => 'REQUIREDEPT',
        'requireloc' => 'REQUIRELOC',
        //'requireproject' 	=> 'REQUIREPROJECT',
        //'requirecustomer' 	=> 'REQUIRECUSTOMER',
        //'requirevendor' 	=> 'REQUIREVENDOR',
        //'requireemployee' 	=> 'REQUIREEMPLOYEE',
        //'requireitem' 	=> 'REQUIREITEM',
        //'requireclass' 	=> 'REQUIRECLASS',
        'status' => 'STATUS',
    ),
    'stkittransaction' => array(
        'key' => 'RECORDNO',
        'transactiontype' => 'DOCPARID',
        'transactionid' => 'DOCID',
        'datecreated' => 'WHENCREATED',
        'createdfrom' => 'CREATEDFROM',
        'referenceno' => 'PONUMBER',
        'message' => 'MESSAGE',
        'transactionstate' => 'STATE',
        'whenmodified' => 'WHENMODIFIED',
        'stkittransitems' => array(
            'stkittransitem' => $stkittransitem_structure
        ),
    ),
    'ictransaction' => array(
        'key' => 'RECORDNO',
        'transactiontype' => 'DOCPARID',
        'transactionid' => 'DOCID',
        'datecreated' => 'WHENCREATED',
        'createdfrom' => 'CREATEDFROM',
        'referenceno' => 'PONUMBER',
        'message' => 'MESSAGE',
        'transactionstate' => 'STATE',
        'whenmodified' => 'WHENMODIFIED',
        'dateprinted' => 'DATEPRINTED',
        'ictransitems' => array(
            'ictransitem' => $ictransitem_structure
        ),
        'subtotals' => array(
            'subtotal' => $subtotal_structure
        )
    ),
    // added key, currency, exchratedate, exchratetypes, exchrate TM 01.28.08
    'sotransaction' => array(
        'key' => 'RECORDNO',
        'prrecordkey' => 'PRRECORDKEY',
        'transactiontype' => 'DOCPARID',
        'transactionid' => 'DOCID',
        'documentnumber' => 'DOCNO',
        'datecreated' => 'WHENCREATED',
        'dateposted' => 'WHENPOSTED',
        'createdfrom' => 'CREATEDFROM',
        'customerid' => 'CUSTVENDID',
        'referenceno' => 'PONUMBER',
        'termname' => 'TERM.NAME',
        'datedue' => 'WHENDUE',
        'message' => 'MESSAGE',
        'shippingmethod' => 'SHIPVIA',
        'shipto' => array('contactname' => 'SHIPTO.CONTACTNAME'),
        'billto' => array('contactname' => 'BILLTO.CONTACTNAME'),
        'supdocid' => 'SUPDOCID',
        'transactionstate' => 'STATE',
        'whenmodified' => 'WHENMODIFIED',
        'dateprinted' => 'DATEPRINTED',
        'retainageheld' => 'RETAINAGEHELD',
        'retainagebilled' => 'RETAINAGEBILLED',
        'retainagebalance' => 'RETAINAGEBALANCE',
        'totallessretainageheld' => 'TOTALLESSRETAINAGEHELD',
        'basecurr' => 'BASECURR',
        'currency' => 'CURRENCY',
        'exchratedate' => 'EXCHRATEDATE',
        'exchratetype' => 'EXCHRATETYPES.NAME',
        'exchratetypeid' => 'EXCH_RATE_TYPE_ID',
        'exchrate' => 'EXCHRATE',
        'vsoepricelist' => 'VSOEPRICELIST',
        'trx_totalpaid' => 'TRX_TOTALPAID',
        'totalpaid' => 'TOTALPAID',
        'trx_totalentered' => 'TRX_TOTALENTERED',
        'totalentered' => 'TOTALENTERED',
        'trx_totaldue' => 'TRX_TOTALDUE',
        'totaldue' => 'TOTALDUE',
        'paymentstatus' => 'PAYMENTSTATUS',
        'project' => 'PROJECT',
        'changelognumber' => 'CHANGELOGNUMBER',
        'needbydate' => 'NEEDBYDATE',
        'shipbydate' => 'SHIPBYDATE',
        'cancelafterdate' => 'CANCELAFTERDATE',
        'donotshipbeforedate' => 'DONOTSHIPBEFOREDATE',
        'donotshipafterdate' => 'DONOTSHIPAFTERDATE',
        'servicedeliverydate' => 'SERVICEDELIVERYDATE',
        'trackingnumber' => 'TRACKINGNUMBER',
        'shippeddate' => 'SHIPPEDDATE',
        'customerponumber' => 'CUSTOMERPONUMBER',
        'scope' => 'SCOPE',
        'inclusions' => 'INCLUSIONS',
        'exclusions' => 'EXCLUSIONS',
        'terms' => 'TERMS',
        'schedulestartdate' => 'SCHEDULESTARTDATE',
        'actualstartdate' => 'ACTUALSTARTDATE',
        'scheduledcompletiondate' => 'SCHEDULEDCOMPLETIONDATE',
        'revisedcompletiondate' => 'REVISEDCOMPLETIONDATE',
        'substantialcompletiondate' => 'SUBSTANTIALCOMPLETIONDATE',
        'actualcompletiondate' => 'ACTUALCOMPLETIONDATE',
        'noticetoproceed' => 'NOTICETOPROCEED',
        'responsedue' => 'RESPONSEDUE',
        'executedon' => 'EXECUTEDON',
        'scheduleimpact' => 'SCHEDULEIMPACT',
        'internalrefno' => 'INTERNALREFNO',
        'internalinitiatedbykey' => 'INTERNALINITIATEDBYKEY',
        'internalinitiatedby' => 'INTERNALINITIATEDBY',
        'internalinitiatedbyname' => 'INTERNALINITIATEDBYNAME',
        'internalverbalbykey' => 'INTERNALVERBALBYKEY',
        'internalverbalby' => 'INTERNALVERBALBY',
        'internalverbalbyname' => 'INTERNALVERBALBYNAME',
        'internalissuedbykey' => 'INTERNALISSUEDBYKEY',
        'internalissuedby' => 'INTERNALISSUEDBY',
        'internalissuedbyname' => 'INTERNALISSUEDBYNAME',
        'internalissuedon' => 'INTERNALISSUEDON',
        'internalapprovedbykey' => 'INTERNALAPPROVEDBYKEY',
        'internalapprovedby' => 'INTERNALAPPROVEDBY',
        'internalapprovedbyname' => 'INTERNALAPPROVEDBYNAME',
        'internalapprovedon' => 'INTERNALAPPROVEDON',
        'internalsignedbykey' => 'INTERNALSIGNEDBYKEY',
        'internalsignedby' => 'INTERNALSIGNEDBY',
        'internalsignedbyname' => 'INTERNALSIGNEDBYNAME',
        'internalsignedon' => 'INTERNALSIGNEDON',
        'internalsource' => 'INTERNALSOURCE',
        'internalsourcerefno' => 'INTERNALSOURCEREFNO',
        'externalrefno' => 'EXTERNALREFNO',
        'externalverbalbykey' => 'EXTERNALVERBALBYKEY',
        'externalverbalby' => 'EXTERNALVERBALBY',
        'externalapprovedbykey' => 'EXTERNALAPPROVEDBYKEY',
        'externalapprovedby' => 'EXTERNALAPPROVEDBY',
        'externalapprovedon' => 'EXTERNALAPPROVEDON',
        'externalsignedbykey' => 'EXTERNALSIGNEDBYKEY',
        'externalsignedby' => 'EXTERNALSIGNEDBY',
        'externalsignedon' => 'EXTERNALSIGNEDON',
        'performancebondrequired' => 'PERFORMANCEBONDREQUIRED',
        'performancebondreceived' => 'PERFORMANCEBONDRECEIVED',
        'performancebondamount' => 'PERFORMANCEBONDAMOUNT',
        'performancesuretycompanykey' => 'PERFORMANCESURETYCOMPANYKEY',
        'performancesuretycompany' => 'PERFORMANCESURETYCOMPANY',
        'performancesuretycompanyname' => 'PERFORMANCESURETYCOMPANYNAME',
        'paymentbondrequired' => 'PAYMENTBONDREQUIRED',
        'paymentbondreceived' => 'PAYMENTBONDRECEIVED',
        'paymentbondamount' => 'PAYMENTBONDAMOUNT',
        'paymentsuretycompanykey' => 'PAYMENTSURETYCOMPANYKEY',
        'paymentsuretycompany' => 'PAYMENTSURETYCOMPANY',
        'paymentsuretycompanyname' => 'PAYMENTSURETYCOMPANYNAME',
        'taxsolutionid' => 'TAXSOLUTIONID',
        'haschange' => 'HASCHANGE',
        'revisedtotal' => 'REVISEDTOTAL',
        'revisedsubtotal' => 'REVISEDSUBTOTAL',
        'trx_revisedtotal' => 'TRX_REVISEDTOTAL',
        'trx_revisedsubtotal' => 'TRX_REVISEDSUBTOTAL',
        'postedchangestotal' => 'POSTEDCHANGESTOTAL',
        'projectcontractkey' => 'PROJECTCONTRACTKEY',
        'projectcontractid' => 'PROJECTCONTRACTID',
        'pcbexternalrefno' => 'PCBEXTERNALREFNO',
        'pcbdescription' => 'PCBDESCRIPTION',
        'pcbdate' => 'PCBDATE',
        'architectkey' => 'ARCHITECTKEY',
        'billthroughdate' => 'BILLTHROUGHDATE',
        'billapplicationno' => 'BILLAPPLICATIONNO',
        'orgcontractamt' => 'ORGCONTRACTAMT',
        'netchanges' => 'NETCHANGES',
        'revisedcontractamt' => 'REVISEDCONTRACTAMT',
        'totalcompletedtodate' => 'TOTALCOMPLETEDTODATE',
        'retcompletedamt' => 'RETCOMPLETEDAMT',
        'retstoredmaterials' => 'RETSTOREDMATERIALS',
        'totalretainage' => 'TOTALRETAINAGE',
        'totalearnedlessret' => 'TOTALEARNEDLESSRET',
        'lesspriorapplication' => 'LESSPRIORAPPLICATION',
        'currentamtdue' => 'CURRENTAMTDUE',
        'balancetofinish' => 'BALANCETOFINISH',
        'tcapmaddition' => 'TCAPMADDITION',
        'tcapmdeduction' => 'TCAPMDEDUCTION',
        'tcatmaddition' => 'TCATMADDITION',
        'tcatmdeduction' => 'TCATMDEDUCTION',
        'previousretainagebalance' => 'PREVIOUSRETAINAGEBALANCE',
        'totalretainageonthisinvoice' => 'TOTALRETAINAGEONTHISINVOICE',
        'totalnetchangesaddition' => 'TOTALNETCHANGESADDITION',
        'totalnetchangesdeduction' => 'TOTALNETCHANGESDEDUCTION',
        'totalnetchanges' => 'TOTALNETCHANGES',
        'architect' => 'ARCHITECT',
        'sotransitems' => array(
            'sotransitem' => $sotransitem_structure
        ),
        'subtotals' => array(
            'subtotal' => $subtotal_structure
        ),

    ),
    'otherreceipts' => array(
        'key' => 'RECORDNO',
        'recordno' => 'RECORDNO',
        'whencreated' => 'WHENCREATED',
        'recordtype' => 'RECORDTYPE',
        'description2' => 'DESCRIPTION2',
        'whenpaid' => 'WHENPAID',
        'paymethod' => 'PAYMETHOD',
        'docnumber' => 'DOCNUMBER',
        'description' => 'DESCRIPTION',
        'undepglaccountno' => 'UNDEPACCTNO',
        'bankaccountid' => 'BANKACCOUNTID',
        'bankaccountname' => 'BANKACCOUNTNAME',
        'bankaccountcurr' => 'BANKACCOUNTCURR',
        'depositdate' => 'DEPOSITDATE',
        'prbatchkey' => 'PRBATCHKEY',
        'basecurr' => 'BASECURR',
        'currency' => 'CURRENCY',
        'exch_rate_date' => 'EXCH_RATE_DATE',
        'exch_rate_type_id' => 'EXCH_RATE_TYPE_ID',
        'exchange_rate' => 'EXCHANGE_RATE',
        'totalentered' => 'TOTALENTERED',
        'trx_totalentered' => 'TRX_TOTALENTERED',
        'recon_date' => 'RECON_DATE',
        'state' => 'STATE',
        'rawstate' => 'RAWSTATE',
        'cleared' => 'CLEARED',
        'parentpayment' => 'PARENTPAYMENT',
        'impliedlocation' => 'IMPLIEDLOCATION',
        'auwhencreated' => 'AUWHENCREATED',
        'whenmodified' => 'WHENMODIFIED',
        'createdby' => 'CREATEDBY',
        'modifiedby' => 'MODIFIEDBY',
        'inclusivetax' => 'INCLUSIVETAX',
        'taxsolutionid' => 'TAXSOLUTIONID',
        'otherreceiptsentries' => array(
            'otherreceiptsentry' => array(
                'accountlabel' => 'ACCOUNTLABEL',
                'glaccountno' => 'ACCOUNTNO',
                'glaccounttitle' => 'ACCOUNTTITLE',
                'amount' => 'AMOUNT',
                'totalpaid' => 'TOTALPAID',
                'memo' => 'ENTRYDESCRIPTION',
                'locationid' => 'LOCATIONID',
                'departmentid' => 'DEPARTMENTID',
                'trx_amount' => 'TRX_AMOUNT',
                'trx_totalpaid' => 'TRX_TOTALPAID',
                'projectid' => 'PROJECTID',
                'customerid' => 'CUSTOMERID',
                'vendorid' => 'VENDORID',
                'employeeid' => 'EMPLOYEEID',
                'itemid' => 'ITEMID',
                'classid' => 'CLASSID',
                'contractid' => 'CONTRACTID',
                'warehouseid' => 'WAREHOUSEID',
                'offsetglaccountno' => 'OFFSETGLACCOUNTNO',
                'taskid' => 'TASKID',
                'costtypeid' => 'COSTTYPEID',
            )
        ),
    ),
    'recursotransaction' => array(
        'key' => 'RECORDNO',
        'transactiontype' => 'DOCID',
        'datecreated' => 'WHENCREATED',
        'customerid' => 'CUSTVENDID',
        'referenceno' => 'PONUMBER',
        'termname' => 'TERM.NAME',
        'customerponumber' => 'CUSTOMERPONUMBER',
        'shippingmethod' => 'SHIPVIA',
        'shipto' => array('contactname' => 'SHIPTO.CONTACTNAME'),
        'billto' => array('contactname' => 'BILLTO.CONTACTNAME'),
        'basecurr' => 'BASECURR',
        'currency' => 'CURRENCY',
        'exchratedate' => 'EXCHRATEDATE',
        'exchratetype' => 'EXCHRATETYPES.NAME',
        'exchratetypeid' => 'EXCH_RATE_TYPE_ID',
        'exchrate' => 'EXCHRATE',
        'taxsolutionid' => 'TAXSOLUTIONID',
        'recursotransitems' => array(
            'recursotransitem' => $recursotransitem_structure
        ),
        'subtotals' => array(
            'subtotal' => $subtotal_structure
        ),
        'paymethod' => 'PAYMETHOD',
        'payinfull' => 'PAYINFULL',
        'accounttype' => 'ACCOUNTTYPE',
        'bankaccount' => 'BANKACCOUNTID',
        'paymentamount'  => 'PAYMENTAMOUNT',
        'chargecard'    => 'CUSTOMERCREDITCARDKEY',
        'cardtype'      => 'CREDITCARDTYPE',
        'customerbankaccount' => 'CUSTOMERBANKACCOUNTKEY',
        'glaccountkey'  =>  'GLACCOUNTKEY',
        'startdate' => 'STARTDATE',
        'enddate' => 'ENDDATE',
        'repeatcount' => 'REPEATCOUNT',
        'execcount' => 'EXECCOUNT',
        'repeatby' => 'REPEATBY',
        'interval' => 'INTERVAL',
        'docstatus' => 'DOCSTATUS',
        'dochdrno' => 'DOCHDRNO',
    ),
    'recursotransitem' => $recursotransitem_structure,
    'recurbill' => array(
        'recordno' => 'RECORDNO',
        'prbatchkey' => 'PRBATCHKEY',
        'prbatch' => 'PRBATCH',
        'recordtype' => 'RECORDTYPE',
        'description' => 'DESCRIPTION',
        'state' => 'STATE',
        'datecreated' => 'WHENCREATED',
        'datemodified' => 'WHENMODIFIED',
        'termkey' => 'TERMKEY',
        'termname' => 'TERMNAME',
        'basecurr' => 'BASECURR',
        'currency' => 'CURRENCY',
        'recordid' => 'RECORDID',
        'totalentered' => 'TOTALENTERED',
        'userkey' => 'USERKEY',
        'docnumber' => 'DOCNUMBER',
        'prmodulekey' => 'MODULEKEY',
        'status' => 'STATUS',
        'schopkey' => 'SCHOPKEY',
        'entity' => 'ENTITY',
        'contractid' => 'CONTRACTID',
        'contractdesc' => 'CONTRACTDESC',
        'vendorid' => 'VENDORID',
        'vendorname' => 'VENDORNAME',
        'username' => 'USERNAME',
        'paytocontactname' => 'PAYTOCONTACTNAME',
        'returntocontactname' => 'RETURNTOCONTACTNAME',
        'locationkey' => 'LOCATIONKEY',
        'aprecurbillentry' => array
        (
            'recurlineitem' => array
            (
                'recordno' => 'RECORDNO',
                'recordkey' => 'RECORDKEY',
                'offset' => 'OFFSET',
                'entrydescription' => 'ENTRYDESCRIPTION',
                'accountkey' => 'ACCOUNTKEY',
                'glaccountno' => 'GLACCOUNTNO',
                'glaccounttitle' => 'GLACCOUNTTITLE',
                'amount' => 'AMOUNT',
                'trx_amount' => 'TRX_AMOUNT',
                'locationid' => 'LOCATIONID',
                'locationname' => 'LOCATIONNAME',
                'departmentname' => 'DEPARTMENTNAME',
                'lineno' => 'LINE_NO',
                'accountlabel' => 'ACCOUNTLABEL',
                'glaccount' => 'GLACCOUNT',
                'projectid' => 'PROJECTID',
                'customerid' => 'CUSTOMERID',
                'vendorid' => 'VENDORID',
                'employeeid' => 'EMPLOYEEID',
                'itemid' => 'ITEMID',
                'classid' => 'CLASSID',
                'contractid' => 'CONTRACTID',
                'warehouseid' => 'WAREHOUSEID',
                'billable' => 'BILLABLE',
                'taskid' => 'TASKID',
                'costtypeid' => 'COSTTYPEID',
            )
        ),
        'supdocid' => 'SUPDOCID',
        'externalurl' => 'EXTERNALURL',
        'startdate' => 'STARTDATE',
        'enddate' => 'ENDDATE',
        'repeatcount' => 'REPEATCOUNT',
        'repeatby' => 'REPEATBY',
        'interval' => 'INTERVAL',
        'vendordata' => 'VENDOR'
    ),
    'recurinvoice' => array(
        'recordno' => 'RECORDNO',
        'prbatchkey' => 'PRBATCHKEY',
        'prbatch' => 'PRBATCH',
        'recordtype' => 'RECORDTYPE',
        'description' => 'DESCRIPTION',
        'state' => 'STATE',
        'datecreated' => 'WHENCREATED',
        'datemodified' => 'WHENMODIFIED',
        'termkey' => 'TERMKEY',
        'termname' => 'TERMNAME',
        'basecurr' => 'BASECURR',
        'currency' => 'CURRENCY',
        'recordid' => 'RECORDID',
        'totalentered' => 'TOTALENTERED',
        'userkey' => 'USERKEY',
        'docnumber' => 'DOCNUMBER',
        'prmodulekey' => 'MODULEKEY',
        'status' => 'STATUS',
        'schopkey' => 'SCHOPKEY',
        'entity' => 'ENTITY',
        'contractid' => 'CONTRACTID',
        'contractdesc' => 'CONTRACTDESC',
        'customerid' => 'CUSTOMERID',
        'customername' => 'CUSTOMERNAME',
        'username' => 'USERNAME',
        'billtocontactname' => 'BILLTOCONTACTNAME',
        'shiptocontactname' => 'SHIPTOCONTACTNAME',
        'locationkey' => 'LOCATIONKEY',
        'taxsolutionid' => 'SOLUTIONID',
        'arrecurinvoiceentry' => array
        (
            'recurlineitem' => array
            (
                'recordno' => 'RECORDNO',
                'recordkey' => 'RECORDKEY',
                'offset' => 'OFFSET',
                'entrydescription' => 'ENTRYDESCRIPTION',
                'accountkey' => 'ACCOUNTKEY',
                'glaccountno' => 'GLACCOUNTNO',
                'glaccounttitle' => 'GLACCOUNTTITLE',
                'amount' => 'AMOUNT',
                'trx_amount' => 'TRX_AMOUNT',
                'locationid' => 'LOCATIONID',
                'locationname' => 'LOCATIONNAME',
                'departmentname' => 'DEPARTMENTNAME',
                'lineno' => 'LINE_NO',
                'accountlabel' => 'ACCOUNTLABEL',
                'glaccount' => 'GLACCOUNT',
                'projectid' => 'PROJECTID',
                'customerid' => 'CUSTOMERID',
                'vendorid' => 'VENDORID',
                'employeeid' => 'EMPLOYEEID',
                'itemid' => 'ITEMID',
                'classid' => 'CLASSID',
                'contractid' => 'CONTRACTID',
                'warehouseid' => 'WAREHOUSEID',
                'taskid' => 'TASKID',
                'costtypeid' => 'COSTTYPEID',
            )
        ),
        'supdocid' => 'SUPDOCID',
        'externalurl' => 'EXTERNALURL',
        'startdate' => 'STARTDATE',
        'enddate' => 'ENDDATE',
        'repeatcount' => 'REPEATCOUNT',
        'repeatby' => 'REPEATBY',
        'interval' => 'INTERVAL',
        'customerdata' => 'CUSTOMER',
        'recurpaymentkey' => 'RECURPAYMENTKEY',
        'paymethod' => 'PAYMETHOD',
        'payinfull' => 'PAYINFULL',
        'paymentamount' => 'PAYMENTAMOUNT',
        'customercreditcardkey' => 'CUSTOMERCREDITCARDKEY',
        'customerbankaccountkey' => 'CUSTOMERBANKACCOUNTKEY',
        'creditcardtype' => 'CREDITCARDTYPE',
        'expirednotify' => 'EXPIREDNOTIFY',
        'expirednotifyemail' => 'EXPIREDNOTIFYEMAIL',
        'accounttype' => 'ACCOUNTTYPE',
        'bankaccountid' => 'BANKACCOUNTID',
        'glaccountkey' => 'GLACCOUNTKEY',
        'nextexecdate' => 'NEXTEXECDATE',
    ),
    'potransaction' => array(
        'key' => 'RECORDNO',
        'prrecordkey' => 'PRRECORDKEY',
        'transactiontype' => 'DOCPARID',
        'transactionid' => 'DOCID',
        'datecreated' => 'WHENCREATED',
        'dateposted' => 'WHENPOSTED',
        'createdfrom' => 'CREATEDFROM',
        'vendorid' => 'CUSTVENDID',
        'referenceno' => 'PONUMBER',
        'vendordocno' => 'VENDORDOCNO',
        'termname' => 'TERM.NAME',
        'datedue' => 'WHENDUE',
        'project' => 'PROJECT',
        'changelognumber' => 'CHANGELOGNUMBER',
        'message' => 'MESSAGE',
        'shippingmethod' => 'SHIPVIA',
        'returnto' => array('contactname' => 'SHIPTO.CONTACTNAME'),
        'payto' => array('contactname' => 'BILLTO.CONTACTNAME'),
        'deliverto' => array('contactname' => 'DELIVERTO.CONTACTNAME'),
        'supdocid' => 'SUPDOCID',
        'transactionstate' => 'STATE',
        'whenmodified' => 'WHENMODIFIED',
        'dateprinted' => 'DATEPRINTED',
        'basecurr' => 'BASECURR',
        'currency' => 'CURRENCY',
        'exchratedate' => 'EXCHRATEDATE',
        'exchratetype' => 'EXCHRATETYPES.NAME',
        'exchratetypeid' => 'EXCH_RATE_TYPE_ID',
        'exchrate' => 'EXCHRATE',
        'trx_totalpaid' => 'TRX_TOTALPAID',
        'totalpaid' => 'TOTALPAID',
        'trx_totalentered' => 'TRX_TOTALENTERED',
        'totalentered' => 'TOTALENTERED',
        'trx_totaldue' => 'TRX_TOTALDUE',
        'totaldue' => 'TOTALDUE',
        'paymentstatus' => 'PAYMENTSTATUS',
        'needbydate' => 'NEEDBYDATE',
        'donotshipbeforedate' => 'DONOTSHIPBEFOREDATE',
        'donotshipafterdate' => 'DONOTSHIPAFTERDATE',
        'promiseddate' => 'PROMISEDDATE',
        'contractstartdate' => 'CONTRACTSTARTDATE',
        'contractenddate' => 'CONTRACTENDDATE',
        'cancelafterdate' => 'CANCELAFTERDATE',
        'scope' => 'SCOPE',
        'inclusions' => 'INCLUSIONS',
        'exclusions' => 'EXCLUSIONS',
        'terms' => 'TERMS',
        'schedulestartdate' => 'SCHEDULESTARTDATE',
        'actualstartdate' => 'ACTUALSTARTDATE',
        'scheduledcompletiondate' => 'SCHEDULEDCOMPLETIONDATE',
        'revisedcompletiondate' => 'REVISEDCOMPLETIONDATE',
        'substantialcompletiondate' => 'SUBSTANTIALCOMPLETIONDATE',
        'actualcompletiondate' => 'ACTUALCOMPLETIONDATE',
        'noticetoproceed' => 'NOTICETOPROCEED',
        'responsedue' => 'RESPONSEDUE',
        'executedon' => 'EXECUTEDON',
        'scheduleimpact' => 'SCHEDULEIMPACT',
        'internalrefno' => 'INTERNALREFNO',
        'internalinitiatedbykey' => 'INTERNALINITIATEDBYKEY',
        'internalinitiatedby' => 'INTERNALINITIATEDBY',
        'internalinitiatedbyname' => 'INTERNALINITIATEDBYNAME',
        'internalverbalbykey' => 'INTERNALVERBALBYKEY',
        'internalverbalby' => 'INTERNALVERBALBY',
        'internalverbalbyname' => 'INTERNALVERBALBYNAME',
        'internalissuedbykey' => 'INTERNALISSUEDBYKEY',
        'internalissuedby' => 'INTERNALISSUEDBY',
        'internalissuedbyname' => 'INTERNALISSUEDBYNAME',
        'internalissuedon' => 'INTERNALISSUEDON',
        'internalapprovedbykey' => 'INTERNALAPPROVEDBYKEY',
        'internalapprovedby' => 'INTERNALAPPROVEDBY',
        'internalapprovedbyname' => 'INTERNALAPPROVEDBYNAME',
        'internalapprovedon' => 'INTERNALAPPROVEDON',
        'internalsignedbykey' => 'INTERNALSIGNEDBYKEY',
        'internalsignedby' => 'INTERNALSIGNEDBY',
        'internalsignedbyname' => 'INTERNALSIGNEDBYNAME',
        'internalsignedon' => 'INTERNALSIGNEDON',
        'internalsource' => 'INTERNALSOURCE',
        'internalsourcerefno' => 'INTERNALSOURCEREFNO',
        'externalrefno' => 'EXTERNALREFNO',
        'externalverbalbykey' => 'EXTERNALVERBALBYKEY',
        'externalverbalby' => 'EXTERNALVERBALBY',
        'externalapprovedbykey' => 'EXTERNALAPPROVEDBYKEY',
        'externalapprovedby' => 'EXTERNALAPPROVEDBY',
        'externalapprovedon' => 'EXTERNALAPPROVEDON',
        'externalsignedbykey' => 'EXTERNALSIGNEDBYKEY',
        'externalsignedby' => 'EXTERNALSIGNEDBY',
        'externalsignedon' => 'EXTERNALSIGNEDON',
        'performancebondrequired' => 'PERFORMANCEBONDREQUIRED',
        'performancebondreceived' => 'PERFORMANCEBONDRECEIVED',
        'performancebondamount' => 'PERFORMANCEBONDAMOUNT',
        'performancesuretycompanykey' => 'PERFORMANCESURETYCOMPANYKEY',
        'performancesuretycompany' => 'PERFORMANCESURETYCOMPANY',
        'performancesuretycompanyname' => 'PERFORMANCESURETYCOMPANYNAME',
        'paymentbondrequired' => 'PAYMENTBONDREQUIRED',
        'paymentbondreceived' => 'PAYMENTBONDRECEIVED',
        'paymentbondamount' => 'PAYMENTBONDAMOUNT',
        'paymentsuretycompanykey' => 'PAYMENTSURETYCOMPANYKEY',
        'paymentsuretycompany' => 'PAYMENTSURETYCOMPANY',
        'paymentsuretycompanyname' => 'PAYMENTSURETYCOMPANYNAME',
        'haschange' => 'HASCHANGE',
        'revisedtotal' => 'REVISEDTOTAL',
        'revisedsubtotal' => 'REVISEDSUBTOTAL',
        'trx_revisedtotal' => 'TRX_REVISEDTOTAL',
        'trx_revisedsubtotal' => 'TRX_REVISEDSUBTOTAL',
        'taxsolutionid' => 'TAXSOLUTIONID',
        'postedchangestotal' => 'POSTEDCHANGESTOTAL',
        'potransitems' => array(
            'potransitem' => $oetransitem_structure
        ),
        'subtotals' => array(
            'subtotal' => $subtotal_structure
        ),
    ),
    'ictransitem' => $ictransitem_structure,
    'whslineitem' => $whslineitem_structure,
    'vendlineitem' => $vendlineitem_structure,
    'complineitem' => $complineitem_structure,
    // This is defined specifically below TM 02.14.08	'sotransitem' => $oetransitem_structure,
    'potransitem' => $oetransitem_empty_linesubtotals,
    'subtotal' => $subtotal_structure,
    'linesubtotal' => $entrySubtotal_structure,
    'journal' => array(
        'symbol' => 'SYMBOL',
        'title' => 'TITLE',
        'status' => 'STATUS',
        'whencreated' => 'WHENCREATED',
        'whenmodified' => 'WHENMODIFIED',
        'createdby' => 'CREATEDBY',
        'modifiedby' => 'MODIFIEDBY',
    ),
    'adjjournal' => array(
        'symbol' => 'SYMBOL',
        'title' => 'TITLE',
        'status' => 'STATUS'
    ),
    'statjournal' => array(
        'symbol' => 'SYMBOL',
        'title' => 'TITLE',
        'status' => 'STATUS'
    ),
    'location' => array(
        'locationid' => 'LOCATIONID',
        'name' => 'NAME',
        'parentid' => 'PARENTID',
        'supervisorid' => 'SUPERVISORID',
        'startdate' => 'STARTDATE',
        'enddate' => 'ENDDATE',
        'whenmodified' => 'WHENMODIFIED',
        'status' => 'STATUS',
        'primary' => array('contactname' => 'CONTACTINFO.CONTACTNAME'),
        'shipto' => array('contactname' => 'SHIPTO.CONTACTNAME'),
    ),
    'member' => $locmember_structure,
    'locationgroup' => array(
        'id' => 'ID',
        'name' => 'NAME',
        'members' => array('member' => $locmember_structure),
    ),
    'locationentity' => array(
        'locationid' => 'LOCATIONID',
        'name' => 'NAME',
        'locationtype' => 'LOCATIONTYPE',
        'currency' => 'CURRENCY',
        'status' => 'STATUS',
        'enablelegalcontact' => 'ENABLELEGALCONTACT',
        'legalname' => 'LEGALNAME',
        'legaladdress1' => 'LEGALADDRESS1',
        'legaladdress2' => 'LEGALADDRESS2',
        'legalcity' => 'LEGALCITY',
        'legalstate' => 'LEGALSTATE',
        'legalzipcode' => 'LEGALZIPCODE',
        'legalcountry' => 'LEGALCOUNTRY',
    ),
    'supdocfolder' => array(
        'name' => 'NAME',
        'description' => 'DESCRIPTION',
        'parentfolder' => 'GROUPKEY',
        'creationdate' => 'CREATED',
        'createdby' => 'CREATEDBY',
        'lastmodified' => 'LASTMODIFIED',
        'lastmodifiedby' => 'LASTMODIFIEDBY',
        'status' => 'STATUS',
    ),
    'attachment' => array(
        'attachmentname' => 'ATTACHMENTNAME',
        'attachmenttype' => 'ATTACHMENTTYPE',
        'attachmentdata' => 'ATTACHMENTDATA',
    ),
    'supdoc' => array(
        'recordno' => 'RECORDNO',
        'supdocid' => 'DOCUMENTID',
        'supdocname' => 'DOCUMENTNAME',
        'folder' => 'GROUPKEY',
        'description' => 'DESCRIPTION',
        'creationdate' => 'CREATED',
        'createdby' => 'CREATEDBY',
        'attachments' => array(
            'attachment' => array(
                'attachmentname' => 'ATTACHMENTNAME',
                'attachmenttype' => 'ATTACHMENTTYPE',
                'attachmentdata' => 'ATTACHMENTDATA',
            ),
        ),
    ),
    'exchangeratetypes' => array(
        'name' => 'NAME'
    ),
    'csnhistory' => array(
        'bookkey' => 'BOOKKEY',
        'locationname' => 'LOCATIONNAME',
        'periodname' => 'PERIODNAME',
        'actiontime' => 'ACTIONTIME',
        'startedby' => 'STARTEDBY',
        'warate' => 'WARATE',
        'bsrate' => 'BSRATE',
        'status' => 'STATUS',
        'message' => 'MESSAGE'
    ),
    'company_info' => array(
        'title' => 'TITLE',
        'name' => 'NAME',
    ),
    'trxcurrencies' => array(
        'code' => 'CODE',
    ),
    'reportingperiod' => array(
        'name' => 'NAME',
        'headingtitle' => 'HEADER1',
        'headingtitle2' => 'HEADER2',
        'startdate' => 'START_DATE',
        'enddate' => 'END_DATE',
        'budgetable' => 'BUDGETING',
        'status' => 'STATUS',
    ),
    'vendorentityaccount' => array(
        'recordno' => 'RECORDNO',
        'vendorid' => 'VENDORID',
        'locationid' => 'LOCATIONID',
        'locationname' => 'LOCATIONNAME',
        'accountno' => 'ACCOUNTNO',
    ),
    'vendor' => array(
        'recordno' => 'RECORDNO',
        'vendorid' => 'VENDORID',
        'name' => 'NAME',
        'parentid' => 'PARENTID',
        'termname' => 'TERMNAME',
        'glaccountno' => 'APACCOUNT',
        'accountlabel' => 'ACCOUNTLABEL',
        'vendtype' => 'VENDTYPE',
        'taxid' => 'TAXID',
        'creditlimit' => 'CREDITLIMIT',
        'totaldue' => 'TOTALDUE',
        'billingtype' => 'BILLINGTYPE',
        'vendoraccountno' => 'VENDORACCOUNTNO',
        'comments' => 'COMMENTS',
        'outsourcecheck' => 'OUTSOURCECHECK',
        'servicestate' => 'OUTSOURCECHECKSTATE',
        'donotcutcheck' => 'DONOTCUTCHECK',
        'onhold' => 'ONHOLD',
        'form1099' => 'FORM1099BOX',
        'name1099' => 'NAME1099',
        'form1099type' => 'FORM1099TYPE',
        'form1099box' => 'FORM1099BOX',
        'supdocid' => 'SUPDOCID',
        'status' => 'STATUS',
        'currency' => 'CURRENCY',
        'onetime' => 'ONETIME',
        'whenmodified' => 'WHENMODIFIED',
        'primary' => array('contactname' => 'CONTACTINFO.CONTACTNAME'),
        'returnto' => array('contactname' => 'RETURNTO.CONTACTNAME'),
        'payto' => array('contactname' => 'PAYTO.CONTACTNAME'),
        'contactto1099' => array('contactname' => 'CONTACTTO1099.CONTACTNAME'),
        'contactinfo' => array(
            'contact' => array(
                'contactname' => 'DISPLAYCONTACT.CONTACTNAME',
                'printas' => 'DISPLAYCONTACT.PRINTAS',
                'companyname' => 'DISPLAYCONTACT.COMPANYNAME',
                'taxable' => 'DISPLAYCONTACT.TAXABLE',
                'taxgroup' => 'DISPLAYCONTACT.TAXGROUP',
                'prefix' => 'DISPLAYCONTACT.PREFIX',
                'firstname' => 'DISPLAYCONTACT.FIRSTNAME',
                'lastname' => 'DISPLAYCONTACT.LASTNAME',
                'initial' => 'DISPLAYCONTACT.INITIAL',
                'phone1' => 'DISPLAYCONTACT.PHONE1',
                'phone2' => 'DISPLAYCONTACT.PHONE2',
                'cellphone' => 'DISPLAYCONTACT.CELLPHONE',
                'pager' => 'DISPLAYCONTACT.PAGER',
                'fax' => 'DISPLAYCONTACT.FAX',
                'email1' => 'DISPLAYCONTACT.EMAIL1',
                'email2' => 'DISPLAYCONTACT.EMAIL2',
                'url1' => 'DISPLAYCONTACT.URL1',
                'url2' => 'DISPLAYCONTACT.URL2',
                'mailaddress' => array(
                    'address1' => 'DISPLAYCONTACT.MAILADDRESS.ADDRESS1',
                    'address2' => 'DISPLAYCONTACT.MAILADDRESS.ADDRESS2',
                    'city' => 'DISPLAYCONTACT.MAILADDRESS.CITY',
                    'state' => 'DISPLAYCONTACT.MAILADDRESS.STATE',
                    'zip' => 'DISPLAYCONTACT.MAILADDRESS.ZIP',
                    'country' => 'DISPLAYCONTACT.MAILADDRESS.COUNTRY',
                    'latitude' => 'DISPLAYCONTACT.MAILADDRESS.LATITUDE',
                    'longitude' => 'DISPLAYCONTACT.MAILADDRESS.LONGITUDE'
                ),
                'taxsolutionid' => 'DISPLAYCONTACT.TAXSOLUTIONID',
                'taxschedule' => 'DISPLAYCONTACT.TAXSCHEDULE',
            ),
        ),
        'contactlist' => 'RECORDNO',
        'paymentnotify' => 'PAYMENTNOTIFY',
        'achenabled' => 'ACHENABLED',
        'wireenabled' => 'WIREENABLED',
        'checkenabled' => 'CHECKENABLED',
        'achbankroutingnumber' => 'ACHBANKROUTINGNUMBER',
        'achaccountnumber' => 'ACHACCOUNTNUMBER',
        'achaccounttype' => 'ACHACCOUNTTYPE',
        'achremittancetype' => 'ACHREMITTANCETYPE',
        'wirebankname' => 'WIREBANKNAME',
        'wirebankroutingnumber' => 'WIREBANKROUTINGNUMBER',
        'wireaccountnumber' => 'WIREACCOUNTNUMBER',
        'wireaccounttype' => 'WIREACCOUNTTYPE',
        'pmplusremittancetype' => 'PMPLUSREMITTANCETYPE',
        'pmplusemail' => 'PMPLUSEMAIL',
        'pmplusfax' => 'PMPLUSFAX',
        'displaytermdiscount' => 'DISPLAYTERMDISCOUNT',
        'displocacctnocheck' => 'DISPLOCACCTNOCHECK',
        'mergepaymentreq' => 'MERGEPAYMENTREQ',
        'offsetglaccountno' => 'OFFSETGLACCOUNTNO',
        'visibility' => array(
            'visibility_type' => 'OBJECTRESTRICTION',
            'restricted_locs' => 'RESTRICTEDLOCATIONS',
            'restricted_depts' => 'RESTRICTEDDEPARTMENTS',
        ),
        'retainagepercentage' => 'RETAINAGEPERCENTAGE',
    ),
    'vendorpref' => array(
        'key' => 'RECORDNO',
        'vendorid' => 'VENDORID',
        'property' => 'PROPERTY',
        'value' => 'VALUE',
        'locationkey' => 'LOCATIONKEY'
    ),
    'employeepref' => array(
        'key' => 'RECORDNO',
        'employeeid' => 'EMPLOYEEID',
        'property' => 'PROPERTY',
        'value' => 'VALUE',
        'locationkey' => 'LOCATIONKEY'
    ),
    'vendorvisibility' => array(
        'vendorid' => 'VENDORID',
        'visibility' => array(
            'visibility_type' => 'VISIBILITY',
            'restricted_locs' => 'RESTRICTEDLOCATIONS',
            'restricted_depts' => 'RESTRICTEDDEPARTMENTS',
        ),
    ),
    'glentry' => $glentry_structure,
    'gltransaction' => array(
        'key' => 'RECORDNO',
        'journalid' => 'JOURNAL',
        'batchno' => 'BATCHNO',
        'datecreated' => 'BATCH_DATE',
        'datemodified' => 'MODIFIED',
        'description' => 'BATCH_TITLE',
        'sourceentity' => 'BASELOCATION_NO',
        'state' => 'STATE',
        'gltransactionentries' => array(
            'glentry' => $glentry_structure,
        )
    ),
    'financialentity' => array(
        'finentid' => 'ID',
        'finentacctno' => 'ACCTNO',
        'finentname' => 'NAME',
        'finenttype' => 'TYPE',
        'glaccountno' => 'GLACCOUNTNO',
    ),
    'warehouse' => array(
        'warehouseid' => 'WAREHOUSEID',
        'name' => 'NAME',
        'parentid' => 'LOC.LOCATIONID',
        'warehouseparentid' => 'PARENTID', 
        'managerid' => 'MANAGERID',
        'contactname' => 'CONTACTINFO.CONTACTNAME',
        'shipto' => 'SHIPTO.CONTACTNAME',
        'usedingl' => 'USEDINGL',
        'status' => 'STATUS',
    ),
    'productline' => array(
        'productlineid' => 'PRODUCTLINEID',
        'productlinedesc' => 'DESCRIPTION',
        'parentid' => 'PARENTLINE',
        'status' => 'STATUS',
    ),
    'ictotal' => array(
        'totalname' => 'NAME',
        'status' => 'STATUS',
    ),
    'itemglgroup' => array(
        'glgroupname' => 'NAME',
    ),
    'custglgroup' => array(
        'glgroupname' => 'NAME',
    ),
    'vendglgroup' => array(
        'glgroupname' => 'NAME',
    ),
    'uom' => array(
        'groupname' => 'GROUPNAME',
        'unit' => 'UNIT',
        'abbreviation' => 'ABBREVIATION',
        'numdecimals' => 'NUMDECIMALS',
    ),
    'sopricelist' => array(
        'pricelistname' => 'NAME',
        'datefrom' => 'DATEFROM',
        'dateto' => 'DATETO',
        'status' => 'STATUS',
    ),
    'vsoepricelist' => array(
        'name' => 'NAME',
        'description' => 'DESCRIPTION',
        'createdby' => 'CREATEDBY',
        'status' => 'STATUS',
        'isdefault' => 'ISDEFAULT',
    ),
    'vsoeitempricelist' => array(
        'key' => 'RECORDNO',
        'pricelistid' => 'PRICELISTID',
        'itemid' => 'ITEMID',
        'currency' => 'CURRENCY',
        'valuebase' => 'VALUEBASE',
        'percentbase' => 'PERCENTBASE',
        'percentof' => 'PERCENTOF',
        'usepricebands' => 'USEPRICEBANDS',
        'pricebandtype' => 'PRICEBANDTYPE',
        'pricebandrule' => 'PRICEBANDRULE',
        'status' => 'STATUS',
        'version' => 'VERSION',
        'priceentries' => array(
            'priceentry' => $priceentry_structure
        ),
    ),
    'priceentry' => array(
        'startdate' => 'STARTDATE',
        'enddate' => 'ENDDATE',
        'value' => 'VALUE',
        'markup' => 'MARKUP',
        'markdown' => 'MARKDOWN',
        'bandup' => 'BANDUP',
        'banddown' => 'BANDDOWN',
        'memo' => 'MEMO'
    ),
    'popricelist' => array(
        'pricelistname' => 'NAME',
        'datefrom' => 'DATEFROM',
        'dateto' => 'DATETO',
        'status' => 'STATUS',
    ),
    'icitem' => array(
        'recordno' => 'RECORDNO',
        'itemid' => 'ITEMID',
        'itemname' => 'NAME',
        'extdescription' => 'EXTENDED_DESCRIPTION',
        'productlineid' => 'PRODUCTLINEID',
        'glgroupname' => 'GLGROUP',
        'substituteid' => 'SUBSTITUTEID',
        'shipweight' => 'SHIP_WEIGHT',
        'standardunit' => 'UOM.INVUOMDETAIL.UNIT',
        'purchasingunit' => 'UOM.POUOMDETAIL.UNIT',
        'purchasingunitfactor' => 'UOM.POUOMDETAIL.CONVFACTOR',
        'salesunit' => 'UOM.SOUOMDETAIL.UNIT',
        'salesunitfactor' => 'UOM.SOUOMDETAIL.CONVFACTOR',
        'uom' => 'UOMGRP',
        'costmethod' => 'COST_METHOD',
        'standardcost' => 'STANDARD_COST',
        'averagecost' => 'AVERAGE_COST',
        'whenlastsold' => 'WHENLASTSOLD',
        'whenlastreceived' => 'WHENLASTRECEIVED',
        'taxable' => 'TAXABLE',
        'taxgroup' => 'TAXGROUP.NAME',
        'defaultwhse' => 'DEFAULT_WAREHOUSE',
        'note' => 'NOTE',
        'itemtype' => 'ITEMTYPE',
        'enablefulfillment' => 'ENABLEFULFILLMENT',
        'whenmodified' => 'WHENMODIFIED',
        'status' => 'STATUS',
        'upc' => 'UPC',
        'hasstartenddates' => 'HASSTARTENDDATES',
        'termperiod' => 'TERMPERIOD',
        'totalperiods' => 'TOTALPERIODS',
        'computeforshortterm' => 'COMPUTEFORSHORTTERM',
        'taxcode' => 'TAXCODE',
        'revenue_posting' => 'REVPOSTING',
        'vsoecategory' => 'VSOECATEGORY',
        'vsoedlvrstatus' => 'VSOEDLVRSTATUS',
        'vsoerevdefstatus' => 'VSOEREVDEFSTATUS',
        'enable_bins' => 'ENABLE_BINS',
        'inventory_precision' => 'INV_PRECISION',
        'purchasing_precision' => 'PO_PRECISION',
        'sales_precision' => 'SO_PRECISION',
        'dropship'  => 'DROPSHIP',
        'buytoorder'  => 'BUYTOORDER',
        'cnbillingtemplate' => 'CNBILLINGTEMPLATE.NAME',
        'cnrevenuetemplate' => 'CNREVENUETEMPLATE.NAME',
        'cnrevenue2template' => 'CNREVENUE2TEMPLATE.NAME',
        'cnexpensetemplate' => 'CNEXPENSETEMPLATE.NAME',
        'cnexpense2template' => 'CNEXPENSE2TEMPLATE.NAME',
        'mrr' => 'MRR',
        'whslineitems' => array(
            'whslineitem' => $whslineitem_structure
        ),
        'vendlineitems' => array(
            'vendlineitem' => $vendlineitem_structure
        ),
        'complineitems' => array(
            'complineitem' => $complineitem_structure
        ),
        'autoprintlabel' => 'AUTOPRINTLABEL',
        'weightuom' => 'WEIGHTUOM',
        'netweight' => 'NETWEIGHT',
        'lwhuom' => 'LWHUOM',
        'length' => 'LENGTH',
        'width' => 'WIDTH',
        'height' => 'HEIGHT',
        'thicknessuom' => 'THICKNESSUOM',
        'thickness' => 'THICKNESS',
        'minimumthickness' => 'MINIMUMTHICKNESS',
        'maximumthickness' => 'MAXIMUMTHICKNESS',
        'areauom' => 'AREAUOM',
        'area' => 'AREA',
        'volumeuom' => 'VOLUMEUOM',
        'volume' => 'VOLUME',
        'diameteruom' => 'DIAMETERUOM',
        'innerdiameter' => 'INNERDIAMETER',
        'outerdiameter' => 'OUTERDIAMETER',
        'durometer' => 'DUROMETER',
        'densityuom' => 'DENSITYUOM',
        'density' => 'DENSITY',
        'upc12' => 'UPC12',
        'ean13' => 'EAN13',
        'safetyitem' => 'SAFETYITEM',
        'restricteditem' => 'RESTRICTEDITEM',
        'compliantitem' => 'COMPLIANTITEM',
        'condition' => 'CONDITION',
        'engineeringalert' => 'ENGINEERINGALERT',
        'specification1' => 'SPECIFICATION1',
        'specification2' => 'SPECIFICATION2',
        'specification3' => 'SPECIFICATION3',
        'engineeringapproval' => 'ENGINEERINGAPPROVAL',
        'qualitycontrolapproval' => 'QUALITYCONTROLAPPROVAL',
        'salesapproval' => 'SALESAPPROVAL',
        'primarycountryoforigin' => 'PRIMARYCOUNTRYOFORIGIN',
        'brand' => 'BRAND',
        'subbrand' => 'SUBBRAND',
        'category' => 'CATEGORY',
        'subcategory' => 'SUBCATEGORY',
        'catalogref' => 'CATALOGREF',
        'color' => 'COLOR',
        'style' => 'STYLE',
        'size1' => 'SIZE1',
        'size2' => 'SIZE2',
        'giftcard' => 'GIFTCARD',
        'webenabled' => 'WEBENABLED',
        'webname' => 'WEBNAME',
        'webshortdesc' => 'WEBSHORTDESC',
        'weblongdesc' => 'WEBLONGDESC',
    ),

    'ictransactiondef' => array(
        'transdefid' => 'DOCID',
        'transclass' => 'DOCCLASS'
    ),
    'sotransactiondef' => array(
        'transdefid' => 'DOCID',
        'transclass' => 'DOCCLASS'
    ),
    'potransactiondef' => array(
        'transdefid' => 'DOCID',
        'transclass' => 'DOCCLASS'
    ),
    'glbudget' => array(
        'budgetid' => 'BUDGETID',
        'description' => 'DESCRIPTION',
        'budgetdefault' => 'DEFAULT_BUDGET',
        'whenmodified' => 'MODIFIED',
        'userid' => 'USER',
        'status' => 'STATUS',
        'megadepartmentid' => 'MEGADEPARTMENTID',
        'megalocationid' => 'MEGALOCATIONID',
        'glbudgetitems' => array(
            'glbudgetitem' => array(
                'budgetid' => 'BUDGETID',
                'glaccountno' => 'ACCOUNTNO',
                'reportingperiodname' => 'NAME',
                'note' => 'NOTE',
                'amount' => 'AMOUNT',
                'locationid' => 'LOCTITLE',
                'departmentid' => 'DEPTTITLE',
            ),
        ),
    ),
    'glbudgetitem' => array(
        'budgetid' => 'BUDGETID',
        'budgetkey' => 'BUDGETKEY',
        'recordno' => 'RECORDNO',
        'glaccountno' => 'GLACCOUNTNO',
        'title' => 'ACCTTITLE',
        'accountkey' => 'ACCOUNTKEY',
        'normalbalance' => 'NORMALBALANCE',
        'note' => 'NOTE',
        'departmentid' => 'DEPARTMENTID',
        'deptkey' => 'DEPTKEY',
        'locationid' => 'LOCATIONID',
        'locationkey' => 'LOCATIONKEY',
        'basedon' => 'BASEDON',
        'growby' => 'GROWBY',
        'reportingperiodname' => 'REPORTINGPERIODNAME',
        'periodkey' => 'PERIODKEY',
        'perperiod' => 'PERPERIOD',
        'startdate' => 'PSTARTDATE',
        'enddate' => 'PENDDATE',
        'amount' => 'AMOUNT',
    ),
    'territory' => array(
        'territoryid' => 'TERRITORYID',
        'name' => 'NAME',
        'parentid' => 'PARENT.TERRITORYID',
        'managerid' => 'MANAGER.EMPLOYEEID',
        'status' => 'STATUS'
    ),
    /////Added bill basic elements 
    'bill' => array(
        'key' => 'RECORDNO',
        'vendorid' => 'VENDORID',
        'datecreated' => 'WHENCREATED',
        'datedue' => 'WHENDUE',
        'datepaid' => 'WHENPAID',
        'termname' => 'TERMNAME',
        'batchkey' => 'PRBATCHKEY',
        'billno' => 'RECORDID',
        'ponumber' => 'DOCNUMBER',
        'totalamount' => 'TOTALENTERED',
        'totalpaid' => 'TOTALPAID',
        'totaldue' => 'TOTALDUE',
        'totalselected' => 'TOTALSELECTED',
        'description' => 'DESCRIPTION',
        'trx_totalamount' => 'TRX_TOTALENTERED',
        'trx_totalpaid' => 'TRX_TOTALPAID',
        'trx_totaldue' => 'TRX_TOTALDUE',
        'trx_totalselected' => 'TRX_TOTALSELECTED',
        'basecurr' => 'BASECURR',
        'currency' => 'CURRENCY',
        'payto' => array('contactname' => 'BILLTOPAYTOCONTACTNAME'),
        'returnto' => array('contactname' => 'SHIPTORETURNTOCONTACTNAME'),
        'inclusivetax' => 'INCLUSIVETAX',
        'taxsolutionid' => 'TAXSOLUTIONID',
        'billitems' => array(
            'lineitem' => array(
                'accountlabel' => 'ACCOUNTLABEL',
                'glaccountno' => 'ACCOUNTNO',
                'glaccounttitle' => 'ACCOUNTTITLE',
                'amount' => 'AMOUNT',
                'totalpaid' => 'TOTALPAID',
                'memo' => 'ENTRYDESCRIPTION',
                'locationid' => 'LOCATIONID',
                'departmentid' => 'DEPARTMENTID',
                'trx_amount' => 'TRX_AMOUNT',
                'trx_totalpaid' => 'TRX_TOTALPAID',
                'projectid' => 'PROJECTID',
                'customerid' => 'CUSTOMERID',
                'vendorid' => 'VENDORID',
                'employeeid' => 'EMPLOYEEID',
                'itemid' => 'ITEMID',
                'classid' => 'CLASSID',
                'contractid' => 'CONTRACTID',
                'warehouseid' => 'WAREHOUSEID',
                'billable' => 'BILLABLE',
                'offsetglaccountno' => 'OFFSETGLACCOUNTNO',
                'taskid' => 'TASKID',
                'costtypeid' => 'COSTTYPEID',
                'retainagepercentage' => 'RETAINAGEPERCENTAGE',
                'amountretained' => 'AMOUNTRETAINED',
                'trx_amountretained' => 'TRX_AMOUNTRETAINED',
                'primarydockey' => 'PRIMARYDOCKEY',
                'primarydoclinekey' => 'PRIMARYDOCLINEKEY',
            )
        ),
        'whenmodified' => 'WHENMODIFIED',
        'state' => 'STATE',
        'supdocid' => 'SUPDOCID',
        'retainagereleased' => 'RETAINAGERELEASED'
    ),

    ///Added invoice basic elements 
    'invoice' => array(
        'key' => 'RECORDNO',
        'customerid' => 'CUSTOMERID',
        'datecreated' => 'WHENCREATED',
        'datedue' => 'WHENDUE',
        'datepaid' => 'WHENPAID',
        'termname' => 'TERMNAME',
        'batchkey' => 'PRBATCHKEY',
        'invoiceno' => 'RECORDID',
        'ponumber' => 'DOCNUMBER',
        'totalamount' => 'TOTALENTERED',
        'totalpaid' => 'TOTALPAID',
        'totaldue' => 'TOTALDUE',
        'description' => 'DESCRIPTION',
        'trx_totalamount' => 'TRX_TOTALENTERED',
        'trx_totalpaid' => 'TRX_TOTALPAID',
        'trx_totaldue' => 'TRX_TOTALDUE',
        'trx_totalselected' => 'TRX_TOTALSELECTED',
        'basecurr' => 'BASECURR',
        'currency' => 'CURRENCY',
        'billto' => array('contactname' => 'BILLTOPAYTOCONTACTNAME'),
        'shipto' => array('contactname' => 'SHIPTORETURNTOCONTACTNAME'),
        'taxsolutionid' => 'TAXSOLUTIONID',
        'invoiceitems' => array(
            'lineitem' => array(
                'accountlabel' => 'ACCOUNTLABEL',
                'glaccountno' => 'ACCOUNTNO',
                'glaccounttitle' => 'ACCOUNTTITLE',
                'amount' => 'AMOUNT',
                'totalpaid' => 'TOTALPAID',
                'memo' => 'ENTRYDESCRIPTION',
                'locationid' => 'LOCATIONID',
                'departmentid' => 'DEPARTMENTID',
                'trx_amount' => 'TRX_AMOUNT',
                'trx_totalpaid' => 'TRX_TOTALPAID',
                'projectid' => 'PROJECTID',
                'customerid' => 'CUSTOMERID',
                'vendorid' => 'VENDORID',
                'employeeid' => 'EMPLOYEEID',
                'itemid' => 'ITEMID',
                'classid' => 'CLASSID',
                'contractid' => 'CONTRACTID',
                'warehouseid' => 'WAREHOUSEID',
                'offsetglaccountno' => 'OFFSETGLACCOUNTNO',
                'taskid' => 'TASKID',
                'costtypeid' => 'COSTTYPEID',
                'retainagepercentage' => 'RETAINAGEPERCENTAGE',
                'amountretained' => 'AMOUNTRETAINED',
                'trx_amountretained' => 'TRX_AMOUNTRETAINED',
            )
        ),
        'whenmodified' => 'WHENMODIFIED',
        'state' => 'STATE',
        'supdocid' => 'SUPDOCID',
        'retainagereleased' => 'RETAINAGERELEASED'
    ),
    'araging' => array(
        'customerid' => 'CUSTOMERID',
        'aging' => array('agingperiod' => 'AGINGPERIOD',
            'totalamount' => 'TOTALAMOUNT',
            'agingdetails' => array(
                'agingdetail' => array(
                    'invoiceno' => 'INVOICENO',
                    'totaldue' => 'TOTALDUE',
                    'agingdate' => 'AGINGDATE',
                    'age' => 'AGE',
                )
            )
        )
    ),
    'taxdetail' => array(
        'detailid' => 'DETAILID',
        'description' => 'DESCRIPTION',
        'taxtype' => 'TAXTYPE',
        'value' => 'VALUE',
        'mintaxable' => 'MINTAXABLE',
        'maxtaxable' => 'MAXTAXABLE',
        'include' => 'INCLUDE',
        'mintax' => 'MINTAX',
        'maxtax' => 'MAXTAX',
        'glaccountno' => 'GLACCOUNT',
        'accountlabel' => 'ACCOUNTLABEL',
        'taxauthority' => 'TAXAUTHORITY',
        'status' => 'STATUS'
    ),
    'taxschedule' => array(
        'recordno' => 'RECORDNO',
        'scheduleid' => 'SCHEDULEID',
        'description' => 'DESCRIPTION',
        'taxtype' => 'TAXTYPE',
        'status' => 'STATUS',
        'taxsolutionid' => 'TAXSOLUTIONID'
    ),
    'taxscheduledetail' => array(
        'recordno' => 'RECORDNO',
        'scheduleid' => 'SCHEDULEID',
        'detailid' => 'DETAILID'
    ),
    'contacttaxgroup' => array(
        'name' => 'NAME',
    ),
    'itemtaxgroup' => array(
        'name' => 'NAME',
    ),
    'taxschedulemap' => array(
        'recordno' => 'RECORDNO',
        'documenttype' => 'DOCTYPE',
        'itemtaxgroup' => 'ITEMGROUP',
        'accountgroup' => 'ACCOUNTGROUP',
        'contacttaxgroup' => 'ENTITYGROUP',
        'scheduleid' => 'TAXSCHED',
        'module' => 'MODULE'
    ),
    'revrectemplate' => array(
        'recordno' => 'RECORDNO',
        'templateid' => 'TEMPLATEID',
        'description' => 'DESCRIPTION',
        'scheduleperiod' => 'SCHEDULEPERIOD',
        'postingday' => 'POSTINGDAY',
        'totalperiods' => 'TOTALPERIODS',
        'recmethod' => 'RECMETHOD',
        'recstartdate' => 'RECSTARTDATE',
        'postingmethod' => 'POSTINGMETHOD',
        'status' => 'STATUS',
        'recognitionterm' => 'RECOGNITIONTERM'
    ),
    // added revrecschedule TM 01.28.08
    'revrecschedule' => array(
        'recordno' => 'RECORDNO',
        'revrectemplatekey' => 'REVRECTEMPLATEKEY',
        'revrectemplateid' => 'REVRECTEMPLATEID',
        'templatepostingmethod' => 'TEMPLATEPOSTINGMETHOD',
        'recmethod' => 'RECMETHOD',
        'completed' => 'COMPLETED',
        'invoiceno' => 'INVOICENO',
        'invoicedate' => 'INVOICEDATE',
        'invoicekey' => 'RECORDKEY',
        'sodocumentid' => 'DOCID',
        'sodocumentdate' => 'DOCUMENTDATE',
        'description' => 'DESCRIPTION',
        'invoiceitemkey' => 'PRENTRYKEY',
        'sodocumententrykey' => 'DOCENTRYKEY',
        'status' => 'STATUS',
        'changecategorykey' => 'REVRECCATKEY',
        'changecategory' => 'CHANGECATEGORY',
        'changememo' => 'CHANGEMEMO',
        'calculationmethod' => 'PACALCSOURCE',
        'calculationbasedon' => 'PACALCHOURS',
        'projectkey' => 'PROJECTKEY',
        'projectid' => 'PROJECTID',
        'projectname' => 'PROJECTNAME',
        'taskkey' => 'TASKKEY',
        'taskname' => 'TASKNAME',
    ),
    // added revrecscheduleentry TM 01.28.08
    'revrecscheduleentry' => array(
        'recordno' => 'RECORDNO',
        'revrecschedulekey' => 'REVRECSCHEDULEKEY',
        'posted' => 'POSTED',
        'position' => 'POSITION',
        'next' => 'NEXT',
        'postingdate' => 'POSTINGDATE',
        'revacctkey' => 'REVACCTKEY',
        'accountno' => 'ACCOUNTNO',
        'accounttitle' => 'ACCOUNTTITLE',
        'journalkey' => 'JOURNALKEY',
        'gljournal' => 'GLJOURNAL',
        'deferredrevacctkey' => 'DEFERREDREVACCTKEY',
        'amount' => 'AMOUNT',
        'glbatchkey' => 'GLBATCHKEY',
        'schopkey' => 'SCHOPKEY',
        'tr_type' => 'TR_TYPE',
        'trx_amount' => 'TRX_AMOUNT',
        'currency' => 'CURRENCY',
        'posted_amount' => 'POSTED_AMOUNT',
        'exchratedate' => 'EXCH_RATE_DATE',
        'exchratetypeid' => 'EXCH_RATE_TYPE_ID',
        'exchrate' => 'EXCHANGE_RATE',
        'description' => 'DESCRIPTION',
        'budgetqty' => 'BUDGETQTY',
        'estqty' => 'ESTQTY',
        'plannedqty' => 'PLANNEDQTY',
        'actualqty' => 'ACTUALQTY',
        'percentcompleted' => 'PERCENTCOMPLETED',
        'percentrecognized' => 'PERCENTRECOGNIZED',
    ),
    'revrecchangelog' => array(
        'recordno' => 'RECORDNO',
        'revrecschedulekey' => 'REVRECSCHEDULEKEY',
        'modifieduserkey' => 'MODIFIEDUSERKEY',
        'modifiedby' => 'MODIFIEDBY',
        'whenmodified' => 'WHENMODIFIED',
        'summary' => 'SUMMARY',
        'memo' => 'MEMO',
    ),
    'subscription' => array(
        'object' => 'OBJECT',
        'intacctid' => 'INTACCTID',
        'subscriber' => 'SUBSCRIBER',
        'externalid' => 'EXTERNALID'
    ),
    'sotransitem' => array(
        'key' => 'RECORDNO',
        'recordno' => 'RECORDNO',
        'dochdrno' => 'DOCHDRNO',
        'docid' => 'DOCUMENT.DOCID',
        'itemid' => 'ITEMID',
        'itemaliasid' => 'ITEMALIASID',
        'itemdesc' => 'ITEMDESC',
        'line_no' => 'LINE_NO',
        'warehouseid' => 'WAREHOUSE.LOCATION_NO',
        'quantity' => 'UIQTY',
        'unit' => 'UNIT',
        'linelevelsimpletaxtype' => 'LINELEVELSIMPLETAXTYPE',
        'price' => 'UIPRICE',
        'retailprice' => 'RETAILPRICE',
        'totalamount' => 'UIVALUE',
        //uncomment below lines with GA of PDLC-0531-13
        'taxrate' => 'PERCENTVAL',
        'tax'           =>  'TAXABSVAL',
        'grossamount'   => 'LINETOTAL',
        'locationid' => 'LOCATIONID',
        'departmentid' => 'DEPARTMENTID',
        'memo' => 'MEMO',
        'discsurchargememo' => 'DISCOUNT_MEMO',
        'revrectemplate' => 'REVRECTEMPLATE',
        'revrecstartdate' => 'REVRECSTARTDATE',
        'revrecenddate' => 'REVRECENDDATE',
        'renewalmacro' => 'RENEWALMACRO',
        'currency' => 'CURRENCY',
        'exchratedate' => 'EXCHRATEDATE',
        'exchratetype' => 'EXCHRATETYPE',
        'exchrate' => 'EXCHRATE',
        'trx_price' => 'TRX_PRICE',
        'trx_value' => 'TRX_VALUE',
        'projectid' => 'PROJECTID',
        'customerid' => 'CUSTOMERID',
        'vendorid' => 'VENDORID',
        'employeeid' => 'EMPLOYEEID',
        'classid' => 'CLASSID',
        'contractid' => 'CONTRACTID',        
        'taskno' => 'TASKKEY',
        'billingtemplate' => 'BILLINGTEMPLATE',
        'discountpercent' => 'DISCOUNTPERCENT',
        'multiplier' => 'MULTIPLIER',
        'dropship'  => 'DROPSHIP',
        'buytoorder'  => 'BUYTOORDER',
        'shipto' => array('contactname' => 'SHIPTO.CONTACTNAME'),
        'taskid' => 'TASKID',
        'costtypeid' => 'COSTTYPEID',
        'needbydate' => 'NEEDBYDATE',
        'shipby' => 'SHIPBY',
        'donotshipbeforedate' => 'DONOTSHIPBEFOREDATE',
        'donotshipafterdate' => 'DONOTSHIPAFTERDATE',
        'datepickticketprinted' => 'DATEPICKTICKETPRINTED',
        'cancelafterdate' => 'CANCELAFTERDATE',
        'shippeddate' => 'SHIPPEDDATE',
        'addedbychange' => 'ADDEDBYCHANGE',
        'projectcontractid' => 'PROJECTCONTRACTID',
        'projectcontractkey' => 'PROJECTCONTRACTKEY',
        'projectcontractlineid' => 'PROJECTCONTRACTLINEID',
        'projectcontractlinekey' => 'PROJECTCONTRACTLINEKEY',
        'pcblexternalrefno' => 'PCBLEXTERNALREFNO',
        'pcbldescription' => 'PCBLDESCRIPTION',
        'pcblbillingtype' => 'PCBLBILLINGTYPE',
        'contractlinevalue' => 'CONTRACTLINEVALUE',
        'priorapplicationamt' => 'PRIORAPPLICATIONAMT',
        'completedthisperiod' => 'COMPLETEDTHISPERIOD',
        'storedmaterials' => 'STOREDMATERIALS',
        'totalcompletedtodate' => 'TOTALCOMPLETEDTODATE',
        'percentcompletedtodate' => 'PERCENTCOMPLETEDTODATE',
        'balanceremaining' => 'BALANCEREMAINING',
        'previousretainagebalance' => 'PREVIOUSRETAINAGEBALANCE',
        'isretainagerelease' => 'ISRETAINAGERELEASE',
        'retainagetobill'  => 'RETAINAGETOBILL',
        'retainagebalance' => 'RETAINAGEBALANCE',
        'issummarized' => 'ISSUMMARIZED',
        'taxscheduleid' => 'TAXSCHEDULEID',
        'reverseconversion' => 'REVERSECONVERSION',
        'reversepriceconverted' => 'REVERSEPRICECONVERTED',
        'reverseqtyconverted' => 'REVERSEQTYCONVERTED',
        'stdpriceconverted' => 'STDPRICECONVERTED',
        'stdqtyconverted' => 'STDQTYCONVERTED',
        'sourcedocid' => 'SOURCEDOCID',
        'sourcedoclineid' => 'SOURCEDOCLINEID',
    ),
    'pricelistitem' => array(
        'recordno' => 'RECORDNO',
        'pricelistid' => 'PRICELISTID',
        'itemid' => 'ITEMID',
        'productlineid' => 'PRODUCTLINEID',
        'item_line' => 'ITEM_LINE',
        'datefrom' => 'DATEFROM',
        'dateto' => 'DATETO',
        'qtylimitmin' => 'QTYLIMITMIN',
        'qtylimitmax' => 'QTYLIMITMAX',
        'value' => 'VALUE',
        'valuetype' => 'VALUETYPE',
        'fixed' => 'FIXED',
        'status' => 'STATUS',
        'currency' => 'CURRENCY',
        'employeeid' => 'EMPLOYEEID',
    ),
    'renewalmacro' => array(
        'recordno' => 'RECORDNO',
        'macroid' => 'MACROID',
        'description' => 'DESCRIPTION',
        'createsalestrans' => 'CREATESALESTRANS',
        'documentparams.docid' => 'SALESDOCID',
        'salesdocrecordkey' => 'SALESDOCRECORDKEY',
        'createsalestranswhen' => 'CREATESALESTRANSWHEN',
        'createsalestransdays' => 'CREATESALESTRANSDAYS',
        'reneweddocdate' => 'RENEWEDDOCDATE',
        'renstartdateopt' => 'RENSTARTDATEOPT',
        'pricingtype' => 'PRICINGTYPE',
        'pricingmarkup' => 'PRICINGMARKUP',
        'pricingmarkupvalue' => 'PRICINGMARKUPVALUE',
        'emailtocustomer' => 'EMAILTOCUSTOMER',
        'emailtocustomerfromemailid' => 'EMAILTOCUSTOMERFROMEMAILID',
        'emailcontact' => 'EMAILCONTACT',
        'emailcontactfilename' => 'EMAILCONTACTFILENAME',
        'emailalertfromemailid' => 'EMAILALERTFROMEMAILID',
        'emailalert' => 'EMAILALERT',
        'emailalertfilename' => 'EMAILALERTFILENAME',
        'emailalertaddresses' => 'EMAILALERTADDRESSES',
        'emailalertwhen' => 'EMAILALERTWHEN',
        'emailalertdays' => 'EMAILALERTDAYS',
        'createsalesopp' => 'CREATESALESOPP',
        'createsalesoppwhen' => 'CREATESALESOPPWHEN',
        'createsalesoppdays' => 'CREATESALESOPPDAYS',
        'stageofopportunity' => 'STAGEOFOPPORTUNITY',
        'latestversionkey' => 'LATESTVERSIONKEY',
        'status' => 'STATUS',
    ),
    'smarteventlog' => array(
        'recordno' => 'RECORDNO',
        'smartlinkid' => 'SMARTLINKID',
        'topic' => 'TOPIC',
        'ownerobject' => 'OWNEROBJECT',
        'timestamp' => 'TIMESTAMP',
        'userid' => 'USERID',
        'objectkey' => 'OBJECTKEY',
    ),

    'appaymentrequest' => array(
        'recordno' => 'RECORDNO',
        'vendorid' => 'VENDORID',
        'vendorname' => 'VENDORNAME',
        'paymenttype' => 'PAYMENTTYPE',
        'paymentdate' => 'PAYMENTDATE',
        'receiptdate' => 'RECEIPTDATE',
        'paymentamount' => 'PAYMENTAMOUNT',
        'documentnumber' => 'DOCUMENTNUMBER',
        'memo' => 'MEMO',
        'state' => 'STATE',
        'status' => 'STATUS',
        'bankaccountid' => 'FINANCIALACCOUNT',
        'whenmodified' => 'WHENMODIFIED',
        'termname' => 'TERMNAME',
        'cleared' => 'CLEARED',
        'cleareddate' => 'CLRDATE',
        'systemgenerated' => 'SYSTEMGENERATED',
        'vendordue' => 'VENDORDUE',
        'currency' => 'CURRENCY',
        'paymenttrxamount' => 'PAYMENTTRXAMOUNT',
    ),
    'project' => array(
        'key' => 'PROJECTID',
        'name' => 'NAME',
        'description' => 'DESCRIPTION',
        'parentid' => 'PARENTID',
        'invoicewithparent' => 'INVOICEWITHPARENT',
        'projectcategory' => 'PROJECTCATEGORY',
        'projecttype' => 'PROJECTTYPE',
        'projectstatus' => 'PROJECTSTATUS',
        'customerid' => 'CUSTOMERID',
        'managerid' => 'MANAGERID',
        'custuserid' => 'CUSTUSERID',
        'salescontactid' => 'SALESCONTACTID',
        'begindate' => 'BEGINDATE',
        'enddate' => 'ENDDATE',
        'departmentid' => 'DEPARTMENTID',
        'locationid' => 'LOCATIONID',
        'classid' => 'CLASSID',
        'supdocid' => 'SUPDOCID',
        'currency' => 'CURRENCY',
        'billingtype' => 'BILLINGTYPE',
        'termname' => 'TERMNAME',
        'docnumber' => 'DOCNUMBER',
        'contactinfo' => array('contactname' => 'CONTACTINFO.CONTACTNAME'),
        'billto' => array('contactname' => 'BILLTO.CONTACTNAME'),
        'shipto' => array('contactname' => 'SHIPTO.CONTACTNAME'),
        'sonumber' => 'SONUMBER',
        'ponumber' => 'PONUMBER',
        'poamount' => 'POAMOUNT',
        'pqnumber' => 'PQNUMBER',
        'whenmodified' => 'WHENMODIFIED',
        'budgetamount' => 'BUDGETAMOUNT',
        'budgetedcost' => 'BUDGETEDCOST',
        'budgetduration' => 'BUDGETQTY',
        'userrestrictions' => 'USERRESTRICTIONS',
        'percentcomplete' => 'PERCENTCOMPLETE',
        'obspercentcomplete' => 'OBSPERCENTCOMPLETE',
        'billingrate' => 'BILLINGRATE',
        'billingpricing' => 'BILLINGPRICING',
        'expenserate' => 'EXPENSERATE',
        'expensepricing' => 'EXPENSEPRICING',
        'poaprate' => 'POAPRATE',
        'poappricing' => 'POAPPRICING',
        'budgetid' => 'BUDGETID',
        'status' => 'STATUS',
        'invoicemessage' => 'INVOICEMESSAGE',
        'invoicecurrency' => 'INVOICECURRENCY',
        'projectresources' => array(
            'projectresource' => array(
                'employeeid' => 'EMPLOYEEID',
                'itemid' => 'ITEMID',
                'description' => 'DESCRIPTION',
                'startdate' => 'STARTDATE',
                'billingrate' => 'BILLINGRATE',
                'expenserate' => 'EXPENSERATE',
                'poaprate' => 'POAPRATE'
            )
        ),
        'billingovermax' => 'BILLINGOVERMAX',
        'excludeexpenses' => 'EXCLUDEEXPENSES',
        'scope' => 'SCOPE',
        'inclusions' => 'INCLUSIONS',
        'exclusions' => 'EXCLUSIONS',
        'terms' => 'TERMS',
        'schedulestartdate' => 'SCHEDULESTARTDATE',
        'actualstartdate' => 'ACTUALSTARTDATE',
        'scheduledcompletiondate' => 'SCHEDULEDCOMPLETIONDATE',
        'revisedcompletiondate' => 'REVISEDCOMPLETIONDATE',
        'substantialcompletiondate' => 'SUBSTANTIALCOMPLETIONDATE',
        'actualcompletiondate' => 'ACTUALCOMPLETIONDATE',
        'noticetoproceed' => 'NOTICETOPROCEED',
        'responsedue' => 'RESPONSEDUE',
        'executedon' => 'EXECUTEDON',
        'scheduleimpact' => 'SCHEDULEIMPACT',
        'contractid' => 'CONTRACTID',
    ),
    'projecttype' => array(
        'name' => 'NAME',
        'status' => 'STATUS',
        'parentname' => 'PARENT.NAME',
    ),
    'projectstatus' => array(
        'name' => 'NAME',
        'description' => 'DESCRIPTION',
        'preventtimesheet' => 'PREVENTTIMESHEET',
        'preventexpense' => 'PREVENTEXPENSE',
        'preventappo' => 'PREVENTAPPO',
        'preventgeninvoice' => 'PREVENTGENINVOICE',
        'status' => 'STATUS',
    ),
    'task' => array(
        'key' => 'TASK.RECORD#',
        'projectid' => 'PROJECTID',
        'taskid' => 'TASKID',
        'name' => 'NAME',
        'taskno' => 'TASKNO',
        'description' => 'DESCRIPTION',
        'ismilestone' => 'ISMILESTONE',
        'parenttaskname' => 'PARENTTASKNAME',
        'budgetqty' => 'BUDGETQTY',
        'estqty' => 'ESTQTY',
        'priority' => 'PRIORITY',
        'pbegindate' => 'PBEGINDATE',
        'penddate' => 'PENDDATE',
        'percentcomplete' => 'PERCENTCOMPLETE',
        'obspercentcomplete' => 'OBSPERCENTCOMPLETE',
        'taskstatus' => 'TASKSTATUS',
        'utilized' => 'UTILIZED',
        'billable' => 'BILLABLE',
        'itemid' => 'ITEMID',
        'timetype' => 'TIMETYPENAME',
        'classid' => 'CLASSID',
        'supdocid' => 'SUPDOCID',
        'taskresources' => array(
            'taskresource' => array(
                'employeeid' => 'EMPLOYEEID',
            )
        ),
        'dependentonname' => 'DEPENDENTONNAME',
    ),
    'class' => array(
        'key' => 'CLASSID',
        'name' => 'NAME',
        'description' => 'DESCRIPTION',
        'parentid' => 'PARENTID',
        'whenmodified' => 'WHENMODIFIED',
        'status' => 'STATUS',
    ),
    'recurringgltransaction' => array(
        'key' => 'RECORDNO',
        'journalid' => 'JOURNAL',
        'batchno' => 'TRANSACTIONNO',
        'datecreated' => 'WHENCREATED',
        'description' => 'DESCRIPTION',
        'referenceno' => 'REFERENCENO',
        'lastposted' => 'LASTPOSTED',
        'lastreversed' => 'LASTREVERSED',
        'whencreated' => 'WHENCREATED',
        'whenmodified' => 'WHENMODIFIED',
        'status' => 'STATUS',
        'startdate' => 'STARTDATEJE',
        'enddate' => 'ENDDATEJE',
        'repeatcount' => 'OCCURJE',
        'repeatby' => 'REPEATJE',
        'interval' => 'REPEATINTERVALJE',
        'eom' => 'EOMJE',
        'errnotifyemailids' => 'EMAILJE',
        'revstartdate' => 'STARTDATEREVJE',
        'revenddate' => 'ENDDATEREVJE',
        'revrepeatcount' => 'OCCURREVJE',
        'reevrepeatby' => 'REPEATREVJE',
        'revinterval' => 'REPEATINTERVALREVJE',
        'reveom' => 'EOMREVJE',
        'reverrnotifyemailids' => 'EMAILREVJE',
        'recurglentry' => array
        (
            'recurglentrylineitem' => array
            (
                'recurglbatchno' => 'RECURGLBATCHNO',
                'locationid' => 'LOCATION',
                'departmentid' => 'DEPARTMENT',
                'glaccountno' => 'ACCOUNTNO',
                'document' => 'DOCUMENT',
                'lineno' => 'LINE_NO',
                'trtype' => 'TR_TYPE',
                'amount' => 'AMOUNT',
                'memo' => 'MEMO',
                'whencreated' => 'WHENCREATED',
                'username' => 'USERNAME',
                'units' => 'UNITS',
            )
        ),
    ),
    'recurringstatgltrans' => array(
        'key' => 'RECORDNO',
        'journalid' => 'JOURNAL',
        'batchno' => 'TRANSACTIONNO',
        'datecreated' => 'WHENCREATED',
        'description' => 'DESCRIPTION',
        'referenceno' => 'REFERENCENO',
        'lastposted' => 'LASTPOSTED',
        'lastreversed' => 'LASTREVERSED',
        'whencreated' => 'WHENCREATED',
        'whenmodified' => 'WHENMODIFIED',
        'status' => 'STATUS',
        'startdate' => 'STARTDATEJE',
        'enddate' => 'ENDDATEJE',
        'repeatcount' => 'OCCURJE',
        'repeatby' => 'REPEATJE',
        'interval' => 'REPEATINTERVALJE',
        'eom' => 'EOMJE',
        'errnotifyemailids' => 'EMAILJE',
        'revstartdate' => 'STARTDATEREVJE',
        'revenddate' => 'ENDDATEREVJE',
        'revrepeatcount' => 'OCCURREVJE',
        'reevrepeatby' => 'REPEATREVJE',
        'revinterval' => 'REPEATINTERVALREVJE',
        'reveom' => 'EOMREVJE',
        'reverrnotifyemailids' => 'EMAILREVJE',
        'recurglentry' => array
        (
            'recurglentrylineitem' => array
            (
                'recurglbatchno' => 'RECURGLBATCHNO',
                'locationid' => 'LOCATION',
                'departmentid' => 'DEPARTMENT',
                'statglaccountno' => 'ACCOUNTNO',
                'document' => 'DOCUMENT',
                'lineno' => 'LINE_NO',
                'trtype' => 'TR_TYPE',
                'amount' => 'AMOUNT',
                'memo' => 'MEMO',
                'whencreated' => 'WHENCREATED',
                'username' => 'USERNAME',
                'units' => 'UNITS',
            )
        ),
    ),
    'cctransaction' => array(
        'record' => 'RECORDNO',
        'chargecardid' => 'FINANCIALENTITY',
        'description' => 'DESCRIPTION',
        'payee' => 'DESCRIPTION2',
        'referenceno' => 'RECORDID',
        'paymentdate' => 'WHENCREATED',
        'totalentered' => 'TOTALENTERED',
        'trx_totalentered' => 'TRX_TOTALENTERED',
        'totalpaid' => 'TOTALPAID',
        'trx_totalpaid' => 'TRX_TOTALPAID',
        'currency' => 'CURRENCY',
        'state' => 'STATE',
        'cleared' => 'CLEARED',
        'inclusivetax' => 'INCLUSIVETAX',
        'taxsolutionid' => 'TAXSOLUTIONID',
        'ccpayitems' => array(
            'ccpayitem' => array(
                'line_num' => 'LINE_NO',
                'glaccountno' => 'ACCOUNTNO',
                'accountlabel' => 'ACCOUNTLABEL',
                'description' => 'DESCRIPTION',
                'paymentamount' => 'TRX_AMOUNT',
                'currency' => 'CURRENCY',
                'exchratedate' => 'EXCH_RATE_DATE',
                'exchratetype' => 'EXCH_RATE_TYPE_ID',
                'exchrate' => 'EXCHANGE_RATE',
                'departmentid' => 'DEPARTMENTID',
                'locationid' => 'LOCATIONID',
                'projectid' => 'PROJECTID',
                'customerid' => 'CUSTOMERID',
                'vendorid' => 'VENDORID',
                'employeeid' => 'EMPLOYEEID',
                'itemid' => 'ITEMID',
                'classid' => 'CLASSID',
                'taskid' => 'TASKID',
                'costtypeid' => 'COSTTYPEID',
            )
        )
    ),
    'ccpayitem' => array(
        'line_num' => 'LINE_NO',
        'glaccountno' => 'ACCOUNTNO',
        'accountlabel' => 'ACCOUNTLABEL',
        'description' => 'DESCRIPTION',
        'paymentamount' => 'TRX_AMOUNT',
        'currency' => 'CURRENCY',
        'exchratedate' => 'EXCH_RATE_DATE',
        'exchratetype' => 'EXCH_RATE_TYPE_ID',
        'exchrate' => 'EXCHANGE_RATE',
        'departmentid' => 'DEPARTMENTID',
        'locationid' => 'LOCATIONID',
        'projectid' => 'PROJECTID',
        'customerid' => 'CUSTOMERID',
        'vendorid' => 'VENDORID',
        'employeeid' => 'EMPLOYEEID',
        'itemid' => 'ITEMID',
        'classid' => 'CLASSID',
        'taskid' => 'TASKID',
        'costtypeid' => 'COSTTYPEID',
    ),
    'vsoeallocation' => array(
        'lineentryallocation' => array(
            'vsoelineentry' => array(
                'docid' => 'DOCID',
                'itemid' => 'ITEMID',
                'doc_line_no' => 'LINENO',
                'line_no' => 'ENTRYLINENO',
                'invoiceprice' => 'INVOICEPRICE',
                'deliverystatus' => 'DLVRSTATUS',
                'deliverydate' => 'DLVRDATE',
                'deferralstatus' => 'REVDEFSTATUS',
            )
        ),
    ),
    'vsoelineentry' => array(
        'docid' => 'DOCID',
        'itemid' => 'ITEMID',
        'doc_line_no' => 'LINENO',
        'line_no' => 'ENTRYLINENO',
        'invoiceprice' => 'INVOICEPRICE',
        'deliverystatus' => 'DLVRSTATUS',
        'deliverydate' => 'DLVRDATE',
        'deferralstatus' => 'REVDEFSTATUS',
    ),

);
// NOTE: Do not put whenmodified in the translatefunctions array.  It is taken care of
// in the translateBLfunctions array.  TM 6/16/09
$translatefunctions = array(
    'memberstype' => 'upperfirst',
    'deliveryoptions' => 'deliveryoptions',
    'contactlist' => 'contactlist',
    'birthdate' => 'xmldate',
    'startdate' => 'xmldate',
    'datecreated' => 'xmldate',
    'dateposted' => 'xmldate',
    'datedue' => 'xmldate',
    'datemodified' => 'xmldate',
    'depositdate' => 'xmldate',
    'begindate' => 'xmldate',
    'enddate' => 'xmldate',
    'entrydate' => 'xmldate',
    'datefrom' => 'xmldate',
    'dateto' => 'xmldate',
    'lastreconcileddate' => 'xmldate',
    'whenlastsold' => 'xmldate',
    'whenlastreceived' => 'xmldate',
    'due' => 'termdue',
    'discount' => 'termdiscount',
    'penalty' => 'termpenalty',
    'trtype' => 'xmltrtype',
    'form1099' => 'xmlform1099',
    'paymentdate' => 'xmldate',
    'receiveddate' => 'xmldate',
    'revrecstartdate' => 'xmldate',
    'revrecenddate' => 'xmldate',
    'onhold' => 'xmlboolean',
    'cardnum' => 'DecryptCardNum',
    'maskcardnum' => 'ObfuscateCardNum',
    'invoicedate' => 'xmldate',
    'sodocumentdate' => 'xmldate',
    'exchratedate' => 'xmldate',
    'postingdate' => 'xmldate',
    'nextexecdate' => 'xmldate',
    'recurduedate' => 'xmldate',
    'receiptdate' => 'xmldate',
    'cleareddate' => 'xmldate',
    'ratestartdate' => 'xmldate',
    'rateenddate' => 'xmldate',
    'restricted_locs' => 'formatLocations',
    'restricted_depts' => 'formatDepartments',
    'itemexpiration' => 'xmldate',
    'needbydate' => 'xmldate',
    'shipbydate' => 'xmldate',
    'cancelafterdate' => 'xmldate',
    'donotshipafterdate' => 'xmldate',
    'donotshipbeforedate' => 'xmldate',
    'servicedeliverydate' => 'xmldate',
    'promiseddate' => 'xmldate',
    'shippeddate' => 'xmldate',
    'contractstartdate' => 'xmldate',
    'contractenddate' => 'xmldate',
    'shipby' => 'xmldate',
    'datepickticketprinted' => 'xmldate',
    'dateconfirmed' => 'xmldate',
    'dateshiptosupplier' => 'xmldate',
    'schedulestartdate' => 'xmldate',
    'actualstartdate' => 'xmldate',
    'scheduledcompletiondate' => 'xmldate',
    'revisedcompletiondate' => 'xmldate',
    'substantialcompletiondate' => 'xmldate',
    'actualcompletiondate' => 'xmldate',
    'noticetoproceed' => 'xmldate',
    'responsedue' => 'xmldate',
    'executedon' => 'xmldate',
    'internalissuedon' => 'xmldate',
    'internalapprovedon' => 'xmldate',
    'internalsignedon' => 'xmldate',
    'externalapprovedon' => 'xmldate',
    'externalsignedon' => 'xmldate'
);

$translateBLfunctions = array(
    'trtype' => 'trtype2bl',
    'whenmodified' => 'gmttimestamp'
);

