<?  
//=============================================================================
//
//	FILE:			xml_functions.inc
//	AUTHOR:			<PERSON><PERSON>
//	DESCRIPTION:	This is the metadata used by the router for executing every function.
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

$xml_functions = array(
    'create_gltransaction' => array(
        'function' => 'CreateGLTransaction',
        'permissionkey' => array('gl/activities/journals/create', 'gl/lists/journal/create'),
        'preProcessing' => 'full'
    ),
    'get_list' => array(
        'function' => 'GetList',
        'preProcessing' => 'full'
    ),
    'create_customer' => array(
        'function' => 'ProcessCustomer',
        'permissionkey' => 'ar/lists/customer/create',
        'key' => 'customerid',
        'entity' => 'customer',
        'preProcessing' => 'partial'
    ),
    'delete_customer' => array(
        'key' => 'customerid',
        'permissionkey' => 'ar/lists/customer/delete',
        'entity' => 'customer'
    ),
    'create_vendor' => array(
        'function' => 'ProcessVendor',
        'permissionkey' => 'ap/lists/vendor/create',
        'key' => 'vendorid',
        'entity' => 'vendor',
        'preProcessing' => 'partial'
    ),
    'delete_vendor' => array(
        'key' => 'vendorid',
        'permissionkey' => 'ap/lists/vendor/delete',
        'entity' => 'vendor'
    ),
    'create_billbatch' => array(
        'function' => 'xmlgw_CreateBatch',
        'permissionkey' => array('ap/activities/apbatch/create'),
        'entity' => 'apbatch',
        'preProcessing' => 'full',
        'recordtype' => 'pi'
    ),
    'create_invoicebatch' => array(
        'function' => 'xmlgw_CreateBatch',
        'permissionkey' => array('ar/activities/arbatch/create'),
        'entity' => 'arbatch',
        'preProcessing' => 'full',
        'recordtype' => 'ri'
    ),
    'create_apadjustmentbatch' => array(
        'function' => 'xmlgw_CreateBatch',
        'permissionkey' => array('ap/activities/apbatch/create'),
        'entity' => 'apadjustmentbatch',
        'preProcessing' => 'full',
        'recordtype' => 'pa'
    ),
    'create_aradjustmentbatch' => array(
        'function' => 'xmlgw_CreateBatch',
        'permissionkey' => array('ar/activities/arbatch/create'),
        'entity' => 'aradjustmentbatch',
        'preProcessing' => 'full',
        'recordtype' => 'ra'
    ),
    'setcurrent_billbatch' => array(
        'function' => 'xmlgw_SetCurrentBatch',
        'preProcessing' => 'full',
        'recordtype' => 'pi'
    ),
    'setcurrent_invoicebatch' => array(
        'function' => 'xmlgw_SetCurrentBatch',
        'preProcessing' => 'full',
        'recordtype' => 'ri'
    ),
    'setcurrent_apadjustmentbatch' => array(
        'function' => 'xmlgw_SetCurrentBatch',
        'preProcessing' => 'full',
        'recordtype' => 'pa'
    ),
    'setcurrent_aradjustmentbatch' => array(
        'function' => 'xmlgw_SetCurrentBatch',
        'preProcessing' => 'full',
        'recordtype' => 'ra'
    ),
    'create_bill' => array(
        'function' => 'xmlgw_CreatePRRecord',
        'permissionkey' => array('ap/activities/apbatch/create', 'ap/lists/apbill/create'),
        'entity' => 'bill',
        'preProcessing' => 'full',
        'recordtype' => 'pi',
        'key' => 'key'
    ),
    'create_invoice' => array(
        'function' => 'xmlgw_CreatePRRecord',
        'permissionkey' => array('ar/activities/arbatch/create', 'ar/lists/arinvoice/create'),
        'entity' => 'invoice',
        'preProcessing' => 'full',
        'recordtype' => 'ri',
        'key' => 'key'
    ),
    'create_apadjustment' => array(
        'function' => 'xmlgw_CreatePRRecord',
        'permissionkey' => array('ap/activities/apadjustment/create', 'ap/lists/apadjustment/create'),
        'entity' => 'apadjustment',
        'preProcessing' => 'full',
        'recordtype' => 'pa',
        'key' => 'key'
    ),
    'create_aradjustment' => array(
        'function' => 'xmlgw_CreatePRRecord',
        'permissionkey' => array('ar/activities/aradjustment/create', 'ar/lists/aradjustment/create'),
        'entity' => 'aradjustment',
        'preProcessing' => 'full',
        'recordtype' => 'ra',
        'key' => 'key'
    ),
    'get_bill' => array(
        'function' => 'xmlgw_GetPRRecord',
        'permissionkey' => array('ap/activities/apbatch/view', 'ap/lists/apbill/view'),
        'entity' => 'bill',
        'preProcessing' => 'full'
    ),
    'get_invoice' => array(
        'function' => 'xmlgw_GetPRRecord',
        'permissionkey' => array('ar/activities/arbatch/view', 'ar/lists/arinvoice/view'),
        'entity' => 'invoice',
        'preProcessing' => 'full'
    ),
    'get_apadjustment' => array(
        'function' => 'xmlgw_GetPRRecord',
        'permissionkey' => array('ap/activities/apbatch/view', 'ap/lists/apadjustment/create'),
        'entity' => 'apadjustment',
        'preProcessing' => 'full'
    ),
    'get_aradjustment' => array(
        'function' => 'xmlgw_GetPRRecord',
        'permissionkey' => array('ar/activities/arbatch/view','ar/lists/aradjustment/create'),
        'entity' => 'aradjustment',
        'preProcessing' => 'full'
    ),
    'update_bill' => array(
        'function' => 'xmlgw_UpdatePRRecord',
        'permissionkey' => array('ap/activities/apbatch/edit','ap/lists/apbill/edit'),
        'entity' => 'bill',
        'preProcessing' => 'full',
        'recordtype' => 'pi',
        'key' => 'key'
    ),
    'update_invoice' => array(
        'function' => 'xmlgw_UpdatePRRecord',
        'permissionkey' => array('ar/activities/arbatch/edit','ar/lists/arinvoice/edit'),
        'entity' => 'invoice',
        'preProcessing' => 'full',
        'recordtype' => 'ri',
        'key' => 'key'
    ),
    'update_apadjustment' => array(
        'function' => 'xmlgw_UpdatePRRecord',
        'permissionkey' => array('ap/activities/apbatch/edit','ap/lists/apadjustment/edit'),
        'entity' => 'apadjustment',
        'preProcessing' => 'full',
        'recordtype' => 'pa',
        'key' => 'key'
    ),
    'update_aradjustment' => array(
        'function' => 'xmlgw_UpdatePRRecord',
        'permissionkey' => array('ar/activities/arbatch/edit','ar/lists/aradjustment/edit'),
        'entity' => 'aradjustment',
        'preProcessing' => 'full',
        'recordtype' => 'ra',
        'key' => 'key'
    ),
    'delete_bill' => array(
        'function' => 'xmlgw_DeletePRRecord',
        'permissionkey' => array('ap/activities/apbatch/delete','ap/lists/apbill/delete'),
        'preProcessing' => 'full',
        'recordtype' => 'pi'
    ),
    'delete_invoice' => array(
        'function' => 'xmlgw_DeletePRRecord',
        'permissionkey' => array('ar/activities/arbatch/delete','ar/lists/arinvoice/delete'),
        'preProcessing' => 'full',
        'recordtype' => 'ri'
    ),
    'delete_apadjustment' => array(
        'function' => 'xmlgw_DeletePRRecord',
        'permissionkey' => array('ap/activities/apbatch/delete','ap/lists/apadjustment/delete'),
        'preProcessing' => 'full',
        'recordtype' => 'pa'
    ),
    'delete_aradjustment' => array(
        'function' => 'xmlgw_DeletePRRecord',
        'permissionkey' => array('ar/activities/arbatch/delete','ar/lists/aradjustment/delete'),
        'preProcessing' => 'full',
        'recordtype' => 'ra'
    ),
    'setcurrent_arpaymentbatch' => array(
        'function' => 'xmlgw_SetCurrentBatch',
        'preProcessing' => 'full',
        'recordtype' => 'rp'
    ),
    'create_arpaymentbatch' => array(
        'function' => 'xmlgw_CreateBatch',
        'permissionkey' => 'ar/activities/arpaymentbatch/create',
        'entity' => 'arpaymentbatch',
        'preProcessing' => 'full',
        'recordtype' => 'rp'
    ),
    'create_arpayment' => array(
        'function' => 'CreateARPayment',
        'permissionkey' => 'ar/activities/applypayments',
        'entity' => 'arpayment',
        'preProcessing' => 'full',
        'recordtype' => 'rp'
    ),
    'create_employee' => array(
        'function' => 'ProcessEmployee',
        'permissionkey' => 'co/lists/employee/create',
        'key' => 'employeeid',
        'entity' => 'employee',
        'preProcessing' => 'partial'
    ),
    'delete_employee' => array(
        'permissionkey' => 'co/lists/employee/delete',
        'key' => 'employeeid',
        'entity' => 'employee'
    ),
    'update_employee' => array(
        'permissionkey' => 'co/lists/employee/edit',
        'key' => 'employeeid',
        'entity' => 'employee'
    ),
    'setcurrent_expensereportbatch' => array(
        'function' => 'xmlgw_SetCurrentBatch',
        'preProcessing' => 'full',
        'recordtype' => 'ei'
    ),
    'create_expensereportbatch' => array(
        'function' => 'xmlgw_CreateBatch', 
        'permissionkey' => 'ee/activities/eebatch/create',
        'entity' => 'eebatch',
        'preProcessing' => 'full',
        'recordtype' => 'ei'
    ),
    'create_expensereport' => array(
        'function' => 'xmlgw_CreateExpenseReport', 
        'permissionkey' => 'ee/activities/myexpenses/create',
        'entity' => 'expensereport',
        'preProcessing' => 'full',
        'recordtype' => 'ei',
        'key' => 'key'
    ),
    'update_expensereport' => array(
        'function' => 'xmlgw_UpdateExpenseReport', 
        'permissionkey' => 'ee/activities/myexpenses/edit',
        'entity' => 'expensereport',
        'preProcessing' => 'full',
        'recordtype' => 'ei',
        'key' => 'key'
    ),
    'get_expensereport' => array(
        'function' => 'xmlgw_GetPRRecord', 
        'permissionkey' => 'ee/activities/myexpenses/view',
        'entity' => 'expensereport',
        'preProcessing' => 'full'
    ),
    'approve_expensereport' => array(
        'function' => 'ApproveExpenseReport', 
        'permissionkey' => 'ee/activities/approve_expenses',
        'entity' => 'expensereport',
        'preProcessing' => 'full'
    ),
    'delete_expensereport' => array(
        'function' => 'xmlgw_DeleteExpenseReport', 
        'permissionkey' => 'ee/activities/myexpenses/delete',
        'preProcessing' => 'full',
        'recordtype' => 'ei'
    ),
    'get_trialbalance' => array(
        'function' => 'GetTrialBalance', 
        'permissionkey' => 'gl/reports/trial_balance',
        'preProcessing' => 'full'
    ),
    'get_accountbalances' => array(
        'function' => 'GetAccountBalances', 
        'permissionkey' => 'gl/reports/account_balances',
        'preProcessing' => 'full'
    ),
    'get_accountgroupdetails' => array(
        'function' => 'GetAccountGroupDetails', 
        'permissionkey' => 'gl/reports/glacctgrp',
        'preProcessing' => 'full'
    ),
    'select_billforpayment' => array(
        'function' => 'SelectBillForPayment', 
        'permissionkey' => 'ap/activities/payments',
        'preProcessing' => 'full'
    ),
    'select_vendorforpayment' => array(
        'function' => 'SelectVendorForPayment', 
        'permissionkey' => 'ap/activities/payments',
        'preProcessing' => 'full'
    ),
    'create_appayment' => array(
        'function' => 'CreateAPPayment', 
        'permissionkey' => 'ap/activities/makepayments',
        'preProcessing' => 'full'
    ),
    'create_contact' => array(
        'permissionkey' => 'co/lists/contact/create',
        'key' => 'contactname',
        'entity' => 'contact'
    ),
    'delete_contact' => array(
        'permissionkey' => 'co/lists/contact/delete',
        'key' => 'contactname',
        'entity' => 'contact'
    ),
    'create_apaccountlabel' => array(
        'preProcessing' => 'partial',
        'function' => 'ProcessAccountLabel',
        'permissionkey' => 'ap/lists/apaccountlabel/create',
        'key' => 'accountlabel',
        'entity' => 'apaccountlabel',
    ),
    'delete_apaccountlabel' => array(
        'permissionkey' => 'ap/lists/apaccountlabel/delete',
        'key' => 'accountlabel',
        'entity' => 'apaccountlabel'
    ),
    'create_araccountlabel' => array(
        'preProcessing' => 'partial',
        'function' => 'ProcessAccountLabel',
        'permissionkey' => 'ar/lists/araccountlabel/create',
        'key' => 'accountlabel',
        'entity' => 'araccountlabel'
    ),
    'delete_araccountlabel' => array(
        'permissionkey' => 'ar/lists/araccountlabel/delete',
        'key' => 'accountlabel',
        'entity' => 'araccountlabel'
    ),
    'create_expensetype' => array(
        'preProcessing' => 'partial',
        'function' => 'ProcessAccountLabel',
        'permissionkey' => 'ee/lists/eeaccountlabel/create',
        'key' => 'expensetype',
        'entity' => 'eeaccountlabel'
    ),
    'delete_expensetype' => array(
        'permissionkey' => 'ee/lists/eeaccountlabel/delete',
        'key' => 'expensetype',
        'entity' => 'eeaccountlabel'
    ),

);
