<?php
//=============================================================================
// 
//      FILE:           htlh_japi.phtml
//      AUTHOR:         rpavlicek
//      DESCRIPTION:    report on the xml gate way status
// 
//      (C)2001, Intacct Corporation, All Rights Reserved
// 
//      Intacct Corporation Proprietary Information.
//      This document contains trade secret data that belongs to Intacct
//      corporation and is protected by the copyright laws. Information herein
//      may not be used, copied or disclosed in whole or part without prior
//      written consent from Intacct Corporation.
//
//=============================================================================

/*
 * Query Parameters:
 *
 * PARAMETER    ; VALUES    ; DESCRIPTION                     ; SAMPLE USAGE
 * url          ; string    ; target url to send xml req      ; url=japi-priv.intacct.com/ia/xml/xmlgw.phtml
 * cuser        ; string    ; company user name               ; cuser=admin
 * cpass        ; string    ; password for company user       ; cpass=123456
 * info         ; 1         ; shows error messages ($info)    ; info=1
 * log          ; 1         ; shows test details   ($details) ; log=1
 * id           ; 1         ; shows server id and PID         ; id=1
 * tmout        ; integer   ; curl timeout seconds            ; tmout=60
 *
 * DEFAULTS:
 * url   : localhost<sandbox_path>/xml/xmlgw.phtml
 * cuser : <determined by convention via createRequestListByConvention() function>
 * cpass : <determined by convention via createRequestListByConvention() function>
 * info  : null
 * log   : null
 * id    : null
 * tmout : 60
 */

require_once 'util.inc';
require_once 'hlth_base.inc';

require_once 'HealthMonitorAPI.cls';
// Do not include newrelic injects script code 
if (extension_loaded('newrelic')) {
  newrelic_disable_autorum();
}

//header('Content-Type: text/plain');

// Defaults
$timeout = 60;
$cuser = null;
$cpass = null;
$url = "localhost" . RootPath() . "/xml/xmlgw.phtml";

// Gather input parameters (if not default)
if ( isset($_GET['tmout']) && intval($_GET['tmout']) > 0 ) {
    $timeout = intval($_GET['tmout']);
}
if ( isset($_GET['url']) && !empty($_GET['url']) ) {
    $url = $_GET['url'];
}
if ( isset($_GET['cuser']) && !empty($_GET['cuser']) ) {
    $cuser = $_GET['cuser'];
}
if ( isset($_GET['cpass']) && !empty($_GET['cpass']) ) {
    $cpass = $_GET['cpass'];
}

$result = [];

//run($url, $timeout, $cuser, $cpass);die();

if (file_exists(IA_MAINTENANCE_FILE) && filesize(IA_MAINTENANCE_FILE) > 0) {
    $result['FINAL']['statusMsg'] = 'maintenance';
    $result['FINAL']['status'] = HealthMonitorAPI::STATUS_OK;
} else {
    $result = runOneSchemaPerDB($url, $timeout, $cuser, $cpass);
}

showResults($result);


/**
 * @param string  $url
 * @param int     $timeout
 * @param string  $cuser
 * @param string  $cpass
 *
 * @return array
 */
function runOneSchemaPerDB($url, $timeout, $cuser, $cpass)
{
    $hm = new HealthMonitorAPI($url, $timeout, $cuser, $cpass);
    $requestList = $hm->createRequestListFromCFG();

//    var_dump($requestList); echo "\n---\n\n";
//    $requestList = $hm->createRequestListByConvention();
//    var_dump($requestList); echo "\n---\n\n";

    $result = $hm->runAllRequests($requestList);

    return $result;

}


/**
 * @param string  $url
 * @param int     $timeout
 * @param string  $cuser
 * @param string  $cpass
 *
 * @return array
 */
function runAllSchemas($url, $timeout, $cuser, $cpass)
{
    $hm = new HealthMonitorAPI($url, $timeout, $cuser, $cpass);
    $requestList = $hm->createRequestListByConvention();
    $requestList[] =
        [ 'dev02' =>
            [
                'dbID' => 199,
                'coID' => 'TA_0707',
                'user' => 'Aman',
                'pass' => '123456',
            ]
        ];
    var_dump($requestList); echo "\n---\n\n";

    $result = $hm->runAllRequests($requestList);

    return $result;
}


/**
 * @param array[][] $result
 */
function showResults($result)
{
?>
<html>
<head>
    <title>XML Gateway multi-exec health check - <? echo $_SERVER['SERVER_NAME']; ?></title>
</head>
<body>
<pre>
<?
    echo $_SERVER['SERVER_NAME'] . "\n" . date('l j M, Y H:i:s') . "\n";

    $status = $result['FINAL']['statusMsg'];
    $statusCode = $result['FINAL']['status'];
    $tm_elapsed = 0;

    if ( $statusCode !== HealthMonitorAPI::STATUS_OK ) {
        $status = "ERROR - $status";
    }

    echo "status=$status" . "\n";

    $info = [];
    $details = [];
    $finalDetails = [];

    foreach ( $result as $batchID => $batch ) {

        if ( 'FINAL' === $batchID && isset($batch['elapsed']) ) {
            $finalDetails[] = "===== TOTAL elapsed: " . $batch['elapsed'] . " =====";
        } else {
            $batchDetails = [];
            foreach ( $batch as $dbID => $data ) {
                if ( 'FINAL' === $dbID ) {
                    $details[] = sprintf("===== B%d =====", $batchID);
                        //"Batch $batchID: " . $data['elapsed'];
                } else {
                    // Show information for each failed request
                    if ( $data['status'] !== HealthMonitorAPI::STATUS_OK ) {
                        $info[] = $data['statusMsg'];
                    }


                    $batchDetails[] = sprintf("   %7s %7s: %20s (%12s) %s",
                        $dbID,
                        '('.$data['server'].')',
                        $data['coID'],
                        HealthMonitorAPI::getStatusName($data['status']),
                        $data['elapsed']
                    );
                }
            }
            $details = array_merge($details, $batchDetails);
        }
    }

    $details = array_merge($details, $finalDetails);


// info is statusMsg for each line item where status is not ok
    if ($_GET['info']) {
        if (count($info) > 0) {
            foreach($info as $i) {
                echo "info: " . $i . "\n";
            }
        }
    }

// log is DBID, server, company, status code, timing
    if ($_GET['log']) {
        echo "--\n";
        //sort($details);
        foreach($details as $dtl) {
            echo "log: $dtl\n";
        }
    }

    if ($_GET['id']) {
        echo substr($_SERVER['SERVER_ADDR'], -2) . "." . getmypid();
    }

    $log_str = sprintf("%0.3f %s [%s]\n", $tm_elapsed, $status, join(", ", $info));
    logHealthCheckStatus($log_str);

?>
</pre>
</body>
</html>
<?
}



