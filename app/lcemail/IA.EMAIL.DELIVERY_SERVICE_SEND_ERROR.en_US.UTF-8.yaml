subject:
    text: "Unsuccessful file delivery"
body:
    text: |+
        Hi ${USER_NAME},

        Our attempt to deliver the file ${DELIVERY_LAST_FILENAME} to the ${DELIVERY_NAME} cloud storage service of type ${DELIVERY_TYPE}
        was unsuccessful due to one or more conditions:

        Upload failed.

    dsUnknownError: |+
        This upload failed to complete due to an unknown error. [Support ID: ${SUPPORT_ID}]
        Check that your configuration is right (sign-in credentials, URL or path, and connection status), then try again.

    dsAuthError: |+
        You are either signed out of your storage target, or your sign-in credentials weren't recognized. [Support ID: ${SUPPORT_ID}]
        Check your credentials, then sign in again.

    dsDriveFullError: |+
        This upload failed to complete because your target storage is maxed out. [Support ID: ${SUPPORT_ID}]
        Increase your storage, then try again.

    dsInvalifPathError: |+
        This upload failed to complete because the path is invalid. [Support ID: ${SUPPORT_ID}]
        Check the path, then try again.

    dsFailedConnectionError: |+
        This upload failed to complete due to connection issues. [Support ID: ${SUPPORT_ID}]
        Wait a few moments, then try again.

    dsInactiveError: |+
        The cloud storage service chosen to deliver this file is inactive in the system. [Support ID: ${SUPPORT_ID}]
        If you are the owner or admin of the service, reactivate it and try again.

    dsDataSizeError: |+
        This upload failed to complete because the file size is too big. [Support ID: ${SUPPORT_ID}]
        Choose to Split files after 15000 records, then try again.

    dsDefaulteError: |+
        ${DELIVERY_DEFAULT_ERROR}  [Support ID: ${SUPPORT_ID}]

    signatureAndServer: |+
        Regards,
        Intacct Support Team (${SERVER_NAME})

    isNotLiveTimerReport: |+
        ${TIMER_REPORT}
