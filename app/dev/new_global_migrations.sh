#!/bin/bash
# Usage: new_global_migrations.sh -h
# Purpose:
#        Use this script to apply migrations to global schemas following the new dev process & tools
#

. /usr/local/intacct/etc/ia_servers

# set this to 0 for DBA mode and 1 for DEV mode
DEV_MODE=0

if [[ $DEV_MODE -eq 1 ]]; then
    # for dev/testing
    MIGRBASEDIR="$HOME/migrations"
else
    # for DBAs
    MIGRBASEDIR="/u02/db_migrations"
fi

LOGDIR="$MIGRBASEDIR/logs"
MIGDIR="$MIGRBASEDIR/migrations"
EXTRAMIGRLIST="/tmp/miglist"
FILETAG="no_file"

# for dev/testing
if [[ $DEV_MODE -eq 1 ]]; then
    # overwrite the location of miglit file for dev
    EXTRAMIGRLIST="$MIGRBASEDIR/miglist"
fi

script_name=$(basename $0)
SCRIPTDIR=`dirname $0`
SCRIPTDIR=`readlink -f $SCRIPTDIR`

if [[ -f "/u02/home/<USER>/opt/flyway/flyway" ]]
  then
    FLYWAYDIR="/u02/home/<USER>/opt/flyway"
  else
    FLYWAYDIR="/opt/flyway-ia/flyway"
fi

# CASE 1: run migrations on PODs excluding app replicated migrations:
#        ./new_global_migrations.sh -d /migration_directory_path -u mega_global -F -l IA_MIG_PROD
#
# CASE 2: run migrations on PODs including app replicated migrations:
#        ./new_global_migrations.sh -d /migration_directory_path -u mega_global -F -l IA_MIG_PROD -P

usage="Usage:
	$script_name -T tag -d MIGDIR -u USERNAME -F -l LIST [-P] [-V] [-R] [-I] [-t] [-a] [-A]
	$script_name -h
	 -T tag         : Tag/Label to use on the migration logs to easier identify the logs for specific migrations
	 -d MIGDIR      : Migration directory containing the flyway compatible files that needs to be applied
	 -u USERNAME    : DB user which will run the migration, e.g. mega_global
	 -t target      : The target version up to which Flyway should consider migrations, e.g. Aug22. Special values: current, latest
	 -a             : To include also archived migrations in flyway phase
	 -A             : To include only archived migrations in flyway phase
	 -F             : For running migrate command in flyway, excepting the app replicated migrations
	 -P             : To include app replicated migrations in flyway phase
	 -V             : For running validate command in flyway, cannot be used with -F, -R, -I
	 -R             : For running repair command in flyway, cannot be used with -F, -V, -I
	 -I             : For running info command in flyway, cannot be used with -F, -V, -R
	 -H             : For automation - updating automatically flyway schema history for app replicated migration; this will not apply the migrations, it will only add them in flyway history
	 -l LIST        : List containing schema information.LIST=LIST={IA_MIG_PROD|IA_MIG_DR|IA_MIG_SPECIAL_PROD|IA_MIG_SPECIAL_DR|IA_MIG_DEBUG|IA_MIG_EXTRA|IA_MIG_POD[0-9]{1,}|IA_MIG_DR_POD[0-9]{1,}|IA_MIG_SPECIAL_POD[0-9]{1,}|IA_MIG_SPECIAL_DR_POD[0-9]{1,}}
	 -h             : Print the usage message

Notes: Make sure "logs" & "migrations" directories exist under $MIGRBASEDIR. Copy the migration file under $MIGDIR. Log files for each schema will be created under $LOGDIR. If you use IA_MIG_EXTRA make sure to have $EXTRAMIGRLIST file with the schemas.
"

#
# Check if flyway finished for the schema and print a summary on the screen.
#
check_flyway_result() {

  local schema=${LIST%%:*}
  schema=${schema^^}
  local server=${LIST##*:}

  # check if log file was already created by flyway
  if [[ -e $LOGDIR/$server.$schema.$FILETAG.$TAG.log ]]
  then
    # read content of the file and check if flyway finished
    if [[ "UPDATE_HISTORY_OPTION" == "Y" ]]
    then
      local successMessage=`cat $LOGDIR/$server.$schema.update_history.$TAG.log | grep 'Successfully applied\|Schema .* is up to date. No migration necessary.'`
    else
      local successMessage=`cat $LOGDIR/$server.$schema.$FILETAG.$TAG.log | grep 'Successfully applied\|Schema .* is up to date. No migration necessary.'`
    fi
    #local successMessage=`cat $LOGDIR/$server.$schema.$FILETAG.$TAG.log | grep 'Successfully applied\|Schema .* is up to date. No migration necessary.'`
    if [[ ! -z $successMessage ]]
    then
      # flyway finished with success for this schema
      echo '####################################################'
      echo 'Migration process completed with SUCCESS'
      echo '####################################################'
    else
      # check if flyway finished with error
      if [[ "$UPDATE_HISTORY_OPTION" == "Y" ]]
      then
	      local errorMessage=`cat $LOGDIR/$server.$schema.update_history.$TAG.log | grep 'ERROR'`
      else
        local errorMessage=`cat $LOGDIR/$server.$schema.$FILETAG.$TAG.log | grep 'ERROR'`
      fi
      if [[ ! -z $errorMessage ]]
      then
        # flyway finished with errors for this schema
        echo '####################################################'
        echo 'Migration process completed with ERROR'
        echo '####################################################'
      fi
    fi
  fi
}

#
# Validate if target has the expected format, e.g. 2208.9
#
validate_target() {
  if [[ ! $targetInfo =~ ^[0-9]{4}.9$ && ! $targetInfo == "current"  && ! $targetInfo == "latest" ]]
  then
    validTargetFormat="Valid target value is formed from:
        - release    : one of the values {Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec}
        - year       : last two digits from the year
        e.g. Aug22"
    echo "Error: Target $TARGET is incorrect."; echo "$validTargetFormat";
    exit 0;
  fi
}

#
# Returns target in the form accepted by flyway
#
get_target() {
  # convert target to the for accepted by flyway
  # if target =current|latest we leave the value as it is and inform the user about the option
  if [[ "$TARGET" == "current" ]]
  then
    targetInfo="$TARGET"
    echo "Target = $TARGET: no versioned migrations will be applied but repeatable migrations will be, together with any callbacks"
  elif [[ "$TARGET" == "latest" ]]
  then
    targetInfo="$TARGET"
    echo "Target = $TARGET: applying migrations up to the latest version of the schema, as defined by the migration with the highest
    version"
  else
    # declare months to be used in release validation
    declare -A months=([Jan]="01" [Feb]="02" [Mar]="03" [Apr]="04" [May]="05" [Jun]="06" [Jul]="07" [Aug]="08" [Sep]="09" [Oct]="10" [Nov]="11" [Dec]="12")

    year=`echo "$TARGET" | cut -c4-5`
    monthInfo=`echo "$TARGET" | cut -c1-3`
    ocr=".9"
    targetInfo=$year${months[$monthInfo]}$ocr
  fi
}

#
# Check if TNS_ADMIN variable is set
#
check_tns() {
  tns_var=`printenv TNS_ADMIN`

  if [[ -z "${tns_var+x}" ]]
  then
    if [[ -f "/etc/tnsnames.ora" ]]
    then
      export TNS_ADMIN='/etc'
    else
      echo "Error: TNS_ADMIN environment variable is not set. Please set the variable value to /etc"; exit 0
    fi
  fi
}

#
# Loop over the migration files of a folder and remove their content
#
emptyFiles() {
  for file in "$1"/*
  do
    if [ -d "$file" ]
    then
      emptyFiles "$file"
    elif [ -f "$file" ]
    then
      echo "" > "$file"
    fi
  done
}

#
# Copy folder db_migration_app_replicated/db_global_migration_archive in a temporary folder and remove original files content
#
moveAppRepMigInTemp() {
  migPath="$1"

  # create temp folder
  newMigPath="$2"

  # if folder already exists, remove it
  if [[ -d "$newMigPath" ]]
  then
    rm -rf "$newMigPath"
  fi
  mkdir "$newMigPath"
  chmod 777 "$newMigPath"

  # copy initial migrations in temp folder
  cp -r "$migPath/." "$newMigPath/"

  # remove content of the migration files
  emptyFiles "$migPath"

}

#
# Copy the content of the migrations back in the original files
#
moveAppRepMigInOriginalFile() {
  tempMigPath="$1"
  migPath="$2"

  # copy the content of the migrations back in the original files
  cp -a "$tempMigPath/." "$migPath/"

  # remove temp folder
  rm -rf "$tempMigPath"
}

run_flyway_migrations() {
    local curDir=$PWD
    cd $INPUT_DIR

    if [[ $DEV_MODE -eq 1 ]]; then
      # for dev/testing
      cd ../

      local configDir="db_global_migration/global_conf"
    else
      # for DBAs

      # redirect in order to be able to access flyway jar
      cd ../

      local configDir="db_global_migration/global_conf"
    fi

    local configFiles="$configDir/flyway.conf"
    # check if we need to include archived migrations
    if [[ "$INCLUDE_ARCHIVE" == 1 ]]
      then
        # include archived migrations
        configFiles="$configFiles,$configDir/flyway.archive.conf"
    fi
    # check if we need to include only archived migrations
    if [[ "$INCLUDE_ARCHIVE_ONLY" == 1 ]]
      then
        # include archived migrations
        configFiles="$configDir/flyway.archive.only.conf"
    fi
    if [[ "$INCLUDE_APP_REPLICATED" == "Y" ]]
    then
      if [[ "$FLYWAY_CMD" == "migrate" ]]
      then
        # extra check in order to validate that the user really wanted to apply app replicated migrations

        # get the details of the schema on which the script is running
        runningOnSchema=`echo $LIST | cut -d ":" -f1`
        runningOnServer=`echo $LIST | cut -d ":" -f2`

        # read config file and validate schemas
        credentialsForAppMigSchema=`cat $configDir/appMigSchemaDetails.conf | grep 'schemaDetails'`
        schemaDetails=`echo $credentialsForAppMigSchema | cut -d "=" -f2`

        ok=false

        for entry in $schemaDetails
        do
          appReplicatedSchema=`echo $entry | cut -d ":" -f1`
          appReplicatedServer=`echo $entry | cut -d ":" -f2`
          # schema is one of the schemas on which app replicated migrations are applied
          if [[ "$runningOnSchema" == "$appReplicatedSchema" && "$runningOnServer" == "$appReplicatedServer" ]]
          then
            ok=true
            break
          fi
        done

        # if schema on which the script is running is the one from the config file then add app replicated migrations
        if [[ "$ok" == true ]]
        then
          configFiles="$configFiles,$configDir/flyway.pod.conf"

          # check if we need to include archived migrations
          if [[ "$INCLUDE_ARCHIVE" == 1 ]]
          then
            # include archived migrations
            configFiles="$configFiles,$configDir/flyway.pod.archive.conf"
          fi

          # check if we need to include archived migrations only
          if [[ "$INCLUDE_ARCHIVE_ONLY" == 1 ]]
          then
            # include archived migrations
            configFiles="$configDir/flyway.pod.archive.only.conf"
          fi

        else
          # else check if the user really wanted to apply app replicated migrations
          read -p "Are you sure you want to apply app replicated migrations?(Y/N): " OPTION

          if [[ "$OPTION" == "Y" || "$OPTION" == "y" ]]
          then
            # apply app replicated migrations
            configFiles="$configFiles,$configDir/flyway.pod.conf"
            echo "Applying app replicated migrations too"

            # check if we need to include archived migrations
            if [[ "$INCLUDE_ARCHIVE" == 1 ]]
            then
              # include archived migrations
              configFiles="$configFiles,$configDir/flyway.pod.archive.conf"
            fi

            # check if we need to include archived migrations only
            if [[ "$INCLUDE_ARCHIVE_ONLY" == 1 ]]
            then
              # include archived migrations
              configFiles="$configDir/flyway.pod.archive.only.conf"
            fi

          else
            echo "Skipping app replicated migrations"
            INCLUDE_APP_REPLICATED="N"
          fi
        fi
      else
        # apply app replicated migrations
        configFiles="$configFiles,$configDir/flyway.pod.conf"

        # check if we need to include archived migrations
        if [[ "$INCLUDE_ARCHIVE" == 1 ]]
        then
          # include archived migrations
          configFiles="$configFiles,$configDir/flyway.pod.archive.conf"
        fi

        # check if we need to include archived migrations only
        if [[ "$INCLUDE_ARCHIVE_ONLY" == 1 ]]
        then
          # include archived migrations
          configFiles="$configDir/flyway.pod.archive.only.conf"
        fi

      fi
    fi

    JAVA_ARGS="-Doracle.net.tns_admin=/etc"

    echo "Starting Flyway migration ($FLYWAY_CMD) on following schema at $NOW"
    #local javaCMD="$FLYWAYDIR/jre/bin/java"
    #local javaCMD=java
    # get java executable path
    javaConfigFile="$configDir/javaConfig.conf"
    javaPathConfig=`grep -n "javaPath" $javaConfigFile`
    pathConfigStartPosition=$(expr index "$javaPathConfig" "=")
    javaCMD=${javaPathConfig:$pathConfigStartPosition}
    #local javaCMD=/usr/lib/jvm/java-17-openjdk-17.0.1.0.12-2.el8_5.x86_64/bin/java
    local CP=""
    CP="./intacct.flyway-parallel.jar"
    CP="$CP:$FLYWAYDIR/lib/*:$FLYWAYDIR/lib/community/*:$FLYWAYDIR/drivers/*"
    local params="flyway.baselineOnMigrate=true\nflyway.user=${USERNAME}\nflyway.password=${PASSWORD}"

    # check if target is specified
    if [[ -z "$TARGET" ]]
    then
      # target is not specified; running flyway without target
      # run flyway in background
      (echo -e $params | $javaCMD $JAVA_ARGS -cp "$CP" com.intacct.db.Main -configFiles_ddl=$configFiles -logDir=$LOGDIR -logTS=$TAG -logFN="no_file" "-list=$LIST" $FLYWAY_CMD) &
    else
      # running flyway with target

      # get target in the format accepted by flyway
      # default: target=latest - applying migrations up to the latest version of the schema, as defined by the migration with the highest
      # version
      targetInfo='latest'
      get_target

      # validate target
      validate_target

      # run flyway in background
      (echo -e $params | $javaCMD $JAVA_ARGS -cp "$CP" com.intacct.db.Main -configFiles_ddl=$configFiles -logDir=$LOGDIR -logTS=$TAG -logFN="no_file" "-list=$LIST" -target="$targetInfo" $FLYWAY_CMD) &
    fi

    # redirect back in INPUT_DIR
    cd $INPUT_DIR

    local flywayPid=$!

    # check flyway result for the schema
    while [[ `ps -eo pid | grep $flywayPid` -eq $flywayPid ]]
    do
      check_flyway_result

      sleep 30
    done
    # check once again to be sure we handled all cases
    check_flyway_result

    # Start the update flyway_schema_history table for app_replicated migrations

    # Keep in mind that this mechanism will produce an error if we update the history table first and after that we try
    # to reapply the migrations which where only written in the history table

    if [[ "$FLYWAY_CMD" == "migrate" && "$INCLUDE_ARCHIVE" == 0 && ("$INCLUDE_APP_REPLICATED" == "N" || "$INCLUDE_APP_REPLICATED" == "n") ]]
    then
        echo "Start the update of flyway_schema_history for the replication process."
        echo "The following steps are requested only on pods where we want to update flyway_schema_history for the replication process. These steps don't apply migrations, they only updates flyway_schema_history."
        echo '####################################################'

        if [[ "$UPDATE_FLYWAY_HISTORY_AUTOMATICALLY" == 1 ]]
        then
          UPDATE_FLYWAY_HISTORY_OPTION="Y"
        else
          read -p "Do you want update flyway_schema_history table?(Y/N): " UPDATE_FLYWAY_HISTORY_OPTION
        fi

        if [[ "$UPDATE_FLYWAY_HISTORY_OPTION" == "Y" || "$UPDATE_FLYWAY_HISTORY_OPTION" == "y" ]]
        then

          # copy original migration files in a temporary directory
          cd ../..
          customPath="$runningOnSchema"
          customPath+="_"
          customPath+="$runningOnServer"
          tmpFolderForFlywayUpdate="/tmp/db_global_migration_$customPath"

          # if the temporary directory already exists remove it
          if [[ -d "$tmpFolderForFlywayUpdate" ]]
          then
            rm -rf "tmpFolderForFlywayUpdate"
          fi

          cp -R "db/" "$tmpFolderForFlywayUpdate"
          chmod 777 -R "$tmpFolderForFlywayUpdate"

          # check if the tempoarry folder was create; if it was not created print an error and don't update flyway schema history
          if [[ -d $tmpFolderForFlywayUpdate ]]
          then
          # we can run the update of flyway_schema_history table

          # redirect the hole execution in the temporary file
          cd "$tmpFolderForFlywayUpdate"

          # remove unnecessary folders releated to schemalets migrations from the temporary folder
          rm -rf "db_migration"
          rm -rf "db_migration_archive"
          rm -rf "db_migration_post"
          rm -rf "db_migration_post_archive"
          rm -rf "db"

          # check if flyway_schema_history needs to be updated for app_replicated migrations in order to be able to use
          # flyway version for replication
          configFiles=""
          APP_REPLICATED_OPTION="N"
          if [[ "$INCLUDE_APP_REPLICATED" == "N" || "$INCLUDE_APP_REPLICATED" == "n" ]]
          then

            # automation parameter or user input is needed for the pod where we apply app replicated migrations with flyway
            # for this pod, in case someone applies only pod migrations, he should not update flyway_schema_history for
            # app replicated migrations as this would lead to miss app replicated migrations for which history is updated but they are not actually applied
            if [[ "$UPDATE_FLYWAY_HISTORY_AUTOMATICALLY" == 1 ]]
            then
              APP_REPLICATED_OPTION="Y"
            else
              read -p "Do you want to update history for app_replicated migrations?(Y/N): " APP_REPLICATED_OPTION
            fi

            if [[ "$APP_REPLICATED_OPTION" == "Y" || "$APP_REPLICATED_OPTION" == "y" ]]
            then
	            UPDATE_HISTORY_OPTION="Y"
              # update configuration files list
              configFiles="$configDir/flyway.pod.only.conf"

              # move the content of the migration files in the temporary directory
              migPath="db_global_migration/db_migration_app_replicated"
              newMigPath="db_migration_app_replicated_temp"
              moveAppRepMigInTemp "$migPath" "$newMigPath"
            fi
          fi

          # do the same check for app replicated migrations that are archived
          ARCHIVED_APP_REPLICATED_OPTION="N"
          #if [[ "$INCLUDE_ARCHIVE" == 0 && "$INCLUDE_ARCHIVE_ONLY" == 0 ]]
          #then
          #  read -p "Do you want to update history for archived app_replicated migrations?(Y/N): " ARCHIVED_APP_REPLICATED_OPTION
          #  if [[ "$ARCHIVED_APP_REPLICATED_OPTION" == "Y" || "$ARCHIVED_APP_REPLICATED_OPTION" == "y" ]]
          #  then
          #    UPDATE_HISTORY_OPTION="Y"
	        #    # update configuration files list
          #    if [[ "$APP_REPLICATED_OPTION" == "Y" || "$APP_REPLICATED_OPTION" == "y" ]]
          #    then
          #      # we include all app replicated migrations (from db_migration_app_replicated and db_global_migration_archive)
          #      configFiles="$configFiles,$configDir/flyway.pod.and.archive.only.conf"
          #    else
          #      # we include only the archived migrations
          #      configFiles="$configDir/flyway.pod.only.archive.conf"
          #    fi

          #    # move the content of the migration files in the temporary directory
          #    migPath="db_global_migration_archive"
          #    newMigPath="db_global_migration_archive_temp"
          #    moveAppRepMigInTemp "$migPath" "$newMigPath"
          #  fi
          #fi

          if [[ "$configFiles" != "" ]]
          then
            # apply updates on flyway_schema_history
            echo '####################################################'
            echo "Starting Flyway update of flyway_schema_history on following schema"

            # we use a standard logFN for the log file to make the difference between migrations that are applied by flyway and the update of flyway_schema_history for app replicated migrations
            logFNTag="update_history"

            # we set flyway.validateOnMigrate=false so that we avoid validation of already applied migrations which would raise checksum mismatch errors as we empty the files
            params="$params\nflyway.validateOnMigrate=false"

            # check if target is specified
            if [[ -z "$TARGET" ]]
            then
                # target is not specified; running flyway without target
                # run flyway in background
                (echo -e $params | $javaCMD $JAVA_ARGS -cp "$CP" com.intacct.db.Main -configFiles_ddl=$configFiles -logDir=$LOGDIR -logTS=$TAG -logFN="$logFNTag" "-list=$LIST" $FLYWAY_CMD) &
            else
                # running flyway with target

                # get target in the format accepted by flyway
                # default: target=latest - applying migrations up to the latest version of the schema, as defined by the
                # migration with the highest version
                targetInfo='latest'
                get_target

                # validate target
                validate_target

                # run flyway in background
                (echo -e $params | $javaCMD $JAVA_ARGS -cp "$CP" com.intacct.db.Main -configFiles_ddl=$configFiles -logDir=$LOGDIR -logTS=$TAG -logFN="$logFNTag" "-list=$LIST" -target="$targetInfo" $FLYWAY_CMD) &
            fi

            # check if flyway finished the update of flyway_schema_history table
            local flywayPid=$!

            # check flyway result for the schema
            while [[ `ps -eo pid | grep $flywayPid` -eq $flywayPid ]]
            do
              check_flyway_result

              sleep 30
            done
            # check once again to be sure we handled all cases
            check_flyway_result
          fi

          # move back the migrations in their original files
          if [[ "$APP_REPLICATED_OPTION" == "Y" || "$APP_REPLICATED_OPTION" == "y" ]]
          then
            # we moved app replicated migrations in a temporary files
            # we move them back in their original files
            migPath="db_global_migration/db_migration_app_replicated"
            newMigPath="db_migration_app_replicated_temp"
            moveAppRepMigInOriginalFile "$newMigPath" "$migPath"
          fi
          if [[ "$ARCHIVED_APP_REPLICATED_OPTION" == "Y" || "$ARCHIVED_APP_REPLICATED_OPTION" == "y" ]]
          then
            # we moved app replicated archived migrations in a temporary files
            # we move them back in their original files
            migPath="db_global_migration_archive"
            newMigPath="db_global_migration_archive_temp"
            moveAppRepMigInOriginalFile "$newMigPath" "$migPath"
          fi

          # remove the temporary folder
          cd ..
          tmpDirectoryForUpdateHistory="db_global_migration_$customPath"
          rm -rf "$tmpDirectoryForUpdateHistory"

          echo "Update of flyway_schema_history table finished."
          else
          echo "Error: There was an error in creating temporary files necessary for flyway_schema_history update. Try again."
        fi
        else
          echo "Skip the update of flyway_schema_history for the replication process."
        fi
    fi
}

if [[ $# -lt 5 ]]
	then
		if [[ $@ == "-h" ]]
			then
				echo "$usage" ; exit 0
		else
				echo "Error: invalid number of arguments specified: $#" ; echo "$usage" ; exit 0
		fi
fi

EXCLUSIVE_COUNT=0
EXCLUSIVE_LIST=""
INCLUDE_ARCHIVE=0
INCLUDE_ARCHIVE_ONLY=0
INCLUDE_APP_REPLICATED="N"
UPDATE_HISTORY_OPTION="N"

while getopts ":d:u:T:t:aAFPVRMIHl:" opt ; do
        case $opt in
        T)  MIGR_TAG=$OPTARG ;;
        d)  INPUT_DIR=$OPTARG ;;
        u)  USERNAME=$OPTARG ;;
        t)  TARGET=$OPTARG ;;
        a)  INCLUDE_ARCHIVE=1;;
        A)  INCLUDE_ARCHIVE_ONLY=1;;
        F)  FLYWAY_CMD=migrate EXCLUSIVE_COUNT=$((EXCLUSIVE_COUNT+1)) EXCLUSIVE_LIST="$EXCLUSIVE_LIST -$opt";;
        P)  INCLUDE_APP_REPLICATED=Y ;;
        V)  FLYWAY_CMD=validate EXCLUSIVE_COUNT=$((EXCLUSIVE_COUNT+1)) EXCLUSIVE_LIST="$EXCLUSIVE_LIST -$opt";;
        R)  FLYWAY_CMD=repair EXCLUSIVE_COUNT=$((EXCLUSIVE_COUNT+1)) EXCLUSIVE_LIST="$EXCLUSIVE_LIST -$opt";;
        I)  FLYWAY_CMD=info EXCLUSIVE_COUNT=$((EXCLUSIVE_COUNT+1)) EXCLUSIVE_LIST="$EXCLUSIVE_LIST -$opt";;
        H)  UPDATE_FLYWAY_HISTORY_AUTOMATICALLY=1;;
        l)  LIST=$OPTARG ;;
        h)  echo "$usage"; exit 0 ;;
        ?)  echo "Error: invalid arguments specified"; echo "$usage" ; exit 0 ;;
        esac
done

if [[ -z "$MIGR_TAG" ]]
then
  echo "Error: A migration tag/label must be specified using the -T parameter" ; echo "$usage" ; exit 0
fi

if [[ $EXCLUSIVE_COUNT -gt 1 ]]
then
  echo "Error: Mutually exclusive options cannot be specified together:$EXCLUSIVE_LIST" ; echo "$usage" ; exit 0
fi

if [[ -z "$INPUT_DIR" ]]
then
		echo "Error: A migration Flyway Directory should be specified with -d option" ; echo "$usage" ; exit 0
fi

if [[ ! -z "$INPUT_DIR" && ! -d $INPUT_DIR ]]
	then
		echo "Error: Flyway migration directory $INPUT_DIR does not exist"; echo "$usage" ; exit 0
fi

if [[ ! -d "$INPUT_DIR/db_migration_app_replicated" ]]
	then
		echo "Error: Flyway migration directory "$INPUT_DIR/db_migration_app_replicated" does not exist"; echo "$usage" ; exit 0
fi

if [[ ! -d "$INPUT_DIR/db_migration_on_pods" ]]
	then
		echo "Error: Flyway migration directory "$INPUT_DIR/db_migration_on_pods" does not exist"; echo "$usage" ; exit 0
fi

if [[ ! -d $FLYWAYDIR ]]
	then
		echo "Error: Flyway runtime directory $FLYWAYDIR does not exist"; echo "$usage" ; exit 0
fi

if [[ ! -d $MIGDIR ]]
	then
		echo "Error: Migrations directory $MIGDIR does not exist"; echo "$usage" ; exit 0
fi

if [[ ! -d $LOGDIR ]]
	then
		echo "Error: Log directory $LOGDIR does not exist"; echo "$usage" ; exit 0
fi

if [[ ! -w $LOGDIR ]]
	then
		echo "Error: User cannot write to $LOGDIR. Change permisssions."; echo "$usage" ; exit 0
fi

if [[ -z $USERNAME ]]
	then
		echo "Error: DB user which will run the migration should be specified with -u option" ; echo "$usage" ; exit 0
fi

if [[ -z $LIST ]]
	then
		echo "Error: Specify the schema list with -l option"; echo "$usage" ; exit 0
fi

if [[ $LIST = 'IA_MIG_EXTRA' && ! -r $EXTRAMIGRLIST ]]
	then
		echo "Error: $EXTRAMIGRLIST with extra schemas does not exist"; echo "$usage" ; exit 0
fi

if [[ "$FLYWAY_CMD" != "migrate" && "$FLYWAY_CMD" != "validate" && "$FLYWAY_CMD" != "repair" && "$FLYWAY_CMD" != "info" ]]
then
  commands="Valid migration commands:
	 -F           : For running migrate command in flyway, excepting the app replicated migrations
	 -P           : To include app replicated migrations in flyway phase
	 -V           : For running validate command in flyway, cannot be used with -F, -R, -I
	 -R           : For running repair command in flyway, cannot be used with -F, -V, -I
	 -I           : For running info command in flyway, cannot be used with -F, -V, -R"
  echo "Error: Invalid migration command. Use one of the valid migration commands. ";  echo "$commands"; exit 0
fi

if [[ $LIST = 'IA_MIG_EXTRA' && -r $EXTRAMIGRLIST ]]
	then
		. $EXTRAMIGRLIST
fi

case $LIST in
    IA_MIG_PROD) LIST=$IA_MIG_PROD ;;
    IA_MIG_DR)	LIST=$IA_MIG_DR ;;
    IA_MIG_SPECIAL_PROD) LIST=$IA_MIG_SPECIAL_PROD ;;
    IA_MIG_SPECIAL_DR) LIST=$IA_MIG_SPECIAL_DR ;;
    IA_MIG_DEBUG) LIST=$IA_MIG_DEBUG ;;
    IA_MIG_EXTRA) LIST=$IA_MIG_EXTRA ;;
    IA_MIG_POD[0123456789]*) listName=$LIST LIST=${!listName} ;;
    IA_MIG_SPECIAL_POD[0123456789]*) listName=$LIST LIST=${!listName} ;;
    IA_MIG_DR_POD[0123456789]*) listName=$LIST LIST=${!listName} ;;
    IA_MIG_SPECIAL_DR_POD[0123456789]*) listName=$LIST LIST=${!listName} ;;
    *) echo "Error: invalid schema specified"; echo "$usage" ; exit 0 ;;
esac

if [[ $LIST = *" "* ]]; then
  # migrations can be applied only on one schema at a time
  echo "Error: Migration on multiple schemas is disabled for global schemas. Please apply migrations one schema at a time."; exit 0
fi


echo Enter database user password for $USERNAME
read -s PASSWORD
if [[ -z $PASSWORD ]]
   then
         echo "Error: Password can't be null"; exit 0
fi

check_tns

NOW=$(date +%Y-%m-%d.%H:%M:%S)
TAG="$NOW.$MIGR_TAG"

if [[ ! -z "$INPUT_DIR" ]]
  then
    run_flyway_migrations
fi

exit

