#!/bin/bash

CONFIGFILE=""
FLYWAYPWD="badsin"
SKIPMIGRATION=0
SKIPMVIEWS=0
SKIPMVIEWSREGEN=0
SKIPPOST=0
SKIPREPAIRACTIONS=0
SKIPDBA=0
INFO=0
REPAIR=0
VALIDATE=0
VALIDATEREPAIRACTIONS=0
BASELINE=0
ARCHIVE=0
ARCHIVE_ONLY=0
FLYWAYUSER=""
FLYWAYURL=""
STDINPUT=""
FLYWAYOUTPUT=""
DDLMIGAPPLYSUCCESSFULLY=1
DDLONLY=0
DMLONLY=0
RESTORE_HISTORY=0

exit_function() {
  local exitCode=$1
  local text=$2
  if [ -z "$exitCode" ]; then
    exitCode=0
  fi
  if [ ! -z "$text" ]; then
    echo "$text"
  fi

  if [ ! -z "$CURRENTDIRECTORY" ]; then
    cd $CURRENTDIRECTORY
  fi

  exit $exitCode
}

help_function () {
  echo -e "Usage $0 [options] \n" \
       "  The options are:\n" \
       "    -h,--help: show the usage \n" \
       "    --config <filename>: use the specified config file \n" \
       "    --schema <owner:server>: apply the migrations on the specified schema (overwrites the config files) \n" \
       "    --ddl-only: will apply flyway command only on ddl migrations, cannot be specified with --dml-only (option will override default behaviour where flyway applies both ddl and dml) \n" \
       "    --dml-only: will apply flyway command only on dml migrations, cannot be specified with --ddl-only (option will override default behaviour where flyway applies both ddl and dml) \n" \
       "    --skip-migrations: skip applying flyway migrations - also skips post migrations \n" \
       "    --skip-post: skip the post migration scripts \n" \
       "    --skip-mviews: skip applying the mega views \n" \
       "    --skip-dba: skip the DBA post migration scripts \n" \
       "    --skip-repair-actions: skip the repair actions - valid only with the --repair command \n" \
       "    --info: run only flyway info, cannot be specified with --skip-migrations \n" \
       "    --repair: run only flyway repair, cannot be specified with --skip-migrations \n" \
       "    --validate: run only flyway validate, cannot be specified with --skip-migrations \n" \
       "    --validate-repair-actions: run only flyway validate-repair-actions, cannot be specified with --skip-migrations \n" \
       "    --baseline: run only flyway baselines, cannot be specified with --skip-migrations \n" \
       "    --target: the target version up to which Flyway should consider migrations, e.g. Aug22. Special values: current, latest \n" \
       "    --archive: to include also archived migrations in flyway phase \n" \
       "    --archive_only: to include only archived migrations in flyway phase \n" \
       "    --restore_history: to restore history for the automated checks (to be used only in the automated checks) \n"
  exit_function 0
}


config_function () {
  if [ -z "$1" ]; then
    exit_function 1 "Error no file specified to --config"
  fi
  if [ ! -f "$1" ]; then
    exit_function 1 "Error $1 doesn't exist"
  fi
  CONFIGFILE="$1"
}

process_schema_function() {
  local value=$1
  local schema=${value%%:*};
  local server=${value##*:};
  if [ -z "$schema" ] || [ -z "$server" ]; then
    exit_function 1 "Expected schema option in the format user:srver. Got '$value'"
  fi
  STDINPUT="$STDINPUT\nflyway.user=${schema}\nflyway.url=jdbc:oracle:thin:@${server}"
}

skip_migration_function() {
  SKIPMIGRATION=1
}

skip_mviews_function() {
  SKIPMVIEWS=1
}

skip_post_function() {
  SKIPPOST=1
}

skip_dba_function() {
  SKIPDBA=1
}

set_ddl_only_function() {
  DDLONLY=1
}

set_dml_only_function() {
  DMLONLY=1
}

check_options_function() {
  local total=$((INFO + REPAIR + VALIDATE + VALIDATEREPAIRACTIONS + BASELINE))
  if [ $total -gt 1 ]; then
    exit_function 1 "Error: --info, --repair, --validate, --validate-repair-actions and --baseline options are mutually exclusive"
  elif [ $total -eq 1 ] ; then
    if [ $SKIPMIGRATION -eq 1 ]; then
      exit_function 1 "Error: --skip-migrations cannot be specified with --info, --repair, --validate, --validate-repair-actions or --baseline"
    fi
    SKIPDBA=1
  fi
  if [[ $SKIPREPAIRACTIONS == 1 && $REPAIR == 0 ]]; then
    exit_function 1 "Error: --skip-repair-actions can be specified only with --repair"
  fi
  if [[ $DDLONLY == 1 && $DMLONLY == 1 ]]; then
    exit_function 1 "Error: --ddl-only and --dml-only options are mutually exclusive"
  fi
}

#
# Returns target in the form accepted by flyway
#
get_target() {
  # convert target to the for accepted by flyway
  # if target =current|latest we leave the value as it is and inform the user about the option
  local target=$1
  if [[ "$target" == "current" ]]
  then
    targetInfo="$target"
    echo "Target = $target: no versioned migrations will be applied but repeatable migrations will be, together with any callbacks"
  elif [[ "$target" == "latest" ]]
  then
    targetInfo="$target"
    echo "Target = $target: applying migrations up to the latest version of the schema, as defined by the migration with the highest
    version"
  else
    # declare months to be used in release validation
    declare -A months=([Jan]="01" [Feb]="02" [Mar]="03" [Apr]="04" [May]="05" [Jun]="06" [Jul]="07" [Aug]="08" [Sep]="09" [Oct]="10" [Nov]="11" [Dec]="12")

    year=`echo "$target" | cut -c4-5`
    monthInfo=`echo "$target" | cut -c1-3`
    ocr=".9"
    targetInfo=$year${months[$monthInfo]}$ocr
  fi
}

#
# Validate if target has the expected format, e.g. 2208.9
#
validate_target() {
  if [[ ! $targetInfo =~ ^[0-9]{4}.9$ && ! $targetInfo == "current"  && ! $targetInfo == "latest" ]]
  then
    validTargetFormat="Valid target value is formed from:
        - release    : one of the values {Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec}
        - year       : last two digits from the year
        e.g. Aug22"
    echo "Error: Target $TARGET is incorrect."; echo "$validTargetFormat";
    exit 0;
  fi
}

#
# Add target to flyway call
#
apply_target_function() {
  # get target in the format accepted by flyway
  # default: target=latest - applying migrations up to the latest version of the schema, as defined by the migration with the highest
  # version
  targetInfo='latest'
  get_target "$1"

  # validate target
  validate_target

  # add target to flyway call
  STDINPUT="$STDINPUT\nflyway.target=${targetInfo}"
}

parse_options_function() {
  while [[ $# -gt 0 ]]
  do
    key="$1"
    case $key in
      -h|--help)
        help_function
        shift
        ;;
      --config)
        config_function "$2"
        shift
        shift
        ;;
      --schema)
        process_schema_function "$2"
        shift
        shift
        ;;
      --ddl-only)
        set_ddl_only_function
        shift
        ;;
      --dml-only)
        set_dml_only_function
        shift
        ;;
      --skip-migrations)
        skip_migration_function
        shift
        ;;
      --skip-post)
        skip_post_function
        shift
        ;;
      --skip-mviews)
        skip_mviews_function
        shift
        ;;
      --skip-dba)
        skip_dba_function
        shift
        ;;
      --skip-repair-actions)
        SKIPREPAIRACTIONS=1
        shift
        ;;
      --info)
        INFO=1
        shift
        ;;
      --repair)
        REPAIR=1
        shift
        ;;
      --validate)
        VALIDATE=1
        shift
        ;;
      --validate-repair-actions)
        VALIDATEREPAIRACTIONS=1
        shift
        ;;
      --baseline)
        BASELINE=1
        shift
        ;;
      --target)
        apply_target_function "$2"
        shift
        shift
        ;;
      --archive)
        ARCHIVE=1
        shift
        ;;
      --archive_only)
        ARCHIVE_ONLY=1
        shift
        ;;
      --restore_history)
        RESTORE_HISTORY=1
        shift
        ;;
      *)
        echo -e "\nUnexpected parameter $key\n"
        help_function
        exit 1
        ;;
    esac
  done
  set -- "${POSITIONAL[@]}"
  check_options_function
}

set_branch_config() {
  local configDir="db_migration/conf/"
  local branchConfigDir="${configDir}branch/"

  # seeting branch configurations (details about private schemas)
  if [ -z "$CONFIGFILE" ]; then
    local branchName=`svn info .. 2>/dev/null | grep '^URL:' | egrep -o '(tags|branches)/[^/]+|trunk' | egrep -o '[^/]+$'`
    if [ -z "$branchName" ]; then
      # this requires a newer version of git on dev servers
      # branchName=`git branch --show-current 2>>/dev/null`
      branchName=`git branch 2>>/dev/null | grep "*" | sed 's/\*\s*//'`
    fi
    if [ -z "$branchName" ]; then
      echo "WARNING: Cannot determine the current branch name. Check svn info / git branch."
    else
      local fileName="${branchConfigDir}flyway.${branchName}.conf"
      if [ -f "$fileName" ]; then
        CONFIGFILE=$fileName
      else
        local fileNameForOtherBranch=`egrep -l "ia.otherbranches=(.*,)*${branchName}(,|$)" ${branchConfigDir}flyway.*.conf`
        if [[ "$fileNameForOtherBranch" = *[[:space:]]* ]]; then
          echo "Error ${branchName} appears in multiple ia.otherbranches property:" $fileNameForOtherBranch
          exit 1
        elif [ -f "$fileNameForOtherBranch" ]; then
          CONFIGFILE=$fileNameForOtherBranch
        else
          echo "WARNING: Could not find branch config file, $fileName."
        fi
      fi
    fi
    if [ -f "${configDir}flyway.local.conf" ]; then
      if [ ! -z "$CONFIGFILE" ]; then
        CONFIGFILE="$CONFIGFILE,"
      fi
      CONFIGFILE="${CONFIGFILE}${configDir}flyway.local.conf"
    fi
  fi
}

set_config_function() {
  local configDir="db_migration/conf/"
  local branchConfigDir="${configDir}branch/"

  # set branch config settings
  set_branch_config

  # set the rest of the configurations
  REG_CONFIGFILES="${configDir}flyway.conf"
  REG_CONFIGFILES_DDL="${configDir}flyway.conf,${configDir}flyway.history.ddl.conf,${configDir}flyway.ddl.conf"
  REG_CONFIGFILES_DML="${configDir}flyway.conf,${configDir}flyway.history.dml.conf,${configDir}flyway.dml.conf"
  POST_CONFIGFILES_DDL="${configDir}flyway.conf,${configDir}flyway.history.ddl.conf,${configDir}flyway.post.ddl.conf"
  POST_CONFIGFILES_DML="${configDir}flyway.conf,${configDir}flyway.history.dml.conf,${configDir}flyway.post.dml.conf"
  POST_CONFIGFILES_DDL_ONLY="${configDir}flyway.conf,${configDir}flyway.history.ddl.conf,${configDir}flyway.post.ddl.only.conf"
  POST_CONFIGFILES_DML_ONLY="${configDir}flyway.conf,${configDir}flyway.history.dml.conf,${configDir}flyway.post.dml.only.conf"
  if [ -z "$CONFIGFILE" ]; then
    echo "WARNING: Using the default config file. Specify a config file or create ${configDir}flyway.local.conf."
  else
    REG_CONFIGFILES="$REG_CONFIGFILES,$CONFIGFILE"
    REG_CONFIGFILES_DDL="${configDir}flyway.conf,${configDir}flyway.history.ddl.conf,${configDir}flyway.ddl.conf,$CONFIGFILE"
    REG_CONFIGFILES_DML="${configDir}flyway.conf,${configDir}flyway.history.dml.conf,${configDir}flyway.dml.conf,$CONFIGFILE"
    POST_CONFIGFILES_DDL="${configDir}flyway.conf,${configDir}flyway.history.ddl.conf,${configDir}flyway.post.ddl.conf,$CONFIGFILE"
    POST_CONFIGFILES_DML="${configDir}flyway.conf,${configDir}flyway.history.dml.conf,${configDir}flyway.post.dml.conf,$CONFIGFILE"
    POST_CONFIGFILES_DDL_ONLY="${configDir}flyway.conf,${configDir}flyway.history.ddl.conf,${configDir}flyway.post.ddl.only.conf,$CONFIGFILE"
    POST_CONFIGFILES_DML_ONLY="${configDir}flyway.conf,${configDir}flyway.history.dml.conf,${configDir}flyway.post.dml.only.conf,$CONFIGFILE"
  fi

  # set archive configurations
  if [[ "$ARCHIVE" == 1 ]]
  then
    ARCHIVEDDLCONF="${configDir}flyway.archive.ddl.conf"
    ARCHIVEDMLCONF="${configDir}flyway.archive.dml.conf"
    REG_CONFIGFILES_DDL="$REG_CONFIGFILES_DDL,$ARCHIVEDDLCONF"
    REG_CONFIGFILES_DML="$REG_CONFIGFILES_DML,$ARCHIVEDMLCONF"
    ARCHIVEPOSTDDLCONF="${configDir}flyway.post.archive.ddl.conf"
    ARCHIVEPOSTDMLCONF="${configDir}flyway.post.archive.dml.conf"
    POST_CONFIGFILES_DDL="$POST_CONFIGFILES_DDL,$ARCHIVEPOSTDDLCONF"
    POST_CONFIGFILES_DML="$POST_CONFIGFILES_DML,$ARCHIVEPOSTDMLCONF"
    ARCHIVEPOSTDDLONLYCONF="${configDir}flyway.post.ddl.only.archive.conf"
    ARCHIVEPOSTDMLONLYCONF="${configDir}flyway.post.dml.only.archive.conf"
    POST_CONFIGFILES_DDL_ONLY="$POST_CONFIGFILES_DDL_ONLY,$ARCHIVEPOSTDDLONLYCONF"
    POST_CONFIGFILES_DML_ONLY="$POST_CONFIGFILES_DML_ONLY,$ARCHIVEPOSTDMLONLYCONF"
  fi
  if [[ "$ARCHIVE_ONLY" == 1 ]]
  then
    ARCHIVEDDLCONF="${configDir}flyway.conf,${configDir}flyway.history.ddl.conf,${configDir}flyway.archive.only.ddl.conf"
    ARCHIVEDMLCONF="${configDir}flyway.conf,${configDir}flyway.history.dml.conf,${configDir}flyway.archive.only.dml.conf"
    REG_CONFIGFILES_DDL="$ARCHIVEDDLCONF"
    REG_CONFIGFILES_DML="$ARCHIVEDMLCONF"
    ARCHIVEPOSTDDLCONF="${configDir}flyway.conf,${configDir}flyway.history.ddl.conf,${configDir}flyway.post.archive.only.ddl.conf"
    ARCHIVEPOSTDMLCONF="${configDir}flyway.conf,${configDir}flyway.history.dml.conf,${configDir}flyway.post.archive.only.dml.conf"
    POST_CONFIGFILES_DDL="$ARCHIVEPOSTDDLCONF"
    POST_CONFIGFILES_DML="$ARCHIVEPOSTDMLCONF"
    POST_CONFIGFILES_DDL_ONLY="${configDir}flyway.conf,${configDir}flyway.history.ddl.conf,${configDir}flyway.post.only.archive.ddl.conf"
    POST_CONFIGFILES_DML_ONLY="${configDir}flyway.conf,${configDir}flyway.history.dml.conf,${configDir}flyway.post.only.archive.dml.conf"
  fi
}

error_function() {
  exit_function 1 "Error in $1"
}

read_config_var_function() {
  local keyword=$1
  local postProcess=$2

  CONFIG_FILE_LIST=`echo $REG_CONFIGFILES | sed 's/,/ /g'`
  # first attempt to read from local input, then from config files
  local value=`echo -e "$STDINPUT" | grep -hP "^${keyword}" | tail -n 1 | cut -d '=' -f 2`
  if [ -z "$value" ]; then
    value=`grep -hP "^${keyword}" $CONFIG_FILE_LIST | tail -n 1 | cut -d '=' -f 2`
  fi
  if [ ! -z "$value" ] && [ ! -z "$postProcess" ]; then
    value=$($postProcess $value)
  fi
  echo "$value"
}

post_process_url_function() {
  local value=$1
  value=`echo "$value" | cut -d '@' -f 2`
  echo $value
}

parse_schema_function() {
  FLYWAYUSER=$(read_config_var_function 'flyway.user=')
  FLYWAYURL=$(read_config_var_function 'flyway.url=' "post_process_url_function")
  FLYWAYPWD=$(read_config_var_function 'flyway.password=')

  if [ -z "$FLYWAYUSER" ] || [ -z "$FLYWAYURL" ] || [ -z "$FLYWAYPWD" ]; then
    exit_function 1 "Error: could not find user, password or URL in $REG_CONFIGFILES"
  fi
}

execute_flyway_function_for_ddl_migrations() {
  local CMD=$1
  local CONFIGFILES=$2

  # when we want to apply mega views every time, we need to change the content of R__new_mega-views.sql so that flyway applies it each time
  # check if the command is migrate and mega views are not skipped
  if [[ "$CMD" == "migrate" && $SKIPMVIEWS == 0 && $SKIPMVIEWSREGEN == 0 ]]
  then
    # in order to apply R__new_mega-views.sql every time, do the following:

    # save the base path
    basePath=$PWD

    # copy the original content of the file in a temporary file
    cd db_migration/db_migration_ddl/me-views
    actionFile="R__new_mega-views.sql"
    if test -f "$actionFile"; then
      # generate the message that will be added as first line so that the content of R__new_mega-views.sql is updated every time
      randomString=`shuf -er -n20  {A..Z} {a..z} {0..9} | tr -d '\n'`
      message="-- Simple comment necessary for flyway to execute mega views every time. Do not delete it. Random string: $randomString"

      # add a comment line at the beginning of the original file so that flyway see a change and apply the file
      echo -e "$message\n$(cat $actionFile)" > $actionFile
    else
        echo "Mega views file R__new_mega-views.sql does not exist."
    fi

    # go back in the base path
    cd $basePath
  fi

  # run flyway
  FLYWAYOUTPUT=$(echo -e $STDINPUT | ~master/opt/flyway/flyway -configFiles=$CONFIGFILES $CMD  2>&1)
  flywayStatus=$?
  echo "$FLYWAYOUTPUT"
  if [ "$flywayStatus" -ne 0 ]; then
    DDLMIGAPPLYSUCCESSFULLY=0
    if [[ "$VALIDATEREPAIRACTIONS" == 1 ]]
    then
      # we don't want to exit yet as we want to check also the repair migrations
      echo "Error in the execution of flyway"
    else
      error_function "the execution of flyway"
    fi
  fi
}

execute_flyway_function_for_dml_migrations() {
  if [[ "$DDLMIGAPPLYSUCCESSFULLY" == 1 ]]
  then
    # if ddl migrations were applied successfully we can apply also dml migrations
    local CMD=$1
    local CONFIGFILES=$2

    # run flyway
    FLYWAYOUTPUT=$(echo -e $STDINPUT | ~master/opt/flyway/flyway -configFiles=$CONFIGFILES $CMD  2>&1)
    flywayStatus=$?
    echo "$FLYWAYOUTPUT"
    if [ "$flywayStatus" -ne 0 ]; then
      if [[ "$VALIDATEREPAIRACTIONS" == 1 ]]
      then
        # we don't want to exit yet as we want to check also the repair migrations
        echo "Error in the execution of flyway"
      else
        error_function "the execution of flyway"
      fi
    fi
  else
    echo "DML migrations will be applied only after DDL migrations are fixed!"
  fi
}

execute_migrations_function() {
  local CMD=migrate
  local MIGRATION_TYPE=$1
  local CONFIGURATION_FILES=$2
  if [ $INFO -eq 1 ]; then
    CMD=info
  elif [ $REPAIR -eq 1 ]; then
    CMD=repair
  elif [ $VALIDATE -eq 1 ]; then
    CMD=validate
  elif [ $VALIDATEREPAIRACTIONS -eq 1 ]; then
    CMD=validate
  elif [ $BASELINE -eq 1 ]; then
    CMD=baseline
  fi

  if [ $RESTORE_HISTORY -eq 1 ]; then
    local actionFile=$(find . -name "RESTORE_HISTORY.sql")
    if [ -z "$actionFile" ]; then
      echo "RESTORE HISTORY ERROR: RESTORE_HISTORY.sql not found"
      exit_function 0 "RESTORE HISTORY ERROR"
    else
      local tmpFile=$( mktemp repair_XXXXXX.ddl )
      cp $actionFile $tmpFile
      echo -e "\ncommit;\nquit;\n" >> $tmpFile
      local ret=$(run_sqlplus_function $tmpFile)
      rm $tmpFile
      if [ ! -z "$ret" ]; then
        echo "RESTORE HISTORY ERROR: Could not apply restore history file"
        exit_function 0 "RESTORE HISTORY ERROR"
      fi
        echo "RESTORE HISTORY SUCCESS"
        exit_function 0 "Success"
    fi
  elif [ $INFO -eq 1 ] || [ $REPAIR -eq 1 ] || [ $VALIDATE -eq 1 ] || [ $VALIDATEREPAIRACTIONS -eq 1 ] || [ $BASELINE -eq 1 ]; then

    # execute flyway command
    if [[ "$MIGRATION_TYPE" == "DDL" ]]
    then
      execute_flyway_function_for_ddl_migrations $CMD $CONFIGURATION_FILES
    elif [[ "$MIGRATION_TYPE" == "DML" ]]
    then
      execute_flyway_function_for_dml_migrations $CMD $CONFIGURATION_FILES
    else
      echo "There was an error in the sent migration type!"
    fi

    # validate repair actions
    if [[ $VALIDATEREPAIRACTIONS == 1 ]];
    then
      local text="Migration checksum mismatch for migration version"
      local changes=$(echo "$FLYWAYOUTPUT" | grep "$text " | sed "s/.*$text //;s/ .*//")
      if [ -z "$changes" ];
      then
        echo "No versioned migrations needs repair"
      else
        echo "Validating repair migartion files too"
        echo "The repair actions for the following versioned migrations need to be analyzed"
        echo "$changes"
        applyRepairActionSuccess=1
        for v in $changes; do
          local versionFile=$(find . -name "V${v}__*.sql")
          # get current date
          local now=$(date +'%m/%d/%Y %T');
          if [ -z "$versionFile" ]; then
            applyRepairActionSuccess=0
            echo "REPAIR ERROR: Cannot find the migration versioned file for $v"
          fi
          local action=$(grep "Repair action" $versionFile)
          if [ -z "$action" ];
          then
            applyRepairActionSuccess=0
            echo "REPAIR ERROR: Cannot find the repair action tag in $versionFile"
          else
            if [[ $action =~ IGNORE ]]; then
              continue
            elif [[ $action =~ EXECUTE ]];
            then
              local actionFile=$(find . -name "A${v}__*.sql")
              if [ -z "$actionFile" ];
              then
                applyRepairActionSuccess=0
                echo "REPAIR ERROR: The version $v specifies EXECUTE repair action but the file A${v}__*.sql doesn't exist"
              else
                echo "The repair action for version $v is set to EXECUTE. Will now validate $actionFile"
                local tmpFile=$( mktemp repair_XXXXXX.ddl )
                cp $actionFile $tmpFile
                echo -e "\ncommit;\nquit;\n" >> $tmpFile
                local ret=$(run_sqlplus_function $tmpFile)
                rm $tmpFile
                if [ ! -z "$ret" ]; then
                  echo "$ret"
                  applyRepairActionSuccess=0
                  echo "REPAIR ERROR: Could not apply the repair migration from $actionFile"
                fi
              fi
            else
              applyRepairActionSuccess=0
              echo "REPAIR ERROR: The version $v specifies an unknown repair action: $action"
            fi
          fi
        done

        if [[ "$applyRepairActionSuccess" == 0 ]]
        then
          exit_function 1 "REPAIR ERROR: There where errors in repair migrations."
        fi
      fi
    fi

    # repair flyway schema history and apply repair actions on the schema
    if [[ $REPAIR == 1 && $SKIPREPAIRACTIONS == 0 ]]; then
      local text="Repairing Schema History table for version"
      local changes=$(echo "$FLYWAYOUTPUT" | grep "$text " | sed "s/.*$text //;s/ .*//")
      if [ -z "$changes" ]; then
        echo "No versioned migrations found to repair"
      else
        echo "The repair actions for the following versioned migrations need to be analyzed"
        echo "$changes"
        applyRepairActionSuccess=1
        for v in $changes; do
          local versionFile=$(find . -name "V${v}__*.sql")
          # get current date
          local now=$(date +'%m/%d/%Y %T');
          if [ -z "$versionFile" ]; then
            applyRepairActionSuccess=0
            echo "ERROR: Cannot find the migration versioned file for $v"
          fi
          local action=$(grep "Repair action" $versionFile)
          if [ -z "$action" ];
          then
            applyRepairActionSuccess=0
            echo "ERROR: Cannot find the repair action tag in $versionFile"
          else
            if [[ $action =~ IGNORE ]]; then
              echo "The repair action for version $v is set to IGNORE"
              continue
            elif [[ $action =~ EXECUTE ]];
            then
              local actionFile=$(find . -name "A${v}__*.sql")
              if [ -z "$actionFile" ];
              then
                applyRepairActionSuccess=0
                echo "ERROR: The version $v specifies EXECUTE repair action but the file A${v}__*.sql doesn't exist"
              else
                echo "The repair action for version $v is set to EXECUTE. Will now run $actionFile"
                local tmpFile=$( mktemp repair_XXXXXX.ddl )
                cp $actionFile $tmpFile
                echo -e "\ncommit;\nquit;\n" >> $tmpFile
                local ret=$(run_sqlplus_function $tmpFile)
                rm $tmpFile
                if [ ! -z "$ret" ]; then
                  echo "$ret"
                  applyRepairActionSuccess=0
                  echo "ERROR: Could not apply the repair migration from $actionFile"
                fi
              fi
            else
              applyRepairActionSuccess=0
              echo "ERROR: The version $v specifies an unknown repair action: $action"
            fi
          fi
        done

        if [[ "$applyRepairActionSuccess" == 0 ]]
        then
          exit_function 1 "ERROR: There where errors in applying repair migrations. Fix the cases and re-run repair command."
        fi

      fi
    fi
  else
    if [ $SKIPMIGRATION -eq 1 ]; then
      echo "Skipping regular and post migrations as instructed"
    else
      if [ $SKIPPOST -eq 1 ]; then
        # running regular migrations
        if [[ "$MIGRATION_TYPE" == "DDL" ]]
        then
          echo "Running regular DDL migrations"
          execute_flyway_function_for_ddl_migrations $CMD $CONFIGURATION_FILES
        elif [[ "$MIGRATION_TYPE" == "DML" ]]
        then
          echo "Running regular DML migrations"
          execute_flyway_function_for_ddl_migrations $CMD $CONFIGURATION_FILES
        else
          echo "There was an error in the sent migration type!"
        fi
        echo "Skipping post migrations as instructed"
      else
        if [[ "$MIGRATION_TYPE" == "DDL" ]]
        then
          echo "Running regular & post DDL migrations"
          execute_flyway_function_for_ddl_migrations $CMD $CONFIGURATION_FILES
        elif [[ "$MIGRATION_TYPE" == "DML" ]]
        then
          echo "Running regular & post DML migrations"
          execute_flyway_function_for_dml_migrations $CMD $CONFIGURATION_FILES
        else
          echo "There was an error in the sent migration type!"
        fi
      fi
    fi
  fi
}

run_sqlplus_function() {
  local file=$1
  local SQLPLUSOUT=$(sqlplus $FLYWAYUSER/$FLYWAYPWD@$FLYWAYURL @$file)
  local SQLPLUSOUTERR=$(grep "ORA-" <<< $SQLPLUSOUT)
  if [ ! -z "$SQLPLUSOUTERR" ]; then
    echo "$SQLPLUSOUT"
  fi
}

generate_mviews_function() {
  if [ $SKIPMVIEWS -eq 1 ]; then
      echo "Skipping mega views as instructed"
      return;
  fi

  local CMD=migrate
    if [ $INFO -eq 1 ]; then
      CMD=info
    elif [ $REPAIR -eq 1 ]; then
      CMD=repair
    elif [ $VALIDATE -eq 1 ]; then
      CMD=validate
    elif [ $VALIDATEREPAIRACTIONS -eq 1 ]; then
      CMD=validate
    elif [ $BASELINE -eq 1 ]; then
      CMD=baseline
    fi

  echo "Refreshing mega views definitions"
  curDir=$PWD
  cd db_migration/db_migration_ddl/me-views

  local tmpMegaViewsFile="R__new_mega-views.sql"
  local initialComment=""

  # if command is any other command than MIGRATE we need to keep our first custom comment line in order to get right status
  if [[ "$CMD" != "migrate" ]]
  then
    # check if R__new_mega-views.sql file already exists
    if [[ -f "$tmpMegaViewsFile" ]]; then
      # if exists, copy the first comment if it's our custom comment
      comment=$(head -n 1 $tmpMegaViewsFile)
      if [[ "$comment" =~ "Simple comment necessary for flyway to execute mega views every time" ]]
      then
        # we found our custom comment
        initialComment="$comment"
      fi
    fi
  fi

  # generate the content of the mega view file
  cd ../../../..
  cd link/private/cstools
  /usr/local/php/bin/php ./genMegaViewsDDL.phtml > ../../../db/db_migration/db_migration_ddl/me-views/$tmpMegaViewsFile
  cd ../../../db/db_migration/db_migration_ddl/me-views
  if [ ! -s $tmpMegaViewsFile ]; then
      error_function "in generating mega views"
  fi
  if [[ "$initialComment" != "" ]]
  then
    # add a comment line at the beginning of the original file so that flyway see no change in the file for the initial comment
    echo -e "$initialComment\n$(cat $tmpMegaViewsFile)" > $tmpMegaViewsFile
  fi

  # ch dir to db_migration
  cd $curDir
}

apply_dba_function() {
  if [ $SKIPDBA -eq 1 ]; then
    echo "Skipping DBA post migrations as instructed"
    return;
  fi
  echo "Running DBA post migrations"
  SCHEMAPREFIX="$(cut -d "_" -f1 <<< "$FLYWAYUSER")"
  SCHEMASUFFIX="$(cut -d "_" -f3 <<< "$FLYWAYUSER")"
  if [ -z $SCHEMAPREFIX ] || [ -z $SCHEMASUFFIX ]; then
    error_function "flyway.user"
  fi
  local CMD="exec intacct_db.process_whole_schema('$SCHEMAPREFIX', '$SCHEMASUFFIX', 'Y');"
  SQLPLUSOUT=$(sqlplus $FLYWAYUSER/$FLYWAYPWD@$FLYWAYURL <<< $CMD)
  SQLPLUSOUTERR=$(grep "ORA-" <<< $SQLPLUSOUT)
  if [ ! -z "$SQLPLUSOUTERR" ]; then
    echo "$SQLPLUSOUT"
    error_function "in the running process_whole_schema"
  fi
  echo "Done DBA post migrations"
}

#
# Check if TNS_ADMIN variable is set
#
check_tns() {
  tns_var=`printenv TNS_ADMIN`

  if [[ -z "${tns_var+x}" ]]
  then
    if [[ -f "/etc/tnsnames.ora" ]]
    then
      export TNS_ADMIN='/etc'
    else
      echo "Error: TNS_ADMIN environment variable is not set. Please set the variable value to /etc"; exit 0
    fi
  fi
}

# first read standard input if any
if [[ ! -t 0 ]]; then
    STDINPUT=`cat -`
fi

check_tns

parse_options_function $@

readonly CURRENTDIRECTORY=$(pwd)
readonly BASEDIR=$(dirname "$0")

cd $BASEDIR/../db

# readonly APPDIRECTORY=$(find . -type d -name app)
# cd $APPDIRECTORY

set_config_function

parse_schema_function

echo "Executing on: $FLYWAYUSER@$FLYWAYURL"

if [[ $DDLONLY == 1 ]]
then
  # execute only ddl migrations
  echo "---------------------------------------------------------------------------------------------------------"
  echo "--- DDL migrations ---"
  echo "---------------------------------------------------------------------------------------------------------"

  generate_mviews_function

  MIG_TYPE="DDL"
  if [[ $SKIPPOST == 0 ]]
  then
    # apply also post migrations
    if [[ $INFO == 1 ]]
    then
      # apply info on both regular and post migrations
      echo "Using config files: " $POST_CONFIGFILES_DDL
      execute_migrations_function $MIG_TYPE $POST_CONFIGFILES_DDL
    else
      # apply ddl migrations
      echo "Using config files: " $REG_CONFIGFILES_DDL
      execute_migrations_function $MIG_TYPE $REG_CONFIGFILES_DDL
      # apply ddl post migrations
      echo "---------------------------------------------------------------------------------------------------------"
      echo "--- DDL post migrations ---"
      echo "---------------------------------------------------------------------------------------------------------"
      echo "Using config files: " $POST_CONFIGFILES_DDL_ONLY
      execute_migrations_function $MIG_TYPE $POST_CONFIGFILES_DDL_ONLY
    fi
  else
    # apply only regular migrations
    echo "Using config files: " $REG_CONFIGFILES_DDL
    execute_migrations_function $MIG_TYPE $REG_CONFIGFILES_DDL
  fi

  # chg dir to app before running dba post migrations
  cd ..
  apply_dba_function

elif [[ $DMLONLY == 1 ]]
then
  # execute only dml migrations
  echo "---------------------------------------------------------------------------------------------------------"
  echo "--- DML migrations ---"
  echo "---------------------------------------------------------------------------------------------------------"

  MIG_TYPE="DML"
  if [[ $SKIPPOST == 0 ]]
  then
    # apply also post migrations
    if [[ $INFO == 1 ]]
    then
      # apply info on both regular and post migrations
      echo "Using config files: " $POST_CONFIGFILES_DML
      execute_migrations_function $MIG_TYPE $POST_CONFIGFILES_DML
    else
      # apply dml migrations
      echo "Using config files: " $REG_CONFIGFILES_DML
      execute_migrations_function $MIG_TYPE $REG_CONFIGFILES_DML
      # apply dml post migrations
      echo "---------------------------------------------------------------------------------------------------------"
      echo "--- DML post migrations ---"
      echo "---------------------------------------------------------------------------------------------------------"
      echo "Using config files: " $POST_CONFIGFILES_DML_ONLY
      execute_migrations_function $MIG_TYPE $POST_CONFIGFILES_DML_ONLY
    fi
  else
    # apply only regular migrations
    echo "Using config files: " $REG_CONFIGFILES_DML
    execute_migrations_function $MIG_TYPE $REG_CONFIGFILES_DML
  fi

else
  # default mode, execute both ddl and dml migrations

  # specifc flow for --info to have only two tables in the output (one with all information regarding DDL and one with all information
  # regarding DML )
  if [[ $INFO == 1 ]]
  then
    # call info on all ddl and after that on all dml
    # execute info on ddl migrations
    echo "---------------------------------------------------------------------------------------------------------"
    echo "--- DDL migrations ---"
    echo "---------------------------------------------------------------------------------------------------------"
    generate_mviews_function

    echo "Using config files: " $POST_CONFIGFILES_DDL
    MIG_TYPE="DDL"
    execute_migrations_function $MIG_TYPE $POST_CONFIGFILES_DDL

    # save current directory as we need it for dml migrations too
    curDir=$PWD
    # chg dir to app before running dba post migrations
    cd ..
    apply_dba_function

    # execute info on dml migrations
    echo "---------------------------------------------------------------------------------------------------------"
    echo "--- DML migrations ---"
    echo "---------------------------------------------------------------------------------------------------------"
    cd $curDir

    echo "Using config files: " $POST_CONFIGFILES_DML
    MIG_TYPE="DML"
    execute_migrations_function $MIG_TYPE $POST_CONFIGFILES_DML
  else
    # for all the other operations follow the order: DDL, DML, DDL post, DML post

    # execute ddl migrations
    echo "---------------------------------------------------------------------------------------------------------"
    echo "--- DDL migrations ---"
    echo "---------------------------------------------------------------------------------------------------------"
    generate_mviews_function

    echo "Using config files: " $REG_CONFIGFILES_DDL
    MIG_TYPE="DDL"
    execute_migrations_function $MIG_TYPE $REG_CONFIGFILES_DDL

    # execute dml migrations
    echo "---------------------------------------------------------------------------------------------------------"
    echo "--- DML migrations ---"
    echo "---------------------------------------------------------------------------------------------------------"

    echo "Using config files: " $REG_CONFIGFILES_DML
    MIG_TYPE="DML"
    execute_migrations_function $MIG_TYPE $REG_CONFIGFILES_DML

    if [[ $SKIPPOST == 0 ]]
    then
      # we apply also post migrations

      # execute ddl post migrations
      echo "---------------------------------------------------------------------------------------------------------"
      echo "--- DDL post migrations ---"
      echo "---------------------------------------------------------------------------------------------------------"
      # skip mega views generation
      SKIPMVIEWSREGEN=1

      echo "Using config files: " $POST_CONFIGFILES_DDL_ONLY
      MIG_TYPE="DDL"
      execute_migrations_function $MIG_TYPE $POST_CONFIGFILES_DDL_ONLY

      # save current directory as we need it for dml migrations too
      curDir=$PWD
      # chg dir to app before running dba post migrations
      cd ..
      apply_dba_function

      # execute dml post migrations
      echo "---------------------------------------------------------------------------------------------------------"
      echo "--- DML post migrations ---"
      echo "---------------------------------------------------------------------------------------------------------"
      cd $curDir

      echo "Using config files: " $POST_CONFIGFILES_DML_ONLY
      MIG_TYPE="DML"
      execute_migrations_function $MIG_TYPE $POST_CONFIGFILES_DML_ONLY
    else
      # we don't execute post migrations

      # we call the rest of functions before ending
      # save current directory as we need it for dml migrations too
      curDir=$PWD
      # chg dir to app before running dba post migrations
      cd ..
      apply_dba_function
    fi
  fi

fi

exit_function 0 "Success"
