<?php
/**
 * Class to handle the RPD data for schemalet database schema
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Intacct Corporation, All Rights Reserved
 */

/**
 * Class RPDDBData - handles the RPD data for schemalet database
 */
class RPDUsageTrackingSchemaDBData extends RPDDBData
{
    /**
     * @var string $dbHost the server host name of the DB server where usage tracking is saved
     */
    private $dbHost;

    /**
     * @var string $dbUser the user name of the DB where usage tracking is saved
     */
    private $dbUser;

    /**
     * @param RPDData $rpdData
     */
    public function __construct(RPDData $rpdData)
    {
        parent::__construct($rpdData);

        $reportingConfig = GetValueForIACFGProperty('REPORTING');
        $this->dbHost = arrayExtractValue($reportingConfig, 'UT_DB_HOST');
        if ( ! $this->dbHost ) {
            dieFL('Cannot register the UT database because the db host is not specified in the configuration');
        }

        $this->dbUser = arrayExtractValue($reportingConfig, 'UT_DB_USER');
        if ( ! $this->dbUser ) {
            dieFL('Cannot register the UT database because the db user is not specified in the configuration');
        }
    }

    /**
     * Return the name of the configuration key holding the encrypted DB password
     *
     * @return string the configuration key holding the password
     */
    protected function getDBPasswordConfigKey()
    {
        return 'UT_DB_PASSWORD';
    }

    /**
     *  Generate the XML for the common components
     */
    protected function generateXMLTemplate()
    {
        // we only have physical layer objects for usage tracking
        $this->physicalLayer->generateXML($this->isNew, $this->rpdData);
    }

    /**
     * Retrieve the name of the DB in the RPD
     *
     * @return string the DB name
     */
    protected function getDBName()
    {
        return 'UsageTracking';
    }

    /**
     * Retrieve the name of the DB user
     *
     * @return string the user name for DB connection
     */
    protected function getDBUserName()
    {
        return $this->dbUser;
    }

    /**
     * Retrieve the host name of the DB server
     *
     * @return string the host name for DB connection
     */
    protected function getDBHostName()
    {
        return strtoupper($this->dbHost) . 'OBIEE';
    }

    /**
     * Retrieve the script to execute after connecting to the DB
     *
     * @return array the SQL scripts
     */
    protected function getPostConnectScript()
    {
        return [];
    }

    /**
     * Retrieve the script to execute before querying the DB
     *
     * @return array the SQL scripts
     */
    protected function getBeforeQueryScript()
    {
        return [];
    }

    /**
     * Get the schema dynamic name
     *
     * @return string
     */
    protected function getSchemaDynamicName()
    {
        return self::RPD_VAR_USAGE_TRACKING_SCHEMA;
    }

    public function initialize()
    {
        foreach ( self::$UT_TABLES as $table => $tableInfo ) {
            $this->physicalLayer->addTable($table, $table, RPDPhysicalLayer::TYPE_TABLE);
            foreach ($tableInfo as $column ) {
                $this->physicalLayer->addColumn($column['n'], $table, $column['t'], $column['p'], $column['l']);
            }
        }

        foreach ( self::$UT_KEYS as $key ) {
            $this->physicalLayer->addPrimaryKey('pk' . $key['t'], $key['t'], $key['c']);
        }

        foreach ( self::$UT_FKs as $key ) {
            $this->physicalLayer->addForeignKey(
                'fk' . $key['t'],
                $key['t'],
                $key['c'],
                $key['p'],
                'pk' . $key['p']
            );
        }
    }

    /**
     * @var array[][] $UT_TABLES
     */
    private static $UT_TABLES = [
        'S_NQ_ACCT' => [
            [ 'n' => 'CACHE_IND_FLG', 't' => 'CHAR', 'p' => 1, 'l' => false, ],
            [ 'n' => 'COMPILE_TIME_SEC', 't' => 'DOUBLE', 'p' => 10 , 'l' => true, ],
            [ 'n' => 'CUM_DB_TIME_SEC', 't' => 'DOUBLE', 'p' => 10 , 'l' => true, ],
            [ 'n' => 'CUM_NUM_DB_ROW', 't' => 'DOUBLE', 'p' => 20 , 'l' => true, ],
            [ 'n' => 'ECID', 't' => 'VARCHAR', 'p' => 1024 , 'l' => true, ],
            [ 'n' => 'END_DT', 't' => 'DATETIME', 'p' => 1024 , 'l' => true, ],
            [ 'n' => 'END_HOUR_MIN', 't' => 'CHAR', 'p' => 5 , 'l' => true, ],
            [ 'n' => 'END_TS', 't' => 'DATETIME', 'p' => 5 , 'l' => true, ],
            [ 'n' => 'ERROR_TEXT', 't' => 'VARCHAR', 'p' => 250 , 'l' => true, ],
            [ 'n' => 'HASH_ID', 't' => 'VARCHAR', 'p' => 128 , 'l' => true, ],
            [ 'n' => 'ID', 't' => 'VARCHAR', 'p' => 50, 'l' => false, ],
            [ 'n' => 'IMPERSONATOR_USER_NAME', 't' => 'VARCHAR', 'p' => 128 , 'l' => true, ],
            [ 'n' => 'NODE_ID', 't' => 'VARCHAR', 'p' => 100 , 'l' => true, ],
            [ 'n' => 'NUM_CACHE_HITS', 't' => 'DOUBLE', 'p' => 10 , 'l' => true, ],
            [ 'n' => 'NUM_CACHE_INSERTED', 't' => 'DOUBLE', 'p' => 10 , 'l' => true, ],
            [ 'n' => 'NUM_DB_QUERY', 't' => 'DOUBLE', 'p' => 10 , 'l' => true, ],
            [ 'n' => 'PRESENTATION_NAME', 't' => 'VARCHAR', 'p' => 128 , 'l' => true, ],
            [ 'n' => 'QUERY_BLOB', 't' => 'LONGVARCHAR', 'p' => 32768 , 'l' => true, ],
            [ 'n' => 'QUERY_KEY', 't' => 'VARCHAR', 'p' => 128 , 'l' => true, ],
            [ 'n' => 'QUERY_SRC_CD', 't' => 'VARCHAR', 'p' => 30 , 'l' => true, ],
            [ 'n' => 'QUERY_TEXT', 't' => 'VARCHAR', 'p' => 1024 , 'l' => true, ],
            [ 'n' => 'REPOSITORY_NAME', 't' => 'VARCHAR', 'p' => 128 , 'l' => true, ],
            [ 'n' => 'RESP_TIME_SEC', 't' => 'DOUBLE', 'p' => 10 , 'l' => true, ],
            [ 'n' => 'ROW_COUNT', 't' => 'DOUBLE', 'p' => 20 , 'l' => true, ],
            [ 'n' => 'SAW_DASHBOARD', 't' => 'VARCHAR', 'p' => 150 , 'l' => true, ],
            [ 'n' => 'SAW_DASHBOARD_PG', 't' => 'VARCHAR', 'p' => 150 , 'l' => true, ],
            [ 'n' => 'SAW_SRC_PATH', 't' => 'VARCHAR', 'p' => 250 , 'l' => true, ],
            [ 'n' => 'SERVICE_NAME', 't' => 'VARCHAR', 'p' => 128 , 'l' => true, ],
            [ 'n' => 'SESSION_ID', 't' => 'DOUBLE', 'p' => 10 , 'l' => true, ],
            [ 'n' => 'START_DT', 't' => 'DATETIME', 'p' => 10 , 'l' => true, ],
            [ 'n' => 'START_HOUR_MIN', 't' => 'CHAR', 'p' => 5 , 'l' => true, ],
            [ 'n' => 'START_TS', 't' => 'DATETIME', 'p' => 5 , 'l' => true, ],
            [ 'n' => 'SUBJECT_AREA_NAME', 't' => 'VARCHAR', 'p' => 128 , 'l' => true, ],
            [ 'n' => 'SUCCESS_FLG', 't' => 'DOUBLE', 'p' => 10 , 'l' => true, ],
            [ 'n' => 'TENANT_ID', 't' => 'VARCHAR', 'p' => 128 , 'l' => true, ],
            [ 'n' => 'TOTAL_TEMP_KB', 't' => 'DOUBLE', 'p' => 20 , 'l' => true, ],
            [ 'n' => 'TOTAL_TIME_SEC', 't' => 'DOUBLE', 'p' => 10 , 'l' => true, ],
            [ 'n' => 'USER_NAME', 't' => 'VARCHAR', 'p' => 128 , 'l' => true, ],
        ],
        'S_NQ_DB_ACCT' => [
            [ 'n' => 'END_DT', 't' => 'DATETIME', 'p' => 7, 'l' => true, ],
            [ 'n' => 'END_HOUR_MIN', 't' => 'CHAR', 'p' => 5, 'l' => true, ],
            [ 'n' => 'END_TS', 't' => 'DATETIME', 'p' => 5, 'l' => true, ],
            [ 'n' => 'HASH_ID', 't' => 'VARCHAR', 'p' => 128, 'l' => true, ],
            [ 'n' => 'ID', 't' => 'DOUBLE', 'p' => 10, 'l' => true, ],
            [ 'n' => 'LOGICAL_QUERY_ID', 't' => 'VARCHAR', 'p' => 50, 'l' => true, ],
            [ 'n' => 'PHYSICAL_HASH_ID', 't' => 'VARCHAR', 'p' => 128, 'l' => true, ],
            [ 'n' => 'QUERY_BLOB', 't' => 'LONGVARCHAR', 'p' => 32768, 'l' => true, ],
            [ 'n' => 'QUERY_TEXT', 't' => 'VARCHAR', 'p' => 1024, 'l' => true, ],
            [ 'n' => 'ROW_COUNT', 't' => 'DOUBLE', 'p' => 20, 'l' => true, ],
            [ 'n' => 'START_DT', 't' => 'DATETIME', 'p' => 20, 'l' => true, ],
            [ 'n' => 'START_HOUR_MIN', 't' => 'CHAR', 'p' => 5, 'l' => true, ],
            [ 'n' => 'START_TS', 't' => 'DATETIME', 'p' => 5, 'l' => true, ],
            [ 'n' => 'TIME_SEC', 't' => 'DOUBLE', 'p' => 10, 'l' => true, ],
        ],
        'S_NQ_INITBLOCK' => [
            [ 'n' => 'BLOCK_NAME', 't' => 'VARCHAR', 'p' => 128, 'l' => true, ],
            [ 'n' => 'DURATION', 't' => 'DOUBLE', 'p' => 13, 'l' => true, ],
            [ 'n' => 'ECID', 't' => 'VARCHAR', 'p' => 1024, 'l' => true, ],
            [ 'n' => 'END_TS', 't' => 'TIMESTAMP', 'p' => 1024, 'l' => true, ],
            [ 'n' => 'NOTES', 't' => 'VARCHAR', 'p' => 1024, 'l' => true, ],
            [ 'n' => 'REPOSITORY_NAME', 't' => 'VARCHAR', 'p' => 128, 'l' => true, ],
            [ 'n' => 'SERVICE_NAME', 't' => 'VARCHAR', 'p' => 128, 'l' => true, ],
            [ 'n' => 'SESSION_ID', 't' => 'DOUBLE', 'p' => 10, 'l' => true, ],
            [ 'n' => 'START_TS', 't' => 'TIMESTAMP', 'p' => 10, 'l' => true, ],
            [ 'n' => 'TENANT_ID', 't' => 'VARCHAR', 'p' => 128, 'l' => true, ],
            [ 'n' => 'USER_NAME', 't' => 'VARCHAR', 'p' => 128, 'l' => true, ],
        ],
    ];

    /**
     * @var array[] $UT_KEYS
     */
    private static $UT_KEYS = [
        [ 't' => 'S_NQ_ACCT', 'c' => ['ID'], ],
    ];

    /**
     * @var array[] $UT_FKs
     */
    private static $UT_FKs = [
        [ 't' => 'S_NQ_DB_ACCT', 'c' => ['LOGICAL_QUERY_ID'], 'p' => 'S_NQ_ACCT', ],
    ];
}
