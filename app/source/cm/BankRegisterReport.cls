<?
import('FinancialAccountRegisterReport');
require_once('bankregister.rpt');
require_once('backend_cashmgmt.inc');
require_once('prrecordhistory.inc');

class BankRegisterReport extends FinancialAccountRegisterReport {

    /* @var bool $reportByPostingDate */
    var $reportByPostingDate = false;

    /**
     * @param array $_params
     */
    function __construct($_params = array()) {
        $_params = INTACCTarray_merge(
            array(	'report' => 'bankregister',
                      '2stage' => true,
                      'forceEntity' => true,
                      'reportslide' => true,
            ),
            $_params
        );
        parent::__construct($_params);
        $this->OverrideReportColumns();
        $this->params['NOREPORTLOCCHECK'] = true;
        if ( $this->mcpEnabled ) {
            $this->params['SHOWTRXCURRENCY'] = true;
        }
    }

    /**
     * @return bool
     */
    function DoQuery() {

        global $_userid;

        global $bStartDate, $bEndDate;

        $finAcctFilter		= "";
        $transferLinkFilter = "";
        $bankFilter			= "";
        $args = array();

        // defaulting params value for showdetail mode selector
        $this->params['SHOWDETAILS'] = ( $this->params['SHOWDETAILS'] == '' ? 'true' : $this->params['SHOWDETAILS'] );

        $this->params['RECORD#'] = Request::$r->_r;
        $this->params['RECORDTYPE'] = Request::$r->_rtype;

        if (!$this->params['RECORDTYPE']) {
            $this->params['RECORDTYPE'] = Request::$r->_recordtype;
        }

        $_r = $this->params['RECORD#'];
        $_rt = $this->params['RECORDTYPE'];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $params = $this->params;

        $enableMultiARPymtReport = FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('ENABLE_BANK_REGISTER_REPORT_FOR_MULTI_ARPYMT');

        $params		= $this->params;
        $params['ENTITY'] = !empty($params['ENTITY']) ? GetEntityValue('pp', $params['ENTITY']) : '';

        $acctID	= $params['ACCOUNTID'] ?? null;

        // startdate and the enddates are assigned to the local variables
        $bStartDate	= $this->startdate;
        $bEndDate	= $this->enddate;

        // Process 'sort by' filter,
        // for backward compatibility (memorized reports etc.),
        // we still may need to allow whitelisted SQL column names for 'sort by',
        // At least they dont show up on URL when running the report now,
        // Even if attempt to pass this over URL as XSS, its only whitelist values we allow now
        $sortMap = array(
            'Date' => 'p.whencreated',
            'Description' => 'p.description',
            'Transaction No' => 'lpad(trim(p.docnumber), 25, \' \')',
            'Debit Amount' => 'to_number(p.debit)',
            'Credit amount' => 'to_number(p.credit)',
            'Reconcile State' => 'p.cleared',
            'Date - Des' => 'p.whencreated desc',
            'Transaction No - Des' => 'lpad(trim(p.docnumber), 25, \' \') desc',
            'Debit Amount - Des' => 'to_number(p.debit) desc',
            'Credit amount - Des' => 'to_number(p.credit) desc',
            'whencreated' => 'p.whencreated', //whitelist starts,
            'description' => 'p.description',
            'lpad(trim(docnumber), 25, \' \')' => 'lpad(trim(p.docnumber), 25, \' \')',
            'to_number(debit)' => 'to_number(p.debit)',
            'to_number(credit)' => 'to_number(p.credit)',
            'cleared' => 'p.cleared',
            'whencreated desc' => 'p.whencreated desc',
            'lpad(trim(docnumber), 25, \' \') desc' => 'lpad(trim(p.docnumber), 25, \' \') desc',
            'to_number(debit) desc' => 'to_number(p.debit) desc',
            'to_number(credit) desc' => 'to_number(p.credit) desc', //whitelist ends,
        );

        $sortBy		= $params['SORTMODE']?explode('#~#',$params['SORTMODE']):array();

        // Translate 'sort by' UI filter to internal values,
        foreach ($sortBy as $key => $val) {
            $mappedVal = $sortMap[$val];
            if (!$mappedVal) {
                unset($sortBy[$key]);
            }
            else {
                $sortBy[$key] = $mappedVal;
            }
        }

        $sortBy		= count($sortBy)?join(',',$sortBy):'';

        $bankSortBy = $params['BANKSORTMODE'] ? :'';

        if ( !empty($bankSortBy) && !in_array($bankSortBy, ['name', 'accountid']) ){
            LogToFile("SQL Injection (possible) detected: Invalid 'banksortmode' column: '" . $bankSortBy . "'\n");
            // If bad sort data is provided, its likely an attack. Just give a generic response.
            popupErrorAndShutdown('BL03002133', __FILE__.":".__LINE__, "Invalid request. Please Retry.",
                                  null, false);
        }

        // SETUP ARGS ARRAY
        $args[0] = '';
        $args[1] = GetMyCompany();

        // RECORD# FILTER FOR DRILL DOWN FROM GL REPORT
        if ( $_r && $_rt ) {
            $record_filter = " where p.record = :2 and p.recordtype = :3";
            $args[2] = $_r;
            $args[3] = $_rt;
        } else {
            $record_filter = 'where 1=1 ';
        }

        $numArgs = count($args);

        $this->reportByPostingDate = ($params['REPORTBY'] == 'GL Posting Date')?true:false;
        $prbatchFilter = ' 1=1 ';
        $dateRangeFilter = '';

        // WHERE FILTERS
        if ( isset($bStartDate) && $bStartDate != '' ) {
            if (!$this->reportByPostingDate) {
                $dateRangeFilter = " and prrecord.whencreated >= :".$numArgs;
            }
            else {
                $prbatchFilter .= " and prbatch.created >= :".$numArgs;
            }
            //$dateRangeFilter = " and prbatch.created >= :".$numArgs;
            $glDateFilter = " and glentry.entry_date >= :".$numArgs++;
            $args[] = $bStartDate;
        }
        if ( isset($bEndDate) && $bEndDate != '' ) {
            if (!$this->reportByPostingDate) {
                $dateRangeFilter .= " and prrecord.whencreated <= :".$numArgs;
            }
            else {
                $prbatchFilter .= " and prbatch.created <= :".$numArgs;
            }
            //$dateRangeFilter .= " and prbatch.created <= :".$numArgs;
            /** @noinspection PhpUndefinedVariableInspection */
            $glDateFilter .= " and glentry.entry_date <= :" . $numArgs++;
            $args[] = $bEndDate;
        }

        if ( $acctID && $acctID != $this->_idSelectText ) {
            $finAcctFilter = " and prrecord.financialentity in ( select entity from financialaccount where cny# = :1 and ( entity = :".$numArgs." or linkentity = :".$numArgs." ) )";
            $transferLinkFilter = " and transferlink.toacct = :".$numArgs;
            /** @noinspection PhpUnusedLocalVariableInspection */
            $bankFilter = " and bank.accountid = :" . $numArgs++;
            $args[] = isl_trim(isl_htmlspecialchars($acctID));
        }

        $filters			= '';
        $multiPymtFilter    = '';

        $location_filter1	= '';
        $department_filter1 = '';

        $fromTables			= '';

        //Need to include prbatch table if records are pulled based on gl posting date
        $outSelects = '';
        $outTables = '';
        $outFilters = '';
        if ($this->reportByPostingDate) {
            if ($params['SHOWDETAILS'] == 'true') {
                $outSelects .= ', nvl(prbatch.created, p.whencreated) as glpostingdate';
            }
            $outTables .= ',prbatchmst prbatch';
            $outFilters .= "and p.cnyNo = prbatch.cny# (+)
						 and p.prbatchkey = prbatch.record# (+)
						 and (($prbatchFilter) or prbatch.created is null)";
        }
        $fundsTransferEntryLocFilter = '';

        if ($params['LOCATION']) {
            $locMgr = Globals::$g->gManagerFactory->getManager("location");
            $locs = array();

            foreach ($params['LOCATION'] as $loc) {
                list($loc_no) = explode("--", $loc);
                GetRecordNo($_userid, 'location', $loc_no, $locrec);
                $locs[] = $locrec;
            }

            $filters = " and exists (	
                                SELECT	1 
                                FROM	prentrymst prentry
                                WHERE	prentry.cny# = prrecord.cny#
                                        and prentry.recordkey = prrecord.record# and rownum = 1	
                        ";
            if ($enableMultiARPymtReport) {
                $multiPymtFilter = " AND EXISTS (
                                    SELECT	1 
                                        FROM	prentrymst prentry
                                    WHERE
                                        prentry.cny# = prrecord.cny# 
                                        AND ((prrecord.recordtype = 'rl'  
                                        AND prentry.recordkey in (SELECT RECORD# FROM prrecordmst prr WHERE prr.CNY# = prrecord.cny# AND prr.multientitypymtkey = prrecord.record#))  
                                        OR prentry.recordkey = prrecord.record# ) and rownum = 1 ";
            } else {
                $multiPymtFilter = $filters;
            }
            // Use the FilterLocations control to get the users selection to get the location list to filter by
            if ($params['FILTERTRANSACTIONS'] == 'CHILDREN') {

                $sublocs = $locMgr->getNestedDimensionKeys($locs, false, 'CHILDREN');
                if (empty($sublocs)) {
                    $sublocs[] = -999999;
                }

                $locInclauseFilter = PrepINClauseStmt('', $sublocs, ' and prentry.location#', false);
                $locInclauseFilter .= ") ";
                $filters .= $locInclauseFilter;
                $multiPymtFilter .= $locInclauseFilter;

                $location_filter1 .= PrepINClauseStmt('', $sublocs, ' and glentry.location#', false);

                //populate funds transfer location filters
                $locFilter = PrepINClauseStmt('', $sublocs, '  prentryct.location#', false);
                $baseLocFilter = PrepINClauseStmt('', $sublocs, '  prentryct.baselocation', false);
                $fundsTransferEntryLocFilter =  ' AND ( ' . $locFilter .' OR '. $baseLocFilter. ' ) ';
            } else if ($params['FILTERTRANSACTIONS'] == 'SELF') {

                $locInclauseFilter = PrepINClauseStmt('', $locs, ' and prentry.location#', false);
                $locInclauseFilter .= ") ";
                $filters .= $locInclauseFilter;
                $multiPymtFilter .= $locInclauseFilter;

                $location_filter1 .= PrepINClauseStmt('', $locs, ' and glentry.location#', false);
                //populate funds transfer location filters
                $locFilter = PrepINClauseStmt('', $locs, '  prentryct.location#', false);
                $baseLocFilter = PrepINClauseStmt('', $locs, '  prentryct.baselocation', false);
                $fundsTransferEntryLocFilter =  ' AND ( ' . $locFilter .' OR '. $baseLocFilter. ' ) ';
            } else {

                $sublocs = $locMgr->getNestedDimensionKeys($locs, false, 'ALL');

                // All locations
                $filters = " and exists (	
									SELECT	1 
									FROM	prrecordentitytotalmst prrecentitytotmst
									WHERE	prrecentitytotmst.cny# = prrecord.cny#
											and prrecentitytotmst.prrecordkey = prrecord.record# and rownum = 1	
							";

                $filters .= PrepINClauseStmt('', $locs, ' and prrecentitytotmst.locationkey', false);
                $filters .= ") ";
                $multiPymtFilter = $filters;

                $location_filter1 .= PrepINClauseStmt('', $sublocs, ' and glentry.location#', false);
                //populate funds transfer location filters
                $locFilter = PrepINClauseStmt('', $sublocs, '  prentryct.location#', false);
                $baseLocFilter = PrepINClauseStmt('', $sublocs, '  prentryct.baselocation', false);
                $fundsTransferEntryLocFilter =  ' AND ( ' . $locFilter .' OR '. $baseLocFilter. ' ) ';
            }
        } else if (IsMultiEntityCompany()) {
            $location_filter1 = " and (bank.location# is null";
            if (!GetContextLocation()) {
                $location_filter1 .= " or bank.locationkey is null";
            }
            else{
                $fundsTransferEntryLocFilter = ' AND ( prentryct.location# = ' . GetContextLocation() .
                     ' OR prentryct.baselocation = ' . GetContextLocation() . ') ';
            }

            $location_filter1 .= " or glentry.location# in (select distinct locationmst.record# from locationmst 
                where locationmst.cny# = :1 start with locationmst.record#=bank.location# and locationmst.cny# = :1 
                connect by prior locationmst.record#=locationmst.parentkey and locationmst.cny# = :1)) ";

        }

        //Funds transfer filter by location
        $fundsTransferFilter = " AND EXISTS (
                SELECT
                    1
                FROM
                    prentrymst prentryct
                WHERE
                        prentryct.cny# = prrecord.cny#
                    AND prentryct.recordkey = prrecord.record#
                    AND rownum = 1
                  $fundsTransferEntryLocFilter
            ) ";

        if ( !empty ($params['DEPARTMENT']) && count($params['DEPARTMENT']) ) {

            $depMgr = Globals::$g->gManagerFactory->getManager("department");
            $deptrecs = [];
            foreach($params['DEPARTMENT'] as $dept) {
                list($deptid) = explode("--", $dept);
                GetRecordNo($_userid, 'department', $deptid, $deptrec);
                $deptrecs[] = $deptrec;
            }

            $subdepts = $depMgr->getNestedDimensionKeys($deptrecs, false, 'ALL');

            $deptFilter = " and exists (	
								SELECT	1 
								FROM	prentrymst prentry
								WHERE	prentry.cny# = prrecord.cny#
										and prentry.recordkey = prrecord.record# and rownum = 1";

            $deptFilter .= PrepINClauseStmt('', $subdepts, ' and prentry.dept#', false);
            $deptFilter .=	") ";

            $filters .= $deptFilter;
            $multiPymtFilter .= $deptFilter;

            $department_filter1 .= PrepINClauseStmt('', $subdepts, ' and glentry.dept#', false);

        }

        //Void Check
        $showonlyvoided = ( $params['SHOWONLYVOIDED'] == 'true' ? " and prrecord.state = 'V' " : "" );

        //Reconcile State
        switch ($params['RECONSTATE']) {
            case 'T':
                $filters .= " and prrecord.cleared!='T' ";
                $multiPymtFilter .= " and prrecord.cleared!='T' ";
                $glReconFilter = " and glentry.cleared!='T' ";
                $fundsTransferFilter .= " and prrecord.cleared!='T' ";
                break;
            case 'C':
                $filters .= " and prrecord.cleared='T' ";
                $multiPymtFilter .= " and prrecord.cleared='T' ";
                $glReconFilter = " and glentry.cleared='T' ";
                $fundsTransferFilter .=  " and prrecord.cleared='T' ";
                break;
            default:
                $glReconFilter = "";
                break;
        }

        // ORDER BY
        $orderbyClause = '';
        if ($sortBy!='') {
            $sortBy = ($sortBy == 'whencreated')?' p.whencreated, p.record':$sortBy;
            $orderbyClause = " ORDER BY ".$sortBy;
        }

        $bookTable = "";
        $bookFilter = "";
        $currencySelect = " prrecord.currency, prrecord.basecurr, ";
        $glcurrencySelectgrp = ", glentry.currency, glentry.basecurr ";


        //Do not show user defined Books in Bank Register reports
        $bookArr = GetGlobalReportingBookVal();
        if ( is_array($bookArr) && count($bookArr) > 0 ) {
            $bookTable = ", gljournal, bookjournals bj";
            $bookFilter =
                " and glbatch.journal# = gljournal.record# and glbatch.cny# = gljournal.cny# and gljournal.cny# = bj.cny# and gljournal.record# = bj.journalkey and bj.bookid IN ('" . implode ('\',\'',  $bookArr) . "') ";
        }

        // GET THE QUERY BASED ON USER SELECTION OF EITHER SUMMARY OR DETAIL
        if ( $params['SHOWDETAILS'] == 'true' ) {
            /** @noinspection PhpUndefinedVariableInspection */
            $qry = "SELECT	p.* $outSelects
					FROM (
							SELECT	prrecord.record# record, prrecord.recordtype, prrecord.financialentity, $currencySelect
									decode(prrecord.description2, '', prrecord.entity, prrecord.description2) entity, 
									prrecord.whencreated, whenpaid, prrecord.recordid, docnumber,
									(case		when	prrecord.recordtype in ('rp', 'rl')
											then	to_char(totalentered)
											when	prrecord.recordtype in ('pp', 'pr', 'rf', 'ep', 'er', 'cw', 'ck', 'ch', 'cc', 'ci', 'cb')
											then	'' 
											when	prrecord.recordtype in ('rr', 'cn', 'cd', 'cq', 'cx', 'cr') 
											then	to_char(totalentered)
											else	'0'
									 end
									) as DEBIT,
                                    (case		when	prrecord.recordtype in ('rp', 'rl')
											then	to_char(trx_totalentered)
											when	prrecord.recordtype in ('pp', 'pr', 'rf', 'ep', 'er', 'cw', 'ck', 'ch', 'cc', 'ci',  'cb')
											then	'' 
											when	prrecord.recordtype in ('rr', 'cn', 'cd', 'cq', 'cx', 'cr') 
											then	to_char(trx_totalentered)
											else	'0'
									 end
									) as TRX_DEBIT,
									( case
											when	prrecord.recordtype in ('pp','ep')
											then	to_char(totalentered)
											when	prrecord.recordtype in ('pr', 'rf', 'er', 'cw', 'ck', 'ch', 'cc', 'ci', 'cb')
											then	to_char(totalentered)
											when	prrecord.recordtype in ('rp', 'rl', 'rr', 'cn', 'cd', 'cq', 'cx', 'cr') 
											then	''
											else	'0'
									 end
									) as CREDIT,
                                    ( case
											when	prrecord.recordtype in ('pp','ep')
											then	to_char(trx_totalentered)
											when	prrecord.recordtype in ('pr', 'rf', 'er', 'cw', 'ck', 'ch', 'cc', 'ci', 'cb')
											then	to_char(trx_totalentered)
											when	prrecord.recordtype in ('rp', 'rl', 'rr', 'cn', 'cd', 'cq', 'cx', 'cr') 
											then	''
											else	'0'
									 end
									) as TRX_CREDIT,
									( case	when	prrecord.recordtype in ('pp', 'pr') 
											then	decode(prrecord.description2, '', vendor.vendorid||' - '||vendor.name||
														decode(prrecord.description,'','',':'||prrecord.description), prrecord.description2) 
											when	prrecord.recordtype in ('ep', 'er') 
											then	employee.employeeid||' - '||employeecontact.name||decode(prrecord.description,'','',':'||prrecord.description) 
											when	prrecord.recordtype in ('rp', 'rr', 'rf') 
											then	decode(prrecord.description2, '', customer.customerid||' - '||customer.name||
														decode(prrecord.description,'','',':'||prrecord.description), prrecord.description2) 
									        when	prrecord.recordtype = 'rl'  
											then	prrecord.payername 
											when	prrecord.recordtype in ('cw', 'ck',  'cc', 'ci') 
											then	decode(prrecord.description2,'',vendor.vendorid||' - '||vendor.name||												
														decode(prrecord.description,'','',':'||prrecord.description),prrecord.description2)

											when	prrecord.recordtype in ( 'ch') 
											then decode(prrecord.description,'','Payment',prrecord.description)

											when prrecord.recordtype in ('cb', 'cr')
											then decode(prrecord.description,'',prrecord.description2, prrecord.description2||':'||prrecord.description)

											when prrecord.recordtype in ('cd')
											then decode(prrecord.description,'',prrecord.recordid, prrecord.recordid||':'||prrecord.description)

											when	prrecord.recordtype in ('cn', 'cq', 'cx') 
											then	decode(prrecord.description,'','Deposit',prrecord.description)
											else	prrecord.description
									 end
									) as DESCRIPTION, 
									decode(prrecord.cleared, 'T', to_char(prrecord.clrdate), 'Transit') cleared, 
									prrecord.state,
									prrecord.locationkey location, 
									( case	when substr(prrecord.entity, 1, 1) = 'C' then customer.name
											when substr(prrecord.entity, 1, 1) = 'E' then employee.title
											when substr(prrecord.entity, 1, 1) = 'V' then vendor.name
											else prrecord.entity
									 end
									) as name,(select entity_no from v_locationent where v_locationent.location# =  prrecord.locationkey and v_locationent.cny# = :1 ) as locationname,
									prrecord.prbatchkey as prbatchkey, prrecord.cny# cnyNo
							FROM	prrecord, customermst customer, employeemst employee, vendormst vendor, contact employeecontact, bankaccount bank $fromTables, prbatch prbatch
							WHERE	
									(CASE
									            WHEN ". ReconciliationUtils::getARPaymentQuery()." 
									            THEN 'T'
												WHEN prrecord.recordtype in ('cw', 'ck', 'ch', 'cc', 'cb', 'cn', 'cd', 'cq', 'cx', 'cr') 
												THEN 'T'
												WHEN (( prrecord.recordtype in ('pr', 'rf', 'ep', 'er') or ( prrecord.recordtype in ('pp') and ( prbatch.recordtype = 'ph' or prrecord.trx_totalentered <> 0))) and prrecord.state in('V', 'C')) 
												THEN 'T'
												WHEN ( prrecord.recordtype = 'ci' and not exists ( select 1 from ccchargerecords where cny# = :1 and chargekey = prrecord.record# ) ) 
												THEN 'T'
												ELSE 'F'
									END 
									) = 'T'
									and prrecord.cny# = :1 
									and customer.cny# (+) = prrecord.cny#
									and customer.entity (+) = prrecord.custentity
									and employee.cny# (+) = prrecord.cny#
									and employee.entity (+) = prrecord.empentity
									and employee.cny#  = employeecontact.cny# (+)
									and employee.contactkey  = employeecontact.record# (+)
									and vendor.cny# (+) = prrecord.cny#
									and vendor.entity (+) = prrecord.vendentity
									and bank.cny#(+) = prrecord.cny#
									and bank.accountid(+) = prrecord.financialentity
									and prbatch.cny# = prrecord.cny#
									and prbatch.record# = prrecord.prbatchkey
									$finAcctFilter $dateRangeFilter $multiPymtFilter $showonlyvoided
					UNION ALL				
					SELECT	prrecord.record# record, prrecord.recordtype, prrecord.financialentity, 
					         nvl(prentry.bankcurrency ,prrecord.currency) as currency,
					         nvl(prentry.bankbasecurrency , prrecord.basecurr) as basecurr,
					         decode(prrecord.description2, '', prrecord.entity, prrecord.description2) entity, 
									prrecord.whencreated, whenpaid, prrecord.recordid, docnumber,
									''  as DEBIT,
                                    ''  as TRX_DEBIT,
                                    nvl(to_char( prentry.bankbaseamount) ,to_char(totalentered) ) as CREDIT,
                                    nvl(to_char( prentry.banktrxamount) ,to_char(trx_totalentered) ) as TRX_CREDIT,
                        
									decode(prrecord.description, '', 'Payment', prrecord.description) as DESCRIPTION, 
									decode(prrecord.cleared, 'T', to_char(prrecord.clrdate), 'Transit') cleared, 
									prrecord.state,
									prrecord.locationkey location, 
									'' as name,(select entity_no from v_locationent where v_locationent.location# =  prrecord.locationkey and v_locationent.cny# = :1 ) as locationname,
									prrecord.prbatchkey as prbatchkey, prrecord.cny# cnyNo
							FROM	prrecordmst prrecord,  bankaccount bank,  prentrymst prentry $fromTables
							WHERE	
									prrecord.recordtype IN ('ct' , 'cp')
									and prrecord.cny# = :1 
									and bank.cny#(+) = prrecord.cny#
									and bank.accountid(+) = prrecord.financialentity
									and prentry.cny#(+) = prrecord.cny#
									and prentry.recordkey = prrecord.record# 
									and prentry.accountkey (+) = bank.glaccountkey
									$finAcctFilter $dateRangeFilter  $showonlyvoided $fundsTransferFilter
					UNION ALL 
							SELECT	prrecord.record# record, prrecord.recordtype, transferlink.toacct financialentity, $currencySelect prrecord.entity,
									prrecord.whencreated, whenpaid, prrecord.recordid, docnumber, 
									to_char(case when prrecord.recordtype in ('ci', 'cp') 
                                            then prrecord.totalentered
											else prrecord.totalpaid
                                            end
                                    ) as DEBIT,
                                    to_char(case when prrecord.recordtype in ('ci', 'cp') 
                                            then prrecord.trx_totalentered
											else prrecord.trx_totalpaid
                                            end
                                    ) as TRX_DEBIT,
                                    '' as CREDIT, 
                                    '' as TRX_CREDIT, 
									decode(prrecord.description,'','Transfer',prrecord.description) as DESCRIPTION, 
									decode(transferlink.cleared, 'T', to_char(transferlink.clrdate), 'Transit') cleared, 
									prrecord.state,prrecord.locationkey location, '' as name,(select entity_no from v_locationent where v_locationent.location# =  prrecord.locationkey and v_locationent.cny# = :1 ) as locationname,
									prrecord.prbatchkey as prbatchkey, prrecord.cny# cnyNo
							FROM	prrecordmst prrecord, transferlink, financialaccount financialentity $fromTables
							WHERE	
									prrecord.cny# = :1 
									and transferlink.cny# = prrecord.cny#
									and transferlink.paymentkey = prrecord.record#
                                    and financialentity.cny# = transferlink.cny#
									and financialentity.entity = transferlink.toacct
									$transferLinkFilter $dateRangeFilter  $showonlyvoided $fundsTransferFilter
					UNION ALL 
							SELECT	glentry.record# record, '' as recordtype, bank.accountid financialentity $glcurrencySelectgrp, '' as entity,
									glentry.entry_date as whencreated, glentry.entry_date as whenpaid, '' as recordid, 
									glentry.document as docnumber,
									decode(glentry.tr_type, 1, to_char(amount), '') as DEBIT, 
                                    decode(glentry.tr_type, 1, to_char(trx_amount), '') as TRX_DEBIT, 
									decode(glentry.tr_type, -1, to_char(amount), '') as CREDIT,
									decode(glentry.tr_type, -1, to_char(trx_amount), '') as TRX_CREDIT,
									glentry.description,
									decode(glentry.cleared, 'T', to_char(glentry.clrdate), 'Transit') cleared, 
									'' as state, glentry.location# location, '' as name,(select entity_no from v_locationent where v_locationent.location# =  glentry.location# and v_locationent.cny# = :1 ) as locationname,
									-1 as prbatchkey, glentry.cny# cnyNo
							FROM	glentry, glbatchmst glbatch, bankaccount bank $bookTable
							WHERE	
									glbatch.cny# = :1 
									and glbatch.modulekey = '2.GL' 
                                    and " . GLBatchManager::getPostStateQryFilter('glbatch', true, 'glentry') . "
									and glentry.cny# = glbatch.cny#
									and glentry.batch# = glbatch.record#
									". ReconciliationUtils::getZeroGLEntryQuery(false). "
									and bank.cny# = glentry.cny#
									and bank.glaccountkey = glentry.account#
									$bankFilter $glDateFilter $location_filter1 $department_filter1 $glReconFilter $bookFilter
					) p 
					$outTables $record_filter $outFilters $orderbyClause 
				";

        } else {
            /** @noinspection PhpUndefinedVariableInspection */
            $qry = "SELECT	p.* $outSelects
					FROM (
							SELECT	prrecord.record# record, prrecord.recordtype, prrecord.financialentity, $currencySelect
									(case	when	prrecord.recordtype in ('pp', 'pr', 'rf', 'ep', 'er', 'cw', 'ck', 'ch', 'cc', 'ci', 'cb')
											then	'' 
											when	prrecord.recordtype in ('rp', 'rl', 'rr', 'cn', 'cd', 'cq', 'cx', 'cr') 
											then	to_char(totalentered)
											else	'0'
									 end
									) as DEBIT,
                                    (case	when	prrecord.recordtype in ('pp', 'pr', 'rf', 'ep', 'er', 'cw', 'ck', 'ch', 'cc', 'ci', 'cb')
											then	'' 
											when	prrecord.recordtype in ('rp', 'rl', 'rr', 'cn', 'cd', 'cq', 'cx', 'cr') 
											then	to_char(trx_totalentered)
											else	'0'
									 end
									) as TRX_DEBIT,
									( case
											when	prrecord.recordtype in ('pp')
											then	to_char(totalentered)
											when	prrecord.recordtype in ('pr', 'rf', 'ep', 'er', 'cw', 'ck', 'ch', 'cc', 'ci', 'cb')
											then	to_char(totalentered)
											when	prrecord.recordtype in ('rp', 'rl', 'rr', 'cn', 'cd', 'cq', 'cx', 'cr') 
											then	''
											else	'0'
									 end
									) as CREDIT,
                                    ( case
											when	prrecord.recordtype in ('pp')
											then	to_char(trx_totalentered)
											when	prrecord.recordtype in ('pr', 'rf', 'ep', 'er', 'cw', 'ck', 'ch', 'cc', 'ci',  'cb')
											then	to_char(trx_totalentered)
											when	prrecord.recordtype in ('rp', 'rl', 'rr', 'cn', 'cd', 'cq', 'cx', 'cr') 
											then	''
											else	'0'
									 end
									) as TRX_CREDIT,
									prrecord.prbatchkey as prbatchkey, prrecord.cny# cnyNo
							FROM	prrecord, bankaccount bank $fromTables, prbatch prbatch
							WHERE	
									(CASE
							                    WHEN ". ReconciliationUtils::getARPaymentQuery(false)." 
									            THEN 'T'
												WHEN prrecord.recordtype in ('cw', 'ck', 'ch', 'cc',  'cb', 'cn', 'cd', 'cq', 'cx', 'cr') 
												THEN 'T'
												WHEN (( prrecord.recordtype in ('pr', 'rf', 'ep', 'er') or ( prrecord.recordtype in ('pp') and ( prbatch.recordtype = 'ph' or prrecord.trx_totalentered <> 0))) and prrecord.state in('V', 'C')) 
												THEN 'T'
												WHEN ( prrecord.recordtype = 'ci' and not exists ( select 1 from ccchargerecords where cny# = :1 and chargekey = prrecord.record# ) ) 
												THEN 'T'
												ELSE 'F'
									END 
									) = 'T'
									and prrecord.cny# = :1 
									and bank.cny#(+) = prrecord.cny#
									and bank.accountid(+) = prrecord.financialentity
									and prbatch.cny# = prrecord.cny#
									and prbatch.record# = prrecord.prbatchkey
									$finAcctFilter $dateRangeFilter $multiPymtFilter $showonlyvoided
					UNION ALL
					 SELECT	prrecord.record# record, prrecord.recordtype, prrecord.financialentity, 
					        nvl(prentry.bankcurrency ,prrecord.currency) as currency,
					         nvl(prentry.bankbasecurrency , prrecord.basecurr) as basecurr,
					
									'' as DEBIT,
                                    '' as TRX_DEBIT,
                                    nvl(to_char( prentry.bankbaseamount) ,to_char(totalentered) ) as CREDIT,
                                    nvl(to_char( prentry.banktrxamount) ,to_char(trx_totalentered) ) as TRX_CREDIT,
									prrecord.prbatchkey as prbatchkey, prrecord.cny# cnyNo
							FROM	prrecordmst prrecord, bankaccount bank , prentrymst prentry $fromTables
							WHERE	
									prrecord.recordtype IN ('ct' , 'cp')
									and prrecord.cny# = :1 
									and bank.cny#(+) = prrecord.cny#
									and bank.accountid(+) = prrecord.financialentity
									and prentry.cny#(+) = prrecord.cny#
									and prentry.recordkey = prrecord.record# 
									and prentry.accountkey (+) = bank.glaccountkey
									$finAcctFilter $dateRangeFilter  $showonlyvoided    $fundsTransferFilter   				
					UNION ALL 
							SELECT	prrecord.record# record, prrecord.recordtype, transferlink.toacct financialentity, $currencySelect
									to_char(case when prrecord.recordtype in ('ci', 'cp') 
                                        then prrecord.totalentered 
                                        else prrecord.totalpaid end
                                    ) as DEBIT, 
                                    to_char(case when prrecord.recordtype in ('ci', 'cp') 
                                        then prrecord.trx_totalentered 
                                        else prrecord.trx_totalpaid end
                                    ) as TRX_DEBIT, 
                                    '' as CREDIT,
                                    '' as TRX_CREDIT,
									prrecord.prbatchkey as prbatchkey, prrecord.cny# cnyNo
							FROM	prrecordmst prrecord, transferlink $fromTables
							WHERE	
									prrecord.cny# = :1 
									and transferlink.cny# = prrecord.cny#
									and transferlink.paymentkey = prrecord.record#
									$transferLinkFilter $dateRangeFilter  $showonlyvoided $fundsTransferFilter
					UNION ALL 
							SELECT	glentry.record# record, '' as recordtype, bank.accountid financialentity $glcurrencySelectgrp,
							to_char(sum(decode(glentry.tr_type, 1, to_char(amount), ''))) as DEBIT, 
                            to_char(sum(decode(glentry.tr_type, 1, to_char(trx_amount), ''))) as TRX_DEBIT, 
							to_char(sum(decode(glentry.tr_type, -1, to_char(amount), ''))) as CREDIT,
							to_char(sum(decode(glentry.tr_type, -1, to_char(trx_amount), ''))) as TRX_CREDIT,
							-1 as prbatchkey, glentry.cny# cnyNo 
							FROM	glentry, glbatchmst glbatch, bankaccount bank $bookTable
							WHERE	
									glbatch.cny# = :1 
									and glbatch.modulekey = '2.GL' 
									and glentry.cny# = glbatch.cny#
									and glentry.batch# = glbatch.record#
									". ReconciliationUtils::getZeroGLEntryQuery(false). "
                                    and " . GLBatchManager::getPostStateQryFilter('glbatch', true, 'glentry') . "
									and bank.cny# = glentry.cny#
									and bank.glaccountkey = glentry.account#
									$bankFilter $glDateFilter $location_filter1 $department_filter1 $glReconFilter $bookFilter
									group by glentry.record#,bank.accountid $glcurrencySelectgrp, prbatchkey, glentry.cny#
					) p 
					$outTables $record_filter $outFilters
				";

        }

        $args[0] = $qry;
        $this->_lastQResult = QueryResult($args);

        // DEBIT CARD TRANSACTIONS BELONG TO THIER ASSOCIATED CHECKING ACCOUNT
        $this->MapDebitTransactionsToBanks();

        $args = array();
        $mcpfld = "";
        if ($this->mcpEnabled) {
            $mcpfld = "nvl(currency, '".GetBaseCurrency()."')";
        }
        $args[0] =  "SELECT	accountid, name, accountid||' - '||name account, accountno, type, $mcpfld currency,
							glaccountkey, 'bankaccount' as tablename, locationkey as location
					FROM	bankaccount WHERE	cny# = :1 ";

        $args[1] = GetMyCompany();

        if ( $acctID && $acctID != $this->_idSelectText && !$_r ) {
            $args[0] .= " and bankaccount.accountid = :2 ";
            $args[2] = isl_trim(isl_htmlspecialchars($acctID));
        }

        if ( $_r && $this->params['RECORDTYPE'] ) {
            $args[3] = $_r;
        }

            //Sorting Bank Info
        if ($bankSortBy!='') {
            $args[0] .= " ORDER BY ".$bankSortBy;
        }

        $this->_accounts = QueryResult($args);
        return true;
    }

    /**
     * @return array
     */
    function DoMap() {
        // Decrypt Account No.
        foreach ($this->_accounts as $key => $value ) {
            $this->_accounts[$key]['ACCOUNTNO'] = TwoWayDecrypt($value['ACCOUNTNO']);
        }

        return parent::DoMap();
    }

    /**
     * @param array $params
     *
     * @return array
     */
    function DrawHeader($params)
    {
        global $gElementMap,  $bStartDate, $bEndDate;
        $_op = Request::$r->_op;
        $_sess = Session::getKey();
        $_batch = Request::$r->_batch;
        $_r = Request::$r->_r;
        $_done = Request::$r->_done;
        $entity = Request::$r->entity;
        $accountId = $this->params['ACCOUNTID'] ?? '';
        $bStartDate = FormatDateForDisplay($bStartDate);
        $bEndDate = FormatDateForDisplay($bEndDate);

        if( $params['type'] == kShowHTML ){
            /** @noinspection PhpUndefinedVariableInspection */
            $backhere = "reportor.phtml?.op=$_op" . "&.sess=$_sess&.type=" . isl_trim($this->params['type'])
                        . "&_obj__ACCOUNTID="
                        . isl_trim(isl_htmlspecialchars(URLCleanParams::insert('_obj__ACCOUNTID', $accountId)))
                        . "&_obj__START_DATE="
                        . isl_trim(isl_htmlspecialchars(URLCleanParams::insert('_obj__START_DATE', $bStartDate)))
                        . "&_obj__END_DATE="
                        . isl_trim(isl_htmlspecialchars(URLCleanParams::insert('_obj__END_DATE', $bEndDate)));
            if ($_batch) {
                $backhere .= "&_batch=$_batch"; }
            if ($_r) {
                $backhere .= "&_r=$_r"; }
            $backhere = ExtendUrl($backhere, OptDone($_done));
            $op1 = GetOperationId("co/lists/bankaccount/view");
            $op2 = ($entity =='employee' ? GetOperationId("co/lists/employee/view") : GetOperationId("ap/lists/vendor/view") );

            $opScript = $gElementMap[$op1]['script'] ?? '';
            $code = ($entity == 'employee' ? "'eecheckregister.phtml', 'eecheckregister.phtml'"
                : "'checkregister.phtml', 'checkregister.phtml'" );
            $lines['report']["0"]['sess']		= $_sess;
            $lines['report']["0"]['backhere']	= $backhere;
            $lines['report']["0"]['op']			= $_op;
            $lines['report']["0"]['ops']		= "$op1, $op2, $_op, $_op";
            $lines['report']["0"]['scripts']	= "'" . $opScript . "', 'editor.phtml', " . $code;
        }
        $lines['report']["0"]['ismegl'] = (IsMultiEntityCompany())? 'Y' : 'N';
        $lines['report']["0"]['locationcontext'] = (GetContextLocation())?: '';
        $lines['report']["0"]['companyid'] = GetMyCompany();
        if($lines['report']["0"]['ismegl'] == 'Y' && $lines['report']["0"]['locationcontext'] == '' 	&& 		$params['SHOWDETAILS'] != 'false'){
            $lines['report']["0"]['showentitycol'] = "true";
        }
        $lines['report']["0"]['today'] = GetCurrentDate(IADATE_USRFORMAT) . "  " . date('H:i:s', time());
        $lines['report']["0"]['nolinks'] = ($params['type'] == kShowExcel) ? 1 : 0;
        $lines['report']["0"]['title']	= $this->title;
        $lines['report']["0"]['title2']	= $this->title2;
        $lines['report']["0"]['titlecomment']	= $this->titlecomment;
        $lines['report']["0"]['actypelabel'] = "Bank";
        $lines['report']["0"]['mode']	= 'summary';
        $lines['report']["0"]['co']		= GetMyCompanyName();
        $lines['report']["0"]['done'] = $_done;
        $lines['report']["0"]['START_DATE']	= $bStartDate;
        $lines['report']["0"]['END_DATE']	= $bEndDate;
        $lines['report']["0"]['orientation'] = ($params['ORIENTATION'])?: 'Portrait';
        $lines['report']["0"]['reportdate']     = GetCurrentDate(IADATE_USRFORMAT);
        if(Profile::getUserCacheProperty('USERPREF', 'TIMEFORMAT') == 'HH24:MI:SS') {
            $lines['report']["0"]['reporttime'] = date("G:i T");
        } else {
            $lines['report']["0"]['reporttime'] = date("g:i A T");
        }
        $lines['report']["0"]['SHOWRECONSTATUS'] = ($params['SHOWRECONSTATUS']!='true')?"false":"true";
        $lines['report']["0"]['header_col1'] = ($this->params['type'] == '_pdf') ? 'IA.TRANS_DATE' : 'IA.TRANSACTION_DATE' ;
        $lines['report']["0"]['header_col2'] = 'IA.DESCRIPTION';
        $lines['report']["0"]['header_col3'] = ($this->params['type'] == '_pdf') ? 'IA.TRANS_NO' : 'IA.TRANSACTION_NUMBER' ;
        $lines['report']["0"]['header_col10'] = 'IA.TXN_DEBIT';
        $lines['report']["0"]['header_col4'] ='IA.DEBIT';
        $lines['report']["0"]['header_col11'] = 'IA.TXN_CREDIT';
        $lines['report']["0"]['header_col5'] = 'IA.CREDIT';
        $lines['report']["0"]['header_col6'] = 'IA.CLEARED';
        $lines['report']["0"]['header_col7'] = 'IA.BALANCE';
        $lines['report']["0"]['header_col8'] = 'IA.GL_POSTING_DATE';
        if($lines['report']["0"]['showentitycol'] == 'true'){
            $lines['report']["0"]['header_col9'] = 'IA.OWNER_ENTITY';
        }
        $lines['report']["0"]['show_detail'] = $this->params['SHOWDETAILS'];
        $lines['report']['0']['location'] = $this->_currloc;
        $lines['report']['0']['department'] = $this->_currdept;
        $lines['report']['0']['reportByPostingDate'] = $this->reportByPostingDate?'true':'false';
        $lines['report']['0']['report_format'] = $this->params['type'];
        $lines['report']["0"]['accttype'] = 'ba';

        if($this->mcpEnabled) {
            $lines['report']['0']['MULTICURRENCY'] = 'true';
        }
        return $lines;

    }

    function OverrideReportColumns() {
        // ONLY PUT DATABASE FIELD NAMES OR METHOD NAMES AS THE VALUES
        $this->_xmlLeaf_DbFieldMap = array(
            'COLUMN2'	=> 'DESCRIPTION',
            'COLUMN3'	=> 'DOCNUMBER',
            'VOIDNAME'	=> 'DoSpecialFieldProcessing',
            'VOID_OP'	=> 'DoSpecialFieldProcessing'
        );
    }

    // DO NOT CHANGE THE SIGNATURE OF THIS METHOD

    /**
     * @param string $xmlLeaf
     * @param array $currentRecord
     *
     * @return int|string
     */
    function DoSpecialFieldProcessing($xmlLeaf, $currentRecord)
    {
        $value = "";
        $doingVoid = ($currentRecord['CLEARED'] != "Cleared"
                      && $currentRecord['STATE'] != "V"
                      && $currentRecord['RECORDTYPE'] != "");

        switch ($xmlLeaf) {
            case 'VOIDNAME':
                $voidableRecTypes = array(
                    PRRECORD_TYPE_PAYMENT, PRRECORD_TYPE_REIMBURSEMENT, CM_PQCHECK_RECTYPE,CM_PMCHECK_RECTYPE,
                    PRRECORD_TYPE_APADVANCE, PRRECORD_TYPE_EEADVANCE, CM_BANKCHRG_RECTYPE, CM_CCFEES_RECTYPE,
                    CM_CCTRANS_RECTYPE, CM_TRANSFUNDS_RECTYPE, CM_DEBITPAY_RECTYPE, CM_PAYCC_RECTYPE,
                    CM_OTHERRECEIPT_RECTYPE, CM_DEPSLIP_RECTYPE, CM_BANKINT_RECTYPE, CM_QUICKINV_RECTYPE,
                    CM_CHARGEPAYOFF_RECTYPE, PRRECORD_TYPE_RECEIPT, PRRECORD_TYPE_ARADVANCE,
                    PRRECORD_TYPE_RECEIPT_MULTI_ENTITY, PRRECORD_TYPE_REFUND,
                );
                $module = Request::$r->_mod;
                if($doingVoid) {
                    if($this->VoidReversePermCheck($module, $currentRecord['RECORDTYPE'], $voidableRecTypes, "Void")){
                        $value = "IA.VOID";
                    } else if($this->VoidReversePermCheck($module, $currentRecord['RECORDTYPE'], $voidableRecTypes, "Reverse")){
                        $value = "IA.REVERSE";
                    }
                } else {
                    $value = "";
                }
                break;
            case 'VOID_OP':
                if ($doingVoid) {
                    $module = Request::$r->_mod;
                    $value = GetOperationId("$module/reports/checkregister");
                }
                break;
            default:
                break;
        }

        return $value;
    }

    function MapDebitTransactionsToBanks() {
        $this->_transactions = $this->_lastQResult;
        $finAcctMgr = Globals::$g->gManagerFactory->getManager('financialaccount');
        $finAcctMgr->GetLinkEntityMap($parentEntityMap);
        if (is_array($this->_transactions)) {
            foreach ($this->_transactions as $index => $transRec) {
                if ($transRec['CLEARED'] != 'Transit') {
                    $this->_transactions[$index]['CLEARED'] = FormatDateForDisplay($transRec['CLEARED']);
                }

                if ( ! empty($parentEntityMap[$transRec['FINANCIALENTITY']]) ) {
                    $this->_transactions[$index]['FINANCIALENTITY'] = $parentEntityMap[$transRec['FINANCIALENTITY']];
                }
            }
        }
    }

    /**
     * @param string $mod
     * @param string $rectype
     * @param array $voidReveRecTypeArr
     * @param string $checkType
     *
     * @return bool
     */
    function VoidReversePermCheck(/** @noinspection PhpUnusedParameterInspection */ $mod, $rectype, $voidReveRecTypeArr, $checkType)
    {
        /*
        ..........................Permission checking for Void link.........................
            pr - AP Posted Advances 'ap/lists/appostedadvance/void'
            pp - AP Posted Payments 'ap/lists/appostedpayment/void'
            ck - CM Posted Payments 'ap/lists/appostedpayment/void'
            cw - CM Posted Payments 'ap/lists/appostedpayment/void'
            ep - EE Posted Reimbursements 'ee/lists/eppostedpayment/void'
            er - EE Posted Advances 'ee/lists/eepostedadvance/void'
        ..........................Permission checking for Reverse link......................
            ch - Bank Charges 'cm/lists/bankfee/reverse'
            cn - Bank Interest 'cm/lists/bankfee/reverse'
            ct - Funds Transfer 'cm/lists/fundstransfer/reverse'
            cb - Credit card debit payment 'cm/lists/cctransaction/reverse'
            cp - Credit Card Payment 'cm/lists/cctransaction/reverse'
            ci - Credit Card Change 'cm/lists/cctransaction/reverse'
            cd - Deposits 'cm/lists/deposit/reverse' or 'ar/lists/deposit/reverse'
            cc - Credit Card Charges and Other Fees 'cm/lists/creditcardfee/reverse'
            cr - Other Receipts 'cm/lists/otherreceipts/reverse'
            rp - AR Posted Payments(AR Receipts) 'ar/activities/arpaymentbatch/view'
            rr - AR Posted Advance(AR Advance) 'ar/activities/arpaymentbatch/view'
        */
        if(in_array($rectype, $voidReveRecTypeArr)) {
            if($checkType == "Void") {
                switch($rectype) {
                    case "pp":
                    case "ck":
                    case "cw":
                        $void_chkgPerm = CheckAuthorization(GetOperationId('ap/lists/appostedpayment/void'), 1);
                        break;
                    case "pr":
                        $void_chkgPerm = CheckAuthorization(GetOperationId('ap/lists/appostedadvance/void'), 1);
                        break;
                    case "ep":
                        $void_chkgPerm = CheckAuthorization(GetOperationId('ee/lists/eppostedpayment/void'), 1);
                        break;
                    case "er":
                        $void_chkgPerm = CheckAuthorization(GetOperationId('ee/lists/eepostedadvance/void'), 1);
                        break;
                    case PRRECORD_TYPE_REFUND:
                        $void_chkgPerm = CheckAuthorization(GetOperationId('ar/lists/customerrefund/void'), 1);
                        break;
                    default: // do nothing;
                        $void_chkgPerm = false;
                        break;
                }
                return $void_chkgPerm;
            } else if($checkType == "Reverse") {
                switch($rectype) {
                    case "ct":
                        $rev_chkgPerm = CheckAuthorization(GetOperationId('cm/lists/fundstransfer/reverse'), 1);
                        break;
                    case "cd":
                        $rev_chkgPerm = CheckAuthorization(GetOperationId('cm/lists/deposit/reverse'), 1);
                        if(!$rev_chkgPerm) {
                            $rev_chkgPerm = CheckAuthorization(GetOperationId('ar/lists/deposit/reverse'), 1);
                        }
                        break;
                    case "ch":
                    case "cn":
                        $rev_chkgPerm = CheckAuthorization(GetOperationId('cm/lists/bankfee/reverse'), 1);
                        break;
                    case "cc":
                        $rev_chkgPerm = CheckAuthorization(GetOperationId('cm/lists/creditcardfee/reverse'), 1);
                        break;
                    case "cr":
                        $rev_chkgPerm = CheckAuthorization(GetOperationId('cm/lists/otherreceipts/reverse'), 1);
                        break;
                    case "rp":
                    case "rl":
                        $rev_chkgPerm = CheckAuthorization(GetOperationId('ar/activities/arpaymentbatch/view'), 1);
                        break;
                    case "rr":
                        $rev_chkgPerm = CheckAuthorization(GetOperationId('ar/activities/arpaymentbatch/view'), 1);
                        break;
                    case "cb":
                    case "cp":
                    case "ci":
                        $rev_chkgPerm = CheckAuthorization(GetOperationId('cm/lists/cctransaction/reverse'), 1);
                        break;
                    default: // do nothing;
                        $rev_chkgPerm = false;
                        break;
                }
                return $rev_chkgPerm;
            }
        }
        return false;

    }
    
    /**
     * Replaces location validation in Reporter::ValidateParams
     *
     * @param array    $params
     *
     *
     * @return bool
     */
    public function affectAndValidateLocation( /** @noinspection PhpUnusedParameterInspection */&$params): bool
    {
        return true;
    }

}


