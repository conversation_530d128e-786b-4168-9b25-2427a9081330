<floatingPage modal="true" compact="true">
    <events>
        <close>closeMapAccountsPage(false)</close>
    </events>
    <title>IA.MAP_ACCOUNTS</title>
    <id>MAPACCOUNTSPAGE</id>
    <path>MAPACCOUNTSPAGE</path>
    <pages>
        <page>
            <section>
                <grid noDragDrop="true">
                    <path>MAPACCOUNTS</path>
                    <column>
                        <field label='IA.INTACCT_UNCONNECTED_ACCOUNT'>
                            <path>INTACCTACCOUNT</path>
                            <type>
                                <type>text</type>
                                <ptype>enum</ptype>
                                <size>120</size>
                            </type>
                            <default></default>
                        </field>
                    </column>
                    <column>
                        <field label='IA.BANKING_CLOUD_ACCOUNT'>
                            <path>SBCACCOUNT</path>
                            <type>
                                <type>text</type>
                                <ptype>enum</ptype>
                                <size>120</size>
                            </type>
                            <default></default>
                            <autofillrelated>false</autofillrelated>
                        </field>
                    </column>
                    <column>
                        <field label='IA.TRANSACTION_START_DATE'>
                            <path>TXNSTARTDATE</path>
                            <type>
                                <type>date</type>
                                <ptype>date</ptype>
                            </type>
                        </field>
                    </column>
                </grid>
            </section>
        </page>
    </pages>
    <footer>
        <button>
            <name>IA.MAP</name>
            <events>
                <click>closeMapAccountsPage(true);</click>
            </events>
        </button>
        <button>
            <name>IA.CANCEL</name>
            <events>
                <click>closeMapAccountsPage(false);</click>
            </events>
        </button>
    </footer>
</floatingPage>
