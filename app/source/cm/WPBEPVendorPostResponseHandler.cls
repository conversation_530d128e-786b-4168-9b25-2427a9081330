<?php

/**
 * Response handler class for Provider vendor POST request
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/**
 * Class WPBEPVendorPostResponseHandler
 */
class WPBEPVendorPostResponseHandler implements IaSbcAsyncResponseHandlerInterface
{

    const HTTP_POST_SUCCESS_CODE = 202;
    const STATUS_REQUEST_RECEIVED = 'Received';
    const STATUS_REQUEST_AWAIT_AUTH = 'AwaitingAuthorisation';

    /** @var int $providerVendorRecordNo */
    private $providerVendorRecordNo;

    /** @var string $requestPayload */
    private $requestPayload;

    /** @var string $vendorId */
    private $vendorId;

    /**
     * WPBEPVendorPostResponseHandler constructor.
     *
     * @param int    $providerVendorRecordNo
     * @param string $vendorId
     * @param string $requestPayload
     */
    public function __construct(int $providerVendorRecordNo, string $vendorId, string $requestPayload)
    {
        $this->providerVendorRecordNo = $providerVendorRecordNo;
        $this->requestPayload = $requestPayload;
        $this->vendorId = $vendorId;
    }

    /**
     * @inheritDoc
     */
    public function complete(SbcHttpResult $result, int $currentIteration)
    {
        logToFileWarning(" WPB-Log: file="
                         . __FILE__
                         . ' method=' . __FUNCTION__
                         . " httpStatusCode="
                         . $result->getStatusCode()
                         . " response: " . $result->getResponse());
        //$responseObj = json_decode($result->getResponse());
        $providerVendorMgr = Globals::$g->gManagerFactory->getManager('providervendor');
        //$status = '';
        if ( $result->getStatusCode() == self::HTTP_POST_SUCCESS_CODE ) {
            $providerState = ProvidervendorManager::STATE_RECIEVED;
        } else {
            $providerState = ProvidervendorManager::STATE_FAILED;
        }

        $opssetup = Globals::$g->gManagerFactory->getManager('opssetup');
        $logType = ActionLogManager::LOG_TYPE_SUCCESS;
        $description = 'Creditor Subscription';
        $logMessage = 'Request : ' . $this->requestPayload . ' Response : ' . $result->getResponse();

        if ( $providerState == ProvidervendorManager::STATE_FAILED ) {
            $logType = ActionLogManager::LOG_TYPE_FAILURE;
        }

        if ( ! $opssetup->storeActionLog($logType, $description, $logMessage,
                                         ActionLogManager::OBJ_TYPE_PROVIDER_VENDOR, $this->providerVendorRecordNo,
                                         $this->vendorId) ) {
            LogToFile("WPB POST Creditors subscription API failure. API Response - " . $result->getResponse());

            throw new IAException("Update provider vendor status failed.", 'EP-0108');
        }
        $hashedValues = ProvidervendorManager::getHashedAPIValues($this->requestPayload);
        $providerVendorMgr->updateState($this->providerVendorRecordNo, $providerState, $hashedValues);
    }
}