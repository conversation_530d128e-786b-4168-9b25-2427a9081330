<?php

/**
 * Entity for the Bank Fee object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */
$kSchemas['bankfee'] = array(
    'children' => array(
        'bankaccount' => array(
            'fkey' => 'financialentity', 'invfkey' => 'accountid', 'join' => 'outer', 'table' => 'bankaccountmst'
        ),
        'voidpayment' => array(
            'fkey' => 'record#', 'invfkey' => 'paymentkey', 'table' => 'voidlink', 'join' => 'outer',
            'children' => array(
                'reversal' => array(
                    'fkey' => 'voidpaymentkey', 'invfkey' => 'record#', 'table' => 'prrecordmst', 'join' => 'outer',
                )
            )
        ),
        'revvoidpayment' => array(
            'fkey' => 'record#', 'invfkey' => 'voidpaymentkey', 'table' => 'voidlink', 'join' => 'outer',
            'children' => array(
                'reversed' => array(
                    'fkey' => 'paymentkey', 'invfkey' => 'record#', 'table' => 'prrecordmst', 'join' => 'outer',
                )
            )
        ),
        'exchangerateinfo' => array(
            'fkey' => 'record#', 'invfkey' => 'recordkey', 'table' => 'exchangerateinfo', 'join' => 'outer'
        ),
        'taxsolution' =>    array (
            'fkey' => 'taxsolutionkey',
            'invfkey'    => 'record#',
            'table' => 'taxsolution',
            'join' => 'outer',
        ),
        'supdocmaps' => array(
            'fkey' => 'record#',
            'invfkey' => 'recordid',
            'fkeyconvto' => 'char',
            'join' => 'outer',
            'table' => 'supdocmaps',
            'filter' => " supdocmaps.transactiontype(+) = 'BANKFEE' ",
            'children' => array(
                'supdoc' => array(
                    'fkey' => 'documentid',
                    'invfkey' => 'record#',
                    'table' => 'supdoc',
                    'join' => 'outer'
                )
            )
        ),
    ),
    'object' => array(
        'RECORDNO',
        'FINANCIALENTITY',
        'BANKACCOUNTCURR',
        'WHENCREATED',
        'RECORDID',
        'RECORDTYPE',
        'TRANSACTIONTYPE', // For UI and lister
        'DESCRIPTION',
        'PRBATCHKEY',
        'BASECURR',
        'CURRENCY',
        'EXCH_RATE_DATE',
        'EXCH_RATE_TYPE_ID',
        'EXCHANGE_RATE',
        'TOTALENTERED',
        'TRX_TOTALENTERED',
        'STATE',
        'RAWSTATE',
        'CLEARED',
        'REVERSALKEY',
        'REVERSALDATE',
        'REVERSEDKEY',
        'REVERSEDDATE',
        'AUWHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'INCLUSIVETAX',
        'TAXSOLUTIONKEY',
        'TAXSOLUTIONID',
        'SHOWMULTILINETAX',
        'TAXMETHOD',
        'SUPDOCID',
        'SUPDOCKEY',
        'PARENTPAYMENT'
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'FINANCIALENTITY' => 'financialentity',
        'BANKACCOUNTCURR' => 'bankaccount.currency',
        'WHENCREATED' => 'whencreated',
        'RECORDID' => 'recordid',
        'RECORDTYPE' => 'recordtype',
        'TRANSACTIONTYPE' => 'recordtype', // For UI and lister
        'DESCRIPTION' => 'description',
        'PRBATCHKEY' => 'prbatchkey',
        'BASECURR' => 'basecurr',
        'CURRENCY' => 'currency',
        'EXCH_RATE_DATE' => 'exchangerateinfo.exch_rate_date',
        'EXCH_RATE_TYPE_ID' => 'exchangerateinfo.exch_rate_type_id',
        'EXCHANGE_RATE' => 'exchangerateinfo.exchange_rate',
        'TOTALENTERED' => 'totalentered',
        'TRX_TOTALENTERED' => 'trx_totalentered',
        'REVERSALKEY' => 'voidpayment.voidpaymentkey',
        'REVERSALDATE' => 'reversal.whencreated',
        'REVERSEDKEY' => 'revvoidpayment.paymentkey',
        'REVERSEDDATE' => 'reversed.whencreated',
        'STATE' => 'state',
        'RAWSTATE' => 'state',
        'CLEARED' => 'cleared',
        'AUWHENCREATED' => 'auwhencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'INCLUSIVETAX'      => 'inclusivetax',
        'TAXSOLUTIONKEY' => 'taxsolutionkey',
        'TAXSOLUTIONID' => 'taxsolution.solutionid',
        'SHOWMULTILINETAX' => 'taxsolution.showmultilinetax',
        'TAXMETHOD'     => 'taxsolution.taxmethod',
        'SUPDOCID' => 'supdoc.documentid',
        'SUPDOCKEY' => 'supdoc.record#',
        'PARENTPAYMENT' => 'parentpayment',
        'SI_UUID'     => 'si_uuid',
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'AUWHENCREATED',
        'WHENMODIFIED'
    ),
    'publish' => array(
        'RECORDNO',
        'FINANCIALENTITY',
        'WHENCREATED',
        'RECORDID',
        'TRANSACTIONTYPE',
        'DESCRIPTION',
        'BASECURR',
        'CURRENCY',
        'EXCH_RATE_DATE',
        'EXCH_RATE_TYPE_ID',
        'EXCHANGE_RATE',
        'TOTALENTERED',
        'TRX_TOTALENTERED',
        'STATE',
        'CLEARED',
        'AUWHENCREATED',
        'WHENMODIFIED',
        'INCLUSIVETAX',
        'TAXSOLUTIONID'
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'RECORDKEY',
            'invfkey' => 'RECORDNO',
            'minLinesRequired' => 1,
            'entity' => 'bankfeeentry',
            'path' => 'ITEMS'
        )
    ),
    'fieldinfo' => array(
        $gRecordNoHiddenFieldInfo,
        array(
            'path' => 'FINANCIALENTITY',
            'fullname' => 'IA.BANK_ACCOUNT',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'bankaccount',
                'pickentity' => 'bankaccountpick',
                'filterForCurrency' => true,
                'pick_url' => 'picker.phtml?.filterForCurrency=1',
                'pickfield' => array(
                    'BANKACCOUNTID', 'BANKNAME', 'DEPARTMENTID', 'LOCATIONID', 'CURRENCY',
                    'SERVICECHARGEGLACCOUNT', 'SERVICECHARGEACCOUNTLABEL', 'INTERESTEARNEDGLACCOUNT',
                    'INTERESTEARNEDACCOUNTLABEL', 'LOCATIONCURRENCY'
                )
            ),
            'required' => true,
            'id' => 1
        ),
        array(
            'path' => 'BANKACCOUNTCURR',
            'fullname' => 'IA.BANK_ACCOUNT_CURRENCY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies'
            ),
            'hidden' => true,
            'id' => 2
        ),
        array(
            'path' => 'WHENCREATED',
            'fullname' => 'IA.DATE',
            'type' => $gDateType,
            'required' => true,
            'id' => 3
        ),
        array(
            'path' => 'RECORDID',
            'fullname' => 'IA.REFERENCE_NUMBER',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 45
            ),
            'id' => 4
        ),
        array(
            'path' => 'RECORDTYPE',
            'fullname' => 'IA.RECORD_TYPE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 2
            ),
            'hidden' => true,
            'id' => 5
        ),
        array(
            'path' => 'DESCRIPTION',
            'fullname' => 'IA.DESCRIPTION',
            'type' => array(
                'type' => 'text',
                'ptype' => 'textarea',
                'maxlength' => 1000
            ),
            'id' => 6
        ),
        array(
            'path' => 'PRBATCHKEY',
            'fullname' => 'IA.PRBATCH_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'hidden' => true,
            'id' => 7
        ),
        array(
            'path' => 'BASECURR',
            'fullname' => 'IA.BASE_CURRENCY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text'
            ),
            'required' => true,
            'readonly' => true,
            'hidden' => true,
            'id' => 8
        ),
        array(
            'path' => 'CURRENCY',
            'fullname' => 'IA.TRANSACTION_CURRENCY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies'
            ),
            'required' => true,
            'hidden' => true,
            'noedit' => true,
            'nonew' => true,
            'noview' => true,
            'id' => 9
        ),
        array(
            'path' => 'EXCH_RATE_DATE',
            'fullname' => 'IA.EXCHANGE_RATE_DATE',
            'type' => array(
                'type' => 'date',
                'ptype' => 'date',
                'format' => $gDateFormat
            ),
            'hidden' => true,
            'id' => 20
        ),
        array(
            'path' => 'EXCH_RATE_TYPE_ID',
            'fullname' => 'IA.EXCHANGE_RATE_TYPE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'exchangeratetypesall'
            ),
            'hidden' => true,
            'id' => 21
        ),
        array(
            'path' => 'EXCHANGE_RATE',
            'fullname' => 'IA.EXCHANGE_RATE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'maxlength' => 12
            ),
            'precision' => 12,
            'hidden' => true,
            'noformat' => true,
            'rpdMeasure' => false,
            'id' => 22
        ),
        array(
            'path' => 'TOTALENTERED',
            'fullname' => 'IA.TOTAL_BASE_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 10
        ),
        array(
            'path' => 'TRX_TOTALENTERED',
            'fullname' => 'IA.TOTAL_TRANSACTION_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 11
        ),
        array(
            'path' => 'REVERSALKEY',
            'fullname' => 'IA.REVERSAL_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'readonly' => true,
            'hidden' => true,
            'id' => 12
        ),
        array(
            'path' => 'REVERSALDATE',
            'fullname' => 'IA.REVERSAL_DATE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text'
            ),
            'readonly' => true,
            'id' => 13
        ),
        array(
            'path' => 'REVERSEDKEY',
            'fullname' => 'IA.REVERSED_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'readonly' => true,
            'hidden' => true,
            'id' => 14
        ),
        array(
            'path' => 'REVERSEDDATE',
            'fullname' => 'IA.ORIGINAL_TRANSACTION_DATE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text'
            ),
            'readonly' => true,
            'id' => 15
        ),
        array(
            'path' => 'STATE',
            'fullname' => 'IA.STATE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'enum',
                'validlabels' => array('IA.VOIDED'), // Should be the only state valid for this transaction
                'validvalues' => array('V')
            ),
            'readonly' => true,
            'id' => 16
        ),
        array(
            'path' => 'RAWSTATE',
            'fullname' => 'IA.RAW_STATE',
            'hidden' => true,
            'readonly' => true,
            'id' => 18
        ),
        array(
            'path' => 'CLEARED',
            'fullname' => 'IA.CLEARED',
            'type' => array(
                'type' => 'text',
                'ptype' => 'enum',
                'validlabels' => array('IA.T', 'IA.F', 'IA.M'),
                'validvalues' => array('T', 'F', 'M')
            ),
            'default' => 'F',
            'readonly' => true,
            'hidden' => true,
            'id' => 17
        ),
        // -- START UI ONLY FIELD
        array(
            'path' => 'SUPDOCID',
            'fullname' => 'IA.ATTACHMENT',
            'type' => array(
                'ptype' => 'supdocptr',
                'type' => 'supdocptr',
                'maxlength' => 20,
                'listAction' => 'pick'
            ),
            'noedit' => false
        ),
        // 'Transaction Type' is the same as recordtype but we will make it a enum and
        // it will be used to display the label of the transaction on the lister without
        // having to make any hack
        array(
            'path' => 'TRANSACTIONTYPE',
            'fullname' => 'IA.TRANSACTION_TYPE',
            'type' => array(
                'type' => 'enum',
                'ptype' => 'enum',
                'validlabels' => array('IA.SERVICE_CHARGE', 'IA.INTEREST_EARNED'),
                'validvalues' => array('Service charge', 'Interest earned'),
                '_validivalues' => array(SubLedgerTxnManager::BANKCHRG_RECTYPE, SubLedgerTxnManager::BANKINT_RECTYPE)
            ),
            'id' => 19
        ),

        array(
            'path' => 'FINANCIALENTITYHEADER',
            'fullname' => 'IA.BANK_ACCOUNT',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
            ),
            'readonly' => true
        ),
        array(
            'path' => 'WHENCREATEDHEADER',
            'fullname' => 'IA.DATE',
            'type' => $gDateType,
            'readonly' => true
        ),
        array(
            'path' => 'TRX_TOTALENTEREDHEADER',
            'fullname' => 'IA.TOTAL_TRANSACTION_AMOUNT',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
            ),
            'readonly' => true
        ),
        array(
            'path' => 'TOTALENTEREDHEADER',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
            ),
            'fullname' => 'IA.TOTAL_BASE_AMOUNT',
            'readonly' => true
        ),
        array(
            'path' => 'TRANSACTIONTYPEHEADER',
            'fullname' => 'IA.TRANSACTION_TYPE',
            'type' => array(
                'type' => 'enum',
                'ptype' => 'enum',
                'validlabels' => array('IA.SERVICE_CHARGE', 'IA.INTEREST_EARNED'),
                'validvalues' => array('Service charge', 'Interest earned'),
                '_validivalues' => array(SubLedgerTxnManager::BANKCHRG_RECTYPE, SubLedgerTxnManager::BANKINT_RECTYPE)
            ),
            'readonly' => true
        ),
        // -- END UI ONLY FIELDS
        array(
            'path' => 'AUWHENCREATED',
            'fullname' => 'IA.WHEN_CREATED',
            'desc' => 'IA.TIMESTAMP_MARKING_LAST_TIME_THIS_WAS_CREATED',
            'type' => array(
                'type' => 'timestamp',
                'ptype' => 'timestamp',
                'maxlength' => 20
            ),
            'id' => 23
        ),
        array(
            'fullname' => 'IA.TAX_SOLUTION',
            'desc' => 'IA.TAX_SOLUTION',
            'path' => 'TAXSOLUTIONID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'taxsolution',
                'pickfield' => array('SOLUTIONID', 'SHOWMULTILINETAX', 'TAXMETHOD', 'RECORDNO'),
                'restrict' => array(
                    array(
                        'pickField' => 'TAXMETHOD',
                        'operand' => 'IN',
                        'value' => BankFeeManager::getTaxImplicationTaxMethodsHelper(false),
                    ),
                )
            ),
            'nonew' => true,
            'id' => 24,
        ),
        array(
            'fullname' => 'IA.TRANSACTION_HAS_TAX_IMPLICATIONS',
            'desc' => 'IA.TRANSACTION_HAS_TAX_IMPLICATIONS',
            'path' => 'TAXIMPLICATIONS',
            'type' => $gBooleanType,
            'default' => 'false',
        ),
        array(
            'path' => 'INCLUSIVETAX',
            'fullname' => 'IA.INCLUSIVE_TAXES',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 25
        ),
        array(
            'path'     => 'PARENTPAYMENT',
            'fullname' => 'IA.PARENT_PAYMENT_KEY',
            'type'     => [
                'type'   => 'integer',
                'ptype'  => 'integer',
                'format' => $gRecordNoFormat,
            ],
            'id'       => 26,
        ),
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        $gSiUuidFieldInfo
    ),
    'printas' => 'IA.BANK_INTEREST_AND_CHARGES',
    'pluralprintas' => 'IA.BANK_INTEREST_AND_CHARGES',
    'sicollaboration' => true,
    'table' => 'prrecord',
    'updatetable' => 'prrecordmst',
    'module' => 'cm',
    'atlasentityonly' => true,
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'supdocentity' => 'BANKFEE',
    'auditcolumns' => true,
    'customerp' => array(
        'SLTypes' => array(
            CUSTOMERP_SMARTLINKFETCH,
            CUSTOMERP_SMARTLINKCLICK,
            CUSTOMERP_SMARTLINKVALIDATE,
            CUSTOMERP_SMARTLINKWORKFLOW
        ),
        'SLEvents' => array(
            CUSTOMERP_EVENT_ADD,
            CUSTOMERP_EVENT_SET,
            CUSTOMERP_EVENT_ADDSET,
            CUSTOMERP_EVENT_CLICK
        ),
        'AllowCF' => true
    ),
    'api' => array(
        'GET_BY_GET' => true,
        'ITEMS_ALIAS' => ['BANKFEEENTRIES'],
        'ITEM_ALIAS' => ['BANKFEEENTRY'],
        'ITEMS_INTERNAL' => ['ITEMS'],
        'PERMISSION_CREATE' => 'lists/bankfee/create',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
        'CANT_UPDATE_FIELDS' => ['PARENTPAYMENT'],
    ),
    'dbfilters' => array(
        array(
            'bankfee.recordtype',
            'in',
            array(SubLedgerTxnManager::BANKCHRG_RECTYPE, SubLedgerTxnManager::BANKINT_RECTYPE)
        )
    ),
    'allowDDS' => false,
    'description' => 'IA.HEADER_INFORMATION_FOR_BANK_TRANSACTIONS_THAT_RECO',
);

require 'taxsummary.ent';
$kSchemas['bankfee'] = EntityManager::inheritEnts($kSchemas['taxsummary'], $kSchemas['bankfee']);
