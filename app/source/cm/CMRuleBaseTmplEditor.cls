<?php

/**
 * Base editor class for the CM rule templates.
 *
 * <AUTHOR>
 * @copyright 2021 Sage Intacct Inc., All Rights Reserved
 */
class CMRuleBaseTmplEditor extends FormEditor
{
    /**
     * Returns the list of Javascript files to include in the page.
     *
     * @return array the list of Javascript files to include
     */
    protected function getJavaScriptFileNames()
    {
        $jsFiles = parent::getJavaScriptFileNames();
        $jsFiles[] = '../resources/js/cmruletax.js';
        return $jsFiles;
    }

    /**
     * Declare global variables to be used in editor
     *
     * @return array $vars all the global variables
     */
    protected function getEditorGlobals()
    {
        $vars = parent::getEditorGlobals();
        $vars['vatEnabled'] = TaxSetupManager::isVATEnabled();
        $vars['VAT_NONE'] = TaxValidationHelper::VAT_NONE;
        $vars['VAT_INBOUND'] = TaxValidationHelper::VAT_INBOUND;
        $vars['VAT_OUTBOUND'] = TaxValidationHelper::VAT_OUTBOUND;
        return $vars;
    }

    /**
     * Hook for subclasses to prepare the object during a save action
     * At the time of the call the object is in business form
     *
     * @param array &$obj the object (in and out)
     *
     * @return bool
     */
    protected function prepareObjectForSave(&$obj)
    {
        $ok = parent::prepareObjectForSave($obj);
        if ( $ok ) {
            $this->prepareObject($obj);
        }

        return $ok;
    }

    /**
     * Hook for subclasses to prepare the object during a create action
     * At the time of the call the object is in business form
     *
     * @param array &$obj the object (in and out)
     *
     * @return bool
     */
    protected function prepareObjectForCreate(&$obj)
    {
        $ok = parent::prepareObjectForCreate($obj);
        if ( $ok ) {
            $this->prepareObject($obj);
        }

        return $ok;
    }

    /**
     * @param array $obj
     */
    private function prepareObject(&$obj)
    {
        if ( is_array($obj) ) {
            if (empty($obj['TAXIMPLICATIONS'])
                || $obj['TAXIMPLICATIONS'] === TaxValidationHelper::VAT_NONE) {
                $obj['TAXSOLUTIONID'] = null;
                $obj['TAXSOLUTIONKEY'] = null;
                $obj['SOTAXSCHEDULEID'] = null;
                $obj['POTAXSCHEDULEID'] = null;
                $obj['SOTAXSCHEDULEKEY'] = null;
                $obj['POTAXSCHEDULEKEY'] = null;
                $obj['VATCUSTOMERID'] = null;
                $obj['VATCUSTOMERKEY'] = null;
                $obj['VATVENDORID'] = null;
                $obj['VATVENDORKEY'] = null;
            } elseif ($obj['TAXIMPLICATIONS'] === TaxValidationHelper::VAT_INBOUND) {
                $obj['VATCUSTOMERID'] = null;
                $obj['VATCUSTOMERKEY'] = null;
            } elseif ($obj['TAXIMPLICATIONS'] === TaxValidationHelper::VAT_OUTBOUND){
                $obj['VATVENDORID'] = null;
                $obj['VATVENDORKEY'] = null;
            }
        }

        return true;
    }
    /**
     * This is a hook functions for subclases to adjust the metadata according
     * to the current data & state
     *
     * @param array &$obj the data
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $ok = parent::mediateDataAndMetadata($obj);
        $view = $this->getView();
        $hideTax = true;
        if ($ok && TaxSetupManager::isVATEnabled()) {
            $hideTax = false;

            //Disable/enable the tax solution picker if slid into entity or single tax jursidiction or single entity company
            $obj['LOCATIONENTITYKEY'] = GetContextLocation();
            $isMultiTaxJurisdictionConfigured = TaxSetupManager::isMultiTaxJurisdictionConfigured();
            $multiEntityCompany = IsMultiEntityCompany();
            if ($obj['LOCATIONENTITYKEY'] || !$isMultiTaxJurisdictionConfigured || !$multiEntityCompany) {
                //Make the tax solution picker as readonly as we will not allow to switch when slid into an entity
                $view->findAndSetProperty(array('path' => 'TAXSOLUTIONID'), array('readonly' => true));
            }

            if (empty($obj['TAXIMPLICATIONS']) || $obj['TAXIMPLICATIONS'] ===  TaxValidationHelper::VAT_NONE) {
                $view->findAndSetProperty(array('path' => 'SOTAXSCHEDULEID'), array('hidden' => true));
                $view->findAndSetProperty(array('path' => 'POTAXSCHEDULEID'), array('hidden' => true));
            }
            elseif ($obj['TAXIMPLICATIONS'] ===  TaxValidationHelper::VAT_INBOUND)
            {
                $view->findAndSetProperty(array('path' => 'SOTAXSCHEDULEID'), array('hidden' => true));
            }
            elseif ($obj['TAXIMPLICATIONS'] ===  TaxValidationHelper::VAT_OUTBOUND)
            {
                $view->findAndSetProperty(array('path' => 'POTAXSCHEDULEID'), array('hidden' => true));
            }

            //Initialize the tax solution fields base on sliding into entity, single jurisdiction, or single entity company
            if ($this->state == $this->kShowNewState
                || (($this->state == $this->kShowEditState)
                    && !$obj['TAXSOLUTIONID'])) {
                if (!$isMultiTaxJurisdictionConfigured || !$multiEntityCompany) {
                    //In single jusrisdiction or standard company, we should only have one tax solution set up, so use that
                    $mgr = Globals::$g->gManagerFactory->getManager('taxsolution');
                    $filter = array('selects' => array('SOLUTIONID', 'RECORDNO', 'SHOWMULTILINETAX'));
                    $taxSoln = $mgr->GetList($filter);
                    if (count($taxSoln) > 1) {
                        $msg = "A single tax jurisdiction company should have only one tax solution, but multiple exist.";
                        $corr = "Contact Sage Intacct support.";
                        Globals::$g->gErr->addError('GL-0811', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                        $ok = false;
                    }
                    $obj['TAXSOLUTIONID'] = $taxSoln[0]['SOLUTIONID'];
                    $obj['TAXSOLUTIONKEY'] = $taxSoln[0]['RECORDNO'];
                } else if ($obj['LOCATIONENTITYKEY']) {
                    //At entity level, use the tax solution from the entity
                    $mgr = Globals::$g->gManagerFactory->getManager('taxsolution');
                    $taxSoln = $mgr->getTaxsolutionDetailsForLocn($obj['LOCATIONENTITYKEY']);
                    if (count($taxSoln) != 1) {
                        $msg = "The entity does not have  a tax solution.";
                        $corr = "Edit the entity and associate a tax solution to it. Then, try again.";
                        Globals::$g->gErr->addError('GL-0812', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                        $ok = false;
                    }
                    $obj['TAXSOLUTIONID'] = $taxSoln[0]['SOLUTIONID'];
                    $obj['TAXSOLUTIONKEY'] = $taxSoln[0]['RECORD#'];
                }
                //Else
                //We are at root level so tax solution id is entered by users. We don't need to
                //init the taxsolution id as it should already have been.

            }
        }
        if ($view->findComponents(['id' => 'taxInfo'], EditorComponentFactory::TYPE_SECTION,
            $taxSection)) {
            if (isset($taxSection) && isset($taxSection[0])) {
                $taxSection[0]->setProperty('hidden', $hideTax);
            }
        }

        return $ok;
    }


    /**
     * Run AJAX commands
     *
     * @param string $cmd  the command to run
     *
     * @return bool  true if the function handled the AJAX command, false otherwise
     *
     */
    protected function runAjax($cmd)
    {
        $ret = parent::runAjax($cmd);
        if ( ! $ret ) {
            switch ($cmd ) {
                case 'getLocationRecordNo':
                    if (Request::$r->equal == 'false') {
                        Request::$r->equal = 'true';
                        Request::$r->useNonTaxImplicationTaxMethods = 'true';
                    }
                    SubLedgerTxnEditor::ajaxGetLocationRecordNo(TaxSolutionManager::$NON_VAT_TAXMETHOD_VALUES);
                    break;
                default:
                    $ret = false;
            }
        }

        return $ret;
    }


    /**
     *
     * @return bool
     */
    protected function CanPrint() : bool
    {
        // hide "Print To" button
        return false;
    }

}
