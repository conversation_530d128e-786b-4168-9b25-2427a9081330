<?php
/**
 * Class to process finacial entity data from QIF file.
 *
 * <AUTHOR>
 * @copyright 2020 Sage Intacct Inc., All Rights Reserved
 */
class QIFFileDataHandler extends FinancialEntityDataHandler
{
    const BANK_CONTENT_HEADER = "!Type:Bank";
    const END_OF_TRANSACTION = "^";
    const DATE_LINE = "D";
    const AMOUNT_LINE = "T";
    const DOCNUMBER_LINE = "N";
    const PAYEE_LINE = "P";
    const MEMO_LINE = "M";

    /**
     * Processes the raw data for the XML file.
     *
     * @param array $rawdata
     * @param array $outputdata
     *
     * @return bool
     */
    public function process($rawdata, &$outputdata)
    {
        $ok = true;
        if(!empty($rawdata)) {
            $count = 0;
            $transaction = [];
            foreach ($rawdata as $data) {
                $data = isl_trim($data);
                if (empty($data)) {
                    continue;
                }
                if ($count == 0) {
                    if ($data != self::BANK_CONTENT_HEADER) {
                        Globals::$g->gErr->addError("CM-0170", "Error in upload file content",
                                                    "The QIF file is not a bank file", "The bank header is missing in the QIF file");
                        return false;
                    }
                } else {
                    if ($data == self::END_OF_TRANSACTION) {
                        $tempArray = [];
                        $ok = $ok && $this->parseTransLine($transaction, $tempArray);
                        if (is_array($tempArray) && count($tempArray) > 0) {
                            // Validate the transactions before continuing
                            $ok = $ok && $this->validateTransactions($tempArray);
                            $outputdata[] = $tempArray;
                        }
                        // Initialize the transaction array
                        $transaction = [];
                    } else {
                        $transaction[] = $data;
                    }
                }
                if(!$ok) {
                    break;
                }
                $count++;
            }
        }
        return $ok;
    }

    /**
     * Implementation method to parse the transaction file line by line.
     * Parse the uploaded transaction file line by line and populate the parsed values into a temporary array
     * to form the final php array.
     *
     * @param array $transLine
     * @param array $tempArray
     *
     * @return bool
     */
    public function parseTransLine($transLine, &$tempArray)
    {
        $ok = true;
        if (is_array($transLine) && count($transLine) > 1) {
            foreach ($transLine as $attributes) {
                $attributes = isl_trim($attributes);
                if (!empty($attributes)) {
                    $char0 = substr($attributes, 0, 1);
                    $data = $this->stripLineInfo($attributes);
                    if ( $char0 == self::DATE_LINE ) {
                        $ok = $this->parseDateLine(convert8601ToLocalDate($data), $tempArray);
                    } else if ( $char0 == self::AMOUNT_LINE ) {
                        $ok = $ok && $this->parseAmountLine($data, $tempArray);
                    } else if ( $char0 == self::DOCNUMBER_LINE ) {
                        $ok = $ok && $this->parseDocNumberLine($data, $tempArray);
                    } else if ( $char0 == self::PAYEE_LINE ) {
                        $ok = $ok && $this->parsePayeeLine($data, $tempArray);
                    } else if ( $char0 == self::MEMO_LINE ) {
                        $ok = $ok && $this->parseMemoLine($data, $tempArray);
                    }
                }
            }
        }
        return $ok;
    }

    /**
     * Converts the csv into array values.
     *
     * @param array    $rawdata
     *
     * @return array
     */
    public function convertData($rawdata)
    {
        $formattedData = [];
        foreach ($rawdata as $data) {
            $formattedData[] = isl_trim($data);
        }
        return $formattedData;
    }

    /**
     * @param array $data
     *
     * @return bool
     */
    private function validateTransactions($data)
    {
        $ok = true;
        if (empty($data['POSTINGDATE'])) {
            Globals::$g->gErr->addError('CM-0171', __FILE__ . '.' . __LINE__, 'Invalid date, please check the posting date ');
            $ok = false;
        } else if (empty($data['AMOUNT'])) {
            Globals::$g->gErr->addIAError('CM-0168', __FILE__ . '.' . __LINE__, 'Invalid amount, please check the amount ', ['AMOUNT' => $data['AMOUNT']]);
            $ok = false;
        }
        return $ok;
    }
}