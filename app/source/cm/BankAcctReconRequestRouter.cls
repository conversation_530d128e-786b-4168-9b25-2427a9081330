<?php

class BankAcctReconRequestRouter  extends BaseRequestRouter
{
    /**
     * @param string $entity
     * @param string $function
     */
    public function __construct($entity, $function)
    {
        parent::__construct($entity, $function);
    }

    /**
     * Method to populate the request response attribute maps.
     */
    public function populateRequestResponseAttributeMap()
    {
        $this->requestAttributeMap['BANKACCOUNTID'] = 'FINANCIALENTITY';
        $this->requestAttributeMap['CUTOFFDATE'] = 'CUTOFFDATE';
        $this->requestAttributeMap['STATEMENTENDINGDATE'] = 'STMTENDINGDATE';
        $this->requestAttributeMap['RECONMODE'] = 'MODE';
        $this->requestAttributeMap['STATEMENTENDINGBALANCE'] = 'STMTENDINGBALANCE';
        $this->requestAttributeMap['SUPDOCKEY'] = 'SUPDOCKEY';
    }


    /**
     * Method for custom prepare the request.
     *
     * @param string    $function   name of the function
     * @param array     $request    request array
     *
     * @return array
     */
    public function prepareRequest($function, $request)
    {
        if($function == 'API_Add' && !empty($request)) {
            //Reset record#
            $request['RECORDNO'] = null;
            if ( ! isset($request['FEEDTYPE']) ) {
                $request['FEEDTYPE'] = ReconciliationUtils::FEEDTYPE_XML;
            }
            $request['MIGRATED'] = 'T';
        }
        return parent::prepareRequest($function, $request);
    }
    /**
     * Utility method to merge the payment items.
     *
     * @param array $request
     * @return bool
     *
     * @throws Exception
     */
    protected function mergeRequestItems(/** @noinspection PhpUnusedParameterInspection */ &$request)
    {
        return true;
    }

}