<?php

/**
 * APIQueryOrchestrator is a orchestrator
 * for invoking query as a FilterView service
 *
 *
 * <AUTHOR>
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */

class APIQueryOrchestrator extends APIOrchestratorBase
{

    /**
     *
     */
    const FILTERS_PERSISTENCE = 'filters';

    /* */
    const FILTER_OPS_REDIRECT = [
        APIConstants::API_OPERATION_READ,
        APIConstants::API_OPERATION_DELETE,
    ];

    const ENT_NAME_SPECIAL_TEST = '__testtest';

    /**
     * @var FilterViewService[] $fvServices
     */
    private static $fvServices = [];

    /**
     * Used in metrics
     * @var bool $isDynamicQuery
     */
    protected $isDynamicQuery = true;

    /**
     * @var string|null $object
     */
    protected $object = null;

    /**
     * Holds mapping returned from api2ent conversion to be used in ent2api conversion
     * @var array $objFieldMap
     */
    protected $objFieldMap = [];

    /**
     * @var array $query
     */
    protected $query = [];

    /**
     * @var string $responseFormat
     */
    protected $responseFormat = APIQueryUtil::DEFAULT_RESPONSE_FORMAT;

    /**
     * @var MetricNextGenAPIQueryService $queryMetrics
     */
    private $queryMetrics = null;

    /**
     * @var PerfDataForAPI $perfData
     */
    private $perfData = null;

    /**
     * @var string $systemViewObjectName
     */
    private $systemViewObjectName = null;

    /**
     * @param string $operation
     * @param array  $request
     * @param array  $extraParams
     *
     * @return array
     */
    protected function validateExtraParams($operation, $request, $extraParams)
    {
        $errors = APIQueryValidator::validateQueryOverrides($request);
        if (!empty($errors)) {
            self::handleErrors($errors);
        }
        $internalParameters = parent::validateExtraParams($operation, $request, $extraParams);

        // add controlls as they have limitted access rules
        $internalParameters[APIConstants::API_EXTRA_PARAM_CONTROL_PARAM]
            = $extraParams[APIConstants::API_EXTRA_PARAM_CONTROL_PARAM] ?? null;

        return $internalParameters;
    }

    /**
     * @inheritDoc
     */
    public function executeInternal($operation, $request, &$extraParams = [])
    {
        if ( RegistryLoader::$doTimers) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $timer = new ScopeTimer(__CLASS__, false, 'RegistryLoader::logTiming', __METHOD__);
        }
        // clear previous if any
        $this->reset();
        $this->initMetrics($extraParams);

        if ( $operation === APIConstants::API_OPERATION_OPTIONS ) {
            return $this->handler->getHTTPMethods();
        }
        // depends on registry type
        $this->systemViewObjectName = RegistryLoader::CRUD_SERVICE_NAME . APIConstants::URI_SEPARATOR .
                                      RegistryLoader::getCoreObjectNameForVersion(APIConstants::SYSTEM_VIEW_OBJ_NAME, $this->version);
        try {
            if (empty($request)) {
                throw (new APIException())->setAPIError(APIError::getInstance(
                    APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0130, []));
            }
            LinguisticSortUtil::enableLinguisticSort();
            
            if (is_numeric(key($request))) {
                // record the request count
                $numRequests = count($request);
                $extraParams[APIConstants::API_EXTRA_PARAM_REQ][APIConstants::API_EXTRA_PARAM_REQ_COUNT] = $numRequests;

                if ($numRequests === 1) {
                    // not to wrap a single request
                    return $this->executeSingleRequest(current($request), $extraParams);
                }
                $combinedResult = [];
                foreach ($request as $singleRequest) {
                    try {
                        $singleResult = $this->executeSingleRequest($singleRequest, $extraParams);
                    } catch (APIException $e) {
                        // at this point it should always have an APIError
                        $apiError = $e->getAPIError();
                        $singleResult = $apiError->freeze()->getErrorResponseObject();
                        $singleResult[APIError::KEY_IA_STATUS] = $apiError->getStatus();
                    }
                    $combinedResult[] = $singleResult;
                }
                return $combinedResult;
            }
            return $this->executeSingleRequest($request, $extraParams);
        }  finally {
            /** @noinspection PhpUndefinedVariableInspection */
            $this->flushMetrics( ( $request )?? null, ( $response )?? null, ( $apiError )?? null );
        }
    }

    /**
     * @param array  $request
     * @param array  $extraParams
     *
     * @return array|string[]
     * @throws APIException
     * @throws APIInternalException
     */
    protected function executeSingleRequest(array $request, array &$extraParams = [])
    {
        if ( RegistryLoader::$doTimers) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $timer = new ScopeTimer(__CLASS__, false, 'RegistryLoader::logTiming', __METHOD__);
        }
        try {
            if (empty($request)) {
                // every single request should not be empty
                throw (new APIException())->setAPIError(APIError::getInstance(
                    APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0130, []));
            }
            if (!array_key_exists(APIConstants::API_OBJECT_TYPE, $request)) {
                throw (new APIException())->setAPIError(
                    APIError::getInstance(
                        APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0103,
                        [], true)
                );
            }
            // store object name for LOS metrics
            $extraParams[APIConstants::API_EXTRA_PARAM_REQ][APIConstants::API_EXTRA_PARAM_REQ_ADDITIONAL_INFO][] = $request[APIConstants::API_OBJECT_TYPE];
            $operation = APIConstants::API_QUERY; // TBD
            $response = $this->executeRequest($operation, $request, $extraParams);
        } catch (APIException $e) {
            if ($e->hasAPIError()) {
                if (($apiError = $e->getAPIError())->getStatus() === APIError::HTTP_405_STATUS) {
                    self::throwQueryErrorWithDetails([$apiError], APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0132);
                }
                throw $e;
            }
            $apiError = APIError::getInstance(
                APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0001, ["MESSAGE" => $e->getMessage()], true);
            throw (new APIInternalException())->setAPIError($apiError);
        } catch (Exception $e) {
            $apiError = APIError::getInstance(
                APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0001, ["MESSAGE" => $e->getMessage()], true);
            throw (new APIInternalException())->setAPIError($apiError);
        }

        return $response;
    }

    /**
     * @param string             $operation
     * @param array              $request
     * @param SchemaHandler|null $handler
     * @param array              $extraParams
     *
     * @throws APIException
     * @throws APIOrchestratorException
     * @noinspection PhpUnusedParameterInspection
     */
    protected function validateRequest($operation, &$request, $handler, array &$extraParams = [])
    {
        // =========================
        // Mandatory validation
        // =========================
        if ($handler !== null && $handler->getRuntimeOwner() === RegistryLoader::DEFAULT_OWNER) {
            $handler->validateOperation(APIConstants::API_HTTP_METHOD_GET, $extraParams);
        } // else: too early to validate, permissions will be checked in some other path

        if (!$handler->hasAdapter()) {
            throw ( new APIException() )->setAPIError(APIError::getInstance(
                APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0010, [ "RESOURCE_NAME" => $handler->getName(), "VERSION" => $this->version ], true));
        }
        $qbody = $request;
        if ($qbody !== null) {
            $errors = APIQueryUtil::validateQueryContent($qbody);
            if (!empty($errors)) {
                self::throwQueryErrorWithDetails($errors);
            }
        }

        // In addition - invoke object validator
        $validator = $handler->getValidator($extraParams);
        if ( $validator !== null ) {
            // If there is a validator - invoke
            $validator->validateSingleRequest($operation, $request, $handler, $extraParams);
        } // else do nothing
    }

    /**
     * @param APIError[] $errors
     *
     * @throws APIException
     */
    public static function handleErrors(array $errors) : void
    {
        // TODO: $errors should be APIError[]
        if (count($errors) === 1) {
            $apiErr = $errors[0];
        } else {
            // if errors needed global error, they already included it. Do not add
            $apiErr = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0115, []);
            foreach ( $errors as $error ) {
                $apiErr->addDetailError($apiErr->getCategory(), $error->getMessage(), '');
            }
        }
        throw ( new APIException() )->setAPIError($apiErr);
    }

    // =========== private methods =============== //

    /**
     * @param string[]      $request
     * @param SchemaHandler $handler
     * @param array         $extraParams
     *
     * @return string[]
     */
    private function convertFromAPI($request, $handler, array &$extraParams)
    {
        if ( $handler->getObjectName() === $this->systemViewObjectName || $handler->getRuntimeOwner() !== RegistryLoader::DEFAULT_OWNER) {
            return $request;
        }
        $updatedRequest = APIQueryUtil::convertQueryApi2Ent($request, $handler, $extraParams);

        $this->objFieldMap = $updatedRequest['mapping']?? [];
        $this->query = $updatedRequest[APIConstants::API_QUERY] ?? [];
        $this->responseFormat = $request[APIQueryUtil::API_QUERY_RESPONSE_FORMAT_KEY] ?? APIQueryUtil::DEFAULT_RESPONSE_FORMAT;

        return $this->query;
    }

    /**
     * @param string[]      $response
     * @param SchemaHandler $handler
     * @param array         $extraParams
     *
     * @return string[]
     */
    private function convertToAPI($response, $handler, array &$extraParams)
    {
        if ( $handler->getObjectName() === $this->systemViewObjectName || $handler->getRuntimeOwner() !== RegistryLoader::DEFAULT_OWNER) {
            return $response;
        }

        $result =  APIQueryUtil::convertQueryEnt2Api(
            $response,
            $handler,
            $this->objFieldMap,
            $this->responseFormat,
            $extraParams,
        );

        // Add page info; start with 1 for external use
        APIQueryUtil::addPageInfo(($this->query[APIQueryUtil::API_PAGE_START]?? 0) + 1,
                                  $this->query['max'] ?? 0, $result[APIConstants::IA_META_KEY]);

        $validator = $handler->getValidator($extraParams);
        if ($validator !== null) {
            // If there is a validator - invoke
            $validator->validateSingleResponse(APIConstants::API_QUERY, $result[APIConstants::IA_RESULT_KEY], $handler);
        } else {
            // If no validator configured, provide a minimum default validation logic w/o having to configure all schema history file
            // configuring a no-op validator to disable default validation
        }

        return $result;
    }

    /**
     *
     * @param string        $operation
     * @param array         $request
     * @param SchemaHandler $handler
     *
     * @throws APIOrchestratorException
     */
    private function authorize($operation, $request, $handler)
    {
        // todo - implement authorization in APIUtil for CRUD

    }

    /**
     * @param SchemaHandler $handler
     * @param array         $request
     * @param array         $extraParams
     *
     * @return array
     * @throws FilterViewFault
     * @throws APIInternalException Remote DS Query service failed
     */
    protected function executeQuery(SchemaHandler $handler, array $request, array $extraParams)
    {
        if ( RegistryLoader::$doTimers) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $timer = new ScopeTimer(__CLASS__, false, 'RegistryLoader::logTiming', __METHOD__);
        }
        $result = [
            APIConstants::IA_RESULT_KEY => [],
            APIConstants::IA_META_KEY => []
        ];
        $objName = $handler->getObjectName();
        if ( endsWith($objName, self::ENT_NAME_SPECIAL_TEST) && RegistryLoader::isTestVersion($handler->getRegistryVersion())) {
            // for a special test
            return $handler->getAdapter()->executeOperation(APIConstants::API_QUERY, $handler->getObjectName(),
                                                            $handler->getRegistryVersion(), $request, $request, $extraParams);
        } else if ($objName === $this->systemViewObjectName) {
            $systemFilterOrch = $this->getSystemViewOrch($handler, $request, $extraParams);
            // we execute read so query is stored in $extraParams
            $result[APIConstants::IA_RESULT_KEY]  = $systemFilterOrch->executeInternal(APIConstants::API_OPERATION_READ, [], $extraParams);
            $result[APIConstants::IA_META_KEY][APIConstants::API_TOTAL_COUNT_KEY] = count($result[APIConstants::IA_RESULT_KEY]);
            return $result;
        } else if (($runtimeOwner = $handler->getRuntimeOwner()) !== RegistryLoader::DEFAULT_OWNER) {
            // delegate
            $trustedAPI = new DomainServiceAPICoreServiceClient($runtimeOwner,
                                                                RegistryLoader::getCoreServiceNameForVersion(APIConstants::API_QUERY, $this->version),
                                                                $this->version);
            $dsResult = $trustedAPI->invokeService($request, APIConstants::API_QUERY, Globals::$g->islive ? [] : APIUtil::buildHeaderWithCookies([], $_COOKIE));
            // and post-process the outcome
            $result = $dsResult[TrustedAPIClient::HTTP_RESPONSE];
            if ($dsResult[TrustedAPIClient::HTTP_CODE] >= 300) {
                // add it
                $result[APIError::KEY_IA_STATUS] = $dsResult[TrustedAPIClient::HTTP_CODE];
            }
            return $result;
        }
        // copy for updates
        $query = $request;
        $firstRow = null;

        // must be added here to avoid reuse
        $query['firstrow'] = &$firstRow;
        // print_r($query);

        try {

            $fvService = $this->getFVService($handler);
            if ( RegistryLoader::$doTimers) {
                /** @noinspection PhpUnusedLocalVariableInspection */
                $timer1 = new ScopeTimer(__CLASS__, false, 'RegistryLoader::logTiming', 'getFilteredData');
            }
            $nocount = $query['nocount']; // set the no count flag
            unset($query['nocount']); // remove this as it is not used in queries.
            $response = $fvService->getFilteredData($query, $nocount);
            /** @noinspection PhpUnusedLocalVariableInspection */
            $timer1 = null;
        } catch (Exception $e) {
            logToFileWarning($e->getMessage(),true);
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::UNPROCESSABLE_ENTITY_OPERATION_FAILED_0001,["OPERATION" => APIConstants::getOperation(APIConstants::API_QUERY),
                                                                              "RESOURCE_NAME" => RegistryLoader::getShortObjectName($handler->getObjectName())], true));
        }
        // There can still be errors even without an explicit exception
        if (Globals::$g->gErr->ErrorCount) {
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::UNPROCESSABLE_ENTITY_OPERATION_FAILED_0001,["OPERATION" => APIConstants::getOperation(APIConstants::API_QUERY),
                                                                              "RESOURCE_NAME" => RegistryLoader::getShortObjectName($handler->getObjectName())], true));
        } else if (!$response['success']) {
            throw new APIQueryExecutionException($response['error-message']);
        } else {
            $result[APIConstants::IA_META_KEY][APIConstants::API_TOTAL_COUNT_KEY] = 0;
            $result[APIConstants::IA_RESULT_KEY] = $response[APIQueryUtil::API_QUERY_RESULT_DATA_KEY];
            if (is_array($result[APIConstants::IA_RESULT_KEY]) && count($result[APIConstants::IA_RESULT_KEY]) > 0) {
                // When $nocount is set to true then QCNT will be set to 0 since no count is done for the query.
                $result[APIConstants::IA_META_KEY][APIConstants::API_TOTAL_COUNT_KEY] =
                    (int)$query['firstrow']['QCNT'] ?? count($result[APIConstants::IA_RESULT_KEY]);
            }
        }

        return $result;
    }

    /**
     * Actual Query processing
     *
     * @param string              $operation
     * @param array               $request
     * @param array               $extraParams
     *
     * @return array|string[]
     * @throws APIException
     * @throws APIOrchestratorException
     * @throws FilterViewFault
     */
    private function executeRequest(string $operation, array $request, array $extraParams)
    {
        if ( RegistryLoader::$doTimers) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $timer = new ScopeTimer(__CLASS__, false, 'RegistryLoader::logTiming', __METHOD__);
        }
        $object = $request[APIConstants::API_OBJECT_TYPE];
        if (strpos($object, RegistryLoader::CRUD_SERVICE_NAME) !== 0) {
            // it is a short name
            $shortObjectName = $object;
            $object = RegistryLoader::getObjectNamePath($object, $this->version) ?? RegistryLoader::getResourceNameInVersion($shortObjectName, $this->version);
            if ( $object === null ) {
                throw (new APIException())->setAPIError(APIError::getInstance(
                    APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0010,["RESOURCE_NAME" => $shortObjectName, "VERSION" => $this->version], true));
            }
        }
        $registry = $this->handler->getRegistryInstance();
        $type = $registry->getTypeByName($object);
        if ( !in_array($type, APIConstants::OBJECT_TYPES, true) ) {
            throw (new APIOrchestratorException())->setAPIError(APIError::getInstance(
                APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0123, ["OBJECT" => $object, "TYPE" => $type],false));
        }
        // use handler for the object being queried
        /** @noinspection NullPointerExceptionInspection */
        $handler = $registry->getSchemaHandler($object);
        $this->authorize($operation, $request, $handler);

        try {
            if ($this->queryMetrics) {$this->queryMetrics->startValidateTimeRequest();}
            /** @noinspection NullPointerExceptionInspection */
            $this->validateRequest($operation, $request, $handler, $extraParams);
            if ($this->queryMetrics) {$this->queryMetrics->stopValidateTimeRequest();}

            if ($this->queryMetrics) {$this->queryMetrics->startConvertTimeRequest();}
            $convertedRequest = $this->convertFromAPI($request, $handler, $extraParams);
            if ($this->queryMetrics) {$this->queryMetrics->stopConvertTimeRequest();}

            if ($this->queryMetrics) {$this->queryMetrics->startOpTime();}
            /** @noinspection NullPointerExceptionInspection */
            $queryResponse = $this->executeQuery($handler, $convertedRequest, $extraParams);
            if ($this->queryMetrics) {$this->queryMetrics->stopOpTime();}

            if ($this->queryMetrics) {$this->queryMetrics->startConvertTimeResponse();}
            $response = $this->convertToAPI($queryResponse, $handler, $extraParams);
            if ($this->queryMetrics) {$this->queryMetrics->stopConvertTimeResponse();}
        } catch(Throwable $t) {
            $this->queryMetrics->stopValidateTimeRequest();
            $this->queryMetrics->stopConvertTimeRequest();
            $this->queryMetrics->stopOpTime();
            $this->queryMetrics->stopConvertTimeResponse();
            throw $t;
        }

        return $response;
    }

    /**
     * @param SchemaHandler $handler
     *
     * @return FilterViewService
     * @throws APIException
     * @throws FilterViewFault
     * @throws IAException
     */
    private function getFVService(SchemaHandler $handler) : FilterViewService
    {
        if ( RegistryLoader::$doTimers) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $timer = new ScopeTimer(__CLASS__, false, 'RegistryLoader::logTiming', __METHOD__);
        }
        $object = $handler->getObjectName();
        if (!array_key_exists($object, self::$fvServices)) {
            $fvService = new FilterViewService($handler->getMappedToName(), $handler->getDocType(),
                                               null, new APIFilterViewDialect());
            $fvService->setFilterViewValidator((new APIFilterValidator())->setMetric($this->queryMetrics));
            self::$fvServices[$object] = $fvService;
        }
        return self::$fvServices[$object];
    }

    /**
     * Initiates both IAMetrics & PerfData for Query flow
     * This method should be invoked at the top of internalExecute
     *
     * @param array|null $extraParams
     */
    private function initMetrics(?array $extraParams = [])
    {
        $this->perfData = new PerfDataForAPI();
        $this->perfData->setStartInfo(Globals::$g->perfdata);
        $this->perfData->setXmlpartnerid(
            $extraParams[APIConstants::API_EXTERNAL_PARAMETER][APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][APIConstants::API_EXTRA_PARAM_CONTROL_PARAM_SENDER_ID]??
            PerfDataForAPI::NOT_APPLICABLE);
        Globals::$g->perfdata->incrementFuncblkCount();
        $this->queryMetrics = new MetricNextGenAPIQueryService();
        $this->queryMetrics->startTime();
        $this->queryMetrics->setOperation('');
        $this->queryMetrics->setObject('');
    }

    /**
     * @param array|null    $request
     * @param array|null    $response
     * @param APIError|null $apiError
     */
    private function flushMetrics($request, $response, $apiError)
    {
        if (!$this->queryMetrics) {return;}

        // publish metrics
        $this->queryMetrics->stopTime();
        $this->queryMetrics->setOperation(APIConstants::API_QUERY);
        $this->queryMetrics->setVersion($this->version);
        $this->queryMetrics->setStatus(APIProfilingUtil::METRICS_RESULT_STATUS_SUCCESS);
        $this->queryMetrics->setObject($request[APIConstants::API_OBJECT_TYPE] ?? '');
        $this->queryMetrics->setAdapterCount(1 /** todo - there are ways of getting this, need Marina to decide */);
        $this->queryMetrics->setType(($this->isDynamicQuery) ?
                                         APIProfilingUtil::METRICS_RESULT_QUERY_TYPE_DYNAMIC :
                                         APIProfilingUtil::METRICS_RESULT_QUERY_TYPE_VIEW
        );
        if (isset($apiError)) {
            $this->queryMetrics->setStatus(APIProfilingUtil::METRICS_RESULT_STATUS_ERROR);
            $this->queryMetrics->setErrorCode($apiError->getCategory());
            $this->queryMetrics->setErrorMessage($apiError->getMessage());
        } else if ($response) {
            $this->queryMetrics->setStatus(APIProfilingUtil::METRICS_RESULT_STATUS_SUCCESS);
            $this->queryMetrics->setTotalCount($response[APIConstants::API_TOTAL_COUNT_KEY] ?? 0);
            $this->queryMetrics->setPageSize($response[APIQueryUtil::API_QUERY_RESULT_PAGE_SIZE] ?? 0);
            $this->queryMetrics->setStartWith($response[APIQueryUtil::API_PAGE_START] ?? 0);
        }
        $this->queryMetrics->publish();

        // publish perf data
        // todo - set domain
        if ($this->perfData) {
            $this->perfData->setProcessor(__CLASS__);
            $this->perfData->setVersion($this->version);
            $this->perfData->setFunctionName(APIConstants::API_QUERY);
            $this->perfData->setObject($request[APIConstants::API_OBJECT_TYPE] ?? '');
            $this->perfData->logCounters(Globals::$g->perfdata);
        }
    }

    /**
     * @param APIError[] $errors
     * @param string     $topMsg
     *
     * @throws APIException
     */
    public static function throwQueryErrorWithDetails(array $errors,
        string $topMsg = APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0115) : void
    {
        // TODO: $errors is an array of APIError
        $apiErr = APIError::getInstance($topMsg, []);
        foreach ( $errors as $error ) {
            $apiErr->addDetailError($apiErr->getCategory(), $error->getMessage(), '');
        }
        throw ( new APIException() )->setAPIError($apiErr);
    }

    /**
     * @param SchemaHandler $handler
     * @param array         $request
     * @param array         $extraParams
     *
     * @return SystemViewOrchestrator
     * @throws APIException
     */
    protected function getSystemViewOrch(SchemaHandler $handler, array $request, array &$extraParams) : SystemViewOrchestrator
    {
        $systemFilterOrch = $handler->getOrchestrator();
        assert($systemFilterOrch instanceof SystemViewOrchestrator);
        // a special handshake for $systemFilterOrch to know that it's an internal request
        $extraParams[APIConstants::API_QUERY] = $request;

        return $systemFilterOrch;
    }

    /**
     * @return array
     */
    public function getObjFieldMap() : array
    {
        return $this->objFieldMap;
    }

    /**
     * @param string $objectName api object name
     * @return bool true if it is a remote object
     * @throws APIException not able to get the handler for remote object
     */
    public function isRemoteObject(string $objectName) : bool
    {
        $registry = $this->handler->getRegistryInstance();
        return $registry->getRuntimeOwner($objectName) !== RegistryLoader::DEFAULT_OWNER;
    }
}

/**
 * Class APIFilterViewDialect
 */
class APIFilterViewDialect implements FilterViewDialect
{

    /**
     * @inheritDoc
     */
    public function preprocessQueryParams(array $queryParams)
    {
        return $queryParams;
    }

    /**
     * @inheritDoc
     */
    public function asQueryParams(array $queryParams)
    {
        return $queryParams;
    }

    /**
     * @inheritDoc
     */
    public function singleExpressionFilter2GetListFilter(array $filter, string $baseDate = null)
    {
        $apiError = APIError::getInstance(
            APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0005, [], false);
        throw (new APIInternalException())->setAPIError($apiError);
    }

    /**
     * @inheritDoc
     */
    public function addPaginationToResponse(array &$response, array $queryParams)
    {
        // do nothing here
    }

    /**
     * @inheritDoc
     */
    public function getContext(array $queryParams)
    {
        return '';
    }

    /**
     * @inheritDoc
     */
    public function skipAuthorizationChecks() : bool
    {
        return true;
    }

}

/**
 * Class APIFilterViewValidator
 */
class APIFilterValidator extends FilterViewValidator
{
    /**
     * @var MetricNextGenAPIQueryService $queryMetrics
     */
    private $queryMetrics = null;

    /**
     * @inheritDoc
     */
    public function validateQueryParams(array $queryParams)
    {
        // TODO: reuse filter validations when filter is converted from API

        $ok = true;
        // TBD: expression is parsed twice - to validate and to convert
        if (isset($queryParams[FilterViewService::EXPRESSION_KEY])) {
            $ok = $this->validateExpression($queryParams);
        }

        return $ok;
    }
    /**
     * @inheritDoc
     */
    protected function validateSelectFields(array $queryableFields, array $selects)
    {
        // This is a solution to bypass restriction on related object fields
        return true;
    }

    /**
     * todo - a temp workaround, needs to review with Valer
     * @param MetricNextGenAPIQueryService $queryMetrics
     *
     * @return $this
     */
    public function setMetric($queryMetrics): APIFilterValidator
    {
        $this->queryMetrics = $queryMetrics;
        return $this;
    }

    /**
     * start MetricNextGenAPIQueryService timer for measuring Query cost at ORM layer
     */
    public function startTimer()
    {
        if ($this->queryMetrics) {$this->queryMetrics->startOrmTime();}
    }

    /**
     * start MetricNextGenAPIQueryService timer for measuring Query cost at ORM layer
     */
    public function stopTimer()
    {
        if ($this->queryMetrics) {$this->queryMetrics->stopOrmTime();}
    }
}
