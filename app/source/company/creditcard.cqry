<?

$kcreditcardQueries['QRY_CREDITCARD_ALL'] = array(
        'QUERY' => 'SELECT creditcard.cardid,creditcard.description,creditcard.cardnum,creditcard.cardtype,creditcard.exp_month,creditcard.exp_year,creditcard.commcard,MAILADDRESS.ADDR1,MAILADDRESS.ADDR2,MAILADDRESS.ADDR3,mailaddress.city,mailaddress.state,mailaddress.zip,mailaddress.country,mailaddress.countrycode,creditcard.status,liabacct.acct_no,feesacct.acct_no as feesacct_no,creditcard.vendorid,dept.dept_no,location.location_no,creditcard.liabilitytype,creditcard.recdate,creditcard.recbal,creditcard.inpdate,creditcard.inpbal,journal.symbol,company2.cckey,feesacctlabel.label,finchrgacct.acct_no as finchrgacct_no,finchrgacctlabel.label, creditcard.locationkey as location,opcardsubscription.outsourcecard,opcardsubscription.automatecardpymtsend,opcardsubscription.automatecardpymtconf,opcardsubscription.amex_payve_accountid FROM creditcard creditcard,financialaccount financialdata,gljournal journal,mailaddress mailaddress,glaccount liabacct,glaccount feesacct,accountlabel feesacctlabel,glaccount finchrgacct,accountlabel finchrgacctlabel,department dept,opsubscription opcardsubscription,location location,company2 company2 WHERE financialdata.journalkey = journal.record#  (+)   and journal.cny# (+) = ?  and creditcard.cardid = financialdata.entity   and financialdata.cny# (+) = ?  and creditcard.mailaddrkey = mailaddress.record#   and mailaddress.cny# (+) = ?  and creditcard.liabacctkey = liabacct.record#  (+)   and liabacct.cny# (+) = ?  and creditcard.feesacctkey = feesacct.record#  (+)   and feesacct.cny# (+) = ?  and creditcard.feesacctlabelkey = feesacctlabel.record#  (+)   and feesacctlabel.cny# (+) = ?  and creditcard.finchrgacctkey = finchrgacct.record#  (+)   and finchrgacct.cny# (+) = ?  and creditcard.finchrgaclabel# = finchrgacctlabel.record#  (+)   and finchrgacctlabel.cny# (+) = ?  and creditcard.dept# = dept.record#  (+)   and dept.cny# (+) = ?  and creditcard.location# = location.record#  (+)   and location.cny# (+) = ?  and creditcard.cardid = company2.cckey  (+)   and company2.cny# (+) = ?  and creditcard.cny# (+) = ? and creditcard.cardid = opcardsubscription.cardid  (+)   and opcardsubscription.cny#  (+)  = ?',
        'ARGTYPES' => array('integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer')
);//added by paul...


$kcreditcardQueries['QRY_CREDITCARD_BYTYPE'] = array(
        'QUERY' => 'SELECT creditcard.cardid,creditcard.description,creditcard.cardnum,creditcard.cardtype,creditcard.exp_month,creditcard.exp_year,creditcard.commcard,MAILADDRESS.ADDR1,MAILADDRESS.ADDR2,MAILADDRESS.ADDR3,mailaddress.city,mailaddress.state,mailaddress.zip,mailaddress.country,mailaddress.countrycode,creditcard.status,liabacct.acct_no,feesacct.acct_no as feesacct_no,creditcard.vendorid,dept.dept_no,location.location_no,creditcard.liabilitytype,creditcard.recdate,creditcard.recbal,creditcard.inpdate,creditcard.inpbal,journal.symbol,company2.cckey,feesacctlabel.label,finchrgacct.acct_no as finchrgacct_no,finchrgacctlabel.label, creditcard.locationkey as location,opcardsubscription.outsourcecard,opcardsubscription.automatecardpymtsend,opcardsubscription.automatecardpymtconf,opcardsubscription.amex_payve_accountid FROM creditcard creditcard,financialaccount financialdata,gljournal journal,mailaddress mailaddress,glaccount liabacct,glaccount feesacct,accountlabel feesacctlabel,glaccount finchrgacct,accountlabel finchrgacctlabel,department dept,opsubscription opcardsubscription,location location,company2 company2 WHERE creditcard.liabilitytype = ?  and  financialdata.journalkey = journal.record#  (+)   and journal.cny# (+) = ?  and creditcard.cardid = financialdata.entity   and financialdata.cny# (+) = ?  and creditcard.mailaddrkey = mailaddress.record#   and mailaddress.cny# (+) = ?  and creditcard.liabacctkey = liabacct.record#  (+)   and liabacct.cny# (+) = ?  and creditcard.feesacctkey = feesacct.record#  (+)   and feesacct.cny# (+) = ?  and creditcard.feesacctlabelkey = feesacctlabel.record#  (+)   and feesacctlabel.cny# (+) = ?  and creditcard.finchrgacctkey = finchrgacct.record#  (+)   and finchrgacct.cny# (+) = ?  and creditcard.finchrgaclabel# = finchrgacctlabel.record#  (+)   and finchrgacctlabel.cny# (+) = ?  and creditcard.dept# = dept.record#  (+)   and dept.cny# (+) = ?  and creditcard.location# = location.record#  (+)   and location.cny# (+) = ?  and creditcard.cardid = company2.cckey  (+)   and company2.cny# (+) = ?  and creditcard.cny# (+) = ? and creditcard.cardid = opcardsubscription.cardid  (+)   and opcardsubscription.cny#  (+)  = ?',
        'ARGTYPES' => array('text', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer', 'integer')
);//added by paul...


$kcreditcardQueries['QRY_CREDITCARD_CCPAYMENTRECORDS'] = array(
        'QUERY' => 'SELECT * FROM ccpaymentrecords WHERE paymentkey like ? and cny# = ?',
        'ARGTYPES' => array('text', 'integer')
);//added by paul...

$kcreditcardQueries['QRY_CREDITCARD_HASVENDOR'] = array(
        'QUERY' => 'SELECT count(*) as count FROM creditcard WHERE vendorid = ? and cny# = ?',
        'ARGTYPES' => array('text', 'integer')
);

