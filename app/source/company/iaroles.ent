<?
/**
 *    FILE:        iaroles.ent
 *    AUTHOR:        rpn
 *    DESCRIPTION:    entity for iaroles
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/

require_once 'backend_module.inc';

$kSchemas['iaroles'] = array (
    'object' => array (
         'RECORDNO',
         'NAME',
         'DESCRIPTION',
         'TYPE',
    ),
    'schema' => array (
         'RECORDNO' => 'record#',
         'NAME' => 'name',
         'DESCRIPTION' => 'description',
         'TYPE' => 'type',
    ), 
    'fieldinfo' => array(
        $gRecordNoHiddenFieldInfo,
        array (
            'path'        => 'NAME',
            'desc'        => 'IA.NAME',
            'fullname'    => 'IA.NAME',
            'required'    => true,
            'type' => array (
                'type'        => 'text',
                'ptype'        => 'text',
                'size'        => 20,
                'maxlength'    => 20,
            ),

        ),
        array (
            'path'        => 'DESCRIPTION',
            'desc'        => 'IA.DESCRIPTION',
            'fullname'    => 'IA.DESCRIPTION',
            'required'    => false,
            'type' => array (
                'type'        => 'multitext',
                'ptype'        => 'multitext',
                'size'        => 20,
                'maxlength'    => 200,
            )
        ),
        array (
            'path'        => 'TYPE',
            'desc'        => 'IA.ROLE_FOR_USER_ON',
            'fullname'    => 'IA.ROLE_FOR_USER_ON',
            'type' => array (
                'ptype' => 'radio', 
                'type' => 'radio',
                'validlabels' => array ('IA.CONSOLE', 'IA.MULTI_ENTITY_DISTRIBUTED', 'IA.MULTI_ENTITY_SHARED', 'IA.ENTERPRISE'),
                'validvalues' => array ('E-Console', 'Multi Entity Distributed', 'Multi Entity Shared', 'Enterprise'),
                '_validivalues' => array(EP_CONSOLE, MULTIENTITY_DISTRIBUTED, MULTIENTITY_SHARED, ENTERPRISE),
                '_disabled' => array(EP_CONSOLE, MULTIENTITY_DISTRIBUTED, MULTIENTITY_SHARED, ENTERPRISE),
                'default' => ENTERPRISE,
            ),
            'layout' => 'landscape',
            'required'    => true,
        ),
    ),
    //'autoincrement' => 'RECORDNO',
    'table' => 'iaroles',
    'vid'     => 'RECORDNO',
    'printas'    => 'IA.IA_ROLE',
    'pluralprintas' => 'IA.IA_ROLES',
    'module'     => 'co',
);
