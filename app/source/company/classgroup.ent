<?php

/**
 * Entity file for Class Group
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */
/**
 * Entity file
 */
$kSchemas['classgroup'] = array(
    'children' => array(
        'glacctgrp' => array(
            'fkey' => 'name', 
            'invfkey' => 'name',
            'table' => 'glacctgrp', 
            'join' => 'outer',
            'filter' => " glacctgrp.membertype(+) = 'CLS' ",
        )        
    ),    
    'object' => array(
        'RECORDNO',
        'ID',
        'NAME',
        'DESCRIPTION',
        'GROUPTYPE',
        'MEMBERFILTERS',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'DIMGRPCOMP',
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'ID' => 'id',
        'NAME' => 'name',
        'DESCRIPTION' => 'description',
        'GROUPTYPE' => 'grouptype',
        'MEMBERFILTERS' => 'memberfilters',
        'WHENCREATED' => 'whencreated', 
        'WHENMODIFIED' => 'whenmodified', 
        'CREATEDBY' => 'createdby', 
        'MODIFIEDBY' => 'modifiedby',
        'DIMGRPCOMP' => 'glacctgrp.name'
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'GROUPKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'classgrpmember',
            'path' => 'MEMBERS',            
        )
    ),    
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENMODIFIED',
        'WHENCREATED',
    ),    
    'fieldinfo' => array(
        array(
            'fullname' => 'IA.RECORD_NUMBER',
            'desc' => 'IA.RECORD_NO',
            'path' => 'RECORDNO',
            'type' => array('ptype' => 'text', 'type' => 'text',
                'maxlength' => 8, 'format' => '/[\w\s_\-\.]{0,8}/'),
            'hidden' => true,
            'id' => 1
        ),
        array(
            'fullname' => 'IA.CLASS_GROUP_ID',
            'required' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => $gLocationIDFormat,
            ),
            'desc' => 'IA.UNIQUE_IDENTIFIER',
            'renameable' => true,
            'path' => 'ID',
            'id' => 2
        ),
        array(
            'fullname' => 'IA.NAME',
            'required' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
            ),
            'desc' => 'IA.NAME',
            'path' => 'NAME',
            'id' => 3
        ),
        array(
            'fullname' => 'IA.DESCRIPTION',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'multitext',
                'maxlength' => 500,
            ),
            'desc' => 'IA.DESCRIPTION',
            'path' => 'DESCRIPTION',
            'id' => 4
        ),
        array(
            'fullname' => 'IA.GROUP_TYPE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.ALL_MEMBERS', 'IA.SPECIFIC_MEMBERS'),
                'validvalues' => array('ALL', 'SPECIFIC'),
                '_validivalues' => array('A', 'S'),
            ),
            'default' => 'ALL',
            'desc' => 'IA.GROUP_TYPE',
            'path' => 'GROUPTYPE',
            'id' => 5
        ),
        array(
            'fullname' => 'IA.SORT_ORDER',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.ASCENDING', 'IA.DESCENDING'),
                'validvalues' => array('ASC', 'DESC'),                
            ),
            'default' => 'ASC',
            'desc' => 'IA.SORT_ORDER',
            'path' => 'SORTORDER',
            'id' => 6
            
        ),
        array(
            'fullname' => 'IA.MAX_MATCHES',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'size' => 7,
                'maxlength' => 4,
            ),
            'desc' => 'IA.RESTRICT_TO',
            'path' => 'RESTRICTTO',
        ),        
        array(
            'fullname' => 'IA.SORT_FIELD',
            'type' => array(
                'ptype' => 'webcombo',
                'type' => 'webcombo',
                'size' => 30
            ),
            'desc' => 'IA.SORTING_FIELD',
            'path' => 'SORTFIELD',
        ),        
        array(
            'fullname' => 'IA.FIELD',
            'type' => array(
                'ptype' => 'webcombo',
                'type' => 'webcombo',
                'size' => 30
            ),
            'desc' => 'IA.FIELD',
            'path' => 'FIELD',
        ),
        array(
            'fullname' => 'IA.OPERATOR',
            'type' => array(
                'ptype' => 'webcombo',
                'type' => 'webcombo',
                'size' => 10,
            ),
            'desc' => 'IA.OPERATOR',
            'path' => 'OPERATOR',
        ),
        array(
            'fullname' => 'IA.VALUE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'size' => 32
            ),
            'desc' => 'IA.VALUE',
            'path' => 'VALUE',
        ),
        array(
            'fullname' => 'IA.CONDITION',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'desc' => 'IA.CONDITION',
            'path' => 'CONDITION',
        ),        
        array(
            'fullname' => 'IA.CONDITION_TYPE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',                
                'validlabels' => array('IA.ALL_AND', 'IA.ANY_OR', 'IA.EXPRESSION'),
                'validvalues' => array('AND', 'OR', 'EXPRESSION'),
            ),
            'default' => 'AND',
            'desc' => 'IA.CONDITION_TYPE',
            'path' => 'CONDITIONTYPE',
        ),                
       array(
            'fullname' => 'IA.DIMENSION_STRUCTURE',
            'type' => array(
                'ptype' => 'boolean',
                'type' => 'boolean',                
            ),
            'desc' => 'IA.CREATE_REPORT_STRUCTURE',
            'path' => 'CREATEDIMCOMP',
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ),
    'printas' => 'IA.CLASS_GROUP',
    'pluralprintas' => 'IA.CLASS_GROUPS',
    'auditcolumns' => true,
    'table' => 'classgroup',
    'module' => 'co',
    'autoincrement' => 'RECORDNO',
    'vid' => 'ID',
    'renameable' => true,
    'nochatter' => true,
    'api' =>array(
        'GETNAME_BY_GET' => true,
        'GET_BY_GET' => true,
        'PERMISSION_MODULES' => array('co'),
        'PERMISSION_CREATE' => 'ALL',
        'PERMISSION_UPDATE' => 'ALL',
        'PERMISSION_DELETE' => 'ALL',
    ),    
    'nameFields' => [ 'NAME', 'ID' ],
);

