<?php

/**
 * Class used to address updates from dropbox, using refresh tokens for getting the access token
 *
 * <AUTHOR> B. <<EMAIL>>
 * @copyright 2020 Intacct Corporation All, Rights Reserved
 */

use \Kunnu\Dropbox\Authentication\OAuth2Client;

class DropboxLocal extends \Kunnu\Dropbox\Dropbox
{
    /**
     * DropboxLocal constructor.
     *
     * @param \Kunnu\Dropbox\DropboxApp $app
     * @param array                     $config
     */
    public function __construct(\Kunnu\Dropbox\DropboxApp $app, array $config = [])
    {
        parent::__construct($app, $config);
    }
    
    /**
     * Get OAuth2Client.
     * Overriding method functionality to use the custom DropboxOAuth2Client which is used to treat the refresh_token
     * update from Dropbox.
     *
     * @return \Kunnu\Dropbox\Authentication\OAuth2Client
     */
    public function getOAuth2Client(): OAuth2Client
    {
        if (!$this->oAuth2Client instanceof OAuth2Client) {
            return new DropboxOAuth2Client(
                $this->getApp(),
                $this->getClient(),
                $this->getRandomStringGenerator()
            );
        }
        
        return $this->oAuth2Client;
    }
}