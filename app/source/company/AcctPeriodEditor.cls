<?
import('Editor');

class AcctPeriodEditor extends Editor
{
    /* @var bool $Transactions*/
    var $Transactions;

    /**
     * User info specific tokens
     * @var string[]
     */
    protected $additionalTokens = [
        'IA.SAVE',
        'IA.CANCEL',
        'IA.DUPLICATE',
        'IA.SAVEANDNEW',
        'IA.QUARTERS_PERIODS_NEED_TO_BE_CONSECUTIVE',
        'IA.TO_PERIOD', 
        'IA.FROM_PERIOD',
        'IA.QUARTER1',
        'IA.QUARTER2',
        'IA.QUARTER3', 
        'IA.QUARTER4',
        'IA.JAN',
        'IA.FEB',
        'IA.MAR',
        'IA.APR',
        'IA.MAY',
        'IA.JUN',
        'IA.JUL',
        'IA.AUG',
        'IA.SEP',
        'IA.OCT',
        'IA.NOV',
        'IA.DEC',
        'IA.SU',
        'IA.MO',
        'IA.TU',
        'IA.WE',
        'IA.TH',
        'IA.FR',
        'IA.SA',
        'IA.SUN',
        'IA.MON',
        'IA.TUE',
        'IA.WED',
        'IA.THU',
        'IA.FRI',
        'IA.SAT',
    ];

    /**
     * @param string[] $_params
     */
    function __construct($_params)
    {
        $_params['helpfile'] = 'Adding_Editing_and_Viewing_Accounting_Period_Information';
        parent::__construct($_params);
    }

    /**
     * @param string[] $_params
     *
     * @return string[]
     */
    function Buttons_Instantiate($_params) 
    {
        switch ($_params['state']) {
        case Editor_ShowNewState:
            $dobutton = GT($this->textMap, 'IA.SAVE');
            $doaction = 'create';
            $cancelbutton = GT($this->textMap, 'IA.CANCEL');
            $cancelaction = 'cancel';
            $saveandnewbutton = GT($this->textMap, 'IA.SAVEANDNEW');
            $saveandnewaction = 'create';
            break;
        case Editor_ShowEditState:
            $dobutton = GT($this->textMap, 'IA.SAVE');
            $doaction = 'save';
            if (Globals::$g->gSecurity->IsOperationAllowed($_params['entity'], 'create')) {
                $copybutton = GT($this->textMap, 'IA.DUPLICATE');
                $copyaction = 'copy';
            }
            $cancelbutton = GT($this->textMap, 'IA.CANCEL');
            $cancelaction = 'cancel';
            break;
        case Editor_ShowViewState:
            $editbutton = GT($this->textMap, 'IA.SAVE');
            $editaction = 'edit';
            $cancelbutton = GT($this->textMap, 'IA.DONE');
            $cancelaction = 'cancel';
            break;
        }
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['dobutton']             = $dobutton;
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['doaction']             = $doaction;
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['copybutton']             = $copybutton;
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['copyaction']             = $copyaction;
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['saveandnewbutton']    = $saveandnewbutton;
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['saveandnewaction']    = $saveandnewaction;
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['cancelbutton']         = $cancelbutton;
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['cancelaction']         = $cancelaction;
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['editbutton']             = $editbutton;
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['editaction']             = $editaction;
        return $_params;
    }

    /**
     * @param string[] $_params
     * @param string|int $disable
     */
    function ShowDoButtons($_params, $disable = "") {
        $entityMgr = Globals::$g->gManagerFactory->getManager($this->_params['entity']);
        /* @var AcctPeriodManager $entityMgr*/
        $this->Transactions = $entityMgr->CheckForTransactions();

        $disable = ($this->Transactions) ? 1 : "";      
        parent::ShowDoButtons($_params, $disable);
    }

    /**
     * @param string[] $_params
     *
     * @return mixed
     */
    function ProcessEditAction(&$_params) 
    {
        $entityMgr = Globals::$g->gManagerFactory->getManager($_params['entity']);

        if ($this->ProcessErrorRetrivalAction($entityMgr)) {        
            return true;
        }

		Request::$r->_changed = false;
        $objId = Request::$r->{Globals::$g->kId};
        $obj = $this->getEntityData($_params['entity'], $objId);
        if (!$obj) {
            global $gErr;
            $entityDesc = $_params['entityDesc'];
            $gErr->addIAError("CO-0925", __FILE__ . ":" . __LINE__, "Fetching $entityDesc '$objId' failed", ['ENTITY' => $entityDesc, 'OBJECTID' => $objId]);
            $this->state = $this->kErrorState;
        }
        else {
            /* @var AcctPeriodManager $entityMgr*/
            $values = $entityMgr->GetMap($obj, $objId);
            Request::$r->SetCurrentObject($values);
            $this->state = $this->kShowEditState;
        }
        return null;
    }

    /**
     * @param string[] $_params
     */
    function ProcessViewAction(&$_params) 
    {
        $entityMgr = Globals::$g->gManagerFactory->getManager($_params['entity']);

        $objId = Request::$r->{Globals::$g->kId};
        $obj = $this->getEntityData($_params['entity'], $objId);

        if (!$obj) {
            $entityDesc = $_params['entityDesc'];
            global $gErr;
            $gErr->addIAError("CO-0925", __FILE__ . ":" . __LINE__, "Fetching $entityDesc '$objId' failed", ['ENTITY' => $entityDesc, 'OBJECTID' => $objId]);
            $this->state = $this->kErrorState;
        }
        else {
            /* @var AcctPeriodManager $entityMgr*/
            $values = $entityMgr->GetMap($obj, $objId);
            Request::$r->SetCurrentObject($values);
            $this->state = $this->kShowViewState;
        }
    }

    /**
     * @param array $fields
     */
    function ShowVerticalFieldLayout(&$fields) 
    {
        /* @var array $title */
        $title = array_slice($fields, 0, 1);
        /** @var array $title */
        $title = $title[0];
        /** @var array $showfields */
        $showfields = array_slice($fields, 1, 34);

        //  make fields readonly on edit mode based on the logic
        if (  $this->state == 'showedit' ) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $entityMgr = Globals::$g->gManagerFactory->getManager($this->_params['entity']);
            $title['readonly'] = true;            
            for( $i=0; $i < count($showfields); $i++ ){
                if($this->Transactions) {
                    $showfields[$i]['readonly'] = true;
                }
            }
        }
        ?>
    <CENTER>
      <TABLE border="0" cellpadding="2" cellspacing="0" width="50%">
      <?php
      if($this->Transactions) { ?>
      <tr>
      <td><?php UIUtils::ShowBottomFrameMessage('Intacct -- Report', 'You can\'t edit this Accounting Period as transactions were created between the following dates', true, 'left'); ?></td>
      </tr>
      <?php } ?>
       <TR>
      <TD align="center"><? $this->ShowSimpleFieldLabel($title); $this->ShowSimpleFieldValue($title); ?></TD>
		  </TR>
		  <TR>
      <TD>
	  			<TABLE border="1" cellpadding="2" cellspacing="0" width="90%">
					  <TR>
						<TD align="left">
                            <FONT color="#FF0000" size="1">
                                <?= I18N::getSingleToken('IA.QUARTER1'); ?>
                            </FONT>

                        </TD>
					</TR>
					<TR>
						<TD align="center">
				  			<TABLE border="2" cellpadding="2" cellspacing="0" width="90%">
								  <TR>
									<TD align="left">
										 <FONT size="1">
                                             <?= I18N::getSingleToken('IA.FROM_PERIOD'); ?>
                                         </FONT>
									</TD>
									<TD align="left">
										 <FONT size="1">
                                             <?= I18N::getSingleToken('IA.TO_PERIOD'); ?>
                                         </FONT>
									</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[0]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[1]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[2]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[3]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[4]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[5]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[6]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[7]); ?>
			 						</TD>
								  </TR>
				  			</TABLE>
 						</TD>
					  </TR>
	  			</TABLE>
      </TD>
		  </TR>
		  <TR>
      <TD>
	  			<TABLE border="1" cellpadding="2" cellspacing="0" width="90%">
					  <TR>
						<TD align="left">
							 <FONT color="#FF0000" size="1">
                                 <?= I18N::getSingleToken('IA.QUARTER2'); ?>
                             </FONT>
						</TD>
					</TR>
					<TR>
						<TD align="center">
				  			<TABLE border="2" cellpadding="2" cellspacing="0" width="90%">
								  <TR>
									<TD align="left">
										 <FONT size="1">
                                             <?= I18N::getSingleToken('IA.FROM_PERIOD'); ?>
                                         </FONT>
									</TD>
									<TD align="left">
										 <FONT size="1">
                                             <?= I18N::getSingleToken('IA.TO_PERIOD'); ?>
                                         </FONT>
									</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[8]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[9]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[10]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[11]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[12]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[13]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[14]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[15]); ?>
			 						</TD>
								  </TR>
				  			</TABLE>
 						</TD>
					  </TR>
	  			</TABLE>
      </TD>
		  </TR>
		  <TR>
      <TD>
	  			<TABLE border="1" cellpadding="2" cellspacing="0" width="90%">
					  <TR>
						<TD align="left">
							 <FONT color="#FF0000" size="1">
                                 <?= I18N::getSingleToken('IA.QUARTER3'); ?>
                             </FONT>
						</TD>
					</TR>
					<TR>
						<TD align="center">
				  			<TABLE border="2" cellpadding="2" cellspacing="0" width="90%">
								  <TR>
									<TD align="left">
										 <FONT size="1">
                                             <?= I18N::getSingleToken('IA.FROM_PERIOD'); ?>
                                         </FONT>
									</TD>
									<TD align="left">
										 <FONT size="1">
                                             <?= I18N::getSingleToken('IA.TO_PERIOD'); ?>
                                         </FONT>
									</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[16]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[17]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[18]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[19]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[20]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[21]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[22]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[23]); ?>
			 						</TD>
								  </TR>
				  			</TABLE>
 						</TD>
					  </TR>
	  			</TABLE>
      </TD>
		  </TR>
		  <TR>
      <TD>
	  			<TABLE border="1" cellpadding="2" cellspacing="0" width="90%">
					  <TR>
						<TD align="left">
							 <FONT color="#FF0000" size="1">
                                 <?= I18N::getSingleToken('IA.QUARTER4'); ?>
                             </FONT>
						</TD>
					</TR>
					<TR>
						<TD align="center">
				  			<TABLE border="2" cellpadding="2" cellspacing="0" width="90%">
								  <TR>
									<TD align="left">
										 <FONT size="1">
                                             <?= I18N::getSingleToken('IA.FROM_PERIOD'); ?>
                                         </FONT>
									</TD>
									<TD align="left">
										 <FONT size="1">
                                             <?= I18N::getSingleToken('IA.TO_PERIOD'); ?>
                                         </FONT>
									</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[24]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[25]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[26]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[27]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[28]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[29]); ?>
			 						</TD>
								  </TR>
								  <TR>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[30]); ?>
			 						</TD>
									<TD>
            <? $this->ShowSimpleFieldValue($showfields[31]); ?>
			 						</TD>
								  </TR>
				  			</TABLE>
 						</TD>
					  </TR>
	  			</TABLE>
      </TD>
		  </TR>
		  <TR>
      <TD>
          <FONT color="#FF0000" size="0">
              <?= I18N::getSingleToken('IA.QUARTERS_PERIODS_NEED_TO_BE_CONSECUTIVE'); ?>
          </FONT>

      </TD>
		  </TR>
      </TABLE>
    </CENTER>
        <?
    }

}


