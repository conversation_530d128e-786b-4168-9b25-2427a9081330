<?
import('NPicker');
class gllocationpickPicker extends NPicker
{


    function __construct()
    {
        $this->showroot = true;
        // for MRU picker
        $noroot = Request::$r->_noroot;
        if ($noroot) {
            $this->showroot = false;
        }
        parent::__construct(
            array(
            'entity'        =>  'gllocationpick',
            'fields'        =>  array('PICKID','STATUS'),
            'pickfield'        =>  'PICKID',
            'helpfile'        => '' // to-be-done
            )
        );
    }

    /**
     * @param array $qspec
     *
     * @return array|string
     */
    function BuildQuerySpec($qspec = [])
    {
        if (!$qspec) {
            $qspec = parent::BuildQuerySpec();        
        }
        
        if (!$this->showroot) {
            $subquery = array("select location_no||'--'||name ".
            "from location where cny# = ".GetMyCompany().
            " and isroot = 'T'");
            $qspec['filters'][0][] = array('PICKID',
            'NOT IN SUBQUERY', $subquery);
        }

        $sourceEntityId = Request::$r->_sourceEntityId;
        if($sourceEntityId != '') {

            $locKeys = InterEntitySetupManager::getAllTargetKeysForSourceId($sourceEntityId, true);

            if (!empty($locKeys)) {

                if(count($locKeys)>0) {
                    $qspec['filters'][0][] = array('ENTITYKEY', 'IN', $locKeys);    
                }
            }
        }
        
        return $qspec;
    }

    /**
     * @return array|string
     */
    function BuildQuerySpecAll()
    {
        return $this->BuildQuerySpec();
    }


    /**
     * @return string
     */
    function genGlobs()
    {
        $noroot          = Request::$r->_noroot;
        $_sourceEntityId = Request::$r->_sourceEntityId;

        $ret = parent::genGlobs();
        $ret .= "<g name='.noroot'>" . $noroot . "</g>";
        $ret .= "<g name='.sourceEntityId'>" . $_sourceEntityId . "</g>";

        return $ret;
    }


    /**
     * @return array
     */
    function GetQuerySpec()
    {
        $qspec = array(
        'selects' => array(
        'PICKID',
        'STATUS',
        ),
        'filters' => array(
        array(
        array(
                        'STATUS',
                        'IN',
                        array( 'active' ),
        ),
        ),
        ),
        'orders' => array(
        array(
        'PICKID',
        'asc',
        ),
        ),
        );
        return $qspec;
    }


}

