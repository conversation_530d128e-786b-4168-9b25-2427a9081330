<?php
import('NLister');
import("UserInfoManager");

class UsrMbrLister extends NLister
{
    /**
     * User group membership specific tokens
     * @var string[]
     */
    protected $additionalTokens = [
        'IA.USER_GROUP_MEMBERSHIPS_TITLE',
        'IA.REMOVE',
    ];
    
    /**
     * @var string $usrid
     */
    private $usrid;
    
    public function __construct()
    {
        parent::__construct(
            [
                'entity' => 'usrmbr',
                'title' => 'IA.USER_GROUPS',
                'fields' => ['NAME', 'DESCR', 'MEMTYPE'],
                'disablesort' => 'true',
                'disablefilter' => 'true',
                'disableedit' => 'true',
                'helpfile' => 'Viewing_Your_Group_Memberships',
            ]
        );
    
        $this->usrid = Request::$r->_usrid;
    }

    /**
     * Get raw table data
     *
     * @param array  $querySpec
     * @param string $querytype
     *
     * @return string[][]
     */
    function GetList($querySpec, $querytype = 'normal')
    {
        /* @var string[][] $ret */
        $ret = $this->getMbrList($querySpec);
        return $ret;
    }

    /**
     * @param array $querySpec
     * @param bool  $_crosscny
     * @param bool  $nocount
     *
     * @return array[]
     */
    function getMbrList($querySpec, $_crosscny = false, $nocount = true)
    {
        return $this->getEntityMgr()->GetList($querySpec, $_crosscny, $nocount);
    }

    /**
     * @param array $querySpec
     *
     * @return int
     */
    function GetCount($querySpec) 
    {
        return $this->getEntityMgr()->GetCount($querySpec);
    }

    /**
     * Build table
     */
    function BuildTable() 
    {
        parent::BuildTable();
        $this->_params['_fields'] =  ['NAME', 'DESCR', 'MEMTYPE'];
    }

    /**
     * @return string
     */
    function calcTitle() 
    {
        $user_im = new UserInfoManager();

        $r = $user_im->DoQuery(
            'QRY_USERINFO_SELECT_SINGLE_USER', 
            array( $this->usrid )
        );

        $usrname = $r[0]['LOGINID'];
    
        $token = [
            [
                'id' => 'IA.USER_GROUP_MEMBERSHIPS_TITLE',
                'placeHolders' =>
                    [
                        ['name' => 'USERNAME', 'value' => $usrname],
                    ]
            ],
        ];
        
        $result = I18N::getTokensForArray($token);
        
        $this->_params[ '_title' ] = GT($result, 'IA.USER_GROUP_MEMBERSHIPS_TITLE');
        return parent::calcTitle();
    }

    /**
     * @param int     $i
     * @param string  $owner
     * @param string  $ownerloc
     * @param bool    $ownedObj
     *
     * @return string
     */
    function calcDeleteURL($i, $owner = null, $ownerloc = null, $ownedObj = null)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $p = &$this->_params; //???
        $t = &$this->table;

        if ( $t[$i]['MEMTYPE'] != 'Assigned' ) {
            return "";
        }

        // What we really need is detaching a member ( $_usrid ) 
        // from the group ( pass with $_groupid )

        $grouprh = $t[$i]['RECORD#'];

        $op = GetOperationId('co/lists/usergroupmembers/delete');
        $dst = 'usergroupmember_del.phtml';
    
        $param = ".groupid=$grouprh&.rh=$this->usrid&.type=User&.op=$op";
        
        $text = GT($this->textMap, 'IA.REMOVE');

        //Add CSRF and make this a POST
        $href = $this->calcCSRFhref($this->calcBaseURL($dst, $param, $this->LCALL), $op);

        $ret = "<a $href " . $this->DLT() . ">" . $text . "</a>";

        return $ret;
    }


    /**
     * @return string
     */
    function calcAddDST() 
    {
        return "usrmbr_add.phtml?.usrid=$this->usrid";
    }
    
    /**
     * @return array
     */
    public function getSecureParameters(): array
    {
        $secureParams = parent::getSecureParameters();
        $secureParams[".usrid"] = Request::$r->_usrid;
        return $secureParams;
    }
}

