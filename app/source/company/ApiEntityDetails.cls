<?php

class ApiEntityDetails
{

    /**
     * @var string $entityId
     */
    private $entityId = '';

    /**
     * @return string
     */
    public function getEntityId()
    {
        return $this->entityId;
    }

    /**
     * @param string $entityId
     */
    public function setEntityId(string $entityId)
    {
        $this->entityId = $entityId;
    }

    /**
     * @var string $entityName
     */
    private $entityName = '';

    /**
     * @return string
     */
    public function getEntityName()
    {
        return $this->entityName;
    }

    /**
     * @param string $entityName
     */
    public function setEntityName(string $entityName)
    {
        $this->entityName = $entityName;
    }

    /**
     * @var string $recordNo
     */
    private $recordNo = '';

    /**
     * @return string
     */
    public function getRecordNo()
    {
        return $this->recordNo;
    }

    /**
     * @param string $recordNo
     */
    public function setRecordNo(string $recordNo)
    {
        $this->recordNo = $recordNo;
    }

    /**
     * @var string $currency
     */
    private $currency = '';

    /**
     * @return string|null
     */
    public function getCurrency()
    {
        return $this->currency;
    }
    
    /**
     * @param string|null $currency
     */
    public function setCurrency(string $currency = null)
    {
        $this->currency = $currency;
    }

    /**
     * @var string $firstFiscalMonth
     */
    private $firstFiscalMonth = '';

    /**
     * @return string|null
     */
    public function getFirstFiscalMonth()
    {
        return $this->firstFiscalMonth;
    }
    
    /**
     * @param string|null $firstFiscalMonth
     */
    public function setFirstFiscalMonth(string $firstFiscalMonth = null)
    {
        $this->firstFiscalMonth = $firstFiscalMonth;
    }

    /**
     * @var string $firstFiscalTaxMonth
     */
    private $firstFiscalTaxMonth = '';

    /**
     * @return string|null
     */
    public function getFirstFiscalTaxMonth()
    {
        return $this->firstFiscalTaxMonth;
    }
    
    /**
     * @param string|null $firstFiscalTaxMonth
     */
    public function setFirstFiscalTaxMonth(string $firstFiscalTaxMonth = null)
    {
        $this->firstFiscalTaxMonth = $firstFiscalTaxMonth;
    }

    /**
     * @var string $weekStart
     */
    private $weekStart = '';

    /**
     * @return string|null
     */
    public function getWeekStart()
    {
        return $this->weekStart;
    }
    
    /**
     * @param string|null $weekStart
     */
    public function setWeekStart(string $weekStart = null)
    {
        $this->weekStart = $weekStart;
    }

    /**
     * ApiEntityDetails constructor.
     */
    public function __construct()
    {
        // Nothing to see here
    }


    /**
     * This function is needed to create a PHP array that gets casted into XML
     *
     * @return array
     */
    public function serializeToIntacctXmlArray()
    {
        $arr = [
            'id' => [[ $this->getEntityId() ]],
            'name' => [[ $this->getEntityName() ]],
            'recordno' => [[ $this->getRecordNo() ]],
            'currency' => [[ $this->getCurrency() ]],
            'firstfiscalmonth' => [[ $this->getFirstFiscalMonth() ]],
            'firstfiscaltaxmonth' => [[ $this->getFirstFiscalTaxMonth() ]],
            'weekstart' => [[ $this->getWeekStart() ]],
        ];
    
        $accountingType = LocationEntityManager::GetEntityAccountingType($this->getRecordNo());
        if ($accountingType !== null) {
            $arr['accountingtype'] = [[$accountingType]];
        }

        return $arr;
    }
}
