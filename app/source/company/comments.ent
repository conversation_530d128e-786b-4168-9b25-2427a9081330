<?
/**
 *  FILE:           comments.ent
 *  AUTHOR:         Valer
 *  Table: PT_COMMENT
 *
 *  (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *  This document contains trade secret data that belongs to Intacct
 *  Corporation and is protected by the copyright laws.  Information
 *  herein may not be used, copied or disclosed in whole or in part
 *  without prior written consent from Intacct Corporation.
*/

$kSchemas['comments'] = array(
    /* children */
    /* maps foreign keys recursivly from the current table */
    /* fkey - column in this table
     * incfkey - column in joined table
     * join - type of join
     * children - the connections FROM the joined table or current table
     */
    'children' => array(
        'userinfo' => array('fkey' => 'created_by', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'userinfo', ),
        'userinfo2' => array('fkey' => 'updated_by', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'userinfo', ),
    ),
    // object

    /**
     * These are the display fields for the form. May be in the same tale
     * joined table or a static/calulated value
     */
    'object' => array(
        'RECORDNO',
        'OBJ_ID',
        'OBJ_DEF_ID',
        'COMMENT_TEXT',
        'CREATED_BY',
        'CREATEDBYUSER',
        'CREATED_AT',
        'UPDATED_BY',
        'UPDATEDBYUSER',
        'UPDATED_AT',
    ),

    'schema' => array(
        'RECORDNO'    => 'record#',
        'OBJ_ID'      => 'obj_id',
        'OBJ_DEF_ID'  => 'obj_def_id',
        'COMMENT_TEXT'  => 'comment_text',
        'CREATED_BY'  => 'created_by',  
        'CREATEDBYUSER' => 'userinfo.loginid',
        'CREATED_AT'  => 'created_at',
        'UPDATED_BY'  => 'updated_by',  
        'UPDATEDBYUSER' => 'userinfo2.loginid',
        'UPDATED_AT'  => 'updated_at',
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'CREATED_AT',
        'UPDATED_AT'
    ),

    'fieldinfo' => array(
        array(
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'hidden' => true,
            'type' => array(
                'type' => 'integer',
                'ptype' => 'sequence',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'id' => 1
        ),
        array(
            'path' => 'OBJ_ID',
            'fullname' => 'IA.PARENT_ID',
            'hidden' => true,
            'type' => array(
                'type' => 'integer',
                'ptype' => 'sequence',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'id' => 2
        ),
        array(
            'path' => 'OBJ_DEF_ID',
            'fullname' => 'IA.PARENT_OBJECT_ID',
            'hidden' => true,
            'type' => array(
                'type' => 'integer',
                'ptype' => 'sequence',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'id' => 8
        ),
        array(
            'path' => 'COMMENT_TEXT',
            'fullname' => 'IA.COMMENT',
            'type' => array(
                    'ptype' => 'text', 
                    'type' => 'text',
            ),
            'id' => 3
        ),

        array(
            'path' => 'CREATEDBYUSER',
            'fullname' => 'IA.CREATED_BY',
            'type' => array(
                    'ptype' => 'ptr', 
                    'type' => 'ptr',
            ),
            'renameable' => true,
            'id' => 4
        ),
        
        array(
            'path' => 'CREATED_AT',
            'fullname' => 'IA.TIME_CREATED',
            'type' => array(
                'ptype' => 'timestamp',
                'type' => 'timestamp',
                ),
            'renameable' => true,
            'id' => 5
        ),

        array(
            'path' => 'UPDATEDBYUSER',
            'fullname' => 'IA.UPDATED_BY',
            'type' => array(
                    'ptype' => 'ptr', 
                    'type' => 'ptr',
            ),
            'renameable' => true,
            'id' => 6
        ),

        array(
            'path' => 'UPDATED_AT',
            'fullname' => 'IA.TIME_UPDATED',
            'type' => array(
                'ptype' => 'timestamp',
                'type' => 'timestamp',
                ),
            'renameable' => true,
            'id' => 7
        ),
    ),

    'table' => 'pt_comment',
    'printas' => 'IA.COMMENT',
    'pluralprintas' => 'IA.COMMENTS',
    'vid' => 'RECORDNO',
    'module' => 'co',
    'renameable' => true,
    'autoincrement' => 'RECORDNO',
    'customerp' => array(
        'SLTypes' => array(
        ),
        'SLEvents' => array(
        ),
        'AllowCF' => false,
    ),
    
    'api' => array(
        'PERMISSION_MODULES' => array('co'),
        'PERMISSION_CREATE' => 'ALL',
        'PERMISSION_UPDATE' => 'ALL',
        'PERMISSION_DELETE' => 'ALL',
        'PERMISSION_READ' => 'ALL',
    ),
    'description' => 'IA.COMMENTS_RECORDED_ON_TRANSACTIONS_OR_COMMENTS_FOR',
);
