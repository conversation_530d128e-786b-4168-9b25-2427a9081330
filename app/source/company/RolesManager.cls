<?php
/**
 * Roles Manager
 *
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

import('UserPolicy');

require_once "AudUserTrailManager.cls";
require_once "UserRightsManager.cls";

/**
 * Class RolesManager
 */
class RolesManager extends EntityManager
{
    /**
     * @var string[] $_userInfo
     */
    var $_userInfo;
    /**
     * @var string $_app
     */
    var $_app = "";
    /**
     * @var string $_userrec
     * @obsolete?
     */
    var $_userrec = "";
    /**
     * @var string $_user_rec
     */
    var $_user_rec;
    /**
     * @var string $_practiceType
     */
    var $_practiceType = '';

    /**
     * @param array $params
     */
    public function __construct($params = array())
    {
        parent::__construct($params);

        // find user record#, cny and $app.
        $t = explode('@', Globals::$g->_userid);
        $this->_user_rec = $t[0];
        $this->_cny = $t[1] ?? null;
        $this->_app = $t[2] ?? null;

    }

    /**
     * @return string[][]|bool
     */
    function GetAllRoles() 
    {
        $roles = $this->_QM->DoQuery('QRY_ROLES_GETALL', array());
        return $roles;

    }

    /**
     * @return string[]
     */
    function GetRolesMap() 
    {
        $map = array();
        $roles = $this->GetAllRoles();
        foreach ( $roles as $value ) {
            $map[$value['NAME']] = $value['NAME'];
        }
        return $map;
    }

    /**
     * Add role
     *
     * @param array &$values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $source = __FILE__." : ".__LINE__;
        XACT_BEGIN($source);

        $ok = $this->Translate($values);
        $ok = $ok && parent::regularAdd($values);
        $ok = $ok && $this->CopyFromTemplate($values);
        $ok = $ok && $this->copyRolePermissions($values['PERMISSIONSFROMROLEKEY'], $values['RECORDNO']);

        //$ok = false;
        if (!$ok) {
            XACT_ABORT($source);
        } else {
            XACT_COMMIT($source);
        }

        return $ok;

    }

    public function processPermissionAllowedAccessRights(array &$permissionInfo): void
    {
        $userPolicy = new UserPolicy($permissionInfo['MODULE']);
        $userPolicy->updatePermissionsBasedOnModule($permissionInfo['MODULE']);
        $userPolicy->expandPolicy();
        $permissionInfo['ALLOWED_ACCESS_RIGHTS'] = $userPolicy->ListPolicyValues($permissionInfo['NAME']);
    }

    /**
     * @param array $role
     * @param string $module
     * @return bool
     */
    public function validateAllowedModules(array $role, string $module): bool
    {
        $allowedModules = $this->GetDefaultModules($role['key'] ?? '');
        if ($allowedModules === false) {
            return false;
        }

        foreach ($allowedModules as $allowedModule) {
            if ($allowedModule['KEY'] === $module) {
                return true;
            }
        }

        Globals::$g->gErr->addIAError('CO-0993', __FILE__ . ':' . __LINE__,
            "You cannot assign this permission because its module is not installed.");
        return false;
    }

    /**
     * Copy all the module permissions from from role to another role.
     *
     * <AUTHOR> Kumar <<EMAIL>>

     * @param int $sourceRoleKey  Source Role Record number
     * @param int $destRoleKey    Destination Role Record number
     * 
     * @return bool
     */
    protected function copyRolePermissions($sourceRoleKey, $destRoleKey)
    {
        global $gInstalledModules;
        $ok = true;
        if ( !empty($sourceRoleKey) && !empty($destRoleKey)) {
            $assignedModules = $this->GetModules($sourceRoleKey);
            $assignedModules = !is_array($assignedModules) ? array($assignedModules) : $assignedModules;
            
            $assignedModulesIds = array();
            $assignedPermissionKeys = array();
            $assignedPtModPerms     = array();
            foreach($assignedModules as $module){
                if ( !in_array($module['MODULE'], $assignedModulesIds) ) {
                    $assignedModulesIds[] = $module['MODULE'];
                    $assignedPermissionKeys[] = $module['PERMKEY'];
                }
            }
            
            // add custom modules
            if ( is_array($gInstalledModules) ) {
                foreach ($gInstalledModules as $moduleDetails) {
                    if ( $moduleDetails['ISCUSTOMMODULE'] == true ) {
                        $tmpModId = $moduleDetails['MODULEID'];
                        $pol      = new RoleCustomPolicy($tmpModId, $sourceRoleKey);
                        if ( $pol->userHasModulePermission() ) {
                            $assignedModulesIds[]     = $tmpModId;
                            $assignedPermissionKeys[] = $moduleDetails['PERMKEY'];
                            // Find out valid policies
                            $tmpPolicyList = $pol->GetUserModulePolicies();
                            foreach ( $tmpPolicyList as $policyName => $policyValue ) {
                                $nameStr = explode("--", $policyName);
                                $pk = $pol->GetPolicyKey($nameStr[1], $nameStr[0]);
                                if ( empty($pk) ) {
                                    unset($tmpPolicyList[$policyName]);
                                }
                            }
                            $assignedPtModPerms[$tmpModId] = $tmpPolicyList;
                        }
                    }
                }
            }
            
            foreach ($assignedModulesIds as $moduleKey) {
                $isCustomRole = (isl_strpos($moduleKey, 'PT_') === 0) ? true : false;
                if ( $isCustomRole ) {
                    $modulePermissions = $assignedPtModPerms[$moduleKey];
                } else {
                    $modulePermissions = $this->GetSavedRolePerms($sourceRoleKey, $moduleKey);
                }
                // save permission of each module
                $ok = $ok && $this->SetObjectPolicies($destRoleKey, $moduleKey, $modulePermissions, $isCustomRole);
            }
            // Save module details
            $ok = $ok && $this->SetModulePolicies($destRoleKey, $assignedPermissionKeys);
        }
        return $ok;
    }

    /**
     * beforeUpdateEntries
     *
     * @param string $path
     * @param OwnedObjectManager &$lineManager
     * @param int $parentKey
     * @param array &$values
     * @param array &$oldRecNumbers
     *
     * @return bool
     */
    protected function beforeUpdateEntries($path, &$lineManager, &$parentKey, &$values, &$oldRecNumbers) : bool
    {
        if (isset($values['ROLE_USERS'])) {
            unset($values['ROLE_USERS']);
        }
        if (isset($values['ROLE_USER_GROUPS'])) {
            unset($values['ROLE_USER_GROUPS']);
        }

        return parent::beforeUpdateEntries($path, $lineManager, $parentKey, $values, $oldRecNumbers);
    }

    /**
     * Update a Role
     *
     * @param array &$values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {

        $source = __FILE__." : ".__LINE__;
        XACT_BEGIN($source);

        $ok = $this->Translate($values);
        $ok = $ok && parent::regularSet($values);

        if (!$ok) {
            XACT_ABORT($source);
        } else {
            XACT_COMMIT($source);
        }

        return $ok;

    }


    /**
     * Translate record
     *
     * @param array &$values
     *
     * @return bool
     */
    function Translate(&$values)
    {

        // type field should be visible only in practice companies
        // so for non-practice companies lets set the default type
        // set default only if $values[''TYPE'] already not set
        if ( empty($values['TYPE']) && !in_array($this->_app, array('C', 'CM', 'M')) ) {

            $type = $this->GetRoleLabelsForApp();
            if ( count($type) == 1) {
                $values['TYPE'] = $type[0];
            } else {
                global $gErr;
                $msg = "Found more than one Role type for this company type " . $this->_app;
                $gErr->addIAError('CO-0463', __FILE__ . ':' . __LINE__, $msg, ['TYPE' => $this->_app]);
                return false;
            }

        }

        return true;
    }


    /**
     * @param array &$values
     *
     * @return bool
     */
    function CopyFromTemplate(&$values) 
    {

        $ok = true;
        $myrolekey = $values[':RECORDNO'];

        if ( $myrolekey != '' && $values['TEMPLATE'] != '' ) {
            global $gManagerFactory, $gErr;
            $IARolesMgr = $gManagerFactory->getManager('iaroles');
            $templateRole = $IARolesMgr->GetByName($values['TEMPLATE']);

            if ( $templateRole['TYPE'] != $values['TYPE'] ) {
                $msg = "Role type is not the same as Selected Template Type.";
                $gErr->addIAError('CO-0464', __FILE__ . ':' . __LINE__, $msg);
                return false;
            } else {
                $cny = GetMyCompany();
                $iarolekey = $templateRole['RECORD#'];
                $stmt = "insert into rolepolicyassignment (rolekey,module,policykey,policyval,cny#) ".
                            "select $myrolekey, module,policykey,policyval, $cny from iapolicyassignment ".
                            "where iarolekey =  $iarolekey ";
                $ok = ExecStmt(array($stmt));
            }

        }

        return $ok;

    }


    /**
     * @return bool
     */
    function Validate() 
    {
        global $gErr, $_userid;
        
        $_op = Request::$r->_op;
        // find user record#, cny and $app.
        list($this->_user_rec, $this->_cny,  $this->_app) = explode('@', $_userid);

        GetNObject('userinfo', $this->_user_rec, $this->_userInfo);

        if ( in_array($this->_app, array('C', 'E')) ) {
            $this->_practiceType = 'cpa';
        } else if ($this->_app == 'M') {
            $this->_practiceType = 'mprac';
        }

        // does user exists
        if ( $this->_userInfo == '' ) {
            $msg = "Invalid user";
            $gErr->addIAError('CO-0465', __FILE__ . ':' . __LINE__, $msg);
            return false;
        }

        // is the access to subsriptions page with proper permissions
        // valid this only for non-practice company
        if ( $this->_app == 'A'
            && $_op != GetOperationId('co/lists/roles/view/permissions')
            && $_op != GetOperationId('mp/lists/roles/view/permissions')
        ) {
            $msg = "Invalid Params. Could not open Subsriptions/Permissions page";
            $gErr->addIAError('CO-0466', __FILE__ . ':' . __LINE__, $msg);
            return false;
        }

        return true;

    }


    /**
     * @param string $name
     *
     * @return bool|string[]
     */
    function GetByName($name) 
    {
        $resultSet = $this->_QM->DoQuery('QRY_ROLES_SELECT_BY_NAME', array( $name ));
        if ( $resultSet && count($resultSet) ) {
            return $resultSet[0];
        } else {
            return $resultSet;
        }
    }


    /**
     * @param int $key
     *
     * @return bool|string[]
     */
    function GetByRecNo($key) 
    {
        $resultSet = $this->_QM->DoQuery('QRY_ROLES_SELECT_BY_RECORDNO', array( $key ));
        if ( $resultSet && count($resultSet) ) {
            return $resultSet[0];
        } else {
            return $resultSet;
        }
    }


    /**
     * @param int $_rolekey
     *
     * @return bool|string[][]
     */
    function GetModules($_rolekey) 
    {
        global $kIAModules;

        // find all the exsting policy assignments for the given irolekey
        $pols = $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_GETALL', array( $_rolekey ));

        if ( is_array($pols)) {
            foreach ($pols as $polkey => $pol) {
                foreach ( $kIAModules as $module) {
                    if ($module['PERMKEY'] && $module['SYMBOL'] == $pol['MODULE']) {
                        $pols[$polkey]['PERMKEY'] = $module['PERMKEY'];
                    }
                }
            }
        }
        return $pols;

    }


    /**
     * @param int    $rolekey
     * @param string $modkey
     *
     * @return string[]
     */
    function GetSavedRolePerms($rolekey, $modkey) 
    {

        $dataH = array();

        // find all the exsting policy assignments for the given irolekey
        $resultSet = $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_GET_SAVED_ROLEPERMS', array( $rolekey, $modkey ));

        foreach ( $resultSet as $row ) {
            $name = $row['NAME'];
            $value = $row['POLICYVAL'];
            $dataH[$name] = $value;
        }

        // This code provides a temporary fix for replacing a typo ('Recocile') stored in the database
        // with the correct version ('reconcile') retrieved from the 'inv.pol' file.
        // It should be removed once all users have the updated value in the database.
        $oldTypoPermissionName = 'Recocile';
        $newUpdatedPermissionName = 'reconcile';
        if ($modkey === 'inv' && isset($dataH['Cycle Counts']) && str_contains($dataH['Cycle Counts'], $oldTypoPermissionName)) {
            // Replace the typo with the corrected version
            $dataH['Cycle Counts'] = str_replace($oldTypoPermissionName, $newUpdatedPermissionName, $dataH['Cycle Counts']);
        }

        return $dataH;

    }


    /**
     * @param int    $_rolekey
     * @param string $role_type
     *
     * @return array[][]|false
     */
    function GetDefaultModules($_rolekey, $role_type='') 
    {

        // is current company a practice company?
        global $_userid;
        $practicetype = GetPracticeType($_userid);
        $IsPracticeCompany = ( in_array($practicetype, array('C', 'M')) ? true : false );

        if ( $IsPracticeCompany ) {
            $pols = $this->GetDefaultModulesForConsole($_rolekey, $role_type);
        } else {
            $pols = $this->GetDefaultModulesForEnterprise($_rolekey, $role_type);
        }

        return $pols;

    }


    /**
     * @param int    $_rolekey
     * @param string $role_type
     *
     * @return array[][]|false
     */
    function GetDefaultModulesForConsole($_rolekey, $role_type='') 
    {

        $pols = array();

        if ( $role_type == '' && $_rolekey != '' ) {
            $role = $this->get($_rolekey);
            $role_type = $role['TYPE'];
        }

        global $gElementMap, $kIAModules;

        foreach ($gElementMap as $el) {
            $permarr[$el['id']] = $el['id'];
        }

        // collect the modules which can be part of the IA Role
        foreach($kIAModules as $mod){

            // we need to allow admin module as subscribable application
            // so assuming APP as current user APP, since we dont have app defined in
            // the kIAModule structure
            if ( $mod['SYMBOL'] == 'admin' && $mod['APP'] == '' ) {
                $pols[] = array('TITLE' => $mod['NAME'], 'KEY' => $mod['SYMBOL'], 'ID' => $mod['PERMKEY'],
                                    'MODULEID' => $mod['SYMBOL']);
            }

            // M-Practice Role cannot have cpa module
            if ( $role_type == MULTIENTITY_DISTRIBUTED && $mod['SYMBOL'] == 'cpa' ) {
                continue;
            }

            // E-Practice Role cannot have mprac module
            if ( $role_type == EP_CONSOLE && $mod['SYMBOL'] == 'mprac') {
                continue;
            }

            // MEGA-SHARED role can be subscribe only in APP = ('A, 'AW')
            if ( $role_type == MULTIENTITY_SHARED && in_array($mod['SYMBOL'], array('cpa', 'mprac', 'cl')) ) {
                continue;
            }

            // find out the role type to app type mapping and validate the applicability
            $appType = $this->GetAppTypeForRoleType($role_type);
            if ( $mod['APP'] != $appType && $mod['APP'] != isl_substr($appType, 0, 1) ) {
                continue;
            }

            // if all is good lets get the permission key and append it to the policy map
            /** @noinspection PhpUndefinedVariableInspection */
            if( $permarr[$mod['PERMKEY']]) {
                $pols[] = array('TITLE' => $mod['NAME'], 'KEY' => $mod['SYMBOL'], 'ID' => $mod['PERMKEY'],
                                    'MODULEID' => $mod['SYMBOL']);
            }
        }

        return $pols;

    }


    /**
     * @param int    $_rolekey
     * @param string $role_type
     *
     * @return array[][]|false
     */
    function GetDefaultModulesForEnterprise($_rolekey, $role_type='') 
    {

        $pols = array();

        if ( $role_type == '' && $_rolekey != '' ) {
            $role = $this->get($_rolekey);
            $role_type = $role['TYPE'];
        }

        // if this role type for practice company throw an error
        if ( $role_type == MULTIENTITY_DISTRIBUTED || $role_type == EP_CONSOLE ) {
            global $gErr;
            $msg = "Invalid role type for this company";
            $gErr->addIAError("CO-0467", __FILE__ . ":" . __LINE__, $msg);
            return false;
        }

        global $gElementMap, $gInstalledModules;

        foreach ($gElementMap as $el) {
            $permarr[$el['id']] = $el['id'];
        }

        // we need to allow admin module as subscribable application
        // so assuming APP as current user APP, since we dont have app defined in
        // the kIAModule structure
        if ( count($gInstalledModules) > 0 && $gInstalledModules != '' ) {
            $pols[] = array('TITLE' => 'IA.ADMINISTRATION', 'KEY' => 'admin', 'MODULEID' => '0.ADMIN', 'ID' => GetOperationId('admin'));
        }

        // collect the installed modules which can be part of the Role
        foreach($gInstalledModules as $mod){
            // MEGA-SHARED role can be subscribe only in APP = ('A, 'AW')
            if ( $role_type == MULTIENTITY_SHARED && in_array($mod['SYMBOL'], array('cpa', 'mprac', 'cl')) ) {
                continue;
            }

            // find out the role type to app type mapping and validate the applicability
            $appType = $this->GetAppTypeForRoleType($role_type);
            if ( $mod['APP'] != $appType && $mod['APP'] != isl_substr($appType, 0, 1) ) {
                continue;
            }

            // if all is good lets get the permission key and append it to the policy map
            /** @noinspection PhpUndefinedVariableInspection */
            if( $permarr[$mod['PERMKEY']]) {
                $pols[] = array('TITLE' => $mod['NAME'], 'KEY' => $mod['SYMBOL'], 'ID' => $mod['PERMKEY'],
                                    'MODULEID' => $mod['SYMBOL']);
            }
        }

        return $pols;

    }



    /**
     *  Possible RoleType combinations = [ 'E', 'M', 'D', 'S', 'N', 'A' ]
     *  Possible APP combinations = [ 'A', 'AW', 'C', 'CM' ]
     *
     * @param string $role_type
     *
     * @return string
     */
    function GetAppTypeForRoleType($role_type='E') 
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $appType = '';

        switch ( $role_type ) {
            // 'EP Console'
        case 'EP' :
            $appType = 'CM';
            break;
            // 'Multi Entity Distributed' / 'M Console'
        case 'MD' :
            $appType = 'CM';
            break;
            // 'Multi Entity Shared'
        case 'MS' :
            // 'Enterprise'
        case 'EN' :
        default :
            $appType = 'AW';
            break;
        }

        return $appType;

    }

    /**
     *  Possible RoleType combinations = [ 'E', 'M', 'D', 'S', 'N', 'A' ]
     *  Possible APP combinations = [ 'A', 'AW', 'C', 'CM' ]
     *
     * @return string[]
     */
    function GetRoleLabelsForApp() 
    {
        $app = GetMyApp();
        /** @noinspection PhpUnusedLocalVariableInspection */
        $roleTypes = array();

        switch ( $app ) {
        case 'C' :
        case 'CM' :
            $roleTypes = array('Enterprise', 'E-Console', 'Multi Entity Distributed', 'Multi Entity Shared');
            break;
        case 'A' :
        default :
            $roleTypes = array ( IsMultiEntityCompany() ? 'Multi Entity Shared' : 'Enterprise');
            break;
        }

        return $roleTypes;

    }


    /**
     * @param int    $_rolekey
     * @param string $_module
     *
     * @return bool|string[][]
     */
    function GetModulePolicies($_rolekey, $_module) 
    {
        return $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_GET_BY_MODULE', array( $_rolekey, $_module ));
    }


    /**
     * @param int    $_rolekey
     * @param string $polkey
     *
     * @return bool|string[][]
     */
    function GetPolicy($_rolekey, $polkey) 
    {
        return $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_GET_BY_POLICY', array( $_rolekey, $polkey));
    }


    /**
     * @return string[][]|bool
     */
    function GetAllIAPolicies() 
    {
        $res = $this->_QM->DoQuery('QRY_IAPOLICIES', null);
        if ( $res ) {
            $ret = [];
            foreach ($res as $rec) {
                $ret[$rec['RECORD#']] = $rec;
            }
            return $ret;
        } else {
            return $res;
        }
    }


    /**
     * @param int         $_rolekey
     * @param string      $module
     * @param int|null    $polkey
     * @param string|null $polvalue
     *
     * @return bool|string[][]
     */
    function AddPolicy($_rolekey, $module, $polkey = null, $polvalue=null)
    {      
        return $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_INS', array( $_rolekey, $module, $polkey, $polvalue ));
    }


    /**
     * @param int    $_rolekey
     * @param int    $polkey
     * @param string $polvalue
     *
     * @return bool|string[][]
     */
    function SetPolicy($_rolekey, $polkey, $polvalue) 
    {
        return $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_UPD', array( $_rolekey, $polkey, $polvalue ));
    }

    /**
     * @param int $_rolekey
     * @param int $polkey
     *
     * @return bool|string[][]
     */
    function DeletePolicy($_rolekey, $polkey) 
    {
        return $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_DEL', array( $_rolekey, $polkey ));
    }


    /**
     * @param int $_rolekey
     *
     * @return bool|string[][]
     */
    function DeletePoliciesByRole($_rolekey) 
    {
        return $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_DEL_BYROLE', array($_rolekey));
    }


    /**
     * @param int    $_rolekey
     * @param string $moduleid
     *
     * @return bool|string[][]
     */
    function DeletePoliciesByRoleModule($_rolekey, $moduleid) 
    {
        return $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_DEL_BYMODULE', array($_rolekey, $moduleid));
    }

    /**
     * @param int    $_rolekey
     * @param string $moduleid
     * @param int    $policykey
     *
     * @return bool|string[][]
     */
    function DeleteByRoleModulePolicy($_rolekey, $moduleid, $policykey) 
    {
        return $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_DEL_BYMODULE', array($_rolekey, $moduleid, $policykey));
    }


    /**
     * @param int   $_rolekey    Role key
     * @param array $permissions Permission list
     *
     * @return bool
     */
    function SetModulePolicies($_rolekey, $permissions=array())
    {
        $ok = false;

        $source = __FILE__." : ".__LINE__;
        if ( !$permissions ) {
            $permissions = array();
        }

        XACT_BEGIN($source);

        // applications that are subscribed for the role
        if ( $_rolekey != '' && $permissions != '' ) {

            // Get affected user ids
            $userIds = AudUserTrailManager::getUsersByRole($_rolekey);
            $moduleKeys = AudUserTrailManager::getModuleKeys();

            // Get permissions before change
            $allPerms = array();
            foreach( $moduleKeys as $moduleKey) {
                $allPerms[$moduleKey] = AudUserTrailManager::getUserArrayPermissions($userIds, $moduleKey);
            }

            // find already existing modules as part of this role
            $existing_modules = array();

            $values = $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_GET_DISTINCT_MODULES', array($_rolekey));
            foreach ( $values as $value ) {
                $existing_modules[] = $value['MODULE'];
            }


            global  $gElementMap;
            $policies = array();
            $ok = true;
            // for all the user given permission IDs lets find the modules
            foreach ($permissions as $op){

                $moduleid = $gElementMap[$op]['module'];
                if ( $moduleid ) {

                    $policies[$moduleid] = array('module' => $moduleid);
                    // if the module is already part of the role then do not insert them again
                    if ( in_array($moduleid, $existing_modules) ) {
                        continue;
                    }

                    // lets populate the policy array
                    $policykey = '';
                    $policyvalue = '';
                    $policyname = $gElementMap[$op]['policy']['title'];

                    // ideally all modules shouldnt have policies only sub permissions should have this is here coz of MY aCccountign I think
                    if ( $policyname ) {
                        $policykey = $this->_QM->DoQuery('QRY_IAPOLICY_GETBYNAME', array($policyname));
                        $policykey = $policykey[0]['RECORD#'];
                        $policyvalue = $gElementMap[$op]['policy']['level'];
                    }

                    // Defect ID: 12739 
                    // If user checks a previously unchecked module, then $policykey and $policyvalue
                    // will be empty and this leads to data corruption
                    if( ($policykey) && ($policyvalue) ) {
                        $ok = $ok && $this->AddPolicy($_rolekey, $moduleid, $policykey, $policyvalue);
                    }

                    $policies[$moduleid]['policykey'] = $policykey;
                    $policies[$moduleid]['policyname'] = $policyname;
                    $policies[$moduleid]['policyvalue'] = $policyvalue;

                }
            }

            // we need to delete any module which was already used but now getting removed from the list
            if ( $ok ) {
                $allModulePolicies = array_keys($policies);
                foreach ( $existing_modules as $currModule ) {
                    if ( !in_array($currModule, $allModulePolicies) ) {
                        $ok = $ok && $this->DeletePoliciesByRoleModule($_rolekey, $currModule);
                    }
                }
            }

            // Record permission changes
            foreach($moduleKeys as $moduleKey) {
                $modulePerms = $allPerms[$moduleKey];
                $ok = $ok && AudUserTrailManager::logGroup('SetModulePolicies', $userIds, $moduleKey, $modulePerms);
            }

        }

        //$ok = false;
        if (!$ok) {
            XACT_ABORT($source);
        } else {
            XACT_COMMIT($source);
        }

        return $ok;

    }


    /**
     * @param string $moduleid
     *
     * @return string[]
     */
    function GetObjectDefaultPolicies($moduleid) 
    {

        global  $gElementMap;
        $permissions = array();

        foreach ($gElementMap as $opid => $el) {
            list($mod) = explode('/', $el['key']);
            if ( $mod == $moduleid ) {
                $permissions[$opid] = $el;
            }
        }

        return $permissions;

    }


    /**
     * Set role permissions
     * TODO: obsolete
     *
     * @param int    $_rolekey       Role key
     * @param string $moduleid       Module Id
     * @param array  $moduleSubPerms Module sub permissions
     *
     * @return bool
     */
    function SetObjectPermissions($_rolekey, $moduleid, $moduleSubPerms=array())
    {

        $ok = true;

        $source = __FILE__." : ".__LINE__;
        if ( !$moduleSubPerms ) { 
            $moduleSubPerms = array(); 
        }

        $modulePols = $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_GET_BY_ROLEMODULE', array($_rolekey, $moduleid));
        foreach ( $modulePols as $value ) {
            $existing_policies[$value['POLICYKEY']] = $value;
        }

        /** @noinspection PhpUndefinedVariableInspection */
        $existing_policykeys = array_keys($existing_policies);
        $policies = array();

        XACT_BEGIN($source);

        // for all the user given permission IDs lets find the policy
        foreach ($moduleSubPerms as $perm){

            // lets populate the policy array
            $policykey = '';
            $policyname = $perm['policy']['title'];
            $policyvalue = $perm['policy']['level'];

            if ( $policyname ) {
                $policykey = $this->_QM->DoQuery('QRY_IAPOLICY_GETBYNAME', array($moduleid, $policyname));
                $policykey = $policykey[0]['RECORD#'];

                $policies[$policykey] = $perm;

                // if the policy is already part of the role then do not insert them again
                if ( in_array($policykey, $existing_policykeys) ) {
                    continue;
                }

                $ok = $this->AddPolicy($_rolekey, $moduleid, $policykey, $policyvalue);

                $policies[$policykey]['policykey'] = $policykey;
                $policies[$policykey]['policyname'] = $policyname;
                $policies[$policykey]['policyvalue'] = $policyvalue;

            }

            if ( !$ok ) { 
                break; 
            }

        }

        // we need to delete any policy which was already used but now getting removed from the list
        if ( $ok ) {
            $allPolicies = array_keys($policies);
            foreach ( $existing_policies as $policy ) {
                /** @noinspection PhpUndefinedVariableInspection */
                if ( $policy['policyname'] != '' && $policy['policyvalue'] != '' && !in_array($policykey, $allPolicies) ) {
                    $ok = $ok && $this->DeleteByRoleModulePolicy($_rolekey, $moduleid, $policy['policykey']);
                }
            }
        }

        if (!$ok) {
            XACT_ABORT($source);
        } else {
            XACT_COMMIT($source);
        }

        return $ok;

    }


    /**
     * @param int        $_rolekey
     * @param string     $moduleid
     * @param string[]   $moduleSubPerms
     * @param bool       $isCustomRole
     *
     * @return bool
     */
    function SetObjectPolicies($_rolekey, $moduleid, $moduleSubPerms=array(), $isCustomRole = false) 
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ok = true;

        $cny = GetMyCompany();

        $source = __FILE__." : ".__LINE__;
        
        // Get permissions before change
        $userIds = AudUserTrailManager::getUsersByRole($_rolekey);
        $allPerms = AudUserTrailManager::getUserArrayPermissions($userIds, $moduleid);

        XACT_BEGIN($source);
        
        if ($isCustomRole) {
            
            $pol = new RoleCustomPolicy($moduleid, $_rolekey);
            $ok = $pol->SavePolicy($moduleSubPerms);
            
        } else {
            
            //  for all the user given permission IDs lets find the policy
            $qry = "delete from rolepolicyassignment where cny# = :1 and rolekey = :2 and module = :3 and policykey is not null and policyval is not null ";
            $ok = ExecStmt(array($qry, $cny, $_rolekey, $moduleid));
            if ( !$ok ) { 
                eppp("error in deleting the existing role permissions "); return false; 
            }
            
            foreach ( $moduleSubPerms as $policyname => $policyval ){

                // lets populate the policy array
                $policykey = $this->_QM->DoQuery('QRY_IAPOLICY_GETBYNAME', array($moduleid, $policyname));
                $policykey = $policykey[0]['RECORD#'];

                $crwSetupManager = Globals::$g->gManagerFactory->getManager('crwsetup');
                $elements = $crwSetupManager->get('');

                if($policyname == 'Interactive Custom Reports' && $elements['AUTHOR'] == 'Run only model') {
                   CRWUtil::filterICRWViewerOnlyPermissions($policyval);
                }
    
                $ok = $this->AddPolicy($_rolekey, $moduleid, $policykey, $policyval);
    
                if ( !$ok ) { 
                    break; 
                }
    
            }
            
            AudUserTrailManager::logGroup('SetModulePolicies', $userIds, $moduleid, $allPerms);
    
            // on UI, if the user haqqs not saved the module but tryes to save only the module-permissions then
            // we should be saving the corresponding module to make sure we save the module too for compatibility
            $qry = "select 1 found from rolepolicyassignment where cny# = :1 and rolekey = :2 and module = :3 and policykey is null and policyval is null";
            $resultSet = QueryResult(array($qry, $cny, $_rolekey, $moduleid));
    
            if ( $ok && $resultSet[0]['FOUND'] == '1' ) {
                $ok = $this->AddPolicy($_rolekey, $moduleid);
            }
        }

        // Record permission changes

        import('ims_publish_1');
        $pub = new ims_publish_1(IMS_MODE_NONBLOCKING, IMS_PROCESS_REMOTE, IMS_MODE_QUEUED);

        $body['DESCRIPTION'] = 'Permission';
        $body['USERIDS'] = $userIds;
        $body['MODULEID'] = $moduleid;
        $body['ALLPERMS'] = $allPerms;

        $assertTest = $pub->PublishMsg(
            'ROLESMANAGER',
            'INTACCT',
            'RUN_AUDITTRAIL_AT_ROLECHANGE',
            IMS_PRIORITY_DEFAULT,
            $body,
            [],
            $reponse
        );
        assert($assertTest);

        //$ok = false;
        if (!$ok) {
            XACT_ABORT($source);
        } else {
            XACT_COMMIT($source);
        }

        return $ok;

    }

    // Do not update permissions if they are not provided in the request
    /**
     * @inheritDoc
     */
    function API_UpdateMerge(&$object, &$values)
    {
        if (!isset($values['CUSTOMPERMISSIONS'])) {
            unset($object['CUSTOMPERMISSIONS']);
        }
        if (!isset($values['USERPERMISSIONS'])) {
            unset($object['USERPERMISSIONS']);
        }
        return parent::API_UpdateMerge($object, $values);
    }


    /**
     * @param int $_rolekey
     *
     * @return bool
     */
    function ReSetPermissionCacheState($_rolekey) 
    {
        if ( $_rolekey == '' ) { 
            return false; 
        }
        $source = __FILE__.":".__LINE__;

        XACT_BEGIN($source);

        // invalidate the userpermissions and all sessions for the company so that new role changes can be in effect immediately
        $ok = (new ProfileHelper())->invalidateSessionCache(
            ProfileHandler::USER_CACHE_FLAG + ProfileHandler::PERM_CACHE_FLAG, GetMyCompany(), roleKey: $_rolekey
        );
        
        $qry = "update userinfo set perm_cache_valid = 'F' where (cny#, record#) in ( ".
        " select cny#, userkey from role_users where cny# = :1 and rolekey = :2 ) ";
        $stmt_desc = array($qry, GetMyCompany(), $_rolekey);

        $ok = $ok && ExecStmt($stmt_desc);
        $ok = $ok && UserRightsManager::PersistUserRightsForAllUsersWithRoles($_rolekey);

        if (!$ok) {
            XACT_ABORT($source);
        } else {
            XACT_COMMIT($source);
        }

        return $ok;

    }


    /**
     * @param int  $_rolekey
     * @param bool $default
     *
     * @return bool|string[][]
     */
    function GetAllPolicies($_rolekey, $default=false) 
    {

        $pols = $this->_QM->DoQuery('QRY_ROLEPOLASSIGN_GETALL', array( $_rolekey ));

        // if no role policy assignment found then we need to make possible permissions for this
        if ( !$pols && $default ) {

            global $gElementMap;

            foreach ($gElementMap as $el) {
                $permarr[$el['id']] = $el['id'];
            }

            // collect the modules which can be part of the IA Role
            /** @noinspection PhpUndefinedVariableInspection */
            foreach($gIAModules as $mod){
                /** @noinspection PhpUndefinedVariableInspection */
                if ($permarr[$mod['PERMKEY']]) {
                    $perms[] = array('TITLE' => $mod['NAME'], 'KEY' => $mod['SYMBOL'], 'ID' => $mod['PERMKEY'],
                      'MODULEID' => $mod['MODULEID']);
                }
            }

        }

        return $pols;

    }


    /**
     * @param int      $cny
     * @param string   $roleOverride
     * @param int      $userkey
     * @param string[] $userInfo
     *
     * @return string[][]
     */
    function GetRolePoliciesByMod($cny, $roleOverride, $userkey, $userInfo=[])
    {

        global $gManagerFactory;

        // we need to query the admin privileges, if it was not passed
        $adminType = $userInfo['ADMIN'] ?? '';
        if ( $adminType == '' ) {
            $userMgr = $gManagerFactory->getManager('userinfo');
            $userInfo = $userMgr->GetRawByCnyAndUserRec($cny, $userkey);
            $userInfo = $userInfo[0];
            $adminType = $userInfo['ADMIN'];
        }

        // if this user is not an admin then, we should filter out and admin module in the query
        $adminFilter = '';
        if ( $adminType != '1' && $adminType != '2' ) {
            $adminFilter = "and  iap.module != 'admin' ";
        }

        $roleFilter = '';
        /** @noinspection PhpUnusedLocalVariableInspection */
        $rolFilterBind = '';
        if ( $roleOverride ) {
            $roleFilter = "and roles.name = :3";
            $roleFilterBind = $roleOverride;
        }

        // Here we will union all the possible permissions for this user with his
        // User Role Assignment or User Group Role Assignment.

        // query for user-roles assignment case
        $qry1 =
        "SELECT
				iap.name, iap.type, iap.module, rpa.cny#, rpa.policykey, rpa.policyval
			FROM
				userinfo, role_users uroles, roles roles,
				rolepolicyassignment rpa, iapolicy iap
			WHERE
				userinfo.cny# = :1
				and userinfo.record# = :2
				--
				and uroles.cny# = userinfo.cny#
				and uroles.userkey = userinfo.record#
				--
				and roles.cny# = uroles.cny#
				and roles.record# = uroles.rolekey
				and roles.applyto in (:3, :4)
				--
				and rpa.cny# = roles.cny#
				and rpa.rolekey= roles.record#
				--
				and iap.record# = rpa.policykey $adminFilter $roleFilter ";

        $theQuery = array($qry1, $cny, $userkey, 'B', 'L');

        if (isset($roleFilterBind) && $roleFilterBind ) {
            $theQuery[] = $roleFilterBind;
        }
        $pols = QueryResult($theQuery);
        $policyByMod = [];

        foreach ( $pols as $pol ) {
            $polmod = $pol['MODULE'];
            $polname = $pol['NAME'];
            $polval = $pol['POLICYVAL'];
            if ( isset($policyByMod[$polmod][$polname])
                 && $policyByMod[$polmod][$polname] != ''
            ) {
                $policyByMod[$polmod][$polname] .= '|'.$polval;
                $polvalarr = explode('|', $policyByMod[$polmod][$polname]);
                $polvalarr = array_unique($polvalarr);
                $policyByMod[$polmod][$polname] = implode('|', $polvalarr);
            }
            else {
                $policyByMod[$polmod][$polname] = $polval;
            }
        }

        return $policyByMod;

    }

    /**
     * Get Role policies for the selected role and without considering the user context
     * 
     * @param int    $cny       CNY#
     * @param string $roleName  Role to be selected while fetching policies
     * 
     * @return string[][] Policies by module
     */
    public function getRolePolicies($cny, $roleName)
    {
        $qryPolicyList =
        "SELECT
				iap.name, iap.type, iap.module, rpa.cny#, rpa.policykey, rpa.policyval
			FROM
				roles roles,
				rolepolicyassignment rpa, iapolicy iap
			WHERE
				roles.cny# = :1
                and roles.name = :2
				and roles.record# = rpa.rolekey
				and roles.applyto in (:3, :4)
				and rpa.cny# = roles.cny#
				and iap.record# = rpa.policykey";

        $queryParams = array($qryPolicyList, $cny, $roleName, 'B', 'L');
        $pols        = QueryResult($queryParams);
        $policyByMod = array();

        foreach ( $pols as $pol ) {
            
            $polmod  = $pol['MODULE'];
            $polname = $pol['NAME'];
            $polval  = $pol['POLICYVAL'];
            
            if ( isset($policyByMod[$polmod][$polname]) && $policyByMod[$polmod][$polname] != '' ) {
                
                $policyByMod[$polmod][$polname] .= '|'.$polval;
                $polvalarr = explode('|', $policyByMod[$polmod][$polname]);
                $polvalarr = array_unique($polvalarr);
                $policyByMod[$polmod][$polname] = implode('|', $polvalarr);
                
            } else {
                $policyByMod[$polmod][$polname] = $polval;
            }
        }
        return $policyByMod;

    }


    /**
     * @param int      $cny
     * @param int      $userkey
     * @param string[] $userInfo
     *
     * @return string[]
     */
    function GetRolesForUser($cny, $userkey, $userInfo=[])
    {

        global $gManagerFactory;

        // we need to query the admin privileges, if it was not passed
        $adminType = $userInfo['ADMIN'];
        if ( $adminType == '' ) {
            $userMgr = $gManagerFactory->getManager('userinfo');
            $userInfo = $userMgr->GetRawByCnyAndUserRec($cny, $userkey);
            $userInfo = $userInfo[0];
            $adminType = $userInfo['ADMIN'];
        }

        // if this user is not an admin then, we should filter out and admin module in the query
        $adminFilter = '';
        if ( $adminType != '1' && $adminType != '2' ) {
            $adminFilter = "and  iap.module != 'admin' ";
        }

        // Here we will union all the possible permissions for this user with his
        // User Role Assignment or User Group Role Assignment.

        // query for user-roles assignment case
        $qry1 =
        "SELECT
                roles.name
            FROM
                userinfo, ugroles ugroles, roles roles, rolepolicyassignment rpa, iapolicy iap
            WHERE
                userinfo.cny# = :1
                and userinfo.record# = :2
                --
                and ugroles.cny# = userinfo.cny#
                and ugroles.u_o_gkey = userinfo.record#
                and ugroles.type = 'U'
                --
                and roles.cny# = ugroles.cny#
                and roles.record# = ugroles.rolekey
                and roles.applyto in ('B', 'L')
                --
                and rpa.cny# = roles.cny#
                and rpa.rolekey= roles.record#
                and roles.applyto in ('B', 'L')
                --
                and iap.record# = rpa.policykey 
                --
                $adminFilter ";

        // query for user-in-usergroup and usergroup-roles assignment case
        /** @noinspection PhpUndefinedVariableInspection */
        $qry2 =
        "SELECT
                roles.name
            FROM
                userinfo, memberugroup ugroup, ugroles ugroles,
                roles roles, rolepolicyassignment rpa, iapolicy iap
            WHERE
                userinfo.cny# = :1
                and userinfo.record# = :2
                --
                and ugroup.cny# = userinfo.cny#
                and ugroup.u_o_gkey = userinfo.record#
                and ugroup.type = 'U'
                --
                and ugroles.cny# = ugroup.cny#
                and ugroles.u_o_gkey = ugroup.parentgroup
                and ugroles.type = 'G'
                --
                and roles.cny# = ugroles.cny#
                and roles.record# = ugroles.rolekey
                --
                and rpa.cny# = roles.cny#
                and rpa.rolekey= roles.record#
                and roles.applyto in ('B', 'L')
                --
                and iap.record# = rpa.policykey $adminFilter $roleFilter 
                --
                $adminFilter ";

        $theQuery = array(" $qry1 UNION $qry2 ", $cny, $userkey);
        $pols = QueryResult($theQuery);

        $ret = array();
        foreach ( $pols as $pol ) {
            $polname = $pol['NAME'];
            if ( ! array_key_exists($polname, $ret) ) {
                $ret[] = $polname;
            }
        }
        return $ret;

    }

    /**
     * Get Role policies for the selected paramters
     * 
     * @param int      $cny              CNY#
     * @param string   $roleOverride     Role to be selected while fetching policies
     * @param int      $userkey          User Record number
     * @param string[] $userInfo         User Info
     * @param bool     $skipUserContext  User context has to be considered or not while fetching policies
     * 
     * @return int[] Permissions
     */
    function GetRolePoliciesForLogin($cny, $roleOverride, $userkey, $userInfo=[], $skipUserContext = false)
    {
        if ( $skipUserContext === false ) {
            $policyByMod = $this->GetRolePoliciesByMod($cny, $roleOverride, $userkey, $userInfo);
        } else {
            $policyByMod = $this->getRolePolicies($cny, $roleOverride);
        }
        // now lets calc the permisions from policies
        import('UserPolicy');

        // we may have to give away admin if its not already stored with roles assigned
        /** @noinspection PhpUndefinedVariableInspection */
        if ( in_array($adminType, array( '1', '2')) && $policyByMod['admin'] == '' ) {
            $pol = new UserPolicy('admin', $userkey);
            $policyByMod['admin'] = $pol->GetUserModulePolicies();
        }


        // permissions are arrived at by each module, so looping through
        $perms = array();
        foreach ( $policyByMod as $module => $policies ) {
            $pol = new UserPolicy($module, $userkey);

            if ($pol->presets) {
                // now call the BL function to apply presets before permisions
                // $pol, $buttons_ary are reference variables, $buttons_ary is dummy array
                $buttons_ary = [];
                ApplyPresetForUser($userkey, $userInfo, $module, $pol, $buttons_ary);

                // for read only users we need to intersect the given policies
                // with readonly filter since the given role policies can be much
                // higher than what a limited user can get
                // If the readonly user (limited user) is an employee user also,
                // then we should intersect permission with 'Employee' preset and not 'Readonly' preset,
                // this results in giving employee limited users 'My Expenses-->Add' permission

                if ( $userInfo['TYPE'] == 'E'
                     || $userInfo['TYPE'] == 'T'
                     || $userInfo['TYPE'] == 'P'
                ) {
                    $presets = [ 'Employee' ];
                    if ( $userInfo['TYPE'] == 'T' ) {
                        $presets[] = 'Platform';
                    } else if ( $userInfo['TYPE'] == 'P' ) {
                        $presets[] = 'Approver';
                    }

                    $pol->ApplyPreset($presets);
                }
            }

            $afterPreset = $pol->policy;

            // we need computed the policies by intersecting role policies
            // with applicable policies after preset
            $computed = array();
            $givenPolicies = $policies;
            foreach ( $afterPreset as $contents) {
                foreach ($contents as $name => $value) {
                    if ( $givenPolicies[$name] ) {
                        $givenValues = explode('|', $givenPolicies[$name]);

                        if ( is_array($value['values']) && !empty($value['values']) ) {
                            $afpValues = array_keys($value['values']);
                            $tempPols = array_intersect($givenValues, $afpValues);
                            $computed[$name] = join('|', $tempPols);
                        }
                    }
                }
            }

            $temp = $pol->GetPolicyOps($computed);

            $perms = INTACCTarray_merge($perms, $temp);
        }

        // for all user direct policy/permissions granted we need to grant allow-oped permissions too
        if ( $perms != '' && count($perms) > 0 ) {
            global $gElementMap, $gAllowOpElements;

            $allowedperms = $perms;
            $allow_ops = array();

            foreach ($allowedperms as $allowperm) {
                foreach ($gAllowOpElements as $aop ){
                    if( $gElementMap[$aop]['allowops'] && in_array($allowperm, $gElementMap[$aop]['allowops']) ) {
                        $allow_ops[] = $aop; 
                    }
                }
            }

            $allow_ops = INTACCTarray_unique($allow_ops);
            /** @noinspection PhpUnusedLocalVariableInspection */
            $perm = INTACCTarray_merge($perms, $allow_ops);
        }

        return $perms;

    }


    /**
     * @param string $slideType
     * @param int    $cny
     * @param int    $userkey
     * @param int    $destcny
     * @param string $destlocation
     *
     * @return string[][]
     */
    function GetRolePoliciesForSlideIn($slideType, $cny, $userkey, $destcny, $destlocation='')
    {
        /*
        - for admin manage clients slide-in
        - client-user assignment found
        - client assignment to the user
         - slide type roles directly assigned to current user
        - client assignment to the user groups where current user is part of
         - slide type roles assigned to each user group
        - client-user assignment not found
        - slide type roles directly assigned to current user
        */
        $policyByMod = $pols = array();

        // if destlocation is given then we need to check the corresponding association table
        if ( $destlocation == '' ) {
            $pcu = 'practiceclientusers';
            /** @noinspection PhpUnusedLocalVariableInspection */
            $pcug = 'practiceclientsugroup';
        } else {
            $pcu = 'practiceclientuserloc';
            /** @noinspection PhpUnusedLocalVariableInspection */
            $pcug = 'practiceclientugrouploc';
        }

        // query for user-to-client direct assignment case
        $qry1 =
        "SELECT
				iap.name, iap.type, iap.module, rpa.cny#, rpa.policykey, rpa.policyval
			FROM
				$pcu pcu, userinfo, role_users uroles,
				roles roles, rolepolicyassignment rpa, iapolicy iap
			WHERE
				pcu.cny# = :1
				and pcu.entitycny# = :2
				and pcu.userid = :3
				--
				and userinfo.cny# = pcu.cny#
				and userinfo.record# = pcu.userid
				--
				and uroles.cny# = userinfo.cny#
				and uroles.userkey = userinfo.record#
				--
				and roles.cny# = uroles.cny#
				and roles.record# = uroles.rolekey
				and roles.applyto in ('B', 'S')
				--
				and rpa.cny# = roles.cny#
				and rpa.rolekey= roles.record#
				--
				and iap.record# = rpa.policykey ";

        $stmt = array($qry1, $cny, $destcny, $userkey);

        $pols = QueryResult($stmt);

        // for admin slide-in from manage clients lister page
        // if above direct assignments is not found, then we should take all
        // roles assiged to the user irrespective of the client assignement
        if ( $slideType == MC_ADMIN_SLIDETYPE && (!isset($pols) || count($pols) == 0 || $pols == '') ) {
            if ( $destlocation == '' ) {
                $pc = 'v_practiceclients';
                /** @noinspection PhpUnusedLocalVariableInspection */
                $pcug = 'practiceclientsugroup';
            } else {
                $pc = 'practiceclientusers';
                /** @noinspection PhpUnusedLocalVariableInspection */
                $pcug = 'practiceclientsugroup';
            }

            // query for user-to-client direct assignment case
            $qry1 =
            "SELECT
					iap.name, iap.type, iap.module, rpa.cny#, rpa.policykey, rpa.policyval
				FROM
					$pc pc, userinfo, role_users uroles,
					roles roles, rolepolicyassignment rpa, iapolicy iap
				WHERE
					pc.cny# = :1
					and pc.entitycny# = :2
					--
					and userinfo.cny# = pc.cny#
					and userinfo.record# = :3
					--
					and uroles.cny# = userinfo.cny#
					and uroles.userkey = userinfo.record#
					--
					and roles.cny# = uroles.cny#
					and roles.record# = uroles.rolekey
					and roles.applyto in ('B', 'S')
					--
					and rpa.cny# = roles.cny#
					and rpa.rolekey= roles.record#
					--
					and iap.record# = rpa.policykey ";

            $stmt = array($qry1, $cny, $destcny, $userkey);

            $pols = QueryResult($stmt);

        }

        foreach ( $pols as $pol ) {
            $polmod = $pol['MODULE'];
            $polname = $pol['NAME'];
            $polval = $pol['POLICYVAL'];
            if (       isset($policyByMod[$polmod][$polname]) 
                && $policyByMod[$polmod][$polname] != ''
            ) {
                $policyByMod[$polmod][$polname] .= '|'.$polval;
                $polvalarr = explode('|', $policyByMod[$polmod][$polname]);
                $polvalarr = array_unique($polvalarr);
                $policyByMod[$polmod][$polname] = implode('|', $polvalarr);
            }
            else {
                $policyByMod[$polmod][$polname] = $polval;
            }
        }

        return $policyByMod;

    }



    /**
     * @param string $slideType
     * @param int    $destcny
     * @param string $destlocation
     *
     * @return int[]
     */
    function GetRolePermsForSlideIn($slideType, $destcny, $destlocation='')
    {
        global $_userid, $gManagerFactory;
        $perms = array();
        /** @noinspection PhpUnusedLocalVariableInspection */
        $policyByMod = array();

        list($user_rec,$cny) = explode('@', $_userid);

        $rolesMgr = $gManagerFactory->getManager('roles');
        $policyByMod = $rolesMgr->GetRolePoliciesForSlideIn($slideType, $cny, $user_rec, $destcny, $destlocation);

        // permissions are arrived at by each module, so looping through
        foreach ( $policyByMod as $module => $policies ) {
            $main = new UserPolicy($module, $user_rec);
            $temp = $main->GetPolicyOps($policies);
            $perms = INTACCTarray_merge($perms, $temp);
        }

        return $perms;
    }



    /**
     * @return bool
     */
    function MigratePermsToRoles()
    {
        global $_userid, $gManagerFactory;

        $ok = true;
        $source = __FILE__." : ".__LINE__;

        $ugroleMgr = $gManagerFactory->getManager('ugroles');

        $cny = GetMyCompany();
        $practicetype = GetPracticeType($_userid);
        $companyType = GetMyApp();

        $qry = "SELECT record#, loginid, admin, visible
                FROM userinfo
                WHERE cny# = :1 AND category NOT IN (:2, :3, :4)
                ORDER BY record#";
        $allUsers = QueryResult(array($qry, GetMyCompany(), UserInfoManager::CATEGORY_SUPPORT, UserInfoManager::CATEGORY_ANONYMOUS, UserInfoManager::CATEGORY_EXTERNAL));

        XACT_BEGIN($source);

        foreach ( $allUsers as $user ) {

            $userKey = $user['RECORD#'];
            $isLimitedAdmin = ( $user['ADMIN'] == '1' ? true : false );
            $isFullAdmin = ( $user['ADMIN'] == '2' ? true : false );

            // select distinct apps assigned to this user
            // we need to create a role each for each of broad category of applications he has access to
            // i.e., A / AW / C / CM / W
            $qry ="select distinct iam.app, iam.symbol, iap.module from iapolicy iap, iamodule iam, policyassignment pa ".
            "  where pa.cny# = :1 and pa.user_role_key = :2 and iap.record# = pa.policykey and iam.symbol = iap.module ";
            $userModules= QueryResult(array($qry, $cny, $user['RECORD#']));

            // lets collect all possible roletypes for this user
            $roleTypes = array();
            foreach ( $userModules as $umod ) {

                // if current company has mega then all roles belong to MULTIENTITY_SHARED type
                // if current company is a M-Console and $uApp[APP] == 'CM' then all perms belong to MP_CONSOLE
                // if current company is E-Practice and type (practice

                // E-Practice Role
                if ( $practicetype == 'C' && in_array($umod['APP'], array('C', 'CM')) ) {
                    $roleTypes[EP_CONSOLE][] = $umod;
                    continue;
                }

                // M-Practice Role
                if ( $practicetype == 'M' && $umod['APP'] == 'CM' ) {
                    $roleTypes[MULTIENTITY_DISTRIBUTED][] = $umod;
                    continue;
                }

                // MEGA - Multi Entity shared roles
                if ( IsMultiEntityCompany() ) {
                    $roleTypes[MULTIENTITY_SHARED][] = $umod;
                    continue;
                }

                if ( in_array($umod['APP'], array('A', 'AW')) ) {
                    $roleTypes[ENTERPRISE][] = $umod;
                    continue;
                }

            }

            // lets do the admin black magic
            // if the current user is admin we need to add the admin module as well
            if ( $isLimitedAdmin || $isFullAdmin ) {
                /** @noinspection PhpUndefinedVariableInspection */
                if ( $practicetype == 'C' && in_array($umod['APP'], array( 'C', 'CM')) ) {
                    $rType = EP_CONSOLE;
                }
                /** @noinspection PhpUndefinedVariableInspection */
                elseif ( $practicetype == 'M' && $umod['APP'] == 'CM' ) {
                    $rType = MULTIENTITY_DISTRIBUTED;
                } else if ( IsMultiEntityCompany() ) {
                    $rType = MULTIENTITY_SHARED;
                } else {
                    $rType = ENTERPRISE;
                }
                /** @noinspection PhpUndefinedVariableInspection */
                $roleTypes[$rType][] = array( 'MODULE' => 'admin', 'SYMBOL' => 'admin', 'APP' => $companyType);
            }

            foreach ( $roleTypes as $roleType => $roleModules) {

                $roleLabel = $this->_convertRoleTypeToRoleLabel($roleType);
                $name = '::SYS::' . $roleLabel . '-ROLE-FOR - ' . $user['LOGINID'];
                $values = array(
                'NAME' => $name,
                'DESCRIPTION' => $name,
                'TYPE' => $roleLabel,
                'USERKEY' => $userKey,
                'ROLEMODULES' => $roleModules,
                'APPLYTO' => 'Login and Slide-In',
                );

                $ok = $ok && $this->add($values);
                $ok = $ok && $this->CopyFromUserInfo($values, $user);
                $ok = $ok && $ugroleMgr->AddUGRole('U', $userKey, $values[':RECORDNO']);

            }

            if ( !$ok ) { 
                break;     
            }

        }

        // invalidate the userpermissions and all sessions for the company so that new role changes can be in effect immediately
        $ok = $ok && InValidateSession(ProfileHandler::USER_CACHE_FLAG + ProfileHandler::PERM_CACHE_FLAG);
        $ok = $ok && ExecStmt(array("UPDATE userinfo SET perm_cache_valid = 'F' WHERE cny# = :1",GetMyCompany()));
        //Enable user rights migration job
        $ok = $ok && UserRightsManager::EnableUserRightsMigration();

        //$ok = false;
        if (!$ok) {
            XACT_ABORT($source);
        } else {
            XACT_COMMIT($source);
        }

        return $ok;

    }



    /**
     * @return bool
     */
    function UNDORoles()
    {

        $source = __FILE__." : ".__LINE__;
        $ok = true;

        XACT_BEGIN($source);

        $cny = GetMyCompany();

        // insert into user policy assignment
        $qry = "insert into policyassignment (cny#, user_role_key, policykey, policyval)
					select u.cny#, u.record#, rpa.policykey, max(rpa.policyval)
					from userinfo u, ugroles ugr, roles r, rolepolicyassignment rpa, iamodule iam
					where
						u.cny# = :1
						and u.category not in (:2, :3, :4)
						and ugr.cny# = u.cny#
						and ugr.u_o_gkey = u.record#
						and ugr.type = 'U'
						and r.cny# = ugr.cny#
						and r.record# = ugr.rolekey
						and r.name like '::SYS::%'
						and rpa.cny# = r.cny#
						and rpa.rolekey = r.record#
						and rpa.policykey is not null
						and rpa.policyval is not null
						and iam.symbol = rpa.module
						and exists (
							select 1 from module m where  m.cny# = rpa.cny# and m.moduleid = iam.iamoduleid
						)
						and not exists (
							select 1 from policyassignment pa
							where
								pa.cny# = rpa.cny#
								and pa.policykey = rpa.policykey
								and pa.user_role_key = u.record#
						) 
						group by u.cny#, u.record#, rpa.policykey";

        $ok = $ok && ExecStmt(array($qry, $cny, UserInfoManager::CATEGORY_SUPPORT, UserInfoManager::CATEGORY_ANONYMOUS, UserInfoManager::CATEGORY_EXTERNAL));

        // insert into user custom policy assignment
        $qry = "insert into custompolassignment (cny#, custappkey, user_role_key, policykey, resourcekey,
                    resourcetype, policyval, UR, RECORD#)
					select p.*, 'U', get_nextrecordid(p.cny#, 'CUSTOMPOLASSIGNMENT')
                    from (
                      select u.cny#, rpa.custappkey, u.record#, rpa.policykey, rpa.resourcekey, rpa.resourcetype,
					         max(rpa.policyval)
					  from userinfo u, role_users ur, roles r, custompolassignment rpa
					  where
						u.cny# = :1
						and rpa.ur = 'R'
						and u.category not in (:2, :3, :4)
						and ur.cny# = u.cny#
						and ur.userkey = u.record#
						and r.cny# = ur.cny#
						and r.record# = ur.rolekey
						and r.name like '::SYS::%'
						and rpa.cny# = r.cny#
						and rpa.user_role_key = r.record#
						and rpa.policykey is not null
						and rpa.policyval is not null
						and not exists (
							select 1 from custompolassignment pa
							where
								pa.cny# = rpa.cny#
								and pa.custappkey = rpa.custappkey
								and pa.policykey = rpa.policykey
								and pa.resourcekey = rpa.resourcekey
								and pa.resourcetype = rpa.resourcetype
								and pa.UR = 'U'
								and pa.user_role_key = u.record#
						)
						group by u.cny#, rpa.custappkey, u.record#, rpa.policykey, rpa.resourcekey, rpa.resourcetype
					) p";

        $ok = $ok && ExecStmt(array($qry, $cny, UserInfoManager::CATEGORY_SUPPORT, UserInfoManager::CATEGORY_ANONYMOUS, UserInfoManager::CATEGORY_EXTERNAL));

        // insert usermodules
        $qry = "insert into usermodule (cny#, userrec, moduleid)
					select distinct u.cny#, u.record#, iam.iamoduleid
					from userinfo u, ugroles ugr, roles r, rolepolicyassignment rpa, iamodule iam
					where
						u.cny# = :1
						and u.category not in (:2, :3, :4)
						and ugr.cny# = u.cny#
						and ugr.u_o_gkey = u.record#
						and ugr.type = 'U'
						and r.cny# = ugr.cny#
						and r.record# = ugr.rolekey
						and r.name like '::SYS::%'
						and rpa.cny# = r.cny#
						and rpa.rolekey = r.record#
						and rpa.policykey is null
						and rpa.module is not null
						and iam.symbol = rpa.module
						and exists (
							select 1 from module m where  m.cny# = rpa.cny# and m.moduleid = iam.iamoduleid
						)
						and not exists (
							select 1 from usermodule um
							where
								um.cny# = rpa.cny#
								and um.userrec = u.record#
								and um.moduleid = iam.iamoduleid
						) ";

        $ok = $ok && ExecStmt(array($qry, $cny, UserInfoManager::CATEGORY_SUPPORT, UserInfoManager::CATEGORY_ANONYMOUS, UserInfoManager::CATEGORY_EXTERNAL));


        $stmts = array(
            "delete from ugroles where cny# = :1 and rolekey in (select record# from roles where cny# = :1 and name like '::SYS::%' )",
            "delete from rolepolicyassignment where cny# = :1 and rolekey in (select record# from roles where cny# = :1 and name like '::SYS::%' )",
            "delete from custompolassignment where cny# = :1 and UR = 'R' and user_role_key in (
                select record# from roles where cny# = :1 and name like '::SYS::%' )",
            "delete from roles where cny# = :1 and name like '::SYS::%' ",
        );

        foreach ( $stmts as $stmt ) {
            $ok = $ok && ExecStmt(array($stmt, $cny));
        }

        // invalidate the userpermissions and all sessions for the company so that new role changes can be in effect immediately
        $ok = $ok && InValidateSession(ProfileHandler::USER_CACHE_FLAG + ProfileHandler::PERM_CACHE_FLAG);
        $ok = $ok && ExecStmt(array("UPDATE userinfo SET perm_cache_valid = 'F' WHERE cny# = :1",$cny));
        //Enable user rights migration job
        $ok = $ok && UserRightsManager::EnableUserRightsMigration();

        if (!$ok) {
            XACT_ABORT($source);
        } else {
            XACT_COMMIT($source);
        }

        return $ok;

    }


    /**
     * @param array    &$values
     * @param string[] $user
     *
     * @return bool
     */
    function CopyFromUserInfo(&$values, $user) 
    {

        $ok = false;

        $source = __FILE__." : ".__LINE__;
        XACT_BEGIN($source);

        $myrolekey = $values[':RECORDNO'];
        $userkey = $values['USERKEY'];
        $cny = GetMyCompany();
        $isLimitedAdmin = ( $user['ADMIN'] == '1' ? true : false );
        $isFullAdmin = ( $user['ADMIN'] == '2' ? true : false );

        if ( $myrolekey != '' && $userkey != '' ) {

            $roleModules = $values['ROLEMODULES'];

            foreach( $roleModules as $rMod ) {
                $modSymbols[] = $rMod['SYMBOL'];
            }
            /** @noinspection PhpUndefinedVariableInspection */
            $modString = join("','", $modSymbols);

            if ( $modString != '' ) {

                $modString = "'" . $modString . "'";

                // following stmt will insert all given modules leaf level policies from existing user policyassignment
                $stmt = "insert into rolepolicyassignment (rolekey,module,policykey,policyval,cny#) ".
                "select $myrolekey, b.module, a.policykey, a.policyval, $cny from policyassignment a, iapolicy b ".
                "where a.policykey = b.record#  ".
                "			and a.cny# = $cny and a.user_role_key = $userkey and b.module in ( $modString ) ";
                $ok = ExecStmt(array($stmt));
            }

            // following stmt will insert all custom policies from existing user
            $stmt = "insert into custompolassignment (cny#, custappkey, user_role_key, policykey, resourcekey, " .
                "resourcetype, policyval, UR) ".
                "select cny#, custappkey, :3, policykey, resourcekey, resourcetype, policyval, 'R' ".
                "from custompolassignment where cny# = :1 and user_role_key = :2 and UR = 'U' ";
            $ok = $ok && ExecStmt(array($stmt, $cny, $userkey, $myrolekey));


            // following stmt will grant all usermodules which this user has but the corresponding
            // policyassignments are missing for some reason
            $stmt = "insert into rolepolicyassignment (rolekey,module,policykey,policyval,cny#) ".
            " select distinct $myrolekey, b.symbol, null, null, a.cny# from usermodule a, iamodule b ".
            " where b.iamoduleid = a.moduleid and a.cny# = $cny and a.userrec = $userkey and b.symbol in ( $modString ) ";
            $ok = $ok && ExecStmt(array($stmt));

            // lets do the admin black magic
            if ( $ok ) {

                // if the current user is admin we need to add the admin module as well
                global $gElementMap, $gForcedAdminElements, $gForcedFullAdminElements;
                $kForcedPolicies = array();

                // calc any admin
                if ( $isLimitedAdmin || $isFullAdmin ) {
                    foreach ($gForcedAdminElements as $aop) {
                        $policyName = $gElementMap[$aop]['policy']['title'];
                        if ( $policyName != '' ) {
                            $kForcedPolicies[$policyName][] = $gElementMap[$aop]['policy']['level'];
                        }
                    }
                }

                // calc full admin
                if ( $isFullAdmin ) {
                    foreach ($gForcedFullAdminElements as $aop) {
                        $policyName = $gElementMap[$aop]['policy']['title'];
                        if ( $policyName != '' ) {
                            $kForcedPolicies[$policyName][] = $gElementMap[$aop]['policy']['level'];
                        }
                    }
                }

                $stmt = "insert into rolepolicyassignment (cny#, rolekey,module,policykey,policyval) ".
                " select :1, :2, 'admin', iap.record#, :4 from iapolicy iap where iap.name = :3 ";

                foreach ($kForcedPolicies as $policyName => $policyValueList) {
                    $policyVal = join('|', array_unique($policyValueList));
                    $ok = $ok && ExecStmt(array($stmt, $cny, $myrolekey, $policyName, $policyVal));
                }


                if ( count($kForcedPolicies) > 0 ) {
                    $stmt = "insert into rolepolicyassignment (rolekey,module,policykey,policyval,cny#) ".
                    " values ( $myrolekey, 'admin', null, null, $cny ) ";
                    $ok = $ok && ExecStmt(array($stmt));
                }

            }

        }

        //$ok = false;
        if (!$ok) {
            XACT_ABORT($source);
        } else {
            XACT_COMMIT($source);
        }

        return $ok;

    }




    /**
     * @param string $roleType
     *
     * @return string
     */
    function _convertRoleTypeToRoleLabel($roleType)
    {
        $label = '';
        switch ($roleType) {
        case EP_CONSOLE :
            $label = 'E-Console';
            break;
        case MULTIENTITY_DISTRIBUTED :
            $label = 'Multi Entity Distributed';
            break;
        case MULTIENTITY_SHARED :
            $label = 'Multi Entity Shared';
            break;
        case ENTERPRISE :
            $label = 'Enterprise';
        default :
            break;
        }
        return $label;
    }

    /**
     * Change given symbol to IAModule name
     *
     * @param string    &$policyName     (output) the name of the policy or false if it wasn't valid
     * @param string    &$moduleName      (output) the name of the policy's module or false if either the policy
     *                                 or policy's module wasn't valid
     * @param int        $key             the policy's record#
     * @param string     $module          a module's symbol to use in case the policy's module is missing
     * @param string[][] $policies        the policies by record#
     * @param string[][] $modules         the modules keyed by their symbol
     * @param array      $modulePolicies  policies by module symbol from getAllModulePolicies
     * 
     * @return bool true if the policy is visible in the module's permission UI
     */
    function getPolicyInfoByKey(&$policyName, &$moduleName, $key, $module, $policies, $modules, $modulePolicies)
    {
        $ret = true;

        if (isset($policies[$key])) {
            $policyName = $policies[$key]['NAME'];
            $moduleKey = $policies[$key]['MODULE'];
            if ($moduleKey === null || $moduleKey === false || $moduleKey === '') {
                $moduleKey = $module;
            }
            if (!isset($modulePolicies[$moduleKey][$policyName])) {
                $ret = false;
            }
        } else {
            $ret = false;
            $moduleKey = $module;
        }

        if (isset($modules[$moduleKey])) {
            $moduleName = $modules[$moduleKey]['NAME'];
        } else {
            $ret = false;
        }

        return $ret;
        
    }

    /**
     * Gather all the installed modules
     * (originally from csvimport_roles::getModulesForObj)
     *
     * @param string[]     &$modules         returned map of module name => module symbol
     * @param string[][][] &$modulePolicies  returned map of module symbol => policy name => permissions
     * 
     */
    public function getAllModulePolicies(&$modules, &$modulePolicies)
    {
        global $kIAModules;

        $modules = [];
        $modulePolicies = [];
        foreach ($kIAModules as $mod) {
            $modules[$mod['NAME']] = $mod['SYMBOL'];
            $this->buildModuleValues($mod['SYMBOL'], $modulePolicies);
        }

    }

    /**
     * Clean up the editor values to make sure we do not copy the original role's users and groups
     *
     * @param array $values the data
     *
     * @return bool false if error else true
     */
    public function CleanupEditorValues(&$values) {
        $ok = parent::CleanupEditorValues($values);

        if ($ok && isset($values['ROLE_USERS'])) {
            unset($values['ROLE_USERS']);
        }
        if ($ok && isset($values['ROLE_USER_GROUPS'])) {
            unset($values['ROLE_USER_GROUPS']);
        }
        if ($ok && isset($values['CUSTOMPERMISSIONS'])) {
            unset($values['CUSTOMPERMISSIONS']);
        }

        return $ok;
    }

    /**
     * Gather the valid permissions for the given module
     * (originally from csvimport_roles::buildModuleValues)
     *
     * @param string       $symbol         symbol for the module to gather valid permissions
     * @param string[][][] $modulePolicies Module policy list
     *
     */
    private function buildModuleValues($symbol, &$modulePolicies)
    {
        $pol = new UserPolicy($symbol);
        if ( is_array($pol->epolicy) ) {
            foreach ( $pol->epolicy as $name => $values ) {
                $modulePolicies[$symbol][$name] = array_keys($values);
            }
        }
    }
}
