<?

//=============================================================================
//  In order to make taxrecord manager object oriented we had to add this ent
//
//	FILE:			gltaxrecord.ent
//	AUTHOR:			Kaushik Ghosh
//
//	(C)2020, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================


require 'taxrecord.ent';
$kSchemas['gltaxrecord'] = $kSchemas['taxrecord'];
$kSchemas['gltaxrecord']['customComponentsEntity'] = 'taxrecord'; // smart events will trigger based on taxrecord entity

