<?php

class TaxRecordLister extends NLister
{
    function __construct()
    {
        $params = [
            'entity' => 'taxrecord',
            'fields' => ['RECORDNO',
                         'TRANSACTIONID',
                         'ENTRY_DATE',
                         'TRANSACTIONLABEL',
                         'TRANSACTIONTYPE',
                         'TRXSOURCEAMOUNT',
                         'TRXTAXAMOUNT',
                         'LOCATIONNAME',
                         'CUSTVENDNAME',
                         'TAXCODE',
                         'STATE',
                         'TAXFILING'],
        ];

        $taxReturnKey = urldecode(Request::$r->_taxreturnkey);
        $filters = Request::$r->filters;
        if(isset($taxReturnKey) && $taxReturnKey !== '' && is_numeric($taxReturnKey)){
            $filters = $filters.'[taxReturn.key:'.$taxReturnKey.']';
            Request::$r->filters = $filters;
        }

        parent::__construct($params);
    }

    /**
     * @return array
     */
    function BuildQuerySpec() {
        $querySpec = parent::BuildQuerySpec();

        $filters = Request::$r->filters;
        $filter = explode(":", trim($filters, "[]"));
        $taxReturnKey = isset($filter[0], $filter[1]) && $filter[0] === "taxReturn.key"
            ? $filter[1]
            : urldecode(Request::$r->_taxreturnkey);

        if ($taxReturnKey!='' && isset($taxReturnKey) && is_numeric($taxReturnKey)) {
            $querySpec['filters'][0][] = array('TAXRETURNKEY', '=', $taxReturnKey);
        }
        return $querySpec;
    }

    function BuildTable()
    {
        parent::BuildTable();
    }
}