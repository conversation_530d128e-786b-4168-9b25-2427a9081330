<?php

/**
 * Manager class for Tax Detail Box
 *
 * <AUTHOR>
 * @copyright 2024 Sage Intacct Inc., All Rights Reserved
 */
class TaxDetailBoxManager extends EntityManager
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Check for validity of the taxdetailbox
     *
     * @param array &$values params
     *
     * @return bool
     */
    protected function validate(&$values)
    {
        $ok = true;
        $tsMgr = Globals::$g->gManagerFactory->getManager('taxsolution');
        $tdMgr = Globals::$g->gManagerFactory->getManager('taxdetail');

        // Check for an attempt to change the box ID
        if (isset($values['RECORDNO']) && isset($values['BOXID'])) {
            $query = ['selects' => ['BOXID'], 'filters' => [[['RECORDNO', '=', $values['RECORDNO']]]]];
            $list = $this->GetList($query);
            $originalBoxID  = Util::countOrZero($list) > 0 ? $list[0]['BOXID'] : null;
            if ($originalBoxID !== $values['BOXID']) {
                Globals::$g->gErr->addIAError('TAX-0332', __FILE__ . ':' . __LINE__, "The Box ID is read only.");
                return false;
            }
        }

        // Validations on the tax detail box map children
        $detailIds = [];
        $values['TAXDETAILBOXMAP'] = $values['TAXDETAILBOXMAP'] ?? [];
        foreach($values['TAXDETAILBOXMAP'] as $tdbm) {
            // Validate we haven't already selected this tax detail
            if (isset($detailIds[$tdbm['DETAILID']])) {
                $ok = false;
                $msg1 = "Duplicate tax detail '{$tdbm['DETAILID']}' detected in mapping from the tax detail box to the tax details.";
                Globals::$g->gErr->addIAError(
                    'TAX-0308', __FILE__ . ':' . __LINE__,
                    $msg1, ['DUPLICATETAXDETAIL' => $tdbm['DETAILID']],
                    "", [],
                    "Remove the duplicate tax detail."
                );
            } else {
                $detailIds[$tdbm['DETAILID']] = true;
            }

            // Get the tax detail object (ensure it exists)
            $tdObj = NULL;
            if (isset($tdbm['DETAILID'])) {
                $query = ['selects' => ['TAXSOLUTIONID'], 'filters' => [[['DETAILID', '=', $tdbm['DETAILID']]]]];
                $list = $tdMgr->GetList($query);
                $tdObj =  Util::countOrZero($list) > 0 ? $list[0] : [];
                if (!isset($tdObj['TAXSOLUTIONID'])) {
                    $ok = false;
                    Globals::$g->gErr->addIAError('TAX-0318', __FILE__ . ':' . __LINE__,
                        "The tax detail was not found.", [], "", [], "Enter a valid Tax detail."
                    );
                }
            }

            // Validate the tax detail is VAT via the tax solution (ensuring the tax solution exists)
            $query = ['selects' => ['SOLUTIONID', 'TAXMETHOD'], 'filters' => [[['SOLUTIONID', '=', $tdObj['TAXSOLUTIONID']]]]];
            $list = $tsMgr->GetList($query);
            $tsObj =  Util::countOrZero($list) > 0 ? $list[0] : [];
            if (!isset($tsObj['SOLUTIONID'])) {
                $ok = false;
                Globals::$g->gErr->addIAError(
                    'TAX-0319', __FILE__ . ':' . __LINE__,
                    "Tax solution was not found for the selected tax detail.", [],
                    "", [],
                    "Select a valid tax detail.");
            } else if ($tsObj['TAXMETHOD'] !== TaxSolutionManager::TAXMETHOD_LABEL_VAT) {
                $ok = false;
                Globals::$g->gErr->addIAError(
                    'TAX-0309', __FILE__ . ':' . __LINE__,
                    "", [], "", [],
                    "Select a VAT or GST tax solution."
                );
            }

            // Validate the tax solution from the tax detail box matches the tax solution from tax detail referenced in the tax detail box map
            if ($values['TAXSOLUTIONID'] !== $tsObj['SOLUTIONID']) {
                $ok = false;
                $msg1 = "The selected tax detail {$tdbm['DETAILID']} is not associated with the tax solution {$tsObj['SOLUTIONID']}.";
                Globals::$g->gErr->addIAError(
                    'TAX-0310', __FILE__ . ':' . __LINE__,
                    $msg1, ['TAXDETAILID' => $tdbm['DETAILID'], 'TAXSOLUTIONID' => $tsObj['SOLUTIONID']],
                    "", [],
                    "Select a valid tax detail."
                );
            }
        }

        // Check BoxID length
        $maxLen = 40;
        if (strlen($values["BOXID"]) > $maxLen) {
            $ok = false;
            Globals::$g->gErr->addIAError(
                'TAX-0333', __FILE__ . ':' . __LINE__,
                "The length of Box Id exceeds $maxLen characters.", ['MAXLEN' => $maxLen],
                "", [],
                "Provide a shorter Box Id.");
        }

        // Ensure the calculation field is not empty if the type is calculation
        if ($values['BOXTYPE'] === "C" && $values['CALCULATION'] === "") {
            $ok = false;
            Globals::$g->gErr->addIAError(
                'TAX-0320', __FILE__ . ':' . __LINE__,
                "The box type is Calculation but the calculation field is empty.", [],
                "", [],
                "Change box type or provide a valid calculation.", []
            );
        }

        // Ensure the calculation format is correct
        if ($values['BOXTYPE'] === "C" && $values['CALCULATION'] !== "") {
            $status = $this->validateCalculation($values['CALCULATION'], $values['BOXID'], (int)$values['TAXSOLUTIONKEY']);
            if ($status !== true) {
                $ok = false;
                Globals::$g->gErr->addIAError(
                    'TAX-0321', __FILE__ . ':' . __LINE__,
                    "The box type is calculation is not correctly formatted.", [],
                    "The formatting issue is: $status.", ["REPORTEDERROR" => $status],
                    "Enter a valid formula in the Calculation field.", []
                );
            }
        }

        // Check for a circular reference
        if (
            $ok &&
            isset($values['RECORDNO']) &&
            $values['BOXTYPE'] === "C" &&
            $values['CALCULATION'] !== ""
        ) {
            // Build a mapping of Box IDs to calculation strings to avoid multiple queries later
            $query = ['selects' => ['BOXID', 'CALCULATION'], 'filters' => [[['TAXSOLUTIONID', '=', $values['TAXSOLUTIONID']], ['BOXTYPE', '=', "C"]]]];
            $list = $this->GetList($query);
            $map = [];
            foreach($list as $item) {
                if ($item['CALCULATION'] != "") {
                    $map[$item['BOXID']] = $item['CALCULATION'];
                }
            }

            // Starting with the current box crawl the chain of references via the calculation strings
            $subjectBoxId = $values['BOXID'];
            $toProbe = [];
            $this->validateBraces($values['CALCULATION'], $toProbe);
            $index = 0;
            while($ok && $index < count($toProbe)) {
                $nextBoxId = $toProbe[$index];
                $index++;

                if($nextBoxId === $subjectBoxId) {
                    $ok = false;
                    Globals::$g->gErr->addIAError(
                        'TAX-0326', __FILE__ . ':' . __LINE__,
                        "The boxes referenced in the calculation are part of a reference chain back to this box.", [],
                        "", [],
                        "Either make a change in this calculation field or make a change in the Tax Details Boxes referenced via this calculation field.", []
                    );
                } else if (isset($map[$nextBoxId])) {
                    $newBoxIDs = [];
                    $this->validateBraces($map[$nextBoxId], $newBoxIDs);
                    foreach($newBoxIDs as $newBoxID) {
                        if (!in_array($newBoxID, $toProbe)) {
                            $toProbe[] = $newBoxID;
                        }
                    }
                }
            }
        }

        // If here all is good
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function PrepValues(&$values)
    {
        $ok = true;

        $values['BOXID']       = trim($values['BOXID']);
        $values['LABEL']       = trim($values['LABEL']);
        $values['DESCRIPTION'] = trim($values['DESCRIPTION']);
        $values['CALCULATION'] = trim($values['CALCULATION']);

        if ($ok) {
            $query = ['selects' => ['RECORDNO', 'TAXMETHOD'], 'filters' => [[['SOLUTIONID', '=', $values['TAXSOLUTIONID']]]]];
            $list = Globals::$g->gManagerFactory->getManager('taxsolution')->GetList($query);
            $tsObj =  Util::countOrZero($list) > 0 ? $list[0] : [];
            if (!isset($tsObj['RECORDNO'])) {
                $ok = false;
                Globals::$g->gErr->addIAError(
                    'TAX-0014', __FILE__ . ':' . __LINE__,
                    "Unable to create record.", [],
                    "Invalid tax solution."
                );
            } else {
                $values['TAXSOLUTIONKEY'] = $tsObj['RECORDNO'];
                $values['TAXMETHOD'] = $tsObj['TAXMETHOD'];
            }
        }

        // Check for only a single TDBM from the XML API
        if (isset($values['TAXDETAILBOXMAP']['DETAILID'])) {
            $values['TAXDETAILBOXMAP'] = [$values['TAXDETAILBOXMAP']];
        }

        // Examine the children, tax detail box map items
        foreach ($values['TAXDETAILBOXMAP'] as &$tdbm) {
            if ($ok && ($tdbm['DETAILID'])) {
                $query = ['selects' => ['RECORDNO'], 'filters' => [[['DETAILID', '=', $tdbm['DETAILID']]]]];
                $list = Globals::$g->gManagerFactory->getManager('taxdetail')->GetList($query);
                $tdObj =  Util::countOrZero($list) > 0 ? $list[0] : [];
                if (isset($tdObj['RECORDNO'])) {
                    $tdbm['TAXDETAILBOXKEY'] = $values['RECORDNO'];
                    $tdbm['TAXDETAILKEY'] = $tdObj['RECORDNO'];
                    $tdbm['BOXID'] = $values['BOXID'];
                    $tdbm['BOXTYPE'] = $values['BOXTYPE'];
                }
            }
        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $source = "TaxDetailBoxManager::Add";

        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->PrepValues($values);
        $ok = $ok && $this->validate($values);
        $ok = $ok && parent::regularAdd($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok ) {
            Globals::$g->gErr->addIAError('TAX-0307', __FILE__ . ':' . __LINE__, "Could not create tax detail box record.");
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $source = "TaxDetailBoxManager::Set";
        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->PrepValues($values);
        $ok = $ok && $this->validate($values);
        $ok = $ok && parent::regularSet($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok ) {
            Globals::$g->gErr->addIAError('TAX-0311', __FILE__ . ':' . __LINE__, "Could not update tax detail box record.");
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * Delete
     *
     * @param string $ID vid of entity
     *
     * @return bool
     */
    public function Delete($ID)
    {
        $source = "TaxDetailBoxManager::Delete";
        $ok = true;
        $trdMgr = Globals::$g->gManagerFactory->getManager('taxreportdefinition');
        $trdbMgr = Globals::$g->gManagerFactory->getManager('taxreportdefinitionbox');

        // Check for the TDB referenced by one or more TRDBs
        $trdbs = $trdbMgr->GetList(['selects' => ['TAXREPORTDEFINITIONKEY'], 'filters' => [[['TAXDETAILBOXKEY', '=', $ID]]]]);
        foreach ($trdbs as $trdb) {
            $ok = false;

            $trds = $trdMgr->GetList(['selects' => ['NAME'], 'filters' => [[['RECORDNO', '=',  $trdb['TAXREPORTDEFINITIONKEY']]]]]);
            $trdName = Util::countOrZero($trds) > 0 ? $trds[0]['NAME'] : "";

            Globals::$g->gErr->addIAError(
                'TAX-0322', __FILE__ . ':' . __LINE__,
                "Can not delete this Tax Detail Box since it is used by the Tax Report Definition, $trdName.", ['TRDNAME' => $trdName],
                "", [],
                "Remove the Tax Detail Box reference in the Tax Report Definition."
            );
        }

        // Check for the TDB referenced in other TDB calculations...
        // ...starting by getting a list of calculations for this tax solution
        $query = ['selects' => ['BOXID', 'RECORDNO', 'TAXSOLUTIONKEY'], 'filters' => [[['RECORDNO', '=', $ID]]]];
        $list = Globals::$g->gManagerFactory->getManager('taxdetailbox')->GetList($query);
        $thisObj =  Util::countOrZero($list) > 0 ? $list[0] : [];
        $thisBoxID = $thisObj['BOXID'];
        $filters = [
            ['RECORDNO', '!=', $thisObj['RECORDNO']],
            ['TAXSOLUTIONKEY', '=', $thisObj['TAXSOLUTIONKEY']],
            ['BOXTYPE', '=', "C"],
        ];
        $query = ['selects' => ['CALCULATION', 'BOXID'], 'filters' => [$filters]];
        $list = $this->GetList($query);
        if (Util::countOrZero($list) > 0) {
            $referencedIn = [];
            foreach ($list as $item) {
                $boxNames = [];
                $this->validateBraces($item['CALCULATION'], $boxNames);
                if (in_array($thisBoxID, $boxNames)) {
                    $referencedIn[] = $item['BOXID'];
                }
            }
            if (Util::countOrZero($referencedIn) > 0) {
                $ok = false;
                $referencedBoxList = implode(", ", $referencedIn);
                Globals::$g->gErr->addIAError(
                    'TAX-0331', __FILE__ . ':' . __LINE__,
                    "Can not delete this Tax Detail Box since it is referenced in the calculations for:  $referencedBoxList", ['BOXLIST' => $referencedBoxList],
                    "", [],
                    "Remove the Tax Detail Box references in the calculations of those boxes."
                );
            }
        }

        if ($ok) {
            // Start the transaction
            $ok = $this->_QM->beginTrx($source);

            // Get the owned object manager to request the owned objects/childen be deleted
            $taxDetailBoxMapMgr = Globals::$g->gManagerFactory->getManager('taxdetailboxmap');
            $ok = $ok && $taxDetailBoxMapMgr->deleteByParent($ID, DELETE_FOR_DELETE);

            // Delete the object/parent
            $ok = $ok && parent::Delete($ID);

            // Complete the transaction
            $ok = $ok && $this->_QM->commitTrx($source);

            // Flag the errors if there were any
            if (!$ok) {
                Globals::$g->gErr->addIAError(
                    'TAX-0315', __FILE__ . ':' . __LINE__,
                    "Delete Tax detail box {$ID} was unsuccessful.", ['TAXDETAILBOXID' => $ID],
                    "", [],
                    "Wait a few minutes and try again. If the issue persists, contact Customer Support."
                );
                $this->_QM->rollbackTrx($source);
            }
        }

        return $ok;
    }

    /**
     * Validation of the round and square brackets, plus extracts the boxIds
     *
     * @param string $calcStr The calculation string to be evaluated.
     * @param array &$boxNames The reference container for returning the array of boxIds.
     *
     * @return bool true on success or a string representing the first error found.
     */
    private function validateBraces(string $calcStr, array &$boxNames)
    {
        if ($calcStr == "") {
            return true;
        }

        $len = strlen($calcStr);
        $braceCount = 0;
        $squareCount = 0;
        $boxNames = [];
        $aBoxName = "";
        $brackets = ['(', ')', '[', ']'];
        for ($index=0; $index<$len; $index++) {
            $char = $calcStr[$index];

            // Case: Building a box name
            if ($squareCount > 0) {
                if ($char === ']') {
                    $squareCount--;
                    if (!preg_match("/^[0-9 a-z_A-Z\-]+$/", $aBoxName)) {
                        return I18N::getSingleToken('IA.TDBOX_CALC_BOXID_INVALID');
                    }
                    $boxNames[] = $aBoxName;
                    $aBoxName = "";
                } else if (in_array($char, $brackets)) {
                    return I18N::getSingleToken('IA.TDBOX_CALC_VALIDATION_BOXID_UNCOMPLETE');
                } else {
                    $aBoxName .= $char;
                }
            }

            // Case: [
            else if ($char === '[') {
                $squareCount++;
            }

            // Case: ]
            else if ($char === ']') {
                return I18N::getSingleToken('IA.TDBOX_CALC_BOX_ID_END');
            }

            // Case: (
            else if ($char === '(') {
                $braceCount++;
            }

            // Case: )
            else if ($char === ')') {
                $braceCount--;
                if ($braceCount < 0) {
                    return I18N::getSingleToken('IA.TDBOX_CALC_BRACKET_RIGHT_UNMATCHED');
                }
            }
        }

        // End review
        if ($braceCount < 0) {
            return I18N::getSingleToken('IA.TDBOX_CALC_BRACKET_RIGHT_UNMATCHED');
        } else if ($braceCount > 0) {
            return I18N::getSingleToken('IA.TDBOX_CALC_BRACKET_LEFT_UNMATCHED');
        } else if ($squareCount !== 0) {
            return I18N::getSingleToken('IA.TDBOX_CALC_VALIDATION_BOXID_UNCOMPLETE');
        }

        // If here then all is good
        return true;
    }

    /**
     * Validation of a correctly formatted simple math string.
     * Example:  Validates a string like "3 + 5.6 * -50 - /8.123 + 10)"
     *
     * @param string $mathStr The math string to be evaluated.
     *
     * @return bool true when valid, false if invalid
     */
    private function validateMath(string $mathStr)
    {
        // Break into elements based on spaces and operators
        $mathStr = preg_replace("/(\d+)([+\-*\/]{1})(\d+)/", "$1 $2 $3", $mathStr); // 1+2 => 1 + 2
        $mathStr = preg_replace("/(\d+)([+\-*\/]{1})/", "$1 $2", $mathStr); // 1+ 2 => 1 + 2
        $mathStr = preg_replace("/([+\-*\/]{1})([+\-]{1})([0-9\(\[])/", "$1 $2$3", $mathStr);  // 1 *+9 => 1 * +9
        $mathStr = preg_replace("/(\d+) ([+\-*\/]{1})([0-9\(\[])/", "$1 $2 $3", $mathStr);  // 1 +2 => 1 +2
        $mathStr = trim(str_replace("  ", " ", $mathStr));
        $elements = explode(" ", $mathStr);
        $size = count($elements);

        // Ensure the first and last elements are not operators
        $oneOpPattern = "/^([+\-*\/]{1})$/";
        if (
            preg_match($oneOpPattern, $elements[0]) > 0 ||
            preg_match($oneOpPattern, $elements[$size - 1]) > 0
        ) {
            return false;
        }

        // Iterate through the elements checking the values and operators are valid
        $valuePattern = "/^([+\-]{0,1}\d+|[+\-]{0,1}\d+\.\d+)$/";
        $isOp = false; // Above we ensure we start with a value and not an operator
        foreach ($elements as $ele) {
            if ($isOp) {
                // Expecting a single operator: +, -, * or /
                if (preg_match($oneOpPattern, $ele) == 0) {
                    return false;
                }
            } else {
                // Expecting a value: 123, 123.456
                if (preg_match($valuePattern, $ele) == 0) {
                    return false;
                }
            }
            $isOp = !$isOp;
        }

        // If here the string is a valid formula string
        return true;
    }

    /**
     * Validate the calculation string.
     *
     * @param string $calcStr The entire calculation string to be evaluated.
     *
     * @return bool|string true when valid, otherwise returns a stirng representing the error;
     */
    public function validateCalculation(string $calcStr, string $boxID, int $tsKey)
    {
        // Check the brackets and get an array of the box names
        $boxNames = [];
        $status = $this->validateBraces($calcStr, $boxNames);
        if ($status !== true) {
            return $status;
        }

        // We can check to see if we are making a self reference
        if (strpos($calcStr, "[$boxID]") !== false) {
            return I18N::getSingleToken('IA.TDBOX_CALC_SELF_REFERENCE');
        }

        // Recurse into sets of braces
        $leftPos = strpos($calcStr, "(");
        while ($leftPos !== false) {
            $braceCount = 1;
            $index = $leftPos + 1;
            $rightPos = null;
            while ($index < strlen($calcStr) && $rightPos === null) {
                $char = $calcStr[$index];
                if ($char === "(") {
                    $braceCount++;
                } else if ($char === ")") {
                    $braceCount--;
                }
                if ($braceCount === 0) {
                    $rightPos = $index;
                }
                $index++;
            }

            if ($rightPos === null) {
                // Note:  The validate braces function should have caught this condition, this check is included for completeness
                return I18N::getSingleToken('IA.TDBOX_CALC_BRACKET_LEFT_UNMATCHED');
            }

            $subCalc = substr($calcStr, $leftPos + 1, $rightPos - $leftPos - 1);
            $subResult = $this->validateCalculation($subCalc, $boxID, $tsKey);
            if ($subResult !== true) {
                return $subResult;
            }

            $leftSide = substr($calcStr, 0, $leftPos);
            $rightSide = substr($calcStr, $rightPos + 1);
            $calcStr = $leftSide . "999" . $rightSide; // "999" is a place holder for the braces and content which were validated

            $leftPos = strpos($calcStr, "(", $leftPos);
        }

        // The actual box names are not important for the validation so lets replace them with the place holder "999" as well
        foreach ($boxNames as $name) {
            $calcStr = str_replace("[" . $name . "]", "999", $calcStr);

            // Check the box exists
            $filter = ['selects' => ['RECORDNO'], 'filters' => [[['BOXID', '=', $name], ['TAXSOLUTIONKEY', '=', $tsKey]]]];
            $list = $this->GetList($filter);
            if (Util::countOrZero($list) === 0) {
                return I18N::getSingleToken('IA.TDBOX_BOX_NOT_FOUND');
            }
        }

        // We need to validate the content of the whole string
        $ok = $this->validateMath($calcStr);
        return ($ok === false) ? I18N::getSingleToken('IA.TDBOX_CALC_FORMULA_INVALID') : true;
    }
}
