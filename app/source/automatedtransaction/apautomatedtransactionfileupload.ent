<?php
/**
 * apautomatedtransactionfileupload.ent
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Intacct Corporation -- All Rights Reserved
 */
global $gRecordNoFormat, $gDateType;
$kSchemas['apautomatedtransactionfileupload'] = array(

    'object' => array(
        'RECORD<PERSON><PERSON>',
        'LOCATIONKEY',
        'STXFILEID',
        'STXDRAFTINVOICEID',
        'SUPDOCID',
        'PRRECORDKEY',
        'STATE',
        'ERRORMSG',
        'UPLOADERRORS',
        'SOURCE',
        'MULT<PERSON>INECONFIG',
        'CREATEDBY',
        'MODIFIEDBY',
        'WHENMODIFIED',
        'WHENCREATED',
        'WEBHOOKSTATUS',
        'FEEDBACK',
        'FILENOTIFICATIONTIME',
        'DRAFTNOTIFICATIONTIME',
        'FILECOMPLETIONTIME',
        'FILENOTIFICATION',
        'DRAFTNOTIFICATION',
        'FILEDET<PERSON>ILSRESPONSE',
        'DRAFT<PERSON><PERSON><PERSON>SRESPONS<PERSON>',
        'GENERICPARAMS',
        'MODULEKEY',
        'IS<PERSON>LL<PERSON><PERSON>',
        'COMPANYID',
        'ORGID',
        'DELETEDRECORDID',
        'DELETEDRECVENDORID',
        'DELETEDRECSTATE',
        'DELETEDRECNO',
        'DELETEDRECAUWHENCREATED',
        'DELETEDRECWHENCREATED',
        'DELETEDRECDATE',
        'DELETEDRECCREATEDBY',
        'DELETEDRECMODIFIEDBY',
        'BILLINGDATE',
        'AUTOMATIONTYPE',
    ),

    'schema' => array(
        'RECORDNO' => 'record#',
        'LOCATIONKEY' => 'locationkey',
        'STXFILEID' => 'stxfileid',
        'STXDRAFTINVOICEID' => 'stxdraftinvoiceid',
        'SUPDOCID' => 'supdocid',
        'PRRECORDKEY' => 'prrecordkey',
        'STATE' => 'state',
        'ERRORMSG' => 'errormsg',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'UPLOADERRORS' => 'uploaderrors',
        'SOURCE' => 'source',
        'MULTILINECONFIG' => 'multilineconfig',
        'WHENMODIFIED' => 'whenmodified',
        'WHENCREATED' => 'whencreated',
        'WEBHOOKSTATUS' => 'webhookstatus',
        'FEEDBACK'=>'feedback',
        'FILENOTIFICATIONTIME' => 'filenotificationtime',
        'DRAFTNOTIFICATIONTIME' => 'draftnotificationtime',
        'FILECOMPLETIONTIME' => 'filecompletiontime',
        'FILENOTIFICATION' => 'filenotification',
        'DRAFTNOTIFICATION' => 'draftnotification',
        'FILEDETAILSRESPONSE' => 'filedetailsresponse',
        'DRAFTDETAILSRESPONSE' => 'draftdetailsresponse',
        'GENERICPARAMS' => 'genericparams',
        'ISBILLABLE' => 'isbillable',
        'COMPANYID ' => 'companyid',
        'ORGID' => 'orgid',
        'DELETEDRECORDID' => 'deletedrecordid',
        'DELETEDRECNO' => 'deletedrecno',
        'DELETEDRECVENDORID' => 'deletedrecvendorid',
        'DELETEDRECSTATE' => 'deletedrecstate',
        'DELETEDRECAUWHENCREATED' => 'deletedrecauwhencreated',
        'DELETEDRECWHENCREATED' => 'deletedrecwhencreated',
        'DELETEDRECDATE'=>'deletedrecdate',
        'DELETEDRECCREATEDBY' => 'deletedreccreatedby',
        'DELETEDRECMODIFIEDBY' => 'deletedrecmodifiedby',
        'MODULEKEY' => 'modulekey',
        'BILLINGDATE' => 'billingdate',
        'AUTOMATIONTYPE' => 'automationtype',
    ),

    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED'
    ),

    'fieldinfo' => array (
        array (
            'path' => 'RECORDNO',
            'desc' => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'hidden' => true,
            'type' => array (
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'id' => 1,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'LOCATIONKEY',
            'fullname' => 'IA.LOCATION_ID',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'location',
                'pickentity' => 'locationpick'
            ),
            'noedit' => true,
            'nonew' => true,
            'noview' => true,
            'required' => false,
            'id' => 3,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'STXFILEID',
            'fullname' => 'IA.SMART_TRANSACTION_FILE_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
            ),
            'required' => false,
            'id' => 5,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'STXDRAFTINVOICEID',
            'fullname' => 'IA.SMART_TRANSACTION_DRAFT_INVOICE_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
                'size' => 40,
            ),
            'required' => false,
            'id' => 6,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'SUPDOCID',
            'fullname' => 'IA.ATTACHMENT_KEY',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'size' => 40,
            ),
            'required' => false,
            'id' => 7,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'PRRECORDKEY',
            'fullname' => 'IA.PRRECORD_RECORDKEY',
            'type' => array (
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'readonly' => false,
            'id' => 8,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'STATE',
            'fullname' => 'IA.UPLOADED_FILE_STATE',
            'readonly' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 10,
                'size' => 10,
                'validlabels' => array('IA.PENDING', 'IA.DRAFTREADY', 'IA.COMPLETED', 'IA.ERROR','IA.CANCELLED'),
                'validvalues' => array('Pending', 'Draftready', 'Completed', 'Error','Cancelled'),
                '_validivalues' => array('P', 'D', 'C', 'E','CN')
            ),
            'id' => 9,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'ERRORMSG',
            'fullname' => 'IA.ERROR_MESSAGE',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'multitext',
                'maxlength' => 1000,
            ),
            'id' => 10,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'UPLOADERRORS',
            'fullname' => 'IA.INTACCT_ERROR_TO_BE_CAPTURED',
            'readonly' => true,
            'type' => array (
                'ptype' => 'multitext',
                'type' => 'multitext',
                'maxlength'=>4000,
                'size' => 4000,
            ),
            'id' => 12,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'ATTACHFILES',
            'fullname' => 'IA.DRAG_AND_DROP_FILES_HERE_OR',
            'desc' => 'IA.ATTACH_FILES',
            'type' => array (
                'ptype' => 'file', 'type' => 'upload'
            ),
            'id' => 13,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'SOURCE',
            'fullname' => 'IA.MODULE_WHO_HAS_UPLOADED_THE_DOCUMENT',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
                'size' => 40,
            ),
            'readonly' => false,
            'id' => 14,
            'fastUpdate' => true,
        ),
        array(
            'fullname' => 'IA.MILTI_LINE_CONFIGURATION_FOR_UPDATING_THE_BILL_FRO',
            'required' => true,
            'type' => array (
                'ptype' => 'radio', 'type' => 'radio',
                'validlabels' => array('IA.A_SINGLE_LINE_ITEM_THAT_SUMMARIZES_THE_TOTAL', 'IA.ALL_LINE_ITEMS_AND_ASSOCIATED_AMOUNTS'),
                'validvalues' => array('Single', 'Multiline'),
                '_validivalues' => array('Single', 'Multiline'),
            ),
            'layout' => 'portrait',
            'default' => 'Single',
            'path' => 'MULTILINECONFIG',
            'id' => 15,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'WEBHOOKSTATUS',
            'fullname' => 'IA.WEBHOOK_STATUS_TO_READ_IF_THE_NOTIFICATION_IS_RECE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'validlabels' => array('IA.NONE', 'IA.FILE', 'IA.DRAFT'),
                'validvalues' => array('None', 'File', 'Draft'),
                '_validivalues' => array('None', 'File', 'Draft'),
            ),
            'default' => 'None',
            'readonly' => false,
            'id' => 16,
            'fastUpdate' => true,
        ),
        array(
            'fullname' => 'IA.THIS_WILL_BE_THE_STATUS_TO_CHECK_IF_THE_DOCUMENT_I',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'validlabels' => array('IA.F', 'IA.T'),
                'validvalues' => array('F', 'T'),
                '_validivalues' => array('F', 'T'),
            ),
            'default' => 'F',
            'path' => 'FEEDBACK',
            'id' => 17,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'FILENOTIFICATIONTIME',
            'fullname' => 'IA.TIME_WE_RECEIVED_THE_FILE_NOTIFICATION',
            'type'      => array(
                'type'      => 'timestamp',
                'maxlength' => 22,
                'size'      => 22,
            ),
            'id' => 18,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'DRAFTNOTIFICATIONTIME',
            'fullname' => 'IA.TIME_WE_RECEIVED_THE_DRAFT_NOTIFICATION',
            'type'      => array(
                'type'      => 'timestamp',
                'maxlength' => 22,
                'size'      => 22,
            ),
            'id' => 19,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'FILECOMPLETIONTIME',
            'fullname' => 'IA.TIME_WHEN_THE_FILE_HAS_BEED_COMLETED_ITS',
            'type'      => array(
                'type'      => 'timestamp',
                'maxlength' => 22,
                'size'      => 22,
            ),
            'id' => 20,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'FILENOTIFICATION',
            'fullname' => 'IA.COLLECT_THE_JSON_VALUE_RECEIVED_IN_THE_FILE',
            'readonly' => true,
            'type' => array (
                'ptype' => 'multitext',
                'type' => 'multitext',
                'maxlength'=>4000,
                'size' => 4000,
            ),
            'id' => 21,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'DRAFTNOTIFICATION',
            'fullname' => 'IA.COLLECT_THE_JSON_VALUE_RECEIVED_IN_THE_DRAFT',
            'readonly' => true,
            'type' => array (
                'ptype' => 'multitext',
                'type' => 'multitext',
                'maxlength'=>4000,
                'size' => 4000,
            ),
            'id' => 22,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'FILEDETAILSRESPONSE',
            'fullname' => 'IA.COLLECT_THE_JSON_VALUE_RECEIVED_IN_THE_GET_FILE',
            'readonly' => true,
            'type' => array (
                'ptype' => 'multitext',
                'type' => 'multitext',
                'maxlength'=>4000,
                'size' => 4000,
            ),
            'id' => 23,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'DRAFTDETAILSRESPONSE',
            'fullname' => 'IA.COLLECT_THE_JSON_VALUE_RECEIVED_IN_THE_GET',
            'readonly' => true,
            'type' => array (
                'ptype' => 'multitext',
                'type' => 'multitext',
                'maxlength'=>4000,
                'size' => 4000,
            ),
            'id' => 24,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'GENERICPARAMS',
            'fullname' => 'IA.GENERIC_WAY_TO_SAVE_THE_PARAMETERS',
            'desc'=>'IA.GENERIC_WAY_TO_SAVE_THE_PARAMETERS',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
                'size' => 50,
            ),
            'id' => 25,
            'fastUpdate' => true,
        ),
        array(
            'path' => 'ISBILLABLE',
            'fullname' => 'IA.ISBILLABLE',
            'desc' => 'IA.ISBILLABLEDESC',
            'readonly' => true,
            'type' => array(
                'ptype' => 'boolean',
                'type' => 'text',
                'validlabels' => ['T', 'F'],
                'validvalues' => ['T', 'F'],
                '_validivalues' => ['T', 'F']
            ),
            'default' => 'F',
            'fastUpdate' => true,
            'id' => 26,
        ),
        array(
            'path' => 'COMPANYID',
            'fullname' => 'IA.COMPANYID',
            'desc' => 'IA.COMPANYID',
            'readonly' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'fastUpdate' => true,
            'id' => 27,
        ),
        array(
            'path' => 'ORGID',
            'fullname' => 'IA.ORGID',
            'desc' => 'IA.ORGID',
            'readonly' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'fastUpdate' => true,
            'id' => 28,
        ),
        array(
            'path' => 'DELETEDRECORDID',
            'fullname' => 'IA.DELETEDRECORDID',
            'desc' => 'IA.DELETEDRECORDID',
            'readonly' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'fastUpdate' => true,
            'id' => 29,
        ),
        array(
            'path' => 'DELETEDRECNO',
            'fullname' => 'IA.DELETEDRECNO',
            'desc' => 'IA.DELETEDRECNO',
            'readonly' => true,
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 15,
                'size' => 15,
                'format' => $gRecordNoFormat,
            ),
            'fastUpdate' => true,
            'id' => 30,
        ),
        array(
            'path' => 'DELETEDRECVENDORID',
            'fullname' => 'IA.DELETEDRECVENDORID',
            'desc' => 'IA.DELETEDRECVENDORID',
            'readonly' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'fastUpdate' => true,
            'id' => 31,
        ),
        array(
            'path' => 'DELETEDRECSTATE',
            'fullname' => 'IA.DELETEDRECSTATE',
            'desc' => 'IA.DELETEDRECSTATE',
            'readonly' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'fastUpdate' => true,
            'id' => 32,
        ),
        array(
            'path' => 'DELETEDRECAUWHENCREATED',
            'fullname' => 'IA.DELETEDRECAUWHENCREATED',
            'desc' => 'IA.DELETEDRECAUWHENCREATED',
            'readonly' => true,
            'type' => array(
                'type' => 'timestamp',
                'maxlength' => 22,
                'size' => 22,
            ),
            'fastUpdate' => true,
            'id' => 33,
        ),
        array(
            'path' => 'DELETEDRECWHENCREATED',
            'fullname' => 'IA.DELETEDRECWHENCREATED',
            'desc' => 'IA.DELETEDRECWHENCREATED',
            'readonly' => true,
            'type' => array(
                'type' => 'timestamp',
                'maxlength' => 22,
                'size' => 22,
            ),
            'fastUpdate' => true,
            'id' => 34,
        ),
        array(
            'path' => 'DELETEDRECDATE',
            'fullname' => 'IA.DELETEDRECDATE',
            'desc' => 'IA.DELETEDRECDATE',
            'readonly' => true,
            'type' => array(
                'type' => 'timestamp',
                'maxlength' => 22,
                'size' => 22,
            ),
            'fastUpdate' => true,
            'id' => 35,
        ),
        array(
            'path' => 'DELETEDRECCREATEDBY',
            'fullname' => 'IA.DELETEDRECCREATEDBY',
            'desc' => 'IA.DELETEDRECCREATEDBY',
            'readonly' => true,
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'integer'
            ),
            'fastUpdate' => true,
            'id' => 36,
        ),
        array(
            'path' => 'DELETEDRECMODIFIEDBY',
            'fullname' => 'IA.DELETEDRECMODIFIEDBY',
            'desc' => 'IA.DELETEDRECMODIFIEDBY',
            'readonly' => true,
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'integer'
            ),
            'fastUpdate' => true,
            'id' => 37,
        ),
        array(
            'path' => 'MODULEKEY',
            'fullname' => 'IA.MODULEKEY',
            'desc' => 'IA.MODULEKEY',
            'readonly' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'fastUpdate' => true,
            'id' => 38,
        ),
        array(
            'path' => 'BILLINGDATE',
            'fullname' => 'IA.BILL_DATE',
            'desc' => 'IA.BILL_DATE',
            'readonly' => true,
            'type' => array(
                'type' => 'timestamp',
                'maxlength' => 22,
                'size' => 22,
            ),
            'fastUpdate' => true,
            'id' => 39,
        ),
        array(
            'path' => 'AUTOMATIONTYPE',
            'fullname' => 'IA.AUTOMATION_TYPE',
            'desc' => 'IA.AUTOMATION_TYPE',
            'readonly' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'fastUpdate' => true,
            'id' => 40,
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo
    ),
    'table' => 'stxfiledetails',
    'vid' => 'STXFILEID',
    'printas' => 'IA.SMART_TRANSACTION_FILE_UPLOAD',
    'pluralprintas' => 'IA.SMART_TRANSACTION_FILE_UPLOAD',
    'module' => 'ap',
    'autoincrement' => 'RECORDNO',
    'auditcolumns' => true,
    'nochatter' => true,
    'fastUpdate' => true,
);
