<?php

global $gRecordNoFormat, $gDateType;
$kSchemas['poautomatedtransactionfileupload'] = [

    'object' => [
        'RECORDNO',
        'LOCATIONKEY',
        'STXFILEID',
        'STXDRAFTINVOICEID',
        '<PERSON><PERSON>DOCID',
        //'PRRECORDKEY',
        'DOCHDRKEY',
        'STATE',
        'ERRORMSG',
        'UPLOADERRORS',
        'SOURCE',
        'MULTILINECONFIG',
        'CREATEDBY',
        'MODIFIEDBY',
        'WHENMODIFIED',
        'WHENCREATED',
        'WEBHOOKSTATUS',
        'FEEDBACK',
        'FILENOTIFICATIONTIME',
        'DRAFT<PERSON>OT<PERSON><PERSON>ATIONTI<PERSON>',
        'FILECOMPLETIONTIME',
        'FILENOTIFICATION',
        'DRAFTNOTIFICATION',
        'FILEDETAILSRESPONSE',
        'DRAFTDETAILSRESPONSE',
        'GENERICPARAMS',
        'MODULEKEY',
        'ISBI<PERSON><PERSON><PERSON>',
        'COMPANYID',
        'ORGID',
        'DELE<PERSON>DRECORDID',
        '<PERSON>LE<PERSON><PERSON><PERSON><PERSON><PERSON>OR<PERSON>',
        '<PERSON><PERSON><PERSON>DRECSTATE',
        'DELETEDRECNO',
        'DELETEDRECAUWHENCREATED',
        'DELETEDRECWHENCREATED',
        'DELETEDRECDATE',
        'DELETEDRECCREATEDBY',
        'DELETEDRECMODIFIEDBY',
        'BILLINGDATE',
        'AUTOMATIONTYPE',
    ],

    'schema' => [
        'RECORDNO'                => 'record#',
        'LOCATIONKEY'             => 'locationkey',
        'STXFILEID'               => 'stxfileid',
        'STXDRAFTINVOICEID'       => 'stxdraftinvoiceid',
        'SUPDOCID'                => 'supdocid',
        // 'PRRECORDKEY'             => 'prrecordkey',
        'DOCHDRKEY'             => 'dochdrkey',
        'STATE'                   => 'state',
        'ERRORMSG'                => 'errormsg',
        'CREATEDBY'               => 'createdby',
        'MODIFIEDBY'              => 'modifiedby',
        'UPLOADERRORS'            => 'uploaderrors',
        'SOURCE'                  => 'source',
        'MULTILINECONFIG'         => 'multilineconfig',
        'WHENMODIFIED'            => 'whenmodified',
        'WHENCREATED'             => 'whencreated',
        'WEBHOOKSTATUS'           => 'webhookstatus',
        'FEEDBACK'                => 'feedback',
        'FILENOTIFICATIONTIME'    => 'filenotificationtime',
        'DRAFTNOTIFICATIONTIME'   => 'draftnotificationtime',
        'FILECOMPLETIONTIME'      => 'filecompletiontime',
        'FILENOTIFICATION'        => 'filenotification',
        'DRAFTNOTIFICATION'       => 'draftnotification',
        'FILEDETAILSRESPONSE'     => 'filedetailsresponse',
        'DRAFTDETAILSRESPONSE'    => 'draftdetailsresponse',
        'GENERICPARAMS'           => 'genericparams',
        'ISBILLABLE'              => 'isbillable',
        'COMPANYID '              => 'companyid',
        'ORGID'                   => 'orgid',
        'DELETEDRECORDID'         => 'deletedrecordid',
        'DELETEDRECNO'            => 'deletedrecno',
        'DELETEDRECVENDORID'      => 'deletedrecvendorid',
        'DELETEDRECSTATE'         => 'deletedrecstate',
        'DELETEDRECAUWHENCREATED' => 'deletedrecauwhencreated',
        'DELETEDRECWHENCREATED'   => 'deletedrecwhencreated',
        'DELETEDRECDATE'          => 'deletedrecdate',
        'DELETEDRECCREATEDBY'     => 'deletedreccreatedby',
        'DELETEDRECMODIFIEDBY'    => 'deletedrecmodifiedby',
        'MODULEKEY'               => 'modulekey',
        'BILLINGDATE'             => 'billingdate',
        'AUTOMATIONTYPE'          => 'automationtype',
    ],

    'sqldomarkup'     => true,
    'sqlmarkupfields' => [
        'WHENCREATED',
        'WHENMODIFIED'
    ],

    'fieldinfo'     => [
        [
            'path'       => 'RECORDNO',
            'desc'       => 'IA.RECORD_NUMBER',
            'fullname'   => 'IA.RECORD_NUMBER',
            'hidden'     => true,
            'type'       => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
            'id'         => 1,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'LOCATIONKEY',
            'fullname'   => 'IA.LOCATION_ID',
            'type'       => [
                'type'       => 'ptr',
                'ptype'      => 'ptr',
                'entity'     => 'location',
                'pickentity' => 'locationpick'
            ],
            'noedit'     => true,
            'nonew'      => true,
            'noview'     => true,
            'required'   => false,
            'id'         => 3,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'STXFILEID',
            'fullname'   => 'IA.SMART_TRANSACTION_FILE_ID',
            'type'       => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 50,
            ],
            'required'   => false,
            'id'         => 5,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'STXDRAFTINVOICEID',
            'fullname'   => 'IA.SMART_TRANSACTION_DRAFT_INVOICE_ID',
            'type'       => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 50,
                'size'      => 40,
            ],
            'required'   => false,
            'id'         => 6,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'SUPDOCID',
            'fullname'   => 'IA.ATTACHMENT_KEY',
            'type'       => [
                'ptype' => 'text',
                'type'  => 'text',
                'size'  => 40,
            ],
            'required'   => false,
            'id'         => 7,
            'fastUpdate' => true,
        ],
        // [
        //     'path'       => 'PRRECORDKEY',
        //     'fullname'   => 'IA.PRRECORD_RECORDKEY',
        //     'type'       => [
        //         'ptype'     => 'integer',
        //         'type'      => 'integer',
        //         'maxlength' => 8,
        //         'size'      => 8,
        //         'format'    => $gRecordNoFormat,
        //     ],
        //     'readonly'   => false,
        //     'id'         => 8,
        //     'fastUpdate' => true,
        // ],
        [
            'path' => 'DOCHDRKEY',
            'fullname' => 'IA.DOCHDR_RECORDKEY',
            'type' => [
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ],
            'readonly' => false,
            'id' => 8,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'STATE',
            'fullname'   => 'IA.UPLOADED_FILE_STATE',
            'readonly'   => true,
            'type'       => [
                'ptype'         => 'text',
                'type'          => 'text',
                'maxlength'     => 10,
                'size'          => 10,
                'validlabels'   => [ 'IA.PENDING', 'IA.DRAFTREADY', 'IA.COMPLETED', 'IA.ERROR', 'IA.CANCELLED' ],
                'validvalues'   => [ 'Pending', 'Draftready', 'Completed', 'Error', 'Cancelled' ],
                '_validivalues' => [ 'P', 'D', 'C', 'E', 'CN' ]
            ],
            'id'         => 9,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'ERRORMSG',
            'fullname'   => 'IA.ERROR_MESSAGE',
            'type'       => [
                'ptype'     => 'multitext',
                'type'      => 'multitext',
                'maxlength' => 1000,
            ],
            'id'         => 10,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'UPLOADERRORS',
            'fullname'   => 'IA.INTACCT_ERROR_TO_BE_CAPTURED',
            'readonly'   => true,
            'type'       => [
                'ptype'     => 'multitext',
                'type'      => 'multitext',
                'maxlength' => 4000,
                'size'      => 4000,
            ],
            'id'         => 12,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'ATTACHFILES',
            'fullname'   => 'IA.DRAG_AND_DROP_FILES_HERE_OR',
            'desc'       => 'IA.ATTACH_FILES',
            'type'       => [
                'ptype' => 'file', 'type' => 'upload'
            ],
            'id'         => 13,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'SOURCE',
            'fullname'   => 'IA.MODULE_WHO_HAS_UPLOADED_THE_DOCUMENT',
            'type'       => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 50,
                'size'      => 40,
            ],
            'readonly'   => false,
            'id'         => 14,
            'fastUpdate' => true,
        ],
        [
            'fullname'   => 'IA.MILTI_LINE_CONFIGURATION_FOR_UPDATING_THE_BILL_FRO',
            'required'   => true,
            'type'       => [
                'ptype'         => 'radio', 'type' => 'radio',
                'validlabels'   => [ 'IA.A_SINGLE_LINE_ITEM_THAT_SUMMARIZES_THE_TOTAL',
                                     'IA.ALL_LINE_ITEMS_AND_ASSOCIATED_AMOUNTS' ],
                'validvalues'   => [ 'Single', 'Multiline' ],
                '_validivalues' => [ 'Single', 'Multiline' ],
            ],
            'layout'     => 'portrait',
            'default'    => 'Single',
            'path'       => 'MULTILINECONFIG',
            'id'         => 15,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'WEBHOOKSTATUS',
            'fullname'   => 'IA.WEBHOOK_STATUS_TO_READ_IF_THE_NOTIFICATION_IS_RECE',
            'type'       => [
                'ptype'         => 'text',
                'type'          => 'text',
                'validlabels'   => [ 'IA.NONE', 'IA.FILE', 'IA.DRAFT' ],
                'validvalues'   => [ 'None', 'File', 'Draft' ],
                '_validivalues' => [ 'None', 'File', 'Draft' ],
            ],
            'default'    => 'None',
            'readonly'   => false,
            'id'         => 16,
            'fastUpdate' => true,
        ],
        [
            'fullname'   => 'IA.THIS_WILL_BE_THE_STATUS_TO_CHECK_IF_THE_DOCUMENT_I',
            'required'   => false,
            'type'       => [
                'ptype'         => 'text',
                'type'          => 'text',
                'validlabels'   => [ 'IA.F', 'IA.T' ],
                'validvalues'   => [ 'F', 'T' ],
                '_validivalues' => [ 'F', 'T' ],
            ],
            'default'    => 'F',
            'path'       => 'FEEDBACK',
            'id'         => 17,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'FILENOTIFICATIONTIME',
            'fullname'   => 'IA.TIME_WE_RECEIVED_THE_FILE_NOTIFICATION',
            'type'       => [
                'type'      => 'timestamp',
                'maxlength' => 22,
                'size'      => 22,
            ],
            'id'         => 18,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'DRAFTNOTIFICATIONTIME',
            'fullname'   => 'IA.TIME_WE_RECEIVED_THE_DRAFT_NOTIFICATION',
            'type'       => [
                'type'      => 'timestamp',
                'maxlength' => 22,
                'size'      => 22,
            ],
            'id'         => 19,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'FILECOMPLETIONTIME',
            'fullname'   => 'IA.TIME_WHEN_THE_FILE_HAS_BEED_COMLETED_ITS',
            'type'       => [
                'type'      => 'timestamp',
                'maxlength' => 22,
                'size'      => 22,
            ],
            'id'         => 20,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'FILENOTIFICATION',
            'fullname'   => 'IA.COLLECT_THE_JSON_VALUE_RECEIVED_IN_THE_FILE',
            'readonly'   => true,
            'type'       => [
                'ptype'     => 'multitext',
                'type'      => 'multitext',
                'maxlength' => 4000,
                'size'      => 4000,
            ],
            'id'         => 21,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'DRAFTNOTIFICATION',
            'fullname'   => 'IA.COLLECT_THE_JSON_VALUE_RECEIVED_IN_THE_DRAFT',
            'readonly'   => true,
            'type'       => [
                'ptype'     => 'multitext',
                'type'      => 'multitext',
                'maxlength' => 4000,
                'size'      => 4000,
            ],
            'id'         => 22,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'FILEDETAILSRESPONSE',
            'fullname'   => 'IA.COLLECT_THE_JSON_VALUE_RECEIVED_IN_THE_GET_FILE',
            'readonly'   => true,
            'type'       => [
                'ptype'     => 'multitext',
                'type'      => 'multitext',
                'maxlength' => 4000,
                'size'      => 4000,
            ],
            'id'         => 23,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'DRAFTDETAILSRESPONSE',
            'fullname'   => 'IA.COLLECT_THE_JSON_VALUE_RECEIVED_IN_THE_GET',
            'readonly'   => true,
            'type'       => [
                'ptype'     => 'multitext',
                'type'      => 'multitext',
                'maxlength' => 4000,
                'size'      => 4000,
            ],
            'id'         => 24,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'GENERICPARAMS',
            'fullname'   => 'IA.GENERIC_WAY_TO_SAVE_THE_PARAMETERS',
            'desc'       => 'IA.GENERIC_WAY_TO_SAVE_THE_PARAMETERS',
            'type'       => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 50,
                'size'      => 50,
            ],
            'id'         => 25,
            'fastUpdate' => true,
        ],
        [
            'path'       => 'ISBILLABLE',
            'fullname'   => 'IA.ISBILLABLE',
            'desc'       => 'IA.ISBILLABLEDESC',
            'readonly'   => true,
            'type'       => [
                'ptype'         => 'boolean',
                'type'          => 'text',
                'validlabels'   => [ 'T', 'F' ],
                'validvalues'   => [ 'T', 'F' ],
                '_validivalues' => [ 'T', 'F' ]
            ],
            'default'    => 'F',
            'fastUpdate' => true,
            'id'         => 26,
        ],
        [
            'path'       => 'COMPANYID',
            'fullname'   => 'IA.COMPANYID',
            'desc'       => 'IA.COMPANYID',
            'readonly'   => true,
            'type'       => [
                'ptype' => 'text',
                'type'  => 'text'
            ],
            'fastUpdate' => true,
            'id'         => 27,
        ],
        [
            'path'       => 'ORGID',
            'fullname'   => 'IA.ORGID',
            'desc'       => 'IA.ORGID',
            'readonly'   => true,
            'type'       => [
                'ptype' => 'text',
                'type'  => 'text'
            ],
            'fastUpdate' => true,
            'id'         => 28,
        ],
        [
            'path'       => 'DELETEDRECORDID',
            'fullname'   => 'IA.DELETEDRECORDID',
            'desc'       => 'IA.DELETEDRECORDID',
            'readonly'   => true,
            'type'       => [
                'ptype' => 'text',
                'type'  => 'text'
            ],
            'fastUpdate' => true,
            'id'         => 29,
        ],
        [
            'path'       => 'DELETEDRECNO',
            'fullname'   => 'IA.DELETEDRECNO',
            'desc'       => 'IA.DELETEDRECNO',
            'readonly'   => true,
            'type'       => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 15,
                'size'      => 15,
                'format'    => $gRecordNoFormat,
            ],
            'fastUpdate' => true,
            'id'         => 30,
        ],
        [
            'path'       => 'DELETEDRECVENDORID',
            'fullname'   => 'IA.DELETEDRECVENDORID',
            'desc'       => 'IA.DELETEDRECVENDORID',
            'readonly'   => true,
            'type'       => [
                'ptype' => 'text',
                'type'  => 'text'
            ],
            'fastUpdate' => true,
            'id'         => 31,
        ],
        [
            'path'       => 'DELETEDRECSTATE',
            'fullname'   => 'IA.DELETEDRECSTATE',
            'desc'       => 'IA.DELETEDRECSTATE',
            'readonly'   => true,
            'type'       => [
                'ptype' => 'text',
                'type'  => 'text'
            ],
            'fastUpdate' => true,
            'id'         => 32,
        ],
        [
            'path'       => 'DELETEDRECAUWHENCREATED',
            'fullname'   => 'IA.DELETEDRECAUWHENCREATED',
            'desc'       => 'IA.DELETEDRECAUWHENCREATED',
            'readonly'   => true,
            'type'       => [
                'type'      => 'timestamp',
                'maxlength' => 22,
                'size'      => 22,
            ],
            'fastUpdate' => true,
            'id'         => 33,
        ],
        [
            'path'       => 'DELETEDRECWHENCREATED',
            'fullname'   => 'IA.DELETEDRECWHENCREATED',
            'desc'       => 'IA.DELETEDRECWHENCREATED',
            'readonly'   => true,
            'type'       => [
                'type'      => 'timestamp',
                'maxlength' => 22,
                'size'      => 22,
            ],
            'fastUpdate' => true,
            'id'         => 34,
        ],
        [
            'path'       => 'DELETEDRECDATE',
            'fullname'   => 'IA.DELETEDRECDATE',
            'desc'       => 'IA.DELETEDRECDATE',
            'readonly'   => true,
            'type'       => [
                'type'      => 'timestamp',
                'maxlength' => 22,
                'size'      => 22,
            ],
            'fastUpdate' => true,
            'id'         => 35,
        ],
        [
            'path'       => 'DELETEDRECCREATEDBY',
            'fullname'   => 'IA.DELETEDRECCREATEDBY',
            'desc'       => 'IA.DELETEDRECCREATEDBY',
            'readonly'   => true,
            'type'       => [
                'ptype' => 'ptr',
                'type'  => 'integer'
            ],
            'fastUpdate' => true,
            'id'         => 36,
        ],
        [
            'path'       => 'DELETEDRECMODIFIEDBY',
            'fullname'   => 'IA.DELETEDRECMODIFIEDBY',
            'desc'       => 'IA.DELETEDRECMODIFIEDBY',
            'readonly'   => true,
            'type'       => [
                'ptype' => 'ptr',
                'type'  => 'integer'
            ],
            'fastUpdate' => true,
            'id'         => 37,
        ],
        [
            'path'       => 'MODULEKEY',
            'fullname'   => 'IA.MODULEKEY',
            'desc'       => 'IA.MODULEKEY',
            'readonly'   => true,
            'type'       => [
                'ptype' => 'text',
                'type'  => 'text'
            ],
            'fastUpdate' => true,
            'id'         => 38,
        ],
        [
            'path' => 'BILLINGDATE',
            'fullname' => 'IA.BILL_DATE',
            'desc' => 'IA.BILL_DATE',
            'readonly' => true,
            'type' => [
                'type' => 'timestamp',
                'maxlength' => 22,
                'size' => 22,
            ],
            'fastUpdate' => true,
            'id' => 39,
        ],
        [
            'path' => 'AUTOMATIONTYPE',
            'fullname' => 'IA.AUTOMATION_TYPE',
            'desc' => 'IA.AUTOMATION_TYPE',
            'readonly' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'fastUpdate' => true,
            'id' => 40,
        ],
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo
    ],
    'table'         => 'stxfiledetails',
    'vid'           => 'STXFILEID',
    'printas'       => 'IA.AUTOMATED_TRANSACTION',
    'pluralprintas' => 'IA.AUTOMATED_TRANSACTIONS',
    'module'        => 'po',
    'autoincrement' => 'RECORDNO',
    'auditcolumns'  => true,
    'nochatter'     => true,
    'fastUpdate'    => true,
];
