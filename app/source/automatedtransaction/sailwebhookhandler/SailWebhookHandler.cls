<?php

/**
 *  SAILWebhookHandler handler
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Intacct Corporation All, Rights Reserved
 */
class SailWebhookHandler
{

    public const PREDICTION_COMPLETED = 'prediction_completed';
    public const FILEAVAILABLE = 'FileAvailable';

    private const ACTION = 'add';
    public const FILERESOURCETYPE = 'File';

    public const MANUALSERVICE = 'manual';

    public const FILE_NOTIFICATION_BODY_TYPE = 'body';

    public const MAILBOX = 'mailbox';

    /**
     * @var array
     */
    private array $sailFileDetails = [];

    /**
     * SailWebhoookHandler constructor.
     */
    public function __construct()
    {

    }

    /**
     * @param array $request
     * @return bool
     * @throws JsonException
     */
    public function handleNotification(array $request): bool
    {
        LogToFile('SAILWEBHOOK WEBHOOK HANDLER : Handle Notifcation Start');
        $ok = true;
        $fileId = $request['orchestrationid'];
        $sailFileDetails = self::checkRecordInSailFileDetails($fileId);

        $inputType = !empty($sailFileDetails) ?
            $sailFileDetails['SERVICESOURCE'] : AutomatedTransactionFileUploadManager::EMAIL_SERVICE;

        //prepare the data for sailfiledetails
        $values = self::prepareSailFileDetailsData($request, $sailFileDetails, $inputType, $fileId);

        $sailMgr = self::decideUploadEntity($sailFileDetails);


        if (empty($sailFileDetails)) {
            LogToFile('SAILWEBHOOK WEBHOOK HANDLER : SAILFILEDETAILS ADD');
            //inset to sailfiledetails
            $ok = $ok && $sailMgr->add($values);
        } else{
            LogToFile('SAILWEBHOOK WEBHOOK HANDLER : SAILFILEDETAILS SET');
            $ok = $ok && $sailMgr->set($values);
        }

        if (!empty($sailFileDetails) && $sailFileDetails['MODULEKEY'] == Globals::$g->kPOid
            && isset($request['errortype'])) {
            $sql = "SELECT DOCHDRKEY FROM STXFILEDETAILS WHERE CNY#=:2 AND STXFILEID=:3";
            $sql = "UPDATE DOCHDR SET STATE=:1 WHERE CNY#=:2 AND RECORD#=($sql)";
            $ok = $ok && ExecStmt(array($sql,'I',GetMyCompany(),$fileId));
        }

        if ($ok && self::canInsertJobToFifo($request)) {
            LogToFile('SAILWEBHOOK WEBHOOK HANDLER : ADD Job to FIFO queue');
            $ok = self::addJobInFifoQueue($request, $fileId);
        }

        LogToFile('SAILWEBHOOK WEBHOOK HANDLER : Handle Notifcation End. FILEID: '. $fileId);
        return $ok;
    }

    /**
     * @param $notificationBody
     * @return false
     * @throws Exception
     */
    public static function createNewSessionFromCompanyId($notificationBody)
    {
        LogToFile('SAILWEBHOOK CONSUMER : Create newsession in webhooks start');
        $ok = true;
        //Get the wpb locationand companyid
        $sailCompanyId = $notificationBody['companyidwithentity'];
        $companyIdLocationKey = self::getCompanyIdLocationkey($sailCompanyId);
        $meLocation = $companyIdLocationKey ?? '';
        LogToFile('SAILWEBHOOK CONSUMER : COMPANYIDLOCATIONKEY - ' . json_encode($companyIdLocationKey));
        LogToFile('SAILWEBHOOK CONSUMER : COMPANYID - ' . $sailCompanyId);
        LogToFile('SAILWEBHOOK CONSUMER : Location - ' . $meLocation);
        if (!self::createNewSession('FILEUPLOADNOTIFICATION', GetMyCompany(), UserInfoManager::SYSTEMUSER, $meLocation)) {
            LogToFile('SAILWEBHOOK CONSUMER : Create newsession failed!');
            $ok = false;
        }
        LogToFile('SAILWEBHOOK CONSUMER : Create newsession in webhooks End');

        return $ok;
    }

    /**
     * @param string $componentId
     * @param int $companyid
     * @param int $userRecNo
     * @param null $locationRecNo
     * @param null $departmentRecNo
     *
     * @return bool
     * @throws Exception
     */
    public static function createNewSession(
        string $componentId, int $companyid, int $userRecNo, $locationRecNo = null, $departmentRecNo = null
    ): bool {
        Backend_Init::SetEnvironment($companyid, $userRecNo);

        if (Globals::$g->_userid) {
            $dbOwnerName = Database::getDbInfo('ownerid');
            if ($dbOwnerName) {
                if (!SetCurrentSchema($dbOwnerName)) {
                    $msg = sprintf(
                        '%s: Cannot setup DB context for company - %s, user - %s, location - %s, department - %s',
                        $componentId,
                        $companyid,
                        $userRecNo,
                        $locationRecNo,
                        $departmentRecNo
                    );
                    throw new Exception($msg);
                }
            }
        }

        Request::$r->_sess = null;
        if (IASessionHandler::imsSetupSession(Globals::$g->_userid, '', $locationRecNo, $departmentRecNo) === null) {
            $msg = sprintf(
                '%s: Cannot setup session for company - %s, user - %s, location - %s, department - %s',
                $componentId,
                $companyid,
                $userRecNo,
                $locationRecNo,
                $departmentRecNo
            );
            throw new \RuntimeException($msg);
        }

        InitGlobals();

        //User/Company Based Initialization
        $_userprefs = &Globals::$g->userprefs;
        GetUserPreferences($_userprefs);
        InitModules();
        InitAuthorization();

        return true;
    }

    /**
     * @param $sailCompanyId
     * @return int|null
     */
    private static function getCompanyIdLocationkey(string $sailCompanyId): int|null
    {
        $guidLocationkey = explode("_", $sailCompanyId);
        if ($guidLocationkey[1] == '0') {
            return null;
        } else {
            return intval($guidLocationkey[1]);
        }
    }

    /**
     * @param array $notificationBody
     * @param array $sailFileDetails
     * @param string $inputType
     * @param string $fileId
     * @return array
     * @throws JsonException
     */
    private static function prepareSailFileDetailsData(array $notificationBody, array $sailFileDetails, string $inputType, string $fileId): array
    {
        LogToFile('SAILWEBHOOK HANDLER : prepareSailFileDetailsData Start');
        $values = [];
        $values['STXFILEID'] = $fileId;
        $values['COMPANYID'] =  $notificationBody['companyidwithentity']?? '';
        $values['ORGID'] = $notificationBody['companyidwithentity'] ?? '';
        $values['STATE'] = AutomatedTransactionFileUploadManager::DRAFT_AVAILABLE_STATE;
        $values['AUTOMATIONTYPE'] = 'SAIL';
        if($inputType !== AutomatedTransactionFileUploadManager::MANUAL_SERVICE){

            $qry = "SELECT atc.MULTILINE FROM automatedtransactioncompany atc
                 JOIN automatedtransactionsetup ats ON ats.record# = atc.setupkey and ats.cny# = atc.cny#
                 WHERE atc.cny#=:1 and atc.state = 'S' and atc.locationkey IS NULL and ats.state = 'S' and ats.provider = 'SAIL'";
            $qryResult = QueryResult(array($qry, GetMyCompany()));

            $values['MULTILINECONFIG'] = $qryResult[0]['MULTILINE'] == 'F' ? 'Single': 'Multiline';
            $values['SERVICESOURCE'] = $inputType;
            $values['SOURCE'] = $inputType;
        }

        if (isset($notificationBody['errortype'])) {
            $values['STATE'] = AutomatedTransactionFileUploadManager::DRAFT_ERROR_STATE;
            //$values['UPLOADERRORS'] = json_encode([$notificationBody['data']['errordescription']], JSON_THROW_ON_ERROR);
            $values['ERRORMSG'] = $notificationBody['errortype'];
        }


        $values['FILENOTIFICATIONTIME'] = GetTimestampGMT();
        $values['FILENOTIFICATION'] = json_encode($notificationBody);
        $values['WEBHOOKSTATUS'] = AutomatedTransactionFileUploadManager::FILE_NOTIFICTION_RECEIVED;
        LogToFile('SAILWEBHOOK FILE HANDLER : prepareSailFileDetailsData End');

        LogToFile('SAILWEBHOOK HANDLER : prepareSailFileDetailsData End');
        return $values;
    }

    /**
     * @param $notificationBody
     * @return bool
     */
    public static function canInsertJobToFifo($notificationBody)
    {
        if (isset($notificationBody['errortype']) &&
            $notificationBody['errortype'] !== SAILUploadFileHandler::DUPLICATE) {
            logToFileInfo("Automated transaction will not be processing this file: ".
                $notificationBody['orchestrationid']." due to error: ".$notificationBody['errortype']);
            return false;
        }
        return true;
    }

    /**
     * @param array $notificationBody
     * @param string $fileId
     *
     * @return bool
     * @throws JsonException
     */
    private static function addJobInFifoQueue(array $notificationBody, string $fileId): bool
    {
        LogToFile('SAILWEBHOOK WEBHOOK HANDLER : Addjob in fifo queue start');

        $sailFileDetails = self::checkRecordInSailFileDetails($fileId);
        $locationKey = self::getCompanyIdLocationkey($notificationBody['companyidwithentity']);
        if (!empty($sailFileDetails)
            && $sailFileDetails['SERVICESOURCE'] !== self::MANUALSERVICE) {
            /** @var STXExternalQueueManager $sailFileUploadQueueObjectMgr */
            $sailQueueObjectMgr = Globals::$g->gManagerFactory->getManager('stxexternalqueue');
            $doctype =
                (POSetupManager::isPOMatchEnabled() || POSetupManager::isStandalonePOMatchConfigured()) ?
                    Globals::$g->kPOid : Globals::$g->kAPid;
        } else {
            /** @var STXFileUploadQueueManager $sailFileUploadQueueObjectMgr */
            $sailQueueObjectMgr = Globals::$g->gManagerFactory->getManager('stxfileuploadqueue');
            $doctype = $sailFileDetails['MODULEKEY'];
        }

        $details[STXFileUploadQueueManager::STATUS] = $sailQueueObjectMgr::STXJOB_QUEUED;
        $details[STXExternalQueueManager::RESOURCETYPE] = $sailQueueObjectMgr::FILE;
        $details[STXExternalQueueManager::FILEID] = $fileId;
        $details['FILEERROR'] = false;
        if (isset($notificationBody['errortype'])){
            $details['FILEERROR'] = true;
        }

        $sailPackagequeue = [
            $sailQueueObjectMgr::TOPIC => self::ACTION . '_' . $sailFileDetails['SERVICESOURCE'] . '_service',
            $sailQueueObjectMgr::OBJECT => databaseStringCompress(json_encode($notificationBody, JSON_THROW_ON_ERROR)),
            $sailQueueObjectMgr::DOCTYPE => $doctype,
            $sailQueueObjectMgr::TYPE => $sailQueueObjectMgr->getType(),
            $sailQueueObjectMgr::DETAILS => json_encode($details, JSON_THROW_ON_ERROR),
            $sailQueueObjectMgr::OBJECTRECID => $fileId,
            $sailQueueObjectMgr::LOCATIONKEY => $locationKey,
            $sailQueueObjectMgr::USERREC => UserInfoManager::SYSTEMUSER
        ];
        try {
            $ok = $sailQueueObjectMgr->addJobInQueue($sailPackagequeue, true, false);
            LogToFile('SAILWEBHOOK WEBHOOK HANDLER ADDED JOBS TO FIFO QUEUE');
        } catch (Exception $exception) {
            $ok = false;
            LogToFile('SAILWEBHOOK WEBHOOK HANDLER ADD JOBS TO FIFO QUEUE FAILED: ' . $exception->getMessage());
        }

        LogToFile('SAILWEBHOOK WEBHOOK HANDLER : Addjob in fifo queue end');
        return $ok;
    }

    /**
     * @return array
     */
    public function getSailFileDetails() :array
    {
        return $this->sailFileDetails;
    }

    /**
     * @param array $sailFileDetails
     */
    public function setSailFileDetails(array $sailFileDetails)
    {
        $this->sailFileDetails = $sailFileDetails;
    }

    /**
     * @param string $fileId
     * @return array[]
     */
    public static function checkRecordInSailFileDetails($fileId)
    {
        $sailData = QueryResult(
            [
                "SELECT stxfileid, record#, state, servicesource, modulekey FROM stxfiledetails WHERE cny# =:1 AND stxfileid =:2",
                GetMyCompany(),
                $fileId
            ]
        );

        return !empty($sailData) ? $sailData[0] : [];
    }

    /**
     * This function decides which ent to be used to SAILFileupload
     *
     * @param array $sailFileDetails
     *
     * @return object
     */
    private static function decideUploadEntity(array $sailFileDetails)
    {
        if ( ! empty($sailFileDetails) ) {
            $sailUploadEntity =
                $sailFileDetails['MODULEKEY'] == Globals::$g->kPOid ? 'poautomatedtransactionfileupload' : 'apautomatedtransactionfileupload';
        } else {
            $sailUploadEntity =
                (POSetupManager::isPOMatchEnabled() || POSetupManager::isStandalonePOMatchConfigured()) ? 'poautomatedtransactionfileupload' : 'apautomatedtransactionfileupload';
        }

        $sailManager = Globals::$g->gManagerFactory->getManager($sailUploadEntity);

        return $sailManager;
    }

}