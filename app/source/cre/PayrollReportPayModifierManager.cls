<?php
/**
 * Manager class for the payroll report tax setup object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Sage Intacct Inc.
 *
 * This document contains trade secret data that belongs to Sage Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Sage Intacct Corporation.
 */

class PayrollReportPayModifierManager extends PayrollReportManager
{

    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
        self::init();
    }

    /**
     * add a record to the database
     *
     * @param array &$values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $source = "PayrollReportPayModifierManager::regularAdd";
        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->translateValues($values);
        $ok = $ok && $this->validateEntityLocationOnCheck($values['CHECKKEY'], false, $values['TIMECARDKEY']);
        $ok = $ok && parent::regularAdd($values);

        if ( ! $ok ) {
            Globals::$g->gErr->addIAError(number: 'CRE-3115', source: __FILE__ . ':' . __LINE__);
            $this->_QM->rollbackTrx($source);
        } else {
            $ok = $ok && $this->_QM->commitTrx($source);
        }

        return $ok;
    }

    /**
     * update the record in the database
     *
     * @param array &$values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $source = "PayrollReportPayModifierManager::regularSet";
        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->translateValues($values);
        $ok = $ok && $this->validateEntityLocationOnCheck($values['CHECKKEY'], false, $values['TIMECARDKEY']);
        $ok = $ok && parent::regularSet($values);

        if ( ! $ok ) {
            Globals::$g->gErr->addIAError(number: 'CRE-3116', source: __FILE__ . ':' . __LINE__);
            $this->_QM->rollbackTrx($source);
        } else {
            $ok = $ok && $this->_QM->commitTrx($source);
        }

        return $ok;
    }


    /**
     * Hook function for the sub classes to perform specific tasks right before the record is deleted
     *
     * @param array $values the transaction data
     *
     * @return bool true on success and false on failure
     */
    protected function beforeDelete(&$values)
    {
        $ok = true;
        $values = $values[0];
        $recordNo = $values['RECORD#'];
        $recordExists = !empty($recordNo) && $this->checkRecordExists($recordNo);
        if(!$recordExists){
            Globals::$g->gErr->addIAError(number: 'CRE-3108', source: __FILE__ . ':' . __LINE__);
            return false;
        }
        $ok = $ok && $this->validateEntityLocationOnCheck($values['CHECKKEY']);
        $ok = $ok && parent::beforeDelete($values);
        return $ok;
    }
}