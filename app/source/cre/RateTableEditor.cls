<?php
/**
 * Editor for the RateTable object
 *
 * <AUTHOR> / <PERSON>
 * @copyright    2000-2021 Sage Intacct Inc.
 *
 * This document contains trade secret data that belongs to Sage Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class RateTableEditor extends FormEditor
{
    /**
     * @param array $params
     */
    protected function buildDynamicMetadata(&$params)
    {
        // Hide any fields that are turned off
        $mgr = Globals::$g->gManagerFactory->getManager('timesheet');
        if ( ! $mgr->isShowEmployeePosition() ) {
            self::findAndSetMetadata($params, [ 'path' => 'EMPPOSITIONID' ], [ 'hidden' => true ]);
        }

        if ( ! $mgr->isShowLaborClass() ) {
            self::findAndSetMetadata($params, [ 'path' => 'LABORCLASSID' ], [ 'hidden' => true ]);
        }

        if ( ! $mgr->isShowLaborShift() ) {
            self::findAndSetMetadata($params, [ 'path' => 'LABORSHIFTID' ], [ 'hidden' => true ]);
        }

        if ( ! $mgr->isShowLaborUnion() ) {
            self::findAndSetMetadata($params, [ 'path' => 'LABORUNIONID' ], [ 'hidden' => true ]);
        }
    }

    /**
     * This is a hook functions for subclases to adjust the metadata according to the current data & state.
     * At the time this function is called:
     *   - the data is available and it is in view format.
     *   - the metadata is expanded and the view objects are built - use $this->getView() call to get a refernece to
     *   the view object.
     *
     * WARNING: Because the metadata is expanded at the time of this call, the subclass has to be careful when making
     * changes to the metadata. For example, when adding/removing fields that belong to a grid, the code needs to
     * operate on the grid object.
     *
     * @param  array $obj  the data
     *
     * @return bool  true on success and false on failure - make sure an error is raised in case of failure
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        /**
         * Filter non-PO entry items to active non-inventory, excluding non-inventory Purchase only
         * We need to do this here because items is a standard dimension, so entity restrictions are ignored
         */
        $this->getView()->findComponents([ 'path' => 'ITEMID' ], EditorComponentFactory::TYPE_FIELD, $matches);
        if ( $matches ) {
            foreach ( $matches as $item ) {
                // PO entries are handled differently in the entity file
                if ( empty($item->params['gridPath']) ) {
                    continue;
                }
                $type = $item->getProperty('type');
                $type['restrict'] = [
                    [
                        'pickField' => 'ITEMTYPE',
                        'operand'   => 'NOT IN',
                        'value'     => [ 'I', 'NP' ],
                    ],
                    [
                        'pickField' => 'STATUS',
                        'operand'   => '=',
                        'value'     => 'active',
                    ],
                ];
                $item->setProperty('type', $type);
            }
        }

        return true;
    }

    /**
     * This function can be overriden if no defaults header fields are wanted
     *
     * @return bool
     */
    protected function isShowGridDefaults()
    {
        return false;
    }

    /**
     * Return false to only ignore rows with no values
     *
     * @param EditorGrid $grid
     *
     * @return bool
     */
    protected function ignoreLineWithoutPrimaryField($grid)
    {
        return false;
    }

    /**
     * @param string $ownerobject
     * @param string $type
     *
     * @return array
     */
    protected function GetCustomComponents($ownerobject, $type = "all")
    {
        $ret = parent::GetCustomComponents($ownerobject, $type);

        // For po entries, use the manager to get the custom fields
        if ( $ownerobject == 'podocumententry' && ( $type == 'all' || $type == 'customfield' ) ) {
            $objDef = Pt_DataObjectDefManager::getByName('ratetablepoentry');
            $mgr = $objDef->getEntityManager();
            $customFields = $mgr->customFields;
            if ( ! empty($customFields) ) {
                $retFields = [];

                // Convert custom field objects to editor compatible array
                foreach ( $customFields as $field ) {
                    $retFields[] = [
                        'COMPONENTTYPE' => 'C',
                        'PATH'          => $field->path,
                        'LABEL'         => $field->label,
                        'HIDDEN'        => $field->hidden,
                        'OBJECTFIELDID' => $field->objectFieldID,
                        'REQUIRED'      => $field->required,
                        'TYPE'          => $field->type,
                    ];
                }

                $ret = INTACCTarray_merge($retFields, $ret);
            }
        }

        /**
         * Reset hidden and required flags for rate table entries.
         * Custom fields can be changed to hidden but they will continue being used in matching criteria,
         * so we want to show them in the rate table entries to reduce confusion.
         * Even if a custom field is required for the object, we do not want to require it on the rate table entries.
         */
        foreach ( $ret as $key => &$field ) {
            if ( in_array($field['TYPE'], RateTableEntryManager::EXCLUDE_CF_TYPES ) ) {
                unset($ret[$key]);
            } else {
                $field['HIDDEN'] = false;
                $field['REQUIRED'] = false;
            }
        }

        return $ret;
    }
}
