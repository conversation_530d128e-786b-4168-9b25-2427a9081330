<?php
/**
 * File obspctcompletedcosttype.ent contains entity definition for obspctcompletedcosttype
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2018 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

global $gRecordNoFieldInfo, $gWhenCreatedFieldInfo, $gWhenModifiedFieldInfo, $gCreatedByFieldInfo, $gModifiedByFieldInfo, $gIntegerType, $gDateType, $gPercentFormat;
$kSchemas['obspctcompletedcosttype'] = array(
    'object' => array(
        'RECORDNO',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',

        'COSTTYPEKEY',
	    'COSTTYPEID',
	    'COSTTYPENAME',
        'ASOFDATE',
        'PERCENT',
        'NOTE',
    ),
    'schema' => array (
        'RECORDNO' => 'record#',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',

        'TYPE' => 'type',
        'COSTTYPEKEY' => 'costtypekey',
	    'COSTTYPEID' => 'costtype.costtypeid',
	    'COSTTYPENAME' => 'costtype.name',
        'ASOFDATE' => 'asofdate',
        'PERCENT' => 'percent',
        'NOTE' => 'note',
    ),

//    'children' => [
//        'contractdetail' => [
//            'fkey' => 'contractdetailkey', 'invfkey' => 'record#', 'table' => 'contractdetail', 'join' => 'inner',
//            'children' => [
//                'contract' => [
//                    'fkey' => 'contractkey', 'invfkey' => 'record#', 'table' => 'contract', 'join' => 'inner',
//                ],
//            ],
//        ],
//    ],
//    'nexus' => [
//        'contractdetail' => [
//            'object' => 'contractdetail', 'relation' => MANY2ONE, 'field' => 'CONTRACTDETAILKEY', 'printas' => 'IA.CONTRACT_DETAIL'
//        ],
//    ],

    'children' => [
        'costtype' => [
            'fkey' => 'costtypekey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'costtype',
        ],
    ],

    'nexus' => [
        'costtype' => [
                'object' => 'costtype',
                'relation' => MANY2ONE,
                'field' => 'costtypekey',
                'printas' => 'IA.COST_TYPE'
        ],
    ],

    'fieldinfo' => [
        $gRecordNoFieldInfo,
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        [
            'id' => 1,
            'path' => 'TYPE',
            'fullname' => 'IA.TYPE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.PROJECT', 'IA.TASK', 'IA.COSTTYPE'),
                'validvalues' => array('Project', 'Task', 'CostType'),
                '_validivalues' => array('P', 'T', 'C'),
            ),
            'readonly' => true,
            'hidden' => true,
        ],
        [
            'id' => 2,
            'path' => 'COSTTYPEKEY',
            'fullname' => 'IA.COST_TYPE_KEY',
            'type' => $gIntegerType,
            'required' => true
        ],
        [
            'id' => 3,
            'path' => 'ASOFDATE',
            'fullname' => 'IA.AS_OF_DATE',
            'type' => $gDateType,
            'required' => true
        ],
        [
            'id' => 4,
            'path' => 'PERCENT',
            'fullname' => 'IA.PERCENT',
            'type' => [
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 5,
                'size' => 5,
                'format' => $gPercentFormat,
            ],
            'required' => true
        ],
        [
            'id' => 5,
            'path' => 'NOTE',
            'fullname' => 'IA.NOTE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
        [
            'path' => 'COSTTYPEID',
            'fullname' => 'IA.COST_TYPE_ID',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'id' => 6
        ],
        [
            'path' => 'COSTTYPENAME',
            'fullname' => 'IA.COST_TYPE_NAME',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
            ],
            'id' => 7
        ],
    ],

    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED'
    ),


    'dbfilters' => array(
        array(
            'obspctcompletedcosttype.type', '=', 'C'
        )
    ),
    'dbsorts' => [
        ['ASOFDATE']
    ],

    'module' => 'pa',
    'table' => 'obspctcomplete',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'parententity' => 'costtype',
    'auditcolumns' => true,
    'nosysview' => true,
    
    'api' => array(
//        'PERMISSION_MODULES' => array('cn'),
        'PERMISSION_READ' => 'ALL',
        'PERMISSION_CREATE' => 'ALL',
        'PERMISSION_UPDATE' => 'ALL',
        'PERMISSION_DELETE' => 'ALL'
    ),
    'printas' => 'IA.COST_TYPE_OBSERVED_PERCENT_COMPLETED',
    'pluralprintas' => 'IA.COST_TYPE_OBSERVED_PERCENT_COMPLETED',
);
