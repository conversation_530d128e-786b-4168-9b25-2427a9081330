<?php

/**
 * podocumententry Pick picker class
 *
 * <AUTHOR>
 * @copyright 2024 Intacct Corporation, All Rights Reserved
 */
class PODocumentEntrypickPicker extends NPicker
{

    public function __construct()
    {
        $nparams = array(
            'entity' => 'podocumententrypick',
            'pickfield' => 'PICKID',
            'fields' => array('PICKID', 'LINE_NO'),
            'sortcolumn'    => 'LINE_NO:a,PICKID:a',
        );
        parent::__construct($nparams);
    }

    /**
     * @return array
     */
    protected function calcSelects()
    {
        $selects = parent::calcSelects();

        $additionFieldsForCalculation = [ 'LINE_NO', 'HASCHANGE', 'TRX_REVISEDPRICE', 'PRICE_CONVERTED', 'TRX_PRICE', 'REVISEDQTY', 'QTY_CONVERTED', 'QUANTITY', 'CONVERSIONTYPE'];
        $selects = INTACCTarray_merge($selects, $additionFieldsForCalculation);
        return $selects;
    }

}