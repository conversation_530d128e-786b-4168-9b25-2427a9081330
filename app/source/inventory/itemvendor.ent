<?php

/**
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 *
 * <AUTHOR> <<EMAIL>>
 * @desc      itemvendor.ent
 * @copyright 2000-2009 Intacct Corporation
 */
$kSchemas['itemvendor'] = array(

    'children' => array(
        'item' => array(
            'fkey' => 'itemkey',
            'invfkey' => 'itemid',
            'table' => 'icitem',
            'join' => 'outer'
        ),
        'vendor' => array(
            'fkey' => 'vendorid', 'invfkey' => 'vendorid',
            'table' => 'vendor', 'join' => 'outer',
        ),
        'uom' => array(
            'fkey' => 'uom', 'invfkey' => 'unit',
            'table' => 'icuom', 'join' => 'outer',
            'filter' => " uom.grpkey(+) = item.uomgrpkey ",
        )
    ),


    'schema' => array(
        'RECORDNO' => 'record#',
        'ITEMID' => 'itemkey',
        'VENDORID' => 'vendorid',
        'STOCKNO' => 'stock_number',
        'LEAD_TIME' => 'lead_time',
        'FORECAST_DEMAND_IN_LEAD_TIME' => 'forecast_demand_in_lead_time',
        'ECONOMIC_ORDER_QTY' => 'economic_order_qty',
        'MIN_ORDER_QTY' => 'min_order_qty',
        'BEST_COST' => 'best_cost',
        'LAST_COST' => 'last_cost',
        'UOM' => 'uom',
        'CONVFACTOR' => 'convfactor',
        'PREFERRED_VENDOR' => 'preferred_vendor',
        //Adding ITEMKEY,VENDORKEY,VENDORNAME and UOMKEY to support NextGen key,id,href format
        'ITEMKEY' => 'item.record#',
        'VENDORKEY' => 'vendor.record#',
        'VENDORNAME' => 'vendor.name',
        'UOMKEY' => 'uom.record#',
    ),
    'object' => array(
        'RECORDNO', 'ITEMID','VENDORID','VENDORNAME', 'STOCKNO', 'LEAD_TIME', 'FORECAST_DEMAND_IN_LEAD_TIME', 'ECONOMIC_ORDER_QTY', 'MIN_ORDER_QTY', 'BEST_COST', 'LAST_COST', 'UOM', 'CONVFACTOR', 'PREFERRED_VENDOR','ITEMKEY','VENDORKEY','UOMKEY'
    ),



    'fieldinfo' => array(
        array(
            'fullname' => 'IA.RECORD_NO',
            'type' => array(
                'ptype' => 'sequence',
                'type' => 'integer',
                'maxlength' => 8,
            ),
            'hidden' => true,
            'readonly' => true,
            'required' => true,
            'desc' => 'IA.RECORD_NO',
            'path' => 'RECORDNO',
            'id' => 1
        ),
        array(
            'fullname' => 'IA.ITEM_ID',
            'required' => true,
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'item',
                'maxlength' => 30,
            ),
            'desc' => 'IA.ITEM_ID',
            'path' => 'ITEMID',
            'renameable' => true,
            'id' => 2
        ),
        array(
            'fullname' => 'IA.VENDOR_ID',
            'required' => true,
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'vendor',
                'pickentity' => 'vendorpick',
                'maxlength' => 20,
                'size' => 30
            ),
            'desc' => 'IA.VENDOR_ID',
            'path' => 'VENDORID',
            'renameable' => true,
            'id' => 3
        ),
        array(
            'fullname' => 'IA.STOCK_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
            //'format' => '/^[\w\s_\- ]{0,50}$/',
            ),
            'desc' => 'IA.VENDOR_STOCK_NO',
            'path' => 'STOCKNO',
            'renameable' => true,
            'id' => 4
        ),
        array(
            'fullname' => 'IA.LEAD_TIME_DAYS',
            'desc' => 'IA.LEAD_TIME_DAYS',
            'type' => array(
                'ptype' => 'text',
                'type' => 'integer',
                'maxlength' => 3,
                'format' => '/[0-9]{0,3}/',
            ),
            'path' => 'LEAD_TIME',
            'renameable' => true,
            'id' => 5
        ),
        array(
            'fullname' => 'IA.ECONOMIC_ORDER_QUANTITY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 8,
                'size' => 8,
            ),
            'desc' => 'IA.ECONOMIC_ORDER_QUANTITY',
            'path' => 'ECONOMIC_ORDER_QTY',
            'id' => 6
        ),
        array(
            'fullname' => 'IA.MINIMUM_ORDER_QUANTITY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 8,
                'size' => 8,
            ),
            'desc' => 'IA.MINIMUM_ORDER_QUANTITY',
            'path' => 'MIN_ORDER_QTY',
            'id' => 7
        ),
        array(
            'fullname' => 'IA.BEST_COST',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 8,
                'format' => $gDecimalFormat
            ),
            'readonly' => true,
            'desc' => 'IA.BEST_COST',
            'path' => 'BEST_COST',
            'id' => 8
        ),
        array(
            'fullname' => 'IA.LAST_COST',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 8,
                'format' => $gDecimalFormat
            ),
            'readonly' => true,
            'desc' => 'IA.LAST_COST',
            'path' => 'LAST_COST',
            'id' => 9
        ),
        array (
            'path' => 'UOM',
            'desc' => 'IA.UNITS_OF_MEASURE',
            'fullname' => 'IA.UNITS_OF_MEASURE',
            'required' => false,
            'type' => array(
                'ptype' => 'webcombo',
                'type' => 'text',
                'size' => 10,
            ),
            'id' => 10
        ),
        array(
            'fullname' => 'IA.NO_OF_BASE_UNIT',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'size' => 10,
                'maxlength' => 30,
                'format' => $gDecimalFormat
            ),
            'iscustom' => true,
            'precision' => 10,
            'desc' => 'IA.UNIT_FACTOR',
            'path' => 'CONVFACTOR',
            'noautodecimal' => true,
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 11
        ),
        array(
            'fullname' => 'IA.DEMAND_FORECAST_DURING_LEAD_TIME',
            'path' => 'FORECAST_DEMAND_IN_LEAD_TIME',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'id' => 12
        ),
        array(
            'fullname' => 'IA.PREFERRED_VENDOR',
            'path'     => 'PREFERRED_VENDOR',
            'type'     => $gBooleanType,
            'default'  => 'false',
            'id' => 13,
        ),
        array(
            'fullname' => 'IA.ITEM_KEY',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'maxlength' => 30,
            ),
            'desc' => 'IA.ITEM_KEY',
            'path' => 'ITEMKEY',
            'id' => 2
        ),
        array(
            'fullname' => 'IA.VENDOR_KEY',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'maxlength' => 30,
            ),
            'desc' => 'IA.VENDOR_KEY',
            'path' => 'VENDORKEY',
            'id' => 2
        ),
        array(
            'fullname' => 'IA.VENDOR_NAME',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'maxlength' => 30,
            ),
            'desc' => 'IA.VENDOR_NAME',
            'path' => 'VENDORNAME',
            'id' => 2
        ),
        array(
            'fullname' => 'IA.UOM_KEY',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'maxlength' => 30,
            ),
            'desc' => 'IA.UOM_KEY',
            'path' => 'UOMKEY',
            'id' => 2
        ),

    ),

    'parententity' => 'item',
    'table' => 'icitemvendor',
    'printas' => 'IA.ITEM_VENDOR_INFO',
    'pluralprintas' => 'IA.ITEMS_VENDORS_INFO',
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'module' => 'inv',
    'renameable' => true,
    'platformProperties' => array(
        SOBJECT_CAN_HAVE_RELATIONSHIPS => false,
    ),
    'api' => array(
        'PERMISSION_MODULES' => array('inv', 'po'),
        'PERMISSION_READ' => 'NONE',
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
        'LESS_GET_FIELDS' => ['CONVFACTOR','VENDORNAME'],
        'LESS_GET_FIELDS_NO_REPLENISHMENT' => ['FORECAST_DEMAND_IN_LEAD_TIME', 'UOM', 'MIN_ORDER_QTY', 'PREFERRED_VENDOR'],
    ),
);

