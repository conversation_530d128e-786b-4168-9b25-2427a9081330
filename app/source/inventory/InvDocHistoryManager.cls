<?


/**
 *  File InvDocHistoryManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025, Intacct Corporation, All Rights Reserved
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class InvDocHistoryManager extends DocHistoryManager
{

    /**
     * @param array $params
     */
    public function __construct($params=array())
    {
        $params['_mod'] = 'inv';
        parent::__construct($params);

        $kdochistoryQueries = [];
        include 'dochistory.cqry';
        $this->_QM->LoadQueries($kdochistoryQueries);
    }
}

