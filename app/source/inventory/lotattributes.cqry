<?

$klotattributesQueries = array(

    'QRY_LOTATTRIBUTES_INSERT' => array(
        'QUERY' => "INSERT INTO lotattributes (record#,lotkey,lineno,attributeid,type,length,cny#) VALUES (?,?,?,?,?,?,?) ",
        'ARGTYPES' => array('integer','text','integer','text','text','integer','integer'),
    ),
    'QRY_LOTATTRIBUTES_UPDATE' => array(
        'QUERY' => "UPDATE lotattributes SET lotkey=?, lineno=?, attributeid=?, type=?, length=? WHERE record# =?  AND cny# =?  ",
        'ARGTYPES' => array('text','integer','text','text','integer','integer','integer' ),
    ),
    'QRY_LOTATTRIBUTES_DELETE_VID' => array(
        'QUERY' => "DELETE FROM lotattributes WHERE record# =?  AND cny# =? ",
        'ARGTYPES' => array('integer' ,'integer' ),
    ),
    'QRY_LOTATTRIBUTES_SELECT_SINGLE_VID' => array(
        'QUERY' => "SELECT lotattributes.record#,lotattributes.lotkey,lotattributes.attributeid,lotattributes.type,lotattributes.length FROM lotattributes lotattributes WHERE (lotattributes.record# =  ? ) and lotattributes.cny# (+) = ? ",
        'ARGTYPES' => array('integer', 'integer'),
    ),
    'QRY_LOTATTRIBUTES_SELECT_RAW_VID' => array(
        'QUERY' => "SELECT * FROM lotattributes WHERE record# =?  AND cny# =?  ",
        'ARGTYPES' => array('integer' ,'integer' ),
    ),
    'QRY_LOTATTRIBUTES_SELECT_BY_PARENT' => array(
        'QUERY' => "SELECT lotattributes.record#,lotattributes.lotkey,lotattributes.attributeid,lotattributes.type,lotattributes.length FROM lotattributes lotattributes WHERE (lotattributes.lotkey =  ? ) and lotattributes.cny# (+) = ? ",
        'ARGTYPES' => array('text', 'integer'),
    ),
    'QRY_LOTATTRIBUTES_DELETE_BY_PARENT' => array(
        'QUERY' => "DELETE FROM lotattributes WHERE lotkey =?  AND cny# =? ",
        'ARGTYPES' => array('text' ,'integer' ),
    ),
);