<?

$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_ALLOWED_RECALL_FROM'] = array (
    'QUERY' => 'SELECT docpar.record#, docpar.userperm, docpar.docid, docpar.docclass, docpar.discount_on_extendedprice FROM docpar, docrecalls, docpar recdpr WHERE docpar.latestversionkey is null and docpar.sale_pur_trans = ? and docpar.RECORD# = docrecalls.RECDOCPARKEY (+) and docrecalls.DOCPARKEY = recdpr.record# and recdpr.docid = ? and recdpr.latestversionkey is null and recdpr.cny# (+) = ? and docpar.cny# = ? and docrecalls.CNY# (+) = ?',
    'ARGTYPES'=> array('text','text','integer','integer','integer'),
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_ALLOWED_ACTIVE_RECALL_FROM'] = array (
    'QUERY' => 'SELECT docpar.record#, docpar.userperm, docpar.docid, docpar.docclass FROM docpar, docrecalls, docpar recdpr WHERE docpar.latestversionkey is null and docpar.sale_pur_trans = ? and docpar.status = \'T\' and docpar.RECORD# = docrecalls.RECDOCPARKEY (+) and docrecalls.DOCPARKEY = recdpr.record# and recdpr.docid = ? and recdpr.latestversionkey is null and recdpr.cny# (+) = ? and docpar.cny# = ? and docrecalls.CNY# (+) = ?',
    'ARGTYPES'=> array('text','text','integer','integer','integer'),
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_ALLOWED_RECALL_TO'] = array (
    'QUERY' => "SELECT docpar.record#, docpar.userperm, docpar.docid, docpar.locationkey,docpar.docclass FROM docpar, docrecalls, docpar recdpr WHERE docpar.latestversionkey is null and docpar.sale_pur_trans = ? and docpar.status = ? and docpar.RECORD# = docrecalls.DOCPARKEY (+) and docrecalls.RECDOCPARKEY = recdpr.record# and recdpr.docid = ? and recdpr.record# = docrecalls.recdocparkey and recdpr.cny# (+) = ? and docpar.cny# = ? and docrecalls.CNY# (+) = ? and nvl(docpar.enable_contract_billing,'F') != 'T' order by docpar.docid",
    'ARGTYPES'=> array('text','text','text','integer','integer','integer'),
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_SELECT_DOCTYPE'] = array (
    'QUERY' => 'SELECT doctype from docpar where record# = ? and cny# = ?',
    'ARGTYPES' => array( 'text', 'integer')
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_SELECT_LATEST_RECORDNO_FROM_ID'] = array(
    'QUERY' => 'SELECT record# from docpar where latestversionkey is null and docid = ? and cny# = ?',
    'ARGTYPES' => array ('text', 'integer')
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_SELECT_LATEST_RAW'] = array(
    'QUERY' => 'SELECT * FROM docpar WHERE latestversionkey is null and docid =?  AND cny# =?',
    'ARGTYPES' => array('text' ,'integer')
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_SELECT_ALL_RAW'] = array(
    'QUERY' => 'SELECT * FROM docpar WHERE docid =?  AND cny# =?',
    'ARGTYPES' => array('text' ,'integer')
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_SELECT_LATEST_FOR_UNIQUENESS_CHECK'] = array(
    'QUERY' => 'SELECT docid, locationkey, record# FROM docparmst WHERE latestversionkey is null and docid =?  AND cny# =?',
    'ARGTYPES' => array('text' ,'integer')
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_UPDATE_LOCATIONKEY_TO_NULL'] = array(
    'QUERY' => 'update docparmst set locationkey = null WHERE record# = ? and cny# = ?',
    'ARGTYPES' => array('integer', 'integer')
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_TRANSACTIONS_EXIST'] = array(
    'QUERY' => 'select sum(cnt) as cnt from (
		SELECT 1 as cnt FROM dochdrmst WHERE docparkey = ?  AND cny# =? and rownum = 1
		union all
		SELECT 1 as cnt FROM recurdochdrmst WHERE docparkey = ?  AND cny# =? and rownum = 1
		union all
		SELECT 1 as cnt FROM renewalsdochdr WHERE docparkey = ?  AND cny# =? and rownum = 1
		union all
		SELECT 1 as cnt FROM renewalmacro WHERE salesdocrecordkey = ?  AND cny# =? and rownum = 1
        union all 
        SELECT 1 as cnt FROM scmmacroresolvemst WHERE cny# =? AND (SOURCEDOCPARKEY = ? OR TARGETDOCPARKEY = ?) and rownum = 1 
		)',
    'ARGTYPES' => array('integer' ,'integer','integer' ,'integer','integer' ,'integer','integer' ,'integer', 'integer', 'integer', 'integer')
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_RECURRING_TRANSACTIONS_EXIST'] = array(
    'QUERY' => 'SELECT count(*) as cnt FROM recurdochdr WHERE docparkey = ?  AND cny# =?',
    'ARGTYPES' => array('integer' ,'integer')
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_UPDATE_VERSION'] = array(
    'QUERY' => 'update docparmst set latestversionkey = ? WHERE record# <> ? AND docid = ? AND cny# =?',
    'ARGTYPES' => array('integer' ,'integer', 'text', 'integer')
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_UPDATE_DOCTYPE'] = array(
    'QUERY' => 'update docparmst set doctype = (select doctype from docpar b where b.cny#=docparmst.cny# and b.record# = ?) WHERE record# = ? and cny# =?',
    'ARGTYPES' => array('integer' ,'integer', 'integer')
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_UPDATE_TO_LATEST_STATUS'] = array(
    'QUERY' => 'update docparmst set status = ?  WHERE docid = ?  and cny# = ?',
    'ARGTYPES' => array('text', 'text', 'integer')
);

$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_UPDATE_TO_LATEST_REPORTINGCATEGORY'] = array(
    'QUERY' => 'update docpar set REPORTINGCATEGORY = ?  WHERE docid = ?  and cny# = ?',
    'ARGTYPES' => array('text', 'text', 'integer')
);

$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_GET_EDITDEL_POLICY'] = array(
	'QUERY' => 'SELECT deltype, edittype, create_type createtype, creation_rule td_creation_rule, updgl from docpar where latestversionkey is null and docid = ? and cny# = ?',
    'ARGTYPES' => array ('text', 'integer')
);
$kdocumentparamsQueries['QRY_DOCRECALLS_UPDATE_TO_LATEST'] = array (
    'QUERY'        => 'update docrecalls set recdocparkey = ? where recdocparkey = ? and cny# = ?',
    'ARGTYPES' => array('integer', 'integer', 'integer' ),
);
$kdocumentparamsQueries['QRY_TAXSCHEDMAP_UPDATE_TO_LATEST'] = array (
    'QUERY'        => 'update taxschedmapmst set docparkey = ? where docparkey in ( select record# from docpar where docid = ? and cny# = ? ) and cny# = ?',
    'ARGTYPES' => array('integer', 'text', 'integer', 'integer' ),
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_GET_DOCID_FROM_RECORDNO'] = array (
    'QUERY'        => 'select docid from docpar where record# = ? and cny# = ?',
    'ARGTYPES' => array('integer', 'integer'),
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_GET_JSPARAMS'] = array (
    'QUERY' => "select docid,docclass,warnonlowqty,creditlimitcheck, allow_allocations from docpar where latestversionkey is null and sale_pur_trans = ? and cny# = ?",
    'ARGTYPES'    => array ('text','integer'),
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_GET_USER_PERMISSION'] = array (
    'QUERY' => "select A_O_D Perm , docpar.userperm FROM PATHPOL P, DIR D, docpar WHERE P.dirkey=D.record# and D.path=? and P.cny#=D.cny# and P.U_O_GKEY = ? and P.cny#=?  and docpar.cny# = ? and D.path = docpar.DOCID and	  docpar.latestversionkey is null  ORDER BY P.type, P.u_o_gkey",
    'ARGTYPES'    => array('text','integer','integer','integer'),
);
$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_ALLOWED_RECALL_TO_CHECK'] = array (
    'QUERY' => 'SELECT docpar.record#, docpar.userperm, docpar.docid, docpar.locationkey FROM docpar, docrecalls, docpar recdpr WHERE docpar.latestversionkey is null and docpar.sale_pur_trans = ? and docpar.status = \'T\' and docpar.RECORD# = docrecalls.DOCPARKEY (+) and docrecalls.RECDOCPARKEY = recdpr.record# and recdpr.docid = ? and recdpr.record# = docrecalls.recdocparkey and recdpr.cny# (+) = ? and docpar.cny# = ? and docrecalls.CNY# (+) = ? and docpar.record# = ?',
    'ARGTYPES'=> array('text','text','integer','integer','integer','integer'),
);

$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_FOR_APPROVALS'] = array (
    'QUERY'        => 'select docid, status, record# from docpar where cny# = ? and latestversionkey is null and docclass in (\'QUOTE\', \'ORDER\') and status = \'T\' and sale_pur_trans = ? ',
    'ARGTYPES' => array('integer', 'text'),
);

$kdocumentparamsQueries['QRY_PODOCUMENTPARAMS_FOR_GENINVOICES'] = array (
    'QUERY'        => 'select docid from docpar where cny# = ? and latestversionkey is null and docclass in (\'INVOICE\', \'ADJ\') and status = \'T\' and sale_pur_trans = \'P\' ',
    'ARGTYPES' => array('integer'),
);

$kdocumentparamsQueries['QRY_PODOCUMENTPARAMS_FOR_APPROVALS'] = array (
    'QUERY'        => 'select docid, status, record# from docpar where cny# = ? and latestversionkey is null and docclass in (\'QUOTE\', \'ORDER\', \'INVOICE\') and status = \'T\' and sale_pur_trans = \'P\' ',
    'ARGTYPES' => array('integer'),
);

$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_SELECT_TYPE_RAW'] = array(
    'QUERY' => 'SELECT * FROM docpar WHERE sale_pur_trans =?  AND cny# =?',
    'ARGTYPES' => array('text' ,'integer')
);

$kdocumentparamsQueries['QRY_SCMMACROHDR_UPDATE_TO_LATEST'] = array (
    'QUERY'        => 'update scmmacrohdrmst set sourcedocparkey = ? where sourcedocparkey in ( select record# from docpar where docid = ? and cny# = ? ) and cny# = ?',
    'ARGTYPES' => array('integer', 'text', 'integer', 'integer' ),
);

$kdocumentparamsQueries['QRY_SCMMACROENTRY_UPDATE_TO_LATEST'] = array (
    'QUERY'        => 'update scmmacroentrymst set targetdocparkey = ? where targetdocparkey in ( select record# from docpar where docid = ? and cny# = ? ) and cny# = ?',
    'ARGTYPES' => array('integer', 'text', 'integer', 'integer' ),
);

$kdocumentparamsQueries['QRY_DOCUMENTPARAMS_VERSION_RECURRING_TRANSACTIONS_EXIST'] = array (
    'QUERY'        => 'select count(*) as cnt from recurdochdr rdh join (select cny#, record#, latestversionkey from docpar start with record# = ? and cny# = ? CONNECT BY  PRIOR record# =  latestversionkey and cny# = ?) dp
                    on dp.cny# = rdh.cny# and dp.record# = rdh.docparkey where rdh.cny# = ?',
    'ARGTYPES' => array('integer', 'integer', 'integer', 'integer' ),
);

$kdocumentparamsQueries['QRY_APPROVALPOLICY_DOCPARKEY_UPDATE_TO_LATEST'] = array (
    'QUERY'        => 'update approvalpolicy set docparkey = ? where docparkey in ( select record# from docpar where docid = ? and cny# = ? ) and cny# = ?',
    'ARGTYPES' => array('integer', 'text', 'integer', 'integer' ),
);
