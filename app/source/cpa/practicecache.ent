<?php
/** 
 * Entity definition for practice cache
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2001 Intacct Corporation, All Rights Reserved
 */

$kSchemas['practicecache'] = array (
    'object' => array (
        'CNY#',
        'TTL',
    ),
    'schema' => array (
        'CNY#' => 'cny#',
        'TTL' => 'ttl',
    ),
    'fieldinfo' => array (
        array (
            'path' => 'CNY#',
            'desc' => 'IA.COMPANY_KEY',
            'fullname' => 'IA.COMPANY_KEY',
            'hidden' => true,
            'type' => array (
                'type' => 'integer',
                'ptype' => 'integer',
            ),
            'id' => 1,
        ),
        array (
            'fullname' => 'IA.TIME_TO_LIVE',
            'desc' => 'IA.TIME_TO_LIVE',
            'type' => array (
                'ptype' => 'timestamp', 
                'type' => 'timestamp', 
            ),
            'path' => 'TTL',
            'id' => 2,
        ),
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'TTL'
    ),
    'table' => 'practicecache',
    'printas' => 'IA.PRACTICE_CACHE',
    'pluralprintas' => 'IA.PRACTICE_CACHE',
    'vid' => 'CNY#',
    'module' => 'mp',
    'renameable' => true,
    'nosysview' => true,
    
    'global' => true,
);

 
