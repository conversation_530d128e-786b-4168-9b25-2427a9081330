<?php
//=============================================================================
//
//	FILE:			cch_export.inc
//	AUTHOR:			<PERSON>
//	DESCRIPTION:	Contains API for CCH Export downloads
//					
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once "csvdownload.inc";

/**
 * @param array $_data
 * @param array $_fields
 *
 * @return bool|string
 */
function ArrayToCCHExportString($_data,$_fields)
{
    if (!$_data || !is_array($_data) || !is_array($_data[0])) {
        return false;
    }
    $lines = [];
    foreach ($_data as $row) {
        $rec = '';
        foreach ($_fields as $fld) {
            $rec .= ExportCCH($row[$fld['name']], $fld['length'], $fld['align']);
            if ($fld['filler']) {
                $rec .= ' ';
            }
        }
        $lines[] = $rec;
    }
    $crlf = "\r\n";
    return \implode($crlf, $lines) . $crlf;
}

/**
 * @param array  $_data
 * @param string $_fname
 * @param array  $_fields
 *
 * @return bool
 */
function PrintCCHExport($_data, $_fname, $_fields)
{
    $stream = ArrayToCCHExportString($_data, $_fields);
    if (!$stream) {
        return false;
    }
    PrintHTTPCSVHeaders($_fname, 1);
    echo $stream;
    return true;
}


/**
 * @param mixed  $val
 * @param int    $length
 * @param string $align
 *
 * @return bool|string
 */
function ExportCCH($val, $length, $align)
{
    $val = strval($val);
    $spacestr = str_repeat(' ', $length);
    if ($align == 'left') {
        $str = isl_substr($val.$spacestr, 0, $length);
    } else {
        // numeric
        if ($val == '') {
            $val = '0.00';
        }
        $str = isl_substr($spacestr . number_format($val, 2, '.', ''), (-1 * $length));
    }
    return $str;
}

/**
 * @param array $lines
 */
function PreProcessCCHExportData(&$lines)
{
    $acctMap = [];
    $acctQry = "SELECT  
					acct_no, 
					taxcode
				FROM	
					glaccount
				WHERE 
					cny# = :1 
				ORDER BY acct_no";
    $acctRes = QueryResult([$acctQry, GetMyCompany()]);
    foreach ($acctRes as $acctRec) {
        $acctMap['AC-' . $acctRec['ACCT_NO']] = [
            'TAXCODE' => $acctRec['TAXCODE'],
        ];
    }
    if (!isset($acctMap) || $acctMap == '') {
        return;
    }
    foreach ($lines as $key => $data) {
        $acctno = $data['ACCTNO'];
        $accttaxcode = $acctMap['AC-'.$acctno]['TAXCODE'];
        // populate TAXCODE 
        $lines[$key]['TAXCODE'] = (isset($accttaxcode) && $accttaxcode != '')? $accttaxcode: '';
    }
}

