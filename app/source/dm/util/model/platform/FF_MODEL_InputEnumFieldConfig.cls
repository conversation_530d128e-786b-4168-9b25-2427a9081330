<?php



/**
 * FF_MODEL_InputEnumFieldConfig
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Copyright (C)2023 Sage Intacct Corporation, All Rights Reserved
 */
class FF_MODEL_InputEnumFieldConfig extends FromToBaseModel
{

    /**
     * @var FF_MODEL_InputEnumFieldConfigOption[]
     */
    protected array $options;

    /**
     *
     */
    public function __construct()
    {
        $this->customSetters = [
            'options' => '_setOptions',
        ];

        $this->customGetters = [
            'options' => '_getOptions',
        ];
    }

    /**
     * @return array
     */
    public function getOptions() : array
    {
        return $this->options;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function _getOptions() : array
    {
        $options = [];
        foreach ( $this->options as $option ) {
            $options[] = $option->toArray();
        }

        return $options;
    }

    /**
     * @param array $options
     *
     * @return FF_MODEL_InputEnumFieldConfig
     */
    public function setOptions(array $options) : FF_MODEL_InputEnumFieldConfig
    {
        $this->options = $options;

        return $this;
    }

    /**
     * @param array $options
     *
     * @return $this
     * @throws Exception
     */
    public function _setOptions(array $options) : FF_MODEL_InputEnumFieldConfig
    {
        $this->options = [];
        foreach ( $options as $option ) {
            $this->options[] = FF_MODEL_InputEnumFieldConfigOption::fromArray($option);
        }

        return $this;
    }
}