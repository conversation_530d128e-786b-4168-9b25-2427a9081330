<?php
/**
 * Copyright (c) 2023 Sage Intacct
 */

class ExprParserToken
{
    const TOKEN_ERROR           = 0;
    const TOKEN_STRING          = 1;
    const TOKEN_NUMBER          = 2;
    const TOKEN_IDENTIFIER      = 3;
    const TOKEN_FUNCTION        = 4;
    const TOKEN_OPEN_PAREN      = 5;
    const TOKEN_CLOSE_PAREN     = 6;
    const TOKEN_OPEN_BRACKET    = 7;
    const TOKEN_CLOSE_BRACKET   = 8;
    const TOKEN_OPEN_FUNC_ARGS  = 9;
    const TOKEN_CLOSE_FUNC_ARGS = 10;
    const TOKEN_COMMA           = 11;
    const TOKEN_ARRAY           = 12;  // Contains an array of simple fixed values - strings, numbers
    const TOKEN_ARRAY_EXPR      = 13;  // Contains at least some expressions, identifiers, etc.

    // Constant tokens
    const TOKEN_TRUE            = 50;
    const TOKEN_FALSE           = 51;
    const TOKEN_NULL            = 52;

    const TOKEN_BIN_OP_ALL      = 100;  // Pseudo-op for all operators.
    const TOKEN_OP_EQUAL        = 101;
    const TOKEN_OP_NOT_EQUAL    = 102;
    const TOKEN_UNA_OP_ALL      = 150;
    const TOKEN_OP_UNARY_NOT    = 151;

    const TOKEN_BINARY_FIRST_OP = 100; // Define the first binary operator for bounds checking.
    const TOKEN_BINARY_LAST_OP  = 149; // Define the last binary operator for bounds checking.

    const TOKEN_UNARY_FIRST_OP  = 150; // Define the first unary operator for bounds checking.
    const TOKEN_UNARY_LAST_OP   = 199; // Define the last unary operator for bounds checking.

    const TOKEN_FIRST_OPERATOR  = 100; // Define the first operator for bounds checking.
    const TOKEN_LAST_OPERATOR   = 199; // Define the last operator for bounds checking.

    const TOKEN_ROP_ALL         = 200;  // Pseudo-op for all relational operators.
    const TOKEN_ROP_AND         = 201;
    const TOKEN_ROP_OR          = 202;

    const TOKEN_FIRST_RELOP     = 200; // Define the first relational operator for bounds checking.
    const TOKEN_LAST_RELOP      = 399; // Define the last relational operator for bounds checking.

    const TOKEN_EOF             = 300; // End of parsing.

    //  Used for debugging only.
    private static array $tokenToStr = [
     self::TOKEN_ERROR           => "Error",
     self::TOKEN_STRING          => "String",
     self::TOKEN_NUMBER          => "Number",
     self::TOKEN_IDENTIFIER      => "Identifier",
     self::TOKEN_FUNCTION        => "Function",
     self::TOKEN_OPEN_PAREN      => "Open Paren",
     self::TOKEN_CLOSE_PAREN     => "Close Paren",
     self::TOKEN_OPEN_BRACKET    => "Open Bracket",
     self::TOKEN_CLOSE_BRACKET   => "Close Bracket",
     self::TOKEN_OPEN_FUNC_ARGS  => "Open Func Args",
     self::TOKEN_CLOSE_FUNC_ARGS => "Close Func Args",
     self::TOKEN_COMMA           => "Comma",
     self::TOKEN_ARRAY           => "Array",
     self::TOKEN_ARRAY_EXPR      => "Array with Expressions",
     self::TOKEN_TRUE            => "True",
     self::TOKEN_FALSE           => "False",
     self::TOKEN_NULL            => "Null",
     self::TOKEN_OP_EQUAL        => "Equals (=)",
     self::TOKEN_OP_NOT_EQUAL    => "Not Equals (!=)",
     self::TOKEN_OP_UNARY_NOT    => "Unary Not (!)",
     self::TOKEN_ROP_AND         => "AND",
     self::TOKEN_ROP_OR          => "OR",
     self::TOKEN_EOF             => "End of expression",
    ];

    private static ?ExprParserToken $trueToken = null;

    private static ?ExprParserToken $falseToken = null;

    private static ?ExprParserToken $nullToken = null;

    private string $tokenStr;

    private int $tokenType;

    private mixed $extra;

    public function __construct(string $tokenStr, int $tokenType, mixed $extra='')
    {
        $this->tokenStr = $tokenStr;
        $this->tokenType = $tokenType;
        $this->extra = $extra;
    }

    public static function getTrueToken() : ExprParserToken
    {
        return self::$trueToken;
    }

    public static function getFalseToken() : ExprParserToken
    {
        return self::$falseToken;
    }

    public static function getBoolToken(bool $isTrue) : ExprParserToken
    {
        return $isTrue ? self::$trueToken : self::$falseToken;
    }

    public static function init()
    {
        if (self::$trueToken === null) {
            self::$trueToken = new ExprParserToken("True", self::TOKEN_TRUE);
            self::$falseToken = new ExprParserToken("False", self::TOKEN_FALSE);
            self::$nullToken = new ExprParserToken("", self::TOKEN_NULL);
        }
    }

    public function getStr() : string
    {
        return $this->tokenStr;
    }

    public function getType() : int
    {
        return $this->tokenType;
    }

    public function isEOF() : bool
    {
        return $this->tokenType === self::TOKEN_EOF;
    }

    public function isOperator() : bool
    {
        return ($this->tokenType >= self::TOKEN_FIRST_OPERATOR &&
         $this->tokenType <= self::TOKEN_LAST_OPERATOR);
    }

    public function isBinaryOperator() : bool
    {
        return ($this->tokenType >= self::TOKEN_BINARY_FIRST_OP &&
         $this->tokenType <= self::TOKEN_BINARY_LAST_OP);
    }

    public function isUnaryOperator() : bool
    {
        return ($this->tokenType >= self::TOKEN_UNARY_FIRST_OP &&
         $this->tokenType <= self::TOKEN_UNARY_LAST_OP);
    }

    public function isRelOperator() : bool
    {
        return ($this->tokenType >= self::TOKEN_FIRST_RELOP &&
         $this->tokenType <= self::TOKEN_LAST_RELOP);
    }

    public function isFunction() : bool
    {
        return $this->tokenType == self::TOKEN_FUNCTION;
    }

    public function isIdentifier() : bool
    {
        return $this->tokenType == self::TOKEN_IDENTIFIER;
    }

    public function isArray() : bool
    {
        return $this->tokenType == self::TOKEN_ARRAY;
    }

    public function isArrayExpr() : bool
    {
        return $this->tokenType == self::TOKEN_ARRAY_EXPR;
    }

    public function isValue() : bool
    {
        return ($this->tokenType == self::TOKEN_STRING ||
         $this->tokenType == self::TOKEN_NUMBER);
    }

    public function isTrue() : bool
    {
        return $this->tokenType == self::TOKEN_TRUE;
    }

    public function isFalse() : bool
    {
        return $this->tokenType == self::TOKEN_FALSE;
    }

    public function isBoolean() : bool
    {
        return $this->isTrue() || $this->isFalse();
    }

    public function isSameValue(ExprParserToken $compareTo) : bool
    {
        // Uses == not ===, to allow proper conversions.
        return $this->getValue() == $compareTo->getValue();
    }

    public function getTokenStr() : string
    {
        return $this->tokenStr;
    }

    public function getExtra() : mixed
    {
        return $this->extra;
    }

    public function setExtra(mixed $extra)
    {
        $this->extra = $extra;
    }

    public function getValue() : mixed
    {
        if ($this->tokenType == self::TOKEN_NUMBER) {
            return (float)$this->tokenStr;
        }
        return $this->tokenStr;
    }

    public function isEmpty(): bool
    {
        //  If this is a boolean false, an empty array, or an empty value, then return empty is true.
        if ($this->tokenType == self::TOKEN_FALSE ||
         ($this->tokenType == self::TOKEN_ARRAY && (!is_array($this->extra) || count($this->extra) == 0)) ||
         (($this->tokenType == self::TOKEN_STRING || $this->tokenType == self::TOKEN_NUMBER) &&
         empty($this->tokenStr))) {
            return true;
        }
        return false;
    }

    public function getIdValue(ExprParserCode $code) : ExprParserToken
    {
        $value = $code->getValue($this->tokenStr);
        if ($value) {
            return new ExprParserToken($value, is_numeric($value) ? self::TOKEN_NUMBER : self::TOKEN_STRING);
        }
        return self::$nullToken;
    }

    public function executeOperator(/** @noinspection PhpUnusedParameterInspection */ExprParserCode $code,
     array &$rtStack, int $operandNum, ?int &$numTokensToSkip) : bool|ErrorMessage
    {
        $numTokensToSkip = 0;
        switch ($this->tokenType) {
            case self::TOKEN_OP_NOT_EQUAL:
            case self::TOKEN_OP_EQUAL:
                if ($operandNum == 2) {
                    if (count($rtStack) < 2) {
                        throw new Exception("Runtime stack underflow on operator token");
                    }
                    $operand1 = array_pop($rtStack);
                    $operand2 = array_pop($rtStack);
                    $res = $operand1->isSameValue($operand2);
                    $res = ($this->tokenType == self::TOKEN_OP_EQUAL) ? $res : !$res;
                    $rtStack[] = $res ? self::$trueToken : self::$falseToken;
                }
                break;
            case self::TOKEN_ROP_OR:
                if ($operandNum == 2) {
                    if (count($rtStack) < 2) {
                        throw new Exception("Runtime stack underflow on OR token");
                    }
                    $operand1 = array_pop($rtStack);
                    $operand2 = array_pop($rtStack);
                    $res = $operand1->isTrue() || $operand2->isTrue();
                    $rtStack[] = $res ? self::$trueToken : self::$falseToken;
                }
                break;
            case self::TOKEN_ROP_AND:
                if ($operandNum == 1) {
                    $operand1 = $rtStack[count($rtStack)-1];
                    if (!$operand1->isTrue()) {
                        $numTokensToSkip = 1;
                        $rtStack[] = self::$falseToken;  // Push false for second operand.
                    }
                } else if ($operandNum == 2) {
                    if (count($rtStack) < 2) {
                        throw new Exception("Runtime stack underflow on AND token");
                    }
                    $operand1 = array_pop($rtStack);
                    $operand2 = array_pop($rtStack);
                    $res = $operand1->isTrue() && $operand2->isTrue();
                    $rtStack[] = $res ? self::$trueToken : self::$falseToken;
                }
                break;
            case self::TOKEN_OP_UNARY_NOT:
                $operand = array_pop($rtStack);

                //  If it's false, push true, else false
                $res = ($operand->tokenType == self::TOKEN_FALSE ||
                 $operand->tokenType == self::TOKEN_NULL ||
                 ($operand->isValue() && empty($operand->tokenStr)));
                $rtStack[] = self::getBoolToken($res);
                break;
        }
        return true;
    }

    /**
     * @return array|string
     */
    public function __toString()
    {
        $extraStr = "";
        if ($this->extra && $this->tokenType == self::TOKEN_ARRAY) {
            $extraStr = ", elems: ".implode(',',$this->extra);
        } else if ($this->extra && $this->tokenType == self::TOKEN_ARRAY_EXPR) {
            $extraStr = ", num elems: ".$this->extra;
        } else if ($this->extra && $this->tokenType == self::TOKEN_FUNCTION) {
            $extraStr = ", num args: ".$this->extra;
        }
        $tokenStr = $this->tokenStr ? "'" . $this->tokenStr . "'," : "";
        return "TOKEN $tokenStr".self::$tokenToStr[$this->tokenType].$extraStr;
    }
}
