<?php
/**
 * Copyright (c) 2016-2019 <PERSON>, licensed under MIT
 */

class ToUppercaseRule extends Rule implements ModifyValue
{

    /**
     * {@inheritDoc}
     */
    public function modifyValue($value)
    {
        return ($value !== '') ? isl_strtoupper($value) : '';
    }

    /**
     * Check the $value is valid
     *
     * @param mixed $value
     *
     * @return bool
     */
    public function check($value) : bool
    {
        return true;
    }
}
