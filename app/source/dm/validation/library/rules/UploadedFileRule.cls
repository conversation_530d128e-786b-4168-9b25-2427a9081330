<?php
/**
 * Copyright (c) 2016-2019 <PERSON>, licensed under MIT
 */

class UploadedFileRule extends Rule implements BeforeValidate
{
    use FileTrait;
    use SizeTrait;

    // protected $maxSize = null;
    // protected $minSize = null;

    protected array $allowedTypes = [];

    /**
     * Given $params and assign $this->params
     *
     * @param array &$params
     * @return self
     */
    public function fillParameters(array &$params): Rule
    {
        $this->minSize(array_shift($params));
        $this->maxSize(array_shift($params));
        $this->fileTypes(array_shift($params));

        return $this;
    }

    /**
     * Given $size and set the max size
     *
     * @param string|int $size
     * @return self
     */
    public function maxSize($size): Rule
    {
        $this->params['max_size'] = $size;
        return $this;
    }

    /**
     * Given $size and set the min size
     *
     * @param string|int $size
     * @return self
     */
    public function minSize($size): Rule
    {
        $this->params['min_size'] = $size;
        return $this;
    }

    /**
     * Given $min and $max then set the range size
     *
     * @param string|int $min
     * @param string|int $max
     * @return self
     */
    public function sizeBetween($min, $max): Rule
    {
        $this->minSize($min);
        $this->maxSize($max);

        return $this;
    }

    /**
     * Given $types and assign $this->params
     *
     * @param mixed $types
     * @return self
     */
    public function fileTypes($types): Rule
    {
        if (is_string($types)) {
            $types = explode('|', $types);
        }

        $this->params['allowed_types'] = $types;

        return $this;
    }

    /**
     * {@inheritDoc}
     */
    public function beforeValidate()
    {
        $attribute = $this->getAttribute();

        // We only resolve uploaded file value
        // from complex attribute such as 'files.photo', 'images.*', 'images.foo.bar', etc.
        if (!$attribute->isUsingDotNotation()) {
            return;
        }

        $keys = explode(".", $attribute->getKey());
        $firstKey = array_shift($keys);
        $firstKeyValue = $this->validation->getValue($firstKey);

        $resolvedValue = $this->resolveUploadedFileValue($firstKeyValue);

        // Return original value if $value can't be resolved as uploaded file value
        if (!$resolvedValue) {
            return;
        }

        $this->validation->setValue($firstKey, $resolvedValue);
    }

    /**
     * Check the $value is valid
     *
     * @param mixed $value
     * @return bool
     */
    public function check($value): bool
    {
        $file = $this->getAttributeAlias();
        $this->message = new ErrorMessage(
            sprintf("The %s is not a valid uploaded file", $file),
            'DM-0047',
            ['FILE_NAME' => $file]
        );
        
        $minSize = $this->parameter('min_size');
        $maxSize = $this->parameter('max_size');
        $allowedTypes = $this->parameter('allowed_types');

        // below is Required rule job
        if (!$this->isValueFromUploadedFiles($value) or $value['error'] == UPLOAD_ERR_NO_FILE) {
            return true;
        }

        if (!$this->isUploadedFile($value)) {
            return false;
        }

        // just make sure there is no error
        if ($value['error']) {
            return false;
        }

        if ($minSize) {
            $bytesMinSize = $this->getBytesSize($minSize);
            if ($value['size'] < $bytesMinSize) {
                $this->message =  new ErrorMessage(
                    sprintf("The %s file is too small, minimum size is %s", $file, $minSize),
                    'DM-0048',
                    [
                        ['FILE_NAME' => $file],
                        ['FILE_MIN_SIZE' => $minSize]
                    ]
                );
                return false;
            }
        }

        if ($maxSize) {
            $bytesMaxSize = $this->getBytesSize($maxSize);
            if ($value['size'] > $bytesMaxSize) {
                $this->message = new ErrorMessage(
                    sprintf("The %s file is too large, maximum size is %s", $file, $bytesMaxSize),
                    'DM-0049',
                    [
                        ['FILE_NAME' => $file],
                        ['FILE_MAX_SIZE' => $bytesMaxSize]
                    ]
                );
                return false;
            }
        }
        
        if (!empty($allowedTypes)) {
            $guesser = new MimeTypeGuesser;
            $ext = $guesser->getExtension($value['type']);
            unset($guesser);

            if (!in_array($ext, $allowedTypes)) {
                $this->message = new ErrorMessage(
                    sprintf("The %s file type must be %s", $file, $allowedTypes),
                    'DM-0050',
                    [
                        ['FILE_NAME' => $file],
                        ['FILE_TYPE' => $allowedTypes]
                    ]
                );
                return false;
            }
        }

        return true;
    }
}
