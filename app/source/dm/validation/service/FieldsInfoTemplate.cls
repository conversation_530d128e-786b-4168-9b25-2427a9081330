<?php
//=============================================================================
//
//  FILE:     FieldsInfoTemplate.cls
//  AUTHOR:   Anca Uricariu
//  DESCRIPTION: Class to manage the template defined in .ent files for each field
//
//  (C)2000,2022 Intacct Corporation, All Rights Reserved
//
//  Intacct Corporation Proprietary Information.
//  This document contains trade secret data that belongs to Intacct
//  corporation and is protected by the copyright laws. Information herein
//  may not be used, copied or disclosed in whole or part without prior
//  written consent from Intacct Corporation.
//
//=============================================================================

/**
 * Class FieldsInfoTemplate.cls used in import,export process
 *
 */
class FieldsInfoTemplate
{
    const MAX_EXCEL_CELL_CHARS = 32000;
    const FIELD_INFO_FIELDS = [
        'DESC', 'editable', 'maxlength'
    ];
    private ?array $imetaDict;
    private ?array $fieldInfo;

    protected string $uiName       = '';
    protected string $type         = 'Character';
    protected string $format       = '';
    protected string $length       = '';
    protected ?int   $maxLength    = null;
    protected string $default      = '';
    protected string $valid        = '';
    protected string $dependencies = '';
    protected string $required     = '';
    protected string $editable     = '';
    protected string $desc         = '';
    protected string $note         = '';

    protected array $validValues       = [];
    protected array $validValuesShorts = [];
    protected bool  $multi             = false;
    protected int   $left              = -1;
    protected int   $right             = -1;

    protected array $template = [];

    /**
     * @var array i18n tokens to be preloaded
     */
    protected array $tokens = [
        "IA.FIELD_NAME",
        "IA.UI_FIELD_NAME",
        "IA.TYPE",
        "IA.FORMAT",
        "IA.MAX_LENGTH",
        "IA.DEFAULT_VALUE",
        "IA.VALID_VALUES",
        "IA.VALID_VALUES_CHECK_APPLICATION",
        "IA.NONE",
        "IA.ONE_OR_MORE_SEPARATED",
        "IA.DEPENDENCIES",
        "IA.REQUIRED",
        "IA.EDITABLE",
        "IA.NOTE",
        'IA.YES',
        'IA.NO'
    ];

    public function __construct(array $fieldInfo, array $imetaDict)
    {
        $this->fieldInfo = $fieldInfo;
        foreach (self::FIELD_INFO_FIELDS as $fieldName) {
            if (!isset($this->fieldInfo[$fieldName])) {
                $this->fieldInfo[$fieldName] = null;
            }
        }
        $this->imetaDict = $imetaDict;

        $this->template = $this->fieldInfo['template'] ?? [];
    }

    /**
     * Get description of an entity field to be included in import template.
     * $fieldInfo['template'] has priority for generation of template
     *
     * @param bool $skipLongValidLabels 
     *
     * @return string
     */
    public function generateFieldSpec(bool $skipLongValidLabels = false)
    {
        // DESC has priority
        if ($this->fieldInfo['DESC']) {
            return $this->fieldInfo['DESC'];
        }

        $this->extractFromFieldInfo();

        $this->extractFromTemplateInfo();
        I18N::addTokens(I18N::tokenArrayToObjectArray($this->tokens));
        I18N::getText();
        $str = [];

        $str[] = I18N::getSingleToken("IA.FIELD_NAME") . ': ' . I18N::getSingleToken($this->fieldInfo['path']);
        $str[] = I18N::getSingleToken("IA.UI_FIELD_NAME") . ': ' . I18N::getSingleToken($this->uiName);
        $str[] = I18N::getSingleToken("IA.TYPE") . ': ' . I18N::getSingleToken($this->type);

        if ($this->format) {
            $str[] = I18N::getSingleToken("IA.FORMAT") . ': ' . $this->format;
        }

        if (!is_null($this->maxLength) && $this->maxLength > 0) {
            $str[] = I18N::getSingleToken("IA.MAX_LENGTH") . ': ' . $this->maxLength;
        }
        if ($this->length != '' ) {
            $str[] = I18N::getSingleToken("IA.LENGTH") . ': ' . $this->length;
        }

        $str[] = I18N::getSingleToken("IA.DEFAULT_VALUE") . ': ' . (I18N::getSingleToken($this->default) ? : I18N::getSingleToken("IA.NONE"));

        if ($this->valid) {
            // Excell has a limit of 32000 characters in a cell
            if ($skipLongValidLabels && strlen($this->valid )> self::MAX_EXCEL_CELL_CHARS) {
                $str[] = I18N::getSingleToken("IA.VALID_VALUES_CHECK_APPLICATION");
            } else {
                if ($this->multi) {
                    $str[] = I18N::getSingleToken("IA.VALID_VALUES") . " " . I18N::getSingleToken(
                            "IA.ONE_OR_MORE_SEPARATED"
                        ) . ': ' . $this->valid;
                } else {
                    $str[] = I18N::getSingleToken("IA.VALID_VALUES") . ': ' . I18N::getSingleToken($this->valid);
                }
            }
        }

        $str[] = I18N::getSingleToken("IA.DEPENDENCIES") . ': ' . (I18N::getSingleToken($this->dependencies) ? : I18N::getSingleToken("IA.NONE"));
        $str[] = I18N::getSingleToken("IA.REQUIRED") . ': ' . I18N::getSingleToken($this->required);
        $str[] = I18N::getSingleToken("IA.EDITABLE") . ': ' . I18N::getSingleToken($this->editable);

        // Description
        // if ( $this->desc ) {
        //     $str[] = _("Description") . ": " . $this->desc;
        // }

        if ($this->note) {
            $str[] = I18N::getSingleToken("IA.NOTE") . ': ' . I18N::getSingleToken($this->note);
        }

        $specs = implode("\n", $str);

        return substr($specs, 0, 32765);

    }

    public function extractFromFieldInfo()
    {
        $this->left = (int) ($this->fieldInfo['type']['leftofdecimal'] ?? $this->left);
        $this->right = (int) ($this->fieldInfo['type']['rightofdecimal'] ?? $this->right);

        $this->uiName = $this->fieldInfo['fullname'] ? : '';
        $this->maxLength = 0;
        if (isset($this->fieldInfo['type']['maxlength']) && (int) $this->fieldInfo['type']['maxlength'] > 0) {
            $this->maxLength = (int) $this->fieldInfo['type']['maxlength'];
        } else if (isset($this->fieldInfo['type']['size']) && (int) $this->fieldInfo['type']['size'] > 0) {
            $this->maxLength = (int) $this->fieldInfo['type']['size'];
        }
        $this->editable = $this->fieldInfo['editable'] ? 'IA.YES': 'IA.NO'; // default
        $this->desc = $this->fieldInfo['desc'] ? : '';

        $this->extractRequired();

        $this->extractDefault();

        $this->extractType();

        $this->extractFormat();

        $this->extractValidValues();

        $this->tokens[] = $this->type;
        if (isset($this->dependencies) && is_string($this->dependencies) && ($this->dependencies !== '')) {
            $this->tokens[] = $this->dependencies;
        }
        if (isset($this->valid) && is_string($this->valid) && ($this->valid !== '')) {
            $this->tokens[] = $this->valid;
        }
        if (isset($this->default) && is_string($this->default) && ($this->default !== '')) {
            $this->tokens[] = $this->default;
        }
        if (isset($this->uiName) && is_string($this->uiName) && ($this->uiName !== '')) {
            $this->tokens[] = $this->uiName;
        }
    }

    /**
     * Extract information from entity template field(inside fieldinfo structure
     */
    public function extractFromTemplateInfo()
    {
        // required may be defined as boolean
        if (isset($this->template['required']) && is_bool($this->template['required'])) {
            $this->template['required'] = $this->booleanToYesOrNo($this->template['required']);
        }

        // editable may be defined as boolean
        if (isset($this->template['editable'])) {
            $this->template['editable'] = $this->booleanToYesOrNo($this->template['editable']);
        }

        // imetagroom functions override the defaults.  Deal with known types here.
        // This is only a subset that we know are used for custom fields.
        // if ( isset($this->imetagroom[$importFieldName]) ) {
        //     $function = $this->imetagroom[$importFieldName];
        //     $foundMetagroom = false;
        //     try {
        //         $desc = $this->$function(IMETAGROOM_INTROSPECTION);
        //         if ( is_array($desc) && array_key_exists('default', $desc) ) {
        //             $foundMetagroom = true;
        //             if ( isset($desc['default']) ) {
        //                 $default = $desc['default'];
        //             }
        //             if ( isset($desc['valid']) ) {
        //                 $this->valid = $desc['valid'];
        //             }
        //             if ( isset($desc['type']) ) {
        //                 $type = $desc['type'];
        //             }
        //         }
        //     } catch (Exception $e) {
        //     }
        //     if ( ! $foundMetagroom ) {
        //         if ( ! Globals::$g->islive ) {
        //             $str[] = "\n(imetagroom function $function does not support introspection. Please log a ticket.)";
        //             logToFileError("Import error: imetagroom function $function does not support introspection.\n");
        //         }
        //     }
        // }

        // template has priority
        $this->uiName = $this->template[Struct::UI_NAME] ?? $this->uiName;
        $this->type = $this->template[Struct::DATA_TYPE] ?? $this->type;
        $this->format = $this->template[Struct::FORMAT] ?? $this->format;
        $this->required = $this->template[Struct::REQUIRED] ?? $this->required;
        $this->editable = $this->template[Struct::EDITABLE] ?? $this->editable;
        $this->maxLength = $this->template[Struct::DATA_LENGTH] ?? $this->maxLength;
        $this->valid = $this->template[Struct::VALID_VALUES] ?? $this->valid;
        $this->dependencies = $this->template[Struct::DEPENDENCIES] ?? $this->dependencies;
        $this->note = $this->template[Struct::NOTE] ?? $this->note;
    }

    protected function extractRequired()
    {
        $required = $this->fieldInfo['required'] ?? false;

        // transform string as boolean into boolean
        if ( ! is_bool($required)) {
            $required = strtolower($required) === 'true' ? true : false;
        }

        $this->required = $this->booleanToYesOrNo($required);
    }

    /**
     * Extract default field from fieldinfo in .ent
     */
    protected function extractDefault()
    {
        $fieldName = $this->fieldInfo['path']; // TODO

        if (isset($this->fieldInfo['default'])) {
            $this->default = $this->fieldInfo['default'];
        } else if (isset($this->fieldInfo['webcombodefault'])) {
            $this->default = $this->fieldInfo['webcombodefault'];
        }

        if (isset($this->imetaDict[$fieldName]['default'])) {
            $this->default = $this->imetaDict[$fieldName]['default'];
        }

        if (isset($this->template[Struct::DEFAULT_VALUE])) {
            $this->default = $this->template[Struct::DEFAULT_VALUE];
        }

        if (is_bool($this->default)) {
            $this->default = $this->default ? 'IA.TRUE' : 'IA.FALSE';
        }
    }

    /**
     * Extract the type text and valid values text to display in template from .ent file
     */
    protected function extractType()
    {
        global $gManagerFactory;

        $this->type = "IA.CHARACTER";

        switch (strtolower($this->fieldInfo['type']['ptype'] ?? 'none')) {
            case 'email':
                $this->type = "IA.CHARACTER";
                $this->valid = "IA.A_VALID_EMAIL_ADDRESS";
                break;
            case 'url':
                $this->type = "IA.CHARACTER";
                $this->valid = "IA.A_VALID_URL_ADDRESS";
                break;
            case 'integer':
                $this->type = 'IA.NUMBER';
                $this->valid = 'IA.DIGITS_0_9';
                break;
            case 'currency':
            case 'decimal':
                $this->type = 'IA.NUMBER';
                $this->valid = 'IA.DIGITS_0_9';
                if ($this->right > 0) {
                    $this->valid = 'IA.DIGITS_0_9_AND_DECIMAL_POINT';
                }
                break;
            case 'text':
            case 'multitext':
            case 'textarea':
            case 'multitextbox':
                break;
            case 'percent':
                $this->type = 'IA.NUMBER_AS_PERCENTAGE';
                $this->valid = 'IA.DIGITS_0_9';
                if ($this->right > 0) {
                    $this->valid = 'IA.DIGITS_0_9_AND_DECIMAL_POINT';
                }
                break;
            case 'date':
                $this->type = 'IA.DATE';
                $this->length = 10;
                if (isset($this->fieldInfo['defaulttotoday']) && $this->fieldInfo['defaulttotoday']) {
                    $this->default = "IA.TODAY_S_DATE";
                }
                $this->valid = 'IA.IMPORT_DATE_FORMAT';
                break;
            case 'boolean':
            case 'webcombo':
            case 'radio':
            case 'multipick':
                // $multi = $this->fieldInfo['type']['type'] == 'multipick';
                // if ( isset($this->fieldInfo['type']['validvalues']) ) {
                //     foreach ( $this->fieldInfo['type']['validvalues'] as $i => $v ) {
                //         if ( strlen($this->valid) ) {
                //             $this->valid .= ", ";
                //         }
                //         $this->valid .= $v;
                //         if ( isset($this->fieldInfo['type']['validlabels'][$i]) ) {
                //             if ( $this->fieldInfo['type']['validlabels'][$i] != $v ) {
                //                 $this->valid .= "(" . $this->fieldInfo['type']['validlabels'][$i] . ")";
                //             }
                //         }
                //     }
                // }
                break;
            case 'ptr':
            case 'supdocptr':
                if (isset($this->fieldInfo['type']['entity'])) {
                    $ptrTypeMgr = $gManagerFactory->getManager($this->fieldInfo['type']['entity']);
                    if ($ptrTypeMgr) {
                        $this->dependencies = 'IA.A_VALID_'.strtoupper($this->fieldInfo['type']['entity']);
                    }
                }
                break;
            case 'none':
            case 'seqnum':
            case 'password':
                // skip this field, this should not be included in import.
                break;
            case 'uddlookup':
            case 'platformptr':
                // skip this field, UDDs are added later.
                break;
            case 'enum':
                // if ( $this->imetaDict && isset($imetaDict[$fieldName]['enum']) ) {
                //     $separator = '';
                //     foreach ( $imetaDict[$fieldName]['enum'] as $external => $enumItemInfo ) {
                //         $english = is_array($enumItemInfo) ? $enumItemInfo['desc'] : $enumItemInfo;
                //         $this->valid .= "$separator$external ($english)";
                //         $separator = ', ';
                //     }
                //     } elseif ( isset($this->fieldInfo['type']['validvalues']) ) {
                //         $this->validValues[] = $this->fieldInfo['type']['validvalues'];
                //         $this->validValuesShorts[] = $this->fieldInfo['type']['_validivalues'];
                // }
                break;
            default:
                global $islive;
                if ( ! $islive) {
                    $specs =
                        "\nUNEXPECTED FIELD TYPE (" . $this->fieldInfo['type']['ptype'] . ") Please report as defect.";
                    logToFileError("Import error: $specs\n");
                }
                break;
        }
    }

    protected function extractFormat()
    {
        // Format
        if (isset($this->template['format'])) {
            $this->format = $this->template['format'];
        } else if ($this->left >= 0 || $this->right >= 0) {
            $this->format = $this->left >= 0 ? $this->left : '0';
            $this->format .= ".";
            $this->format .= $this->right >= 0 ? $this->right : '0';
        }
    }

    protected function extractValidValues()
    {
        $fieldName = $this->fieldInfo['path'] ?? '';

        if ($this->imetaDict && isset($imetaDict[$fieldName]['enum'])) {
            foreach ( $imetaDict[$fieldName]['enum'] as $external => $enumItemInfo ) {
                $english = is_array($enumItemInfo) ? $enumItemInfo['desc'] : $enumItemInfo;
                $this->validValues[] = $english;
                $this->validValuesShorts[] = $external;
            }
            if ( ! empty($this->validValues)) {
                $this->valid = implode(", ", $this->validValues);

                if ( ! empty($this->validValuesShorts) && count($this->validValuesShorts) == count($this->validValues)) {
                    $newValues = [];
                    foreach ( $this->validValues as $i => $value ) {
                        $newValues[] = $value . " (" . ($this->validValuesShorts[$i]) . ")";
                    }
                    $this->valid = implode(", ", $newValues);
                }
            }
        } else {
            $this->validValues = $this->fieldInfo['type']['validvalues'] ?? [];
            $this->validValuesShorts = $this->fieldInfo['type']['_validivalues'] ?? [];

            $this->validValues = array_filter($this->validValues); // remove empty values
            $this->validValuesShorts = array_filter($this->validValuesShorts); // remove empty values
            $validLabels = array_filter($this->fieldInfo['type']['validlabels'] ?? []);

            $processValues = [];
            if ( ! empty($this->validValues)) {
                $processValues = $this->validValues;
            }
            if ( ! empty($this->validValuesShorts)) {
                $processValues = $this->validValuesShorts;
            }
            $displayValues = [];
            foreach ($processValues as $key => $value) {
                if (isset($validLabels[$key])) {
                    $displayValues[] = $value . ' = ' . I18N::getSingleToken($validLabels[$key]);
                } else {
                    $displayValues[] = $value;
                }
            }
            if (count($processValues) > 0) {
                $this->valid = implode(", ", $processValues) . ' (' . implode(", ", $displayValues) . ')';
            }
        }
    }

    /**
     * @param $value
     *
     * @return string
     */
    private function booleanToYesOrNo($value) : string
    {
        if (is_bool($value)) {
            $value = ($value ? 'IA.YES' : 'IA.NO');
        }

        return $value;
    }
}
