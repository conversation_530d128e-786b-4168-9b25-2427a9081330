<?

import('NLister');

/**
 * Class SubsidiaryLister
 */
class SubsidiaryLister extends NLister
{

    /**
     * Subsidiary Links specific tokens
     *
     * @var string[]
     */
    protected $additionalTokens
        = [
            'IA.THE_MAPPED_PERIODS_ACCOUNTS_DEPARTMENTS_LOC'
        ];
    
    function __construct()
    {

        $fieldNames = [
            'RECORDNO',
            'SUBSIDIARYKEY',
            'SUBS<PERSON><PERSON>RYID',
            'SUBSIDIARYNAME',
            'LOCATION.LOCATIONID',
            'DEFAULTPCT'
        ];

        parent::__construct(
            array(
                'entity' => 'subsidiary',
                'title' => 'IA.SUBSIDIARY_LINKS',
                'fields' => $fieldNames,
                'helpfile' => 'Subsidiary_Links_Lister',
                'entitynostatus' => true,
                'showstatus' => false,
                'disablestatus' => true,
                'hide_cancel' => true
            )
        );

        // add labels specific to this lister
        $this->addLabelMapping('DEFAULTPCT', 'IA.PERCENTAGE_OWNERSHIP', true);
        $this->addLabelMapping('SUBSIDIARYID_URL', 'IA.COMPANY_ID', true);
    }


    /**
     * BuildTable
     */
    function BuildTable()
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $_sess = Session::getKey();

        parent::BuildTable();
        $table = &$this->table;

        for ($i = 0; $i < count($table); $i++) {
            $rec = $table[$i];
            $table[$i]['SUBSIDIARYID_URL'] = "<a href=\"javascript:SlideLaunch('subsidiary',0," . $rec['SUBSIDIARYKEY']
                . ",'','','')\">" . $rec['SUBSIDIARYID'] . "</a>";
        }

        $flds = ['SUBSIDIARYID_URL', 'SUBSIDIARYNAME', 'LOCATION.LOCATIONID', 'DEFAULTPCT'];
        $this->SetOutputFields($flds, []);
    }

    /**
     * @return array
     */
    function CalcFiltersLite()
    {
        $this->_params['_fields'] = array('SUBSIDIARYID_URL', 'SUBSIDIARYNAME', 'LOCATION.LOCATIONID', 'DEFAULTPCT');
        $this->_params['_fieldorders'] = array('SUBSIDIARYID', 'SUBSIDIARYNAME', 'LOCATION.LOCATIONID', 'DEFAULTPCT');
        $this->_params['_fullnames'] = array('SUBSIDIARYID', 'SUBSIDIARYNAME', 'LOCATION.LOCATIONID', 'DEFAULTPCT');
        CalcFiltersLite($this->_params, $ret, 1);
        $entityFilters = $this->getEntityMgr()->GetDBFilters();
        $ret = ($entityFilters) ?  INTACCTarray_merge($ret, $entityFilters) : $ret ;
        return $ret;  
    }

    // To Warn the user when he tries to delete the subsidiary link with the effect of the deletion.

    /**
     * @param int $i
     * @return string
     */
    function DLT($i = 0)
    {
        $showYUIPanel = ( GetValueForIACFGProperty('IA_LISTER_DELETE_YUIPANEL') == 'DISABLED' ) ? false : true;
        $msg = GT($this->textMap, 'IA.THE_MAPPED_PERIODS_ACCOUNTS_DEPARTMENTS_LOC');
        $onclick = " onclick='return confdel(\"$msg\", \"$showYUIPanel\");'  ";
        return $onclick;
    }
}


