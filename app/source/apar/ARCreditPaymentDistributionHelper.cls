<?php

/**
 * Helper class for distributing payments.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Intacct Corporation All, Rights Reserved
 */
class ARCreditPaymentDistributionHelper extends CreditPaymentDistributionHelper
{

    /**
     * ARCreditPaymentDistributionHelper constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Implementing method to populate the entity manager for AR Payment detail processor.
     */
    protected function populateEntityManager()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $this->entityManager = $gManagerFactory->getManager('arpymt');
    }

    /**
     * @return string
     */
    protected function getNegativeCreditType()
    {
        return self::NEGATIVE_INVOICE;
    }

    /**
     * @return string
     */
    protected function getTransactionType()
    {
        return PaymentUtils::INVOICE_RECTYPE;
    }

    /**
     * @return string
     */
    protected function getAdjustmentTransactionType()
    {
        return PaymentUtils::ARADJUSTMENT_RECTYPE;
    }

    /**
     * @return string
     */
    protected function getAdvanceTransactionType()
    {
        return PaymentUtils::ARADV_RECTYPE;
    }

    /**
     * @return string
     */
    protected function getOverPaymentTransactionType()
    {
        return PaymentUtils::ARPOSTEDPAYMENT_RECTYPE;
    }

    /**
     * Calculate the transaction entry total due
     *
     * @param PREntryDTO    $entry the entry data
     * @param bool          $includeCurrentPymt true if we should include the distrubution already applied else false
     *
     * @return string|float the transaction entry total due
     */
    public function calculateEntryTotalDue(PREntryDTO $entry, $includeCurrentPymt = true)
    {
        // Calculate the original total due
        $trx_totaldue = ibcsub($entry->getTrxAmount(), $entry->getTrxTotalPaid());

        // Also consider the payment already applied as part of this process
        if ($includeCurrentPymt) {
            // $distributionPath = $this->getDistributionAmountPath();
            // $trx_totaldue = ibcsub($trx_totaldue, $entry['__distribute'][$distributionPath]);
            //$appliedCredit = $this->paymentInfoObj->getBillLinePaymentDistribution($entry->getRecordKey(),
            //    $entry->getRecordNo(), $this->distributionPath);
            /** @var PaymentInfo $paymentInfoObj */
            $paymentInfoObj = $this->getPaymentInfoObj();
            $appliedCredit = $paymentInfoObj->getTotalBillLinePaymentDistribution($entry->getRecordKey(), $entry->getRecordNo()) ;
            $appliedCredit = $appliedCredit ?? 0;
            if($appliedCredit == 0) {
                // In some cases Credits are applied from UI, that info is available in the line object
                // source it from there if the payment info do not have
                if(!empty($entry->getCustomFieldValue('CREDITSAPPLIED'))) {
                    $appliedCredit = $entry->getCustomFieldValue('CREDITSAPPLIED');
                }
            }
            $trx_totaldue = ibcsub($trx_totaldue, $appliedCredit);
        }

        return $trx_totaldue;
    }

    /**
     * Method to build the positive location Map
     *
     * @param string $key
     * @param array  $transactions
     */
    public function buildPosLocationMap($key, &$transactions)
    {
        // 1. Order the credits.
        $transactions = $this->orderTransactions($transactions);
        $positiveLinesMap = [];
        foreach ( $transactions as $transaction) {
            if ( is_countable($transaction['ITEMS']) ) {
                array_multisort(array_column($transaction['ITEMS'], 'LINE_NO'),
                                SORT_ASC, $transaction['ITEMS']);
                foreach ( $transaction['ITEMS'] as $itemValue ) {
                    //This condition is written because when we have discount applied we have two values set
                    //i.e $txnDiscount and $itemValue['TRX_SELECTED'] hence the amount adds up twice so we are
                    //commenting the below line. Will delete later if the effect is nill
                    $txnDiscount = $itemValue['TRX_SELECTED'] ?? $itemValue['DISCOUNTAPPLIED'] ?? 0;
                    $linePaidAmt = ibcadd($txnDiscount, $itemValue['TRX_TOTALPAID'],
                                          2, 1);
                    $totalReserveAmt = ibcadd($linePaidAmt, $itemValue['TRX_AMOUNTRETAINED']);
                    $availableAmount = ibcsub($itemValue['TRX_AMOUNT'], $totalReserveAmt, 2, 1);
                    if ( $availableAmount > 0 ) {
                        $posMap = [
                            'RECORD#'   => $itemValue['RECORDNO'],
                            'LOCATION#' => $itemValue['LOCATION#'],
                            'AMOUNT'    => $availableAmount
                        ];
                        $positiveLinesMap[$key][$transaction['RECORDNO']][$itemValue['LINE_NO']][] = $posMap;

                        $this->setTxnLocationKeys($itemValue['LOCATION#']);
                    }
                }
            }

            $this->setPositiveLinesMap($positiveLinesMap);
        }
    }

    /**
     * @param string $creditType
     * @param string $creditAmount
     * @param string $creditNo
     * @param string $docTotalDue
     */
    protected function docTotalDueCalculation($creditType, &$creditAmount, $creditNo,$docTotalDue)
    {
        // For all credits, the amount available should be less than or equal to the doc total due

        // check if previously updated doc total due present for the -ve bill, if so then take the
        // updated doc total due amount
        if ( ! empty($this->docTotalDueMap) && isset($this->docTotalDueMap[$creditNo]) ) {
            $docTotalDue = $this->docTotalDueMap[$creditNo];
        }
        // If the credit line's credit is more than the total bill's -ve amount then only apply the
        // doc total due that is available for other bills
        if ( isset($docTotalDue) && $creditAmount > ibcabs($docTotalDue) ) {
            $creditAmount = ibcabs($docTotalDue);
        }
    }

    /**
     * Now we have used the doctotal due to restricted the user to only oay the portion of the credits, we have to
     * recalculate the doctotal due
     *
     * @param string $creditType
     * @param string $creditNo
     * @param string $distAmt
     */
    protected function recalculateDocTotalDue($creditType,$creditNo,$distAmt)
    {
        // Update the doc total due for the credits
        // doc total due will be in -ve, so lets add the amount to reduce the
        // available amount
        if ( ! empty($this->docTotalDueMap) && isset($this->docTotalDueMap[$creditNo]) ) {
            $docTotalDue = $this->docTotalDueMap[$creditNo];
            $this->docTotalDueMap[$creditNo] = ibcsub($docTotalDue, $distAmt, 2, true);
        }

    }

    /**
     * @param array $creditLineDetail
     *
     * @return string
     */
    protected function getNegativeTotalDueMap($creditLineDetail)
    {
        return $creditLineDetail['TEMPDOCTOTALDUE'] ?? $creditLineDetail['DOCTOTALDUE'] ?? null;
    }
}