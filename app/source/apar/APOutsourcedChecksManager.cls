<?php

/**
 * Class APOutsourcedChecksManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

import('APPrintChecksManager');

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class APOutsourcedChecksManager extends APPrintChecksManager
{
    /**
     * @param array $_params the parameters of the class
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);
    }

    /**
     * search
     *
     * @param array  $data data
     * @param string $printed
     *
     * @return array
     */

    function search($data, $printed = 'false')
    {
        $querySpec = array();
        $respArray = array();
        $checkDetails = array();
        $checkVendors = array();
        $res = '';

        if ($this->createFilters($data, $querySpec)) {
            $res = QueryResult($querySpec);
        }
        GetPaymethodKey(OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD, $achpayMethodKey);
        GetPaymethodKey(OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD, $cdpayMethodKey);
        GetPaymethodKey(OutsourcedCheckUtils::OUTSOURCE_CC_METHOD, $ccpayMethodKey);
        foreach ($res as $resp) {
            $resp['C5'] = Currency($resp['C5'], 0, 0, $resp['C3']);
            $resp['C7'] = FormatDateForDisplay($resp['C7']);
            $jsFunctionToCall = 'previewCheckDelivery('.$resp['C12'].')';
            if ( ($resp['C22'] != OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD) && ($resp['C22'] != OutsourcedCheckUtils::OUTSOURCE_CC_METHOD) ) {
                $resp['PREVIEW'] = '<a href="#" onclick="'
                    . $jsFunctionToCall
                    . '"><img style="padding-top:5px"
                src="../resources/theme2011/blue/images/ia-app/buttons/view_new.png"
                /></a>';
            }
            $checkDetails[$resp['C12']] = $resp;
            $checkVendors[] = $resp['C0'];
            // C17: Column State from propayments table
            // C6:  Column docnumber from prrecord table
            if ( ($resp['C17'] && ($resp['C17'] != 'Unknown') ) && ( ($resp['C19'] == $achpayMethodKey) || ($resp['C19'] == $ccpayMethodKey) ||
                    ($resp['C19'] == $cdpayMethodKey && $resp['C6'] != '') ) ) {
                // If the status is 'Sent to Print' then
                // set the C18 field to the whensubmitted field (C15)
                if ( ($resp['C17'] == OutsourcedCheckUtils::AMEX_PYMT_STATE_S) || ($resp['C17'] == OutsourcedCheckUtils::AMEX_PYMT_STATE_S_TXT)
                    || ($resp['C17'] == OutsourcedCheckUtils::AMEX_PYMT_STATE_N) || ($resp['C17'] == OutsourcedCheckUtils::AMEX_PYMT_STATE_D)
                    || ($resp['C17'] == OutsourcedCheckUtils::AMEX_PYMT_STATE_F) ) {
                    $resp['C18'] = $resp['C15'];
                }
                if ($resp['C19']  == $ccpayMethodKey) {
                    $resp['C6'] = null;
                }
                $respArray['tobeposted'][] = $resp;
            } else {
                $respArray['tobesent'][]= $resp;
            }
        }

        /*
        * Get The Vendor Account number for each Check
        */
        if (!empty($checkDetails) && !empty($checkDetails) ) {
            //Get a list of unique vendors
            $checkVendors = array_unique($checkVendors);
            $this->AcctNo = $this->getLocAcctNo($checkDetails, $checkVendors);
            foreach ($respArray as &$resptab) {
                foreach ($resptab as &$resp) {
                    if ( $this->AcctNo[$resp['C12']] != '') {
                        $resp['C8'] = $this->AcctNo[$resp['C12']];
                        if ($resp['C8'] == 'MULTIPLE_AC_NO') {
                            $resp['C8'] = '';
                        }
                    }
                }
            }
        }
        return $respArray;


        //return(parent::Search($data, 13, "('A')"));
    }

    /**
     * Creates a query spec from the specified filter values.
     *
     * @param array $values values to use as filters
     * @param array &$query generated query spec
     *
     * @return bool true if successfull, false if not
     */

    private function createFilters(array $values, array &$query)
    {
        $pymethodkey = OutsourcedCheckUtils::getPayMethodKey();
        $prrecState = "('A' , 'P' , 'VP')";
        $from = '';
        $select = '';
        $entityFilter = '';
        $vendorFilter = '';
        $startDateFilter = '';
        $endDateFilter = '';
        $entitylevelchecksFilter = '';
        $locationFilter = '';

        // PROCESS WHERE CLAUSE
        $gManagerFactory = Globals::$g->gManagerFactory;
        global $gCheckPeriodSelectText;
        $query = array();
        $query[0] = "";
        $query[1] = GetMyCompany();
        $orders = "";
        $sorts = array();
        $period = $values['TABS_FILTERS']['PERIOD'];
        $period = ($period == $gCheckPeriodSelectText) ? '' : $period;
        if ( (!isset($values['TABS_FILTERS']['START_DATE'])
            || $values['TABS_FILTERS']['START_DATE'] == '')
            && (isset($period) && $period != '')
        ) {
            $asofDate = $values['ASOFDATE'];
            $periodMgr = $gManagerFactory->getManager('reportingperiod');
            $period = $periodMgr->GetRaw($period);
            GetReportingDateRange($period[0], $asofDate, $start, $end);
            $values['TABS_FILTERS']['START_DATE'] = $start;
            $values['TABS_FILTERS']['END_DATE'] = $end;
        }

        $numArgs = 1;
        if (IsMultiEntityCompany()) {
            $select = "location.location_no C13, location.record# C20," ;
            $from = ",location location";
            //$sorts[0] = "Entity";
            $entitylevelchecksFilter = "and apprintchecks.locationkey
            = location.record# (+) and location.cny# (+) = :1";

            if (isset($values['TABS_FILTERS']['SEARCH_LOCATIONID'])
                && $values['TABS_FILTERS']['SEARCH_LOCATIONID'] != ''
            ) {
                SetReportViewContext();
                $location = explode('--', $values['TABS_FILTERS']['SEARCH_LOCATIONID']);
                $locationFilter = "AND location.location_no = '".$location[0]."'";
            } else if (isset($values['TABS_FILTERS']['ENTITYLEVELCHECKS'])
                && $values['TABS_FILTERS']['ENTITYLEVELCHECKS'] == 'true'
            ) {
                SetReportViewContext();
            }
            if (GetContextLocation() != '') {
                $contextLocation = GetContextLocation();
                $entityFilter = " and (apprintchecks.locationkey = $contextLocation)";
            }
        }
        if ((isset($values['TABS_FILTERS']['SEARCH_FROM_VENDORID'])
            && $values['TABS_FILTERS']['SEARCH_FROM_VENDORID'] != '')
            || (isset($values['TABS_FILTERS']['SEARCH_TO_VENDORID']))
            && $values['TABS_FILTERS']['SEARCH_TO_VENDORID'] != ''
        ) {
            if ($values['TABS_FILTERS']['SEARCH_FROM_VENDORID'] != $values['TABS_FILTERS']['SEARCH_TO_VENDORID']) {
                $vendorFilter = '';
                if ( isset($values['TABS_FILTERS']['SEARCH_FROM_VENDORID'])
                    && $values['TABS_FILTERS']['SEARCH_FROM_VENDORID'] != ''
                ) {
                    $numArgs++;
                    $vendorFilter .= " and apprintchecks.entity >= :".$numArgs ;
                    $fromVendor = explode('--', $values['TABS_FILTERS']['SEARCH_FROM_VENDORID']);
                    $query[$numArgs] = "V".$fromVendor[0];
                }
                if ( isset($values['TABS_FILTERS']['SEARCH_TO_VENDORID'])
                    && $values['TABS_FILTERS']['SEARCH_TO_VENDORID'] != ''
                ) {
                    //need to correct this.
                    $numArgs++;
                    $vendorFilter .= " and apprintchecks.entity <= :".$numArgs;
                    $toVendor = explode('--', $values['TABS_FILTERS']['SEARCH_TO_VENDORID']);
                    $query[$numArgs] = "V".$toVendor[0];
                }
            } else {
                $vendorID = explode('--', $values['TABS_FILTERS']['SEARCH_TO_VENDORID']);
                $vendorFilter = "AND apprintchecks.entity = 'V".$vendorID[0]."'";
            }
        }
        if (isset($values['CHECKINGACCOUNT'])
            && $values['CHECKINGACCOUNT'] != ''
        ) {
            $numArgs++;
            $checkingAccount = explode('--', $values['CHECKINGACCOUNT']);
            $query[$numArgs] = $checkingAccount[0];
        }
        if (isset($values['TABS_FILTERS']['START_DATE'])
            && $values['TABS_FILTERS']['START_DATE'] != ''
        ) {
            $numArgs++;
            $startDateFilter = "AND apprintchecks.whencreated >= :".$numArgs;

            $query[$numArgs] = $values['TABS_FILTERS']['START_DATE'];
        }
        if (isset($values['TABS_FILTERS']['END_DATE'])
            && $values['TABS_FILTERS']['END_DATE'] != ''
        ) {
            $numArgs++;
            $endDateFilter = "AND apprintchecks.whencreated <= :".$numArgs;
            $query[$numArgs] = $values['TABS_FILTERS']['END_DATE'];
        }
        if (isset($values['TABS_FILTERS']['SORTORD'])
            && ($values['TABS_FILTERS']['SORTORD'] != '')
        ) {
            $sorts        = INTACCTarray_merge($sorts, explode('#~#', $values['TABS_FILTERS']['SORTORD']));
        }
        if (count($sorts) > 0 ) {
            $sortMap = array(
                'ENTITYID' => 'C0',
                'ENTITYNAME' => 'C1',
                'Bank'    => 'C2',
                'Amount' => 'C4',
                'Check' => 'C6',
                'Date' => 'C7',
                'Entity' => 'C13',
            );
            foreach ($sorts as $key => $sortBy) {
                if ( array_key_exists($sortBy, $sortMap) ) {
                    $orders .= $sortMap[$sortBy]." asc ";
                    if ($key < (count($sorts) -1)) {
                        $orders .= ", ";
                    }
                }
            }
            $orders = "ORDER BY ". $orders;
        }

        //Filter for check style
        $from .= ",bankaccount bankaccount, creditcard creditcard";
        $checkStyleFilter = "AND bankaccount.cny#(+) =:1
        AND bankaccount.accountid(+) = apprintchecks.financialentity
        AND apprintchecks.financialentity = creditcard.cardid(+) AND creditcard.cny#(+) = :1";

        $bankRestrictionFilter = '';
        //Apply Bank Restrictions
        if ( IsMultiVisibilitySubscribed('checkingaccount')
            && (GetContextLocation() || IsRestrictedUser())
        ) {
            $bankMgr = $gManagerFactory->getManager('checkingaccount');
            $allowAccts = $bankMgr->GetAllRestrictedFinancialAccts('checkingaccount');
            if (!empty($allowAccts)) {
                $accts = [];
                foreach ($allowAccts as $act) {
                    $accts[] = "'$act'";
                }
                GetPaymethodKey(OutsourcedCheckUtils::OUTSOURCE_CC_METHOD, $ccardMethodKey);
                $accts = implode(',', $accts);
                $bankRestrictionFilter =
                    " AND (apprintchecks.financialentity IN ($accts) OR apprintchecks.paymethodkey = $ccardMethodKey ) ";
            }
        }

        $query[0] = "SELECT vendor.vendorid C0,
                            vendor.name C1,
                            apprintchecks.financialentity C2,
                            apprintchecks.currency C3,
                            apprintchecks.totalentered C4,
                            apprintchecks.trx_totalentered C5,
                            apprintchecks.docnumber C6,
                            apprintchecks.whencreated C7,
                            nvl(apprintchecks.description, vendoracctno.accountno) C8,
                            apprintchecks.whendue C9,
                            apprintchecks.state C10,
                            apprintchecks.record# C12,
                            propymts.whensubmitted C15,
                            propymts.whenacknowledged C16,
                            propymts.whenprocessed C18,
                            DECODE(propymts.state,  'S', 'Sent to print',
                                                    'C', 'Cancelled',
                                                    'M', 'Mailed',
                                                    'Q', 'Queued for print',
                                                    'I', 'In-Transit',
                                                    'F', 'Failed',
                                                    'N', 'In progress',
                                                    'R', 'Credit initiated',
                                                    'P', 'Paid',
                                                    'T', 'Payment sent',
                                                    'L', 'Declined',
                                                    'A', 'Payment accepted',
                                                    'Unknown') C17,
                            apprintchecks.paymethodkey C19,
                            iap.name C22,
                            {$select}
                            COUNT(1) over() qcnt
                            FROM prrecord apprintchecks,
                            vendormst vendor,
                            vendoracctno vendoracctno,
                            propayments propymts,
                            iapaymethod iap
                            {$from}
                        WHERE (
                                apprintchecks.recordtype  IN ( 'pp' , 'pr' )
                                AND apprintchecks.paymethodkey in $pymethodkey
                                AND apprintchecks.state       IN $prrecState
                            )
                        AND apprintchecks.entity       = vendor.entity
                        AND vendor.cny#                = :1
                        AND vendor.vendoracctnokey     = vendoracctno.record# (+)
                        AND vendoracctno.cny# (+)      = :1
                        AND apprintchecks.cny#         = :1
                        AND propymts.cny# (+)          = :1
                        AND propymts.paymentkey (+)    = apprintchecks.record#
                        AND iap.record#                = apprintchecks.paymethodkey
						{$entityFilter}
                        {$vendorFilter}
                        {$startDateFilter}
                        {$endDateFilter}
                        {$entitylevelchecksFilter}
                        $locationFilter
                        $checkStyleFilter
						$bankRestrictionFilter
                        {$orders}";

        return true;
    }

}