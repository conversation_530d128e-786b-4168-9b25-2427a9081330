<?php
/**
 * Manager class for the AR Advance reverse functions.
 *
 * <AUTHOR>
 * @copyright 2021 Sage Intacct Inc., All Rights Reserved
 */

class ARAdvanceReverseManager extends ARAdvanceManager
{
    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = [])
    {
        parent::__construct($params);
        // set batch already created in AR Reverse Manager
        $this->setIsBatchAlreadyCreated(true);
        $this->setHasAcctLabel(false);
    }

    /**
     * Figure out if we allow negative line in the transaction
     *
     * @return bool true if negative line is allowed else false
     */
    protected function allowNegativeLine(): bool
    {
        // in case of reverse we will allow negative line
        return true;
    }

    /**
     * @return string
     */
    protected function getBatchField(): string
    {
        return 'PRBATCHKEY';
    }

    /**
     * @param string $batch
     *
     * @return array
     */
    protected function getBatchFilter($batch)
    {
        return [[
            ['RECORDNO', '=', $batch],
            ['RECORDTYPE', '=', BasePymtManager::ARPYMT_RECTYPE]
        ]];
    }

    /**
     * Figure out if the user has the post permission
     *
     * @return bool true if the user has the post permission
     */
    protected function hasSubmitPermission()
    {
        // in case of reverse, its allways true
        return true;
    }

    /**
     * Validate advance records
     *
     * @param array  $values the transaction data
     * @param string $action the action
     *
     * @return bool false if error else true
     */
    protected function validateData(&$values, $action)
    {
        // in case of reversal we need to reset the payment
        // & receipt date as whenpaid & whencreated
        $values['PAYMENTDATE'] = $values['WHENPAID'];
        $values['RECEIPTDATE'] = $values['WHENCREATED'];
        return parent::validateData($values, $action);
    }

    /**
     * Get a batch. Create it if it does not exists.
     *
     * The batching logic is not centralized or organized in any approriate way so we need ot delegate the job
     * to the transaction classes to figure out where to put the transaction.
     *
     * @param array   $values        the transaction data
     * @param string  $journalSymbol the journal symbol
     * @param bool $postToGL      true if we post to GL else false
     * @param int &$batchKey     the batch key
     * @param string &$returnBatchDate     the batch date
     *
     * @return bool false if error else true
     */
    protected function getBatchKey($values, $journalSymbol, $postToGL, &$batchKey, &$returnBatchDate)
    {
        // we already created a reverse batch in ar reverse manager,
        // no need to validate again
        return true;
    }

    /**
     * Figure out the record type from the entity
     *
     * @return string the record type
     */
    public function getRecordType()
    {
        return BasePymtManager::ARADV_RECTYPE;
    }

    /**
     * getAuditEntity - Audit entity
     *
     * @return string
     */
    public function getAuditEntity()
    {
        return 'aradvance';
    }
}