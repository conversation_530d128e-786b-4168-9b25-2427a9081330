<?
/**
 *  FILE:       bbtemplaterecs.ent
 *  AUTHOR:     <PERSON><PERSON>el
 *  DESCRIPTION:    Entity defination for BillBackTemplate
 *
 *  (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *  This document contains trade secret data that belongs to Intacct
 *  Corporation and is protected by the copyright laws.  Information
 *  herein may not be used, copied or disclosed in whole or in part
 *  without prior written consent from Intacct Corporation.
*/

 $kSchemas['bbtemplaterecs'] =array(
    'object' => array ( 
        'RECORD#',
        'INVOICEKEY',
        'BILLKEY',
        'TEMPLATEKEY',        
    ), 
    'schema' => array(
        'RECORD#'        => 'record#',
        'INVOICEKEY' => 'invoicekey',
        'BILLKEY' => 'billkey',        
        'TEMPLATEKEY' => 'templatekey',        
    ),    
    
    'fieldinfo' => array(
        array (
            'fullname' => 'IA.INVOICE_NUMBER',
            'desc' => 'IA.INVOICE_NUMBER',
            'type' => array (
                'ptype' => 'text', 
                'type' => 'text', 
                'maxlength' => 30, 
                'size' => 30, 
            ),
            'path' => 'INVOICEKEY',
            'required' => true,            
        ),        
        array (            
            'fullname' => 'IA.BILL_NUMBER',
            'desc' => 'IA.BILL_NUMBER',
            'type' => array (
                'ptype' => 'text', 
                'type' => 'text', 
                'maxlength' => 30, 
                'size' => 30, 
            ),
            'path' => 'BILLKEY',
            'required' => true,            
        ),
        array (            
            'fullname' => 'IA.TEMPLATE_NUMBER',
            'desc' => 'IA.TEMPLATE_NUMBER',
            'type' => array (
                'ptype' => 'text', 
                'type' => 'text', 
                'maxlength' => 30, 
                'size' => 30, 
            ),
            'path' => 'TEMPLATEKEY',
            'required' => true,            
        ),        
    ),    
    'table' => 'bbtemplaterecs',
    'module' => 'ar',
    'autoincrement' => 'RECORD#',
    'vid' => 'RECORD#',
    'printas' => 'IA.BILLBACK_TEMPLATE_RECORDS',
    'pluralprintas' => 'IA.BILLBACK_TEMPLATE_RECORDS',
 );
