<?php
/**
 * Created by JetBrains PhpStorm.
 * User: r<PERSON><PERSON><PERSON>
 * Date: 18/6/15
 * Time: 12:53 PM
 * To change this template use File | Settings | File Templates.
 */

class VendorRunMatchEditor extends FormEditor
{
    /**
     * @param array $params
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * Return an array of javascript files to include into the page
     *
     * @return array the list of javascript files to include
     */
    protected function getJavaScriptFileNames()
    {
        return array(
            '../resources/js/outsourcepaymentssetup.js',
            '../resources/js/outsourcedprintchecks.js',
            '../resources/js/common_helper.js',
        );
    }


    /**
     * I18N. Collection of tokens used primarily in JavaScript.
     *
     * @return string[]
     */
    protected function getFormTokens() : array
    {
        $this->textTokens[] = 'IA.EMAIL_TO_NOTIFY_CANNOT_BE_EMPTY';
        $this->textTokens[] = 'IA.PLEASE_SELECT_AT_LEAST_ONE_TYPE_OF_SUPPLER_MATCH_RUN_OPTIONS';
        $this->textTokens[] = 'IA.AMEX_AJAX_FAILURE_MSG';
        return parent::getFormTokens();
    }
    /**
     * @return array
     */
    protected function getCssFileNames()
    {
        $cssfiles[] = "../resources/css/amexsuppliermatch.css";
        return $cssfiles;
    }

    /**
     * Get the editor button list
     *
     * @param string $state the editor state
     *
     * @return array the buttons list
     */
    public function getStandardButtons($state)
    {
        return array();
    }

    /**
     * Map the ajax action to a PHP function
     *
     * @param string $cmd the Ajax action requested
     *
     * @return bool
     */
    protected function runAjax($cmd)
    {
        switch ( $cmd ) {
            case 'callSupplierMatch':
                $this->ajaxRunSupplierMatch();
                return true;
            case 'acceptTnC':
                $this->ajaxSupplierMatchTnC();
                return true;
            default:
                return parent::runAjax($cmd);
        }
    }

    protected function ajaxRunSupplierMatch()
    {
        $response = null;
        $notificationEmail = Request::$r->{'notifyMailId'};
        $suppMatchOption = Request::$r->{'suppMatchOption'};
        $selectedVendors = Request::$r->{'selectedVendors'};

        //We should not persist the notification email here, unless we are sure that all the conditions are met to run supplier match

        //Create an IMS job to run the supplier match
        /** @var array $selectedVendors */
        $suppMatchIMSUtil = new AMEXVendorMatchUtil($notificationEmail, $suppMatchOption, $selectedVendors);
        $suppMatchIMSUtil->addRunSupplierMatchIMSJob($response);

        echo json_encode($response);
    }

    protected function ajaxSupplierMatchTnC()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $outsourcePaymentsSetupMgr = $gManagerFactory->getManager('outsourcepaymentssetup');
        $ok = $outsourcePaymentsSetupMgr->SetPreference(AMEXVendorMatchRESTv1::VM_AGREEMENT_ACCEPT_PREFERENCE, 'T');
        $response = array('SUCCESS' => $ok);
        echo json_encode($response);
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    public function mediateDataAndMetadata(&$obj)
    {
        $view = $this->getView();
        $gManagerFactory = Globals::$g->gManagerFactory;

        $outsourcePaymentsSetupMgr = $gManagerFactory->getManager('outsourcepaymentssetup');
        $agreementAccepted =  $outsourcePaymentsSetupMgr->GetPreference(AMEXVendorMatchRESTv1::VM_AGREEMENT_ACCEPT_PREFERENCE);
        $obj['SUPPLIERMATCHAGREETERM'] = $agreementAccepted;

        $view->findAndSetProperty(array('id' => 'RunSupplierMatchSection'), array('hidden' => true), EditorComponentFactory::TYPE_SECTION);
        $view->findAndSetProperty(array('id' => 'VendorMatchIntroSection'), array('hidden' => true), EditorComponentFactory::TYPE_SECTION);
        $view->findAndSetProperty(array('id' => 'SupplierMatchOptions'), array('hidden' => true), EditorComponentFactory::TYPE_SECTION);
        $view->findAndSetProperty(array('id' => 'TncSupplierMatchSection'), array('hidden' => true), EditorComponentFactory::TYPE_SECTION);
        $view->findAndSetProperty(array('id' => 'SupplierMatchInprogress'), array('hidden' => true), EditorComponentFactory::TYPE_SECTION);
        //Check whether the Agreement has already been accepted, on the basis of this hide and unhide corresponsidng sections
        if($agreementAccepted == 'T'){
            $view->findAndSetProperty(array('id' => 'RunSupplierMatchSection'), array('hidden' => false), EditorComponentFactory::TYPE_SECTION);
            //Get supplier match run count, if RUN COUNT is greater than 1 then enable the different bucket options
            //for supplier match run
            $runCount =  $outsourcePaymentsSetupMgr->GetPreference(AMEXVendorMatchRESTv1::VM_SUPPLIER_MATCH_RUN);
            if($runCount > 0){
                $view->findAndSetProperty(array('id' => 'VendorMatchIntroSection'), array('hidden' => false), EditorComponentFactory::TYPE_SECTION);
                $view->findAndSetProperty(array('id' => 'VendorMatchInitialIntroSection'), array('hidden' => true), EditorComponentFactory::TYPE_SECTION);
                $view->findAndSetProperty(array('id' => 'SupplierMatchOptions'), array('hidden' => false), EditorComponentFactory::TYPE_SECTION);
                if(AMEXVendorMatchUtil::isSupplierMatchRunning(GetMyCompanyTitle())){
                    $view->findAndSetProperty(array('id' => 'SupplierMatchInprogress'), array('hidden' => false), EditorComponentFactory::TYPE_SECTION);
                }else{
                    $view->findAndSetProperty(array('id' => 'SupplierMatchInprogress'), array('hidden' => true), EditorComponentFactory::TYPE_SECTION);
                }
            }
        }else{
            $view->findAndSetProperty(array('id' => 'TncSupplierMatchSection'), array('hidden' => false), EditorComponentFactory::TYPE_SECTION);
        }
        $obj['EMAILID'] = $this->getNotificationEmail();

        return true;
    }

    /**
     * @return bool|null|string
     */
    private function getNotificationEmail()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $opsMgr = $gManagerFactory->getManager('outsourcepaymentssetup');
        $notifyEmail =  $opsMgr->GetPreference(AMEXVendorMatchRESTv1::VM_NOTIFY_EMAIL_PREFERENCE);
        if(!isset($notifyEmail) || $notifyEmail == '' || $notifyEmail == false){
            $profile = Profile::getUserCache();
            $notifyEmail = $profile->getProperty('contact', 'EMAIL1');
        }
        return $notifyEmail;
    }
}
