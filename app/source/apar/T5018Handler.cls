<?php

require_once 'backend_megl.inc';

/**
 * Class T5018Handler
 */
class T5018Handler
{

    /**
     * @param string|null $from
     * @param string|null $to
     * @param bool $selectAll
     * @param array $vendGrpRecs
     * @return array
     */
    public function getVendorDetails(string $from = null, string $to = null,
                                     bool   $selectAll = false, array $vendGrpRecs): array
    {
        $start = microtime(true);
        $stmt = [];
        $stmt[0] = "select ";
        $stmt[1] = GetMyCompany();
        $stmt[2] = 'T';
        $stmt[0] .= self::getVendorSelectFields();

        $stmt[0] .= " from vendor vendor, contactmst displaycontact , contactmst primarycontact, 
        mailaddress mailaddress";

        $stmt[0] .= " where vendor.cny# = :1 and vendor.ist5018enabled = :2
        and displaycontact.cny# = vendor.cny# 
        and displaycontact.record# = vendor.displaycontactkey
        and primarycontact.cny# (+)= vendor.cny# 
        and primarycontact.record# (+)= nvl(vendor.contactkey, vendor. displaycontactkey)
        and mailaddress.cny# (+)= primarycontact.cny# 
        and mailaddress.record# (+)= primarycontact.MAILADDRKEY ";

        $bindCount = 3;
        if ($selectAll) {
            $vendorIDfilter = " ";
        } else if ((!empty($from) || !empty($to))) {

            if ($from != '' && $to == '') {
                $vendorIDfilter = " and vendor.vendorid >= :" . $bindCount . " ";
                $stmt[] = $from;
            } elseif ($from != '' && $to != '') {
                $vendorIDfilter = " and vendor.vendorid >= :" . $bindCount . " and vendor.vendorid <= :" . ++$bindCount . " ";
                $stmt[] = $from;
                $stmt[] = $to;
            } elseif ($to != '') {
                $vendorIDfilter = " and vendor.vendorid = :" . $bindCount . " ";
                $stmt[] = $to;
            }
        } else if (!isArrayEmpty($vendGrpRecs)) {
            $stmt = PrepINClauseStmt($stmt, $vendGrpRecs, ' and vendor.vendorid', true, 'vendor');
        }
        $stmt[0] .= $vendorIDfilter;

        $res = QueryResult($stmt);
        $vendorDetails = [];
        if (!empty($res)) {
            foreach ($res as $detail) {
                $vendorDetails[$detail['ENTITY']] = $detail;
            }
        }

        $timeTaken = microtime(true) - $start;
        LogToFile("T5018Handler::getVendorDetails API:::: $timeTaken\n");

        return $vendorDetails;
    }

    /**
     * @return string
     */
    private static function getVendorSelectFields() : string
    {
        return " vendor.record# recordno, vendor.vendorid vendorid, vendor.name vendorname, vendor.t5018number vendor_t5018number, vendor.entity , displaycontact.lastname vendor_lastname, displaycontact.firstname vendor_firstname, displaycontact.printas vendor_printas, mailaddress.addr1 vendor_address1, mailaddress.addr2 vendor_address2, mailaddress.city vendor_city, 
        mailaddress.state vendor_state, mailaddress.zip vendor_postcode, mailaddress.country vendor_country";
    }

    /**
     * @param array $entityKeyList
     * @return array $subLocations
     */
    public function getSublocations(array $entityKeyList): array
    {
        $subLocations = [];
        if (!empty($entityKeyList)) {
            $locQryStmt = array("SELECT DISTINCT le.location#, le.entity# FROM v_locationent le WHERE le.cny# = :1", GetMyCompany());
            $locQryStmt = PrepINClauseStmt($locQryStmt, $entityKeyList, " and le.entity# ");

            $locations = QueryResult($locQryStmt);
            if (!empty($locations)) {
                foreach ($locations as $loc) {
                    $subLocations[$loc['ENTITY#']][] = $loc['LOCATION#'];
                }
            }
        }
        return $subLocations;
    }

    /**
     * @param $filters
     * @return array
     */
    public function getT5018EntityContactInfo($params): array
    {

        if (IsMultiEntityCompany()) {
            list($locrec) = explode("--", $params['ORIG_LOCATION']);
            if (GetContextLocation() != '' || ($locrec != '' && !$params['ISGROUP'])) {
                $entity = GetContextLocation() ?: $locrec;
                $pickEntity = GetEntityVid($entity);
                $entityDetail = $this->getEntityContactInfo($pickEntity);
            } else if ($params['ISGROUP']) {
                $entityDetail = $this->getEntityContactInfo($params['ORIG_LOCATION'], true);
            } else {
                $entityDetail = $this->getEntityContactInfo();
            }
        } else {
            $entityDetail = $this->getEntityContactInfo();
        }

        return $entityDetail;
    }

    /**
     * @param array $objectEntityList
     * @param mixed $startDate
     * @param mixed $endDate
     * @param array $locationList
     * @param mixed $minimumAmount
     * @return array
     */
    public function getT5018Payments(array $objectEntityList, mixed $startDate, mixed $endDate, array $locationList, mixed $minimumAmount): array
    {
        $start = microtime(true);

        $query = "SELECT entity, SUM(paid_amount) AS paid_amount
              FROM 
              (
              SELECT SUM(prplink.amount) AS paid_amount,
                pymt.entity
                FROM 
                prentrymst line
                JOIN 
                prentrypymtrecs prplink ON line.cny# = prplink.cny# AND line.record# = prplink.paiditemkey
                JOIN 
                prrecordmst pymt ON pymt.cny# = prplink.cny# AND pymt.record# = prplink.paymentkey
                WHERE prplink.cny# = :1 AND prplink.state IN ('C', 'V')
                AND pymt.recordtype IN ('pp', 'po')
                AND line.istax <> 'T'";
        $stmt = [$query, GetMyCompany()];
        $bindCount = 1;
        if ($startDate) {
            $stmt[0] .= " AND prplink.paymentdate >= TO_DATE(:" . ++$bindCount . ", 'MM/DD/YYYY')";
            $stmt[] = $startDate;
        }
        if ($endDate) {
            $stmt[0] .= " AND prplink.paymentdate <= TO_DATE(:" . ++$bindCount . ", 'MM/DD/YYYY')";
            $stmt[] = $endDate;
        }
        $stmt = PrepINClauseStmt($stmt, $objectEntityList, ' AND pymt.entity', true, 'object');
        if (!isArrayEmpty($locationList)) {
            $stmt = PrepINClauseStmt($stmt, $locationList, ' AND line.location#', true, 'location');
        }
        $stmt[0] .= " GROUP BY pymt.entity";
        $stmt[0] .= ") 
                GROUP BY entity
                HAVING SUM(paid_amount) >= " . $minimumAmount;
        $result = QueryResult($stmt);

        $timeTaken = microtime(true) - $start;
        LogToFile("TIME TAKEN for T5018Handler::getT5018Payments API:::: $timeTaken\n");

        return !empty($result) ? $result : [];
    }

    /**
     * @param $entid
     * @param $isgroup
     * @return array
     */
    private function getEntityContactInfo($entid = '', $isgroup = false): array
    {

        $gManagerFactory = Globals::$g->gManagerFactory;

        $companyQry = " select title as companyid,name as companyname,contactname,contactphone,contactemail,address1,address2,address3,city,zipcode,state,country,taxid,countrycode 
				 from company where record# = :1";
        $companyInfo = QueryResult(array($companyQry, GetMyCompany()));
        $companyInfo = $companyInfo[0];
        $companyInfoProps = Profile::getCompanyCacheProperty('company');
        $companyInfo['COMPANYNAME'] = $companyInfoProps['LEGALNAME'];
        $companyInfo['ADDRESS1'] = $companyInfoProps['LEGALADDRESS1'];
        $companyInfo['ADDRESS2'] = $companyInfoProps['LEGALADDRESS2'];
        $companyInfo['ADDRESS3'] = $companyInfoProps['LEGALADDRESS3'];
        $companyInfo['CITY'] = $companyInfoProps['LEGALCITY'];
        $companyInfo['STATE'] = $companyInfoProps['LEGALSTATE'];
        $companyInfo['ZIPCODE'] = $companyInfoProps['LEGALZIPCODE'];
        $companyInfo['COUNTRY'] = $companyInfoProps['LEGALCOUNTRY'];
        $companyInfo['COUNTRYCODE'] = $companyInfoProps['LEGALCOUNTRY'];
        $companyInfo['TAXID'] = $companyInfoProps['TAXID'];
        $companyInfo['FIRSTMONTH'] = $companyInfoProps['FIRSTMONTH'];

        if (IsMultiEntityCompany()) {
            if ($isgroup) {
                /** @var LocationGroupManager $locgrpmgr */
                $locgrpmgr = $gManagerFactory->getManager('locationgroup');
                $contactmgr = $gManagerFactory->getManager('contact');
                $locgrp = $locgrpmgr->Get($entid);
                $contactname = $locgrp['CONTACTINFO']['CONTACTNAME'];
                if (isset($contactname) && $contactname != '') {
                    $contact = $contactmgr->get($contactname);
                    if ($contact) {
                        // print location group's contact PRINTAS as company name
                        $companyInfo['COMPANYNAME'] = $contact['PRINTAS'];
                        $companyInfo['CONTACTNAME'] = $contact['PRINTAS'];
                        $companyInfo['CONTACTPHONE'] = $contact['PHONE1'];
                        $companyInfo['CONTACTEMAIL'] = $contact['EMAIL1'];
                        $companyInfo['ADDRESS1'] = $contact['MAILADDRESS']['ADDRESS1'];
                        $companyInfo['ADDRESS2'] = $contact['MAILADDRESS']['ADDRESS2'];
                        $companyInfo['ADDRESS3'] = $contact['MAILADDRESS']['ADDRESS3'];
                        $companyInfo['CITY'] = $contact['MAILADDRESS']['CITY'];
                        $companyInfo['ZIPCODE'] = $contact['MAILADDRESS']['ZIP'];
                        $companyInfo['STATE'] = $contact['MAILADDRESS']['STATE'];
                        $companyInfo['COUNTRY'] = $contact['MAILADDRESS']['COUNTRY'];
                        $companyInfo['COUNTRYCODE'] = $contact['MAILADDRESS']['COUNTRYCODE'];
                        $companyInfo['TAXID'] = $contact['TAXID'];
                        $companyInfo['FIRSTMONTH'] = $contact['FIRSTMONTH'];
                    }
                }
            } else if ($entid != '') {
                /** @var LocationEntityManager $entlocmgr */
                $entlocmgr = $gManagerFactory->getManager('locationentity');
                $entity = $entlocmgr->Get($entid);
                $companyInfo['COMPANYNAME'] = ($entity['REPORTPRINTAS'] ?: $companyInfo['COMPANYNAME']);
                $contactname = $entity['CONTACTINFO']['CONTACTNAME'];
                if (isset($contactname) && $contactname != '') {
                    $contactmgr = $gManagerFactory->getManager('contact');
                    $contact = $contactmgr->get($contactname);
                    if ($contact) {
                        $companyInfo['CONTACTNAME'] = $contact['PRINTAS'];
                        $companyInfo['CONTACTPHONE'] = $contact['PHONE1'];

                        $companyInfo['CONTACTEMAIL'] = $contact['EMAIL1'];
                        $companyInfo['ADDRESS1'] = $contact['MAILADDRESS']['ADDRESS1'];
                        $companyInfo['ADDRESS2'] = $contact['MAILADDRESS']['ADDRESS2'];
                        $companyInfo['ADDRESS3'] = $contact['MAILADDRESS']['ADDRESS3'];
                        $companyInfo['CITY'] = $contact['MAILADDRESS']['CITY'];
                        $companyInfo['ZIPCODE'] = $contact['MAILADDRESS']['ZIP'];
                        $companyInfo['STATE'] = $contact['MAILADDRESS']['STATE'];
                        $companyInfo['COUNTRY'] = $contact['MAILADDRESS']['COUNTRY'];
                        $companyInfo['COUNTRYCODE'] = $contact['MAILADDRESS']['COUNTRYCODE'];
                        $companyInfo['TAXID'] = $contact['TAXID'];
                    }
                    $companyInfo['FIRSTMONTH'] = $entity['FIRSTMONTH'];
                }
            }
        }

        return $companyInfo;
    }
}