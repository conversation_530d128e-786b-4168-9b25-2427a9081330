<?xml version="1.0" encoding="UTF-8"?>
<!--  This file contains auto generated contents. -->
<!--  The file contents are generated using arpostedadvance_layout_edit.xml, location: Editor.cls::Editor_Deliver() -->
<!--  Final xsl file with generated contents is kept in ../../private/xmlinc/ directory which cannot be touched. -->
<!--  For pdf printing requirements, we need to modify the contents of this file, so we have copied the contents of -->
<!--  the automatically generated xsl file and pasted here. Now we can modify it. -->
<!--  Otherwise, we have to modify arpostedadvance_layout_edit.xml, which -->
<!--  will result in modification of ARPostedAdvanceEditor UI. -->
<!--  To avert that, we have created this file. -->
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <xsl:template match="/">
        <xsl:apply-templates select="ROOT"/>
    </xsl:template>
    <xsl:template match="ROOT">
        <fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:svg="http://www.w3.org/2000/svg">
            <!--  master template -->
            <fo:layout-master-set>
                <!-- <fo:simple-page-master margin-right="0.60in" margin-left="0.60in" margin-bottom="0in" margin-top="0.5in" page-width="8.5in" page-height="11.0in" master-name="first"> -->
                <fo:simple-page-master
                        margin-right="0.60in"
                        margin-left="0.00in"
                        margin-bottom="0in"
                        margin-top="0.5in"
                        page-width="8.5in"
                        page-height="11.0in"
                        master-name="first">
                    <!-- <fo:region-body overflow="auto" margin-top="0.20in" margin-bottom="0.60in"/> -->
                    <fo:region-body overflow="visible" margin-top="0.20in" margin-bottom="0.60in"/>
                    <fo:region-before extent="1.00in"/>
                    <fo:region-after extent="0.60in"/>
                </fo:simple-page-master>
            </fo:layout-master-set>
            <!--  page printing -->
            <fo:page-sequence master-reference="first">
                <fo:static-content flow-name="xsl-region-before">
                    <!-- static page content -->
                    <!-- Letterhead -->
                    <!-- company logo -->
                    <xsl:if test="COMPANY/LOGO and COMPANY/LOGO != ''">
                        <fo:block-container height="0.6in" width="1.0in" top="0in" left="0.80in" position="absolute">
                            <fo:block>
                                <fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
                    <!-- company fields -->
                    <fo:block-container height="0.7in" width="2.5in" top="0.55in" left="0.72in" position="absolute">
                        <fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
                            <xsl:value-of select="COMPANY/TITLE"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
                            <xsl:value-of select="COMPANY/ADDRESS1"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
                            <xsl:value-of select="COMPANY/ADDRESS2"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
                            <xsl:value-of select="COMPANY/CITY"/>
                            <xsl:text>, </xsl:text>
                            <xsl:value-of select="COMPANY/STATE"/>
                            <xsl:text> </xsl:text>
                            <xsl:value-of select="COMPANY/ZIPCODE"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
                            <xsl:value-of select="COMPANY/CONTACTPHONE"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- Title -->
                    <!-- Title background -->
                    <fo:block-container height="0.61in" width="2.6in" top="0in" left="4.60in" position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="2.6in" height="0.61in">
                                    <svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- Title Text -->
                    <fo:block-container height="0.61in" width="2.3in" top=".08in" left="5.0in" position="absolute">
                        <fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
                            Posted Advance						</fo:block>
                    </fo:block-container>
                    <!-- Up-right static -->
                    <!-- Vertical Line under title -->
                    <fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.95in" position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.001in" height="0.6in">
                                    <svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- Text at the right of the vertical line -->
                    <fo:block-container height="0.6in" width="0.5in" top="0.60in" left="6.05in" position="absolute">
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
                            <xsl:value-of select="REC/RECORDNO"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- Text at the left of the vertical line -->
                    <fo:block-container height="0.6in" width="0.5in" top="0.6in" left="5.35in" position="absolute">
                        <fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
                            Arpostedadvance						</fo:block>
                        <fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
                            <xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
                                (<xsl:value-of select="REC/STATUS"/>)
                            </xsl:if>
                        </fo:block>
                    </fo:block-container>
                </fo:static-content>
                <fo:static-content flow-name="xsl-region-after">
                    <!-- footer - page number -->
                    <fo:block-container height="0.2in" width="7.7in" top="0.01in" left=".4in" position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <fo:block-container height="0.5in" width="7.7in" top="0.20in" left=".4in" position="absolute">
                        <fo:table height="0.25in" width="7.7in">
                            <fo:table-column column-width="3.85in"/>
                            <fo:table-column column-width="3.85in"/>
                            <fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
                                <fo:table-row line-height="11pt">
                                    <fo:table-cell>
                                        <fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
                                            Advance information										</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">PAGE <fo:page-number/>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block-container>
                </fo:static-content>
                <fo:flow flow-name="xsl-region-body">
                    <!-- dynamic page content -->
                    <!-- Fields 1 -->
                    <fo:block-container width="6.0in" height="3.4in" top="1.82in" left="0.72in" position="absolute">
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:value-of select="RENAMETERM/Term_Customer"/><xsl:text> ID</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/CUSTOMERID"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:value-of select="RENAMETERM/Term_Customer"/><xsl:text> name</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/CUSTOMERNAME"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Payment method</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/PAYMENTTYPE"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Payment summary</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/BATCHTITLE"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Financial account</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/FINANCIALACCOUNT"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Payment date</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/PAYMENTDATE"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Document/check no</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/DOCUMENTNUMBER"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Currency</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/CURRENCY"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Exchange rate date</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/EXCH_RATE_DATE"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Exchange rate type</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/EXCH_RATE_TYPE_ID"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Exchange rate</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/EXCHANGE_RATE"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Payment amount</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/PAYMENTAMOUNT"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Txn payment amount</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/PAYMENTTRXAMOUNT"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Reconciliation status</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/CLEARED"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Reconciliation date</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/CLRDATE"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:text>Record number</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/RECORDNO"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- Multiline 1 -->
                    <!-- column headers -->
                    <!-- column header 2 -->
                    <!-- column header round box 2 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.771in"
                            top="4.9211111111111in"
                            left="0.5in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.771in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.611in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.691in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 2 -->
                    <fo:block-container
                            height="0.2in"
                            width="1.111in"
                            top="4.9511111111111in"
                            left="0.58in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            GL account#
                        </fo:block>
                    </fo:block-container>
                    <!-- column header horizontal line 2 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.30525in"
                            top="4.8811111111111in"
                            left="1.271in"
                            position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header 3 -->
                    <!-- column header round box 3 -->
                    <fo:block-container
                            height="0.2in"
                            width="1.049in"
                            top="4.9211111111111in"
                            left="1.57625in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="1.049in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.889in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.969in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 3 -->
                    <fo:block-container
                            height="0.2in"
                            width="1.389in"
                            top="4.9511111111111in"
                            left="1.65625in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            GL account title
                        </fo:block>
                    </fo:block-container>
                    <!-- column header horizontal line 3 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.99in"
                            top="4.8811111111111in"
                            left="2.62525in"
                            position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header 4 -->
                    <!-- column header round box 4 -->
                    <xsl:if test="REC/PRENTRY/TRX_AMOUNT and REC/PRENTRY/TRX_AMOUNT != ''">
                        <fo:block-container
                                height="0.2in"
                                width="0.771in"
                                top="4.9211111111111in"
                                left="2.7in"
                                position="absolute">
                            <fo:block>
                                <fo:instream-foreign-object>
                                    <svg:svg width="0.8in" height="0.2in">
                                        <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                        <svg:rect x="0.08in" y="0in" width="0.611in" height="0.16in" style="fill: #BBBBBB"/>
                                        <svg:circle cx="0.691in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    </svg:svg>
                                </fo:instream-foreign-object>
                            </fo:block>
                        </fo:block-container>
                        <!-- column header label 4 -->
                        <fo:block-container
                                height="0.2in"
                                width="0.833in"
                                top="4.9511111111111in"
                                left="2.8in"
                                position="absolute">
                            <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                                Trx amount
                            </fo:block>
                        </fo:block-container>
                        <!-- column header horizontal line 4-->
                        <fo:block-container
                                height="0.2in"
                                width="0.4in"
                                top="4.8811111111111in"
                                left="3.993in"
                                position="absolute">
                            <fo:block>
                                <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
                    <!-- column header 5 -->
                    <!-- column header round box 5 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.493in"
                            top="4.9211111111111in"
                            left="3.6in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.493in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.333in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.413in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 5 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.833in"
                            top="4.9511111111111in"
                            left="3.7in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            Amount
                        </fo:block>
                    </fo:block-container>
                    <!-- column header horizontal line 5 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.23575in"
                            top="4.8811111111111in"
                            left="4.6in"
                            position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header 6 -->
                    <!-- column header round box 6 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.382in"
                            top="4.9211111111111in"
                            left="4.22875in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.382in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.222in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.302in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 6 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.722in"
                            top="4.9511111111111in"
                            left="4.30875in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            Memo
                        </fo:block>
                    </fo:block-container>
                    <!-- column header horizontal line 6 -->
                    <fo:block-container
                            height="0.2in"
                            width="1.208in"
                            top="4.8811111111111in"
                            left="4.61075in"
                            position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header 7 -->
                    <!-- column header round box 7 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.716in"
                            top="4.9211111111111in"
                            left="5.81875in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.716in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.556in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.636in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 7 -->
                    <fo:block-container
                            height="0.2in"
                            width="1.056in"
                            top="4.9511111111111in"
                            left="5.89875in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            Department
                        </fo:block>
                    </fo:block-container>
                    <!-- column header horizontal line 7 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.2915in"
                            top="4.8811111111111in"
                            left="6.53475in"
                            position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header 8 -->
                    <!-- column header round box 8 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.604in"
                            top="4.9211111111111in"
                            left="6.82625in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.604in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.444in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.524in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 8 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.944in"
                            top="4.9511111111111in"
                            left="6.90625in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            Location
                        </fo:block>
                    </fo:block-container>
                    <!-- code to drive printing of rows -->
                    <fo:block space-before.optimum="5.2011111111111in">
                        <fo:table>
                            <fo:table-column column-width="0.5in"/>
                            <fo:table-column column-width="1.07625in"/>
                            <fo:table-column column-width="1.42375in"/>
                            <fo:table-column column-width="0.5in"/>
                            <fo:table-column column-width="0.72875in"/>
                            <fo:table-column column-width="1.59in"/>
                            <fo:table-column column-width="1.0075in"/>
                            <fo:table-column column-width="0.8675in"/>
                            <fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
                                <xsl:apply-templates select="REC/PRENTRY"/>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:flow>
            </fo:page-sequence>
            <!--  page printing -->
            <fo:page-sequence master-reference="first">
                <fo:static-content flow-name="xsl-region-before">
                    <!-- static page content -->
                    <!-- Letterhead -->
                    <!-- company logo -->
                    <xsl:if test="COMPANY/LOGO and COMPANY/LOGO != ''">
                        <fo:block-container height="0.6in" width="1.0in" top="0in" left="0.80in" position="absolute">
                            <fo:block>
                                <fo:external-graphic max-height="0.6in" max-width="1.0in" src="{COMPANY/LOGO}"/>
                            </fo:block>
                        </fo:block-container>
                    </xsl:if>
                    <!-- company fields -->
                    <!-- Title -->
                    <!-- Title background -->
                    <fo:block-container height="0.61in" width="2.6in" top="0in" left="4.60in" position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="2.6in" height="0.61in">
                                    <svg:rect x="0.15in" y="0in" width="2.3in" height="0.3in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.15in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="2.45in" cy="0.15in" r="0.15in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- Title Text -->
                    <fo:block-container height="0.61in" width="2.3in" top=".08in" left="5.0in" position="absolute">
                        <fo:block text-align="start" color="#ffffff" line-height="15pt" font-family="Times Roman" font-size="13pt" font-weight="bold">
                            Posted Advance						</fo:block>
                    </fo:block-container>
                    <!-- Up-right static -->
                    <!-- Vertical Line under title -->
                    <fo:block-container height="0.6in" width="0.001in" top="0.3in" left="5.95in" position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.001in" height="0.6in">
                                    <svg:rect x="0in" y="0in" width="0.0001in" height="0.6in" style="fill: #FFFFFF; stroke:black"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- Text at the right of the vertical line -->
                    <fo:block-container height="0.6in" width="0.5in" top="0.60in" left="6.05in" position="absolute">
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="12pt">
                            <xsl:value-of select="REC/RECORDNO"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- Text at the left of the vertical line -->
                    <fo:block-container height="0.6in" width="0.5in" top="0.6in" left="5.35in" position="absolute">
                        <fo:block text-align="end" line-height="11pt" font-family="Helvetica" font-size="8pt">
                            Arpostedadvance						</fo:block>
                        <fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="7pt">
                            <xsl:if test="REC/STATUS and (REC/STATUS != 'Active')">
                                (<xsl:value-of select="REC/STATUS"/>)
                            </xsl:if>
                        </fo:block>
                    </fo:block-container>
                </fo:static-content>
                <fo:static-content flow-name="xsl-region-after">
                    <!-- footer - page number -->
                    <fo:block-container height="0.2in" width="7.7in" top="0.01in" left=".4in" position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <fo:block-container height="0.5in" width="7.7in" top="0.20in" left=".4in" position="absolute">
                        <fo:table height="0.25in" width="7.7in">
                            <fo:table-column column-width="3.85in"/>
                            <fo:table-column column-width="3.85in"/>
                            <fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
                                <fo:table-row line-height="11pt">
                                    <fo:table-cell>
                                        <fo:block border-width="1.0pt" border-color="white" text-align="start" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">
                                            Invoices										</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block border-width="1.0pt" border-color="white" text-align="end" line-height="11pt" font-family="Helvetica" font-weight="bold" font-size="8pt">PAGE <fo:page-number/>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block-container>
                </fo:static-content>
                <fo:flow flow-name="xsl-region-body">
                    <!-- dynamic page content -->
                    <!-- Fields 1 -->
                    <fo:block-container width="6.0in" height="2in" top="1.82in" left="0.72in" position="absolute">
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:value-of select="RENAMETERM/Term_Customer"/><xsl:text> ID</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/CUSTOMERID"/>
                        </fo:block>
                        <fo:block text-align="start" line-height="14pt" font-family="Helvetica" font-size="10pt">
                            <xsl:value-of select="RENAMETERM/Term_Customer"/><xsl:text> name</xsl:text><xsl:text>: </xsl:text>
                            <xsl:value-of select="REC/CUSTOMERNAME"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- Multiline 1 -->
                    <!-- column headers -->
                    <!-- column header 2 -->
                    <!-- column header round box 2 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.66in"
                            top="2.1988888888889in"
                            left="0.46674445740957in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.66in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.5in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.58in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 2 -->
                    <fo:block-container
                            height="0.2in"
                            width="1in"
                            top="2.2288888888889in"
                            left="0.54674445740957in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            Invoice #
                        </fo:block>
                    </fo:block-container>
                    <!-- column header horizontal line 2 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.31199533255543in"
                            top="2.1588888888889in"
                            left="1.1267444574096in"
                            position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header 3 -->
                    <!-- column header round box 3 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.771in"
                            top="2.1988888888889in"
                            left="1.438739789965in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.771in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.611in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.691in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 3 -->
                    <fo:block-container
                            height="0.2in"
                            width="1.111in"
                            top="2.2288888888889in"
                            left="1.518739789965in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            Reference #
                        </fo:block>
                    </fo:block-container>
                    <!-- column header horizontal line 3 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.2336674445741in"
                            top="2.1588888888889in"
                            left="2.209739789965in"
                            position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header 4 -->
                    <!-- column header round box 4 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.382in"
                            top="2.1988888888889in"
                            left="2.4434072345391in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.382in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.222in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.302in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 4 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.722in"
                            top="2.2288888888889in"
                            left="2.5234072345391in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            Date
                        </fo:block>
                    </fo:block-container>
                    <!-- column header horizontal line 4 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.65152858809802in"
                            top="2.1588888888889in"
                            left="2.8254072345391in"
                            position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header 5 -->
                    <!-- column header round box 5 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.938in"
                            top="2.1988888888889in"
                            left="3.4769358226371in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.938in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.778in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.858in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 5 -->
                    <fo:block-container
                            height="0.2in"
                            width="1.278in"
                            top="2.2288888888889in"
                            left="3.5569358226371in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            Total invoiced
                        </fo:block>
                    </fo:block-container>
                    <!-- column header horizontal line 5 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.21514585764294in"
                            top="2.1588888888889in"
                            left="4.4149358226371in"
                            position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header 6 -->
                    <!-- column header round box 6 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.66in"
                            top="2.1988888888889in"
                            left="4.63008168028in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.66in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.5in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.58in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 6 -->
                    <fo:block-container
                            height="0.2in"
                            width="1in"
                            top="2.2288888888889in"
                            left="4.71008168028in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            Total due
                        </fo:block>
                    </fo:block-container>
                    <!-- column header horizontal line 6 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.27071061843641in"
                            top="2.1588888888889in"
                            left="5.29008168028in"
                            position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header 7 -->
                    <!-- column header round box 7 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.993in"
                            top="2.1988888888889in"
                            left="5.5607922987165in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.993in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.833in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.913in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 7 -->
                    <fo:block-container
                            height="0.2in"
                            width="1.333in"
                            top="2.2288888888889in"
                            left="5.6407922987165in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            Payment applied
                        </fo:block>
                    </fo:block-container>
                    <!-- column header horizontal line 7 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.2107747957993in"
                            top="2.1588888888889in"
                            left="6.5537922987165in"
                            position="absolute">
                        <fo:block>
                            <fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header 8 -->
                    <!-- column header round box 8 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.438in"
                            top="2.1988888888889in"
                            left="6.7645670945158in"
                            position="absolute">
                        <fo:block>
                            <fo:instream-foreign-object>
                                <svg:svg width="0.438in" height="0.2in">
                                    <svg:circle cx="0.08in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                    <svg:rect x="0.08in" y="0in" width="0.278in" height="0.16in" style="fill: #BBBBBB"/>
                                    <svg:circle cx="0.358in" cy="0.08in" r="0.08in" style="fill: #BBBBBB"/>
                                </svg:svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:block-container>
                    <!-- column header label 8 -->
                    <fo:block-container
                            height="0.2in"
                            width="0.778in"
                            top="2.2288888888889in"
                            left="6.8445670945158in"
                            position="absolute">
                        <fo:block text-align="start" color="#afffff" line-height="11pt" font-family="Times Roman" font-size="8pt" font-weight="bold">
                            Total
                        </fo:block>
                    </fo:block-container>
                    <!-- code to drive printing of rows -->
                    <fo:block space-before.optimum="2.4788888888889in">
                        <fo:table>
                            <fo:table-column column-width="0.46674445740957in"/>
                            <fo:table-column column-width="0.97199533255543in"/>
                            <fo:table-column column-width="1.0046674445741in"/>
                            <fo:table-column column-width="0.97199533255543in"/>
                            <fo:table-column column-width="1.1995332555426in"/>
                            <fo:table-column column-width="0.87514585764294in"/>
                            <fo:table-column column-width="1.2637106184364in"/>
                            <fo:table-column column-width="0.6487747957993in"/>
                            <fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
                                <xsl:apply-templates select="REC/INVOICES"/>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:flow>
            </fo:page-sequence>
        </fo:root>
    </xsl:template>
    <!-- code for printing one row -->
    <xsl:template match="REC/PRENTRY">
        <fo:table-row line-height="10pt">
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="number(position())"/>.
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="ACCOUNTNO"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="ACCOUNTTITLE"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="TRX_AMOUNT"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="AMOUNT"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="ENTRYDESCRIPTION"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="DEPARTMENTID"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="LOCATIONID"/>
                </fo:block>
            </fo:table-cell>
        </fo:table-row>
    </xsl:template>
    <!-- code for printing one row -->
    <xsl:template match="REC/INVOICES">
        <fo:table-row line-height="10pt">
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="number(position())"/>.
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="DRILLDOWN"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="DOCNUMBER"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="WHENCREATED"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="TOTALENTERED"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="TOTALDUE"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="PAYMENTAMOUNT"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="APPLIEDAMOUNT"/>
                </fo:block>
            </fo:table-cell>
        </fo:table-row>
    </xsl:template>
</xsl:stylesheet>
