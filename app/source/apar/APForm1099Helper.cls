<?
/**
 *    FILE: APForm1099Helper.cls
 *    AUTHOR: Nithin C
 *    DESCRIPTION:
 *
 *    (C) 2022, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

class APForm1099Helper extends Form1099Helper
{
    /**
     * @param string $filters
     *
     * @return string
     */
    protected function getContactListQuery($filters)
    {
        return $this->getVendorContactSelectList() . " 
                    FROM vendor vendor,
                          contact displaycontact ,
                          mailaddress displaymailaddress ,
                          contact paytocontact ,
                          mailaddress paytomailaddress,
                          contact contactto1099 ,
                          mailaddress cnt1099mailaddress
                    WHERE vendor.cny#               = :1
                        AND vendor.displaycontactkey    = displaycontact.record# (+)
                        AND vendor.paytokey             = paytocontact.record# (+)
                        AND vendor.contactkey1099       = contactto1099.record# (+)
                        AND displaycontact.cny# (+)     = :1
                        AND displaymailaddress.cny# (+) = :1
                        AND displaycontact.mailaddrkey  = displaymailaddress.record# (+)
                        AND paytocontact.cny# (+)       = :1
                        AND paytomailaddress.cny# (+)   = :1
                        AND paytocontact.mailaddrkey    = paytomailaddress.record# (+)
                        AND contactto1099.cny# (+)      = :1
                        AND cnt1099mailaddress.cny# (+) = :1
                        AND contactto1099.mailaddrkey   = cnt1099mailaddress.record# (+)
	                $filters";
    }


    /**
     * Description     : Get vendor select query for contact
     *
     * @return string
     */
    private function getVendorContactSelectList()
    {
        return "SELECT vendorid vendorid,
              vendor.name name,
              vendor.name1099 name1099,
              entity entity,
              vendor.taxid AS taxid,
              form1099type form1099type,
              form1099box form1099box,
              displaycontact.name displaycontactname,
              displaycontact.companyname displaycontactcompanyname,
              displaycontact.firstname displaycontactfirstname,
              displaycontact.lastname displaycontactlastname,
              displaycontact.mi displaycontactmiddlename,
              displaycontact.printas displaycontactprintas,
              displaycontact.taxable displaycontacttaxable,
              displaycontact.phone1 displaycontactphone1,
              displaycontact.phone2 displaycontactphone2,
              displaycontact.cellphone displaycontactcellphone,
              displaycontact.pager displaycontactpager,
              displaycontact.fax displaycontactfax,
              displaycontact.email1 displaycontactemail1,
              displaycontact.email2 displaycontactemail2,
              displaycontact.url1 displaycontacturl1,
              displaycontact.url2 displaycontacturl2,
              displaymailaddress.addr1 displaymailaddressaddr1,
              displaymailaddress.addr2 displaymailaddressaddr2,
              displaymailaddress.city displaymailaddresscity,
              displaymailaddress.state displaymailaddressstate,
              displaymailaddress.zip displaymailaddresszip,
              displaymailaddress.country displaymailaddresscountry,
              displaycontact.status displaycontactstatus,
              paytocontact.name paytocontactname,
              paytocontact.companyname paytocontactcompanyname,
              paytocontact.firstname paytocontactfirstname,
              paytocontact.lastname paytocontactlastname,
              paytocontact.mi paytocontactmiddlename,
              paytocontact.printas paytocontactprintas,
              paytocontact.taxable paytocontacttaxable,
              paytocontact.phone1 paytocontactphone1,
              paytocontact.phone2 paytocontactphone2,
              paytocontact.cellphone paytocontactcellphone,
              paytocontact.pager paytocontactpager,
              paytocontact.fax paytocontactfax,
              paytocontact.email1 paytocontactemail1,
              paytocontact.email2 paytocontactemail2,
              paytocontact.url1 paytocontacturl1,
              paytocontact.url2 paytocontacturl2,
              paytomailaddress.addr1 paytomailaddressaddr1,
              paytomailaddress.addr2 paytomailaddressaddr2,
              paytomailaddress.city paytomailaddresscity,
              paytomailaddress.state paytomailaddressstate,
              paytomailaddress.zip paytomailaddresszip,
              paytomailaddress.country paytomailaddresscountry,
              paytocontact.status paytocontactstatus,
              contactto1099.name contactto1099name,
              contactto1099.companyname contactto1099companyname,
              contactto1099.firstname contactto1099firstname,
              contactto1099.lastname contactto1099lastname,
              contactto1099.mi contactto1099middlename,
              contactto1099.printas contactto1099printas,
              contactto1099.taxable contactto1099taxable,
              contactto1099.phone1 contactto1099phone1,
              contactto1099.phone2 contactto1099phone2,
              contactto1099.cellphone contactto1099cellphone,
              contactto1099.pager contactto1099pager,
              contactto1099.fax contactto1099fax,
              contactto1099.email1 contactto1099email1,
              contactto1099.email2 contactto1099email2,
              contactto1099.url1 contactto1099url1,
              contactto1099.url2 contactto1099url2,
              cnt1099mailaddress.addr1 cnt1099mailaddressaddr1,
              cnt1099mailaddress.addr2 cnt1099mailaddressaddr2,
              cnt1099mailaddress.city cnt1099mailaddresscity,
              cnt1099mailaddress.state cnt1099mailaddressstate,
              cnt1099mailaddress.zip cnt1099mailaddresszip,
              cnt1099mailaddress.country cnt1099mailaddresscountry,
              contactto1099.status contactto1099status,
              'vendor' as entitytype,
              vendor.isindividual isindividual";
    }

    /**
     * @param array $params
     * @param int $bindingCount
     * @param array $args
     * @return string
     */
    protected function getContactListFilters(&$params, &$bindingCount, &$args)
    {
        $filters = '';
        $params['SHOWVENDORDATA'] = false;
        if ((($params['ALLVENDORS'] == 'true'))
            || (($params['FROMVENDOR'] != "") || ($params['TOVENDOR'] != ""))) {

            if ($params['INCLUDEALL1099BILLS'] == 'false') {
                $filters = " and  vendor.form1099type is not null  ";
            }

            if ($params['ALLVENDORS'] == 'true') {
                $params['SHOWVENDORDATA'] = true;
            } else {
                $params['SHOWVENDORDATA'] = true;
                $startVendor = explode("--", $params['FROMVENDOR']);
                $startVendor = $startVendor[0];
                $endVendor = explode("--", $params['TOVENDOR']);
                $endVendor = $endVendor[0];

                if ($startVendor != '' && $endVendor == '') {
                    $filters .= " and vendor.vendorid >= :" . $bindingCount++ . " ";
                    $args[] = $startVendor;
                } elseif ($startVendor != '' && $endVendor != '') {
                    $filters .= " and vendor.vendorid >= :" . $bindingCount++ . " and vendor.vendorid <= :" . $bindingCount++ . " ";
                    $args[] = $startVendor;
                    $args[] = $endVendor;
                } elseif ($endVendor != '') {
                    $filters .= " and vendor.vendorid = :" . $bindingCount++ . " ";
                    $args[] = $endVendor;
                }
            }
        }

        return $filters;
    }

    /**
     * @param int $formtypebindcnt
     * @param int $entity
     * @param string $locationQuery
     * @param string $vendorFilter
     * @param string $reportingYear
     * @param string $payMethodClause
     * @return string
     */
    protected function getPaymentEntriesQuery($formtypebindcnt, $entity, $locationQuery, $vendorFilter, $reportingYear,
                                              $payMethodClause)
    {
        return "select 
								invprrecord.entity entity, 
								sum(prentrypymtrecs.amount) as form1099amount, 	
								invprentry.form1099Type form1099type,
                                invprentry.form1099box form1099box
							from
								prentry invprentry ,
								prrecordmst invprrecord,
								prentry paymententry,
								prrecordmst paymentrecord,
								prentrypymtrecs prentrypymtrecs
							where 
								invprentry.cny# = :1 and 
								invprentry.form1099 like :$formtypebindcnt and
								invprentry.lineitem = 'T' and 
								invprentry.totalpaid > 0 and 
								invprentry.amount > 0 and " . ($locationQuery ? "invprentry.location# in ( $locationQuery ) and " : "") .
            "invprrecord.cny# = :1 and
								invprrecord.recordtype in ('pi', 'pa', 'cq') and
								invprentry.recordkey = invprrecord.record# and 
								nvl(invprrecord.state, 'A') != 'V' and
								exists (SELECT 1 FROM vendormst vendor WHERE vendor.cny# = :1 and vendor.entity = invprrecord.entity $vendorFilter) and  
								--
								prentrypymtrecs.cny# = :1 and      	
								prentrypymtrecs.recordkey = invprrecord.record# and
								prentrypymtrecs.paiditemkey = invprentry.record#  and
								prentrypymtrecs.paymentdate >= to_date('01/01/$reportingYear 00:00:00','mm/dd/yyyy hh24:mi:ss') and
								prentrypymtrecs.paymentdate <= to_date('12/31/$reportingYear 23:59:59','mm/dd/yyyy hh24:mi:ss') and
								prentrypymtrecs.state = 'C' and 
								--
								paymententry.cny# = :1 and
								paymententry.record# = prentrypymtrecs.payitemkey and
								paymententry.lineitem = 'T' and " . ($locationQuery ? "paymententry.location# in ( $locationQuery ) and " : "") .
            "paymentrecord.cny# = :1 and
								paymentrecord.record# = prentrypymtrecs.paymentkey and
								paymentrecord.recordtype in ('pp', 'po', 'ck', 'cw') and 
								paymentrecord.state = 'C'" . $payMethodClause . "
								GROUP BY invprrecord.entity, invprentry.form1099type, invprentry.form1099box";
    }
}


