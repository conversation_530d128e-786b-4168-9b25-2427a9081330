<?
/**
 * <AUTHOR> <PERSON><PERSON> <<EMAIL>>
 * @copyright Intacct 2019
 * @package   intacct
 */
$kSchemas['aprecurbillentry'] = [
    'children' => [
        'aprecurbill' => ['fkey' => 'recordkey', 'invfkey' => 'record#', 'table' => 'recurprrecordmst',],
        'offsetglaccountno' => [
            'fkey' => 'offset', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'glaccount',
        ],
        'department' => ['fkey' => 'dept#', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'departmentmst',],
        'location' => ['fkey' => 'location#', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'locationmst',],
        'glaccount' => ['fkey' => 'accountkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'glaccount',],
        'apaccountlabel' => [
            'fkey' => 'accountlabelkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'accountlabel',
        ],
        'alloc' => ['fkey' => 'allocationkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'allocation',],
        'detail' => [
            'fkey' => 'taxdetail#', 'invfkey' => 'record#', 'table' => 'taxdetailmst', 'join' => 'outer',
        ],
    ],
    // object
    'object' => [
        'RECORDNO',
        'RECORDKEY',
        'OFFSET',
        'ENTRYDESCRIPTION',
        'ACCOUNTKEY',
        'GLACCOUNTNO',
        'GLACCOUNTTITLE',
        'AMOUNT',
        'LOCATIONID',
        'LOCATIONNAME',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'LINE_NO',
        'FORM1099',
        'ACCOUNTLABEL',
        'CURRENCY',
        'BASECURR',
        'EXCHRATEDATE',
        'EXCHRATETYPE',
        'EXCHRATE',
        'TRX_AMOUNT',
        'ALLOCATIONKEY',
        'ALLOCATION',
        'BILLABLE',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'DETAILID',
        'ISTAX',
        'DETAILKEY',
        'TAXRATE',
        'PARENTENTRY'
    ],
    // schema
    'schema' => [
        'RECORDNO' => 'record#',
        'RECORDKEY' => 'recordkey',
        'ACCOUNTKEY' => 'accountkey',
        'GLACCOUNTNO' => 'glaccount.acct_no',
        'OFFSET' => 'offset',
        'OFFSETGLACCOUNTNO' => 'offsetglaccountno.acct_no',
        'OFFSETGLACCOUNTTITLE' => 'offsetglaccountno.title',
        'GLACCOUNTTITLE' => 'glaccount.title',
        'ACCOUNTLABELKEY' => 'accountlabelkey',
        'ACCOUNTLABEL' => 'apaccountlabel.label',
        'AMOUNT' => 'amount',
        'TRX_AMOUNT' => 'trx_amount',
        'DEPT#' => 'dept#',
        'DEPARTMENTID' => 'department.dept_no',
        'DEPARTMENTNAME' => 'department.title',
        'LOCATION#' => 'location#',
        'LOCATIONID' => 'location.location_no',
        'LOCATIONNAME' => 'location.name',
        'ENTRYDESCRIPTION' => 'description',
        'EXCHRATEDATE' => 'exch_rate_date',
        'EXCHRATETYPE' => 'exch_rate_type_id',
        'EXCHRATE' => 'exchange_rate',
        'ALLOCATIONKEY' => 'allocationkey',
        'ALLOCATION' => 'alloc.allocationid',
        'LINE_NO' => 'line_no',
        'CURRENCY' => 'currency',
        'BASECURR' => 'basecurr',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'FORM1099' => 'form1099',
        'BILLABLE' => 'billable',
        'DETAILKEY' => 'taxdetail#',
        'ISTAX' => 'istax',
        'DETAILID' => 'detail.detailid',
        'TAXRATE' => 'detail.value',
        'PARENTENTRY' => 'parententry',
    ],
    'sqldomarkup' => true,
    'sqlmarkupfields' => ['WHENCREATED', 'WHENMODIFIED'],
    // fieldinfo
    'fieldinfo' => [
        [
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ],
            'hidden' => true,
            'readonly' => true,
            'id' => 1,
        ],
        [
            'path' => 'ACCOUNTLABELKEY',
            'fullname' => 'IA.ACCOUNT_LABEL_KEY',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ],
            'derived' => true,
            'hidden' => true,
            'reclass' => true,
            'id' => 2,
        ],
        [
            'path' => 'GLACCOUNTNO', // GLACCOUNT
            'fullname' => 'IA.ACCOUNT',
            'type' => [
                'type' => 'text',
                'ptype' => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity' => 'glaccount',
            ],
            'reclass' => true,
            'behavesLikeDimension' => true,
            'standard' => true,
            'gridPath' => 'ITEMS',
            'id' => 3,
        ],
        [
            'path' => 'ENTRYDESCRIPTION',
            'fullname' => 'IA.MEMO',
            'type' => [
                'type' => 'text',
                'ptype' => 'textarea',
                'maxlength' => 1000,
            ],
            'partialedit' => true,
            'id' => 4,
        ],
        [
            'path' => 'AMOUNT',
            'fullname' => 'IA.BASE_AMOUNT',
            'type' => [
                'type' => 'currency',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18,
            ],
            'required' => true,
            'readonly' => true,
            'id' => 5,
        ],
        [
            'path' => 'DEPARTMENTID',
            'fullname' => 'IA.DEPARTMENT_ID',
            'type' => [
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'department',
                'pickentity' => 'departmentpick',
            ],
            'renameable' => true,
            'standard' => true,
            'isDimension' => true,
            'autofillrelated' => false,
            'reclass' => true,
            //we don't allow saving objects with no department if the root department is hidden
            'required' => hasHiddenDepartment(),
            'id' => 6,
        ],
        [
            'path' => 'LOCATIONID',
            'fullname' => 'IA.LOCATION_ID',
            'type' => [
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'location',
                'pickentity' => 'locationpick',
            ],
            'renameable' => true,
            'standard' => true,
            'isDimension' => true,
            'autofillrelated' => false,
            'reclass' => true,
            'id' => 7,
        ],
        [
            'path' => 'ALLOCATION',
            'fullname' => 'IA.ALLOCATION',
            'type' => [
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'allocation',
                'pickfield' => ['ALLOCATIONID', 'APPLYTO'],
                'maxlength' => 50,
            ],
            'noedit' => 'true',
            'noview' => 'true',
            'nonew' => 'true',
            'id' => 8,
        ],
        [
            'path' => 'LINE_NO',
            'fullname' => 'IA.LINE_NO',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 4,
                'format' => $gLineNoFormat,
            ],
            'readonly' => true,
            'derived' => true,
            'id' => 9,
        ],
        [
            'fullname' => 'IA.FORM_1099',
            'desc' => 'IA.FORM_1099',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 4,
                'size' => 4,
            ],
            'required' => false,
            'default' => false,
            'path' => 'FORM1099',
            'id' => 10,
        ],
        [
            'path' => 'TRX_AMOUNT',
            'fullname' => 'IA.TRANSACTION_AMOUNT',
            'type' => [
                'type' => 'currency',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18,
            ],
            'required' => true,
            'id' => 11,
        ],
        [
            'fullname' => 'IA.BILLABLE',
            'type' => $gBooleanType,
            'desc' => 'IA.BILLABLE',
            'path' => 'BILLABLE',
            'id' => 12,
        ],
        [
            'path' => 'ACCOUNTKEY',
            'fullname' => 'IA.ACCOUNT_KEY',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ],
            'derived' => true,
            'hidden' => true,
            'reclass' => true,
            'id' => 13,
        ],
        [
            'path' => 'OFFSET',
            'fullname' => 'IA.OFFSET_ACCOUNT_KEY',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ],
            'readonly' => true,
            'platform' => false,
            'id' => 14,
        ],
        [
            'path' => 'OFFSETGLACCOUNTNO',
            'fullname' => 'IA.AP_ACCOUNT',
            'type' => [
                'type' => 'text',
                'ptype' => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity' => 'glaccount',
                'restrict' => [
                    [
                        'value' => ['R'],
                        'pickField' => 'ALTERNATIVEACCOUNT',
                    ],
                ],
            ],
            'hidden' => true,
            'id' => 15,
        ],
        [
            'path' => 'OFFSETGLACCOUNTTITLE',
            'fullname' => 'IA.AP_ACCOUNT_TITLE',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 80,
            ],
            'id' => 16,
        ],
        [
            'path' => 'GLACCOUNTTITLE',
            'fullname' => 'IA.ACCOUNT_TITLE',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 80,
            ],
            'id' => 17,
        ],
        [
            'path' => 'ACCOUNTLABEL',
            'fullname' => 'IA.ACCOUNT_LABEL',
            'type' => [
                'type' => 'text',
                'ptype' => 'ptr',
                'noSplit' => true,
                'entity' => 'apaccountlabel',
                'pickfield' => ['ACCOUNTLABEL', 'GLACCOUNTNO', 'GLACCOUNTTITLE'],
                'restrict' => [
                    [
                        'pickField' => 'SUBTOTAL',
                        'value' => ['false'],
                        'nulls' => true,
                    ],
                ],
            ],
            'gridPath' => 'ITEMS',
            'reclass' => true,
            'id' => 18,
        ],
        [
            'path' => 'DEPT#',
            'fullname' => 'IA.DEPARTMENT_KEY',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ],
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'reclass' => true,
            'platform' => false,
            'id' => 19,
        ],
        [
            'path' => 'DEPARTMENTNAME',
            'fullname' => 'IA.DEPARTMENT_NAME',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 40,
            ],
            'id' => 20,
        ],
        [
            'path' => 'LOCATION#',
            'fullname' => 'IA.LOCATION_KEY',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ],
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'reclass' => true,
            'platform' => false,
            'id' => 21,
        ],
        [
            'path' => 'LOCATIONNAME',
            'fullname' => 'IA.LOCATION_NAME',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 40,
            ],
            'id' => 22,
        ],
        [
            'path' => 'EXCHRATEDATE',
            'fullname' => 'IA.EXCHANGE_RATE_DATE',
            'type' => [
                'type' => 'date',
                'ptype' => 'date',
                'format' => $gDateFormat,
            ],
            'id' => 23,
        ],
        [
            'path' => 'EXCHRATETYPE',
            'fullname' => 'IA.EXCHANGE_RATE_TYPE',
            'type' => [
                'type' => 'integer',
                'ptype' => 'ptr',
                'entity' => 'exchangeratetypesall',
            ],
            'id' => 24,
        ],
        [
            'path' => 'EXCHRATE',
            'fullname' => 'IA.EXCHANGE_RATE',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'decimal',
                'maxlength' => 12,
            ],
            'precision' => 12,
            'noformat' => true,
            'readonly' => true,
            'rpdMeasure' => false,
            'id' => 25,
        ],
        [
            'path' => 'ALLOCATIONKEY',
            'fullname' => 'IA.ALLOCATION_KEY',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ],
            'derived' => true,
            'hidden' => true,
            'id' => 26,
        ],
        [
            'path' => 'BASECURR',
            'fullname' => 'IA.BASE_CURRENCY',
            'type' => [
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies',
            ],
            'readonly' => true,
            'required' => true,
            'id' => 27,
        ],
        [
            'path' => 'CURRENCY',
            'fullname' => 'IA.TRANSACTION_CURRENCY',
            'type' => [
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies',
            ],
            'readonly' => true,
            'required' => true,
            'id' => 28,
        ],
        // Extra fields for Custom Allocation
        [
            'path' => 'SPAMOUNT',
            'fullname' => 'IA.BASE_AMOUNT',
            'type' => [
                'type' => 'currency',
                'ptype' => 'currency',
                'format' => $gCurrencyFormat,
                'maxlength' => 14,
            ],
            'hasTotal' => true,
            'id' => 29,
        ],
        [
            'path' => 'SPBASEAMOUNT',
            'fullname' => 'IA.BASE_AMOUNT',
            'type' => [
                'type' => 'currency',
                'ptype' => 'currency',
                'format' => $gCurrencyFormat,
                'maxlength' => 14,
            ],
            'hasTotal' => true,
            'id' => 30,
        ],
        [
            'path' => 'SPLOCATIONID',
            'fullname' => 'IA.LOCATION',
            'type' => [
                'type' => 'ptr',
                'ptype' => 'ptr',
                'pickentity' => 'locationpick',
                'entity' => 'location',
            ],
            'renameable' => true,
            'id' => 31,
        ],
        [
            'path' => 'SPDEPARTMENTID',
            'fullname' => 'IA.DEPARTMENT',
            'type' => [
                'type' => 'ptr',
                'ptype' => 'ptr',
                'pickentity' => 'departmentpick',
                'entity' => 'department',
            ],
            'renameable' => true,
            'id' => 32,
        ],
        [
            'path' => 'RECORDKEY',
            'fullname' => 'IA.PARENT_KEY',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ],
            'hidden' => true,
            'id' => 43,
        ],
        [
            'path' => 'ISTAX',
            'fullname' => 'IA.IS_TAX',
            'type' => $gBooleanType,
            'default' => 'false',
            'readonly' => true,
        ],
        [
            'fullname' => 'IA.TAX_DETAIL',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'potaxdetail',
                'pickfield' => [
                    'DETAILID', 'DESCRIPTION', 'VALUE', 'STATUS'
                ],
            ],
            'noedit' => true,
            'nonew' => true,
            'desc' => 'IA.TAX_DETAIL_UNIQUE_ID',
            'path' => 'DETAILID',
        ],
        [
            'path' => 'DETAILKEY',
            'fullname' => 'IA.TAX_DETAIL_KEY',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ],
            'derived' => true,
            'hidden' => true,
        ],
        [
            'path' => 'TRX_TAX',
            'fullname' => 'IA.TRANSACTION_TAX',
            'desc' => 'IA.TRANSACTION_TAX_AMOUNT',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'currency',
                'format' => $gDecimalFormat,
                'size' => 22,
                'maxlength' => 15,
            ],
            'hasTotal' => true,
            'id' => 44
        ],
        [
            'path' => 'TAX',
            'fullname' => 'IA.BASE_TAX',
            'desc' => 'IA.BASE_TAX_AMOUNT',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'currency',
                'format' => $gDecimalFormat,
                'size' => 22,
                'maxlength' => 15,
            ],
            'hasTotal' => true,
            'readonly' => true,
            'id' => 45
        ],
        [
            'fullname' => 'IA.RATE',
            'type' => [
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 12,
                'size' => 12,
            ],
            'precision' => 3,
            'desc' => 'IA.TAX_DETAIL_RATE_IN_PERCENT',
            'path' => 'TAXRATE',
        ],
        [
            'path' => 'MULTIPLETAXES',
            'fullname' => 'IA.MULTIPLE_TAXES_ON_LINE',
            'type' => $gBooleanType,
            'default' => 'false',
        ],
        [
            'path' => 'TOTALBASEAMOUNT',
            'fullname' => 'IA.BASE_TOTAL',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ],
            'readonly' => true,
            'id' => 46
        ],
        [
            'path' => 'TOTALTRXAMOUNT',
            'fullname' => 'IA.TRANSACTION_TOTAL',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ],
            'readonly' => true,
            'id' => 47
        ],
        [
            'path' => 'BASETAXAMOUNT',
            'fullname' => 'IA.BASE_TAX_AMOUNT',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ],
            'readonly' => true,
            'id' => 48
        ],
        [
            'path' => 'PARENTENTRY',
            'fullname' => 'IA.PARENT_ENTRY_KEY',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ],
            'hidden' => true,
            'readonly' => true,
            'id' => 49
        ],
        // End Extra fields for Custom Allocation
        $gStatusFieldInfo,
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ],
    'pairedFields' => [
        'GLACCOUNTNO' => 'GLACCOUNTTITLE',
        'DEPARTMENTID' => 'DEPARTMENTNAME',
        'LOCATIONID' => 'LOCATIONNAME',
        'OFFSETGLACCOUNTNO' => 'OFFSETGLACCOUNTTITLE',
    ],
    'dbsorts' => [
        ['LINE_NO'], ['RECORDNO'],
    ],
    'dbfilters' => [
        [
            'aprecurbill.recordtype', '=', SubLedgerTxnManager::BILL_RECTYPE,
        ],
        ['aprecurbillentry.istax', '!=', 'T' ],
    ],
    'printas' => 'IA.RECURRING_AP_BILL_DETAIL',
    'pluralprintas' => 'IA.RECURRING_AP_BILL_DETAILS',
    'table' => 'recurprentry',
    'parententity' => 'aprecurbill',
    'module' => 'ap',
    'vid' => 'RECORDNO',
    'hasdimensions' => true,
    'auditcolumns' => true,
    'autoincrement' => 'RECORDNO',
    'bulkoperation' => true, // insert multiple line item at once
    SearchTable::GSALLOWED => true,
    SearchTable::GSCOLUMNS =>
        ['RECORDID', 'AMOUNT', 'GLACCOUNTNO', 'GLACCOUNTTITLE', 'LOCATIONID', 'LOCATIONNAME', 'DEPARTMENTID',
            'DEPARTMENTNAME', 'TRX_AMOUNT',],
    SearchTable::GSANYFIELD => 'RECORDID',
    SearchTable::GSDEFAULTFILTERFIELD => 'RECORDID',
];
$kSchemas['aprecurbillentry']['ownedobjects'][] = [ 'entity'  => 'aprecurbilltaxentry',
                                              'path'    => 'TAXENTRIES', 'fkey' => 'PARENTENTRY',
                                              'invfkey' => 'RECORDNO', ];
