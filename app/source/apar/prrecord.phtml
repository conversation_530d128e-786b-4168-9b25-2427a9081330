<?
//=============================================================================
//
//	FILE:			prrecord.phtml
//	AUTHOR:			<PERSON>
//	DESCRIPTION:	Lister for invoices -- both for AP and AR
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

/*
URL VARIABLES:
Bathces.phtml takes the URL parameter '_recordtype', which designates which
batches to list.  The allowed values in 'type' are [p|r][i|p|a] where
p = Accounts Payable
r = Accounts Receivable
e = Employee Expense
i = invoice
h = Payroll

the '_batch' parameter is the foreign key value for the batchkey field
and is optional
*/

require_once 'util.inc';
require_once 'html_header.inc';
require_once 'show_listing.inc';
require_once 'groom_lister.inc';
require_once 'backend_prbatch.inc';
require_once 'backend_payment.inc';
require_once 'common_apar.inc';
require_once 'Dictionary.cls';

import("NLister");

/* Proxy Lister */


class PrLister extends NLister
{

    /**
     * @param string[] $_params
     */
    function __construct($_params = [])
    {
        $_params['suppressPrivate'] = true; //Until we can sort out this mess
        parent::__construct($_params);
    }

    /**
     * @param array $params
     *
     * @return void
     * @throws APIInternalException
     */
    public function addAndLoadTokenLabels(array $params)
    {
        $fields = $params['fields'];
        $labels = $params['fieldlabels'];
        $count = count($fields);
        for ($i = 0; $i < $count; $i++) {
            $this->addLabelMapping($fields[$i], $labels[$i]);
        }


        $this->additionalTokens[] = $params['title'];
        $this->additionalTokens[] = 'IA.APPLY_MORE';
        $this->additionalTokens[] = 'IA.REVERSE';
        $this->additionalTokens[] = 'IA.REVERSED';
        $this->additionalTokens[] = 'IA.REVERSAL';

        $this->loadTokenLabels();
    }

    /**
     * @return string
     */
    function calcAddUrl()
    {

        global $obj;

        $p = &$this->_params; //???
        $t = &$this->table;

        if ($p['_disableadd'] || $t[0]['IS_QUICKDEPOSIT']) {
            return "";
        }

        $text = $p['_addbutton'];

        $u = $obj['addurl'];

        $u = str_replace('&', '&amp;', $u);

        $ret = "<a href=\"" . $u . "\" " .
            "onclick=\"if(!ValidateGLPostingDateForBatchItemAdd())return false;\" >" .
            $text .
            "</a>";

        return $ret;
    }


    /**
     * @param int $i index into lister table of displaying objects

     * @return bool true if the object at index $i can be deleted
     */
    protected function canDelete(/** @noinspection PhpUnusedParameterInspection */$i)
    {
        $t = &$this->table;
        $values = $t[$i];

        if($values['RECORDTYPE'] === 'rm' || $values['RECORDTYPE'] === 'rd'){
            return false;
        } else if($values['RECORDTYPE'] === 'rp' || $values['RECORDTYPE'] === 'rr'){
            return $this->canReverse($values);
        }
        return parent::canDelete($i);
    }

    /**
     * Calculates the delete url. Method takes $owner, $ownerloc, $ownedObj parameters
     * for performance reasons.
     *
     * @param int    $i
     * @param string $owner
     * @param string $ownerloc
     * @param bool   $ownedObj
     *
     * @return string
     */
    function calcDeleteUrl($i, $owner = null, $ownerloc = null, $ownedObj = null)
    {
        $p = &$this->_params; //???
        $t = &$this->table;
        $values = $t[$i];

        if($values['RECORDTYPE'] == 'rp' || $values['RECORDTYPE'] == 'rr'){
            return $this->ReceivedPaymentVoidString($i);
        }

        $key = $p['_key'];
        $keyval = $t[$i][$key];
        $text = $p['_delbutton'];

        if ($p['_edit'] != 'editor.phtml') {

            // Special case. prrecord.
            $u = CallUrl(
                ExtendUrl(
                    $p['_edit'],
                    (".ret=1&.do=del&.r=" . urlencode($keyval) . "&.op=" . urlencode($p['_op']['delete']))
                )
            );

            $u = str_replace('&', '&amp;', $u);
            $ret = "<a href=\"" . $u . "\" " . $this->DLT() . ">" . $text . "</a>";
            return $ret;
        }

        // Default behavior.

        return NLister::calcDeleteUrl($i);
    }

    /**
     * @return string
     */
    function calcSendUrl()
    {
        $p = &$this->_params; //???

        $text = $p['_sendbutton'];
        $dst = $p['_sendurl'];

        $ret = "<a href=\"" .
            $this->U($dst, "", $this->LCALL) . "\" >" .
            $text .
            "</a>";

        return $ret;
    }

    /**
     * Figure out if a transaction can be reversed
     *
     * @param array $values
     *
     * @return bool true if the transaction can be reserved else false
     */
    function canReverse($values)
    {
        // Build the reverse OP
        $op = "";
        if($values['RECORDTYPE'] === 'rp'){
            $op = 'ar/activities/arpaymentbatch/view';
        }else if($values['RECORDTYPE'] === 'rr'){
            $op = 'ar/lists/aradvance/reverse';
        }else {
            return true;
        }

        $reverseOp = GetOperationId($op);
        $ok = CheckAuthorization($reverseOp, 1);

        if(!$ok){
            return false;
        }

        // No reversal of reconciled transactions
        if (!in_array($values['STATE'], array('Reversed', 'Reversal', 'Draft','Declined', 'Submitted', 'Partially Approved')) &&
            $this->allowReconciledTrxToReverse($values)) {
            return true;
        }else if ($values['STATE'] == 'Posted') {
        if ($this->hasOpenBatch($values)) {
            // If the batch open
            return true;
        }
    }

        return false;
    }

    /**
     * Utility method to form the received payment void string.
     *
     * @param string $i
     *
     * @return string
     */
    function ReceivedPaymentVoidString($i)
    {
        $_op = Request::$r->_op;
        $_sess = Session::getKey();
        $paymentRec = &$this->table[$i];
        $values = $this->values[$i];

        if ($paymentRec['STATUS'] == '1') {
            // THIS MEANS IT WAS VOIDED
            $voidToken = (bcadd($values['TOTALENTERED'], $values['TOTALOVERPAY']) > 0) ? 'IA.REVERSED' : 'IA.REVERSAL';
            $voidStr = I18N::getSingleToken($voidToken);
        } else if ($paymentRec['STATE'] === BasePymtManager::DRAFT_RAWSTATE) {
            $voidStr = I18N::getSingleToken('IA.DRAFT');
        }else {
            $keyToVoid = $paymentRec['PAYMENTKEY'];
            $voidText = I18N::getSingleToken('IA.REVERSE');
            $recordType = $paymentRec['RECORDTYPE'];
            if(!$this->canReverse($paymentRec)){
                return "";
            }
            $voidStr = "<a href=\"javascript: VoidPayment('$keyToVoid', '$_op', '$_sess', '$recordType');\">$voidText</a>";
        }
        return $voidStr;
    }

    /**
     * @param array $values
     * @return bool
     */
    function allowReconciledTrxToReverse(array $values): bool
    {
        return $values['CLEARED'] != 'T' ? true : false;
    }

    /**
     * Figure out if the transaction is in an open batch
     *
     * @param array $values
     *
     * @return bool true if the transaction is in an open batch else false
     */
    protected function hasOpenBatch(&$values)
    {
        $result = true;

        // Is the transaction batch closed ?
        $isBatchOpen = ( $values['PRBATCH_OPEN'] == 'T' );
        if ( !$isBatchOpen ) {
            $result = false;
        }

        // Is the module batch closed ?
        if ( !$result ) {
            $batchOpenDate = $this->getModuleBatchOpenDate($values['RECORDTYPE']);
            $isModuleBatchOpen = ( $batchOpenDate && SysDateCompare($values['WHENCREATED'], $batchOpenDate) < 0 );
            if ( !$isModuleBatchOpen ) {
                $result = false;
            }
        }

        return $result;
    }

    /**
     * Get the module batch open date
     * @param string $recordType
     *
     * @return string the batch open date
     */
    protected function getModuleBatchOpenDate($recordType)
    {
        $batchOpenDate = '';

        $prType = isl_substr($recordType, 0, 1);
        GetOpenDate($prType, $batchOpenDate);

        return $batchOpenDate;
    }


    function DrawHTML()
    {
        NLister::DrawHTML();
        $_sess = Session::getKey();
        $_op = Request::$r->_op; ?>
        <script language="javascript">
            function invoiceWindowByRecNo(recno) {
                var URL = "invoice.phtml" + "?.sess=<? echo $_sess ?>&.op=<? echo $_op?>&.r=" + recno;
                params = "width=300,height=450,scrollbars=yes,dependent";
                var hWnd = window.open(URL);
                if (hWnd == null) {
                    return;
                }
                if (hWnd.opener == null) {
                    hWnd.opener = self;
                    window.name = "invoice";
                }
                hWnd.location.href = URL;
                hWnd.focus();
            }

            function ValidateGLPostingDateForBatchItemAdd() {
                if (typeof IsCompanyCashAndNotMultibooks == 'undefined' || IsCompanyCashAndNotMultibooks == null) return true;
                if (IsCompanyCashAndNotMultibooks) return true;

                if (!(typeof batchCreated == 'undefined' || batchCreated == null || batchCreated == "")) {
                    if (ReformatDate(batchCreated, UserDateFormat, ' Ymd') > ReformatDate(LastDayOfCurrentPeriod, '/mdY', ' Ymd')) {
                        if (glpostingdatecheck == 'WARN') {
                            //TODO need to tokenize this
                            if (!confirm("Warning! You are attempting to enter a transaction in a batch with a date outside of the current period. Continue?")) {
                                return false;
                            }
                        } else if (glpostingdatecheck == 'DISALLOW') {
                            //TODO need to tokenize this
                            alert("You are attempting to enter a transaction in a summary with a date outside of the current period.\nPlease select a summary of a date within the current period.")
                            return false;
                        }
                    }
                }
                return true;
            }
        </script>
        <?
        include_once 'js_common.inc';
        ?>
        <script src="../resources/js/payments.js"></script>
        <?
    }

    /**
     * @return string
     */
    function genTopPanel()
    {
        $ret = NLister::genTopPanel();
        $popupArg = Request::$r->_popup;
        if ($popupArg) {
            $ret = "<b id='cancel'/>" . $ret;
        }
        return $ret;
    }

    /**
     * @return string
     */
    function genBotPanel()
    {
        $ret = NLister::genBotPanel();
        $popupArg = Request::$r->_popup;
        if ($popupArg) {
            $ret = "<b id='cancel'/>" . $ret;
        }
        return $ret;
    }

}

Init();
$_sess = Session::getKey();
$_done = &Request::$r->_done;
$_batch = Request::$r->_batch;
$_quick = Request::$r->_quick;
$_s = Request::$r->_s;
$_rb = Request::$r->_rb;
$_rs = Request::$r->_rs;
$_op = &Request::$r->_op;
$_recordtype = &Request::$r->_recordtype;
$_mod = Request::$r->_mod;
$id = &Request::$r->id;
$hlpfile = &Request::$r->hlpfile;
$F_TOTALENTERED = Request::$r->F_TOTALENTERED;
$_backhere = Request::$r->_backhere;
$_cancel = Request::$r->_cancel;
$_queryfromend = Request::$r->_queryfromend;
$_add = Request::$r->_add;
$atlas = &Request::$r->atlas;
$entity = &Request::$r->entity;

global $obj, $gErr, $_userid;
[$userid, $cny] = explode("@", $_userid);

// IF WE USE ADD TO DIRECT THIS FORM TO ANOTHER PAGE, IT MUST RETAIN OUR $_done VARIABLE FOR US
//$embeddedDone = FwdUrl(ScriptRequest(), $_done);

// The value of $embeddedDone is changed,as ScriptRequest itself carrying a $_done variable.so there is no need for another $_done
// variable. < BugID = 4256 >
$embeddedDone = ScriptRequest();
$perm_arr = GetPagePermissions($_op);

////////////////////////
// GET THE BATCH INFO
////////////////////////
$prbatch = [];
if ($_batch) {
    if (!GetPRBatch($_batch, $prbatch)) {
        // do nothing?
        $title = '';
    } else {
        $title = $prbatch['TITLE'];
        $_recordtype = $prbatch['RECORDTYPE'];

        // this error was added for bug# 3374.
        if ($_recordtype == '') {
            $gErr->addError(
                "SL-0330", __FILE__ . __LINE__,
                "The system does not have any data related to this transaction"
            );
            include 'popuperror.phtml';
            exit();
        }

        if ($_recordtype == "ro") {
            //$typeinfo['ro']['filter'] = "lower(PRRECORD.RECORD#) = $overpaymentParent";
            // eppp($typeinfo);
        }

        if ($_recordtype == 'ei') {
            $perm_arr = GetPagePermissions(GetOperationId('ee/lists/eexpenses'));
        }
    }

    ///////////////////////////////////////////
    // INCLUDE THE APPROPRIATE METADATA FILE
    ///////////////////////////////////////////
    $tablename = ($_recordtype == 'ro') ? 'prrecord' : 'payment';
    $keyname = ($_recordtype == 'ro') ? 'prrecord.record#' : 'paymentkey';

    $applyeMoreLabel = 'Apply More';
    if ($perm_arr['create'] != '' || $perm_arr['edit'] != '') {
        $arpymtOp = GetOperationId('ar/lists/arpymt/create');
        $rpurl = '<a href="editor.phtml?.op='. $arpymtOp .'&.rpr=\'|| ' . $keyname . ' ||\'&.uisource=applymore&.batchkey=\'|| ' .
            $tablename . '.prbatchkey ||\'&.custentity=\'|| ' . $tablename . '.entity ||\'&' . OptDone(ScriptRequest()) .
            '" ONMOUSEOVER=\'\' window.status="' . statusdisp('Apply more of this payment') .
            '" ; return true;\'\' onfocus=\'\'window.status="' . $applyeMoreLabel .
            '"; return true;\'\' onblur=\'\'window.status=""\'\' ONMOUSEOUT=\'\'window.status=""\'\'>'.$applyeMoreLabel.'</a>';
    } else {
        $rpurl = $applyeMoreLabel;
    }
    /*if ($perm_arr['delete'] != '') {
        if ($_recordtype != 'rp') {
            $vpurl = '<a href="edit_payment.phtml?.op=' . $_op . '&.r=\'|| ' . $keyname . ' ||\'&.do=void&.batch=\'|| ' . $tablename . '.prbatchkey ||\'&.entity=\'|| ' . $tablename . '.entity ||\'&' . OptDone(ScriptRequest()) . '" ONMOUSEOVER=\'\' window.status="' . statusdisp('Void the payment') . '" ; return true;\'\' onfocus=\'\'window.status="' . statusdisp('Void the payment') . '"; return true;\'\' onblur=\'\'window.status=""\'\' ONMOUSEOUT=\'\'window.status=""\'\'>Void</a>';
        }
    } else {
        $vpurl = 'Void';
    }*/

    // IF WE USE ADD TO DIRECT THIS FORM TO ANOTHER PAGE, IT MUST RETAIN OUR $_done VARIABLE FOR US
    $urlrecordtype = ($_recordtype == 'ro') ? 'rp' : $_recordtype;
    // $embeddedDone = FwdUrl(ScriptRequest(), $_done);
    // The value of $embeddedDone is changed,as ScriptRequest itself carrying a $_done variable.so there is no need for another $_done
    // variable. < BugID = 4256 >
    $embeddedDone = ScriptRequest();

    if ($prbatch['SYSTEMGENERATED'] == 'T') {
        unset($perm_arr['edit']);
    }

    switch ($_recordtype) {
        case 'ri':
            if ($prbatch['SYSTEMGENERATED'] == 'T') {
                include_once 'meta_rq_record.inc';
            } else {
                include_once 'meta_ri_record.inc';
            }
            break;
        case 'ci': // EXACTLY THE SAME AS pi
        case 'cc': // EXACTLY THE SAME AS pi HERE
        case 'ra':
            include_once 'meta_ra_record.inc';
            break;
        case 'hi':
            include_once 'meta_hi_record.inc';
            break;
        case 'ei':
            include_once 'meta_ei_record.inc';
            break;
        case 'ro':
            include_once 'meta_ro_record.inc';
            break;
        case 'rd':
        case 'rm':
        case 'rr':
        case 'rp':
            include_once 'meta_rp_record.inc';
            break;
        default:
            dieFL("Invalid recordtype $_recordtype");
    }
} else {
    $_recordtype = ($_recordtype) ?: 'ei';
    if ($_recordtype == 'ei') {
        $empentity = GetMyEmpentity();
        $title = GetFullEmployeeName($empentity);
        if (!$title) {
            $myLogin = GetMyLogin();
            $gErr->addIAError(
                "SL-0434", __FILE__ . __LINE__,
                sprintf('The userid %1$s is not associated with any employee', $myLogin),
                ['MY_LOGIN' => $myLogin]
            ); //TODO:i18N-SL-Error-Message (code change review)
            include 'popuperror.phtml';
            exit();
        }
        include_once 'meta_myexpense_record.inc';
    } elseif ($_recordtype == 'ri') {
        if ($_quick) {
            include_once 'meta_quickdep_record.inc';
            $title = 'Manual Deposits';
            if (IsMCMESubscribed() && !GetContextLocation()) {
                $atlas = true;
            }
        } else {
            include_once 'meta_invoice_record.inc';
            $title = I18N::getSingleToken('IA.INVOICES');
        }

    } elseif ($_recordtype == 'ra') {
        include_once 'meta_aradjustment_record.inc';
        $title = 'Adjustments';
    } else {
        // THIS SHOULD NEVER HAPPEN
        $gErr->addError(
            'SL-0331',
            __FILE__ . '.' . __LINE__,
            'No document found to support this call.'
        );
    }
}


// WE DON'T WANT THE SHOWLISTING() DEFAULT OF HISTORY.BACK
if ($_cancel) {
    // WE ALSO HANDLE THE ADD BUTTON BELOW
    if (!$_backhere) {
        if (!isl_stristr($_done, ".sess")) {
            $_done .= "&.sess=" . $_sess;
        }
        Ret();
    } else {
        Go($_done . "&.backhere=" . urlencode(URLCleanParams::insert('.backhere', $_done)));
    }
}

/** @noinspection PhpUndefinedVariableInspection */
if ($_send) {
    Go($obj['sendurl']);
}

[$user_rec, $cny] = explode('@', $_userid);


//epp("recordtype: $_recordtype");
//////////////////////////////
// BUILD PARAMETERS FROM URL 
//////////////////////////////

$qryFields = $obj['qryfields'];

if ($_add) {
    $id = GetIdForRecType($urlrecordtype);
    Redirect($obj['addurl']);
}
$found_col = 0;
foreach (explode(",", $_s) as $col) {
    $col = preg_replace('/:[ad]/', '', $col);
    if (in_array($col, $obj['tblfields'])) {
        $found_col = 1;
    }
}
if (!$found_col) {
    if ($obj['sortcol']) {
        $_s = $obj['sortcol'];
    } else {
        $_s = 'NAME:a';
    }
}

//this is a hack for not to display the add button when the batch was closed and the customform property is set.
if ($prbatch['OPEN'] == 'F' && isset($obj['customform']['buttons'])) {
    $custombuttons = [];
    foreach (array_keys($obj['customform']['buttons']) as $foo) {
        if ($foo != '.add' && isl_strtolower($obj['customform']['buttons'][$foo]) != 'add') {
            $custombuttons[$foo] = $obj['customform']['buttons'][$foo];
        }
    }
    $obj['customform']['buttons'] = $custombuttons;
}

if ($obj['isTokenUsed'] === true ) {
    if (!empty($title)) {
        $title = [
            'id' => $obj['placeholdertitle'],
            'placeHolders' => [
                [ 'name' => 'SUMMARY_TITLE', 'value' => $title ],
            ]
        ];
    } else {
        $title = $obj['title'];
    }
} else {
    $title = ($obj['title']) ? $title . ' - ' . $obj['title'] : $title;
}


//$dict = Dictionary::getInstance();
//$fieldlabels = array();
//foreach ($obj['fieldlabels'] as $fld) {
//    $fieldlabels[] = $dict->GetRenamedText($fld);
//}

$params = [
    'title' => $title,
    'fields' => $obj['tblfields'],
    'fieldlabels' => $obj['fieldlabels'],
    'fullnames' => $obj['fullnames'],
    'groomtype' => $obj['groomtype'],
    'key' => 'RECORD#',
    'op' => $perm_arr,
    'edit' => $obj['editurl'],
    'disableedit' => ($obj['disableedit']) ?: null,
    'disabledelete' => ($obj['disabledelete'] == true) ? true : null,
    'disableadd' => $prbatch['OPEN'] == 'F' ? true : null,
    'list' => $obj['detail'],
    'action' => $obj['detail'], // THIS FILE WILL HANDLE ITS OWN ADD AND CANCEL BUTTONS
    'addlabel' => $obj['addlabel'],
    'rangebegin' => $_rb,
    'rangesize' => $_rs,
    'sortcolumn' => $_s,
    'format' => $obj['format'],
    'queryfromend' => $_queryfromend,
    'customform' => $obj['customform'],
    'sendbutton' => $obj['sendbutton'],
    'sendurl' => $obj['sendurl'],
    'quick' => $_quick,
    'defaultview' => 'Recently viewed',
    'nosysview' => true,
];

if (!empty($_r)) {
    $params['disablefilter'] = $params['disablesort'] = true;
}

if ($atlas) {
    $params['disableadd'] = $params['disablesort'] = true;
    $params['editbutton'] = '';
}
if ($_mod == 'ar' && empty($prbatch['ACCOUNTNOKEY']) && empty($prbatch['UNDEPFUNDSACCT'])) {
    $params['disableadd'] = true;
}

$entity = ($obj['entity']) ?: 'prrecord';

$listerEntity = Request::$r->_it;

if (class_exists($listerEntity . "Lister", false)) {
    $listerClass = $listerEntity . "Lister";
    $l = new $listerClass;
} else {
    $l = new PrLister(['entity' => $entity, 'helpfile' => $hlpfile,]);

    $l->addAndLoadTokenLabels($params);
}

$listing = ShowListingInit($params);

if (!CalcFiltersLite($listing, $filters)) {
    // some error handling here
    // epp("Error occurred in CalcFiltersLite.");
    $filters = '';
}

if (isset($obj['filter']) && $obj['filter'] != '') {
    $filters .= ($filters == '') ? ' ' : ' and ';
    $filters .= $obj['filter'];
}

if (isset($_r)) {
    $F_TOTALENTERED = $F_CREATEDDATE = "";
}

// BUILD ORDER BY CLAUSE
$desc_dir = $_queryfromend ? ' asc' : ' desc';
$asc_dir = $_queryfromend ? ' desc' : ' asc';
$order = str_replace(':d', $desc_dir, str_replace(':a', $asc_dir, $listing['_sortcolumn']));

$table = [];

if ($obj['custom_queryfunc']) {
    //Initialize the variable
    $fullTable = [];
    $tableSlice = [];
    $goodCustomQry = $obj['custom_queryfunc']($fullTable, $_batch, $filters, "", "", "", $_r);
    SetDomainSize($listing, count($fullTable));
    $goodCustomQry = $obj['custom_queryfunc']($tableSlice, $_batch, $filters, $order, $listing['_rangebegin'], $listing['_rangesize'], $_r, $_queryfromend);
    $table = $tableSlice;
    $l->values = $table;
} else {
    $entity = ($obj['entity']) ?: 'prrecord';
    GetCount($entity, $filters, $resultCount);
    SetDomainSize($listing, $resultCount);
    $table = GetNObjects(
        $entity, join(',', $qryFields), $filters, $order,
        $listing['_rangebegin'], $listing['_rangesize'], '', ''
    );
}

if ($_queryfromend) {
    $table = array_reverse($table);
}

if ($table && ($obj['custom_voidlinkfunc'] && $obj['voidlinkcol'])
    || ($obj['custom_applyMorefunc'] && $obj['applyMorelinkCol'])) {

    foreach ($table as $index => $record) {
        if ($obj['custom_voidlinkfunc'] && $obj['voidlinkcol']) {
            $voidLink = $obj['custom_voidlinkfunc']($record);
            $table[$index][$obj['voidlinkcol']] = $voidLink;
        }

        if ($obj['custom_applyMorefunc'] && $obj['applyMorelinkCol']) {
            $applyMoreLink = $obj['custom_applyMorefunc']($record);
            $table[$index][$obj['applyMorelinkCol']] = $applyMoreLink;
        }
    }
}

if (isset($table[0]) or $table[0] != '') {
    GroomResultantData($table, $obj['groomtype'], $obj['tblfields']);
}

if (HasErrors()) {
    // add some error here
    include "popuperror.phtml";
    exit();
    // epp("Error occurred in GetNObjects.");
} else {
    // groom the data output?
}

$hlpfile = $obj['hlpfile'];

// ARE WE LISTING QUICK DEPOSITS (payments)?
if ($table[0]['IS_QUICKDEPOSIT']) {
    // Payments are never deleted from the batch, only reversed.
    $hlpfile = $obj['qdep_hlpfile'];
}

/* PAULT. */

// global $_dbg; $_dbg=1; eppp( $listing ); exit;

$listing['_recordtype'] = $_recordtype;
$listing['_batch'] = $_batch;
$listing['_entitynostatus'] = 1;

if ($_recordtype == 'ei') {
    $listing['_entity'] = 'eexpenses';

    // the expense report might be at the entity level so need to set
    // report view context
    $needcontext = (IsMultiEntityCompany() && !GetContextLocation());
    if ($needcontext) {
        SetReportViewContext();
    }
}

$l->initialized = 1;
$l->table = $table;
$l->_params = $listing;
$l->DrawHTML();

if ($_recordtype == 'ei') {
    // need to reset report view context
    if ($needcontext) {
        SetTransactionViewContext();
    }
}

// to validate GL Posting to Future Periods
InitJSGlobals();
if ($_batch && $prbatch['CREATED']) {
    echo("\n<script>var batchCreated = \"" . $prbatch['CREATED'] . "\";</script>\n\n");
}


