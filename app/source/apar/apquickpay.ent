<?php

/**
 * Entity for the AP Quick Pay object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */
$kSchemas['apquickpay'] = array(
    'iapaymethod' => array('global' => 1),
    'children' => array(
        'bankaccount' => array(
            'fkey' => 'financialentity', 'invfkey' => 'accountid', 'join' => 'outer', 'table' => 'bankaccount'
        ),
        'vendor' => array(
            'fkey' => 'vendorkey', 'invfkey' => 'record#', 'table' => 'vendormst',
            'children' => array(
                'vendortype' => array('fkey' => 'vendtypekey', 'table' => 'vendtype', 'join' => 'outer'),
            )
        ),
        'payto' => array(
            'fkey' => 'billtopaytokey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'contactversionmst',
            'children' => array(
                'taxgroup' => array(
                    'fkey' => 'taxgroupkey', 'invfkey' => 'record#', 'table' => 'taxgrp', 'join' => 'outer',
                ),
            ),
        ),
        'iapaymethod' => array(
            'fkey' => 'paymethodkey', 'invfkey' => 'record#', 'table' => 'iapaymethod','join' => 'outer'
        ),
        'billpayment' => array(
            'fkey' => 'record#', 'invfkey' => 'paymentkey', 'table' => 'prpaymentrecordsmst',
            'children' => array(
                'apbill' => array('fkey' => 'recordkey', 'invfkey' => 'record#', 'table' => 'prrecordmst')
            )
        ),
        'batch' => array(
            'fkey' => 'prbatchkey', 'invfkey' => 'record#', 'table' => 'prbatchmst'
        ),
        'exchangerateinfo' => array(
            'fkey' => 'record#', 'invfkey' => 'recordkey', 'table' => 'exchangerateinfo', 'join' => 'outer'
        ),
        'taxsolution' => array(
            'fkey' => 'taxsolutionkey', 'invfkey' => 'record#', 'table' => 'taxsolution', 'join' => 'outer',
        ),
    ),
    'nexus' => array(
        'payto' => array(
            'object' => 'contactversion',
            'relation' => MANY2ONE,
            'field' => 'billtopaytokey',
            'printas' => 'IA.PAY_TO_CONTACT',
        ),
    ),
    'object' => array(
        'RECORDNO',
        'RECORDKEY',
        'BILLCURRENCY',
        'BILLBASECURRENCY',
        'CONTACTTAXGROUP',
        'RECORDTYPE',
        'WHENCREATED',
        'PAYMETHODKEY',
        'PAYMETHOD',
        'VENDORID',
        'VENDORNAME',
        'FINANCIALENTITY',
        'CREDITCARD',
        'BANKNAME',
        'DOCNUMBER',
        'RECORDID',
        'DESCRIPTION',
        'BILLTOPAYTOCONTACTNAME',
        'BILLTOPAYTOKEY',
        'BASECURR',
        'CURRENCY',
        'EXCH_RATE_DATE',
        'EXCH_RATE_TYPE_ID',
        'PAY_EXCH_RATE_TYPE_ID',
        'EXCHANGE_RATE',
        'TOTALENTERED',
        'TRX_TOTALENTERED',
        'TOTALPAID',
        'TRX_TOTALPAID',
        'WHENPAID',
        'STATE',
        'PRBATCH',
        'PRBATCHKEY',
        'CHECKFORMAT',
        'AUWHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'FORM1099TYPE',
        'FORM1099BOX',
        'VENDTYPE1099TYPE',
        'INCLUSIVETAX',
        'TAXSOLUTIONKEY',
        'TAXSOLUTIONID',
        'SHOWMULTILINETAX',
        'PAYTO.TAXGROUP.NAME',
        'PAYTO.TAXGROUP.RECORDNO',
        'PAYTO.TAXID',
        'TAXMETHOD',
        'VENDORKEY'
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'RECORDKEY' => 'billpayment.recordkey',
        'BILLCURRENCY' => 'apbill.currency',
        'BILLBASECURRENCY' => 'apbill.basecurr',
        'CONTACTTAXGROUP' => 'taxgroup.name',
        'RECORDTYPE' => 'recordtype',
        'WHENCREATED' => 'whencreated',
        'VENDORID' => 'vendor.vendorid',
        'VENDORNAME' => 'vendor.name',
        'PAYMETHODKEY' => 'paymethodkey',
        'PAYMETHOD' => 'iapaymethod.name',
        'FINANCIALENTITY' => 'financialentity',
        'CREDITCARD' => 'financialentity',
        'BANKNAME' => 'bankaccount.name',
        'DOCNUMBER' => 'docnumber',
        'RECORDID' => 'apbill.recordid', // This is coming from the pi record
        'DESCRIPTION' => 'description',
        'BILLTOPAYTOCONTACTNAME' => 'payto.name',
        'BILLTOPAYTOKEY' => 'billtopaytokey',
        'BASECURR' => 'basecurr',
        'CURRENCY' => 'currency',
        'EXCH_RATE_DATE' => 'exchangerateinfo.exch_rate_date',
        'EXCH_RATE_TYPE_ID' => 'exchangerateinfo.exch_rate_type_id',
        'PAY_EXCH_RATE_TYPE_ID' => 'exchangerateinfo.exch_rate_type_id',
        'EXCHANGE_RATE' => 'exchangerateinfo.exchange_rate',
        'TOTALENTERED' => 'totalentered',
        'TRX_TOTALENTERED' => 'trx_totalentered',
        'TOTALPAID' => 'totalpaid',
        'TRX_TOTALPAID' => 'trx_totalpaid',
        'WHENPAID' => 'whenpaid',
        'STATE' => 'state',
        'PRBATCH' => 'batch.title',
        'PRBATCHKEY' => 'prbatchkey',
        'CHECKFORMAT' => 'checkformat',
        'AUWHENCREATED' => 'auwhencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'FORM1099TYPE' => 'vendor.form1099type',
        'FORM1099BOX' => 'vendor.form1099box',
        'VENDTYPE1099TYPE' => 'vendortype.form1099type',
        'LOCATIONKEY' => 'locationkey',
        'INCLUSIVETAX' => 'inclusivetax',
        'TAXSOLUTIONKEY' => 'taxsolutionkey',
        'TAXSOLUTIONID' => 'taxsolution.solutionid',
        'SHOWMULTILINETAX' => 'taxsolution.showmultilinetax',
        'TAXMETHOD' => 'taxsolution.taxmethod',
        'RETAINAGEPERCENTAGE' => 'retainagepercentage',
        'TRX_TOTALRETAINED' => 'trx_totalretained',
        'TOTALRETAINED' => 'totalretained',
        'TRX_TOTALRELEASED' => 'trx_totalreleased',
        'PAYTO' => array(
            'contactversion.*' => 'payto.*',
            'TAXGROUP' => array(
                'taxgroup.*' => 'taxgroup.*',
            ),
        ),
        'VENDORKEY' => 'vendorkey',
        'SI_UUID'     => 'si_uuid',
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'AUWHENCREATED',
        'WHENMODIFIED'
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'RECORDKEY',
            'invfkey' => 'RECORDNO',
            'minLinesRequired' => 1,
            'entity' => 'apquickpayentry',
            'path' => 'ITEMS'
        )
    ),
    'fieldinfo' => array(
        $gRecordNoHiddenFieldInfo,
        array(
            'path' => 'RECORDTYPE',
            'fullname' => 'IA.RECORD_TYPE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 2
            ),
            'hidden' => true,
            'id' => 1
        ),
        array(
            'path' => 'WHENCREATED',
            'fullname' => 'IA.DATE',
            'type' => $gDateType,
            'required' => true,
            'id' => 2
        ),
        array(
            'path' => 'VENDORID',
            'fullname' => 'IA.VENDOR_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'vendor',
                'pickentity' => 'vendorpick',
                'pickfield' => array(
                    'PICKID', 'ACCOUNTLABEL', 'APACCOUNT', 'APACCOUNTTITLE', 'DISPLAYCONTACT.CONTACTNAME', 
                    'PAYTO.CONTACTNAME', 'RETURNTO.CONTACTNAME', 'ONHOLD', 'CREDITLIMIT', 'TOTALDUE', 'FORM1099TYPE', 
                    'FORM1099BOX', 'VENDTYPE1099TYPE', 'VENDORACCOUNTNO', 'DISPLAYCONTACT.TAXGROUP',
                    'DISPLAYCONTACT.TAXID', 'PAYTO.TAXGROUP', 'PAYTO.TAXID', 'RETAINAGEPERCENTAGE',
                )
            ),
            'required' => true,
            'isDimension' => true,
            'autofillrelated' => false,
            'renameable' => true,
            'id' => 3
        ),
        array(
            'path' => 'VENDORNAME',
            'fullname' => 'IA.VENDOR_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
                'format' => '/^.{0,100}$/'
            ),
            'renameable' => true,
            'id' => 4
        ),
        array(
            'path' => 'FINANCIALENTITY',
            'fullname' => 'IA.BANK',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'bankaccount',
                'pickentity' => 'bankaccountpick',
                'filterForCurrency' => true,
                'pick_url' => 'picker.phtml?.filterForCurrency=1',
                'pickfield' => array('BANKACCOUNTID', 'BANKNAME', 'CURRENCY', 'NEXTCHECK', 'PRINTON', 'GLACCOUNTNO'),
                'restrict' => array(
                    array(
                        'pickField' => 'CURRENCY',                         
                        'nulls' => true
                    ),
                    array(
                        'pickField' => 'BANKACCOUNTTYPE',
                        'value' => 'checking',
                    ),
                    )
            ),
            'required' => true,
            'id' => 5
        ),
        array(
            'path' => 'CREDITCARD',
            'fullname' => 'IA.CHARGE_CARD',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'creditcard',
                'pickfield' => array('CARDID', 'LIABACCT#'),
                'hideExpired' => true,
                'pick_url' => 'picker.phtml?.hideExpired=1',
                'restrict' => array(
                    array(
                        'value' => 'Credit',
                        'pickField' => 'LIABILITYTYPE'
                    ),
                    array(
                        'nonulls'   => true,
                        'pickField' => 'LIABACCT#'
                    )
                )
            ),
            'hidden' => true,
            'required' => true,
            'id' => 25
        ),
        array(
            'path' => 'BANKACCOUNTNAME',
            'fullname' => 'IA.BANK_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'id' => 22
        ),
        array(
            'path' => 'DOCNUMBER',
            'fullname' => 'IA.DOCUMENT_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 10
            ),
            'required' => true,
            'id' => 6
        ),
        array(
            'path' => 'RECORDID',
            'isHTML' => true,
            'fullname' => 'IA.BILL_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 45
            ),
            'id' => 7
        ),
        array(
            'path' => 'DESCRIPTION',
            'fullname' => 'IA.MEMO',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'multitext',
                'size' => 60,
                'maxlength' => 1000
            ),
            'numofcols' => 60,
            'id' => 8
        ),
        array(
            'path' => 'BILLTOPAYTOCONTACTNAME',
            'fullname' => 'IA.PAY_TO',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'noSplit' => true,
                'entity' => 'contact',
                'format' => $gContactNameFormat,
                'pickfield' => array(
                    'CONTACTNAME', 'MAILADDRESS.ADDRESS1', 'MAILADDRESS.ADDRESS2',
                    'MAILADDRESS.CITY', 'MAILADDRESS.STATE', 'MAILADDRESS.ZIP', 'EMAIL1', 'TAXID', 'TAXGROUP'
                )
            ),
            'id' => 9
        ),
        array(
            'path' => 'BILLTOPAYTOKEY',
            'fullname' => 'IA.PAYTO_CONTACT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'derived' => true,
            'readonly' => true,
            'hidden' => true,
            'id' => 21
        ),
        array(
            'path' => 'BASECURR',
            'fullname' => 'IA.BASE_CURRENCY',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies'
            ),
            'required' => true,
            'hidden' => true,
            'noedit' => true,
            'nonew' => true,
            'noview' => true,
            'id' => 10
        ),
        array(
            'path' => 'CURRENCY',
            'fullname' => 'IA.TRANSACTION_CURRENCY',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies'
            ),
            'required' => true,
            'hidden' => true,
            'noedit' => true,
            'nonew' => true,
            'noview' => true,
            'id' => 11
        ),
        array(
            'path' => 'EXCH_RATE_TYPE_ID',
            'fullname' => 'IA.EXCHANGE_RATE_TYPE',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'ptr',
                'entity' => 'exchangeratetypesall'
            ),
            'hidden' => true,
            'id' => 27
        ),
        array(
            'path' => 'PAY_EXCH_RATE_TYPE_ID',
            'fullname' => 'IA.PAY_EXCHANGE_RATE_TYPE',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'ptr',
                'entity' => 'exchangeratetypesall',
                'comboCacheKey' => 'hidecustom',
                'restrict' => array(
                    array(
                        'pickField' => 'ID',
                        'operand' => '!=',
                        'value' => CUSTOM_RATE_ID,
                    ),
                ),
            ),
            'hidden' => true,
            'id' => 30
        ),
        array(
            'path' => 'EXCHANGE_RATE',
            'fullname' => 'IA.EXCHANGE_RATE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'maxlength' => 12
            ),
            'precision' => 12,
            'hidden' => true,
            'noformat' => true,
            'rpdMeasure' => false,
            'id' => 28
        ),
        array(
            'path' => 'EXCH_RATE_DATE',
            'fullname' => 'IA.EXCHANGE_RATE_DATE',
            'type' => array(
                'type' => 'date',
                'ptype' => 'date',
                'format' => $gDateFormat
            ),
            'hidden' => true,
            'id' => 29
        ),
        array(
            'path' => 'TOTALENTERED',
            'fullname' => 'IA.TOTAL_BASE_AMOUNT',
            'type' => array(
                'type' => 'currency',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 12
        ),
        array(
            'path' => 'TRX_TOTALENTERED',
            'fullname' => 'IA.TOTAL_TRANSACTION_AMOUNT',
            'type' => array(
                'type' => 'currency',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 13
        ),
        array(
            'path' => 'TOTALPAID',
            'fullname' => 'IA.BASE_PAYMENT_AMOUNT',
            'required' => true,
            'hidden' => true,
            'type' => array(
                'type' => 'currency',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 14
        ),
        array(
            'path' => 'TRX_TOTALPAID',
            'fullname' => 'IA.TOTAL_TRANSACTION_PAID',
            'type' => array(
                'type' => 'currency',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 15
        ),
        array(
            'path' => 'WHENPAID',
            'fullname' => 'IA.DATE_FULLY_PAID',
            'type' => $gDateType,
            'id' => 16
        ),
        array(
            'path' => 'STATE',
            'fullname' => 'IA.STATE',
            'type' => array(
                'type' => 'enum',
                'ptype' => 'enum',
                'validvalues' => array('Printed', 'Confirmed', 'Voided'),
                '_validivalues' => array('Q', 'C', 'V'),
                'validlabels' => array('IA.PRINTED', 'IA.CONFIRMED', 'IA.VOIDED'),
            ),
            'readonly' => true,
            'id' => 17
        ),
        array(
            'path' => 'PRBATCH',
            'fullname' => 'IA.PAYMENT_SUMMARY',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'apquickcheckbatch',
                'maxlength' => 100
            ),
            'id' => 19
        ),
        array(
            'path' => 'PRBATCHKEY',
            'fullname' => 'IA.PAYMENT_SUMMARY_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'readonly' => true,
            'hidden' => true,
            'id' => 23
        ),
        array(
            'path' => 'PAYMETHOD',
            'fullname' => 'IA.PAYMENT_METHOD',
            'desc' => 'IA.PAYMENT_METHOD',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => [ 'IA.CHECK', 'IA.MANUAL_CHECK', 'IA.RECORD_TRANSFER', 'IA.CASH', 'IA.CHARGE_CARD' ],
                'validvalues' => [ 'Printed Check', 'Manual Check', 'EFT', 'Cash', 'Credit Card' ]
            ),
            'id' => 24
        ),
        array(
            'path' => 'CHECKFORMAT',
            'fullname' => 'IA.CHECK_FORMAT',
            'type' => array(
                'type' => 'enum',
                'ptype' => 'enum',
                'validlabels' => array('IA.CA', 'IA.MX', 'IA.US'),
                'validvalues' => array('CA', 'MX', 'US')
            ),
            'hidden' => true,
            'readonly' => true,
            'id' => 18
        ),
        // --
        // -- START UI ONLY FIELDS
        // --
        array(
            'path' => 'SUPDOCID',
            'fullname' => 'IA.ATTACHMENT',
            'type' => array(
                'ptype' => 'supdocptr',
                'type' => 'supdocptr',
                'maxlength' => 20,
                'listAction' => 'pick'
            ),
            'noedit' => false,
        ),
        array(
            'path' => 'ACCOUNTBALANCE',
            'fullname' => 'IA.BALANCE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'readonly' => true
        ),
        // --
        // -- END UI ONLY FIELDS
        // --
        $gAUWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        array(
            'fullname' => 'IA.FORM_1099_TYPE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'path' => 'FORM1099TYPE',
            'id' => 31
        ),
        array(
            'fullname' => 'IA.FORM_1099_BOX',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'path' => 'FORM1099BOX',
            'id' => 32
        ),
        array(
            'fullname' => 'IA.VENDOR_TYPE_FORM_1099_TYPE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'path' => 'VENDTYPE1099TYPE',
            'id' => 33
        ),
        array(
            'path' => 'TAXID',
            'fullname' => 'IA.TAX_ID',
            'desc' => 'IA.TAX_IDENTIFICATION_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => '/^.{0,20}$/'
            ),
            'readonly' => true,
            'showInGroup' => true,
            'id' => 34
        ),
        array(
            'path' => 'INCLUSIVETAX',
            'fullname' => 'IA.INCLUSIVE_TAXES',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 35
        ),
        array(
            'fullname' => 'IA.TAX_SOLUTION',
            'desc' => 'IA.TAX_SOLUTION',
            'path' => 'TAXSOLUTIONID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'taxsolution',
                'pickfield' => array('SOLUTIONID', 'SHOWMULTILINETAX', 'TAXMETHOD', 'RECORDNO'),
                'restrict' => array(
                    array(
                        'pickField' => 'TAXMETHOD',
                        'operand' => 'IN',
                        'value' => APBillManager::getTaxImplicationTaxMethodsHelper(false),
                    ),
                )
            ),
            'nonew' => true,
            'id' => 36,
        ),
        array(
            'fullname' => 'IA.TRANSACTION_HAS_TAX_IMPLICATIONS',
            'desc' => 'IA.TRANSACTION_HAS_TAX_IMPLICATIONS',
            'path' => 'TAXIMPLICATIONS',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 37
        ),
        array(
            'path' => 'CONTACTTAXGROUP',
            'fullname' => 'IA.CONTACT_TAX_GROUP',
            'desc' => 'IA.THE_VENDOR_PAYTO_CONTACT_TAX_GROUP',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'taxgroup',
            ),
            'readonly' => true,
            'id' => 38
        ),
        array(
            'path' => 'TAXID',
            'fullname' => 'IA.TAX_ID',
            'desc' => 'IA.TAX_IDENTIFICATION_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => '/^.{0,20}$/'
            ),
            'readonly' => true,
            'showInGroup' => true,
            'id' => 39
        ),
        $gSiUuidFieldInfo,
    ),
    'primaryDimensions' => array('VENDORID' => 'VENDORID'),
    'primaryDimensionKey' => ['vendor' => ['VENDORKEY' => 'VENDORID']],
    'pairedFields' => array(
        'VENDORID' => 'VENDORNAME',
        'FINANCIALENTITY' => 'BANKNAME'
    ),
    'printas' => 'IA.CHECK',
    'pluralprintas' => 'IA.CHECKS',
    'sicollaboration' => true,
    'table' => 'prrecord',
    'updatetable' => 'prrecordmst',
    'module' => 'ap',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'supdocentity' => 'APPAYMENT',
    'auditcolumns' => true,
    'customComponentsEntity' => 'apbill',
    'platform_entity' => 'appayment',
    'dbfilters' => array(
        array(
            'apquickpay.recordtype',
            'in',
            array(
                SubLedgerTxnManager::QUICKPAY_RECTYPE, 
                SubLedgerTxnManager::POSTEDCHECK_RECTYPE, 
                SubLedgerTxnManager::PRINTEDCHECK_RECTYPE
            )
        ),
        array(
            'apquickpay.systemgenerated',
             '=',
            'T'
        )
    ),
    'description' => 'IA.HEADER_INFORMATION_FOR_MANUAL_PAYMENTS_DESC',
);
require 'taxsummary.ent';
$kSchemas['apquickpay'] = EntityManager::inheritEnts($kSchemas['taxsummary'], $kSchemas['apquickpay']);
