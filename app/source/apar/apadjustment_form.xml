<?xml version="1.0" encoding='UTF-8'?>
<ROOT>
    <view system="true">
        <title>IA.AP_ADJUSTMENT</title>
        <events>
            <load>
                warnGLPostingDateOnLoad('WHENPOSTED');
                populateForm1099OnLoad('ITEMS', null, false, false);
                subledgertxnOnLoad('P');
            </load>
        </events>
        <pages>
            <page id="header" title="IA.HEADER">
                <section id="header_top" skipFieldSet="true" customFields="no">
                    <field noLabel="true" readonly="true" className="entityHeader">ENTITYHEADER</field>
                    <field noLabel="true" isHTML="true" readonly="true" className="entityBalance">TRX_ENTITYDUEHEADER
                    </field>
                </section>
                <section id="header_bottom" className="horizontal headerCustom" customFields="no">
                    <field fullname="IA.ADJUSTMENT_DATE" readonly="true">WHENCREATEDHEADER</field>
                    <field fullname="IA.ADJUSTMENT_TOTAL" isHTML="true" readonly="true">TRX_TOTALENTEREDHEADER</field>
                    <field fullname="IA.AMOUNT_PAID" isHTML="true" readonly="true">TRX_TOTALPAIDHEADER</field>
                    <field fullname="IA.AMOUNT_STILL_AVAILABLE" isHTML="true" readonly="true">TRX_TOTALDUEHEADER</field>
                    <field>
                        <path>STAMP</path>
                        <type type="stamp" ptype="stamp"></type>
                    </field>
                    <field fullname="IA.STATE" readonly="true" isHTML="true">STATEHEADER</field>
                </section>
                <section id="mainSection" className="transaction-section" customFields="no">
                    <row noLabel="true">
                        <field>
                            <path>WHENCREATED</path>
                            <events>
                                <change>
                                    populateHeaderField(this.meta);
                                    populateExchangeRateDate(this.meta);
                                    filterTaxDetailsPicker('P', false, true, true);
                                </change>
                            </events>
                        </field>
                        <field>
                            <path>WHENPOSTED</path>
                            <events>
                                <change>warnGLPostingDate(this.meta);</change>
                            </events>
                        </field>
                    </row>
                    <row noLabel="true">
                        <field fullname="IA.VENDOR" clazz="vendorField">
                            <path>VENDORID</path>
                            <events>
                                <change>vendorChange(this.meta);</change>
                            </events>
                        </field>
                        <field userUIControl="ContactControl" clazz="contactField">
                            <path>BILLTOPAYTOCONTACTNAME</path>
                            <events>
                                <change>populateContactAddress(this.meta);populateTaxFields(this.meta, 'P');</change>
                            </events>
                        </field>
                        <field userUIControl="ContactControl" clazz="contactField">
                            <path>SHIPTORETURNTOCONTACTNAME</path>
                            <events>
                                <change>populateContactAddress(this.meta);</change>
                            </events>
                        </field>
                    </row>
                    <row noLabel="true">
                        <field>
                            <path>ADJTYPE</path>
                            <events>
                                <change>changeAdjSequence(this.meta);</change>
                            </events>
                        </field>
                        <field isHTML="true">BILLTOPAYTOADDRESS</field>
                        <field isHTML="true">SHIPTORETURNTOADDRESS</field>
                    </row>
                    <row noLabel="true">
                        <field>STATE</field>
                    </row>
                    <row noLabel="true">
                        <field>RECORDID</field>
                        <field>DOCNUMBER</field>
                        <field>DESCRIPTION</field>
                    </row>
                    <row noLabel="true">
                        <field hidden="true">CONTACTTAXGROUP</field>
                        <field hidden="true">TAXID</field>
                    </row>
                    <row noLabel="true">
                        <field hidden="true" path="TAXIMPLICATIONS">
                            <events>
                                <change>handleTaxImplicationsCheckboxChange(this.meta, 'P');</change>
                            </events>
                        </field>
                        <field hidden="true">
                            <path>TAXSOLUTIONID</path>
                            <events>
                                <change>handleTaxSolutionChange(this.meta, 'P');</change>
                            </events>
                        </field>
                    </row>
                    <row noLabel="true">
                        <field>SUPDOCID</field>
                        <field>PRBATCH</field>
                    </row>
                    <row noLabel="true">
                        <field rightSideLabel="true" hidden="true">
                            <path>INCLUSIVETAX</path>
                            <events>
                                <change>
                                    handleInclusiveTaxSelectEvent(this.meta);
                                </change>
                            </events>
                        </field>
                    </row>
                </section>
                <section id="mainSection2" columnCount="2" customFields="yes"></section>
                <section id="mainSection3" columnCount="2" customFields="no">
                    <title>IA.CURRENCY</title>
                    <field>
                        <path>CURRENCY</path>
                        <events>
                            <change>updateExchangeRate(this.meta);</change>
                        </events>
                    </field>
                    <field>
                        <path>BASECURR</path>
                        <events>
                            <change>updateExchangeRate(this.meta);</change>
                        </events>
                    </field>
                    <field>
                        <path>EXCH_RATE_DATE</path>
                        <events>
                            <change>updateExchangeRate(this.meta);</change>
                        </events>
                    </field>
                    <field>
                        <path>EXCH_RATE_TYPE_ID</path>
                        <events>
                            <change>updateExchangeRate(this.meta);</change>
                        </events>
                    </field>
                    <field>
                        <path>EXCHANGE_RATE</path>
                        <events>
                            <change>calculateBaseAmount(null, this.meta);</change>
                        </events>
                    </field>
                </section>
                <grid clazz="EntriesGrid" allowEditPage="true">
                    <path>ITEMS</path>
                    <title>IA.ENTRIES</title>
                    <column>
                        <field required="true" hidden="true" clazz="accountLabelField">
                            <path>ACCOUNTLABEL</path>
                            <events>
                                <change>populateGLAccount(this.meta, null); populateLineMemo(this.meta,
                                    'ENTRYDESCRIPTION');
                                </change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field required="true" hidden="true">
                            <path>ACCOUNTNO</path>
                            <events>
                                <change>populateLineMemo(this.meta, 'ENTRYDESCRIPTION');</change>
                            </events>
                        </field>
                    </column>
                    <column className="center">
                        <field clazz="Form1099Field">
                            <path>FORM1099</path>
                            <type>
                                <type>boolean</type>
                            </type>
                            <events>
                                <change>changeForm1099Fields('ITEMS', 'VENDORID', this.meta);</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field hasTotal="true">
                            <path>TRX_AMOUNT</path>
                            <events>
                                <change>populateLineMemo(this.meta, 'ENTRYDESCRIPTION');onChangeTrxAmount(this.meta);</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field hidden="true" readonly="true" required="false" hasTotal="true">
                            AMOUNT
                        </field>
                    </column>
                    <column className="center">
                        <field hidden="true">
                            <path>MULTIPLETAXES</path>
                            <events>
                                <change>onClickMultipleTaxes(this.meta);</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field fullname="IA.DEPARTMENT">DEPARTMENTID</field>
                    </column>
                    <column>
                        <field fullname="IA.LOCATION">LOCATIONID</field>
                    </column>
                    <column>
                        <field fullname="IA.TAX_DETAIL">
                            <path>DETAILID</path>
                            <events>
                                <change>onChangeTaxDetail(this.meta);</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field fullname="IA.RATE" hidden="true">
                            <path>TAXRATE</path>
                            <readonly>true</readonly>
                        </field>
                    </column>
                    <column>
                        <field hidden="true">
                            <path>TRX_TAX</path>
                            <events>
                                <change>calculateBaseTaxForOverriddenTax(this.meta);updateParentGridTotalAmountValues(this.meta.getGrid(), this.meta.getLineNo());</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field hidden="true">
                            <path>TAX</path>
                        </field>
                    </column>
                    <column>
                        <field hidden="true">
                            <path>PAYMENTTAXCAPTURE</path>
                        </field>
                    </column>
                    <column>
                        <field>ENTRYDESCRIPTION</field>
                    </column>
                    <column>
                        <field readonly="true" required="false" hasTotal="true" hidden="true">
                            <path>TOTALTRXAMOUNT</path>
                            <events>
                                <change>onChangeTotalTrxAmount(this.meta);
                                </change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field readonly="true" required="false" hasTotal="true" hidden="true">
                            <path>TOTALBASEAMOUNT</path>
                        </field>
                    </column>
                    <lineDetails clazz="EntriesDetails">
                        <pages>
                            <page title="IA.DETAILS">
                                <section id="customFieldSection" title="IA.CUSTOM_FIELDS" skipFieldSet="true"
                                         customFields="ITEMS" columnCount="2">
                                </section>
                                <section id="vatSection" title="IA.TAXES">
                                    <grid clazz="TaxEntriesGrid">
                                        <path>TAXENTRIES</path>
                                        <column>
                                            <field fullname="IA.TAX_DETAIL">
                                                <path>DETAILID</path>
                                                <events>
                                                    <change>onChangeTaxDetail(this.meta);</change>
                                                </events>
                                            </field>
                                        </column>
                                        <column>
                                            <field fullname="IA.RATE">
                                                <path>TAXRATE</path>
                                                <readonly>true</readonly>
                                            </field>
                                        </column>
                                        <column>
                                            <field>
                                                <path>TRX_TAX</path>
                                                <events>
                                                    <change>calculateBaseTaxForOverriddenTax(this.meta);updateTxnTaxBasedOnMultiTaxTotal(this.meta);</change>
                                                </events>
                                            </field>
                                        </column>
                                        <column>
                                            <field>
                                                <path>TAX</path>
                                            </field>
                                        </column>
                                        <column>
                                            <field noLabel="true" hidden="true">
                                                <path>OVERRIDDENTAX</path>
                                                <type type='boolean' ptype='boolean'></type>
                                                <default>false</default>
                                            </field>
                                        </column>
                                    </grid>
                                </section>
                                <section hidden="true" id="taxTotalsSection" title="" columnCount="3">
                                    <field hidden="true">
                                        <path>BASETAXAMOUNT</path>
                                    </field>
                                    <field>
                                        <path>BASECURR</path>
                                    </field>
                                </section>
                                <section id="form1099section" title="" columnCount="2">
                                    <field clazz="Form1099TypeField">
                                        <path>FORM1099TYPE</path>
                                        <events>
                                            <change>setForm1099Box('ITEMS', this.meta);</change>
                                        </events>
                                    </field>
                                    <field clazz="Form1099BoxField">
                                        <path>FORM1099BOX</path>
                                    </field>
                                </section>
                                <section id="dimensionSection" title="IA.DIMENSIONS" dimFields="ITEMS" columnCount="2">
                                    <field>
                                        <path>OFFSETGLACCOUNTNO</path>
                                    </field>
                                </section>
                            </page>
                        </pages>
                    </lineDetails>
                </grid>
                <grid hasFixedNumOfRows="true" readonly="true" hidden="true">
                    <path>TAXSUMMARY</path>
                    <title>IA.TAX_SUMMARY</title>
                    <caption className="float_left" id="taxGridCalculateTaxCaption"
                             isCollapsible="false">
                        <button id="taxGridCalculateTaxButton">
                            <name>IA.SHOW_SUMMARY</name>
                            <events>
                                <click>reCalculateTax();</click>
                            </events>
                        </button>
                    </caption>
                    <column>
                        <field fullname="IA.DESCRIPTION">
                            <path>DETAILID</path>
                        </field>
                    </column>
                    <column>
                        <field fullname="IA.PERCENT">
                            <path>TAXRATE</path>
                        </field>
                    </column>
                    <column>
                        <field fullname="IA.TRANSACTION_TAX_TOTAL" hasTotal="true">
                            <path>TRX_TAX</path>
                        </field>
                    </column>
                    <column>
                        <field fullname="IA.BASE_TAX_TOTAL" hasTotal="true">
                            <path>TAX</path>
                        </field>
                    </column>
                </grid>
            </page>
            <!-- GL Posting tabs -->
            <page id="glPosting">
                <readonly>true</readonly>
                <hidden>true</hidden>
                <field hidden="true">RECORDNO</field>
                <xi:include href="glposting_grid.xml" xmlns:xi="http://www.w3.org/2003/XInclude"/>
            </page>
            <page title="IA.ADDITIONAL_INFORMATION"><!-- For platform regression since we removed a tab in Nov14 --></page>
        </pages>
    </view>
    <helpfile></helpfile>
</ROOT>
