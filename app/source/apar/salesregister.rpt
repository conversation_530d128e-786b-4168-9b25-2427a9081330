<?
/**
*    FILE:        salesregister.rpt
*    AUTHOR:        <PERSON><PERSON>
*    DESCRIPTION:    rpt file for AR Sales Register
*
*    (C) 2000, Intacct Corporation, All Rights Reserved
*
*    This document contains trade secret data that belongs to Intacct
*    Corporation and is protected by the copyright laws.  Information
*    herein may not be used, copied or disclosed in whole or in part
*    without prior written consent from Intacct Corporation.
*/
require_once 'util.inc';
require_once 'financialaccountregister.rpt';
require_once 'backend_bankaccount.inc';

$groupbyvalues = array('None', 'Customer', 'Location');
$groupbylabels = array('IA.NONE', 'IA.CUSTOMER', 'IA.LOCATION');

$kSchemas['salesregister'] = array (
    'schema' => array (
            'DRILLCHECK' => 'drillcheck',
            'DRILLINVOICE' => 'drillinvoice',
            'SHOWPAYDETAILS'     => 'dummy',
            'SHOWCREDITDETAILS' => 'showcreditdetails',
            'DRILLDOWN_POPUP'    => true,
            'SHOWDETAILS'        => 'dummy',
            'HIDEPAIDDOCS'         => 'dummy',
            'FILTERTRANSACTIONS'=> 'filtertransactions',
            'REPORT_LOC' => 'report_loc',
            'GROUPBY' => 'groupby',
    ),
    'promptonrun' => array (
        'REPORTINGTIMEPERIODFIELDS'        => array ( 'default' => false ),
        'LOCATION'        => array ( 'default' => false ),
        'DEPARTMENT'    => array ( 'default' => false ),
        'STARTENDDATES' => array ( 'default' => false ),
    ),
    'individualreport' => array (
        'LOCATION'        => array ( 'default' => false ),
        'DEPARTMENT'    => array ( 'default' => false ),
        'TERRITORY'        => array ( 'default' => false ),
    ),
    'fieldgroup' => array (
        'REPORTINGTIMEPERIODFIELDS' => $gReportingTimePeriodFieldGroup,
        'STARTENDDATES' => $gStartDateEndDateFieldGroup,
    ),
    'fieldinfo' => array ( 
                'userprefs' => true,
                'lines' => array(                    
                        $gTimePeriodGroupFieldInfo,
                    array(
                        'title' => 'IA.FILTERS',
                        'fields' => array(
                            array (
                                'fullname' => 'IA.DRILLINVOICE',
                                'type' => array (
                                    'ptype' => 'text', 
                                    'type' => 'text', 
                                ),                            
                                'path' => 'DRILLINVOICE',
                                'post' => true,
                                'hidden' => true,
                            ),
                            array (
                                'fullname' => 'IA.DRILLCHECK',
                                'type' => array (
                                    'ptype' => 'text', 
                                    'type' => 'text', 
                                ),                            
                                'path' => 'DRILLCHECK',
                                'post' => true,
                                'hidden' => true,
                            ),
                            //report_loc is used in Atlas companies
                            array (
                                'fullname' => 'IA.REPORT_LOC',
                                'type' => array (
                                    'ptype' => 'text', 
                                    'type' => 'text', 
                                ),                            
                                'path' => 'REPORT_LOC',
                                'post' => true,
                                'hidden' => true,
                            ),
                            array(
                                'fullname' => 'IA.CUSTOMER',
                                'type' => array (
                                    'ptype' => 'ptr', 
                                    'entity' => 'customer',
                                    'addlPickFields' => [ 'NAME' ],
                                    'type' => 'text', 
                                    'size' => '20',
                                    'pickcount' => PICKCOUNTER,
                                ),
                                'path' => 'CUSTOMER',
                                'noview' => true,
                                'noedit' => true,
                                'nonew' => true

                            ),

                            array(
                                'fullname' => 'IA.CUSTOMER_TYPE',
                                'type' => array (
                                    'ptype' => 'ptr', 
                                    'entity' => 'custtype',
                                    'type' => 'text', 
                                    'size' => '20'
                                ),
                                'path' => 'CUSTOMERTYPE',
                                'noview' => true,
                                'noedit' => true,
                                'nonew' => true

                            ),
                            $gCurrencyFilter,
                            $gTerritoryPick,
                            $gLocationPick,
                            $gDepartmentPick,
                            $gLocationSubFilter,
                            array (
                                'fullname' => 'IA.DISPLAY_TRANSACTION_DETAILS',
                                'type' => array (
                                    'ptype' => 'boolean', 
                                    'type' => 'char', 
                                    'validvalues' => $gBooleanValues,
                                    '_validivalues' => $gBooleanIValues,                                
                                ),                            
                                'path' => 'SHOWDETAILS',
                                'default' => 'false',
                                'onchange' => "ToogleCreditDetailsCheckBox();",
                            ),
                            array (
                                'fullname' => 'IA.DISPLAY_PAYMENT_DETAILS',
                                'type' => array (
                                    'ptype' => 'boolean', 
                                    'type' => 'char', 
                                    'validvalues' => $gBooleanValues,
                                    '_validivalues' => $gBooleanIValues,                                
                                ),                            
                                'path' => 'SHOWPAYDETAILS',
                                'default' => 'false',
                                'onchange' => "ToogleCreditDetailsCheckBox();",
                            ),
                            array (
                                'fullname' => 'IA.SHOW_CREDIT_DETAILS',
                                'type' => array (
                                    'ptype' => 'boolean', 
                                    'type' => 'char', 
                                    'validvalues' => $gBooleanValues,
                                    '_validivalues' => $gBooleanIValues,                                
                                ),                            
                                'path' => 'SHOWCREDITDETAILS',
                                'default' => 'false',
                                'onchange' => "ToogleCreditDetailsCheckBox('credit');",
                            ),
                            array (
                                'fullname' => 'IA.HIDE_PAID_DOCUMENTS',
                                'type' => array (
                                    'ptype' => 'boolean', 
                                    'type' => 'char', 
                                    'validvalues' => $gBooleanValues,
                                    '_validivalues' => $gBooleanIValues,                                
                                ),                            
                                'path' => 'HIDEPAIDDOCS',
                                'default' => 'false',
                            ),
                            [
                                'fullname' => 'IA.SHOW_PAYER_NAME',
                                'type' => [
                                    'ptype' => 'boolean',
                                    'type' => 'char',
                                    'validvalues' => $gBooleanValues,
                                    '_validivalues' => $gBooleanIValues,
                                ],
                                'default' => 'false',
                                'multi_entity_pymt' => true,
                                'desc' => 'IA.SHOW_PAYER_NAME',
                                'path' => 'SHOWPAYER',
                                'onchange' => "ToogleCreditDetailsCheckBox('payer');",
                            ],
                        ),
                    ),
                    array(
                        'title' => 'IA.FORMAT',
                        'fields' => array(
                            array (
                                'fullname' => 'IA.SORT_BY',
                                'type' => array (
                                    'type'             =>    'enum',
                                    'ptype'         =>    'enum',
                                    'validvalues'     =>    array (  
                                                    'Date', 
                                                    'Date desc',
                                                    'Customer ID',
                                                    'Customer Name',
                                                    'CustomerInvNo',    
                                                    'Invoice Number',
                                                    'Due Date',
                                                    'Due Date desc',
                                                    'Amount',
                                                    'Amount desc',
                                                    'Amount Paid',
                                                    'Amount Paid desc',
                                                ),
                                    '_validivalues'    =>    array (),    
                                    'validlabels'     =>    array (  'IA.DATE','IA.DATE_DESC','IA.CUSTOMER_ID','IA.CUSTOMER_NAME','IA.CUSTOMER_ID_INVOICE_NUMBER','IA.INVOICE_NUMBER','IA.DUE_DATE','IA.DUE_DATE_DESC','IA.AMOUNT','IA.AMOUNT_DESC','IA.AMOUNT_PAID','IA.AMOUNT_PAID_DESC',),
                                ),
                                'default'         => 'Customer ID',
                                'path' => 'SORTMODE'
                            ),                                        
                            $gPageOrientation,
                            array (
                                'fullname' => 'IA.GROUP_BY',
                                'type' => array (
                                    'type'             =>    'enum',
                                    'ptype'         =>    'enum',
                                    'validvalues'     =>    $groupbyvalues,
                                    '_validivalues'    =>    $groupbyvalues,
                                    'validlabels'     =>    $groupbylabels,
                                ),
                                'default'            => 'None',
                                'path' => 'GROUPBY',
                            ),
                        ),
                    ),
                ),
            ),
    'controls' => $gInvRptControls,
    'layout' => 'frame',
    'layoutproperties' => array(
                'rows'    => "*, 0",
                'border' => "0",
                'frames' => $gReportFrames,
    ),
    'xsl_file' => 'aparregister',
    'customjs' => 'billsregister.js',
    'printas' => 'IA.SALES_REGISTER',
    'module' => 'ar',
    'helpfile' => 'Running_Sales_Register_Reports',
    'popupfilterflds'    => array('DRILLINVOICE', 'SHOWDETAILS', 'SHOWPAYDETAILS', 'SHOWCREDITDETAILS', 'DRILLCHECK',
                           'REPORT_LOC'),
        'reportingAccounts' => true,
);

//Check whether report has been called SFDC User
if (IsSalesforceUser()) {
    include_once "backend_sforce.inc";
  
    // Change layout  for Processoffline etc
    $kSchemas["salesregister"]["controls"] = array( 
                                                    kShowHTML,
                                                    kShowPDF,
                                                    kShowExcel,
                                                    kShowCSV,
                                                    kShowText,
                                                );

    ///set Disable or Excluding  fields
    $excludeArr = array('CUSTOMERTYPE');

    ///Readonly fields 
    $readOnlyArr = array('CUSTOMER' );

    // Make Schema for readonly and exclude fields as per arguments array 
    $kSchemas = MakeSFDCUserSchema($kSchemas, "salesregister", $excludeArr, $readOnlyArr);

}

