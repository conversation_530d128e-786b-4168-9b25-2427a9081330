<?
$toform1099xsl = '<?xml version="1.0" encoding="iso-8859-1"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
	<xsl:template match="/">
		<fo:root>
			<fo:layout-master-set>

				<xsl:variable name="margin-top"><xsl:value-of select="//MARGIN/TOP"/></xsl:variable>
				<xsl:variable name="margin-left"><xsl:value-of select="//MARGIN/LEFT"/></xsl:variable>

				<fo:simple-page-master master-name="page"
						page-height="11in" page-width="8.5in"
						margin-top="0mm" margin-bottom="0mm"
						margin-left="0mm" margin-right="0mm"
						>
					<fo:region-body
						margin-top="{$margin-top}in" margin-bottom="0in"
						margin-left="{$margin-left}in" margin-right="0.25in"
						overflow1="auto"/>
				</fo:simple-page-master>

			</fo:layout-master-set>

			<fo:page-sequence master-reference="page">
				<fo:flow flow-name="xsl-region-body" font-family="Helvetica" font-size="10pt">
					<xsl:apply-templates/>
				</fo:flow>
			</fo:page-sequence>
		</fo:root>
	</xsl:template>

	<xsl:template match="Company"/>

	<xsl:template match="FORMMISC">
		<xsl:variable name="vendorCnt">
			<xsl:value-of select="count(./Vendor)"/>
		</xsl:variable>
		<xsl:for-each select="Vendor">
			<fo:block-container width="3.30in" height="4.5in" position="absolute">
				<xsl:attribute name="top"><xsl:value-of select="((position() - 1) mod 2) * 5.52"/>in</xsl:attribute>
				<xsl:call-template name="LeftColMisc"/>
			</fo:block-container>
			<fo:block-container width="3.75in" left="3.13in" height="4.5in" position="absolute">
				<xsl:attribute name="top"><xsl:value-of select="((position() - 1) mod 2) * 5.52"/>in</xsl:attribute>
				<xsl:call-template name="RightColMisc"/>
			</fo:block-container>
			<xsl:if test="(position() &lt; $vendorCnt) and (((position()+1) mod 2) = 1)">
					<fo:block break-before="page"/>
			</xsl:if>
		</xsl:for-each>
	</xsl:template>

	<xsl:template name="LeftColMisc">
		<fo:block-container width="3.3in" left="0.0in" height="4.5in" top="0.12in" position="absolute">
			<xsl:call-template name="payor_address"/>
			<xsl:call-template name="idnumbers"/>
			<xsl:call-template name="vendor_address"/>
			<xsl:call-template name="account_number"/>
			<xsl:call-template name="tin_details"/>
			<xsl:call-template name="col15ab">
				<xsl:with-param name="valuea" select="./FORM1099AMOUNT/BOX15A"/>
				<xsl:with-param name="valueb" select="./FORM1099AMOUNT/BOX15B"/>
			</xsl:call-template>
		</fo:block-container>
	</xsl:template>

	<xsl:template name="payor_address">
		<fo:block><xsl:value-of select="//Company/COMPANYNAME"/></fo:block>
		<xsl:variable name="add1" select="//Company/ADDRESS1"/>
		<xsl:choose>
			<xsl:when test="$add1!=\'\'">
				<fo:block><xsl:value-of select="$add1"/></fo:block>
			</xsl:when>
			<xsl:otherwise>
				<fo:block color="white">0</fo:block>
			</xsl:otherwise>
		</xsl:choose>

		<xsl:variable name="add2" select="//Company/ADDRESS2"/>
		<xsl:choose>
			<xsl:when test="$add2!=\'\'">
				<fo:block><xsl:value-of select="$add2"/></fo:block>
			</xsl:when>
			<xsl:otherwise>
				<fo:block color="white">0</fo:block>
			</xsl:otherwise>
		</xsl:choose>

		<fo:block>
			<xsl:value-of select="//Company/CITY"/><xsl:text>, </xsl:text><xsl:value-of select="//Company/STATE"/><xsl:text> </xsl:text><xsl:value-of select="//Company/ZIPCODE"/>
		</fo:block>

		<xsl:variable name="cphone" select="//Company/CONTACTPHONE"/>
		<xsl:choose>
			<xsl:when test="$cphone!=\'\'"><fo:block><xsl:value-of select="$cphone"/></fo:block></xsl:when>
			<xsl:otherwise><fo:block color="white">0</fo:block></xsl:otherwise>
		</xsl:choose>
		<fo:block line-height="0.50in" color="white">0</fo:block>
	</xsl:template>

	<xsl:template name="vendor_address">
		<fo:block line-height="-0.08in" color="white">,</fo:block>
		<fo:block line-height="0.09in" vertical-align="top" >
      		<fo:block line-height="0.11in"><xsl:value-of select="LINE_ONE"/></fo:block>
      		<xsl:variable name="line_two" select="LINE_TWO"/>
			<xsl:choose>
				<xsl:when test="$line_two!=\'\'">
         		<fo:block line-height="0.18in">
  					<xsl:value-of select="$line_two"/>
         		</fo:block>
        		</xsl:when>
				<xsl:otherwise><fo:block color="white">,</fo:block></xsl:otherwise>
			</xsl:choose>
		</fo:block>
		<fo:block line-height="0.22in" color="white">0</fo:block>
		<fo:block vertical-align="top" line-height="0.17in">
		    <xsl:variable name="baddr1" select="./BILLADDR1"/>
			<xsl:choose>
				<xsl:when test="$baddr1!=\'\'">
					<fo:block line-height="0.13in"><xsl:value-of select="$baddr1"/></fo:block>
				</xsl:when>
				<xsl:otherwise>
					<fo:block color="white">0</fo:block>
				</xsl:otherwise>
			</xsl:choose>
			<xsl:variable name="baddr2" select="./BILLADDR2"/>
			<xsl:choose>
				<xsl:when test="$baddr2!=\'\'">
					<fo:block line-height="0.15in"><xsl:value-of select="$baddr2"/></fo:block>
				</xsl:when>
				<xsl:otherwise>
					<fo:block color="white">0</fo:block>
				</xsl:otherwise>
			</xsl:choose>
		</fo:block>
		<fo:block line-height="0.08in" color="white">,</fo:block>

		<fo:block vertical-align="top" line-height="0.58in">
			<xsl:value-of select="./BILLCITY"/>
			<xsl:variable name="bstate" select="./BILLSTATE"/>
			<xsl:variable name="bzip" select="./BILLZIP"/>
			<xsl:choose>
				<xsl:when test="($bstate!=\'\') or ($bzip!=\'\')"><xsl:text>, </xsl:text><xsl:value-of select="$bstate"/><xsl:text> </xsl:text><xsl:value-of select="$bzip"/></xsl:when>
			</xsl:choose>
		</fo:block>
	</xsl:template>

	<xsl:template name="idnumbers">
		<xsl:variable name="value1" select="//Company/FEDERALID"/>
		<xsl:variable name="value2" select="TAXID"/>

		<fo:block line-height="0.68in">
			<fo:table>
				<fo:table-column column-width="1.5in" padding="4pt"/>
				<fo:table-column column-width="1.8in" padding="4pt"/>
				<fo:table-body>
					<fo:table-row>
						<fo:table-cell vertical-align="middle" >
							<xsl:choose>
								<xsl:when test="$value1!=\'\'">
									<fo:block text-align="center" line-height="0.75in" font-size="9.5pt">
                                                                            <xsl:value-of select="//Company/FEDERALID"/>
                                                                        </fo:block>
								</xsl:when>
								<xsl:otherwise>
									<fo:block color="white" text-align="center">blank</fo:block>
								</xsl:otherwise>
							</xsl:choose>
						</fo:table-cell>
						<fo:table-cell vertical-align="bottom" >
							<xsl:choose>
								<xsl:when test="$value2!=\'\'">
									<fo:block text-align="center" line-height="0.75in"><xsl:value-of select="TAXID"/></fo:block>
								</xsl:when>
								<xsl:otherwise>
									<fo:block color="white" text-align="center">blank</fo:block>
								</xsl:otherwise>
							</xsl:choose>
						</fo:table-cell>
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block>
	</xsl:template>

	<xsl:template name="col15ab">
		<xsl:param name="valuea"/>
		<xsl:param name="valueb"/>

		<fo:block line-height="0.52in">
			<fo:table>
				<fo:table-column column-width="1.5in" padding="4pt"/>
				<fo:table-column column-width="1.8in" padding="4pt"/>
				<fo:table-body>
					<fo:table-row>
						<fo:table-cell vertical-align="middle" >
							<fo:block text-align="center"><xsl:value-of select="$valuea"/></fo:block>
						</fo:table-cell>
						<fo:table-cell vertical-align="bottom" >
							<fo:block text-align="center"><xsl:value-of select="$valueb"/></fo:block>
						</fo:table-cell>
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block>
	</xsl:template>

	<xsl:template name="account_number">
		<xsl:variable name="value" select="./VENDORID"/>
		<xsl:choose>
			<xsl:when test="$value!=\'\'">
				<fo:block vertical-align="top" line-height="0.6in"><xsl:value-of select="$value"/></fo:block>
			</xsl:when>
			<xsl:otherwise>
				<fo:block vertical-align="top" line-height="0.6in" color="white">0</fo:block>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>

    <xsl:template name="tin_details">
        <fo:block-container width="0.6in" left="2.11in" height="0.55in" position="absolute" top="3.40in">
            <fo:block  line-height="0.34in">
                <xsl:variable name="valueFatca" select="./FORM1099AMOUNT/BOXFATCA"/>
                <xsl:choose>
                    <xsl:when test="$valueFatca!=\'\'">
                        <fo:block line-height="0.44in"><xsl:value-of select="$valueFatca"/></fo:block>
                    </xsl:when>
                    <xsl:otherwise>
                        <fo:block color="white" >0</fo:block>
                    </xsl:otherwise>
                </xsl:choose>
            </fo:block>
        </fo:block-container>
        <fo:block-container width="0.6in" left="2.72in" height="0.55in" position="absolute" top="3.30in">
            <fo:block  line-height="0.34in">
                <xsl:variable name="valueTin" select="./FORM1099AMOUNT/BOXTIN"/>
                <xsl:choose>
                    <xsl:when test="$valueTin!=\'\'">
                        <fo:block line-height="0.44in"><xsl:value-of select="$valueTin"/></fo:block>
                    </xsl:when>
                    <xsl:otherwise>
                        <fo:block color="white" >0</fo:block>
                    </xsl:otherwise>
                </xsl:choose>
            </fo:block>
        </fo:block-container>

    </xsl:template>

	<xsl:template name="showentry">
		<xsl:param name="value"/>
		<xsl:choose>
			<xsl:when test="$value != \'\'">
				<xsl:call-template name="doshow">
					<xsl:with-param name="vsize">0.48in</xsl:with-param>
					<xsl:with-param name="value" select="$value"/>
					<xsl:with-param name="color">black</xsl:with-param>
				</xsl:call-template>
			</xsl:when>
			<xsl:otherwise>
				<xsl:call-template name="showentryblank"/>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>

	<xsl:template name="showentryblank">
		<xsl:call-template name="doshow">
			<xsl:with-param name="vsize">0.48in</xsl:with-param>
			<xsl:with-param name="value">0</xsl:with-param>
			<xsl:with-param name="color">white</xsl:with-param>
		</xsl:call-template>
	</xsl:template>

	<xsl:template name="showtextentrysize">
		<xsl:param name="vsize"/>
		<xsl:param name="value"/>
		<xsl:choose>
			<xsl:when test="$value!=\'\'">
			<fo:block vertical-align="top" text-align="end">
				<xsl:attribute name="line-height"><xsl:value-of select="$vsize"/></xsl:attribute>
				<xsl:attribute name="color">black</xsl:attribute>
				<xsl:value-of select="$value"/>
			</fo:block>
			</xsl:when>
			<xsl:otherwise>
				<xsl:call-template name="showentryblanksize">
					<xsl:with-param name="vsize" select="$vsize"/>
				</xsl:call-template>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>

	<xsl:template name="showentrysize">
		<xsl:param name="vsize"/>
		<xsl:param name="value"/>
		<xsl:choose>
			<xsl:when test="$value!=\'\'">
				<xsl:call-template name="doshow">
					<xsl:with-param name="vsize" select="$vsize"/>
					<xsl:with-param name="value" select="$value"/>
					<xsl:with-param name="color">black</xsl:with-param>
				</xsl:call-template>
			</xsl:when>
			<xsl:otherwise>
				<xsl:call-template name="showentryblanksize">
					<xsl:with-param name="vsize" select="$vsize"/>
				</xsl:call-template>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>

	<xsl:template name="showentryblanksize">
		<xsl:param name="vsize"/>
		<xsl:call-template name="doshow">
			<xsl:with-param name="vsize" select="$vsize"/>
			<xsl:with-param name="value">0</xsl:with-param>
			<xsl:with-param name="color">white</xsl:with-param>
		</xsl:call-template>
	</xsl:template>

	<xsl:template name="doshow">
		<xsl:param name="vsize"/>
		<xsl:param name="value"/>
		<xsl:param name="color"/>

		<fo:block vertical-align="top" text-align="end">
			<xsl:attribute name="line-height"><xsl:value-of select="$vsize"/></xsl:attribute>
			<xsl:attribute name="color"><xsl:value-of select="$color"/></xsl:attribute>
			<xsl:value-of select="format-number($value, \'######0.00\')"/>
		</fo:block>
	</xsl:template>

	<xsl:template name="RightColMisc">
		<fo:block-container width="3.75in" height="0.96in" position="absolute" top="-0.03in">
			<fo:block-container width="1.25in" height="0.96in" top="0in">
				<xsl:call-template name="showentry">
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX1"/>
				</xsl:call-template>
				<xsl:call-template name="showentry">
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX2"/>
				</xsl:call-template>
			</fo:block-container>
			<fo:block-container width="2.5in" left="1.25in" height="1.06in" position="absolute" top="0in"/>
		</fo:block-container>

		<fo:block-container width="3.75in" height="3.8in"  top="0.98in">
			<fo:block-container width="1.25in"  height="3.8in" position="absolute" top="-0.01in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.26in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX3"/>
				</xsl:call-template>
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">1.1in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX5"/>
				</xsl:call-template>
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.28in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX7"/>
				</xsl:call-template>
				<xsl:call-template name="showtextentrysize">
					<xsl:with-param name="vsize">0.70in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX9"/>
				</xsl:call-template>
				<xsl:call-template name="showentryblanksize">
					<xsl:with-param name="vsize">0.20in</xsl:with-param>
				</xsl:call-template>
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.54in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX13"/>
				</xsl:call-template>
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.18in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX16"/>
				</xsl:call-template>
			</fo:block-container>

			<fo:block-container width="1.25in" left="1.25in"  height="3.8in" position="absolute"  top="-0.01in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.26in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX4"/>
				</xsl:call-template>
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">1.1in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX6"/>
				</xsl:call-template>
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.28in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX8"/>
				</xsl:call-template>
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.75in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX10"/>
				</xsl:call-template>
				<xsl:call-template name="showentryblanksize">
					<xsl:with-param name="vsize">0.20in</xsl:with-param>
				</xsl:call-template>
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.55in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX14"/>
				</xsl:call-template>
				<fo:block vertical-align="bottom" text-align="end">
					<xsl:attribute name="line-height">0.17in</xsl:attribute>
					<xsl:attribute name="color">black</xsl:attribute>
					<xsl:value-of select="./FORM1099AMOUNT/BOX17"/>
				</fo:block>
			</fo:block-container>

			<fo:block-container width="1.25in" left="2.5in"  height="4in" position="absolute"  top="0in">
				<xsl:call-template name="showentryblanksize">
					<xsl:with-param name="vsize">2.80in</xsl:with-param>
				</xsl:call-template>
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.70in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX18"/>
				</xsl:call-template>
			</fo:block-container>
		</fo:block-container>
	</xsl:template>
</xsl:stylesheet>';
