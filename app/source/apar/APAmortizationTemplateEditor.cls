<?php
/**
 * =============================================================================
 *
 * FILE:          APAmortizationTemplateEditor.cls
 * AUTHOR:        Shone
 * DESCRIPTION:
 *
 * (C)2024 Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 * =============================================================================
 */

/**
 * Editor class for APAmortizationTemplateEditor object
 */
class APAmortizationTemplateEditor extends AmortizationTemplateEditor
{

    /**
     * @param array $_params Initial params
     */
    public function __construct($_params = [])
    {
        parent::__construct($_params);
    }

    /**
     * @return string[]
     */
    protected function getJavaScriptFileNames()
    {
        $jsfiles = [
            '../resources/js/amortization.js',
        ];

        return $jsfiles;
    }

    /**
     * @param array &$obj the data
     *
     * @return bool true on success and false on failure - make sure an error is raised in case of failure
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $view = $this->getView();
        if ( isset($obj['TEMPLATETYPE']) && $obj['TEMPLATETYPE'] != ''
             && $obj['TEMPLATETYPE'] != AmortizationScheduleManager::SL_METHOD ) {
            $view->findAndSetProperty(
                [ 'path' => 'PERIODCOUNT' ],
                [ 'hidden' => true ]
            );
            $view->findAndSetProperty(
                [ 'path' => 'POSTINGDAY' ],
                [ 'hidden' => true ]
            );
        }
        $this->setDefaultJournalForDisplay($obj);

        if ( $this->state == $this->kShowEditState ) {

            $templateFld = [];
            $view->findComponents(array('path' => 'TEMPLATEID'), EditorComponentFactory::TYPE_FIELD, $templateFld);
            if ($templateFld) {
                $templateFld[0]->setProperty('readonly', true);
            }

            $journalFld = [];
            $view->findComponents(array('path' => 'JOURNALID'), EditorComponentFactory::TYPE_FIELD, $journalFld);
            if ($journalFld) {
                $journalFld[0]->setProperty('readonly', true);
            }

            $accountFld = [];
            $view->findComponents(array('path' => 'ACCOUNTID'), EditorComponentFactory::TYPE_FIELD, $accountFld);
            if ($accountFld) {
                $accountFld[0]->setProperty('readonly', true);
            }

            $termFld = [];
            $view->findComponents(array('path' => 'TERM'), EditorComponentFactory::TYPE_FIELD, $termFld);
            if ($termFld) {
                $termFld[0]->setProperty('readonly', true);
            }

            $statusFld = [];
            $view->findComponents(array('path' => 'STATUS'), EditorComponentFactory::TYPE_FIELD, $termFld);
            if ($statusFld) {
                $statusFld[0]->setProperty('readonly', true);
            }
        }

        return true;
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function setDefaultJournalForDisplay(&$obj)
    {
        // if journal is already set, no need to check default journal
        if ( ! empty($obj['JOURNALID']) ) {
            return true;
        }

        $defaultJournal = GetPreferenceForProperty(Globals::$g->kAPid, 'AMORTIZATION_JOURNAL');
        $obj['JOURNALID'] = $defaultJournal;

        return true;
    }
}

