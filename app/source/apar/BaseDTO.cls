<?php

/**
 * Base data transfer object(value object).
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Intacct Corporation All, Rights Reserved
 */
abstract class BaseDTO
{
	/** @var  array  $arrayParams Main array that holds all the parameters for entity */
	protected $arrayParams;

	/**
	 * @return array
	 */
	public function getArrayParams()
	{
		return $this->arrayParams;
	}

	/**
	 * @param array $arrayParams
	 */
	public function setArrayParams($arrayParams)
	{
		$this->arrayParams = $arrayParams;
	}

	/**
	 * Method to get the custom field value.
	 *
	 * @param string $customFieldName name of the custom field
     *
	 * @return mixed the value from array param if the field name exist
	 */
	public function getCustomFieldValue($customFieldName)
	{
        return $this->arrayParams[$customFieldName] ?? null;
    }

	/**
	 * Sets the custom field value for the given custom field name.
	 *
	 * @param string $customFieldName name of the custom field
	 * @param mixed  $customFieldValue value of the custom field
	 */
	public function setCustomFieldValue($customFieldName, $customFieldValue)
	{
		// set the value in array params
        $this->arrayParams[$customFieldName] = $customFieldValue;
	}

    /**
     * Method to get the value from the array params.
     *
     * @param string     $property
     * @param mixed|null $default
     *
     * @return mixed
     */
    protected function _getValue(string $property, mixed $default = null) : mixed
    {
        if (!isset($this->arrayParams[$property]) || $this->arrayParams[$property] === '') {
            return $default;
        }

        return $this->arrayParams[$property];
    }

	/**
	 * Abstract method to implement on building array params based on the object values.
	 *
	 * @return array of parameters and values for the entity
	 */
	abstract public function getValues();
}