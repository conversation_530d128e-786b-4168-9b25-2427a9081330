<?
/**
 *    FILE:            arbatch.ent
 *    AUTHOR:            rpn
 *    DESCRIPTION:    entity for arbatch, extends from prbatch.
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/

require 'prbatch.ent';

$kSchemas['arbatch'] = $kSchemas['prbatch'];
$kSchemas['arbatch']['dbfilters'] = array(
    array('arbatch.recordtype', 'in', array('ra', 'ri'))
);
$kSchemas['arbatch']['printas'] = 'IA.SUMMARY';
$kSchemas['arbatch']['pluralprintas'] = 'IA.SUMMARIES';
$kSchemas['arbatch']['module'] = 'ar';
$kSchemas['arbatch']['autoincrement'] = 'RECORDNO';
