<?php

/**
 * =============================================================================
 *
 * FILE:        APAccountLabelEditor
 * AUTHOR:       
 * DESCRIPTION: 
 *
 * (C)2000,2009 Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 * =============================================================================
 */

/**
 * Editor class for APAccountLabelEditor object
 */
class APAccountLabelEditor extends FormEditor
{

    /**
     * @var array
     */
    protected $additionalTokens = ['IA.FORM1099'];
    /**
     * @param array $_params the parameters of the class
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);
    }

    /**
     * mediateDataAndMetadata
     * 
     * @param array $obj parameters
     * 
     * @return bool true on success and false on failure - make sure an error is raised in case of failure
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        parent::mediateDataAndMetadata($obj);
        if ( $this->state == 'shownew' || TaxSetupManager::hide_1099_Config()) {
            $matches = array();
            $view = $this->getView();
            $view->findComponents(array('path' => 'FORM1099'), EditorComponentFactory::TYPE_FIELD, $matches);
            $matches[0]->setProperty('hidden', true);
        }
        return true;
    }
    
    /**
     * transformBizObjectToView
     *
     * @param array $obj parameters
     *
     * @return bool   true on success and false on failure - make sure an error is raised in case of failure
     */
    protected function transformBizObjectToView(&$obj)
    {
        if ( $this->state == $this->kShowEditState || $this->state == $this->kShowViewState ) {
            $obj['FORM1099'] = '';
            if ( $this->state == $this->kShowEditState ) {
                $_sess = Session::getKey();
                $_op = Request::$r->_op;
                $r = Request::$r->GetCurrentObjectValueByPath('RECORDNO');
                $obj['FORM1099'] = '<a href="edit_retform1099.phtml?.object=accountlabel&.op=' . $_op . '&.r=' . $r . '&.sess=' . $_sess . '" target="_blank">'.GT($this->textMap,'IA.FORM1099').'</a>' . $obj['FORM1099'];
            }
            if ( !empty($obj['FORM1099TYPE']) ) {
                  $placeHolder =  [
                              ['name' => 'FORM1099TYPE', 'value' => $obj['FORM1099TYPE'] ]
                          ];
                $obj['FORM1099'] .=  I18N::getSingleToken("IA.FORM_COLON_1099_PLACEHOLDER",$placeHolder);
            }
        }

        parent::transformBizObjectToView($obj);
        return true;
    }

}

