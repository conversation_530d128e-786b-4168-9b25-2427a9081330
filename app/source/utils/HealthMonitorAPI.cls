<?php
/**
 * Class managing health status checks of the Intacct eco-system
 *
 * @package Utilities
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

require_once 'backend_util.inc';

class HealthMonitorAPI
{
    // Constants used to create company credentials based on convention
    const CONV_COMPANY  = 'IA_XMLGW_TEST_%d';
    const CONV_USER_ID  = 'XML_U_%d';
    const CONV_PASSWORD = 'XML_P_%d';

    // String messages
    const MSG_STATUS_OK         = 'ok';

    // Overall status codes
    const STATUS_OK             = 0;
    const STATUS_CURL_ERR       = 1;
    const STATUS_NO_RESPONSE    = 2;
    const STATUS_HTTP_RESP_ERR  = 3;
    const STATUS_XMLGW_AUTH_ERR = 4;
    const STATUS_COMP_AUTH_ERR  = 5;
    const STATUS_DATA_INVALID   = 6;

    /**
     * The format of all XMLGW requests
     * (%s will substituted with the operation)
     *
     * @var string $requestFormat
     */
    protected $requestFormat;

    /* @var string $url */
    protected $url;
    /* @var string $cuser */
    protected $cuser;
    /* @var string $cpass */
    protected $cpass;
    /* @var int $timeout */
    protected $timeout;
    
    
    /**
     * @param string  $url     target url for the curl requests
     * @param int     $timeout timeout for individual curl requests
     * @param string  $cuser   username to use for all requests
     * @param string  $cpass   password to use for all requests
     * 
     */
    public function __construct($url, $timeout=60, $cuser=null, $cpass=null)
    {
        $this->url = $url;
        $this->timeout = $timeout;
        $this->cuser = $cuser;
        $this->cpass = $cpass;
        
        // set the control senderid and password based on production or development environment
        $control_senderid = GetValueForIACFGProperty('HEALTHCHECK_GW_SENDER_ID');
        $control_password = GetValueForIACFGProperty('HEALTHCHECK_GW_SENDER_PASSWORD', ia_cfg::DECRYPT_KEY);

        // build xml request string to test
        $this->requestFormat = "
            <request>
                <control>
                    <senderid>$control_senderid</senderid>
                    <password>$control_password</password>
                    <controlid>xmlgw_test</controlid>
                    <uniqueid>false</uniqueid>
                    <dtdversion>2.1</dtdversion>
                </control>
                %s
            </request>";
    }
    
    /**
     * Get a human-readable string that represents the status code value.
     * 
     * @param int $statusCode  (enum) one of the various STATUS_* consts of this class
     * 
     * @return string
     */
    public static function getStatusName($statusCode)
    {
        switch ($statusCode) {
            case self::STATUS_OK:
                return 'OK';
            case self::STATUS_CURL_ERR:
                return 'CURL ERR';
            case self::STATUS_NO_RESPONSE:
                return 'NO RESPONSE';
            case self::STATUS_HTTP_RESP_ERR:
                return 'HTTP RESP ERR';
            case self::STATUS_XMLGW_AUTH_ERR:
                return 'XMLGW AUTH';
            case self::STATUS_COMP_AUTH_ERR:
                return 'CNY AUTH';
            case self::STATUS_DATA_INVALID:
                return 'DATA INVALID';
            default:
                return "\<UNKNOWN ERR>";
        }
        
    }

    /**
     * Create an array of batches of [DB Server => DBID]
     *
     * Each batch will contain a DB server at most once.
     * This is to prevent multiple calls to the same DB during a test.
     *
     * Example:
     *
     *   [
     *       1 => [
     *           'dev02' => ['dbid' => 100],
     *           'dev25' => ['dbid' => 1601,
     *       ],
     *       2 => [
     *           'dev02' => ['dbid' => 20],
     *           'dev25' => ['dbid' => 1602],
     *       ],
     *       3 => [
     *           'dev02' => ['dbid' => 15],
     *       ],
     *   ]
     *
     * @return array
     */
    protected static function getDBServerBatch()
    {

        $schemaList = GetSchemaList();
        $serverCount = [];
        $serverBatch = [];

        foreach ( $schemaList as $dbID => $userServer ) {
            list( , $server) = explode('@', $userServer);
            if ( isset($serverCount[$server]) ) {
                $serverCount[$server]++;
            } else {
                $serverCount[$server] = 1;
            }

            $serverBatch[$serverCount[$server]][$server] = ['dbID' => $dbID];
        }

        return $serverBatch;
    }


    /**
     * Create the request list using convention for company/user/password
     *
     * @return array
     */
    public function createRequestListByConvention()
    {
        $serverBatch = self::getDBServerBatch();
        foreach ( $serverBatch as &$batch ) {
            foreach ( $batch as &$data ) {
                $dbID = $data['dbID'];
                $data['coID'] = sprintf(self::CONV_COMPANY, $dbID);
                $data['user'] = $this->cuser ?? sprintf(self::CONV_USER_ID, $dbID);
                $data['pass'] = $this->cpass ?? sprintf(self::CONV_PASSWORD, $dbID);
            }
        }

        return $serverBatch;
    }


    /**
     * Create the request list using convention for company/user/password
     *
     * @return array
     */
    public function createRequestListFromCFG()
    {
        // find the company to test against for healthcheck.
        /** @noinspection PhpUnusedLocalVariableInspection */
        $property = "IA_XMLGW_TEST";

        $credentials = GetValueForIACFGProperty('IA_XMLGW_TEST');

        // Create the server batch array
        $singleBatch = [];
        foreach ( $credentials as $key => $data ) {
            $components = explode("IA_XMLGW_TEST_", $key);
            if ( isset($components[1]) ) {
                $db = $components[1];
            } else {
                throw new Exception("Cannot determine DB server from credentials key '$key'");
            }

            list($coID, $user, $pass) = explode('@', $data);

            $singleBatch[$db] = [
                'dbID' => $db . '_S', // Make up a DBID
                'coID' => $coID,
                'user' => $this->cuser ?? $user,
                'pass' => $this->cpass ?? $pass
            ];
        }
        
        // Since this is only one schema per DB, we only have one batch
        return [1 => $singleBatch];
    }


    /**
     * Generate the full XML request based on company credentials
     *
     * @param string $coID    Company Title
     * @param string $userID  User Login
     * @param string $pwd     Password
     *
     * @return string (xml)
     */
    protected function generateXMLRequest($coID, $userID, $pwd)
    {
        $operation = "
            <operation>
                <authentication>
                    <login>
                        <userid>$userID</userid>
                        <companyid>$coID</companyid>
                        <password>$pwd</password>
                    </login>
                </authentication>
                <content>
                    <function controlid=\"testControlId\">
                        <get externalkey=\"false\" key=\"1000\" object=\"glaccount\"></get>
                    </function>
                </content>
            </operation>";

        return sprintf($this->requestFormat, $operation);
    }

    /**
     * Run all request batches from a list of batches
     *
     * @param array   $requestList  list of request batches to execute
     *
     * @return array (results for all request batches in the list including final status (integer indexed))
     */
    public function runAllRequests($requestList)
    {
        $result = [];
        $final = [
            'status'    => self::STATUS_OK,
            'statusMsg' => self::MSG_STATUS_OK
        ];

        $startTime = microtime(true);
        
        foreach ( $requestList as $i => $batch ) {
            $result[$i] = $this->executeMultiCurl($batch);
            $status = $result[$i]['FINAL']['status'];
            // Change the final status if status is an error and 'worse' than the current final status
            if ( self::STATUS_OK !== $status && (self::STATUS_OK === $final['status'] || $status < $final['status'])) {
                $final['status']    = $status;
                $final['statusMsg'] = $result[$i]['FINAL']['statusMsg'];
            }
        }

        // Compute overall time to run all requests
        $stopTime = microtime(true);
        $final['elapsed'] = (1000 * round($stopTime - $startTime, 3)) . ' msec';
        
        $result['FINAL'] = $final;

        return $result;
    }

    /**
     * Execute a single batch of requests based using curl_multi_exec
     *
     * @param array $requestBatch  A single batch containing multiple databases and authentication data
     * Example:
     *
     *   [
     *       'dev02' => [
     *           'dbid' => 100,
     *           'coID' => 'IA_XMLGW_TEST_100',
     *           'user' => 'XML_U_100',
     *           'pass' => 'XML_P_100',
     *       ],
     *       'dev25' => [
     *           'dbid' => 1601,
     *           'coID' => 'IA_XMLGW_TEST_1601',
     *           'user' => 'XML_U_1601',
     *           'pass' => 'XML_P_1601',
     *       ],
     *   ]
     *
     * @return array (Structure containing results for each element in the batch, and final status)
     *
     * Example:
     *
     *   [
     *       'FINAL' =>
     *       [
     *           'status'    => 0           // (self::STATUS_OK)
     *           'statusMsg' => 'OK',
     *       ]
     *       100 =>
     *       [
     *           'content' => "<?xml ...",
     *           'result'  => "OK",
     *           'elapsed' => "325 msec"
     *       ],
     *       1601 =>
     *       [
     *           'content' => "<?xml ...",
     *           'result'  => 'curl error (28): Operation exceeded timeout (60 seconds).',
     *           'elapsed' => '60001 msec',
     *       ]
     *   ]
     *
     */
    protected function executeMultiCurl($requestBatch)
    {
        // Final result structure returned by this function
        $resultStruct = [];

        // Contains the curl handles and curl results
        $curlKeys = [];

        foreach ( $requestBatch as $server => $data ) {
            // Generate the curl handle
            $ch = self::initCurlHandle($this->url, $this->timeout);

            // Generate the xml request and set it into the curl handle
            $req = $this->generateXMLRequest($data['coID'], $data['user'], $data['pass']);
            curl_setopt($ch, CURLOPT_POSTFIELDS, "xmlrequest=" . urlencode($req));

            // Add this curl handle to the array (for later multi exec)
            $resultStruct[$data['dbID']] = [
                'chint'  => intval($ch),
                'server' => $server,
                'coID'   => $data['coID'],
            ];
            $curlKeys[intval($ch)] = ['ch' => $ch];
        }

        // Initialize the multi_exec handle
        $mh = curl_multi_init();

        foreach ( $curlKeys as $cstruct ) {
            curl_multi_add_handle($mh, $cstruct['ch']);
        }

        $final = [
            'status'    => self::STATUS_OK,
            'statusMsg' => self::MSG_STATUS_OK
        ];

        // Execute the multi curl
        $running = 0;
        $startTime = microtime(true);
        do {
            $curlMERes = curl_multi_exec($mh, $running);
            if ( $running ) {
                curl_multi_select($mh, 0.02);
            }
            $stopTime = microtime(true);

            // Gather information about the curl requests finished so far
            while ( $info = curl_multi_info_read($mh) ) {
                if ( CURLMSG_DONE === $info['msg'] ) {
                    $ch = $info['handle'];
                    $chint = intval($ch);
                    $curlRes = $info['result'];

                    // If there is an error, show the result
                    if ( CURLE_OK !== $curlRes ) {
                        if ( CURLE_OPERATION_TIMEOUTED == $curlRes ) {
                            /** @noinspection PhpUndefinedVariableInspection */
                            $curlResMsg = "Operation exceeded timeout ($timeout seconds).";
                        } else {
                            $curlResMsg = curl_error($ch);
                        }
                        $resultMsg = "curl error ($curlRes): " . $curlResMsg;
                    } else {
                        $resultMsg = self::MSG_STATUS_OK;
                    }
                    
                    $curlKeys[$chint] += [
                        'elapsed'    => (1000 * round($stopTime - $startTime, 3)) . ' msec',
                        'statusMsg'  => $resultMsg,
                        'curlResult' => $curlRes,
                    ];
                }
            }
        } while ($curlMERes === CURLM_CALL_MULTI_PERFORM || $running > 0);

        // Compute time elapsed for the entire curl multiple execution
        $stopMHTime = microtime(true);
        $final['elapsed'] = (1000 * round($stopMHTime - $startTime, 3)) . ' msec';

        // Get the curl error from the multi handle
        if ( CURLE_OK !== $curlMERes ) {
            $final['status'] = self::STATUS_CURL_ERR;
            $final['statusMsg'] = "multi-exec curl error ($curlMERes): " . curl_error($mh);
        }

        // close the handles and gather content result
        foreach ( $resultStruct as &$result ) {
            $curlKey = $curlKeys[$result['chint']];
            $ch = $curlKey['ch'];
            $content    = curl_multi_getcontent($ch);
            $result['curlResult'] = $curlKey['curlResult'];
            $result['elapsed']    = $curlKey['elapsed'];
            curl_multi_remove_handle($mh, $ch);
            curl_close($ch);
            unset($result['chint']);

            // Verify the xmlgw response
            if ( CURLE_OK === $result['curlResult'] ) {
                $errorMsg = null;
                $result['status'] = $this->verifyXMLResponse($content, $errorMsg );
                if ( self::STATUS_OK !== $result['status']) {
                    $result['statusMsg'] = $errorMsg;
                } else {
                    $result['statusMsg'] = self::MSG_STATUS_OK;
                }
            } else {
                $result['statusMsg']  = $curlKey['statusMsg'];
                $result['status'] = self::STATUS_CURL_ERR;
            }

            // Change the final status if status is an error and 'worse' than the current final status
            if ( self::STATUS_OK === $final['status'] || $result['status'] < $final['status']) {
                $final['status'] = $result['status'];
                $final['statusMsg'] = $result['statusMsg'];
            }

        }
        
        $resultStruct['FINAL'] = $final;

        curl_multi_close($mh);
        
        return $resultStruct;
    }

    /**
     * @param string $content
     *
     * @return string
     */
    protected static function parseHTMLResponseIntoErrMsg($content)
    {
        $dom = new DOMDocument();
        $dom->loadHTML($content);
        $errorMsg = strtok($dom->textContent, "\n");
        $elipses = strlen($errorMsg) > 20 ? '...' : '';
        return "Unexpected response from server: '" . substr($errorMsg, 0, 20) . "$elipses'";
    }

    /**
     * @param int         $content
     * @param string|null &$errorMsg
     *
     * @return int
     */
    protected function verifyXMLResponse($content, &$errorMsg)
    {
        $errorMsg = null;

        if ( empty($content) ) {
            $errorMsg = 'Empty response from server';
            return self::STATUS_NO_RESPONSE;
        }

        $domResponse = xmlstr2dom($content);
        if ( $domResponse && is_array($domResponse) ) {
            if ( !isset($domResponse['control'][0]['status'][0]['cdata']) ) {
                $errorMsg = self::parseHTMLResponseIntoErrMsg($content);
                return self::STATUS_HTTP_RESP_ERR;
            } else if ($domResponse['control'][0]['status'][0]['cdata'] != 'success') {
                $error_desc1 = $domResponse['errormessage'][0]['error'][0]['description'][0]['cdata'];
                $error_desc2 = $domResponse['errormessage'][0]['error'][0]['description2'][0]['cdata'];
                $errorMsg = 'Failed xmlgw authentication: ' . $error_desc1 . "/" . $error_desc2;
                return self::STATUS_XMLGW_AUTH_ERR;
            } else {
                foreach ( $domResponse['operation'] as $op ) {
                    $company_id = $op['authentication'][0]['companyid'][0]['cdata'];
                    if ( $op['authentication'][0]['status'][0]['cdata'] != 'success' ) {
                        $error_desc1 = $op['errormessage'][0]['error'][0]['description'][0]['cdata'];
                        $error_desc2 = $op['errormessage'][0]['error'][0]['description2'][0]['cdata'];
                        $errorMsg = "Failed company authentication ['$company_id']: " . $error_desc1 . "/" . $error_desc2;
                        return self::STATUS_COMP_AUTH_ERR;
                    } else if ($op['result'][0]['data'][0]['glaccount'][0]['glaccountno'][0]['cdata'] != '1000' ) {
                        $errorMsg = 'Failed data verification, expected gl account 1000, actual value: ' . pp($op['result'][0]['data'][0]['glaccount'][0]['glaccountno'][0]['cdata']);
                        return self::STATUS_DATA_INVALID;
                    }
                }
            }
        } else {
            $errorMsg = self::parseHTMLResponseIntoErrMsg($content);
            return self::STATUS_HTTP_RESP_ERR;
        }

        return self::STATUS_OK;
    }

    /**
     * @param string $url
     * @param int    $timeout
     *
     * @return resource
     */
    public static function initCurlHandle($url, $timeout=60)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);

        $headers = Util::computeSpecialHeaders();
        if ( ! empty($headers) ) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }
        // For debugging purposes
        $cookie = Util::computeDevCookie();
        if ($cookie != '') {
            curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        }

        return $ch;
    }
}
