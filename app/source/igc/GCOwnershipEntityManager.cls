<?php

/**
 *    FILE:            GCOwnershipEntityManager.cls
 *    AUTHOR:          Nithin MG <<EMAIL>>
 *    DESCRIPTION:     File GCOwnershipEntityManager.cls contains implementation of Consolidation ownership entity manager
 *
 *    (C) 2008, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

/**
 * Class GCOwnershipEntityManager
 */
class GCOwnershipEntityManager extends OwnedObjectManager
{
    /** @var array $modifiedRecords */
    protected $modifiedRecords;
    
    /**
     * __construct
     *
     * @param array $params
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
        $this->modifiedRecords = [];
    }

    /**
     * Here's a hook to allow the subclass to transform the inbound values
     * to a suitable PHP structure.  The API integration point is responsible
     * for formatting values -- Objects are responsible for formatting structures
     *
     * @param array $values Inbound object structure
     *
     * @return array formatted structure
     */
    public function API_FormatObject($values)
    {
        $newValues = $values;
        unset($newValues['GCOWNERSHIPCHILDENTITIES']);

        if (is_array($values['GCOWNERSHIPCHILDENTITIES']['GCOWNERSHIPCHILDENTITY'][0])) {
            $newValues['GCOWNERSHIPCHILDENTITIES'] = $values['GCOWNERSHIPCHILDENTITIES']['GCOWNERSHIPCHILDENTITY'];
        } else {
            if (isset($values['GCOWNERSHIPCHILDENTITIES']['GCOWNERSHIPCHILDENTITY'])) {
                $newValues['GCOWNERSHIPCHILDENTITIES']
                    = array($values['GCOWNERSHIPCHILDENTITIES']['GCOWNERSHIPCHILDENTITY']);
            }
        }

        return $newValues;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $ok = true;
        if (isset($values['BOOK']['BOOKID']) && !isset($values['BOOK']['BOOKKEY'])) {
            $ok = $this->createConsolidationBook($values);
        }

        $ok = $ok && parent::regularAdd($values);

        return $ok;

    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $ok = true;
        if (isset($values['BOOK']['BOOKID']) && $values['BOOK']['BOOKID'] != '') {
            if (isset($values['BOOK']['BOOKKEY']) && $values['BOOK']['BOOKKEY'] != '') {
                $ok = $this->updateConsolidationBook($values);
            } else {
                $ok = $this->createConsolidationBook($values);
            }
        }
        $ok = $ok && $this->recordsToUpdate($values);
        $ok = $ok && parent::regularSet($values);

        return $ok;

    }

    /**
     * @param array $values
     *
     * @return bool
     */
    private function recordsToUpdate($values)
    {
        $this->modifiedRecords = $values['MODIFIED_CHILD_RECORDS'];
        unset($values['MODIFIED_CHILD_RECORDS']);

        return true;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    public function validateNTranslate(&$values)
    {
        $ok = true;
        $gError = Globals::$g->gErr;
        $entities = [];
        $parentEntityID = '';
        $parentEntity = '';
        $nciAccounts = [];

        if (!isset($values['PARENTENTITYID']) || $values['PARENTENTITYID'] == '') {
            $gError->addError(
                'IGC-0005', __FILE__ . ":" . __LINE__,'',
                "Parent entity is required"
            );
            $ok = false;
        } else {
            $parentEntity = $values['PARENTENTITYID'];
            list($entityID) = explode("--", $parentEntity);
            $entities[$entityID] = $entityID;
            $parentEntityID = $entityID;
        }
        //is structure state activated
        $isStateActivated = $values['STATE'] == GCOwnershipStructureDetailManager::STATE_ACTIVATED;
        // Validate book
        if ($isStateActivated && $values['BOOK']['BOOKID'] == '') {
            $gError->addIAError(
                'IGC-0006', __FILE__ . ":" . __LINE__, '', [],
                "Book is missing for entity : " . $values['PARENTENTITYID'],
                ['PARENT_ENTITY_ID' => $values['PARENTENTITYID']]
            );
            $ok = false;
        }

        foreach ($values['GCOWNERSHIPCHILDENTITIES'] as &$value) {
            $subsidiaryEntity = '';

            // Validate Entity ID
            if (!isset($value['ENTITYID']) || $value['ENTITYID'] == '') {
                $gError->addIAError(
                    'IGC-0007', __FILE__ . ":" . __LINE__,'',[],
                    "Subsidiary entity is required under Parent entity : $parentEntity",['PARENT_ENTITY' => $parentEntity]
                );
                $ok = false;
            } else {
                $subsidiaryEntity = $value['ENTITYID'];
                list($entityID) = explode("--", $subsidiaryEntity);
                if (isset($entities[$entityID])) {
                    if ($entityID == $parentEntityID) {
                        $gError->addIAError(
                            'IGC-0008', __FILE__ . ":" . __LINE__, '', [],
                            "Subsidiary entity : $subsidiaryEntity cannot be same as Parent entity : $parentEntity",
                            ['SUBSIDIARY_ENTITY' => $subsidiaryEntity, 'PARENT_ENTITY' => $parentEntity]
                        );
                        $ok = false;
                    } else {
                        $gError->addIAError(
                            'IGC-0009', __FILE__ . ":" . __LINE__,'',[],
                            "Duplicate Subsidiary entity $subsidiaryEntity found under Parent entity : $parentEntity",
                            ['SUBSIDIARY_ENTITY' => $subsidiaryEntity, 'PARENT_ENTITY' => $parentEntity]
                        );
                        $ok = false;
                    }
                } else {
                    $entities[$entityID] = $entityID;
                }
            }

            // Validate Ownership Percentage
            if (!isset($value['OWNERSHIPPERCENTAGE']) || $value['OWNERSHIPPERCENTAGE'] == '') {
                if ($isStateActivated) {
                    $gError->addIAError(
                        'IGC-0010', __FILE__ . ":" . __LINE__, '', [],
                        "Ownership percentage is required for Subsidiary entity : $subsidiaryEntity under 
                    Parent entity : $parentEntity",
                        ['SUBSIDIARY_ENTITY' => $subsidiaryEntity, 'PARENT_ENTITY' => $parentEntity]
                    );
                    $ok = false;
                }
            } else {
                if (($value['OWNERSHIPPERCENTAGE']) == 0
                    && ($isStateActivated)
                ) {
                    $gError->addIAError(
                        'IGC-0011', __FILE__ . ":" . __LINE__, '', [],
                        "Ownership percentage cannot be 0 when activated state for Subsidiary entity : 
                            $subsidiaryEntity under Parent entity : $parentEntity. Should be between 0 and 100",
                        ['SUBSIDIARY_ENTITY' => $subsidiaryEntity, 'PARENT_ENTITY' => $parentEntity]
                    );
                    $ok = false;
                }
                $ownershipPercent = $value['OWNERSHIPPERCENTAGE'];
                if (!is_numeric($ownershipPercent)) {
                    $gError->addIAError(
                        'IGC-0012', __FILE__ . ":" . __LINE__, '', [],
                        "Invalid Ownership percentage : $ownershipPercent for Subsidiary entity : 
                        $subsidiaryEntity under Parent entity : $parentEntity",
                        [
                            'OWNERSHIP_PERCENT' => $ownershipPercent, 'SUBSIDIARY_ENTITY' => $subsidiaryEntity,
                            'PARENT_ENTITY' => $parentEntity
                        ]
                    );
                    $ok = false;
                } else {
                    if ($ownershipPercent < 0 || $ownershipPercent > 100) {
                        $gError->addIAError(
                            'IGC-0013', __FILE__ . ":" . __LINE__, '', [],
                            "Invalid Ownership percentage : $ownershipPercent for Subsidiary entity : 
                            $subsidiaryEntity under Parent entity : $parentEntity. Should be between 0 and 100",
                            [
                                'OWNERSHIP_PERCENT' => $ownershipPercent, 'SUBSIDIARY_ENTITY' => $subsidiaryEntity,
                                'PARENT_ENTITY' => $parentEntity
                            ]
                        );
                        $ok = false;
                    }
                    if ($value['CONSOLIDATIONMETHOD'] == GCOwnershipChildEntityManager::FULL_CONSOLIDATION_VALUE
                        && $ownershipPercent != 100
                    ) {
                        $gError->addIAError(
                            'IGC-0014', __FILE__ . ":" . __LINE__, '', [],
                            "Invalid Consolidation Method : " . $value['CONSOLIDATIONMETHOD']
                            . " for Ownership percentage less than 100. Please update ownership percentage to 100
                             OR change the consolidation method to consolidation/proportional ",
                            ['CONSOLIDATION_METHOD' => $value['CONSOLIDATIONMETHOD']]
                        );
                        $ok = false;
                    }
                    
                    if ($value['CONSOLIDATIONMETHOD'] != GCOwnershipChildEntityManager::FULL_CONSOLIDATION_VALUE
                        && $ownershipPercent == 100
                    ) {
                        $gError->addIAError(
                            'IGC-0015', __FILE__ . ":" . __LINE__, '', [],
                            "Invalid Consolidation Method : " . $value['CONSOLIDATIONMETHOD']
                            . " for Ownership percentage equal to 100. Please update ownership percentage to less
                             than 100 OR change the consolidation method to full consolidation",
                            ['CONSOLIDATION_METHOD' => $value['CONSOLIDATIONMETHOD']]
                        );
                        $ok = false;
                    }
                }
            }
            //validate consolidatin method 
            if (!isset($value['CONSOLIDATIONMETHOD'])
                || !in_array(
                    $value['CONSOLIDATIONMETHOD'], [
                        GCOwnershipChildEntityManager::FULL_CONSOLIDATION_VALUE,
                        GCOwnershipChildEntityManager::PROPORTIONAL_VALUE,
                        GCOwnershipChildEntityManager::CONSOLIDATION_VALUE
                    ]
                )
            ) {
                $gError->addError(
                    'IGC-0016', __FILE__ . ":" . __LINE__,'',
                    "Invalid Consolidation Method : Please add a valid consolidation method."
                );
                $ok = false;
            }

            // to get the record no for the required account in $accountDetails
            // get all nci accounts to pass in getRecordNoFromAccountNumber below
            if ($value['CONSOLIDATIONMETHOD'] == GCOwnershipChildEntityManager::CONSOLIDATION_VALUE) {
                $nciAccounts[] = $value['INVESTMENTINSUBSACCT'];
                $nciAccounts[] = $value['SUBSIDIARYREVENUEACCT'];
                $nciAccounts[] = $value['NETINCOMEATTRIBUTABLETONCI'];
                $nciAccounts[] = $value['EQUITYATTRIBUTABLETONCI'];
                $nciAccounts[] = $value['CONTRIBUTEDCAPITALACCT'];
            }else{
                if (!isset($value['NCILINKS']) && ( $value['INVESTMENTINSUBSACCT']
                    || $value['SUBSIDIARYREVENUEACCT']
                    || $value['NETINCOMEATTRIBUTABLETONCI']
                    || $value['EQUITYATTRIBUTABLETONCI']
                    || $value['CONTRIBUTEDCAPITALACCT'] )
                ) {
                    $gError->addError(
                        'IGC-0017', __FILE__ . ":" . __LINE__,'',
                        "The selected consolidation method does not use non-controlling interest accounts. 
                        Change the consolidation method to Consolidation or remove the NCI accounts."
                    );
                    $ok = false;
                }
            }
        }

        // Validate the entities
        $entitiesMap = $this->getEntityDetails($entities);
        if ($entitiesMap[$parentEntityID]) {
            $values['PARENTENTITYKEY'] = $entitiesMap[$parentEntityID]['RECORD#'];
        } else {
            $gError->addIAError(
                'IGC-0018', __FILE__ . ":" . __LINE__,'',[],
                "Invalid Parent entity : " . $values['PARENTENTITYID'],
                ['PARENT_ENTITY_ID' => $values['PARENTENTITYID']]

            );
            $ok = false;
        }

        //to get record no. for account number
        $accountDetails = $this->getRecordNoByAccountNumber($nciAccounts);
        //IA-61621 we should block user from setting same investment account for  different subsidiaries under same parent.
        $investInSubsAcct = [];
        $isEliminateByAffiliateEntity = ($values['ELIMINATEBYAFFILIATEENTITY']) == 'true' ? true : false;
        foreach ($values['GCOWNERSHIPCHILDENTITIES'] as &$value) {
            // validate nci accounts only when consolidation method is 'consolidation'
            $isConsolidationMethodSelected = isset($value['CONSOLIDATIONMETHOD'])
                && $value['CONSOLIDATIONMETHOD'] == GCOwnershipChildEntityManager::CONSOLIDATION_VALUE;

            if ($isConsolidationMethodSelected) {
                $ok = $ok
                    && $this->validateNCIAccounts(
                        $value, $accountDetails, $investInSubsAcct, $isStateActivated, $parentEntityID,
                        $isEliminateByAffiliateEntity
                    );
            } else {
                // In the application, ISALLOCATESUBSIDIARYINCOME is a checkbox, the DOM returns only true or false
                // but in the table we need to store it as blank when method is not consolidation
                $value['ISALLOCATESUBSIDIARYINCOME']
                    = $value[':subsidiaryrevenueacctkey']
                    = $value[':investmentinsubacctkey']
                    = $value[':netincomenciacctkey']
                    = $value[':retainedearnnciacctkey'] = $value[':contributedcapacctkey'] = null;
            }

            if ($entitiesMap[$value['ENTITYID']]) {
                $value['ENTITYKEY'] = $entitiesMap[$value['ENTITYID']]['RECORD#'];
            } else {
                $gError->addIAError(
                    'IGC-0028', __FILE__ . ":" . __LINE__, '', [],
                    "Invalid Subsidiary entity : " . $value['ENTITYID'] . " under Parent entity : "
                    . $values['PARENTENTITYID'],
                    ['ENTITY_ID' => $value['ENTITYID'], 'PARENT_ENTITY_ID' => $values['PARENTENTITYID']]
                );
                $ok = false;
            }
        }

        return $ok;
    }

    /**
     * @param array   $value
     * @param array   $accountDetails
     * @param array   $investInSubsAcct
     * @param boolean $isStateActivated
     * @param string  $parentEntityID
     * @param bool  $isEliminateByAffiliateEntity
     *
     * @return bool
     * @throws IAException
     */
    private function validateNCIAccounts(
        &$value, $accountDetails, &$investInSubsAcct, $isStateActivated, $parentEntityID,
        $isEliminateByAffiliateEntity = false
    )
    {
        $gError = Globals::$g->gErr;
        $ok = true;

        // When consolidation method is selected then ISALLOCATESUBSIDIARYINCOME should be true/false and cannot be null
        if (!in_array($value['ISALLOCATESUBSIDIARYINCOME'], ['true', 'false'])) {
            $msg
                = "Allocate subsidiary income to parent entity cannot be blank for consolidation method in subsidiary entity : "
                . $value['ENTITYID'] . " under parent entity : " . $parentEntityID;
            $gError->addIAError(
                'IGC-0312', __FILE__ . ":" . __LINE__, '', [], $msg,
                ['SUBSIDIARY_ENTITY_ID' => $value['ENTITYID'], 'PARENT_ENTITY_ID' => $parentEntityID]
            );
            return false;
        }

        if ($value['NETINCOMEATTRIBUTABLETONCI'] != '') {
            $invinSubsAcct = $accountDetails[$value['NETINCOMEATTRIBUTABLETONCI']] ?? null;
            if (!isset($invinSubsAcct)) {
                $gError->addIAError('IGC-0023', __FILE__ . ':' . __LINE__, '', [],
                    "Could not find specified Net income attributable to NCI in subsidiary entity :"
                    . $value['ENTITYID'] . " under parent entity : " . $parentEntityID,
                    ['SUBSIDIARY_ENTITY_ID' => $value['ENTITYID'], 'PARENT_ENTITY_ID' => $parentEntityID]
                );
                return false;
            }
            $value[':netincomenciacctkey'] = $invinSubsAcct;
        } else {
            if ($isStateActivated) {
                $gError->addIAError(
                    'IGC-0024', __FILE__ . ':' . __LINE__, '', [],
                    "Net income attributable to NCI is required for subsidiary entity : " . $value['ENTITYID']
                    . " under parent entity : " . $parentEntityID,
                    ['SUBSIDIARY_ENTITY_ID' => $value['ENTITYID'], 'PARENT_ENTITY_ID' => $parentEntityID]
                );
                $ok = false;
            }
        }

        if ($value['EQUITYATTRIBUTABLETONCI'] != '') {
            $invinSubsAcct = $accountDetails[$value['EQUITYATTRIBUTABLETONCI']] ?? null;
            if (!isset($invinSubsAcct)) {
                $gError->addIAError(
                    'IGC-0025', __FILE__ . ':' . __LINE__, '', [],
                    "Could not find specified Equity attributable to NCI in subsidiary entity : "
                    . $value['ENTITYID'] . " under parent entity : " . $parentEntityID,
                    ['SUBSIDIARY_ENTITY_ID' => $value['ENTITYID'], 'PARENT_ENTITY_ID' => $parentEntityID]
                );
                return false;
            }
            $value[':retainedearnnciacctkey'] = $invinSubsAcct;
        } elseif ($isStateActivated) {
            $gError->addIAError(
                'IGC-0026', __FILE__ . ':' . __LINE__, '', [],
                "Equity attributable to NCI is required for subsidiary entity : " . $value['ENTITYID']
                . " under parent entity : " . $parentEntityID,
                ['SUBSIDIARY_ENTITY_ID' => $value['ENTITYID'], 'PARENT_ENTITY_ID' => $parentEntityID]
            );
            $ok = false;
        }

        // Validate below accounts only when ISALLOCATESUBSIDIARYINCOME is enabled
        if ($value['ISALLOCATESUBSIDIARYINCOME'] == 'true') {
            if ($value['INVESTMENTINSUBSACCT'] != '') {
                $invinSubsAcct = $accountDetails[$value['INVESTMENTINSUBSACCT']] ?? null;
                if (!isset($invinSubsAcct)) {
                    $gError->addIAError(
                        'IGC-0019', __FILE__ . ':' . __LINE__, '', [],
                        "Could not find specified Investment in subsidiary account in subsidiary entity : "
                        . $value['ENTITYID'] . " under parent entity : " . $parentEntityID,
                        ['SUBSIDIARY_ENTITY_ID' => $value['ENTITYID'], 'PARENT_ENTITY_ID' => $parentEntityID]
                    );
                    return false;
                }

                //checking if the older investinsubsacc and new investinsubs is repeated in more than 1 child
                //added the index in quotes to maintain the actual number otherwise it will convert e.g : 1.2=>1
                // Validate this case only when Eliminate by Affiliate entity is not selected 
                if (!$isEliminateByAffiliateEntity && isset($investInSubsAcct["$invinSubsAcct"])) {
                    $msg = "Investment in subsidiary account " . $value['INVESTMENTINSUBSACCT']
                        . " is being used for child entity " . $investInSubsAcct["$invinSubsAcct"]
                        . " under the same parent entity ($parentEntityID). 
                            Select another investment in subsidiary account for this child entity.";
                    $gError->addIAError(
                        'IGC-0309', __FILE__ . ':' . __LINE__, $msg, [
                            'INVESTMENT_IN_SUBSIDIARY_NUMBER' => $value['INVESTMENTINSUBSACCT'],
                            'PARENT_ENTITY' => $parentEntityID,
                            'CHILD_ENTITY' => $investInSubsAcct["$invinSubsAcct"]
                        ]
                    );
                    return false;
                } else {
                    $investInSubsAcct["$invinSubsAcct"] = $value['ENTITYID'];
                }

                $value[':investmentinsubacctkey'] = $invinSubsAcct;
            } elseif ($isStateActivated) {
                $gError->addIAError(
                    'IGC-0020', __FILE__ . ":" . __LINE__, '', [],
                    "Investment in subsidiary account is required for subsidiary entity : " . $value['ENTITYID']
                    . " under parent entity : " . $parentEntityID,
                    ['SUBSIDIARY_ENTITY_ID' => $value['ENTITYID'], 'PARENT_ENTITY_ID' => $parentEntityID]
                );
                $ok = false;
            }

            if ($value['SUBSIDIARYREVENUEACCT'] != '') {
                $invinSubsAcct = $accountDetails[$value['SUBSIDIARYREVENUEACCT']] ?? null;
                if (!isset($invinSubsAcct)) {
                    $gError->addIAError(
                        'IGC-0021', __FILE__ . ':' . __LINE__, '', [],
                        "Could not find specified Subsidiary revenue account in subsidiary entity : "
                        . $value['ENTITYID'] . " under parent entity : " . $parentEntityID,
                        ['SUBSIDIARY_ENTITY_ID' => $value['ENTITYID'], 'PARENT_ENTITY_ID' => $parentEntityID]
                    );
                    return false;
                }
                $value[':subsidiaryrevenueacctkey'] = $invinSubsAcct;
            } elseif ($isStateActivated) {
                $gError->addIAError(
                    'IGC-0022', __FILE__ . ':' . __LINE__, '', [],
                    "Subsidiary revenue account is required for subsidiary entity : "
                    . $value['ENTITYID'] . " under parent entity : " . $parentEntityID,
                    ['SUBSIDIARY_ENTITY_ID' => $value['ENTITYID'], 'PARENT_ENTITY_ID' => $parentEntityID]
                );
                $ok = false;
            }

            if ($value['CONTRIBUTEDCAPITALACCT'] != '') {
                $invinSubsAcct = $accountDetails[$value['CONTRIBUTEDCAPITALACCT']] ?? null;
                if (!isset($invinSubsAcct)) {
                    $gError->addIAError(
                        'IGC-0027', __FILE__ . ':' . __LINE__, '', [],
                        "Could not find specified Contributed capital account in subsidiary entity : "
                        . $value['ENTITYID'] . " under parent entity : " . $parentEntityID,
                        ['SUBSIDIARY_ENTITY_ID' => $value['ENTITYID'], 'PARENT_ENTITY_ID' => $parentEntityID]
                    );
                    return false;
                }
                $value[':contributedcapacctkey'] = $invinSubsAcct;
            }
        } else {
            // Customers can send other account via api when ISALLOCATESUBSIDIARYINCOME is not selected
            // we don't need to validate it but store it as blank in the table
            $value[':investmentinsubacctkey']
                = $value[':subsidiaryrevenueacctkey'] = $value[':contributedcapacctkey'] = null;
        }

        return $ok;
    }

    /**
     *
     * Get the record number for the corresponding account number passed in $nciAccounts
     *
     * @param array $nciAccounts
     *
     * @return array
     */
    protected function getRecordNoByAccountNumber($nciAccounts): array
    {
        $accountData = [];
        if (!empty($nciAccounts)) {
            $glAccountMgr = Globals::$g->gManagerFactory->getManager('glaccount');
            // Get the nciaccount record#
            $params = [
                'selects' => ['RECORDNO', 'ACCOUNTNO'],
                'filters' => [[['ACCOUNTNO', 'IN', $nciAccounts]]],
            ];

            $accountData = $glAccountMgr->GetList($params);
            foreach ($accountData as $account) {
                $accountData[$account['ACCOUNTNO']] = $account['RECORDNO'];
            }
        }
        return $accountData;
    }
    
    /**
     * Get book details for the parent
     *
     * @param string $structureName
     * @param string $parentEntity
     * @return array
     */
    public function getParentEntityConsolidationBook($structureName, $parentEntity) {
        // Get entity id
        list($entityID) = explode('--', $parentEntity);

        $params['filters'][0][] = array('PARENTENTITYID','=', $entityID);
        $params['filters'][0][] = array('STRUCTURENAME','=',$structureName);
        // in order to get the record of bookey with bookid as first and null at last,
        // since we are only taking first result value, order by bookkey will give notnull bookkey entry first
        $params['orders'][] = ['BOOKKEY'];
        $result = $this->GetList($params);
        $result = $result[0];

        $bookValues = [];
        $bookValues['BOOKID'] = $result['BOOK.BOOKID'] ?: '';
        $bookValues['BOOKDESCRIPTION'] = $result['BOOK.BOOKDESCRIPTION'] ?: '';
        // If no books are fetched then auto set currency to entity base currency
        $bookValues['CURRENCY'] = $result['BOOK.CURRENCY'] ?: GetLocationBaseCurrency($entityID);
        $bookValues['CTANETASSETACCOUNTNO'] = $result['BOOK.CTANETASSETACCOUNTNO'] ?: '';
        $bookValues['CTANETINCOMEACCOUNTNO'] = $result['BOOK.CTANETINCOMEACCOUNTNO'] ?: '';
        $bookValues['BSTRANMETHOD'] = $result['BOOK.BSTRANMETHOD'] ?: '';
        $bookValues['ISTRANMETHOD'] = $result['BOOK.ISTRANMETHOD'] ?: '';
        $bookValues['EENAME'] = $result['BOOK.EENAME'] ?: '';
        $bookValues['ELIMINATIONADJACCT'] = $result['BOOK.ELIMINATIONADJACCT'] ?: '';

        return $bookValues;
    }

    /**
     * @param string[] $locNos
     *
     * @return array
     */
    private function getEntityDetails($locNos)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $locEntityMgr = $gManagerFactory->getManager('location');
        $params = array(
            'selects' => array('RECORD#', 'LOCATIONID'),
            'filters' => array(
                array(
                    array('LOCATIONID', 'IN', $locNos),
                    array('LOCATIONTYPE', '=', 'E'),
                ),
            ),
        );

        $result = $locEntityMgr->GetList($params);

        $locMap = array();
        foreach ($result as $loc) {
            $locMap[$loc['LOCATIONID']] = $loc;
        }

        return $locMap;
    }

    /**
     * The function return true for all the line item that need to be updated
     *
     * @param array &$line line items
     *
     * @return bool
     */
    protected function needToUpdateLine(&$line)
    {
        if (isset($this->modifiedRecords[$line['RECORDNO']])) {
            return true;
        }

        return false;
    }

    /**
     * @param array $raw
     *
     * @return bool
     */
    protected function beforeDelete(&$raw)
    {
        $ok = parent::beforeDelete($raw);

        $gManagerFactory = Globals::$g->gManagerFactory;
        $structDetailMgr = $gManagerFactory->getManager('gcownershipstructuredetail');
        $params = array(
            'selects' => array('STATE', 'FROMPERIOD'),
            'filters' => array(
                array(
                    array('RECORDNO', '=', $raw[0]['GCOWNERSHIPDETAILKEY']),
                ),
            ),
        );

        $result = $structDetailMgr->GetList($params);
        $result = $result[0];

        if ($result['STATE'] != GCOwnershipStructureDetailManager::STATE_DRAFT) {
            $gError = Globals::$g->gErr;
            $gError->addError(
                'IGC-0029', __FILE__ . ":" . __LINE__,'',
                "Cannot delete parent entity in activated or review state. 
                Change to draft state and delete."
            );

            $ok = false;

        }

        return $ok;
    }


    /**
     * @param array $raw
     *
     * @return bool
     */
    protected function afterDelete(&$raw)
    {
        $ok = parent::afterDelete($raw);
        $gManagerFactory = Globals::$g->gManagerFactory;
        $gcBookMgr = $gManagerFactory->getManager('gcbook');
        $result = [];

        if ($raw[0]['BOOKKEY'] != '') {
            $params = array(
                'selects' => array('BOOKID'),
                'filters' => array(
                    array(
                        array('RECORDNO', '=', $raw[0]['BOOKKEY']),
                    ),
                ),
            );

            $result = $gcBookMgr->GetList($params);
            $result = $result[0];
        }

        // Allow the tier consolidation book delete operation
        $gcBookMgr->setAllowTierCSNBookDelete(true);
        if ($result) {
            $gcOwnershipEntMgr = $gManagerFactory->getManager('gcownershipentity');
            $params = array(
                'selects' => array('RECORDNO'),
                'filters' => array(
                    array(
                        array('BOOK.BOOKID', '=', $result['BOOKID']),
                    ),
                ),
            );

            $res = $gcOwnershipEntMgr->GetList($params);
            $res = $res[0];

            if (!isset($res['RECORDNO'])) {
                $ok = $gcBookMgr->Delete($result['BOOKID']);
            }
        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    private function createConsolidationBook(&$values)
    {
        $bookValues = $this->translateBookValues($values);

        // Set the affiliate entity dimension if Eliminate by affiliate entity is enabled
        if ($bookValues['ELIMINATEBYAFFILIATEENTITY'] == 'true') {
            $bookValues['DIMENSIONS'] = AffiliateEntityManager::AFFILIATE_ENTITY_DIMENSION_NAME;
        }
        $gcBookMgr = Globals::$g->gManagerFactory->getManager('gcbook');
        $ok = $gcBookMgr->add($bookValues);

        if ($ok) {
            $params = array(
                'selects' => array('RECORDNO'),
                'filters' => array(array(array('BOOKID', '=', $values['BOOK']['BOOKID']))),
            );
            $bookDetails = $gcBookMgr->GetList($params);
            $values['BOOKKEY'] = $bookDetails[0]['RECORDNO'];
        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    private function updateConsolidationBook(&$values): bool
    {
        $ok = true;
        if (isset($values['BOOK']['BOOKID']) && $values['BOOK']['BOOKID'] != '') {
            $gcBookMgr = Globals::$g->gManagerFactory->getManager('gcbook');
            // Get the book details
            $params = [
                'selects' => ['RECORDNO', 'BUDGETID', 'DEPARTMENTID', 'DIMENSIONS', 'HISTORICALRATEDATETYPE'],
                'filters' => [[['BOOKID', '=', $values['BOOK']['BOOKID']]]]
            ];
            $bookDetails = $gcBookMgr->GetList($params);
            $values['BOOK']['RECORDNO'] = $bookDetails[0]['RECORDNO'];
            $bookValues = $this->translateBookValues($values);
            $bookValues = array_merge($bookValues, $bookDetails[0]);

            // Set the affiliate entity dimension if Eliminate by affiliate entity is enabled
            if ($bookValues['ELIMINATEBYAFFILIATEENTITY'] == 'true') {
                $dimensions = explode('#~#', $bookDetails[0]['DIMENSIONS']);
                if (!in_array(AffiliateEntityManager::AFFILIATE_ENTITY_DIMENSION_NAME, $dimensions)) {
                    $dimensions[] = AffiliateEntityManager::AFFILIATE_ENTITY_DIMENSION_NAME;
                    $bookValues['DIMENSIONS'] = implode('#~#', $dimensions);
                }
            }
            $ok = $ok && $gcBookMgr->set($bookValues);

        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return array
     */
    private function translateBookValues($values)
    {
        $bookValues = [];
        $bookDetail = $values['BOOK'];

        // book id and type
        $bookValues['BOOKID'] = $bookDetail['BOOKID'];

        if (isset($bookDetail['RECORDNO']) && $bookDetail['RECORDNO'] != '') {
            $bookValues['RECORDNO'] = $bookDetail['RECORDNO'];
        }

        // book description
        if (!isset($bookDetail['BOOKDESCRIPTION']) || $bookDetail['BOOKDESCRIPTION'] == '') {
            $bookValues['DESCRIPTION'] = I18N::getSingleToken(
                "IA.DEFAULT_PARENT_ENTITY_BOOK_DESCRIPTION",
                [['name' => 'PARENT_ENTITY_NO', 'value' => $values['PARENTENTITYNO']]], true
            );
        } else {
            $bookValues['DESCRIPTION'] = $bookDetail['BOOKDESCRIPTION'];
        }

        // currency
        if (!isset($bookDetail['CURRENCY']) || $bookDetail['CURRENCY'] == '') {
            // if not set, default to parent entity base currecny
            $bookValues['CURRENCY'] = GetLocationBaseCurrency($values['PARENTENTITYNO']);
        } else {
            $bookValues['CURRENCY'] = $bookDetail['CURRENCY'];
        }

        // transaction methods
        if (!isset($bookDetail['BSTRANMETHOD']) || $bookDetail['BSTRANMETHOD'] == '') {
            $bookValues['BSTRANMETHOD'] = "Ending spot rate";
        } else {
            $bookValues['BSTRANMETHOD'] = $bookDetail['BSTRANMETHOD'];
        }

        if (!isset($bookDetail['ISTRANMETHOD']) || $bookDetail['ISTRANMETHOD'] == '') {
            $bookValues['ISTRANMETHOD'] = "Weighted average rate";
        } else {
            $bookValues['ISTRANMETHOD'] = $bookDetail['ISTRANMETHOD'];
        }

        $bookValues['AUTOELIMINATION'] = $values['AUTOELIMINATION'];
        $bookValues['SOURCEBOOKID'] = $values['SOURCEBOOKID'];
        $bookValues['ELIMINATEBYAFFILIATEENTITY'] = $values['ELIMINATEBYAFFILIATEENTITY'];

        // elimination account and exchange gain/loss accounts
        $bookValues['ELIMINATIONADJACCT'] = $bookDetail['ELIMINATIONADJACCT'];
        $bookValues['CTANETASSETACCOUNTNO'] = $bookDetail['CTANETASSETACCOUNTNO'];
        $bookValues['CTANETINCOMEACCOUNTNO'] = $bookDetail['CTANETINCOMEACCOUNTNO'];
        $bookValues['EENAME'] = $bookDetail['EENAME'];

        // Set book type for tier consolidation
        $bookValues['TYPE'] = BOOKTYPE_TIER;

        // Map book with ownership structure
        $bookValues['OWNERSHIPSTRUCTUREKEY'] = $values['STRUCTUREKEY'];
        return $bookValues;
    }

    /**
     * @param string $structureID
     *
     * @return array
     */
    public function getEntityBookMap($structureID)
    {
        $params = array(
            'selects' => array('BOOK.BOOKID', 'BOOK.BOOKKEY', 'PARENTENTITYID'),
            'filters' => array(
                array(
                    array('STRUCTURENAME', '=', $structureID),
                    array('BOOK.BOOKKEY', 'ISNOTNULL'),
                ),
            ),
        );

        $result = $this->GetList($params);

        $entityBookMap = [];

        foreach ($result as $val) {
            $entityBookMap[$val['PARENTENTITYID']] = array(
                'BOOKKEY' => $val['BOOK.BOOKKEY'],
                'BOOKID' => $val['BOOK.BOOKID']
            );
        }

        return $entityBookMap;

    }

}
