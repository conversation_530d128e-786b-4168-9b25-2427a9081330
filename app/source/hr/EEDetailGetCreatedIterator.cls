<?php
/**
 * Class definition for EEDetailGetCreatedIterator object
 *
 * <AUTHOR> Rusu <<EMAIL>>
 * @copyright 2015 Intacct Corporation All, Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 */

/**
 * Class EEDetailGetCreatedIterator
 * Created by PhpStorm.
 * Date: 6/8/2015
 * Time: 4:01 PM
 */
class EEDetailGetCreatedIterator extends UnionGetCreatedIterator
{
    /**
     * @param EntityManager $entMgr
     * @param GetListBatch  $batch
     * @param array         $queryParams
     */
    public function __construct(EntityManager $entMgr, GetListBatch $batch, $queryParams)
    {
        parent::__construct($entMgr, $batch, $queryParams, 'eedetail');
    }
}