<?xml version='1.0' encoding='ISO-8859-1'?>
<ROOT>
    <entity>eexpenses</entity>
    <title>IA.EXPENSE_REPORT</title>

    <view system="true">
        <events>
            <load>
                isEmployeeExpenseTxn = true; //Please look at function getExchangeRate() in subledgertax.js
                expenseOnLoad('P');
                EEShowDuplicatePage();
                checkAndLoadForm1099('ITEMS');
            </load>
        </events>
        <pages id="mainPages">
            <page id="mainPage" title="IA.HEADER">
                <child>
                    <section isCollapsible="true" id="mainSection" columns="2">
                        <title>IA.EXPENSE_REPORT_INFO</title>
                        <field>
                            <path>RECORD#</path>
                            <hidden>1</hidden>
                        </field>
                        <field>
                            <path>WHENCREATED</path>
                            <events>
                                <change>onChangeDateFiled(this);</change>
                            </events>
                        </field>
                        <field>
                            <path>EMPLOYEEID</path>
                            <events>
                                <change>onChangeEmployee(this);</change>
                            </events>
                        </field>
                        <field>RECORDID</field>
                        <field>WHENPOSTED</field>
                        <field hidden="1">BASECURR</field>
                        <field hidden="1">CURRENCY</field>
                        <field>SUPDOCID</field>
                        <field clazz="EEHeaderField" id="header_desc">DESCRIPTION</field>
                        <field clazz="EEHeaderField">COMMENTS</field>
                        <field>STATELINK</field>
                        <field hidden="1" fullname="IA.AMOUNT">TOTALENTERED</field>
                        <field hidden="1">TOTALPAID</field>
                        <field hidden="1">TOTALDUE</field>
                        <field hidden="1" fullname="IA.AMOUNT">TRX_TOTALENTERED</field>
                        <field hidden="1" fullname="IA.AMOUNT_PAID">TRX_TOTALPAID</field>
                        <field hidden="1" fullname="IA.AMOUNT_DUE">TRX_TOTALDUE</field>
                        <field hidden="1">WHENPAID</field>
                        <field>
                            <hidden>true</hidden>
                            <path>WHENMODIFIED</path>
                            <fullname>IA.LAST_MODIFIED</fullname>
                        </field>
                        <field>RECLASS_NOTES</field>
                        <field hidden="true" path="TAXIMPLICATIONS">
                            <events>
                                <change>handleExpenseTaxImplicationsCheckboxChange(this.meta, 'P', 'top');</change>
                            </events>
                        </field>
                        <field hidden="true" path="ENTITYTAXIMPLICATIONS">
                            <events>
                                <change>handleExpenseTaxImplicationsCheckboxChange(this.meta, 'P', 'entity');</change>
                            </events>
                        </field>
                        <field hidden="true" path="TAXSOLUTIONID">
                            <events>
                                <change>handleExpenseTaxSolutionChange(this.meta, 'P');</change>
                            </events>
                        </field>
                        <field rightSideLabel="true" hidden="true">
                            <path>INCLUSIVETAX</path>
                            <events>
                                <change>
                                    handleInclusiveTaxSelectEvent(this.meta);
                                </change>
                            </events>
                        </field>
                        <field hidden="true">
                            <path>EXPENSABLECCENTRIES_LOADED</path>
                            <type>
                                <type>boolean</type>
                                <default>false</default>
                            </type>
                        </field>
                        <grid hidden="true">
                            <path>LINKED_CCENTRIES</path>
                            <column>
                                <field label='IA.RECORD_NUMBER' hidden="true">
                                    <path>RECORDNO</path>
                                </field>
                            </column>
                            <column>
                                <field label='IA.DATE' readonly="true">
                                    <path>ENTRYDATE</path>
                                </field>
                            </column>
                            <column>
                                <field label='IA.DESCRIPTION_SLASH_MEMO' readonly="true">
                                    <path>DESCRIPTION</path>
                                </field>
                            </column>
                            <column>
                                <field label='IA.AMOUNT' readonly="true">
                                    <path>AMOUNT</path>
                                </field>
                            </column>
                            <column>
                                <field label='IA.TOTAL_EXPENSED' readonly="true">
                                    <path>TOTALEXPENSED</path>
                                </field>
                            </column>
                            <column>
                                <field label='IA.AMOUNT_REMAINING' readonly="true">
                                    <path>EXPENSABLE_AMOUNT</path>
                                </field>
                            </column>
                            <column>
                                <field label='IA.CREDIT_CARD_ACCOUNT' readonly="true">
                                    <path>CCINFO</path>
                                </field>
                            </column>
                        </grid>
                        <field hidden="true">
                            <path>EXPENSABLERECEIPTITEMS_LOADED</path>
                            <type>
                                <type>boolean</type>
                                <default>false</default>
                            </type>
                        </field>
                        <grid hidden="true">
                            <path>LINKED_RECEIPTITEMS</path>
                            <column>
                                <field label='IA.RECORD_NUMBER' hidden="true">
                                    <path>RECORDNO</path>
                                </field>
                            </column>
                            <column>
                                <field required="true" readonly="true">
                                    <path>ACCOUNTLABEL</path>
                                </field>
                            </column>
                            <column>
                                <field required="true" hidden="true" userUIControl="PrimaryFieldControl" readonly="true">
                                    <path>ACCOUNTNO</path>

                                </field>
                            </column>
                            <column>
                                <field label='IA.DATE' readonly="true">
                                    <path>ENTRY_DATE</path>
                                </field>
                            </column>
                            <column>
                                <field label='IA.DESCRIPTION_SLASH_MEMO' readonly="true">
                                    <path>DESCRIPTION</path>
                                </field>
                            </column>
                            <column>
                                <field label='IA.AMOUNT' readonly="true">
                                    <path>AMOUNT</path>
                                </field>
                            </column>
                            <column>
                                <field label='IA.TOTAL_EXPENSED' readonly="true">
                                    <path>TOTALEXPENSED</path>
                                </field>
                            </column>
                            <column>
                                <field label='IA.AMOUNT_REMAINING' readonly="true">
                                    <path>EXPENSABLE_AMOUNT</path>
                                </field>
                            </column>
                        </grid>
                    </section>
                </child>
                <child>
                    <grid dimFields="ITEMS_not" clazz="EEntriesGrid" allowEditPage="true">
                        <title>IA.EXPENSE_ENTRIES</title>
                        <path>ITEMS</path>
                        <gridContextualActions>
                            <button id="addAllocatedExpenseButton" path="addAllocatedExpenseButton">
                                <name>IA.ADD_DISTRIBUTED_EXPENSE</name>
                            </button>
                            <button id="selectCCEntriesButton" path="selectCCEntriesButton">
                                <name>IA.SELECT_CREDIT_CARD_TRANSACTIONS</name>
                                <events>
                                    <click>onSelectCCTxnClick();</click>
                                </events>
                            </button>
                            <button id="selectReceiptItemsButton" path="selectReceiptItemsButton">
                                <name>IA.SELECT_ELECTRONIC_RECEIPTS</name>
                                <events>
                                    <click>onSelectReceiptItemsClick();</click>
                                </events>
                            </button>
                        </gridContextualActions>
                        <column>
                            <field required="true">
                                <path>ACCOUNTLABEL</path>
                                <events>
                                    <change>onChangeExpenseType(this);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field required="true" hidden="true" userUIControl="PrimaryFieldControl">
                                <path>ACCOUNTNO</path>

                            </field>
                        </column>
                        <column>
                            <field id="EEGRID_AMOUNT">
                                <path>AMOUNT</path>
                                <events>
                                    <change>onChangeAmount(this);calculateLineItemTax(this.meta);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field>
                                <path>EXPPMTTYPE</path>
                                <events>
                                    <change>onChangePaymentType(this.meta.getGrid(), this.meta.getLineNo());</change>
                                </events>
                            </field>
                        </column>
                        <column className="center">
                            <field readonly="true">NONREIMBURSABLE</field>
                        </column>
                        <column className="center">
                            <field>
                                <path>FORM1099</path>
                                <events>
                                    <change>changeForm1099Fields('ITEMS', 'EMPLOYEEID', this.meta);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field id="paid_to">DESCRIPTION</field>
                        </column>
                        <column>
                            <field>DESCRIPTION2</field>
                        </column>
                        <column>
                            <field>
                                <id>line_date</id>
                                <path>ENTRY_DATE</path>
                                <events>
                                    <change>onChangeEntryDate(this);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field>DEPARTMENTID</field>
                        </column>
                        <column>
                            <field>LOCATIONID</field>
                        </column>
                        <column>
                            <field>CC_LINKED</field>
                        </column>
                        <column>
                            <field>ER_LINKED</field>
                        </column>
                        <column className="center">
                            <field hidden="true">
                                <path>MULTIPLETAXES</path>
                                <events>
                                    <change>onClickMultipleTaxes(this.meta);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.TAX_DETAIL">
                                <path>DETAILID</path>
                                <events>
                                    <change>onChangeTaxDetail(this.meta);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.RATE" hidden="true">
                                <path>TAXRATE</path>
                                <readonly>true</readonly>
                            </field>
                        </column>
                        <column>
                            <field hidden="true">
                                <path>TRX_TAX</path>
                                <events>
                                    <change>calculateBaseTaxForOverriddenTax(this.meta);updateParentGridTotalAmountValues(this.meta.getGrid(), this.meta.getLineNo());</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field hidden="true">
                                <path>TAX</path>
                            </field>
                        </column>
                        <column>
                            <field readonly="true" required="false" hasTotal="true" hidden="true">
                                <path>TOTALTRXAMOUNT</path>
                                <events>
                                    <change>calculateInclusiveTax(this.meta);onChangeTransactionTotal(this);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field hidden="true">
                                <path>CCENTRYKEY</path>
                            </field>
                        </column>
                        <column>
                            <field hidden="true">
                                <path>ERENTRYKEY</path>
                            </field>
                        </column>
                        <lineDetails clazz="EELineDetails">
                            <pages>
                                <page title="IA.DETAILS">
                                    <subsection className="subSection">
                                        <field>BILLABLE</field>
                                        <field clazz="Form1099TypeField">
                                            <path>FORM1099TYPE</path>
                                            <events>
                                                <change>setForm1099Box('ITEMS', this.meta);</change>
                                            </events>
                                        </field>
                                        <field clazz="Form1099BoxField">
                                            <path>FORM1099BOX</path>
                                        </field>
                                        <field>
                                            <path>QUANTITY</path>
                                            <events>
                                                <change>onChangeQuantity(this);</change>
                                            </events>
                                        </field>
                                        <field>
                                            <path>UNITRATE</path>
                                            <events>
                                                <change>onChangeUnitRate(this);</change>
                                            </events>
                                        </field>
                                        <field>ORG_CURRENCY</field>
                                        <field>
                                            <path>ORG_AMOUNT</path>
                                            <events>
                                                <change>onChangeOrgAmount(this);</change>
                                            </events>
                                        </field>
                                        <field>ORG_EXCHRATEDATE</field>
                                        <field><path>ORG_EXCHRATETYPE</path></field>
                                        <field>ORG_EXCHRATE</field>
                                        <field hidden="true">USER_EXCHRATE</field>
                                    </subsection>
                                    <subsection className="subSection" title="IA.LINKED_TRANSACTION" forceColumn="true" columnCount="1">
                                        <field noLabel="true">CCTRANSACTIONLINK</field>
                                        <field noLabel="true">REMOVECCLINK</field>
                                        <field noLabel="true">ERTRANSACTIONLINK</field>
                                        <field noLabel="true">REMOVEERLINK</field>
                                    </subsection>
                                    <subsection className="subSection">
                                        <grid entity="expensesapproval" path="HISTORYITEMS" title="IA.APPROVAL_HISTORY">
                                            <column>
                                                <field>APPROVAL_STAGE</field>
                                            </column>
                                            <column>
                                                <field>APPROVAL_TYPE</field>
                                            </column>
                                            <column>
                                                <field>APPROVERID</field>
                                            </column>
                                            <column>
                                                <field>APPROVEDBY</field>
                                            </column>
                                            <column>
                                                <field>EVENTDATE</field>
                                            </column>
                                            <column>
                                                <field>STATE</field>
                                            </column>
                                            <column>
                                                <field>COMMENTS</field>
                                            </column>
                                        </grid>
                                    </subsection>
                                    <subsection className="subSection" id="vatSection" title="IA.TAXES">
                                        <grid clazz="TaxEntriesGrid">
                                            <path>TAXENTRIES</path>
                                            <column>
                                                <field fullname="IA.TAX_DETAIL">
                                                    <path>DETAILID</path>
                                                    <events>
                                                        <change>onChangeTaxDetail(this.meta);</change>
                                                    </events>
                                                </field>
                                            </column>
                                            <column>
                                                <field fullname="IA.RATE">
                                                    <path>TAXRATE</path>
                                                    <readonly>true</readonly>
                                                </field>
                                            </column>
                                            <column>
                                                <field>
                                                    <path>TRX_TAX</path>
                                                    <events>
                                                        <change>calculateBaseTaxForOverriddenTax(this.meta);updateTxnTaxBasedOnMultiTaxTotal(this.meta);</change>
                                                    </events>
                                                </field>
                                            </column>
                                            <column>
                                                <field noLabel="true" hidden="true">
                                                    <path>OVERRIDDENTAX</path>
                                                    <type type='boolean' ptype='boolean'></type>
                                                    <default>false</default>
                                                </field>
                                            </column>
                                        </grid>
                                    </subsection>
                                </page>
                            </pages>
                        </lineDetails>
                    </grid>
                </child>
                <child>
                    <field path="STATE_LEGEND" noPrint="1" isHTML="1" component="msgcomp">
                        <type type="msgcomp" ptype="msgcomp"></type>
                        <infoText></infoText>
                    </field>
                </child>
                <child>
                    <field path="APPROVE_LEGEND" hidden="1" noPrint="1" isHTML="1" component="msgcomp">
                        <type type="msgcomp" ptype="msgcomp"></type>
                        <infoText></infoText>
                    </field>
                </child>
            </page>
        </pages>

        <floatingPage move="true" resize="true" close="true" fixedcenter="true" modal="true">
            <title>IA.DUPLICATE_EXPENSES</title>
            <id>duplicateExpensePage</id>
            <path>DUPLICATE_EXPENSEPAGE</path>
            <pages>
                <page>
                    <field noLabel="1" isHTML="1" path="DUMMY" component="msgcomp">
                        <type type="msgcomp" ptype="msgcomp"></type>
                        <infoText>IA.DO_YOU_WANT_TO_CLEAR_THE_EXPENSE_DETAILS_HEADER</infoText>
                    </field>
                </page>
            </pages>
            <footer>
                <button>
                    <name>IA.YES</name>
                    <events>
                        <click>EEprepareForDuplicate('R');</click>
                    </events>
                </button>
                <button>
                    <name>IA.NO</name>
                    <events>
                        <click>EEprepareForDuplicate('A');</click>
                    </events>
                </button>
            </footer>
        </floatingPage>

        <floatingPage clazz="EEApproveDeclinePage" move="true" resize="true" close="true"
                      position="alignToBottomRight(this.panel, triggerElement);" modal="true">
            <title>IA.APPROVE_DECLINE_LINE</title>
            <id>lineApprovalPage</id>
            <path>APPROVE_EEXPENSESITEM</path>
            <entity>eexpensesitem</entity>
            <map>
                <source>trigger.parentComponent.parentValue</source>
            </map>
            <pages>
                <page id="approvalPage" hideTitle="true" columnCount="1">
                    <section title="IA.EXPENSE_ENTRY">
                        <field>ACCOUNTLABEL</field>
                        <field>ENTRY_DATE</field>
                        <field>AMOUNT</field>
                    </section>
                    <section title="IA.REVIEWER_COMMENTS">
                        <field path="COMMENTS" forceReadWrite="true" fullname="IA.COMMENTS" noLabel="true"
                               className='width98percent'>
                            <type type="textarea" ptype="textarea"/>
                        </field>
                    </section>
                </page>
            </pages>
            <footer>
                <button id="approveLineButton">
                    <name>IA.APPROVE</name>
                    <events>
                        <click>EEApproveDeclineLine('A');</click>
                    </events>
                </button>
                <button id="declineLineButton">
                    <name>IA.DECLINE</name>
                    <events>
                        <click>EEApproveDeclineLine('R');</click>
                    </events>
                </button>
                <button id="entrySaveButton">
                    <name>IA.CANCEL</name>
                    <events>
                        <click>editor.hidePage('lineApprovalPage', false);</click>
                    </events>
                </button>
            </footer>
        </floatingPage>

        <floatingPage modal="true">
            <title>IA.SELECT_CREDIT_CARD_TRANSACTIONS</title>
            <id>selectCCtransactionsPage</id>
            <path>selectCCtransactionsPage</path>
            <entity>expensableccentry</entity>

            <pages>
                <page>
                    <section>
                        <field path="TOTALSELECTED" label='IA.TOTAL_SELECTED_AMOUNT' readonly="true">
                            <type type="decimal" ptype="currency"/>
                        </field>
                        <grid noDragDrop="true" customFields="no" enableSelect="true"
                              noNewRows="true" showDelete="false">
                            <selectColumn autoRedraw="true" autoUpdateSelected="true"></selectColumn>
                            <enableEmptyMessage>true</enableEmptyMessage>
                            <emptyMessage>IA.NO_RECORDS_TO_DISPLAY</emptyMessage>
                            <hideLineNo>true</hideLineNo>
                            <maxRows>20</maxRows>
                            <path>EXPENSABLECCENTRIES</path>
                            <column>
                                <field label='IA.RECORD_NUMBER' hidden="true">
                                    <path>RECORDNO</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="ENTRYDATE">
                                        <path>SEARCH_ENTRYDATE</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.DATE' readonly="true" sortable="true">
                                    <path>ENTRYDATE</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="DESCRIPTION">
                                        <path>SEARCH_DESCRIPTION</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.DESCRIPTION_SLASH_MEMO' readonly="true" sortable="true">
                                    <path>DESCRIPTION</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="AMOUNT">
                                        <path>SEARCH_AMOUNT</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.AMOUNT' readonly="true" sortable="true">
                                    <path>AMOUNT</path>
                                </field>
                            </column>
                            <column hidden="true">
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="TOTALEXPENSED">
                                        <path>SEARCH_TOTALEXPENSED</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.TOTAL_EXPENSED' readonly="true" sortable="true">
                                    <path>TOTALEXPENSED</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="EXPENSABLE_AMOUNT">
                                        <path>SEARCH_EXPENSABLE_AMOUNT</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.AMOUNT_REMAINING' readonly="true" sortable="true">
                                    <path>EXPENSABLE_AMOUNT</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>AMOUNT_TO_APPLY</path>
                                    <events>
                                        <change>onChangeAmountToApply(this.meta);</change>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <field id="allocationColumn">ALLOCATION</field>
                            </column>
                            <column>
                                <field>PAID_TO</field>
                            </column>
                            <column>
                                <field>PAID_FOR</field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="CCINFO">
                                        <path>SEARCH_CCINFO</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.CREDIT_CARD_ACCOUNT' readonly="true" sortable="true">
                                    <path>CCINFO</path>
                                </field>
                            </column>
                        </grid>
                    </section>
                </page>
            </pages>
            <footer>
                <button id="addSelectedButton" path="addSelectedButton">
                    <name>IA.ADD_SELECTED</name>
                    <events>
                        <click>addSelectedCCEntries();</click>
                    </events>
                </button>
                <button>
                    <name>IA.CANCEL</name>
                    <events>
                        <click>closeSelectCCTransactionsPage();</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
        <floatingPage modal="true">
            <title>IA.SELECT_ELECTRONIC_RECEIPTS</title>
            <id>selectReceiptItemsPage</id>
            <path>selectReceiptItemsPage</path>
            <entity>expensablereceiptitem</entity>
            <pages>
                <page>
                    <section>
                        <field path="TOTALSELECTED" label='IA.TOTAL_SELECTED_AMOUNT' readonly="true">
                            <type type="decimal" ptype="currency"/>
                        </field>
                        <grid noDragDrop="true" customFields="no" enableSelect="true"
                              noNewRows="true" showDelete="false">
                            <selectColumn autoRedraw="true" autoUpdateSelected="true"></selectColumn>
                            <enableEmptyMessage>true</enableEmptyMessage>
                            <emptyMessage>IA.NO_RECORDS_TO_DISPLAY</emptyMessage>
                            <hideLineNo>true</hideLineNo>
                            <maxRows>20</maxRows>
                            <path>EXPENSABLERECEIPTITEMS</path>
                            <column>
                                <field label='IA.RECORD_NUMBER' hidden="true">
                                    <path>RECORDNO</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="ACCOUNTLABEL">
                                        <path>SEARCH_ACCOUNTLABEL</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.EXPENSE_TYPE' readonly="true" sortable="true">
                                    <path>ACCOUNTLABEL</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="ACCOUNTNO">
                                        <path>SEARCH_ACCOUNTNO</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.GL_ACCOUNT_NUMBER' readonly="true" sortable="true">
                                    <path>ACCOUNTNO</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="ENTRYDATE">
                                        <path>SEARCH_ENTRYDATE</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.DATE' readonly="true" sortable="true">
                                    <path>ENTRY_DATE</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="DESCRIPTION">
                                        <path>SEARCH_DESCRIPTION</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.DESCRIPTION_SLASH_MEMO' readonly="true" sortable="true">
                                    <path>DESCRIPTION</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="TRX_AMOUNT">
                                        <path>SEARCH_TRX_AMOUNT</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.TRX_AMOUNT' readonly="true" sortable="true">
                                    <path>TRX_AMOUNT</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="CURRENCY">
                                        <path>SEARCH_CURRENCY</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.TRX_CURRENCY' readonly="true" sortable="true">
                                    <path>CURRENCY</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="AMOUNT">
                                        <path>SEARCH_AMOUNT</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.AMOUNT' readonly="true" sortable="true">
                                    <path>AMOUNT</path>
                                </field>
                            </column>
                            <column hidden="true">
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="TOTALEXPENSED">
                                        <path>SEARCH_TOTALEXPENSED</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.TOTAL_EXPENSED' readonly="true" sortable="true">
                                    <path>TOTALEXPENSED</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center">
                                    <field noLabel="true" searchPath="EXPENSABLE_AMOUNT">
                                        <path>SEARCH_EXPENSABLE_AMOUNT</path>
                                    </field>
                                </gridHeading>
                                <field label='IA.AMOUNT_REMAINING' readonly="true" sortable="true">
                                    <path>EXPENSABLE_AMOUNT</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>AMOUNT_TO_APPLY</path>
                                    <events>
                                        <change>onChangeAmountToApply(this.meta);</change>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <field>PAID_TO</field>
                            </column>
                            <column>
                                <field>PAID_FOR</field>
                            </column>
                        </grid>
                    </section>
                </page>
            </pages>
            <footer>
                <button id="addSelectedButton" path="addSelectedButton">
                    <name>IA.ADD_SELECTED</name>
                    <events>
                        <click>addSelectedReceiptItems();</click>
                    </events>
                </button>
                <button>
                    <name>IA.CANCEL</name>
                    <events>
                        <click>closeSelectReceiptItemsPage();</click>
                    </events>
                </button>
            </footer>
        </floatingPage>

    </view>
</ROOT>
