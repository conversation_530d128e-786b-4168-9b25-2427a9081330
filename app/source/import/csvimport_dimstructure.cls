<?php

class csvimport_dimstructure extends csvimport_base
{

    /* @var string $imptype */
    var $imptype = 'dimension structure';

    /* @var string $memLabel */
    var $memLabel;

    /* @var string $memLabelPlural */
    var $memLabelPlural;

    /** @var GLAcctGrpManager $acctGrpMgr */
    var $acctGrpMgr;

    /**
     * @param array $args
     */
    function __construct($args = [])
    {
        include ('csv_metadata_dimstructure.inc');

        global $gManagerFactory;
        $this->acctGrpMgr = $gManagerFactory->getManager('glacctgrp');
        /** @noinspection PhpUndefinedVariableInspection */
        $this->imeta = $imeta;
        /** @noinspection PhpUndefinedVariableInspection */
        $this->imetagroom = $imetagroom;

        // current entity manager
        $this->entMgr = $this->acctGrpMgr;

        $compType = $args['postprocess_arg'];


        $dims = IADimensions::getAllDimensions(!util_isPlatformDisabled());

        foreach ( $dims as $val ) {
            if ( $val['componentval'] == $compType ) {
                $this->memLabelPlural = $val['internal_label_plural'] ?? $val['label'];
                $this->memLabel = $val['internal_label'] ?? $val['label'];
                break;
            }
        }
    }

    /**
     * @param resource $fp
     * @param array $hdrMap
     * @param array|null $record
     *
     * @return bool
     */
    function _GetImportCSVRow($fp, $hdrMap, &$record)
    {
        $ok = parent::_GetImportCSVRow($fp, $hdrMap, $record);
        $record['LINE_NO'] = $record['MEMBER_RANK'];
        return $ok;
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    function _GetNextImportObject(&$obj)
    {
        return $this->_GetImportCSVTransaction($this->impFP, $this->objMap, $obj);
    }

    /**
     * Overide function for writing the error file
     *
     * @param array $obj
     * @param resource $errfp
     * @param int $noheaders
     */
    function _WriteToImportErrorFile($obj, $errfp, $noheaders = 1)
    {
        $this->_writeUTF8Bom($errfp);
        foreach ( $obj as $values ) {
            $csvstring = ArrayToCSVString($values, [], $noheaders);
            fwrite($errfp, $csvstring);
        }
    }

    /**
     * @param array $transarray
     * @param array $grps
     *
     * @return bool
     */
    function ConstructRecord($transarray, &$grps)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $gErr = Globals::$g->gErr;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ok = true;
        $ownedObj = 'GLDIMGRPS';
        $memberName = 'GROUPID';
        foreach ( $transarray as $lineitem ) {
            foreach ( $this->imeta as $key => $value ) {
                $jarray[$key] = $lineitem[$value];
            }

            /** @noinspection PhpUndefinedVariableInspection */
            $structarray = $this->FlatToStructured($jarray);
            if ( $structarray['LINE_NO'] == '1' ) {
                $ownedObj = 'GLDIMGRPS';
                $memberName = 'GROUPID';

                $grps = array(
                    'NAME' => $structarray['NAME'],
                    'TITLE' => $structarray['TITLE'],
                    'TOTALTITLE' => $structarray['TOTALTITLE'],
                    'MEMBERTYPE' => $this->memLabelPlural
                );

                if ( $structarray['MEMBERTYPE'] == 'HS' ) {
                    $grps['MEMBERTYPE'] = 'Group of ' . $this->memLabelPlural;
                    $ownedObj = 'GLACCTGRPS';
                    $memberName = 'CHILDNAME';
                }
            }
            $thislineitem = array($memberName => $structarray['MEMBERS']);

            $grps[$ownedObj][] = $thislineitem;
        }

        return true;
    }

    /**
     * @param array $transarray
     *
     * @return bool
     */
    function Import($transarray)
    {
        $result = $this->ConstructRecord($transarray, $grps);
        if ( !$result ) {
            return false;
        }

        $result = $this->entMgr->add($grps);

        return $result;
    }

}
