<?
/**
 * Engine to perform import.
 */
require_once "Pt_ImportResults.cls";
require_once "Pt_ImportData.cls";
require_once "Pt_ImportMap.cls";
require_once "Pt_ImportNode.cls";
require_once "Pt_ConversionUtil.cls";
require_once "Pt_DataFieldManager.cls";
require_once "Pt_DataObjectManager.cls";
require_once "Pt_ObjectController.cls";
require_once 'Pt_UniqueValueController.cls';
require_once "Pt_EventRunner.cls";
require_once "Pt_EventDefManager.cls";

/**
 * Import data platform objects in asynchronous mode.
 */
class csvimport_platform extends csvimport_base
{
    /* @var int $companyNo */
    private $companyNo;
    /* @var string $userId */
    private $userId;
    /* @var Pt_DataObjectDef $objDef */
    private $objDef;
    /* @var Pt_ImportMap $map */
    private $map;

    /* @var string $buff */
    public $buff;
    /* @var bool $sendEmail */
    public $sendEmail;

    /**
     * @param array $args
     */
    public function __construct($args) 
    {
        parent::__construct($args['objDefName']);
        $this->companyNo    = intval($args['companyNo']);
        $this->userId       = intval($args['userId']);
        $this->emailaddress = $args['emailaddress'];
        $this->live         = (bool) getenv("IA_PRODUCTION"); // in production or dev

        $objDefId = intval($args['objDefId']);
        $this->objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($this->objDef == null) {
             throw new Exception("Object definition with id $objDefId not found for customer ".$this->companyNo);
        }
        $this->checklist = "Object ".$this->objDef." import tool";

        $xmlString = strval($args['xmlString']);
        $this->map = new Pt_ImportMap(100, 0, 'Import Map', $objDefId, 0, $xmlString, $this->userId, 0,
                                      $this->userId, 0, []);

        $this->buff = '';
        $this->sendEmail = true;
        I18N::addTokens([ "IA.ROW", "IA.ERROR.ERROR_OR_WARNING", "IA.ROW_ROW_COUNTER",
                          "IA.ERROR.ERROR_IMPORTING_DATA_FOR_UI_FIELD_FIELD_M", "IA.ERROR.VALIDATION_ERROR_MSG",
                          "IA.IMPORTED_DATA_ID_NEW_ID", "IA.IMPORT_WARNING_IN_LINE_ROW_COUNTER_WARNINGS",
                          "IA.ERROR.IMPORT_ERROR_IN_ROW_ROW_COUNTER_MSG", "IA.ERROR.IMPORT_ERROR_IN_LINE_LINE_MSG" ]);
        I18N::getText();
    }

    /**
     * Perform actual import.
     *
     * @param array $importParams
     * @param array &$returnParams
     *
     * @return bool
     */
    function ImportCSVFile($importParams, &$returnParams) 
    {
        parent::OverrideLineEndingsForMac();

        $this->startTime        = time();   // time stamp of import
        $this->impCount         = 0;        // current count of successful imports
        $this->errCount         = 0;        // current count of errors
        $warningsCount          = 0;
        $this->allErrors        = array();  // stores all errors that have occured thus far
        $this->globalErrIndex   = 0;        // Track the global error index for error processing
        $this->impFilename  = $importParams['origfilename'];
        $this->errFilename  = "err_".GetMyCompany()."_".GetMyUserid().".csv";

        $importer = new Pt_CSVReader();
        $importer->open($importParams['filestoreID']);

        $rowData = $importer->readLine();  // Always ignore the first row because we assume it's the header row
        $this->logErrorRow(I18N::getSingleToken("IA.ROW"), $rowData, I18N::getSingleToken("IA.ERROR.ERROR_OR_WARNING"));

        $fields = $this->objDef->getFields();
        $actiallyImported = array();
        $rowCounter = 1;
        
        $losProfile = LOSManager::getLOSProfileForCompany(
            GetMyCompany(), LOSProfile::LOS_PROCESS_LOG_TYPE_WEB_SERVICE,
            LOSProfile::LOS_CLIENT_CSV_UPLOAD
        );
        try {
            if ( $losProfile->clientHasTransactionQuota() == false ) {
                throw new Pt_I18nException('PAAS-0900', LOSManager::INSUFFICIENT_QUOTA_MESSAGE);
            }
            $runner = new Pt_EventRunner();
            $debug = http_getSessionObject(ATTR_TRIGGER_DEBUG); // Bug 36589
            if ($debug != null) {
                $runner->setDebugger($debug);
            }
            if ( HasErrors() ) {
                logToFileError(json_encode(Globals::$g->gErr->getErrors()));
                throw new Pt_I18nException('PAAS-0888', "An import error has occurred. Contact customer support for assistance.");
            }

            while (!$importer->isEOF()) {
                Globals::$g->gErr->Clear();
                if ( $losProfile->clientHasTransactionQuota() == false ) {
                    throw new Pt_I18nException('PAAS-0901', LOSManager::INSUFFICIENT_QUOTA_MESSAGE);
                }
                $rowData = $importer->readLine();
                if ($rowData==null || count($rowData)==0) {
                    continue;
                }
                $rowCounter++;
                $this->buff .= I18N::getSingleToken("IA.ROW_ROW_COUNTER", [
                        [ "name" => 'ROW_COUNTER', "value" => "$rowCounter" ] ]) . " ";   // Bug 37392

                /** @noinspection PhpUnusedLocalVariableInspection */
                $data = null;
                try {
                    $fieldData = array();
                    $hasError = false;
                    $warnings = '';
                    foreach ($fields as $field) {
                        if ($field->isReadOnly()) {
                            continue;
                        }
                        $uiField = $field->getUIField();
                        $destName = $field->getFieldName();

                        $value = null;
                        $index = $this->map->getColNo($destName);
                        try {
                            if ($index == Pt_ImportNode::COL_CONST) {
                                $strValue = $this->map->getConstValue($destName);
                                $useIds = ($uiField instanceof Pt_Relationship ? true : false);
                                $value = Pt_ConversionUtil::toObject($strValue, $field, $useIds, $warnings);
                            }
                            else if ($index >= 0 && $index < count($rowData)) {
                                $strValue = $rowData[$index];
                                $value = Pt_ConversionUtil::toObject($strValue, $field, false, $warnings);
                            }
                        }
                        catch (Exception $eex) {
                            $msg = $eex->getMessage();
                            $this->errCount++;
                            $this->buff .= I18N::getSingleToken("IA.ERROR.ERROR_IMPORTING_DATA_FOR_UI_FIELD_FIELD_M", [
                                    [ "name" => 'UI_FIELD', "value" => "$uiField" ],
                                    [ "name" => 'MSG', "value" => "$msg" ] ]) . "\n";
                            $this->logErrorRow($rowCounter, $rowData, $msg);
                            $hasError = true;
                            break;
                        }

                        if ($value == null) {
                            if ($uiField instanceof Pt_ProcessSelect) {
                                $value = $this->objDef->getDefaultProcessId();
                            }
                            else if ($uiField instanceof Pt_StatusSelect) {
                                $p = $this->objDef->getDefaultProcess();
                                $value = ($p==null ? 0 : $p->getDefaultStatusId());
                            }
                        }

                        if ( is_string($value) ) {
                            isl_translate_from_browser($value, true);
                        }
                        $fieldData[$destName] = $value;
                    }
                    if ($hasError || count($fieldData)==0) {
                        continue;
                    }

                    if ( ! $this->validateFields($fields, $fieldData, $rowCounter, $rowData,
                        self::VALIDATE_SKIP_AUTONUMBER)
                    ) {
                        $hasError = true;
                    }

                    if ($hasError) {
                        continue;
                    }

                    Pt_UniqueValueController::validate($this->objDef, -1, $fieldData, null,
                        Pt_UniqueValueController::VALIDATE_SKIP_AUTONUMBER);

                    // Bug 37316 
                    if (Pt_EventDefManager::hasValidation($this->objDef->getId(), ON_BEFORE_CREATE)) {
                        try {
                            $data = new Pt_DataObject(1, $this->objDef->getId(), $fieldData);
                            $runner->runValidation($data, ON_BEFORE_CREATE);
                        } catch (Exception $eex) {
                            $msg = $eex->getMessage();
                            $this->errCount++;
                            $this->logErrorRow($rowCounter, $rowData, $msg);
                            $hasError = true;
                            $this->buff .= I18N::getSingleToken("IA.ERROR.VALIDATION_ERROR_MSG", [
                                    [ "name" => 'MSG', "value" => "$msg" ] ]) . "\n";
                            continue;
                        }
                    }

                    // Now that we've passed validation, auto-number fields can be populated with the next number
                    Pt_ObjectController::populateAutoNumberFields($this->objDef, $fieldData);

                    if ( ! $this->validateFields($fields, $fieldData, $rowCounter, $rowData,
                        self::VALIDATE_ONLY_AUTONUMBER)
                    ) {
                        $hasError = true;
                    }

                    // Validate unique values now that we have auto-numbers
                    Pt_UniqueValueController::validate($this->objDef, -1, $fieldData, null,
                        Pt_UniqueValueController::VALIDATE_ONLY_AUTONUMBER);

                    if ($hasError) {
                        continue;
                    }


                    XACT_BEGIN('PLATFORM_IMP');
                    try {
                        $data = Pt_DataObjectManager::create($this->objDef, $fieldData, $runner);
                        XACT_COMMIT('PLATFORM_IMP');
                        }
                    catch (Exception $ex) {
                        XACT_ABORT('PLATFORM_IMP');
                        throw $ex;
                    }

                    $newId = $data->getId();
                    $this->buff .= I18N::getSingleToken("IA.IMPORTED_DATA_ID_NEW_ID", [
                            [ "name" => 'DATA', "value" => "$data" ],
                            [ "name" => 'NEW_ID', "value" => "$newId" ] ]) . "\n";
                    array_push($actiallyImported, $newId);
                    $runner->reset();
                    
                    if (isl_strlen($warnings)>0) {  // Bug 37572
                        $this->logErrorRow($rowCounter, $rowData, $warnings);
                        $this->buff .= I18N::getSingleToken("IA.IMPORT_WARNING_IN_LINE_ROW_COUNTER_WARNINGS", [
                                [ "name" => 'ROW_COUNTER', "value" => "$rowCounter" ],
                                [ "name" => 'WARNINGS', "value" => "$warnings" ] ]) . "\n";
                        $warningsCount++;
                    }

                } catch (Exception $ex2) {
                    $this->errCount++;
                    $msg = $ex2->getMessage();
                    $this->logErrorRow($rowCounter, $rowData, $msg);
                    $this->buff .= I18N::getSingleToken("IA.ERROR.IMPORT_ERROR_IN_ROW_ROW_COUNTER_MSG", [
                            [ "name" => 'ROW_COUNTER', "value" => "$rowCounter" ],
                            [ "name" => 'MSG', "value" => "$msg" ] ]) . "\n";
                    if (PLATFORM_DEBUG>=1) {
                        $this->buff .= $ex2->getTraceAsString()."\n";
                    }
                }
                $losProfile->incrementCurrentTransactionCount();
            }

            $importer->close();

            $runner->finalize();

        } catch (Exception $ex3) {
            $this->errCount++;
            $line = $ex3->getLine();
            $message = $ex3->getMessage();
            $this->buff .= I18N::getSingleToken("IA.ERROR.IMPORT_ERROR_IN_LINE_LINE_MSG", [
                    [ "name" => 'LINE', "value" => "$line" ],
                    [ "name" => 'MSG', "value" => "$message" ] ]) . "\n";
            if (PLATFORM_DEBUG>=1) {
                $this->buff .= $ex3->getTraceAsString()."\n";
            }
        }

        $this->impCount = count($actiallyImported);
        $returnParams['impcount'] = $this->impCount;
        $returnParams['result'] = $this->buff;

        $this->impStats = array(
            'impcount'      => $this->impCount,
            'errcount'      => $this->errCount
        );

        if ($this->sendEmail) {
            if ($this->errCount > 0 || $warningsCount > 0) {
                $this->_SendImportFailureEmail();
            } else {
                $this->_SendImportSuccessEmail();
            }
        }

        $importType = "platform.import".$this->imptype;
        if ( $this->imptype == null ) {
            if ( $this->objDef != null ) {
                if ( $this->objDef->getSingularName() != null ) {
                    $importType = "import.".$this->objDef->getSingularName();
                } else {
                    $importType = $this->impFilename;
                }
            }
        }
        /** @noinspection PhpUndefinedVariableInspection */
        $importStatus = !( $hasError || ( $this->errCount > 0));
        $webServicesLog = WebServicesLogManager::createNewWebServicesLog($losProfile, $rowCounter, $importType, $importStatus, GetMyLogin());
        $losProfile->addToProcessLog($webServicesLog);
        LOSManager::finalizeLOSProfile($losProfile);
        
        return true;
    }

    const VALIDATE_SKIP_AUTONUMBER = 0;
    const VALIDATE_ONLY_AUTONUMBER = 1;

    /**
     * performs field-level validation
     *
     * @param Pt_DataFieldDef[] $fields      fields to validate
     * @param array             $fieldData   the fields' data
     * @param int               $rowCounter  what row are we processing (for error message)
     * @param array             $rowData     line from the CSV file (for error message)
     * @param int               $strategy    one of the VALIDATE_*_AUTONUMBER constants
     *                                       indicating how we treat auto-number fields
     *
     * @return bool  true if there are no errors
     */
    private function validateFields($fields, $fieldData, $rowCounter, $rowData, $strategy)
    {
        $hasError = false;

        foreach ($fields as $field) {
            $isAutoNum = $field instanceof Pt_FieldAuto;
            if (    ( ! $field->isReadOnly() || $isAutoNum )
                 && (    ( $strategy == self::VALIDATE_SKIP_AUTONUMBER && ! $isAutoNum )
                      || ( $strategy == self::VALIDATE_ONLY_AUTONUMBER && $isAutoNum ) )
            ) {
                $uiField = $field->getUIField();
                try {
                    $uiField->validateAPI($fieldData);
                } catch (Exception $eex) {
                    $msg = $eex->getMessage();
                    $this->errCount++;
                    $this->logErrorRow($rowCounter, $rowData, $msg);
                    $hasError = true;
                    $this->buff .= I18N::getSingleToken("IA.ERROR.ERROR_IMPORTING_DATA_FOR_UI_FIELD_FIELD_M", [
                            [ "name" => 'UI_FIELD', "value" => "$field" ],
                            [ "name" => 'MSG', "value" => "$msg" ] ]) . " \n";
                    break;
                }
            }
        }

        return ! $hasError;

    }

    /**
     * Add row to errors CSV
     *
     * @param int    $rowNo
     * @param array  $rowData
     * @param string $errorMsg
     */
    private function logErrorRow($rowNo, $rowData, $errorMsg)
    {
        $arr = [$rowNo, $errorMsg];

        if (is_array($rowData)) {
            foreach ($rowData as $value) {
                $arr[] = $value == null ? " " : $value;
            }
        }

        $this->_LogImportErrors($arr);
    }

}
