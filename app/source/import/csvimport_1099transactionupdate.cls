<?
//=============================================================================
//
//	FILE:					csvimport_1099transactionupdate.cls
//	AUTHOR:					
//	DESCRIPTION:			1099 Transactions import
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================
    require_once 'csvimport_base.cls';
    require_once 'backend_vendor.inc';
    require_once 'backend_form1099.inc';
    require_once 'selectvendors.inc';
    require_once "backend_dates.inc";

class csvimport_1099transactionupdate extends csvimport_base
{
    /* @var string $imptype */
    var $imptype = 'Vendor 1099 Transaction Update';

    /* @var VendorManager $vendMgr */
    var $vendMgr;

    /* @var int $cny */
    var $cny;

    /* @var string $recordtype */
    var $recordtype;

    /* @var backend_form1099_Utils $f1099_utilMgr */
    var $f1099_utilMgr;

    /* @var array $vendorids */
    var $vendorids;

    /* @var array $locs */
    var $locs;

    /* @var string|bool $ctxlocation */
    var $ctxlocation;

    /* @var string $separateForms */
    var $separateForms;

    /**
     * @param array $args
     */
    function __construct(/** @noinspection PhpUnusedParameterInspection */ $args = [])
    {
        parent::__construct('vendor');
        
        include 'csv_metadata_1099transactionupdate.inc';

        /** @noinspection PhpUndefinedVariableInspection */
        $this->recordtype = $import_record_type;
        $this->f1099_utilMgr = new backend_form1099_Utils();
        /** @noinspection PhpUndefinedVariableInspection */
        $this->imetaDict = $imetaDict ?? [];
        $this->vendMgr = Globals::$g->gManagerFactory->getManager('vendor');
        /** @noinspection PhpUndefinedVariableInspection */
        $this->imeta = $imeta;
        $this->cny = GetMyCompany();

        SetReportViewContext();

        //get all vendor ids for this company
        $vendorids = $this->vendMgr->DoQuery('QRY_VENDOR_SELECT_ALL_VENDORID', array(), true);
        foreach($vendorids as $value){
            $this->vendorids[$value['VENDORID']] = true ;
        }

        //get the location id
        $qry = "select name,record# from location where cny# = :1 and locationtype = 'E'";
        $this->locs = QueryResult(array($qry, $this->cny));
        //get the preferences for mega comp

        //getcontext returns nothing --root
        $this->ctxlocation = GetContextLocation();

        /** @noinspection PhpUndefinedVariableInspection */
        if( IsMultiEntityCompany() && $ctxlocation == '' ) {
            global $kMEid;
            GetModulePreferences($kMEid, $preferences);
            $this->separateForms     = $preferences['FORM1099'];                
        }            
            
    }

    /**
     * @param resource $fp
     * @param array    $hdrMap
     *
     * @return bool
     */
    function _GetImportCSVHeader($fp, &$hdrMap)
    {
        global $gErr;
           
        $result = ExecSimpleStmt("alter session set cursor_sharing=force");

        if ($result == false ) {
            return false;
        }
        $hdrs = fgetcsv($fp, 40000, $this->delim);
        if (!isset($hdrs) || !is_array($hdrs)) {
            $hdrs = fgetcsv($fp, 40000, $this->delim);
            if (feof($fp) || !isset($hdrs) || !is_array($hdrs) || count($hdrs)==0) {
                return false;
            }
        }
        $i = 0;
        $ok = true;
        foreach ($hdrs as $hdr){
            $hdrMap[isl_trim($hdr)] = $i++;    
            //checking for valid headers 
            if ( ! in_array($hdr, $this->imeta) && $hdr != self::DONOTIMPORT_TITLE ) {
                $gErr->addIAError('AP-0135', __FILE__ . ':' . __LINE__,
                "The header " . $hdr . " is invalid",['HDR'=>$hdr]);
                return false;
            }
                
        }
        return $ok;
    }

    /**
     * @param array $rowarray
     *
     * @return bool
     */
    function Import($rowarray)
    {
        $gErr = Globals::$g->gErr;

        $result = true;
        $source = 'csvimport_vendor1_import';
        $this->vendMgr->_QM->beginTrx($source);

        $fromdate = $rowarray['FROMDATE'];
        $todate = $rowarray['TODATE'];
        $updateflag = $rowarray['UPDATETRANS'];
        $vendorid = $rowarray['VENDORID'];
        $rowformtype = $rowarray['DEFAULT1099TYPE'];
        $rowformbox = isl_strtoupper($rowarray['DEFAULT1099BOX']);

        $form1099boxes = GetForm1099Boxes();            

        $validtypes = array_keys(GetForm1099Types());

        //check for mandatory & valid VENDORID field
        if($rowarray['VENDORID']) {         
            if (!$this->vendorids[$rowarray['VENDORID']]) {
                $gErr->addIAError('AP-0136', __FILE__ . ':' . __LINE__,
                "VENDORID column does not contain a valid vendor id for this company",
                ['VENDOR_ID'=>'VENDORID']);
                $result = false;
            }
        } else {           
            $gErr->addIAError('AP-0137', __FILE__ . ':' . __LINE__,
            "VENDORID is a mandatory field. Please provide suitable values.",
            ['VENDOR_ID'=>'VENDORID']);
            $result = false;
        }        
        //check for valid formtype
        if($rowformtype != '' && $rowformtype != 'NULL') {
            if(!in_array($rowformtype, $validtypes)) {
                $gErr->addIAError('AP-0138', __FILE__ . ':' . __LINE__,
                $rowformtype . " -This formtype is not a valid type. Please provide suitable values.",
                ['ROWFORMTYPE'=>$rowformtype]);
                $result = false;
            }
        }
            /*NOT REQUIRED TO CHECK FOR 1099 VENDOR AS IT CAN ALLOW SETTING DEFAULT VALUES
        if($vendorid){
        //check if 1099 vendor
        $res = GetForm1099Items($vendorid, 'vendor', $_vform);

        if(!$_vform['FORM1099TYPE'] && !$_vform['FORM1099BOX'] && $updateflag = 'Y'){
        $gErr->addIAError('AP-0139', __FILE__.':'.__LINE__,"Only 1099 vendor can update transactions.
        Please set default 1099 values first before proceeding further.");
        $result = false;			
        }
            }*/
        //check if box is valid for the formtype
        if($rowformbox != '' && $rowformtype != '') {
            if($rowformbox != 'NULL' && $rowformtype != 'NULL') {
                $validBoxes = is_array($form1099boxes[$rowformtype]) ? $form1099boxes[$rowformtype] : [];
                if(!array_key_exists($rowformbox, $validBoxes) ) {
                    $gErr->addIAError('AP-0140', __FILE__ . ':' . __LINE__,
                    $rowarray['DEFAULT1099BOX'] . "-This is an invalid box for the form type " . $rowformtype,
                    ['ROWARRAY_DEFAULT1099BOX'=>$rowarray['DEFAULT1099BOX'],'ROWFORMTYPE'=>$rowformtype]);
                    $result = false;
                }
            }
        }
        if($updateflag == 'Y' || $updateflag == 'A') {
            if($todate == '' || $fromdate == '') {
                $gErr->addError('AP-0130', __FILE__ . ':' . __LINE__,
                "The date range should be specified");
                $result = false;
            }
            if (!ValidateDate($todate) || !ValidateDate($fromdate)) {
                $gErr->addError("AP-0131", __FILE__ . ':' . __LINE__, "Invalid date");
                $result = false;
            }
            if($rowformtype == '' || $rowformbox == '') {
                $gErr->addError('AP-0132', __FILE__ . ':' . __LINE__,
                "DEFAULT1099TYPE and DEFAULT1099BOX fields are mandatory");
                $result = false;
            }     
                
        } else if ($updateflag == 'N') {
            if(($rowformtype == '' || $rowformbox == '') && $rowarray['NAME1099'] =='') {
                $gErr->addError('AP-0133', __FILE__ . ':' . __LINE__,
                "Please provide the required DEFAULT1099TYPE/DEFAULT1099BOX or NAME1099 fields for
                further processing ");
                $result = false;
            }
        } else{

            $gErr->addIAError('AP-0141', __FILE__ . ':' . __LINE__,
            "The UPDATETRANS field should be either 'Y','A' or 'N'",
            ['UPDATETRANS'=>'UPDATETRANS','Y'=>'Y','A'=>'A','N'=>'N']);
            $result = false;
        }

        //when type and box has NULL ,update prentry with NULL
        if ($rowarray['DEFAULT1099TYPE'] == 'NULL' || $rowarray['DEFAULT1099BOX'] == 'NULL') {
            $rowformtype = null;
            $rowformbox = null;        
            $form1099 = array(
            'FORM1099TYPE' => $rowformtype,
            'FORM1099BOX' => $rowformbox);
            $form1099entry = null;
        } else {
            //take values from csv file
            $form1099 = [
                'FORM1099TYPE' => $rowformtype,
                'FORM1099BOX'  => $rowformbox,
            ];
            $form1099entry = $rowformtype.'@'.$rowformbox;
        }            
             
        if ($rowarray['NAME1099'] == '') {
            /** @noinspection PhpUndefinedVariableInspection */
            if (is_array($_vform)) {
                /** @noinspection PhpUnusedLocalVariableInspection */
                $name1099 = $_vform['NAME1099'];
            }
        }
            
        //that is vendorpref(inserted) and vendor updated
        //if vendor is not 1099 raise error
        if ($result) {
            if ($updateflag == 'Y' || $updateflag == 'A') {
                //get all the related prentries for that vendor
                //update transaction 
                //get all bills with entry_date in between the date range
                //value in field form1099 in prentry is MISC@1

                $ok =  VendorManager::updateBillsFor1099VendorWithBoxType($vendorid,$form1099entry,$fromdate,$todate,$updateflag);
                if (!$ok) { 
                    $gErr->addError("AP-0125", __FILE__ . ":" . __LINE__,
                    "Update failed for this transaction");
                    $result = false;
                }
            }
            //update vendor table only             
            if($rowarray['NAME1099'] != '') {
                $form1099 = INTACCTarray_merge($form1099, array('NAME1099' => $rowarray['NAME1099']));
            }
            /** @noinspection PhpUnusedLocalVariableInspection */
            $vendupdate = $this->f1099_utilMgr->SetForm1099Items($vendorid, 'vendor', $form1099);
        }
                   
        $result = $result && $this->vendMgr->_QM->commitTrx($source);
        if ( ! $result) {
            $this->vendMgr->_QM->rollbackTrx($source);
        }

        return $result;
    }

        
}
