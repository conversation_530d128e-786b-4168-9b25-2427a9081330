<?php

/**
 * Metadata for the CSV Import of StandardTask object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2019 Sage Intacct Inc.
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 *
 */

$imeta = array(
    'STANDARDTASKID' => 'STANDARDTASK_ID',
    'NAME'           => 'STANDARDTASK_NAME',
    'DESCRIPTION'    => 'DESCRIPTION',
    'STATUS'         => 'ACTIVE',
    'ITEMID'         => 'ITEM_ID',
    'BILLABLE'       => 'BILLABLE',
    'ISMILESTONE'    => 'MILESTONE',
    'UTILIZED'       => 'UTILIZED',
    'PRIORITY'       => 'PRIORITY',
    'TIMETYPENAME'   => 'TIMETYPE_NAME',
    'TASKNO'         => 'WBS_CODE',
    'PARENTID'       => 'PARENT_STANDARDTASK_ID',
    'CLASSID'        => 'CLASS_ID',
);

if (CRESetupManager::isCREInstalled()) {
    $imeta['PRODUCTIONUNITDESC'] = 'PRODUCTIONUNITDESC';
    $imeta['STANDARDCOSTTYPES.STANDARDCOSTTYPEID'] = 'STANDARDCOSTTYPES_STANDARDCOSTTYPEID';
}

$imetagroom = array(
    'ACTIVE'    => 'booleanA',
    'BILLABLE'  => 'booleanF',
    'MILESTONE' => 'booleanF',
    'UTILIZED'  => 'booleanF',
);

$imetakeys = array(
    'objkey' => 'STANDARDTASKID',
    'csvkey' => 'STANDARDTASK_ID'
);
