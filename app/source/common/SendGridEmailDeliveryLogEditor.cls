<?

/**
 * Editor class for the SendGrid Email Delivery Log object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Intacct Corporation All, Rights Reserved
 */

/**
 * Editor class for the Sendgrid Email Delivery Log object
 */

class SendGridEmailDeliveryLogEditor extends FormEditor
{
    /**
     * @param string[] $_params
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);
        
        I18N::addTokens([
            ['id' => 'IA.ERROR_FIELDS_RESULT_SENDGRID'],
            ['id' => 'IA.ERROR_RESPONSE_CODE_AND_RESULT_DETAIL_SENDGRID']
        ]);
        I18N::getText();
    }
    
    /**
     * getEntityData
     *    helper function to get data from entity manager and format the data for display
     *
     * @param string   $entity   entity or object
     * @param string   $objId    entityid or objectid
     * @param string   $doctype
     * @param string[] $fields
     *
     * @return string[] the formatted result set
     */
    protected function getEntityData($entity, $objId, $doctype='', $fields=null)
    {
        // Get the data
        $entityData = parent::getEntityData($entity, $objId);
        
        //  Potentially track the stored access.
        AdvAuditTracking::addStoredAccess(AdvAuditTracking::getAccessType('sendgridemaildeliverylog'),
            $objId, AuditTrail::OPID_EMAIL_LOG);
        
        return $entityData;
    }
    
    /**
     * @return bool
     */
    protected function CanPrint()
    {
        return false;
    }
}
