<?php

require_once 'SforceConstants.inc';

/**
 * Manager file for the Authentication user object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class OAuthUserManager extends EntityManager
{
    /* @var string $moduleID */
    private $moduleID = '';

    /**
     * @param array|Pt_DataObjectDef $params
     */
    public function __construct($params = [])
    {
        parent::__construct($params);
        I18N::addToken(IntacctPartner::STATUS_INVALID);
        I18N::addToken(IntacctPartner::STATUS_VERIFIED);
        I18N::getText();
    }
    /**
     * Configures and returns the list of mapped users
     *
     * @param  string|null $accessType
     * @return array
     * @throws Exception
     */
    public function getSforceUserAccessList($accessType = null)
    {
        // Read the Intacct user list mapped to the SFDC users
        $queryParams = array(
            'filters' => [[['ACCESSTYPE', '=', $accessType]]]
        );
        $userList = $this->GetList($queryParams);
        if (empty($userList)) {
            return [];
        }

        $userNames = [];
        foreach ($userList as $u) {
            $userNames[] = str_replace("'", "\'", $u['PARTNER_USER']);
        }

        $inClause = "'" . implode("','", $userNames) . "'";

        // Read the list of the SFDC Intacct user list mapped to the SFDC users
        $client = SalesforceUtil::getDefaultSforceClient();
        $select = "SELECT username, id FROM User WHERE isactive = true and username in ($inClause)";
        $sforceUsers = $client->query($select);
        $sforceUsersMap = [];
        foreach ($sforceUsers->records as $sfu) {
            $sforceUsersMap[strtolower($sfu->Username)] = $sfu->Id;
        }

        // Read all protected custom settings for the mapped users
        $customSettings = $client->queryCustomSetting(SforceCustomSetting::CUSTOM_SETTING_USER);
        $customSettingsMap = [];
        if (!isset($customSettings->errors) && isset($sforceUsers->records)) {
            /* @var stdClass $pcs */
            foreach ($customSettings as $pcs) {
                if (isset($pcs->Id)) {
                    $customSettingsMap[$pcs->Id] = $pcs;
                }
            }
        }

        foreach ($userList as & $user) {
            // By default set the status as invalid
            I18N::addToken(IntacctPartner::STATUS_INVALID);
            $status = IntacctPartner::STATUS_INVALID;
            $customSettingId = $user['PARTNER_USERID'];
            if (isset($customSettingsMap[$customSettingId])) {
                // There is a protected custom setting record with the given Id
                $customSetting = $customSettingsMap[$customSettingId];
                $csUuid = $customSetting->{SforceNamespaceHandler::getUserUuidField()};
                if ($user['UUID'] === $csUuid) {
                    // UUID in custom setting record and mapping record in intacct are the same.
                    // Validate the salesforce user Id
                    $sforceUser = strtolower($user['PARTNER_USER']);
                    if (isset($sforceUsersMap[$sforceUser])) {
                        $userSforceInternalId = $sforceUsersMap[$sforceUser];
                        if ($userSforceInternalId === $customSetting->SetupOwnerId) {
                            // Check if the setup owner id in custom setting matches the salesforce
                            // user internal id
                            $status = IntacctPartner::STATUS_VERIFIED;
                        }
                    }
                }
            }
            $user['STATUS'] = I18N::getSingleToken($status);
        }

        return $userList;
    }

    /**
     * @param string    $ID
     * @param string[]  $fields
     *
     * @return array|false
     */
    function Get($ID, $fields=null)
    {
        // $fields = $this->getFields();

        $obj = parent::get($ID);
        $obj['STATUS'] = $this->getUserSforceStatus($obj);

        return $obj;
    }

    /**
     * @param array $user
     *
     * @return string
     */
    private function getUserSforceStatus($user)
    {
        if (isset($user) && isArrayValueProvided($user, 'PARTNER_USER')) {
            $client = SalesforceUtil::getDefaultSforceClient();
            if (!$client) {
                $client = SalesforceUtil::getDefaultSagePeopleClient();
            }

            $partnerUserName = str_replace("'", "\'", $user['PARTNER_USER']);
            $select = "SELECT Id, Username from User WHERE Username='$partnerUserName'";
            $sforceUser = $client->query($select);
            if (isset($sforceUser->records) && count($sforceUser->records) > 0) {
                $status = IntacctPartner::STATUS_VERIFIED;
            } else {
                $status = IntacctPartner::STATUS_INVALID;
            }


        } else {
            $status = IntacctPartner::STATUS_INVALID;
        }
        return I18N::getSingleToken($status);
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(& $values)
    {
        $validator = new OAuthUserValidator($this);
        $ok = $validator->validate($values);

        $ok = $ok && $this->registerUser($values);

        return $ok;
    }

    /**
     * @param array &$values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $validator = new OAuthUserValidator($this);
        $ok = $validator->validate($values);

        if ($ok) {
            $ok = $this->updateMapping($values);
        } else {
            $values['STATUS'] = IntacctPartner::STATUS_INVALID;
        }

        return $ok;
    }

    /**
     * Updates Intacct / Salesforce user mapping.
     *
     * @param array $values
     *
     * @return bool
     * @throws Exception
     */
    private function updateMapping(& $values)
    {
        global $gErr;
        $ok = true;

        $storedUser = $this->Get($values['UUID']);

        if ($values['USER'] !== $storedUser['USER'] || $values['PARTNER_USER'] !== $storedUser['PARTNER_USER']) {
            $source = 'OAuthUserManager::Set';
            /** @noinspection PhpUnusedLocalVariableInspection */
            $ok = $this->_QM->beginTrx($source);

            $sforceUser = $values['PARTNER_USER'];

            if ($values['USER'] !== $storedUser['USER']) {
                // Intacct user has changed
                $values['SHAREDSECRECT'] = TwoWayEncryptWithKey(OAuthBase::generateSecret(), "IA_INIT");
            }

            if ($values['PARTNER_USER'] !== $storedUser['PARTNER_USER']) {
                // Sforce user has changed
                $sforceUserId = $this->getSforceUserId($sforceUser);
                if (!$sforceUserId) {
                    /** @noinspection PhpUnusedLocalVariableInspection */
                    $ok = false;
                } else {
                    try {
                        // Try to assign relevant permissions
                        $permissionSets = SalesforceUtil::getPermissionSetsBySubscriptions(true);
                        $ok = SalesforceUtil::enableMultiplePermissionSets($sforceUserId, false, $permissionSets);
                        $values['PARTNER_USERID'] = $sforceUserId;
                    } catch (SalesforceRestError $e) {
                        $ok = false;
                        $msg = "Selected salesforce user ($sforceUser) cannot be used for mapping. Check its profile and permissions.";
                        $gErr->addIAError('SFDC-1140', __FILE__.':'.__LINE__,
                            $msg, ['SFORCE_USER' => $sforceUser]);
                    }
                }
            }

            $ok = $ok && $this->Delete($values['UUID']);
            if ($ok) {
                $values[':preserve_uuid'] = true;
                $ok = $this->add($values);
            }

            $ok = $ok && $this->_QM->commitTrx($source);

            $values['STATUS'] = $ok === true
                ? IntacctPartner::STATUS_VERIFIED : IntacctPartner::STATUS_INVALID;

            if (!$ok) {
                if(HasErrors() || HasWarnings()) {
                    $msg = "Failed to update a user in protected custom setting";
                    $gErr->addError('SFDC-1141', __FILE__ . ':' . __LINE__, $msg);
                    epp("$source: Error: $msg");
                }
                $this->_QM->rollbackTrx($source);
            }
        }

        return $ok;

    }
    /**
     * Deletes the user authentication record from the Intacct and SF databases.
     *
     * @param int|string $id
     *
     * @return bool
     */
    function Delete($id)
    {
        if ($this->getCallerModule() == Globals::$g->kSAGEPEOPLEid) {
            // Delete the user custom setting from SagePeople
            $this->deleteSagePeopleUserSettingByUuid($id);
        } else {
            // Delete the user custom setting from salesforce first
            $this->deleteSfdcUserSettingByUuid($id);
        }

        // Delete the record in Intacct. Do not check for successful deletion from salesforce
        $ok = parent::Delete($id);

        return $ok;
    }

    /**
     * Unmaps all the users by dropping the corresponding protected custom
     * settings in the salesforce database and deleting records in the Intacct
     * environment.
     *
     * @param bool $deleteOnSalesforce
     *
     * @return bool
     */
    public function unmapAllUsers($deleteOnSalesforce = true)
    {
        $ok = true;
        $allUsers = $this->getAllOAuthUsers();
        foreach ($allUsers as $user) {
            $id = $user['UUID'];
            if ($deleteOnSalesforce) {
                $ok = $ok && $this->Delete($id);
            } else {
                $ok = $ok && parent::Delete($id);
            }
        }

        return $ok;
    }

    /**
     * Maps an intacct user to the salesforce user.
     * 1. Creates a new record in the salesforce for the intacct user setting.
     * 2. Creates a new record in the intacct database (oauthuser entity)
     * @param array $user
     *
     * @return bool
     */
    public function registerUser(& $user)
    {
        global $gErr;
        $ok = false;

        $uuid = null;
        if (isset($user[':preserve_uuid']) && true === $user[':preserve_uuid']) {
            $uuid = $user['UUID'];
        }
        $sfUserInfo = $this->assignSfdcUserInfo($user, $uuid);

        if (!empty($sfUserInfo)) {
            $sfUserSettingId = $sfUserInfo['SFDC_USER_SETTING_ID'];

            $source = 'OAuthUserManager::registerUser';
            $ok = $this->_QM->beginTrx($source);

            try {
                $ok = $ok && $this->insertOauthUser($sfUserInfo);
            } catch (Exception $e) {
                $gErr->addError('SFDC-1115', __FILE__ . ':' . __LINE__,
                    'Generic Salesforce Error  : '. $e->getMessage());
                $ok = false;
            }

            $ok = $ok && $this->_QM->commitTrx($source);
            if ($ok) {
                $user['UUID'] = $sfUserInfo['UUID'];
                $user[':UUID'] = $sfUserInfo['UUID'];
                $user['STATUS'] = IntacctPartner::STATUS_VERIFIED;
            } else {
                if(HasErrors() || HasWarnings()) {
                    $msg = "Was not able to register a user in protected custom setting";
                    $gErr->addError('SFDC-1142', __FILE__ . ':' . __LINE__, $msg);
                    epp("$source: Error: $msg");
                }
                $this->_QM->rollbackTrx($source);

                $this->deleteSfdcUserSetting($sfUserSettingId);
            }
        }
        return $ok;
    }

    /**
     * The function validates the user and implements the following tasks:
     * 1. Generates UUID and shared secret for the user
     * 2. Pushes generated values info the protected custom field on SFDC.
     *
     * @param array       $user
     * @param string|null $uuid
     *
     * @return array returns an array with generated UUID, shared key, and
     *               the SFDC user record id if successful and the empty
     *               array otherwise
     * @throws Exception
     */
    public function assignSfdcUserInfo($user, $uuid = null)
    {
        $ret = [];
        $sfClient = SalesforceUtil::getDefaultSforceClient();
        if ($sfClient->getSfUserSession()) {
            if (is_null($uuid)) {
                $uuid = OAuthBase::generateUserUniqueId();
            }

            // Read the internal salesforce
            $sforceUser = $user['PARTNER_USER'];
            $sforceUserId = $this->getSforceUserId($sforceUser);

            // Assign all subscription based permission sets to the user
            // including the default Intacct_Connect
            $success = false;
            try {
                $permissionSets = SalesforceUtil::getPermissionSetsBySubscriptions(true);
                $success = SalesforceUtil::enableMultiplePermissionSets($sforceUserId, false, $permissionSets);
                if ($success) {
                    // Generate the shared secret and update the protedted custom setting record in salesforce
                    $sharedSecret = OAuthBase::generateSecret();
                    $recordId = $this->upsertSfdcUserInfo($user['USER'], $user['PARTNER_USER'], $uuid, $sharedSecret);
                    if ($recordId) {
                        $ret['PARTNER_USERID'] = $sforceUserId;
                        $ret['PARTNER_USER'] = $user['PARTNER_USER'];
                        $ret['USER'] = $user['USER'];
                        $ret['UUID'] = $uuid;
                        // Encrypt the shared secret before storing it into the database
                        $ret['SHARED_SECRET'] = TwoWayEncryptWithKey($sharedSecret, "IA_INIT");
                        $ret['SFDC_USER_SETTING_ID'] = $recordId;
                    }
                } else {
                    $msg = "Failed to assign permission sets to the user. Check your user profile.";
                    Globals::$g->gErr->addError("SFDC-1143", __FILE__.':'.__LINE__, $msg);
                }
            } catch (SalesforceRestError $e) {
                if ($success === false) {
                    $errorCode = "SFDC-1148";
                    $msg = 'Failed to assign permission sets to the user';
                } else {
                    $errorCode = "SFDC-1149";
                    $msg = 'Failed to store mapping in salesforce';
                }
                $e->logIt(__METHOD__ . $msg);
                Globals::$g->gErr->addError($errorCode, __FILE__.':'.__LINE__, $msg);
            }
        }

        return $ret;
    }

    /**
     * Creates a new record or updates an existing one to map an intacct user
     * with the salesforce user in the SFDC database.
     *
     * @param string      $intacctUserName
     * @param string      $sforceUserName
     * @param string|null $uuid         optional. Will be generated if not
     *                                  provided.
     * @param string|null $sharedSecret optional. Will be generated if not
     *                                  provided.
     *
     * @return bool|string salesforce internal record id if successful and
     *                     false otherwise.
     * @throws Exception
     */
    public function upsertSfdcUserInfo($intacctUserName, $sforceUserName, $uuid = null, $sharedSecret = null)
    {
        $ret = false;
        // Create a client based on default salesforce admin
        $client = SalesforceUtil::getDefaultSforceClient();
        if ($client) {
            // Query the internal salesforce Id for the given SF user
            $sforceUserId = $this->getSforceUserId($sforceUserName);
            if ($sforceUserId) {
                if (is_null($uuid)) {
                    $uuid = OAuthBase::generateUserUniqueId();
                }
                if (is_null($sharedSecret)) {
                    $sharedSecret = OAuthBase::generateSecret();
                }
                $postingData = '{ "' . SforceUserSettings::SFORCE_USER_NAME . '" : "' . $intacctUserName
                    . '", "' . SforceNamespaceHandler::getUserUuidField() . '": "' . $uuid
                    . '", "' . SforceUserSettings::SFORCE_USER_SETUP_OWNER_ID . '": "' . $sforceUserId
                    . '", "' . SforceNamespaceHandler::getUserSharedSecretField() . '": "' . $sharedSecret
                    . '" }';
                $result = $client->upsertCustomSetting(
                    SforceCustomSetting::CUSTOM_SETTING_USER, $postingData);
                if ($result->success) {
                    $ret = $result->id;
                }
            }
        }
        return $ret;
    }

    /**
     * Queries the internal id of the user with the given salesforce id.
     *
     * @param string $sforceUser
     *
     * @return string|false
     */
    public function getSforceUserId($sforceUser)
    {
        global $gErr;
        $ret = false;
        $sfClient = SalesforceUtil::getDefaultSforceClient();

        // Escape all the ' in salesforce user name
        $sforceUserEsc = str_replace("'", "\'", $sforceUser);

        $select = "SELECT id from User where Username = '$sforceUserEsc'";
        $userRecord = $sfClient->query($select);
        if (isset($userRecord->records[0])) {
            $ret = (string)$userRecord->records[0]->Id;
        } else {
            $msg = "Cannot find the user with the given ID in Salesforce: $sforceUser";
            $gErr->addIAError('SFDC-1144', __FILE__ . ':' . __LINE__,
                $msg, ['SFORCE_USER' => $sforceUser]);
        }
        return $ret;
    }

    /**
     * Creates a record in the OAUTHUSER table
     *
     * @param array $userInfo
     * @param string|null $accessType
     *
     * @return true
     * @throws Exception
     */
    private function insertOauthUser($userInfo, $accessType = null)
    {
        if (is_null($userInfo) || empty($userInfo)) {
            throw new InvalidArgumentException(
                "OAuthUserManager::insertOauthClient: SFDC user info are not provided.");
        }
        if (!isArrayValueProvided($userInfo, 'SFDC_USER_SETTING_ID')) {
            throw new InvalidArgumentException(
                "OAuthUserManager::insertOauthClient: SFDC user ID is not provided.");
        }
        if (!isArrayValueProvided($userInfo, 'UUID')) {
            throw new InvalidArgumentException(
                "OAuthUserManager::insertOauthClient: SFDC UUID is not provided.");
        }
        if (!isArrayValueProvided($userInfo, 'SHARED_SECRET')) {
            throw new InvalidArgumentException(
                "OAuthUserManager::insertOauthClient: SFDC user shared secret is not provided.");
        }

        if (is_null($accessType)) {
            global $kSALESFORCE2id;
            $accessType = $kSALESFORCE2id;
        }

        $gManagerFactory = Globals::$g->gManagerFactory;
        $oauthUser = [];

        $oauthClientManager = $gManagerFactory->getManager('oauthclient');
        $sfClientId = $oauthClientManager->GetRecordNo('ACCESSTYPE', $accessType);

        $userManager = $gManagerFactory->getManager('userinfo');
        $recordNo = $userManager->GetRecordNo('loginid', $userInfo['USER']);
        $oauthUser['USER'] = $recordNo;
        $oauthUser[':userkey'] = $recordNo;

        $oauthUser['UUID'] = $userInfo['UUID'];
        $oauthUser['ACCESSTYPE'] = $accessType;
        $oauthUser['ALGORITHM'] = OAuthFactory::ALGORITHM_HS256;
        $oauthUser['SHAREDSECRECT'] = $userInfo['SHARED_SECRET'];
        $oauthUser[':oauthclientkey'] = $sfClientId;
        $oauthUser['PARTNER_USER'] = $userInfo['PARTNER_USER'];
        $oauthUser['PARTNER_USERID'] = $userInfo['SFDC_USER_SETTING_ID'];

        $ok = parent::regularAdd($oauthUser);
        return $ok;
    }

    /**
     * Deletes the protected custom setting in SFDC representing a user record.
     *
     * @param string $sfUserSettingId
     *
     * @return bool
     */
    private function deleteSfdcUserSetting($sfUserSettingId)
    {
        $sfClient = SalesforceUtil::getDefaultSforceClient();
        $ok = $sfClient->delete(SforceNamespaceHandler::getUserSettingsField() . '/' . $sfUserSettingId);
        return $ok;
    }

    /**
     * Deletes the Intacct User Setting in from the salesforce database.
     *
     * @param string $salesforceUserId  internal id of the salesforce user.
     *
     * @return bool|mixed|SimpleXMLElement
     */
    public function deleteUserCustomSetting($salesforceUserId)
    {
        $client = SalesforceUtil::getDefaultSforceClient();
        $ret = $client->deleteCustomSetting(
            SforceCustomSetting::CUSTOM_SETTING_USER, $salesforceUserId);

        if ($ret->success === false) {
            // Failed deleting protected custom setting record
            if (!empty($ret->errors)) {
                // Log the list of errors
                foreach ($ret->errors as $error) {
                    logDiagnostics("CUSTOM_SETTING_DELETE", $error->message);
                }
            }
        }

        return $ret;
    }
    /**
     * Deletes the protected user setting in the SFDC by its UUID.
     *
     * @param string $uuid
     *
     * @return bool true if successful and false otherwise.
     */
    private function deleteSfdcUserSettingByUuid($uuid) {
        $user = $this->Get($uuid);
        $sfUserId = $user['PARTNER_USERID'];
        $ret = $this->deleteUserCustomSetting($sfUserId);
        $ok = $ret->success === true;
        if ($ok) {
            // Continue if the user is not an admin
            $sforceSetupManager = Globals::$g->gManagerFactory->getManager('salesforcesetup');
            $admin = $sforceSetupManager->getAdmin();
            if ($admin !== $user['PARTNER_USER']) {
                // Disable all subscription based permission sets for the user
                // including the default Intacct_Connect
                $permissionSets = SalesforceUtil::getPermissionSetsBySubscriptions(true);
                $sfUserRecordId = $this->getSforceUserId($user['PARTNER_USER']);
                if ($sfUserRecordId !== false) {
                    $ok = SalesforceUtil::disableMultiplePermissionSets($sfUserRecordId, $permissionSets);
                }
            }
        }

        return $ok;
    }

    /**
     * Deletes the protected user setting in the SagePeople by its UUID.
     *
     * @param string $uuid
     *
     * @return bool true if successful and false otherwise.
     */
    private function deleteSagePeopleUserSettingByUuid($uuid) {
        $filter = [
            'selects' => ['PARTNER_USERID'],
            'filters' => [[['UUID', '=', $uuid]]]
        ];
        $user = $this->GetList($filter);
        $sfUserId = $user[0]['PARTNER_USERID'];
        $client = SalesforceUtil::getDefaultSagePeopleClient();
        $ret = $client->deleteCustomSetting(
            SforceCustomSetting::CUSTOM_SETTING_USER, $sfUserId);

        if ($ret->success === false) {
            // Failed deleting protected custom setting record
            if (!empty($ret->errors)) {
                // Log the list of errors
                foreach ($ret->errors as $error) {
                    logDiagnostics("CUSTOM_SETTING_DELETE", $error->message);
                }
            }
        }
        $ok = $ret->success === true;
        return $ok;
    }

    /**
     * Returns collection of all mapped salesforce users.

     * @return array
     */
    public function getAllOAuthUsers()
    {
        $queryParams = array(
            'selects' => array('UUID', 'OAUTHCLIENT', 'PARTNER_USER', 'PARTNER_USERID'),
            'filters' => [[['ACCESSTYPE', '=', $this->getCallerModule()]]]
        );
        $result = $this->GetList($queryParams);

        return $result;

    }

    /**
     * Queries the partner user data from intacct databases. The result of the
     * query is cached locally.
     *
     * @param string $uuid
     * @param string $clientId
     *
     * @return PartnerUserOauthInfo|null
     */
    public function getPartnerUserInfo($uuid, $clientId)
    {
        if ($uuid == null) {
            throw new InvalidArgumentException("OAuthUserManager::getPartnerUserInfo: Missing UUID");
        }

        if ($clientId == null) {
            throw new InvalidArgumentException("OAuthUserManager::getPartnerUserInfo: Missing client ID");
        }

        $partnerUserInfo = null;

        static $userCache = [];
        if (isset($userCache[$uuid])) {
            return $userCache[$uuid];
        }

        $query =
            'SELECT ui.LOGINID AS LOGINID, ui.record# AS USER_RECORDNO, ui.PASSWORD AS ENCRYPTED_PASSWORD, ' .
            '       ou.PARTNER_USER as PARTNER_USER, ou.PARTNER_USERID as PARTNER_USER_ID, ' .
            '       ou.SHAREDSECRET as ENCRYPTED_SHARED_SECRET, ui.STATUS as USER_STATUS ' .
            'FROM OAUTHUSER ou, userinfo ui, OAUTHCLIENT oc ' .
            'WHERE ou.UUID = :1 ' .
            '      and ou.CNY# = ui.CNY# ' .
            '      and ou.USERKEY = ui.RECORD# ' .
            '      and ou.CNY# = oc.CNY# ' .
            '      and ou.OAUTHCLIENTKEY = oc.RECORD# ' .
            '      and oc.OAUTH_CLIENTID = :2';
        $resultSet = QueryResult(array($query, $uuid, $clientId));
        if (is_array($resultSet) && count($resultSet) === 1) {
            $iaUserLoginId = $resultSet[0]['LOGINID'];
            $iaUserRecordNo = $resultSet[0]['USER_RECORDNO'];
            $iaUserEncryptedPassword = $resultSet[0]['ENCRYPTED_PASSWORD'];
            $iaUserStatus = $resultSet[0]['USER_STATUS'];
            $partnerUserName = $resultSet[0]['PARTNER_USER'];
            $partnerUserId = $resultSet[0]['PARTNER_USER_ID'];
            $sharedSecret = TwoWayDecryptWithKey($resultSet[0]['ENCRYPTED_SHARED_SECRET'], "IA_INIT");

            $partnerUserInfo = new PartnerUserOauthInfo(
                $iaUserLoginId, $iaUserEncryptedPassword,
                $iaUserRecordNo, $iaUserStatus,
                $partnerUserName, $partnerUserId, $sharedSecret
            );

            $partnerUserInfo->setUuid($uuid);
            $partnerUserInfo->setClientId($clientId);

            $userCache[$uuid] = $partnerUserInfo;
        }
        return $partnerUserInfo;
    }

    /**
     * Here start code for sage people user map for Integration user, above code are not generic to use and required refactor
     * Below code may be duplicate code of above code base
     */

    /**
     * @param array $user
     *
     * @return bool
     */
    public function registerSagePeopleIntegrationUser($user)
    {
        global $gErr;
        $ok = false;

        $sfUserInfo = $this->assignSagePeopleUserInfo($user);

        if (!empty($sfUserInfo)) {
            $sfUserSettingId = $sfUserInfo['SFDC_USER_SETTING_ID'];

            $source = 'OAuthUserManager::registerUser';
            $ok = $this->_QM->beginTrx($source);

            try {
                $ok = $ok && $this->insertOauthUser($sfUserInfo, Globals::$g->kSAGEPEOPLEid);
            } catch (Exception $e) {
                $gErr->addError('SFDC-1115', __FILE__ . ':' . __LINE__,
                    'Generic Salesforce Error  : '. $e->getMessage());
                $ok = false;
            }

            if ($ok) {
                $this->_QM->commitTrx($source);
            } else {
                if(HasErrors() || HasWarnings()) {
                    $msg = "Was not able to register a user in protected custom setting";
                    $gErr->addError('SFDC-1142', __FILE__ . ':' . __LINE__, $msg);
                    epp("$source: Error: $msg");
                }
                $this->_QM->rollbackTrx($source);

                $this->deleteSagePeopleUserSetting($sfUserSettingId);
            }
        }
        return $ok;
    }

    /**
     * The function validates the user and implements the following tasks:
     * 1. Generates UUID and shared secret for the user
     * 2. Pushes generated values info the protected custom field on SFDC.
     *
     * @param array       $user
     *
     * @return array returns an array with generated UUID, shared key, and
     *               the SFDC user record id if successful and the empty
     *               array otherwise
     * @throws Exception
     */
    public function assignSagePeopleUserInfo($user)
    {
        $ret = [];
        $sfClient = SalesforceUtil::getDefaultSagePeopleClient();
        if ($sfClient->getSfUserSession()) {

            $uuid = OAuthBase::generateUserUniqueId();

            // Read the internal salesforce
            $sforceUser = $user['PARTNER_USER'];
            $sforceUserId = $this->getSagePeopleUserId($sforceUser);

            // Assign all subscription based permission sets to the user
            // including the default Intacct_Connect
            try {

                // Generate the shared secret and update the protedted custom setting record in salesforce
                $sharedSecret = OAuthBase::generateSecret();
                $recordId = $this->upsertSagePeopleUserInfo($user['USER'], $user['PARTNER_USER'], $uuid, $sharedSecret);
                if ($recordId) {
                    $ret['PARTNER_USERID'] = $sforceUserId;
                    $ret['PARTNER_USER'] = $user['PARTNER_USER'];
                    $ret['USER'] = $user['USER'];
                    $ret['UUID'] = $uuid;
                    // Encrypt the shared secret before storing it into the database
                    $ret['SHARED_SECRET'] = TwoWayEncryptWithKey($sharedSecret, "IA_INIT");
                    $ret['SFDC_USER_SETTING_ID'] = $recordId;
                }

            } catch (SalesforceRestError $e) {
                $msg = 'Failed to store mapping in sagepeople';
                $e->logIt(__METHOD__ . $msg);
                Globals::$g->gErr->addError("SFDC-1146", __FILE__.':'.__LINE__, $msg);
            }
        }

        return $ret;
    }

    /**
     * Queries the internal id of the user with the given salesforce id.
     *
     * @param string $sforceUser
     *
     * @return string|false
     */
    public function getSagePeopleUserId($sforceUser)
    {
        global $gErr;
        $ret = false;
        $sfClient = SalesforceUtil::getDefaultSagePeopleClient();

        // Escape all the ' in salesforce user name
        $sforceUserEsc = str_replace("'", "\'", $sforceUser);

        $select = "SELECT id from User where Username = '$sforceUserEsc'";
        $userRecord = $sfClient->query($select);
        if (isset($userRecord->records[0])) {
            $ret = (string)$userRecord->records[0]->Id;
        } else {
            $msg = "Cannot find the user with the given ID in Salesforce: $sforceUser";
            $gErr->addIAError('SFDC-1144', __FILE__ . ':' . __LINE__, $msg,
                ['SFORCE_USER' => $sforceUser]);
        }
        return $ret;
    }

    /**
     * Creates a new record or updates an existing one to map an intacct user
     * with the salesforce user in the SFDC database.
     *
     * @param string      $intacctUserName
     * @param string      $sforceUserName
     * @param string|null $uuid         optional. Will be generated if not
     *                                  provided.
     * @param string|null $sharedSecret optional. Will be generated if not
     *                                  provided.
     *
     * @return bool|string salesforce internal record id if successful and
     *                     false otherwise.
     * @throws Exception
     */
    public function upsertSagePeopleUserInfo($intacctUserName, $sforceUserName, $uuid = null, $sharedSecret = null)
    {
        $ret = false;
        // Create a client based on default salesforce admin
        $client = SalesforceUtil::getDefaultSagePeopleClient();
        if ($client) {
            // Query the internal salesforce Id for the given SF user
            $sforceUserId = $this->getSagePeopleUserId($sforceUserName);
            if ($sforceUserId) {
                if (is_null($uuid)) {
                    $uuid = OAuthBase::generateUserUniqueId();
                }
                if (is_null($sharedSecret)) {
                    $sharedSecret = OAuthBase::generateSecret();
                }
                $postingData = '{ "' . SforceUserSettings::SFORCE_USER_NAME . '" : "' . $intacctUserName
                    . '", "' . SagePeopleNamespaceHandler::getUserUuidField() . '": "' . $uuid
                    . '", "' . SforceUserSettings::SFORCE_USER_SETUP_OWNER_ID . '": "' . $sforceUserId
                    . '", "' . SagePeopleNamespaceHandler::getUserSharedSecretField() . '": "' . $sharedSecret
                    . '" }';
                $result = $client->upsertCustomSetting(
                    SforceCustomSetting::CUSTOM_SETTING_USER, $postingData);
                if ($result->success) {
                    $ret = $result->id;
                }
            }
        }
        return $ret;
    }

    /**
     * Deletes the protected custom setting in SFDC representing a user record.
     *
     * @param string $sfUserSettingId
     *
     * @return bool
     */
    private function deleteSagePeopleUserSetting($sfUserSettingId)
    {
        $sfClient = SalesforceUtil::getDefaultSagePeopleClient();
        $ok = $sfClient->delete(SagePeopleNamespaceHandler::getUserSettingsField() . '/' . $sfUserSettingId);
        return $ok;
    }

    /**
     * Unmaps all the users by dropping the corresponding protected custom
     * settings in the salesforce database and deleting records in the Intacct
     * environment.
     *
     * @param bool $deleteOnSalesforce
     *
     * @return bool
     */
    /*public function unmapAllSagePeopleUsers($deleteOnSalesforce = true)
    {
        $ok = true;
        $allUsers = $this->getAllOAuthSagePeopleUsers();
        foreach ($allUsers as $user) {
            $id = $user['UUID'];
            if ($deleteOnSalesforce) {
                $ok = $ok && $this->Delete($id);
            } else {
                $ok = $ok && parent::Delete($id);
            }
        }

        return $ok;
    }*/

    /**
     * Returns collection of all mapped salesforce users.

     * @return array
     */
    /*public function getAllOAuthSagePeopleUsers()
    {
        $queryParams = array(
            'selects' => array('UUID', 'OAUTHCLIENT', 'PARTNER_USER', 'PARTNER_USERID'),
            'filters' => [[['ACCESSTYPE', '=', Globals::$g->kSAGEPEOPLEid]]]
        );
        $result = $this->GetList($queryParams);

        return $result;

    }*/

    /**
     * @param string $moduleID
     */
    public function setCallerModule($moduleID)
    {
        $this->moduleID = $moduleID;
    }

    /**
     * @return string
     */
    public function getCallerModule()
    {
        return $this->moduleID;
    }

}

