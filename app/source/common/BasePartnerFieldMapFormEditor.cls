<?php

/**
 * Base partner field map form editor.
 * Holds the core partner field form editor logic.
 */
class BasePartnerFieldMapFormEditor extends FormEditor
{
    const LOOKUP_SELECTION = 'selectLookupObject';
    /** @var PartnerFieldMapService $partnerService */
    protected $partnerService;

    /**
     * @param array $_params
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);
    }
    /**
     * I18N. Collection of tokens used in sforcesetup.js
     *
     * @return string[]
     */
    protected function getFormTokens() : array
    {
        $this->textTokens = array_merge($this->textTokens, ['IA.SODOCUMENT', 'IA.SODOCUMENTENTRY', 'IA.SF_CONTRACT', 'IA.SF_CONTRACT_ITEM', 'IA.FIELD_MAPPING'] );
        return parent::getFormTokens();
    }

    /**
     * getJavaScriptFileNames
     *  override parent
     *
     * @return array list of js files
     */
    protected function getJavaScriptFileNames()
    {
        return array(
            '../resources/js/partnerfieldmap.js',
        );
    }

    /**
     * Dynamically set the title of the field mapping popup window.
     */
    protected function setPageTitle()
    {
        static $text = null;

        if ($text === null) {
            $text = getLocalizedTextWithThrow(I18N::tokenArrayToObjectArray([
                'IA.SFDC_CONTRACT_FIELD_MAPPING',
                'IA.SFDC_CONTRACT_LINE_FIELD_MAPPING',
                'IA.INTACCT_CONTRACT_FIELD_MAPPING',
                'IA.INTACCT_CONTRACT_LINE_FIELD_MAPPING',
                'IA.TEAMMEMBER_FIELD_MAPPING',
                'IA.ACCOUNT_FIELD_MAPPING',
                'IA.PROJECT_FIELD_MAPPING',
                'IA.PRODUCT_FIELD_MAPPING',
                'IA.TASK_FIELD_MAPPING',
                'IA.JOURNALENTRY_FIELD_MAPPING',
                'IA.JOURNALENTRYLINE_FIELD_MAPPING',
                'IA.SODOCUMENT_FIELD_MAPPING',
                'IA.CONTACT_FIELD_MAPPING',
                'IA.SODOCUMENTENTRY_FIELD_MAPPING',
                'IA.SODOCUMENT'
            ]));
        }
        $object = strtolower(Request::$r->{'_mappedObject'});
        switch ($object) {
            case 'sfcontract':
                $objectTitle = 'IA.SFDC_CONTRACT_FIELD_MAPPING';
                break;
            case 'sfcontractdetail':
                $objectTitle = 'IA.SFDC_CONTRACT_LINE_FIELD_MAPPING';
                break;
            case 'contract':
                $objectTitle = 'IA.INTACCT_CONTRACT_FIELD_MAPPING';
                break;
            case 'contractdetail':
                $objectTitle = 'IA.INTACCT_CONTRACT_LINE_FIELD_MAPPING';
                break;
            case 'teammember':
                $objectTitle = 'IA.TEAMMEMBER_FIELD_MAPPING';
                break;
            case 'account':
                $objectTitle = 'IA.ACCOUNT_FIELD_MAPPING';
                break;
            case 'project':
                $objectTitle = 'IA.PROJECT_FIELD_MAPPING';
                break;
            case 'product':
                $objectTitle = 'IA.PRODUCT_FIELD_MAPPING';
                break;
            case 'task':
                $objectTitle = 'IA.TASK_FIELD_MAPPING';
                break;
            case 'journalentry':
                $objectTitle = 'IA.JOURNALENTRY_FIELD_MAPPING';
                break;
            case 'journalentryline':
                $objectTitle = 'IA.JOURNALENTRYLINE_FIELD_MAPPING';
                break;
            case 'contact':
                $objectTitle = 'IA.CONTACT_FIELD_MAPPING';
                break;
            case 'sodocument':
                $objectTitle = 'IA.SODOCUMENT_FIELD_MAPPING';
                break;
            case 'sodocumententry':
                $objectTitle = 'IA.SODOCUMENTENTRY_FIELD_MAPPING';
                break;
            default :
                $objectTitle = 'IA.FIELD_MAPPING';
        }

        $this->setTitle(GT($text, $objectTitle));
    }

    /**
     * Specifies the visibility of the custom fields mapping section on the
     * page.
     *
     * @param array $obj
     */
    protected function configureCustomMappingSection(& $obj)
    {
        $view = $this->getView();

        $isFieldMap = $this->isModuleHasFieldMap();
        $sectionVisible = $isFieldMap == 'true';

        $sectionVisible = $sectionVisible &&
            Editor_ShowNewState !== $this->state &&
            isArrayValueProvided($obj, 'AVAILABLE_SFORCE_FIELDS') &&
            is_array($obj['AVAILABLE_SFORCE_FIELDS']) && count($obj['AVAILABLE_SFORCE_FIELDS']) > 0;

        $view->findAndSetProperty(
            array('id' => 'custom_mapping_section'), array('hidden' => !$sectionVisible),
            EditorComponentFactory::TYPE_SECTION
        );

        if ($sectionVisible) {
            $this->configureCustomMappingGrid($obj);
        }
    }

    /**
     * Check module has field map.
     *
     * @return string
     */
    protected function isModuleHasFieldMap()
    {
        return GetPreferenceForProperty($this->getModule(), 'ISFIELDMAP');
    }

    /**
     * Configures the custom mapping grid on the page:
     * 1. Sets the valid values and valid labels for the drop down list box
     *    in the Intacct Field  column.
     * 2. Sets the valid values and valid labels for the drop down list box
     *    in the Salesforce Field  column.
     * 3. Sets the default selection for the sync rule column in the grid
     *    based on value from the 'MASTER' field
     * 4. Populates the grid with the custom mappings from the database.
     *
     * @param array $obj
     */
    private function configureCustomMappingGrid(& $obj)
    {
        $sectionId = 'custom_mapping_section';

        $this->configureIntacctFieldInGrid($obj, $sectionId);

        $this->configureSalesforceFieldInGrid($obj, $sectionId);

        $this->configureSyncRuleDefaultInGrid($obj, $sectionId);

        if ($obj['REFRESH_ON_ERROR'] !== true) {
            $this->populateCustomMappings($obj);
        }
        $this->configureSubscriptionObjectLookUpInGrid();
    }

    /**
     * Dynamically updates the settings for the INTACCT_FIELD column in the grid
     * found in the section with the given section Id.
     *
     * @param array  $obj
     * @param string $sectionId
     */
    private function configureIntacctFieldInGrid(& $obj, $sectionId)
    {
        $this->getElementsByPathAndSectionId('INTACCT_FIELD', $sectionId, $matches);

        $validityProperties = $obj['INTACCT_FIELDS_PROPERTIES'];
        $matches[0]['type']['validlabels'] = array_values($validityProperties['validvalues']);
        $matches[0]['type']['validvalues'] = array_values($validityProperties['validlabels']);
    }

    /**
     * Dynamically updates the settings for the SALESFORCE_FIELD column in the
     * grid found in the section with the given section Id.
     *
     * @param array  $obj
     * @param string $sectionId
     *
     * @throws IAException
     */
    private function configureSalesforceFieldInGrid(& $obj, $sectionId)
    {
        $this->getElementsByPathAndSectionId('SALESFORCE_FIELD', $sectionId, $matches);
        $sforceData = $obj['AVAILABLE_SFORCE_FIELDS']['listedData'];
        $matches[0]['type']['validlabels'] = array_map(function ($v){ return $v['name']; }, $sforceData);
        $matches[0]['type']['validvalues'] = array_map(function ($v){ return $v['label']; }, $sforceData);
    }

    /**
     * Dynamically updates the settings for the SYNC_RULE column's default
     * value in the grid found in the section with the given section Id.
     *
     * @param array  $obj
     * @param string $sectionId
     */
    private function configureSyncRuleDefaultInGrid(& $obj, $sectionId)
    {
        $this->getElementsByPathAndSectionId('SYNC_RULE', $sectionId, $matches);
        $default = SforceSyncRule::convertToSforceSyncRule($obj['MASTER']);
        $matches[0]['default'] = $default;
    }

    /**
     * Dynamically updates the valid values in Subscription Object
     * Look-up for Custom Mapping Grid
     *
     */
    private function configureSubscriptionObjectLookUpInGrid() {
        $view = $this->getView();
        $grid = [];
        $view->findComponents(
            array('path' => 'CUSTOM_MAPPING'),
            EditorComponentFactory::TYPE_GRID,
            $grid
        );
        if (isset($grid) && isset($grid[0])) {
            $grid = $grid[0];
            $subscriptionObject = null;
            $grid->findComponents(array('path' => 'SUBSCRIPTIONOBJ'), EditorComponentFactory::TYPE_FIELD, $subscriptionObject);
            if ( $subscriptionObject && $subscriptionObject[0] ) {
                /**
                 * @var EditorField $editorField
                 */
                $editorField = $subscriptionObject[0];

                $subscriptionObjectType = $editorField->getProperty('type');
                $data = array_combine($subscriptionObjectType['validvalues'], $subscriptionObjectType['_validivalues']);
                $labeldata = array_combine($subscriptionObjectType['validvalues'], $subscriptionObjectType['validlabels']);
                $exclusionList = ['Project Opportunity', 'GL Journal', 'GL Account', 'Contract',
                    'Company', 'Salesforce Contract', 'Sales Document', 'SF Contract Line Opportunity',
                    'SF Contract Detail Opportunity Product'];
                foreach ($exclusionList as $exclusion) {
                    unset($data[$exclusion]);
                    unset($labeldata[$exclusion]);
                }
                $subscriptionObjectType['validlabels'] = array_values($labeldata);
                $subscriptionObjectType['validvalues'] = array_keys($data);
                $subscriptionObjectType['_validivalues'] = array_values($data);
                $editorField->setProperty('type', $subscriptionObjectType);
            }
        }
    }

    /**
     * @param string $sectionTitle
     *
     * @return array
     * @throws IAException
     */
    private function getSectionFromView($sectionTitle)
    {
        $view = $this->getView();
        $sections = $view->params['child'][0]['pages']['child'][0]['page'][0]['child'][0]['section'];
        foreach ($sections as $section) {
            if ($sectionTitle === $section['id']) {
                return $section;
            }
        }
        throw new IAException("Cannot find a section with the name $sectionTitle");
    }

    /**
     * Initializes the custom mapping grid with the data from the database.
     *
     * @param array $obj
     */
    private function populateCustomMappings(& $obj)
    {
        $mappedCustomFields = $obj['MAPPED_CUSTOM_FIELDS'];
        $index = 0;

        foreach ( $mappedCustomFields as $mapping) {
            unset($obj['CUSTOM_MAPPING'][$index]['__dummy']);
            unset($obj['CUSTOM_MAPPING'][$index]['_isNewLine']);

            $obj['CUSTOM_MAPPING'][$index]['INTACCT_FIELD'] = $mapping['FULL_NAME'];

            $partnerField = $mapping['PARTNERFIELD'];
            $partnerFieldValue =
                $obj['AVAILABLE_SFORCE_FIELDS']['namedFields'][$partnerField]['label'];
            $obj['CUSTOM_MAPPING'][$index]['SALESFORCE_FIELD'] = $partnerFieldValue;

            $obj['CUSTOM_MAPPING'][$index]['SYNC_RULE'] =
                SforceSyncRule::convertToSforceSyncRule($mapping['MASTER']);

            $obj['CUSTOM_MAPPING'][$index]['STATUS'] = $mapping['STATUS'];
            if ( !empty($mapping['SUBSCRIPTIONOBJ']) ) {
                $obj['CUSTOM_MAPPING'][$index]['SUBSCRIPTIONOBJ'] = $mapping['SUBSCRIPTIONOBJ'];
            }

            $index++;
        }

        // Add a record for a blank row
        $obj['CUSTOM_MAPPING'][$index]['__dummy'] = '';
        $obj['CUSTOM_MAPPING'][$index]['_isNewLine'] = true;
    }

    /**
     * Specifies the visibility of the standard fields mapping section on the
     * page.
     *
     * @param array $obj
     */
    protected function configureStandardMappingSection(& $obj)
    {
        $sectionVisible =
            Editor_ShowNewState !== $this->state &&
            isArrayValueProvided($obj, 'STANDARD_FIELDS') &&
            is_array($obj['STANDARD_FIELDS']) && count($obj['STANDARD_FIELDS']) > 0;

        $view = $this->getView();
        $view->findAndSetProperty(
            array('id' => 'standard_mapping_section'), array('hidden' => !$sectionVisible),
            EditorComponentFactory::TYPE_SECTION
        );

        if ($sectionVisible) {
            $this->configureStandardMappingGrid($obj);
        } else {
            $obj['STANDARD_MAPPING'] = [];
        }
    }

    /**
     * Configures the standard mapping grid on the page:
     * 1. Makes the grid read only.
     * 2. Populate the Intacct Field column
     * 3. Populate the Salesforce Field column
     *
     * @param array $obj
     */
    private function configureStandardMappingGrid(& $obj)
    {
        $view = $this->getView();

        // Configure Intacct Field column in the grid
        $this->getDefaultMappingElementsByPath('INTACCT_FIELD', $matches);
        $index = 0;
        foreach ( $obj['STANDARD_FIELDS'] as $field) {
            // Keep collecting available validlabes and validvalues
            $matches[0]['type']['validlabels'][] = $field['INTACCTFIELD'];
            $matches[0]['type']['validvalues'][] = $field['FULL_NAME'];

            // Select the proper item in the webcombo
            $obj['STANDARD_MAPPING'][$index++]['INTACCT_FIELD'] = $field['FULL_NAME'];
        }

        $this->getDefaultMappingElementsByPath('SALESFORCE_FIELD', $matches);
        $index = 0;
        foreach ( $obj['STANDARD_FIELDS'] as $field) {
            // Keep collecting available validlabes and validvalues
            $matches[0]['type']['validlabels'][] = $field['PARTNERFIELD'];
            $matches[0]['type']['validvalues'][] = $field['PARTNERFIELD'];

            // Select the proper item in the webcombo
            $obj['STANDARD_MAPPING'][$index++]['SALESFORCE_FIELD'] = $obj['AVAILABLE_SFORCE_FIELDS']['namedFields'][$field['PARTNERFIELD']]['label']
                ?: $field['PARTNERFIELD'];
        }

        if ( !isset($this->partnerService) ) {
            $master = $obj['MASTER'];
        } else {
            $master = $this->partnerService->getMappingInitParams()->getMaster();
        }

        $fieldMappingFeature = FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('FIELD_MAPPING_V2_ENABLE');
        $grid = [];
        if (!$fieldMappingFeature) {
            // make the grid read only
            $view->findAndSetProperty(
                array('path' => 'STANDARD_MAPPING'), array('readonly' => 'true'),
                EditorComponentFactory::TYPE_GRID
            );
            $view->findComponents(
                array('path' => 'STANDARD_MAPPING'),
                EditorComponentFactory::TYPE_GRID,
                $grid
            );
            if (isset($grid) && isset($grid[0])) {
                $grid = $grid[0];
                $grid->findAndSetProperty(array('path' => 'STATUS'), array('hidden' => true), EditorComponentFactory::TYPE_GRID_COLUMN);
            }
        } else {
            if( 'T' === $master ) {
                // make the grid read only
                $view->findAndSetProperty(
                    array('path' => 'STANDARD_MAPPING'), array('readonly' => 'true'),
                    EditorComponentFactory::TYPE_GRID
                );
            } else {
                $view->findComponents(
                    array('path' => 'STANDARD_MAPPING'),
                    EditorComponentFactory::TYPE_GRID,
                    $grid
                );
                if (isset($grid) && isset($grid[0])) {
                    $grid = $grid[0];
                    $grid->findAndSetProperty(array('path' => 'INTACCT_FIELD'), array('readonly' => true), EditorComponentFactory::TYPE_GRID_COLUMN);
                    $grid->findAndSetProperty(array('path' => 'SALESFORCE_FIELD'), array('readonly' => true), EditorComponentFactory::TYPE_GRID_COLUMN);
                }
            }
        }

        $index = 0;
        foreach ($obj['STANDARD_FIELDS'] as $field) {
            $syncRule = isArrayValueProvided($field, 'MASTER') ? SforceSyncRule::convertToSforceSyncRule($field['MASTER'])
                : SforceSyncRule::convertToSforceSyncRule($master);
            $obj['STANDARD_MAPPING'][$index]['SYNC_RULE'] = $syncRule;
            $obj['STANDARD_MAPPING'][$index]['STATUS'] = $field['STATUS'];
            $obj['STANDARD_MAPPING'][$index]['NILLABLE'] = $field['NILLABLE'];
            if (isArrayValueProvided($field, 'READONLYFLDS')) {
                $obj['STANDARD_MAPPING'][$index]['READONLYFLDS'] = $field['READONLYFLDS'];
            }
            if (!empty($field['SUBSCRIPTIONOBJ'])) {
                $obj['STANDARD_MAPPING'][$index]['SUBSCRIPTIONOBJ'] = $this->entityMgr->transformValidiValueToValidValue(
                    'SUBSCRIPTIONOBJ', $field['SUBSCRIPTIONOBJ']);
            }
            $index++;
        }
        logToFileInfo(__FILE__ . "  " . __FUNCTION__ . " Field Map Form Editor STANDARD_MAPPING :: " . json_encode($obj['STANDARD_MAPPING']));
    }

    /**
     * Configured the General section of the page. By default it is visible
     * unless instructed otherwise.
     *
     * @param array     $obj
     * @param bool|null $showObjectSelection
     */
    protected function configureHeaderSection(& $obj, $showObjectSelection = null)
    {
        $view = $this->getView();

        if ($showObjectSelection === null) {
            $showObjectSelection = true;
            if (Request::$r->{'_showObjectSelection'}) {
                $showObjectSelection = Request::$r->{'_showObjectSelection'};
                $showObjectSelection = '' != $showObjectSelection &&
                    'false' != $showObjectSelection;
            }
        }
        if (isset($showObjectSelection) && '' !== $showObjectSelection) {
            $view->findAndSetProperty(
                array('id' => 'general_settings_section'), array('hidden' => !$showObjectSelection),
                EditorComponentFactory::TYPE_SECTION
            );

            if ($showObjectSelection) {
                $this->configureObjectControlAvailablility($obj);
            }

            $this->configureDocumentNameVisibility($obj);

        }

        $obj['ACCESS_FROM_MENU'] = $showObjectSelection;
    }

    /**
     * @param array $obj
     */
    private function configureObjectControlAvailablility(& $obj)
    {
        $view = $this->getView();
        $readOnly = $this->getState() !== Editor_ShowNewState;
        $controls = ['PARTNER', 'MASTER', 'INTACCTOBJECT', 'INTACCTDOCUMENTTYPE'];
        foreach ($controls as $control) {
            $view->findAndSetProperty(
                array('path' => $control), array('readonly' => $readOnly),
                EditorComponentFactory::TYPE_FIELD
            );
        }

        $view->findAndSetProperty(
            array('id' => 'refresh_mapping_selection'), array('hidden' => $readOnly),
            EditorComponentFactory::TYPE_BUTTONS
        );

        // Set the status of Refresh Selection button
        $obj['REFRESH_SELECTION_BUTTON_STATE'] = $readOnly;
    }

    /**
     * Sets the visibility of the document type field based on Intacct Object
     * selection.
     *
     * @param array $obj
     */
    private function configureDocumentNameVisibility(& $obj)
    {
        $view = $this->getView();
        $documentTypeHidden = true;
        if (in_array('INTACCTDOCUMENTTYPE', $obj)) {
            $documentTypeHidden = false;
        } else if (isset($this->partnerService)){
            $iaDocumentType = $this->partnerService->getMappingInitParams()->getIaDocumentType();
            if (isset($iaDocumentType) && '' != $iaDocumentType) {
                $obj['INTACCTDOCUMENTTYPE'] = $iaDocumentType;
                $documentTypeHidden = false;
            }
        }
        $view->findAndSetProperty(
            array('path' => 'INTACCTDOCUMENTTYPE'), array('hidden' => $documentTypeHidden),
            EditorComponentFactory::TYPE_SECTION
        );
    }

    /**
     * Handles ajax requests
     *
     * @param string $cmd
     *
     * @return bool
     */
    protected function runAjax($cmd)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ok = true;

        switch ( $cmd ) {
            case 'refreshSelection':
                $ok = $this->ajaxRefreshSelection();
                break;
            case  self::LOOKUP_SELECTION:
                $ok = $this->enableLookupObjectField(Request::$r->sforceFldVal);
                break;
            case 'resetStandardFieldMap':
                $ok = $this->resetStandardFieldMapping(Request::$r->intacctobj, Request::$r->partnerobj, Request::$r->doctype);
                echo json_encode($ok);
                break;
            default:
                $ok = parent::runAjax($cmd);
                break;
        }

        return $ok;
    }

    /**
     * Responds to an ajax request from the page when the user clicks
     * Refresh button
     *
     * @return bool
     */
    private function ajaxRefreshSelection()
    {
        $ok = true;

        try {
            $results = [];

            /* @var array $data */
            $data = $this->getData();

            $service = PartnerFieldMapService::getPartnerFieldMapService($data['PARTNER']);
            $service->initializeMappingInitParams($data);

            /** @var PartnerFieldMapViewer $viewer */
            $viewer = PartnerFieldMapViewer::getInstance($service->getMappingInitParams(), $this);
            $customSyncRuleEditable = $viewer->isCustomMappingSyncRuleEditable();
            $results['CUSTOM_MAPPING_SYNC_RULE_EDITABLE'] = $customSyncRuleEditable;
            $results['CUSTOM_MAPPING_DEFAULT_SYNC_RULE'] = $viewer->getDefaultSyncRule();

            $isSyncRuleEditable = $viewer->isCustomMappingSyncRuleEditable();
            $service->populateEntityData($data, $isSyncRuleEditable);

            // Read and return standard data
            if ($service->isStandardSettingVisible()) {
                $standardMapping = $this->ajaxRefreshStandardMappingSelection($data);
                if ($standardMapping) {
                    $results['STANDARD_MAPPING'] = $standardMapping;
                } else {
                    $results['STANDARD_MAPPING'] = [];
                    $results['STANDARD_MAPPING_HIDDEN'] = true;
                }
            } else {
                $results['STANDARD_MAPPING_HIDDEN'] = true;
            }

            // Read and return custom data
            $results['CUSTOM_MAPPING'] = $this->ajaxRefreshCustomMappingSelection($data);

            $results['DYNAMICALLY_UPDATED'] = 'true';

            echo json_encode($results);
        } catch (Exception $e) {
            $msg = 'Unable to get Salesforce Account Details. ' . $e->getMessage();
            echo $msg;
            $ok = false;
        }
        return $ok;

    }

    /**
     * Converts json string to PHP structure.
     *
     * @return array|null
     */
    private function getData()
    {
        $jsondata = Request::$r->_data;
        if (isset($jsondata) && $jsondata != '') {
            $data = Util_DataRecordFormatter::jsonToPhp($jsondata);
        } else {
            $data = null;
        }
        return $data;
    }

    /**
     * function returning true if type of salesforcefield is lookup or else false
     *
     * @param string|array  $selectedSforceFieldVal
     *
     * @return bool
     * @throws IAException
     */
    private function enableLookupObjectField($selectedSforceFieldVal)
    {
        $data = $this->getData();
        if(!isset($data['AVAILABLE_SFORCE_FIELDS'])){
            $service = PartnerFieldMapService::getPartnerFieldMapService($data['PARTNER']);
            $service->initializeMappingInitParams($data);
            $viewer = PartnerFieldMapViewer::getInstance($service->getMappingInitParams(), $this);
            $isSyncRuleEditable = $viewer->isCustomMappingSyncRuleEditable();
            $service->populateEntityData($data, $isSyncRuleEditable);
        }
        if (is_array($selectedSforceFieldVal)) {
            $result = [] ;
            foreach ($selectedSforceFieldVal as $key => $val){
                $sforceSelectedType = $data['AVAILABLE_SFORCE_FIELDS']['labeledFields'][$val]['type'];
                if ($sforceSelectedType === 'reference') {
                    $result[$key] =  true;
                }
                else{
                    $result[$key] =  false;
                }
            }
            echo json_encode($result);
        } else {
            $result = false;
            $sforceSelectedType = $data['AVAILABLE_SFORCE_FIELDS']['labeledFields'][$selectedSforceFieldVal]['type'];
            if ($sforceSelectedType === 'reference') {
                $result = true;
            }
            echo json_encode($result);
        }
        return true;
    }

    /**
     * Responds to the ajax request from the page and returns the following data
     * for the standard mapping:
     * 1. Metadata for the PARTNER_FIELD (validvalues / validlabels)
     * 2. Metadata for the INTACCT_FIELD (validvalues / validlabels)
     * 3. The list of the mapped fields.
     *
     * @param array $data
     *
     * @return array
     */
    private function ajaxRefreshStandardMappingSelection($data)
    {
        $standardMapping = [];
        $master = $data['MASTER'];

        // For Contact master is always Partner
        if (in_array($data['INTACCTOBJECT'], ['CONTACT'])) {
            $master = 'Partner';
        }

        if (isArrayValueProvided($data, 'STANDARD_FIELDS') && count($data['STANDARD_FIELDS']) > 0)
        {
            foreach ( $data['STANDARD_FIELDS'] as $field) {
                // Set the valid value / valid label and the selection for each
                // INTACCT_FIELD for each standard mapping
                $standardMapping['INTACCT_FIELD']['validlabels'][] = $field['INTACCTFIELD'];
                $standardMapping['INTACCT_FIELD']['validvalues'][] = $field['FULL_NAME'];
                $standardMapping['INTACCT_FIELD']['value'][] = $field['FULL_NAME'];

                // Set the valid value / valid label and the selection for each
                // INTACCT_FIELD for each standard mapping
                $standardMapping['PARTNER_FIELD']['validlabels'][] = $field['PARTNERFIELD'];
                $standardMapping['PARTNER_FIELD']['validvalues'][] = $field['PARTNERFIELD'];
                $standardMapping['PARTNER_FIELD']['value'][] = $field['PARTNERFIELD'];

                $m = [];
                $m['INTACCTFIELD'] = $field['FULL_NAME'];
                $m['PARTNERFIELD'] = $data['AVAILABLE_SFORCE_FIELDS']['namedFields'][$field['PARTNERFIELD']]['label']
                    ?: $field['PARTNERFIELD'];
                if (isArrayValueProvided($field, 'MASTER')) {
                    $master = $field['MASTER'];
                }
                $m['MASTER'] = SforceSyncRule::convertToSforceSyncRule($master);
                $m['STATUS'] = $field['STATUS'];
                $m['NILLABLE'] = $field['NILLABLE'];
                if ( !empty($field['SUBSCRIPTIONOBJ']) ) {
                    $m['SUBSCRIPTIONOBJ'] = $this->entityMgr->transformValidiValueToValidValue(
                        'SUBSCRIPTIONOBJ', $field['SUBSCRIPTIONOBJ']);
                }
                $standardMapping['VALUES'][] = $m;
            }
        } else {
            $standardMapping = false;
        }

        return $standardMapping;
    }

    /**
     * Responds to the ajax request from the page and returns the following data
     * for the custom mapping:
     * 1. Metadata for the PARTNER_FIELD (validvalues / validlabels)
     * 2. Metadata for the INTACCT_FIELD (validvalues / validlabels)
     * 3. The list of the mapped fields.
     *
     * @param array $data
     *
     * @return array
     */
    private function ajaxRefreshCustomMappingSelection($data)
    {
        $customMapping = [];
        $master = $data['MASTER'];

        $sforceData = $data['AVAILABLE_SFORCE_FIELDS']['listedData'] ?? [];
        $customMapping['PARTNER_FIELD']['validlabels'] = array_map(function ($v){ return $v['name']; }, $sforceData);
        $customMapping['PARTNER_FIELD']['validvalues'] = array_map(function ($v){ return $v['label']; }, $sforceData);

        $validityProperties = $data['INTACCT_FIELDS_PROPERTIES'];
        $customMapping['INTACCT_FIELD']['validlabels'] = array_values($validityProperties['validvalues']);
        $customMapping['INTACCT_FIELD']['validvalues'] = array_values($validityProperties['validlabels']);

        $customMapping['VALUES'] = [];
        if (isArrayValueProvided($data, 'MAPPED_CUSTOM_FIELDS') && count($data['MAPPED_CUSTOM_FIELDS']) > 0) {
            foreach ( $data['MAPPED_CUSTOM_FIELDS'] as $mapping) {
                $m = [];
                $m['INTACCTFIELD'] = $mapping['FULL_NAME'];
                $m['PARTNERFIELD'] = $data['AVAILABLE_SFORCE_FIELDS']['namedFields'][$mapping['PARTNERFIELD']]['label'];

                if (isArrayValueProvided($mapping, 'MASTER')) {
                    $master = $mapping['MASTER'];
                }
                $m['MASTER'] = SforceSyncRule::convertToSforceSyncRule($master);

                $m['STATUS'] = $mapping['STATUS'];
                if ( !empty($mapping['SUBSCRIPTIONOBJ']) )
                {
                    $m['SUBSCRIPTIONOBJ'] = $mapping['SUBSCRIPTIONOBJ'];
                }
                $customMapping['VALUES'][] = $m;
            }
        } else {
            $m = [];
            $m['INTACCTFIELD'] = '';
            $m['PARTNERFIELD'] = '';
            $m['MASTER'] = '';
            $m['STATUS'] = '';
            $customMapping['VALUES'][] = $m;
        }

        return $customMapping;
    }

    /**
     * Show Duplicate button.
     *
     * @return bool
     */
    protected function CanDuplicate()
    {
        return false;
    }

    /**
     * Show Print button
     *
     * @return bool
     */
    protected function CanPrint()
    {
        return false;
    }

    /**
     * Get Owner Object
     *
     * @return string
     */
    public function GetOwnerObject()
    {
        $ownerObject = Request::$r->GetCurrentObjectValueByPath("INTACCTOBJECT");
        return $ownerObject;
    }

    /**
     * @return string
     */
    protected function getModule()
    {
        return Globals::$g->kSALESFORCE2id;
    }

    /**
     * Finds Elements From 'Default Mapping' Section View By Path.
     * @param string $path
     * @param array $matches
     * @throws IAException
     */
    private function getDefaultMappingElementsByPath($path, &$matches)
    {
        $this->getElementsByPathAndSectionId($path, 'standard_mapping_section', $matches);
    }

    /**
     * Finds Elements From Section View By Path and Section Id.
     * @param string $path
     * @param string $sectionId
     * @param array  $matches
     *
     * @throws IAException
     */
    private function getElementsByPathAndSectionId($path, $sectionId, &$matches)
    {
        $params = $this->getSectionFromView($sectionId);
        $matches = [];
        self::findElements($params, array('path' => $path), null, $matches);
    }

    /**
     * @param string $intacctobj
     * @param string $partnerobj
     * @param string $doctype
     *
     * @return bool
     */
    public function resetStandardFieldMapping($intacctobj, $partnerobj, $doctype)
    {
        if (!is_object($this->entityMgr)) {
            $this->entityMgr = $this->GetManager($this->getEntity());
        }
        $entityMgr = $this->getEntityMgr();
        $ok = $entityMgr->resetStandardFieldMapping($intacctobj, $partnerobj, $doctype);

        return $ok;
    }
}