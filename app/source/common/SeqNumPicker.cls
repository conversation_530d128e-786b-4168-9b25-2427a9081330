<?
class SeqNumPicker extends NPicker
{
    public function __construct()
    {
        parent::__construct(
            array(
            'entity'            =>  'seqnum',
            'title'                =>    _('Document Numbering'),
            'fieldlabels'        =>    array(_('Sequence ID'), _('Print title'), _('Next number'), _('Last updated')),
            'fields'            =>    array ( 'TITLE', 'PRINTTITLE', 'NEXT', 'WHENUPDATED'),
            'helpfile'            =>    'Viewing_and_Managing_the_Document_Numbering_List',
            'disablesort'        =>  1,
            'nofilteronthesefields' => array('NEXT', 'WHENUPDATED', 'STATUS')
            )
        );
    }

    /**
     *
     */
    public function BuildTable()
    {
        /** @var SeqNumManager $seqMgr */
        $seqMgr = Globals::$g->gManagerFactory->getManager('seqnum');

        // Call base class Build Table
        parent::BuildTable();
        
        // Generate the sample display
        foreach($this->table as $key => $row) {
            $seq = $seqMgr->Get($row['TITLE']);
            $this->table[$key]['NEXT'] = $seq['SAMPLEDISPLAY'];
        }

    }

    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     *
     * @param bool $addYuiCss include the YUI css files
     */
    public function showScripts($addYuiCss = true)
    {
        $_refresh = Request::$r->_refresh;
        parent::showScripts($addYuiCss);
    ?>
    <? UIUtils::PrintLayerSetupForBrowser(); ?>
    <? UIUtils::PrintSetField($_refresh); ?>
    <?
    }
}

