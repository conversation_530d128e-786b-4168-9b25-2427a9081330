<?php

/**
 *    DasboardPermissionsManager.cls
 *
 * <AUTHOR> <janarthanan.r<PERSON><PERSON><PERSON>@sage.com>
 * @copyright 2023, Intacct Corporation, All Rights Reserved
 */
class DashboardPermissionsManager extends ObjectPermissionManager
{

    /**
     * @return string
     */
    function getRestrictedViewName() : string
    {
        return "V_USERDASHBOARD_RESTRICTED";
    }

    /**
     * @return bool
     */
    public static function isMigrated() : bool
    {
        $cny = GetMyCompany();
        $hasDashboards = !empty(QueryResult(["SELECT 1 FROM USERDASHBOARD WHERE CNY# = :1 AND ROWNUM = 1", $cny]));
        if ( $hasDashboards ) {
            $qry = "SELECT 1 FROM DASHBOARDPERMISSIONS WHERE CNY# = :1 AND ROWNUM = 1";

            return ! empty(QueryResult([ $qry, $cny ]));
        }

        return true; // if no dashboards, then nothing to migrate

    }

}