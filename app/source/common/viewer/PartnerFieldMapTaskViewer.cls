<?php
/**
 * Class PartnerFieldMapTaskViewer
 * Implements specifics for Task salesforce object
 *
 * <AUTHOR> lokesh <<EMAIL>>
 * @copyright 2000-2022 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class PartnerFieldMapTaskViewer extends PartnerFieldMapViewer
{

    /**
     * For the task object make the sync rule editable by default
     *
     * @return bool
     */
    public function isCustomMappingSyncRuleEditable()
    {
        return true;
    }

}