<?php
//  M-Console properties

$tokens = [
    ['id' => 'IA.INTACCT_MANAGEMENT_CONSOLE_HOME'],
    ['id' => 'IA.COMPANY_INFORMATION'],
    ['id' => 'IA.ANNUAL_USER_CONFERENCE'],
    ['id' => 'IA.PRINT_NETGAIN_INVITATION'],
    ['id' => 'IA.MANAGEMENT_CONSOLE_MESSAGE_BOARD'],
    ['id' => 'IA.MANAGE_INSTANCES'],
    ['id' => 'IA.SELECT_AN_INSTANCE'],
    ['id' => 'IA.CONSOLE_SETUP'],
    ['id' => 'IA.MY_INSTANCES'],
    ['id' => 'IA.SERVICE_AGREEMENT'],
    ['id' => 'IA.INSTANCE_INFORMATION'],
    ['id' => 'IA.I_ACCEPT'],
    ['id' => 'IA.MANAGEMENT_CONSOLE'],
    ['id' => 'IA.CONSOLE_MEMBER'],
    ['id' => 'IA.INSTANCE'],
    ['id' => 'IA.MANAGEMENT_CONSOLE'],
    ['id' => 'IA.INTACCT_MANAGEMENT_CONSOLE_SUBSCRIPTION'],
    ['id' => 'IA.MANAGEMENT_CONSOLE_INFORMATION'],
    ['id' => 'IA.CLIENT_WELCOME_TEXT'],
    ['id' => 'IA.CONSOLE_SUBSCRIBE_MSG'],
];
// TODO: I18N - this will have to be refactored not to call the text service
$textMap = I18N::getTokensForArray($tokens);

$appmeta = array(
    'title' => GT($textMap, 'IA.INTACCT_MANAGEMENT_CONSOLE_HOME'),
    'name' => 'MPRAC',
    'switch' => 'MPRAC',
    'CompanyInfo' => array(
        'msg' => GT($textMap, 'IA.COMPANY_INFORMATION'),
    ),

    //Used by new console - pulled in by MarketingBean
    'MarketingMessage' => array(
        'enabled' => 0,
        'title' => GT($textMap, 'IA.ANNUAL_USER_CONFERENCE'),
        'content' => GT($textMap, 'IA.PRINT_NETGAIN_INVITATION'),
        'prehandler' => '_NetGainURLMunger',
        'handler' => '_NetGainMessage',
    ),

    'messageboardtitle' => GT($textMap, 'IA.MANAGEMENT_CONSOLE_MESSAGE_BOARD'),
    'ClientLister' => array(
        'title' => GT($textMap, 'IA.MANAGE_INSTANCES'),
        'mytitle' => GT($textMap, 'IA.SELECT_AN_INSTANCE'),
        'slidetype' => 'client',
        'clienttype' => GT($textMap, 'IA.INSTANCE'),
        'xmod' => GT($textMap, 'IA.CONSOLE_SETUP'),
        'myxmod' => GT($textMap, 'IA.MY_INSTANCES'),
        'helpid' => 'Manage_Clients_Lister',
        'myhelpid' => 'Entities_Lister',
        'showhiddenusers' => 1,
    ),
    'ClientEditor' => array(
        'title' => GT($textMap, 'IA.INSTANCE_INFORMATION'),
        'helpid' => 'Edit_Client',
    ),
    'ClientWizard' => array(
        'wizardheader' => 'clientcomp_wizard',        // Buttons, etc. Leave the same for now
        'defaultcontactname' => GT($textMap, 'IA.CONSOLE_MEMBER'),
        'practicedesc' => GT($textMap, 'IA.MANAGEMENT_CONSOLE'),
        'adminuser' => 'CPAUser',
        'admindesc' => GT($textMap, 'IA.CONSOLE_MEMBER'),
        'entitycontact' => 'Console Member',
        'billmeagreement' => 'service_agreement',
        'billclientagreement' => 'service_agreement',
        'label'    => GT($textMap, 'IA.MANAGEMENT_CONSOLE'),
    ),
    'PracticeWizard' => array(
        'wizardheader' => 'setup_wizard_cpa',
        'wizardheaderacct' => 'setup_wizard_acct',
        'agreement' => 'service_agreement.html',
        'pricelisturl' => 'http://www.intacct.com/popups/sales_quote.html',
        'checklist' => array(
            'prefnames' => array('NEEDSAGREEMENT','USESAGREEMENT','SESSIONTIMEOUT','LOGINTIMEOUT','MAXSESSIONTIMEOUT','MAXLOGINTIMEOUT'),
            'prefvals' => array('service_agreement','service_agreement',3600,21600,3,12),
        ),
    ),
    'ClientWelcome' => array(
        '1'    => array(
            'action'    => 'agreement',
            'activity'    => GT($textMap, 'IA.SERVICE_AGREEMENT'),
            'prefname'    => 'NEEDSAGREEMENT',
            'saveprefname' => 'USESAGREEMENT',
            'defaultval' => 'service_agreement',
            'text'        => GT($textMap, 'IA.CLIENT_WELCOME_TEXT'),
            'button'    => GT($textMap, 'IA.I_ACCEPT'),
            'useprefvalues' => 1,
        ),
    ),
    'CSTool' => array(
        'enable' => array(
            'msg' => 'M-Practice',
            'swmodule' => array('18.MPRAC'),
            'installmodule' => array('18.MPRAC'),
            'prefname' => 'MPRAC',
            'enablename' => 'Enable M-Practice',    // copies from backend_cs.inc
            'newname' => 'New M-Practice',
            'label' => 'Management Console',
        ),
//        'link' => array(
//            'user' => 'CPAUser',
//            'desc' => 'M-Console Member',
//        ),
        'billing' => array(
            'practicetype' => 'M-Practice',
            'entitytype' => 'Entity',
            'agreement' => 'service_agreement',
            'label' => 'Management Console',
        ),
    ),
    'subscribe' => array(
        'title' => GT($textMap, 'IA.INTACCT_MANAGEMENT_CONSOLE_SUBSCRIPTION'),
        'infotitle' => GT($textMap, 'IA.MANAGEMENT_CONSOLE_INFORMATION'),
        'msg' => GT($textMap, 'IA.CONSOLE_SUBSCRIBE_MSG'),
    ),
);
