<?php
/**
 * User permissions aggregate class
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

require_once 'html_header.inc';
require_once 'util.inc';
require_once 'show_listing.inc';
require_once 'Dictionary.cls';

global $gAllApps;
$_sess    = Request::$r->_sess;
$_done    = &Request::$r->_done;
$_p       = Request::$r->_p;
$_r       = &Request::$r->_r;
$_report  = Request::$r->_report;
$_cancel  = Request::$r->_cancel;
$_export  = Request::$r->_export;
$body     = &Request::$r->body;

$gAllApps = 1;

if ($_export == 1) {
    // initialize, here begins the step process with mgr
    Init();

    include_once "RolesTreeInfo.cls";
    $obj = new RolesTreeInfo();

    $obj->writeUserPermCSV();
    exit(0);
}

if (!isset($_p) || $_p == '') {
    ?>
    <html>
    <head>
        <script>
            function showWithDelay() {
                // To let the animation work
                setTimeout('show()', 400);
            }
            function show() {
                URL = '<?php echo ScriptRequestUrlEncoded(); ?>' + '&.p=1';
                document.forms[0].action = URL;
                document.forms[0].method = "POST";
                document.forms[0].submit();
            }
        </script>
        <link href='../resources/css/intacct_styles.css' rel="stylesheet" type="text/css">
    </head>
    <?php
    // Call Init() so we can determine the icon path
    Init();
    $body = "<body onload='showWithDelay();'>";
    $ref = '<img src="' . IALayoutManager::getCSSIconPath('newclock_slow.gif') . '"/>';
    ?>
    <?php echo $body; ?>
    <form width=100%>
        <table width="100%" height="100%">
            <tr>
                <td width="100%" height="100%" style='text-align: center' align="center" valign="center">
                    <?php echo $ref; ?>
                </td>
            </tr>
        </table>
    </form>

    </body>
    </html>
    <?php
} else {

    // if the user is cancelling out of the page lets go out of here
    if (isset($_cancel)) {
        $_done = ExtendUrlEx($_done, '.sess=' . $_sess);
        Ret();
    }

    // initialize, here begins the step process with mgr
    Init();

    global $gErr;

    // Reformulate the $_userid so that it no longer contains an $app.
    list($user_rec, $cny, $app) = explode('@', $_userid);
    $dict = Dictionary::getInstance();
    $_userid = $user_rec . '@' . $cny;
    if (!isset($_r) || $_r == '') {
        $_r = $user_rec;
    }

    if (empty(Request::$r->_uservid)) {
        // get uservid by userid if not set
        Request::$r->_uservid = GetMyLogin();
    }

    // Limited or Non Admin users can not view permissions for full admin users

    GetUserRecFromLogin(Request::$r->_uservid, $userRecord);

    if ( ( GetMyAdminLevel() <= 1 )
         && ( ( GetUserAdminLevel($userRecord) == 2 ) || ( GetUserAdminLevel(Request::$r->_userkey) == 2 )
              || ( GetUserAdminLevel(Request::$r->_u_o_gkey) == 2 ) )
    ) {
        Fwd("noauthorization.phtml", basename($_SERVER['HTTP_REFERER']));
    }

    // if doing report
    if ($_report == 1) {
        include_once "RolesTreeInfo.cls";
        $obj = new RolesTreeInfo();
        $ok = $obj->ShowInfo();
        Shutdown();
    }
    
    Shutdown();

    htmlFooter($params['title']);
}

