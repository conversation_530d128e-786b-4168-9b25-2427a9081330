<?php
/**
 * EmailNotificationManager.cls
 *
 * <AUTHOR> <raja.m<PERSON><PERSON><PERSON><PERSON><PERSON>@sage.com>
 * @copyright 2021 Intacct Corporation, All Rights Reserved
 */

/**
 * Manager for Email Notification
 */
class EmailNotificationManager extends EntityManager
{

    /**
     * @var int $maxFallBackCount
     */
    private $maxFallBackCount;
    /**
     * @param array $_params parameters
     */
    public function __construct($_params = [])
    {
        parent::__construct($_params);
        $this->maxFallBackCount =  date('z',strtotime(date('y').'-12-31'))+1;
    }

    /**
     * @param array $recordNos
     *
     * @return bool
     */
    function deleteEmailNotification($recordNos)
    {
        $source = "EmailNotification::deleteEmailNotificaton";
        $stmt = [];

        $stmt[0] = "DELETE emailnotification WHERE cny# = :1 ";
        $stmt[1] = GetMyCompany();
        $stmt = PrepINClauseStmt($stmt, $recordNos, " AND record# ");

        $ok = $this->_QM->beginTrx($source);
        if ($ok) {
            $ok = ExecStmt($stmt);
            $ok = $ok && $this->_QM->commitTrx($source);
            if ( ! $ok) {
                $this->_QM->rollbackTrx($source);
            }
        }

        return $ok;
    }

    /**
     * @return array
     */
    public function getEmailNotificationRecordNos()
    {
        $qry[1] = GetMyCompany();
        $qry[0] = "select emailnotificationkey from glapproveremailconfig ge where ge.cny# = :1";

        if (IsMultiEntityCompany() && GetContextLocation()) {
            $qry[0] .= " and ge.locationkey = :2";
            $qry[2] = GetContextLocation();
        } else {
            $qry[0] .= " and ge.locationkey is null";
        }
        
        $result = QueryResult($qry);
        $recordNos = [];
        if(!empty($result)) {
            foreach ($result as $row) {
                $recordNos[] = $row['EMAILNOTIFICATIONKEY'];
            }
        }
        return $recordNos;
    }

    /**
     * @param string $frequency
     *
     * @return string
     */
    public function calculateNextRunDate($frequency)
    {
        GetCompanyPreferences($currentCnyPrefs, GetMyCompany());
        $strBusinessDays = $currentCnyPrefs['BUSINESSDAYS'];
        $businessdays = explode(',', $strBusinessDays);
        $lastWorkingDay = $this->getLastWorkingDayOfAWeek(
            $businessdays
        );

        $today = gmdate('m/d/Y');

        $holidaysMgr = Globals::$g->gManagerFactory->getManager('holidays');

        switch ($frequency) {
        case 'D':
            $counter = 1;
            $nextRunDate = $today;
            do {
                $nextRunDate = $this->AddDate($nextRunDate, 1);
                $counter++;
            } while (($holidaysMgr->isHoliday($nextRunDate)
                || ! $this->isWorkingDay($nextRunDate))
            && $counter <= $this->maxFallBackCount);

            break;
        case 'W':
            $lastWorkingDayOfAWeek = "next {$lastWorkingDay} " . gmdate(
                    'm/d/Y'
                );
            $nextWeekRunDate = gmdate(
                'm/d/Y', strtotime($lastWorkingDayOfAWeek)
            );

            if ($holidaysMgr->isHoliday($nextWeekRunDate)) {
                $counter = 1;
                $lessThanCheckCounter = 0;
                //Default variable initialization
                $lastWeekLastWorkingDate = null;
                do {
                    $nextWeekRunDate = $this->MinusDate($nextWeekRunDate, 1);
                    if ($nextWeekRunDate <= gmdate('m/d/Y')) {
                        if ($lessThanCheckCounter == 0) {
                            /** @noinspection PhpUndefinedVariableInspection */
                            $lastWeekLastWorkingDate = gmdate('m/d/Y');
                        }
                        //echo "Next Monday:". date('Y-m-d', strtotime('next monday', strtotime($givenDate)));
                        $lastWorkingDayOfAWeek
                            = "next {$lastWorkingDay} "
                            . $lastWeekLastWorkingDate;
                        $nextWeekRunDate = gmdate(
                            'm/d/Y', strtotime($lastWorkingDayOfAWeek)
                        );
                        $lastWeekLastWorkingDate = $nextWeekRunDate;
                        $lessThanCheckCounter++;
                    }
                    $counter++;
                } while (($holidaysMgr->isHoliday($nextWeekRunDate)
                    || ! $this->isWorkingDay($nextWeekRunDate))
                && $counter <= $this->maxFallBackCount);
            }
            $nextRunDate = $nextWeekRunDate;
            break;
        case 'M':
            $lastDayOfMonth = "last day of this month "
                . gmdate('m/d/Y');

            $nextMonthRunDate = gmdate(
                'm/d/Y', strtotime($lastDayOfMonth)
            );

            if ($nextMonthRunDate <= gmdate('m/d/Y')) {
                $lastWorkingDayOfAMonth
                    = "last day of next month " . gmdate('m/d/Y');
                $nextMonthRunDate = gmdate('m/d/Y', strtotime($lastWorkingDayOfAMonth));
            }

            if ($holidaysMgr->isHoliday($nextMonthRunDate)
                || ! $this->isWorkingDay($nextMonthRunDate)) {
                $counter = 1;
                $lessThanCheckCounter = 0;
                //Default variable initialization
                $lastMonthLastWorkingDate = null;
                do {
                    $nextMonthRunDate = $this->MinusDate($nextMonthRunDate, 1);
                    if ($lessThanCheckCounter == 0) {
                        /** @noinspection PhpUndefinedVariableInspection */
                        $lastMonthLastWorkingDate = gmdate('m/d/Y');
                    }
                    if ($nextMonthRunDate <= gmdate('m/d/Y')) {
                        $lastWorkingDayOfAMonth
                            = "last day of next month " . $lastMonthLastWorkingDate;
                        $nextMonthRunDate = date(
                            'm/d/Y', strtotime($lastWorkingDayOfAMonth)
                        );
                        $lastMonthLastWorkingDate = $nextMonthRunDate;
                        $lessThanCheckCounter++;
                    }
                    $counter++;
                } while (($holidaysMgr->isHoliday($nextMonthRunDate)
                    || ! $this->isWorkingDay($nextMonthRunDate))
                && $counter <= $this->maxFallBackCount);
            }
            $nextRunDate = $nextMonthRunDate;
            break;
        default:
            $nextRunDate = $this->AddDate($today, 1);
        }

        $arrNextRunDate = explode('/', $nextRunDate);
        $nextRunDate = strtotime(
            gmdate(
                "m/d/Y H:i:s", mktime(
                    0, 0, 0, $arrNextRunDate[0],
                    $arrNextRunDate[1],
                    $arrNextRunDate[2]
                )
            )
        );

        return $nextRunDate;
    }

    /**
     * @param int $cny
     * @param int $recordNo
     * @param string $frequency
     *
     * @return bool
     */
    public function updateNextRunDate($cny, $recordNo, $frequency)
    {
        $ok = false;
        $nextRunDate = $this->calculateNextRunDate($frequency);
        if ($nextRunDate) {
            $setQry
                = "UPDATE emailnotification SET nextrundate = :1 WHERE cny# = :2 AND record# = :3 ";
            $ok = ExecStmt(
                [$setQry, $nextRunDate, $cny, $recordNo], 1,
                $this->getConnectionHandle()
            );
        }

        return $ok;
    }

    /**
     * @param array $businessdays
     *
     * @return mixed
     */
    public function getLastWorkingDayOfAWeek($businessdays)
    {
        $account_company = QueryResult(
            [
                "SELECT
						WEEKSTART
					FROM
						acctcompany
					WHERE
						cny#=:1",
                GetMyCompany(),
            ]
        );

        if (isset($account_company[0]['WEEKSTART'])) {
            $weekStart = $account_company[0]['WEEKSTART'];
            //If 'First day of the year is selected
            if ($weekStart == 9) {
                $weekStart = array_search(
                    date('l', strtotime(date('Y-01-01'))),
                    Globals::$g->kWeekDays
                );
            }
        } else {
            $weekStart = GetMyWeekStart();
        }

        $counter = 1;
        $workdays = [];
        for ($i = $weekStart; $i <= 7; $i++) {
            if ($counter <= 7) {
                if (in_array(
                        strtoupper(Globals::$g->kWeekDays[$i]), $businessdays
                    )
                    && ! in_array(
                        Globals::$g->kWeekDays[$i], $workdays
                    )
                ) {
                    $workdays[] = Globals::$g->kWeekDays[$i];
                }
                $counter++;
                if ($i == 7) {
                    $i = 0;
                }
            }
        }

        return end($workdays);
    }

    /**
     * @param string $asOfDate
     * @param int $noOfDays
     *
     * @return string
     */
    public function AddDate($asOfDate, $noOfDays)
    {
        $result = date(
            "m/d/Y",
            strtotime($asOfDate . " + {$noOfDays} day")
        );

        return $result;
    }

    /**
     * @param string $asOfDate
     * @param int $noOfDays
     *
     * @return string
     */
    public function MinusDate($asOfDate, $noOfDays)
    {
        $result = date(
            "m/d/Y",
            strtotime($asOfDate . " - {$noOfDays} day")
        );

        return $result;
    }

    /**
     * Check the given date falls under working days as per company schedule
     * @param string $date
     *
     * @return bool
     */
    public function isWorkingDay($date)
    {
        $ok = false;
        GetCompanyPreferences($currentCnyPrefs, GetMyCompany());
        $strBusinessDays = $currentCnyPrefs['BUSINESSDAYS'];
        $businessdays = array_filter(explode(',', $strBusinessDays));

        if (!$businessdays || in_array(
            strtoupper(date('l', strtotime($date))), $businessdays
        )
        ) {
            $ok = true;
        }

        return $ok;
    }
}
