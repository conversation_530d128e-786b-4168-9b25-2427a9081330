<?

/**
*    FILE:            InventoryFilterEditor.cls
*    AUTHOR:            rpn
*    DESCRIPTION:    A class for showing the Inventory Filter Editor (Filtering) screens for reports(may be custom screens also)
*
*    (C)2000, Intacct Corporation, All Rights Reserved
*
*    Intacct Corporation Proprietary Information.
*    This document contains trade secret data that belongs to Intacct
*    corporation and is protected by the copyright laws. Information herein
*    may not be used, copied or disclosed in whole or part without prior
*    written consent from Intacct Corporation.
*/


import('Request');
import('Editor');

require_once 'std_reports.inc';
require_once 'html_header.inc';


define('kShowFrames', '0');
define('kShowCtrl', '1');
define('kShowHTML', '2');
define('kShowPDF', '3');
define('kShowCSV', '4');
define('kShowText', '5');

/**
 * Class InventoryFilterEditor
 */
class InventoryFilterEditor extends Editor
{
    /**
     * @var string[] $fieldlist
     */
    private $fieldlist;

    /**
     *
     */
    function Show()
    {
        $this->ShowTop();
    }

    /**
     *
     */
    function ShowTop()
    {
        $_sess = Session::getKey();
        $fieldinfo = $this->_params['_fieldinfo'];
        $this->fieldlist = is_array($fieldinfo['lines']) ? $fieldinfo['lines'] : array($fieldinfo);
        $list = '"&.type="+aType+"&.sess='.$_sess.'"';
        $allfields = array();
        foreach($this->fieldlist as $fields){
            foreach ($fields as $field) {
                if (!isset($field['varpath'])) { 
                    $field['varpath'] = $field['path'];
                }
                if (!isset($field['varname'])) { 
                    $field['varname'] = Request::PathToFieldName($field['varpath']);
                }
                $list .='+"&'.$field['varname'].'="+escape(elements["'.$field['varname'].'"].value)';
                $allfields[] = $field;
                //eppp($field);
            }
        }
        $this->_params['allfields'] = $allfields;
    ?><HTML>
     <HEAD>
    <? $this->showScripts(); ?>
		<script language=Javascript>
			function Do(aType) {
				if (aType=='filter') {
					document.getElementById('filterLink').style.display="none";
					parent.document.getElementById('reportFilter').rows = '*,0';
					try{
						parent.iachild_frame__2.document.write("");
						parent.iachild_frame__2.document.close();
					} catch(e) {};
					return;
				}
				with (document.main) {
					elements['.type'].value = aType;
					args = <?=$list?>;
					parent.RefreshReport(args);
				}
			}
		</script>
    <? $this->ShowMetas();
    ?>
    <TITLE>
        <?  echo 'Intacct - ' .  $this->_params['title']; ?>
	</TITLE>
  </HEAD>
  <BODY marginheight="0" topmargin="0" leftmargin="0" marginwidth="0" text="#000000" link="#006699" alink="#990000" vlink="#000000"
    <? if ($this->_params['focusedfield']) { ?> onload="document.main.elements['<?
    echo Request::$r->PathToFieldName($this->_params['focusedfield']); ?>'].focus()" <?
} ?> >
    <? $this->ShowLayout(); ?>
     </BODY>
   </HTML>
    <?
    }


    /**
     *
     */
    function ShowLayout()
    {
    ?>
     <FORM name="main" action="<?=$this->_params['action'];?>" method="post">
        <? //$this->ShowTopBar(); ?>
        <? //if ($this->GetMessage()) { global $gUIUtils; $gUIUtils->ShowMessage($this->GetMessage()); } ?>
        <? $this->ShowHiddenFields(); ?>
        <? $this->ShowFields($this->fieldlist); ?>
     </FORM>
    <?
    }


    /**
     *
     */
    function ShowHiddenFields()
    {
        /*
        <INPUT type="hidden" name='.sess' value="<? echo Session::getKey(); ?>">
        <INPUT type="hidden" name='_done' value="<? echo  Request::$r->_done; ?>">
        <INPUT type="hidden" name='_state' value="<? echo Request::$r->_state; ?>">
        <INPUT type="hidden" name='_popup' value="<? echo $this->_params['popup']; ?>">
        <INPUT type="hidden" name="_changed" value="<? echo Request::$r->_changed; ?>">
        <INPUT type="hidden" name=".type">
        <INPUT type="hidden" name="_currentfocus" value="<? echo Request::$r->_currentfocus; ?>">
        <INPUT type="hidden" name="_currentform" value="<? echo Request::$r->_currentform; ?>">
        <INPUT type="hidden" name="_currentlayer" value="<? echo Request::$r->_currentlayer; ?>">
        <INPUT type="hidden" name="_currentmline_name" value="<? echo Request::$r->_currentmline_name; ?>">
        <INPUT type="hidden" name="_currentmline_rowsnum" value="">
        <INPUT type="hidden" name='<? echo Globals::$g->kNoun; ?>' value="<? echo Request::$r->_it; ?>">
        <INPUT type="hidden" name='<? echo Globals::$g->kVerb; ?>' value="<? echo Request::$r->_do; ?>">
        <INPUT type="hidden" name='<? echo Globals::$g->kMod; ?>' value="<? echo Request::$r->_mod; ?>">
        <INPUT type="hidden" name='<? echo $this->kAction; ?>' value="<? echo $action; ?>">
        */


        $this->ShowGeneralHiddenFields();
    ?>
     <INPUT type="hidden" name=".type">
    <?
    }

    /**
     * @param array $fieldlist
     */
    function ShowFields( $fieldlist )
    {
        ?>
     <CENTER>
     <TABLE border="0" cellpadding="2" cellspacing="0"><TR>
        <?    foreach( $fieldlist as $fields ){
                ?><TD><?
                $this->ShowVerticalFieldLayout($fields);
                ?></TD><?
}
    ?>
        <? $this->ShowControls(); ?>
     </TR>
     </TABLE>
     </CENTER>
        <?
    }

    /**
     *
     */
    function ShowControls()
    {
        ?>
      <td valign="bottom" align="center">
       <a href# onclick="Do('_html');"
       onmouseover='window.status="<? echo isl_htmlspecialchars(statusdisp('HTML Format'));?>";return true;'
				onmousemove='window.status="<? echo statusdisp('HTML Format'); ?>"; return true;'
				onfocus='window.status="<? echo statusdisp('HTML Format'); ?>"; return true;'
				onblur ="window.status=''; return true;" onmouseout ="window.status='';">
				<img src="<? echo IALayoutManager::getCSSButtonPath("view.gif"); ?>" border=0 alt="View"></a>
      </td>
        <?
    }

    /**
     * @param array $_fields
     */
    function ShowVerticalFieldLayout(&$_fields )
    {
    ?>
     <TABLE border="0" cellpadding="2" cellspacing="0">
        <?
        foreach ($_fields as $field) {
            $this->ShowFieldsRow($field);
            //if ($this->FieldNeedsStar($field)) { $tableHasStar = true; }
        }
        ?>
        <? /** @noinspection PhpUndefinedVariableInspection */
        if ($tableHasStar) { ?>
		  <TR>
			<TD>
			  <FONT color="#FF0000" face="Verdana, Arial, Helvetica" size="0">* required</FONT>
			</TD>
		  </TR>
    <?
} ?>
      </TABLE>
        <?
    }

    /**
     * @param array $_field
     * @param string $layout used only in some descendants of Editor class
     * @param bool   $first  used only in some descendants of Editor class
     */
    function ShowFieldsRow(&$_field, $layout = "", $first = false)
    {
        ?>
        <TR>
            <TD align="right" valign="middle" nowrap>
                <? $this->ShowSimpleFieldLabel($_field); ?>
            </TD>
            <TD NOWRAP valign="middle">
                <? $this->ShowSimpleFieldValue($_field); ?>
            </TD>
        </TR>
        <?
    }

    /**
     * @param array $_field
     * @param bool  $_inlineitem
     * @param bool  $encodeLabel
     */
    function ShowSimpleFieldLabel(&$_field, $_inlineitem = false, $encodeLabel = true)
    {
        $size = $_inlineitem ? "1" : "2";
        // enum fields are indirectly enforced by the UI
        if ($this->FieldNeedsStar($_field)) {
            $color = ' color="#FF0000" ';
            $star = '*';
        }
        /** @noinspection PhpUndefinedVariableInspection */
        $label = $star . isl_ucfirst($_field['fullname']);
        ?>
     <FONT <? /** @noinspection PhpUndefinedVariableInspection */
     echo $color; ?> face="verdana, arial" size="<? echo $size; ?>">
        <? echo $_inlineitem ?  "<B>" : ""; ?>
        <? echo $label; ?>
        <? echo $_inlineitem ?  "</B>" : ""; ?>
     </FONT>
        <?
    }

    /**
     * @param array $_field
     */
    function ShowSimpleFieldValue(&$_field)
    {
        if (!isset($_field['varpath'])) { 
            $_field['varpath'] = $_field['path'];
        }
        if (!isset($_field['varname'])) { 
            $_field['varname'] = Request::PathToFieldName($_field['varpath']);
        }
        $ctrl =  GetUIControl($_field);
        $_field['readonly'] ?  $ctrl->ShowReadOnly() : $ctrl->Show();
    }

    /**
     * @param string $_report
     *
     * @return InventoryFilterEditor
     */
    public static function GetReportEditor($_report)
    {
        $params = array();
        // Build the schema objects
        $reportSchemas = GetParams($_report);
        $schema = $reportSchemas[$_report]['schema'];
        if (!$schema) {
            dieFL("kSchemas[" . $_report . "] does not exist.\n");
        }
        $params['_entity'] = $_report;
        $params['_schemas'] = $schema;
        $params['_fieldinfo'] = $reportSchemas[$_report]['fieldinfo'];
        $params['action'] = $params['action'] ?? 'filters.phtml';
        $params['_mod'] = $reportSchemas[$_report]['_mod'];
        $params['helpfile'] = $reportSchemas[$_report]['helpfile'];
        return new self($params);
    }






    /** @var string $_entity */
    var $_entity;


}


/**
 * @param string $_report
 *
 * @return mixed
 */
function GetParams($_report)
{
    /** @noinspection PhpUndefinedVariableInspection */
    (include $_report . '.rpt')
    or (include$_report = isl_strtolower($_report).'.rpt')
    or dieFL("Couldn't find a .rpt file for report: $report\n");

    /** @noinspection PhpUndefinedVariableInspection */
    return $kSchemas;
}


/**
 * creates a instance of the FrameControl and spits the HTML code
 *
 * @param array $params
 */
function ShowFrames($params)
{
    $_op = Request::$r->_op;
    $_sess = Session::getKey();
    ?>
	<html>
	<title><?= /** @noinspection PhpUndefinedVariableInspection */
        $title?></title>
	</html>
	<script>
	function RefreshReport(args) {
		self.iachild_frame__1.document.getElementById('filterLink').style.display = "block";
		document.getElementById('reportFilter').rows = '38,*';
		//self.iachild_frame__1.document.body.scroll = 'no';
		self.iachild_frame__2.location.href = "reportor.phtml?.sess=<?=$_sess;?>&.op=<?=$_op?>"+args;
	}
	</script>
    <?
    import('FrameControl');
    $ctrl = new FrameControl($params);
    $ctrl->Show();
}


/**
 * creates a instance of the CustomWizard and spits the HTML code
 */
function ShowWizard()
{
    $_op = Request::$r->_op;
    $_sess = Session::getKey();
    ?>
	<html>
	<title><?= /** @noinspection PhpUndefinedVariableInspection */
        $title?></title>
	</html>
	<script>
	function RefreshReport(args) {
		self.iachild_frame__1.document.getElementById('filterLink').style.display = "block";
		document.getElementById('reportFilter').rows = '38,*';
		//self.iachild_frame__1.document.body.scroll = 'no';
		self.iachild_frame__2.location.href = "reportor.phtml?.sess=<?=$_sess;?>&.op=<?=$_op?>"+args;
	}
	</script>
    <?
    epp(' Report Wizard ');
}


