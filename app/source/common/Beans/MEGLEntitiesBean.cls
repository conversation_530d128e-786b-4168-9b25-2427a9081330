<?php

import("MRUEntityManager");

/**
 * Class MEGLEntitiesBean
 */
class MEGLEntitiesBean extends Bean {
    /**
     * @var array
     */
    public array $translatedTokens = [];

    /**
     * @var string[]
     */
    public array $tokens = [
        'IA.ENTITY',
        'IA.SELECT_NO_DASHES',
        'IA.MOST_RECENT',
    ];

    /**
     * @param array $_params
     */
    function __construct($_params = null) {
        $_etype = Request::$r->_etype;
        $_pickent = Request::$r->_pickent;
        if ($_pickent) {
            $this->MRUInit($_etype, $_pickent);
        }
        parent::__construct($_params);

        $this->translatedTokens = I18N::getTokensForArray(I18N::tokenArrayToObjectArray($this->tokens));
    }

    function RenderBody() {
        if ($this->beanOnLoad) { ?>
            <script>
                <?= $this->beanOnLoad; ?>
            </script>
        <? } ?>

        <div style="position: relative">
            <table CELLPADDING="0" CELLSPACING="0" BORDER="0" WIDTH="100%">
                <tr>
                    <td>
                        <table CLASS="Box1" CELLPADDING="1" CELLSPACING="0" BORDER="0" WIDTH="100%">
                            <tr>
                                <td>
                                    <table CLASS="Box2" CELLPADDING="0" CELLSPACING="10" BORDER="0" WIDTH="100%">
                                        <tr>
                                            <td><? $this->renderMRUControl('E'); ?></td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
    <? }

    /**
     * @param string $etype
     */
    function renderMRUControl($etype) {
        $this->renderMRUPicker($etype);
        ?>
        <table width='100%'>
            <tr>
                <td align='center'>
                    <hr color='lightgrey'>
                </td>
            </tr>
        </table>
        <?
        $this->renderMRULister($etype);
    }

    /**
     * @param string $etype
     */
    function renderMRUPicker($etype )
    {
        global $cmboArr;
        $_pickent = Request::$r->_pickent;

        $label = $this->calcLabel();

        $ename     = 'LocationEntity';
        $mod     = 'co';
        $eop     = 'co/lists/locationentity';
        
        // Construct combo params
        $params = array();
        $params['path'] = ".pickent";
        $params['varname'] = ".pickent";
        $params['value'] = isl_trim($_pickent);
        $params['onchange'] = "SetSel(this.value, '$ename')";
        $params['noroot'] = true;
        if ($cmboArr[$ename]) {
            $params['skipquery'] = true;
        }

        if (QXCommon::isQuixote()) {
            $params['type']['forceCombo'] = true;
            $params['assists'] = PrintEntityLink(array(0), 'ff', '.pickent', $ename, $mod, $eop, $label, '', '', '&.refresh=1&.noroot=1', '', '', true,true);
            ?>
            <table>
                <tr>
                    <td>
                        <div class="form-group">
                            <label><?= $label; ?></label>
                            <?
                            $rendercmbo = PrintEntityCombo($ename, $params);
                            if ($rendercmbo) {
                                $cmboArr[$ename] = true;
                            } else { ?>
                                <input type='text' name='.pickent' size='20'>
                            <? } ?>
                        </div>
                    </td>
                    <td align="right">
                        <div class="form-group">
                            <label></label>
                            <input type='submit' class="btn btn-secondary" name='notused' value='<?= GT($this->translatedTokens, 'IA.SELECT_NO_DASHES'); ?>'>
                        </div>
                    </td>
                    <input type='hidden' name='.etype' value='<?= $etype ?>'>
                </tr>
            </table>
        <? } else { ?>
            <table width='100%'>
                <tr>
                    <td>
                        <table border="0" cellpadding="0" cellspacing="0">
                            <tr>
                                <td align="left" valign="top">
                                    <FONT FACE="Verdana, Arial, Helvetica" SIZE="1"><B><?= $label ?>&nbsp;</B></FONT>
                                </td>
                                <td align="left" valign="top">
                                    <? // Render combo
                                    $rendercmbo = PrintEntityCombo($ename, $params);
                                    if ($rendercmbo) {
                                        $cmboArr[$ename] = true;
                                    } else { ?>
                                        <input type='text' name='.pickent' size='20'>
                                    <? } ?>
                                </td>
                                <td align="left" valign="middle" class="tdAssistLinks">
                                    <?= PrintEntityLink(array(0), 'ff', '.pickent', $ename, $mod, $eop, $label, '', '', '&.refresh=1&.noroot=1', '', '', true) ?>
                                </td>
                            </tr>
                        </table>
                    </td>
                    <tr align='right'>
                        <input type='submit' class="nosavehistory" name='notused' value='<?= GT($this->translatedTokens, 'IA.SELECT_NO_DASHES'); ?>'>
                    </tr>
                    <input type='hidden' name='.etype' value='<?= $etype ?>'>
                </tr>
            </table>
        <? }
    }

    /**
     * @param string $etype
     */
    function renderMRULister($etype )
    {
    ?>
    <TABLE width='100%' border='0'>

    <TR><TD><FONT FACE="Verdana, Arial, Helvetica" SIZE="1"><B><?= GT($this->translatedTokens, 'IA.MOST_RECENT'); ?></B></FONT></TD></TR>
    <TR><TD height='3'></TD></TR>	
    <?

    $mru_em = new MRUEntityManager();
    $r = $mru_em->GetMRUList(GetMyUserid(), $etype, 'location', 'record#', '', true);
    $_prefs = [];
    GetCompanyPreferences($_prefs);
    foreach ( $r as $rec ) {
        
        if (isset($_prefs['ACCOUNTINGPRACTICE']) && $_prefs['ACCOUNTINGPRACTICE'] === TRADITIONAL && isset($rec['ACCOUNTINGTYPE']) && $rec['ACCOUNTINGTYPE'] === FUND) {
            continue;
        }
        
        $title = $rec['LOCATION_NO'];
        $name  = util_encode($rec['NAME']);

        $url = "<a href='#' onclick=\"SetSel('$title'); return false;\">". GT($this->translatedTokens, 'IA.SELECT_NO_DASHES') ."</A>";

        print "<TR>";
        print "<TD><FONT CLASS=BaseBlack>$title</FONT></TD>";
        print "<TD><FONT CLASS=BaseBlack>$name</FONT></TD>";

        print "<TD NOWRAP align='right'><FONT CLASS=BaseBlack>$url</FONT></TD>";

        print "</TR>";

    }
    ?>
      </TABLE>
    <?

    }

    /**
     * @return string
     */
    function calcLabel()
    {
        return GT($this->translatedTokens, 'IA.ENTITY');
    }
}
