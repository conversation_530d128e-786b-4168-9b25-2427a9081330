<?php

/**
 * Editor class for the Storage Options screen for Report Groups
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */


/**
 * Editor class for the Storage Options screen
 */
class StorageOptionsRptGrpEditor extends StorageOptionsEditor
{
    /**
     * @param array $_params Initial params
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);
    }

    /**
     * (non-PHPdoc)
     *
     * @see FormEditor::getMetadataKeyName()
     *
     * @param array $params
     *
     * @return string
     */
    protected function getMetadataKeyName(&$params)
    {
        return "storageoptions_form.pxml";
    }

    /**
     * (non-PHPdoc)
     *
     * @see FormEditor::massageRawMetadata()
     *
     * @param array $top
     */
    protected function massageRawMetadata(&$top)
    {
        $top['entity'] = 'storageoptionsrptgrp';
        self::findElements($top, [ 'entity' => 'storageoptions' ], null, $matches);

        if ( $matches && is_array($matches) ) {
            foreach ( $matches as &$match ) {
                $match['entity'] = 'storageoptionsrptgrp';
            }
        }
    }

    /**
     * This is a hook functions for subclases to add the dynamic metadata into the current layout.
     * At the time this function is called, the data, state and view objects are not available.
     * The subclass must operate on the given params structure.
     *
     * @param array &$params params
     */
    protected function buildDynamicMetadata(&$params)
    {
        $matches = array();
        self::findElements(
            $params, array('title' => 'IA.SCHEDULE'), EditorComponentFactory::TYPE_SECTION, $matches
        );

        if ($matches) {
            $matches[0]['title'] = 'IA.RUN_THIS_REPORT';
        }

        $this->setupRptGrpScheduleFields($matches[0]);
    }

    /**
     * Setup metadata fields for scheduling
     *
     * @param array $params Params that is to be updated to reflect the changes in UI
     */
    private function setupRptGrpScheduleFields(&$params)
    {
        $scheduleSection =
            array(
                '0' => array(
                    'field' => array(
                        '0' => array(
                            'events' => array(
                                'change' => 'if(!ValidateDocDate(this)){return false};'
                            ),
                            'path' => 'ASOFDATE'
                        ),
                        '1' => array(
                            'path' => 'LOCATION'
                        ),
                        '2' => array(
                            'path' => 'ENTITYCOLLATE'
                        ),
                        '3' => array(
                            'path' => 'DEPARTMENT'
                        ),
                        '4' => array(
                            'events' => array(
                                'change' => 'setReportingAccountFields(this);'

                            ),
                            'path' => 'REPORTINGACCOUNTS'
                        ),
                        '5' => array(
                            'events' => array(
                                'change' => 'setReportingAccountFields(this);'
                            ),
                            'path' => 'REPORTINGACCOUNTSET'
                        ),
                        '6' => array(
                            'path' => 'COVERLTRPERIOD'
                        ),
                        '7' => array(
                            'path' => 'FOOTNOTE'
                        )
                    )
                )
            );
        $params['child'] = $scheduleSection;
    }

    /**
     * (non-PHPdoc)
     *
     * @see FormEditor::getStandardButtons()
     *
     * @param string $state
     *
     * @return mixed
     */
    public function getStandardButtons($state)
    {
        $sess = Request::$r->_sess;
        $op = Request::$r->_op2;
        $memgrpnme = Request::$r->_memgrpnme;
        $memrec = Request::$r->_memrec;
        $schopkey = Request::$r->_schopkey;

        switch ($state) {

        case Editor_ShowNewState:

            $urlPostArgs = json_encode(array( CsrfUtils::DOT_CSRF_PARAM_NAME => CsrfUtils::generateToken($op) ));

            $this->setButtonDetails(
                $values, 'okbutton', 'okbutton', 'IA.OK', 'OKB', false,
                "setRedirectRepGrp('".addslashes($memgrpnme)."', '$memrec', '$schopkey', '$sess', '$op', $urlPostArgs)",
                false, true
            );

            $this->setButtonDetails(
                $values, Editor_CancelBtnID, 'cancelbutton', 'IA.CANCEL', 'cancel', false,
                "closeWindow();",
                false, true
            );

            break;

        }

        $this->addHelpButton($values);
        return $values;
    }

    /**
     * This is a hook functions for subclases to adjust the metadata according to the current data & state.
     * At the time this function is called:
     *   - the data is available and it is in view format.
     *   - the metadata is expanded and the view objects are built - use $this->getView() call
     * to get a refernece to the view object.
     *
     * WARNING: Because the metadata is expanded at the time of this call, the subclass has to be careful
     * when making changes to the metadata. For example, when adding/removing fields that belong to a grid,
     * the code needs to operate on the grid object.
     *
     * @param array &$obj the data
     *
     * @return bool true on success and false on failure make sure an error is raised in case of failure
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $view = $this->getView();
        $validlabels = array();
        $validvalues = array();
        $fnprompt = Request::$r->_fnprompt;
        $cl = Request::$r->_cl;
        $view->findAndSetProperty(
            array('path' => 'OFFLINEREPORTDESCRIPTION'),
            array('hidden' => true)
        );

        if ($fnprompt !== 'true') {
            $view->findAndSetProperty(
                array('path' => 'FOOTNOTE'),
                array('hidden' => true)
            );
        }

        if (!isset($cl) || $cl == '') {
            $view->findAndSetProperty(
                array('path' => 'COVERLTRPERIOD'),
                array('hidden' => true)
            );
        }

        $validlabels['_pdf'] = 'IA.PRINTABLE';
        $validlabels['_excel'] = 'IA.EXCEL';
        $validvalues['_pdf'] = '_pdf';
        $validvalues['_excel'] = '_excel';

        $fields = array();
        $view->findComponents(
            array('path' => 'DELIVERYREPORTTYPE'), EditorComponentFactory::TYPE_FIELD, $fields
        );
        if ($fields) {
            $dsvalidlabels = array_values($validlabels);
            $dsvalidvalues = array_values($validvalues);

            $fields[0]->setProperty(array('type', 'validlabels'), $dsvalidlabels);
            $fields[0]->setProperty(array('type', 'validvalues'), $dsvalidvalues);
            $fields[0]->setProperty('default', $dsvalidvalues[0]);
        }

        $fields = array();
        $view->findComponents(
            array('path' => 'OFFLINEREPORTTYPE'), EditorComponentFactory::TYPE_FIELD, $fields
        );
        if ($fields) {
            $validlabels = array_values($validlabels);
            $validvalues = array_values($validvalues);

            $fields[0]->setProperty(array('type', 'validlabels'), $validlabels);
            $fields[0]->setProperty(array('type', 'validvalues'), $validvalues);
            $fields[0]->setProperty('default', $validvalues[0]);
        }

        if (!IsReportingAccountEnabled()) {
            //Reporting accounts not enabled
            $view->findAndSetProperty(
                array('path' => 'REPORTINGACCOUNTS'),
                array('hidden' => true)
            );
            //This is specified in the _form.xml
            $view->findAndSetProperty(
                array('path' => 'REPORTINGACCOUNTS'),
                array('onchange' => '')
            );
            $view->findAndSetProperty(
                array('path' => 'REPORTINGACCOUNTSET'),
                array('hidden' => true)
            );
            //This is specified in the _form.xml
            $view->findAndSetProperty(
                array('path' => 'REPORTINGACCOUNTSET'),
                array('onchange' => '')
            );
        } else {
            //Reporting accounts enabled, then set the values
            if ($this->state == Editor_ShowNewState) {
                //For the first time, get the preference values and assign
                //This will be very first time, after SAVE is clicked, it will change forever
                $rptAcctUserPref = GetUserPreferences($userprefs, 'RPTACCTUSERPREF');
                $retAccounts = ($rptAcctUserPref == 'A' ? true : false);
                $retAccountset = GetUserPreferences($userprefs, 'DEFAULTRPTACCTSET');
                $obj['REPORTINGACCOUNTS'] = $retAccounts;
                $obj['REPORTINGACCOUNTSET'] = $retAccountset;
            }

            if ($obj['REPORTINGACCOUNTS'] != true) {
                $view->findAndSetProperty(
                    array('path' => 'REPORTINGACCOUNTSET'),
                    array('hidden' => true)
                );
            }
        }

        return true;
    }

    /**
     * @see FormEditor::transformBizObjectToView($obj)
     *
     * @param array $obj
     *
     * @return bool
     */
    protected function transformBizObjectToView(&$obj)
    {
        $obj['RUNDATE'] = GetCurrentDate();

        $memgrpnme = Request::$r->_memgrpnme;
        if (isset($memgrpnme) && $memgrpnme != '') {
            $fnprompt = Request::$r->_fnprompt;
            if ($fnprompt == 'true') {
                $footnote = Request::$r->_fn;
                $obj['FOOTNOTE'] = $footnote;
            }

            $cl = Request::$r->_cl;
            // PHP8_NUMERIC_STRING_COMPARE; Priority: low; Behavior: same, Risk: low, Solution: php7gt0
            if (Util::php7gt0($cl)) {
                $glBudgetMgr = Globals::$g->gManagerFactory->getManager('glbudgettype');
                $res = $glBudgetMgr->GetList(
                    array(
                        'selects' => array('NAME'),
                        'filters' => array(array(
                            array('RECORD#', '=', $cl)
                        ))
                    )
                );
                $obj['COVERLTRPERIOD'] = $res[0]['NAME'];
            }
        }

        return true;
    }
}