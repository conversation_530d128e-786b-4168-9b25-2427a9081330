<?  
//=============================================================================
//
//	FILE:			edit_account.phtml
//	AUTHOR:			
//	DESCRIPTION:	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================



require_once 'html_header.inc';
require_once 'util.inc';
Init();
$_op 	  = Request::$r->_op;
$_sess    = Request::$r->_sess;
$_r 	  = &Request::$r->_r;
$hlpfile  = &Request::$r->hlpfile;
$parsed   = '';
$_bodyarg = &Request::$r->_bodyarg;
$_save    = Request::$r->_save;

if( $_save ) {
    // Range check only valid for members that are accounts

    /** @noinspection PhpUndefinedVariableInspection */
    $_currentranges = explode('#~#', $_currentranges);
    if (empty($_currentranges) || count($_currentranges) == 0 || $_currentranges[0] == '0') {
        $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__, "The range cannot be empty");
    }    
    if (!HasErrors()) {
        $ranges = array();
        for ($i = 0; $i < count($_currentranges); $i++) {
            list($rangefrom,$rangeto) = explode(' -> ', $_currentranges[$i]);
             
                
            if ( isl_trim($rangefrom) != '' && isl_trim($rangeto) != '' ) {
                $ranges[] = $rangefrom . '-' .$rangeto;
            }else if($rangefrom !='' && $rangeto=='') {
                $ranges[] = $rangefrom .'-';
            }else if($rangefrom =='' && $rangeto!='') {
                $ranges[] = '-'.$rangeto; 
            }
        }     
        $parsed = join(',', $ranges);
    }
}
if( !empty($_r) ) {
    $currentranges = array();
    $_r = explode(',', $_r);
    for ($i = 0; $i < count($_r); $i++) {
        list($fromacc, $toacc) = explode('-', $_r[$i]);
        $currentranges["$fromacc -> $toacc" ] = "$fromacc -> $toacc";
    }
    if (count($_r) == 0) {
        $currentranges['0'] =  '-- No Members --';
    }
}

    $title = 'Intacct - Aging Information';
    $hlpfile = 'Account_Information';

?>
<? if($_bodyarg == 'T') { ?>
    <?    Head('#FFFFFF', '#000000', '#006699', '#990000', '#000000', '', '', '', 'true'); ?>
		<SCRIPT>
	    //**  the following function is used to return the QuickAdds values into the calling screen
	    //**  screen and submit the form with newly added data in it.
	    function doLoading(aform, afield, amode){
			 var pform = window.opener.document.forms[aform];
			 pform.elements['.sVal'].value = "<? echo $parsed; ?>";
	         pform.elements['.sTxt'].value = "<? echo $parsed; ?>";
	         window.opener.document.forms[aform].elements[afield].value = "<? echo $parsed; ?>";
			 if( amode == 'r' ){
				 pform.submit( pform.elements['.sVal'].value);
				 //pform.submit();
			 }
	         window.close('QuickAdds');
	    }
	    </SCRIPT>
	    <body onLoad="doLoading(<? /** @noinspection PhpUndefinedVariableInspection */
        echo "'$_targetForm', '$_targetField', '$_amode'"; ?>);"></body>
    <? 
}else{
    /** @noinspection PhpUndefinedVariableInspection */
    if ( $_doclose == 'yes' && $_bodyarg == 'F') {
    $_bodyarg = 'T';
}  ?>
<?    Head('#FFFFFF', '#000000', '#006699', '#990000', '#000000', '', 'mf'); ?>
<script type="text/javascript" language="javascript" src="../resources/js/base_lib.js"></script>
<form action="<? echo GoUrl("ageing.phtml?.id=" . GetOperationId('co/services/modules/configure')); ?>" method=post name="mf">
<script>
function memberup(fld) {
	sel = fld.selectedIndex;
	if (sel == -1) {
		 alert("Select a member, please.");
		 return false;
	}
	if (sel > 0) {
		oldText  = fld[sel].text;
		oldValue = fld[sel].value;
		fld[sel].text  = fld[sel - 1].text;
		fld[sel].value = fld[sel - 1].value;
		fld[sel - 1].text  = oldText;
		fld[sel - 1].value = oldValue;
		fld.selectedIndex--;
	}
	return true;
}

function memberdown(fld) {
	sel = fld.selectedIndex;
	if (sel == -1) {
		 alert("Select a member, please.");
		 return false;
	}
	if (sel < fld.length - 1) {
		oldText  = fld[sel].text;
		oldValue = fld[sel].value;
		fld[sel].text  = fld[sel + 1].text;
		fld[sel].value = fld[sel + 1].value;
		fld[sel + 1].text  = oldText;
		fld[sel + 1].value = oldValue;
		fld.selectedIndex++;
	}
	return true;
}


function memberdel(fld) {
	if (fld.selectedIndex == -1) {
		 alert("Select a member, please.");
		 return false;
	}
	if (fld.length && fld[0].text == '-- No Members --') { 
		return false; 
	}
	remove_list = new Array(fld.length);
	remove_cnt = 0;
	name_list = "";
	for (i=0;i<fld.length;i++) {
		if (fld[i].selected) {
			remove_list[remove_cnt++] = i;
			name_list += "\n\t" + fld[i].text;
		}
	}
	if (!confirm("Delete the following?"+ name_list)) { 
		return false; 
	}
	lastkeep = remove_list[0] - 1;
	remove_i = 0;
	for (i=remove_list[0];i<fld.length;i++) {
		if (i != remove_list[remove_i]){
			lastkeep++;
			fld[lastkeep].value = fld[i].value;
			fld[lastkeep].text = fld[i].text;
		} else {
			remove_i++;
		}
	}
	fld.length = lastkeep + 1;
	if (fld.length == 0) {
		fld.length++;
		fld[0].value = '';
		fld[0].text = '-- No Members --';
	}

	return true;
}


function memberadd(fromfld,tofld) {
	fromsel = fromfld.selectedIndex;

	if (fromsel == -1 || fromfld[fromsel].value == '') {
		 alert("Select a member, please.");
		 return false;
	}
	for (i=0;i<tofld.length;i++) {
		if (tofld[i].value == fromfld[fromsel].value) {
			alert(tofld[i].text + " is already a member");
			return false;
		}
	}

	if (tofld.length && tofld[0].text == '-- No Members --') {
		tofld.length = 0;
	}

	tosel = tofld.selectedIndex;
	if (tosel == -1) {
		tosel = tofld.length;
	}
	len = tofld.length;
	tofld.length++;
	for (i=len; i>tosel; i--) {
		tofld[i].value = tofld[i-1].value;
		tofld[i].text = tofld[i-1].text;
	}
	tofld[tosel].value = fromfld[fromsel].value;
	tofld[tosel].text = fromfld[fromsel].text;
	if (tofld.selectedIndex != -1) {
		tofld.selectedIndex=tosel+1;
	}
	return true;
}

function rangeadd(field1, field2, tofld) {
	frm = document.forms['mf'];
	if (field1.value =='' && field2.value==''){
		alert('Select the Aging ranges');
		return false;
	}
	
	if (isNaN(field1.value) || isNaN(field2.value)) {
		alert('Aging range is invalid');
		return false;
	}
	if (field1.value < 0 || field2.value < 0) {
		alert('Aging range should be positive');
		return false;
	}
	fld1 = field1.value 
	fld2 = field2.value;

	var fromPos = String(fld1).indexOf('-');
	var fromacct = String(fld1);
	if (fromPos > 0)
		fromacct = fromacct.substr(0,fromPos-1);

	var toPos = String(fld2).indexOf('-');
	var toacct = String(fld2);
	if (toPos > 0)
		toacct = toacct.substr(0,toPos-1);

//	if (toacct < fromacct) {
//		alert('The Account range To \"' + fld2 + '\' is smaller than From \'' + fld1 + '\'');
//		return false;
//	}

	var acctName = fromacct + ' -> ' + toacct;
	for (i=0;i<tofld.length;i++) {
		if (tofld[i].value == acctName) {
			alert(tofld[i].text + " is already a member");
			return false;
		}
	}

	if (tofld.length && tofld[0].text == '-- No Members --') {
		tofld.length = 0;
	}

	tosel = tofld.selectedIndex;
	if (tosel == -1) {
		tosel = tofld.length;
	}
	len = tofld.length;
	tofld.length++;
	for (i=len; i>tosel; i--) {
		tofld[i].value = tofld[i-1].value;
		tofld[i].text = tofld[i-1].text;
	}

	tofld[tosel].value = acctName;
	tofld[tosel].text = acctName;
	if (tofld.selectedIndex != -1) {
		tofld.selectedIndex=tosel+1;
	}
	field1.value = '';
	field2.value = '';
	return true;
}


function Collect(fld) {
	if (fld){
		vals = new Array();
		notArray = (fld.value != null);
		if (notArray && fld.checked){
			vals[0] = fld.value;
			return vals;
		}
		ischeck = (fld.length>0 && fld[0].checked != null );
		for (j=0,i=0;i<fld.length;i++) {
			if (!ischeck || fld[i].checked)  {
				vals[j++] = fld[i].value;
			}
		}
		return vals.join('#~#');
	}
} 

function Collect1(frm,fldName) {
	i=0;
	vals = new Array();
	while (f.elements[fldName + '[' + i + ']'] != null) {
		fl = f.elements[fldName + '[' + i + ']'];
		if (fl.selectedIndex != null) {
            // list
			vals[i] = fl[fl.selectedIndex].value;
		}
		else {
            // text/hidden field
			vals[i] = fl.value;
		}
		i++;
	}
	return vals.join('#~#');
}

function CollectAll(fld) {
	f = document.mf;
	var i;
	if (fld && fld.length != 0) { 
		iflds = fld.split(',');
		for (i=0;i<iflds.length;i++) {
			if (f.elements[iflds[i]] != null) {
				f.elements['.'+iflds[i]].value = Collect(f.elements[iflds[i]]); 
			}
			else if (f.elements[iflds[i] + '[0]'] != null) {
				f.elements['.'+iflds[i]].value = Collect1(f,iflds[i]); 
			}
			else {
				f.elements['.'+iflds[i]].value = '';
			}
		}
	}
}

function DoSubmit(fld, arg, val) {
	CollectAll(fld);
	flag = 1;
	f = document.mf;

	// IE 4.0 doesn't like getting objects that don't exist and
	// edit_acctgrpmembers.phtml doesn't have/need a .saveas object in form mf
	if (typeof(f.elements['.save']) == 'object') {
		f.elements['.save'].value = '';
	}
	if(flag == 1) {
		return f.submit();
	}
}

</script>
	<input type=hidden name='.sess' value="<? echo $_sess; ?>">
    <input type=hidden name=".bodyarg" value="<?echo $_bodyarg; ?>">
	<input type=hidden name=".quickadd" value="<? /** @noinspection PhpUndefinedVariableInspection */
    echo $_quickadd; ?>">
	<input type=hidden name=".targetForm" value="<? /** @noinspection PhpUndefinedVariableInspection */
    echo $_targetForm; ?>">
	<input type=hidden name=".quickedit" value="<? /** @noinspection PhpUndefinedVariableInspection */
    echo $_quickedit; ?>">
	<input type=hidden name=".targetField" value="<? /** @noinspection PhpUndefinedVariableInspection */
    echo $_targetField; ?>">
	<input type=hidden name=".amode" value="<? /** @noinspection PhpUndefinedVariableInspection */
    echo $_amode; ?>">

	<input type=hidden name=".op" value="<? echo $_op; ?>">
	<input type=hidden name=".currentranges" value="1">
<? if( $_quickadd != '1' ) { ?>
    <table width="90%" border="0" cellpadding=0 cellspacing=0 bgcolor="<? /** @noinspection PhpUndefinedVariableInspection */
    echo $color4 ?>">
	<tr bgcolor="<? /** @noinspection PhpUndefinedVariableInspection */
    echo $color1 ?>">
	<td valign="top"><img src="../resources/images/ia-app/error/topleft.gif" alt="" width="14" height="14" border="0"></td>
	<td><img src="../resources/images/ia-app/backgrounds/trans.gif" alt="" width="1" height="20" border="0"></td>
	<td colspan="2" align=left width="100%"><font color="#ffffff" face="arial"><b> Aging Periods</b></font></b></font></td> 
	</tr>
	<tr><? /** @noinspection PhpUndefinedVariableInspection */
        PrintSaveCancelButton("onclick=\"return DoSubmit('$members', '.save');\""); ?></tr>
	</table>
	<p>
    <? 
} else{  ?>
	<table border="0" cellpadding="0" cellspacing="0" width="100%" bgcolor="#003366">
	<tr><td valign="top"> <img src="../resources/images/ia-app/icons/quickadd_header_icon.gif" alt="" width="47" height="40" border="0"></td> 
	<td valign="middle" width="100%">
	&nbsp;&nbsp;<font face="Arial, Helvetica" color="#FFFFFF"><b>Quick <? echo ($_quickedit == 1) ? "Edit:" : "Add:"; ?> Aging Periods</b></font> </td>
	</tr>
	</table>
	<table width="90%" border="0" cellpadding="4" cellspacing="0" bgcolor="<? /** @noinspection PhpUndefinedVariableInspection */
    echo $color4 ?>">
	<tr><? PrintSaveCancelButton("onclick=\"return DoSubmit('currentranges', '.save');\"", $_doclose); ?></tr>
	</table>
<?  
}  //**  end of if for table split  **//?>

	<table border=0 cellpadding=2 cellspacing=0 width="90%">
        <tr>
            <td align=right valign=top>
                <font face="verdana, arial" >
                    Ranges
                </font>
            </td>
            <td valign=top>
		<table border="0" cellpadding="0" cellspacing="0">
			<tr>
				<td>
				<select name="currentranges" size=10>
				<? ShowOptions($currentranges,  $currentranges); ?>
				<? if (count($currentranges) == 0) { ?>
					<option value="0">-- No Members --
				<?  
} ?>				
				</select>
				    </td>
				    <td valign="top">
										<a href="#here" onClick="return memberup(document.mf.currentranges)"
						onmouseover='window.status="Member Up";return true;'
						onmousemove='window.status="Member Up"; return true;'
						onfocus='window.status="Member Up"; return true;'
						onblur="window.status='';return true;"
						onmouseout ="window.status='';">
					<img src="<? echo IALayoutManager::getCSSButtonPath("up.gif"); ?>" border="0"></a><br>
					<a href="#here" onClick="return memberdown(document.mf.currentranges)"
						onmouseover='window.status="Member Down";return true;'
						onmousemove='window.status="Member Down"; return true;'
						onfocus='window.status="Member Down"; return true;'
						onblur="window.status='';return true;"
						onmouseout ="window.status='';">
					<img src="<? echo IALayoutManager::getCSSButtonPath("down.gif"); ?>" border="0"></a>
					<p>
					<a href="#here" onClick="return memberdel(document.mf.currentranges)"
						onmouseover='window.status="Member Delete";return true;'
						onmousemove='window.status="Member Delete"; return true;'
						onfocus='window.status="Member Delete"; return true;'
						onblur="window.status='';return true;"
						onmouseout ="window.status='';">
					<img src="<? echo IALayoutManager::getCSSButtonPath("delete.gif"); ?>" border="0"></a><br>
									</td>
			</tr>
		</table>
	   </td>
	</tr>
		        <tr>
	    <td align=right>
		<font face="verdana, arial" >
		    Enter Ranges
		</font>
	    </td>
	    <td valign=top>
		<table border="0" cellpadding="0" cellspacing="0">
			<tr>
				<td>
				<input type="text" name="range1" value="" maxlength="3" size="3">
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font face="verdana, arial" size="4"><b>-</b></font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="text" name="range2" value="" maxlength="3" size="3">
				<input type=hidden name=".accounts" size=30 value="1">
				<a href="#here" onClick="return rangeadd(document.mf['range1'], document.mf['range2'], document.mf.currentranges)"
					onmouseover='window.status="Member Add";return true;'
					onmousemove='window.status="Member Add"; return true;'
					onfocus='window.status="Member Add"; return true;'
					onblur="window.status='';return true;"
					onmouseout ="window.status='';">
				<img src="<? echo IALayoutManager::getCSSButtonPath("add.gif"); ?>" border="0" align="absmiddle"></a>
				</td>
			</tr>
		</table>
        </tr>
       </table>
      <p>
      <table width="90%" border="0" cellpadding=4 cellspacing=0 bgcolor="<?echo $color4 ?>">
        <tr>
            <? PrintSaveCancelButton("onclick=\"return DoSubmit('currentranges', '.save');\"", $_doclose); ?>
        </tr>
      </table>
  </form>
<? if( $_quickadd != '1' ) { include_once 'footer.inc'; 
}?>
<? 
}  //**  END OF BODY CONDITION FOR BodyArg  **
