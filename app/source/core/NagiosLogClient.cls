<?php

//
//  FILE:           NagiosLogClient.cls
//  AUTHOR:         <PERSON> <<EMAIL>>
//  DESCRIPTION:    Null client that does nothing
//
//  (C)2000, Intacct Corporation, All Rights Reserved
//
//  Intacct Corporation Proprietary Information.
//  This document contains trade secret data that belongs to Intacct
//  corporation and is protected by the copyright laws. Information herein
//  may not be used, copied or disclosed in whole or part without prior
//  written consent from Intacct Corporation.
//

/**
 * Class NagiosLogClient a Nagios service implementation that simply logs.
 */
class NagiosLogClient extends NagiosBaseClient implements INagiosService {

    /**
     * @param string $host       hostname wanting to send message to <PERSON><PERSON><PERSON> (web04)
     * @param string $service    service messages are about (upload, db, ...)
     *
     * @throws Exception on invalid parameters
     */
    public function __construct($host, $service)
    {
        parent::__construct($host, $service);
    }

    /**
     * sends a message to Nagios
     *
     * @param string $status   one of the interface's STATUS_* strings
     * @param string $message  the status message for <PERSON><PERSON><PERSON>
     *
     * @throws Exception on invalid statuses
     */
    public function send($status, $message)
    {
        $this->validateStatus($status);

        LogToFile("NAGIOS: host: {$this->host} service: {$this->service} status: $status message: $message\n");
    }

}

