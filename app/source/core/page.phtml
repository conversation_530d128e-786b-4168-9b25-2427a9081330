<?  
//=============================================================================
//
//	FILE:			page.phtml
//	AUTHOR:			dwilks
//	DESCRIPTION:    Generic page framework	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once 'util.inc';
require_once 'Page.cls';

Init();

$page = Page::createPage();

$method = $_SERVER['REQUEST_METHOD'];
if ($method == 'GET') {
    $page->get();
} else if ($method == 'POST') {
    $page->post();
}

Shutdown();


