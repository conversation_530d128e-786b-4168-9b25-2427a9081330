<?php

/**
 * Class EditorControl
 */
class EditorControl
{
    /**
     * @var array $params
     */
    protected $params;
    /**
     * @var EditorField $editorField
     */
    protected $editorField;

    /**
     * @param array       $params
     * @param EditorField $editorField
     */
    public function __construct(&$params, EditorField $editorField)
	{
		$this->params =& $params;
		$this->editorField = $editorField;
		$this->setUIControlType();
		$this->CalcParams();
	}

    /**
     * @return int
     */
    protected function getDefaultSize()
	{
		return 50;
	}

    /**
     *
     */
    protected function CalcParams()
	{
		$size = $this->getDefaultSize();
		$maxlength = $this->params['type']['maxlength'] ?? null;
		if( $this->params['type']['size'] ?? null ) {
            $size = $this->params['type']['size'];
        } else if( $maxlength && $maxlength  > 50 ) {
		    $size = 50;
        } else if( $maxlength && $maxlength < 50 ) {
		    $size = $maxlength;
        }
		
		$this->params['type']['size'] = $size;
	}

    /**
     *
     */
    private function setUIControlType()
	{
		$uiControl = get_class($this);
		if( strpos($uiControl, 'Editor') === 0 )
		{
			$uiControl = substr($uiControl, 6);
		}
		$this->params['uiControl'] = $uiControl;
	}

    /**
     * @param string $name
     *
     * @return null
     */
    protected function getProperty($name)
	{
        return $this->params[$name] ?? $this->editorField->getProperty($name);
	}

    /**
     * @param array $object
     */
    public function instantiate(&$object)
	{
	}

    /**
     * @param array $object
     */
    public function finalize(&$object)
	{
	}

    /**
     * @param CspPolicy  $cspPolicy
     * @param FormEditor $editor
     */
    public function setCspPolicy(CspPolicy $cspPolicy, FormEditor $editor)
    {
    }
}