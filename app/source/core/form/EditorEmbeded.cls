<?php


/**
 * EditorEmbeded.cls
 *
 * <AUTHOR> <<EMAIL>>
 *
 */
class EditorEmbeded extends EditorComponent
{
    /**
     * @param array      $params
     * @param FormEditor $editor
     */
    public function __construct(&$params, FormEditor $editor)
    {
        parent::__construct($params, $editor);
    }

    /**
     * I18N. Return labels for all buttons.
     *
     * @return null
     */
    public function getLabel()
    {
        return null;
    }
}