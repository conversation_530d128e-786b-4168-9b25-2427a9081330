<?php

/**
 * Class EditorControlTextLabel
 */
class EditorControlTextLabel extends EditorControl
{
    /**
     * @param array       $params
     * @param EditorField $editorField
     */
    public function __construct(&$params, EditorField $editorField)
	{
		parent::__construct($params, $editorField);
	}
	
	protected function CalcParams()
	{
		parent::CalcParams();
		
		if (!isset($this->params['maxlength'])) { 
			$this->params['maxlength'] = $this->params['type']['maxlength'] ?? null;
		}

		if (!isset($this->params['size'])) { 
			$this->params['size'] = $this->params['type']['size']; 
			if (!isset($this->params['size'])) { 
				$this->params['size'] = $this->params['maxlength'] ?  min($this->params['maxlength'],50) : 40;
			}
		}

        // ww: need to get rid of this at some point. Its just confusing when both are set.
		if (!isset($this->params['default'])) { 
			$this->params['default'] = $this->params['type']['default'] ?? null;
		}

        // ww: By default assume its a token.
        if (!isset($this->params['isToken'])) {
            $this->params['isToken'] = "true";
        }
	}
}
