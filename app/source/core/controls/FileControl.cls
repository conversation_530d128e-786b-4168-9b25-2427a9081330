<?
import('UIControl');

class FileControl extends UIControl
{

    /**
     * @param array $_params
     */
    function __construct($_params)
    {
        parent::__construct($_params);
        $this->_params = INTACCTarray_merge($this->_params, $this->CalcParams($_params));
    }


    /**
     * @param array $_params
     *
     * @return array
     */
    function CalcParams($_params)
    {
        
        if (!isset($_params['maxlength'])) { 
            $_params['maxlength'] = $this->type['maxlength']; 
        }

        if (!isset($_params['size'])) { 
            $_params['size'] = $this->type['size']; 
            if (!isset($_params['size'])) { 
                $_params['size'] = $_params['maxlength'] ?  min($_params['maxlength'], 50) : 40;
            }
        }

        return $_params;
    }



    function Show() 
    {
        $name    = $this->_params['varname'];
        $value    = $this->_params['value'];
        /*$form    = $this->_params['form'];
        $layer    = $this->_params['layer'];
        $prefix = $this->_params['parent_prefix'];
        $path    = $this->_params['path'];*/
        $onclick_js=$this->_params['onclick'];
        $onchange_js=$this->_params['onchange'];
        $attachbutton = $this->_params['attachbutton'];
        $button_props = $this->_params['button_props'];

        echo "<table border=0 cellpadding=0 cellspacing=0 width1='100%'><tr><td>";

        echo "<input type='hidden' name='MAX_FILE_SIZE' value='4000'/>";

        echo "<INPUT type='file' name='$name' " .
        " value='" . isl_htmlspecialchars($value) . "' " .
        " size=" . $this->_params['size'] . 
        " maxlength=" . $this->_params['maxlength'] .
        ( $onclick_js != '' ? " onclick=\"" . $onclick_js . "\"" : '' ) .
        ( $onchange_js!='' ? " onChange=\"" . $onchange_js . "\"" : '' ) .
        $this->_params['eventcall'] . 
        " />";

        echo "</td>";

        if ( $attachbutton === true && $button_props['name'] != '' ) {
            $onclick_js = $button_props['onclick'];
            $onchange_js = $button_props['onchange'];

            echo "<td width=10>&nbsp;</td><td valign=center>";

            echo "<input class='nosavehistory' " .
            " type='" . $button_props['type'] . "' ".
            " name='" . $button_props['name'] . "' ".
            " value='" . $button_props['title'] . "' ".
            ( $onclick_js != '' ? " onclick=\"" . $onclick_js . "\"" : '' ) .
            ( $onchange_js!='' ? " onChange=\"" . $onchange_js . "\"" : '' ) .
            $this->_params['eventcall'] . 
            " />";

            echo "</td>";

        }

        echo "</tr></table>";

    }

}


