<?php
/**
 * Metric class for DispatcherEnginecomponent, consumerError metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricDispatcherEngineConsumerError  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $componentName the component name
     * @param string $id
     * @param string $error

     */
    public function __construct(string $componentName, $id = null, $error = null)
    {
        parent::__construct(
            $componentName,
            'consumerError',
            0,
            [],
            [
                'id',
                'error'
            ]
            , $id, $error
        );
    }

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return parent::getAttribute('id');
    }
    
    /**
     * @param string $id
     */
    public function setId(string $id)
    {
        parent::setAttribute('id', $id);
    }

    /**
     * @return string|null
     */
    public function getError(): ?string
    {
        return parent::getAttribute('error');
    }
    
    /**
     * @param string $error
     */
    public function setError(string $error)
    {
        parent::setAttribute('error', $error);
    }

}
