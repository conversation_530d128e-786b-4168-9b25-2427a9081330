<?php
//
// FILE:            ExpenseFeedbackHttpRequest.cls
// AUTHOR:          Suresh <PERSON><<EMAIL>>
// DESCRIPTION:
//
// (C)2024, Intacct Corporation, All Rights Reserved
//
// Intacct Corporation Proprietary Information.
// This document contains trade secret data that belongs to Intacct
// corporation and is protected by the copyright laws. Information herein
// may not be used, copied or disclosed in whole or part without prior
// written consent from Intacct Corporation.
//

class ExpenseFeedbackHttpRequest implements ExpenseFeedbackHttpRequestInterface
{

    /**
     * Get Expense Token in HttpResonse Object
     *
     * @param bool $noCacheToken
     *
     * @return SimpleHttpResponse
     *
     * @throws \Exception|SimpleHttpResponseException|\InvalidArgumentException
     */
    private static function getTokenResponse(bool $noCacheToken): SimpleHttpResponse
    {
        return ExpenseTokenFactory::get(ExpenseTokenFactory::SCOPE_EXPENSE)->getTokenResponse($noCacheToken);
    }

    /**
     * Get access_token from HttpRespose object
     *
     * @param bool $noCacheToken
     *
     * @return string
     *
     * @throws SimpleHttpResponseException
     */
    private static function getAccessToken($noCacheToken): string
    {
        $tokenResponseObj = self::getTokenResponse($noCacheToken);
        $tokenResponse = $tokenResponseObj->getResponse();
        $tokenResponse = json_decode($tokenResponse);
        if (!isset($tokenResponse->access_token) || !is_string($tokenResponse->access_token)) {
            throw (new SimpleHttpResponseException($tokenResponseObj, "Unable to build HTTP Request header"));
        }

        return $tokenResponse->access_token;
    }

    /**
     * Build auth header for Expense Feedback request
     *
     * @param string[] $headers
     * @param bool $noCacheToken Shouldn't be call in usual scenerio
     *
     *
     * @throws SimpleHttpResponseException
     */
    private static function buildAuthHeader(&$headers = [], $noCacheToken = false)
    {
        $accessToken = self::getAccessToken($noCacheToken);
        $headers[] = "Authorization: Bearer {$accessToken}";
        $companyGuid = \CompanyCacheHandler::getInstance()->getProperty('COMPANYPREF', 'GUID');
        if (empty($companyGuid)) {
            throw new \Exception("GUID is not set for cny# -" . GetMyCompany());
        }
        $headers[] = "Content-Type: application/json";
    }

    /**
     * Do the request to the Expense Feedback server with Auth header
     *
     * @param string       $action
     * @param string       $url
     * @param array|string $body
     * @param array        $headers
     * @param string       $httpMethod
     *
     * @return SimpleHttpResponse
     * @throws Exception
     */
    private static function doHTTPCall(string $action, string $url, $body, $headers,
                                       string $httpMethod = "POST"): SimpleHttpResponse
    {
        $timeout = ExpenseFeedbackProcess::getIAConfig("EXPENSE_FEEDBACK_TIMEOUT");
        $curlInfo = null;
        $guId = \CompanyCacheHandler::getInstance()->getProperty('COMPANYPREF', 'GUID');
        $httpCallMetric = new MetricExpenseFeedbackHttpRequestHttpCall();
        $httpCallMetric->startTime();
        $httpCallMetric->setAction($action);
        $httpCallMetric->setEndPoint($url);
        $httpCallMetric->setGuId($guId);
        ExpenseFeedbackProcess::log('Request headers - '.json_encode($headers), LogManager::DEBUG, ExpenseFeedbackTrait::$processorPrefix);
        ExpenseFeedbackProcess::log('Request Http Method - ' . $httpMethod, LogManager::DEBUG, ExpenseFeedbackTrait::$processorPrefix);
        $responseCode = \Util::httpCall($url, $body, $response, ($httpMethod === 'GET'), null,
            null, true, $headers, $httpMethod, null, true, $theRetHeaders,
            false, $curlInfo, $timeout);
        ExpenseFeedbackProcess::log('Response headers - '. json_encode($theRetHeaders), LogManager::DEBUG, ExpenseFeedbackTrait::$processorPrefix);
        ExpenseFeedbackProcess::log('Response Body - '. json_encode($response), LogManager::DEBUG, ExpenseFeedbackTrait::$processorPrefix);
        ExpenseFeedbackProcess::log('Response Code - '. $responseCode, LogManager::DEBUG, ExpenseFeedbackTrait::$processorPrefix);
        $httpCallMetric->setResCode($responseCode);
        $httpCallMetric->stopTime();
        $httpCallMetric->publish();

        return (new SimpleHttpResponse($responseCode, $response, $theRetHeaders));
    }

    /**
     * All Expense Feedback request must pass through this function.
     *
     * @param string       $action Expense Feedback Action
     * @param string       $url  partial, environment-agnostic, url
     * @param array|string $body JSON request body
     * @param string       $httpMethod
     * @param bool         $noCacheToken
     *
     * @return SimpleHttpResponse
     * @throws SimpleHttpResponseException
     */
    public static function makeHttpRequest(string $action, string $url, $body, string $httpMethod = "POST",
                                           bool $noCacheToken = false): SimpleHttpResponse
    {
        self::buildAuthHeader($headers, $noCacheToken);
        return self::doHTTPCall($action, $url, $body, $headers, $httpMethod);
    }
}