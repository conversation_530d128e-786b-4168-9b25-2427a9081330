<?
/**
 * Activity trail records associated with data object.
 * Table: PT_ACT_TRAIL
 */
require_once('Pt_Includes.inc');

class Pt_ActivityTrail extends Pt_EntityRecord {

    /* @var string $text */
	private $text;
    /* @var string $emailText */
	private $emailText;

	/**
     * @param int    $id
     * @param int    $objId
     * @param string $text
     * @param string $emailText
     * @param int    $createdBy
     * @param string $createdAt
	 */
	public function __construct($id, $objId, $text, $emailText, $createdBy, $createdAt) {
		parent::__construct($id, $objId, $createdBy, $createdAt, $createdBy, $createdAt);
		$this->text = $text;
		$this->emailText = $emailText;
	}

	/**
	 * Get text of activity trail record
     *
     * @return string
	 */
	public function getText() {
		return $this->text;
	}

	/**
	 * Get text of email (if any)
     *
     * @return string
	 */
	public function getEmailText() {
		return $this->emailText;
	}

	/**
	 * Is email record?
     *
     * @return bool
	 */
	public function isEmail() {
		return strlen($this->emailText ?? '') > 0;
	}

	/**
	 * String representation
     *
     * @return string
	 */
	public function __toString() {
		return strval($this->text);
	}

}
