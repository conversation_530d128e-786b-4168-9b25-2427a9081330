<?
/**
 * Stores ids defined in packed (old) coupled with local (new) ids.
 */
require_once 'Pt_StdDataObjectDef.cls';

class Pt_IdDepot
{
    /* @var int[][] $map */
    private $map;

    public function __construct()
    {
        $this->map = [];
    }

    /**
     * Store pair of ids (old vs new).
     *
     * @param string $idType
     * @param int    $oldId
     * @param int    $newId
     */
    public function put($idType, $oldId, $newId) 
    {
        $oid = intval($oldId);
        $nid = intval($newId);
        if ( $oid > 0 && $nid > 0 ) {
            $this->map[$idType][$oid] = $nid;
        }
    }

    /**
     * attempts to decompose a field id and figure out if it's the platform version
     * of a standard object's standard (as opposed to custom) field.  There are two
     * functions that conspire to make this more difficult than it needs to be.
     * For custom fields Util_StandardFieldMap::createPlatformFieldDef adds 1000 to the
     * custom field's record#.  To make all platform fields
     * Util_StandardFieldMap::createPlatformFieldDef takes the field id and shoves it
     * into the low-order digits of a 15 digit string begining with 999.
     *
     * @param int $id the field id
     *
     * @return bool true if the field is a standard built-in field.  False if it's a
     *              custom field on a standard object or any field on a platform object.
     */
    public static function isStandardFieldId($id) 
    {

        // See Util_StandardFieldRegistry::registerField
        if ( $id < Pt_StdDataObjectDef::DOCTYPE_STARTING_RANGE ) {
            return false;
        }

        // See Util_StandardFieldMap::createPlatformFieldDef
        $fieldId = $id % (Util_StandardFieldMap::FIELD_MAP_CUSTFIELD_OFFSET * 10);

        return $fieldId < Util_StandardFieldMap::FIELD_MAP_CUSTFIELD_OFFSET;
    }

    /**
     * Get new id for given old id, throw exception if not found.
     *
     * @param string $idType
     * @param int    $oldId
     *
     * @return int
     */
    public function get($idType, $oldId) 
    {
        $oid = intval($oldId);
        if (    ( $oid <= 0 )
            || ( $idType == 'OBJECT' && $oid <= STANDARD_OBJECT_ID_CEILING )
            || ( $idType == 'FIELD' &&  self::isStandardFieldId($oid) ) 
        ) {
            return $oid;
        }
        $value = $this->map[$idType][$oid] ?? null;
        if ( ! isset($value) && $oid > 0 ) {
            throw new Pt_I18nException('PAAS-0131',
                                       "Error: Something is missing in Application XML: ID for $idType $oldId not found. ",
                                       [ 'IDTYPE' => "$idType", 'OLDID' => "$oldId" ]);
        }
        return $value;
    }

    /**
     * Get new id for given old id, do not throw exception.
     *
     * @param string $idType
     * @param int    $oldId
     *
     * @return int
     */
    public function getAnyway($idType, $oldId) 
    {
        $oid = intval($oldId);
        if (    ( $oid <= 0 )
            || ( $idType == 'OBJECT' && $oid <= STANDARD_OBJECT_ID_CEILING )
            || ( $idType == 'FIELD' &&  self::isStandardFieldId($oid) ) 
        ) {
            return $oid;
        }

        return $this->map[$idType][$oid] ?? null;
    }

    /**
     * True, if there is new id for old id.
     *
     * @param string $idType
     * @param int    $oldId
     *
     * @return bool
     */
    public function has($idType, $oldId) 
    {
        $oid = intval($oldId);
        return isset($this->map[$idType][$oid]);
    }

    /**
     * String representation.
     *
     * @return string
     */
    public function __toString() 
    {
        return util_dump($this->map);
    }
}
