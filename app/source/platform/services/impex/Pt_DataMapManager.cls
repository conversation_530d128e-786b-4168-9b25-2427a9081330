<?
/**
 * Manages persistence of maps used for conversion and import.
 * Table: PT_DATA_MAP
 */
require_once 'Pt_Includes.inc';

require_once 'Pt_AbstractDataMap.cls';
require_once 'Pt_ImportMap.cls';
require_once 'Pt_ConversionMap.cls';
require_once 'Pt_Cache.cls';

class Pt_DataMapManager
{
    const OBJECTTYPE = 'Pt_DataMap';

    const SQL_SELECT_ALL =
        "SELECT RECORD#, ORIGINAL_ID, IS_IMPORT, MAP_NAME, DEST_DEF_ID, SRC_DEF_ID, XML_STRING,
            CREATED_BY, TO_CHAR(CREATED_AT, 'MM/DD/YYYY HH24:MI:SS') CREATED_AT,
            UPDATED_BY, TO_CHAR(UPDATED_AT, 'MM/DD/YYYY HH24:MI:SS') UPDATED_AT
        FROM PT_DATA_MAP WHERE CNY#=:1";

    const SQL_INSERT =
        "INSERT INTO PT_DATA_MAP (CNY#, RECORD#, ORIGINAL_ID, IS_IMPORT, MAP_NAME, DEST_DEF_ID, SRC_DEF_ID, XML_STRING,
            CREATED_BY, CREATED_AT, UPDATED_BY, UPDATED_AT)
        VALUES (:1, :2, :3, :4, :5, :6, :7, :8, :9, CURRENT_TIMESTAMP AT TIME ZONE 'GMT', :10,
            CURRENT_TIMESTAMP AT TIME ZONE 'GMT')";

    const SQL_UPDATE =
        "UPDATE PT_DATA_MAP SET MAP_NAME=:1, XML_STRING=:2, UPDATED_BY=:3, UPDATED_AT=CURRENT_TIMESTAMP AT TIME ZONE 'GMT'
        WHERE CNY#=:4 AND RECORD#=:5";

    const SQL_DELETE =
        "DELETE FROM PT_DATA_MAP WHERE CNY#=:1 AND RECORD#=:2";

    const SQL_DELETE_DEF =
        "DELETE FROM PT_DATA_MAP WHERE CNY#=:1 AND (DEST_DEF_ID=:2 OR SRC_DEF_ID=:3)";

    /**
     * Get list of all maps for given source definition.
     *
     * @param int $objDefId
     *
     * @return Pt_AbstractDataMap[]
     * @throws Exception
     */
    public static function getByObjectDef($objDefId) 
    {
        $arr = [];
        if ($objDefId <= 0) {
            return $arr; 
        }
        $maps = Pt_Cache::getDataMaps();
        foreach ( $maps as $map) {
            if (($map instanceof Pt_ConversionMap && $map->getSrcObjectDefId() == $objDefId)
                || ($map instanceof Pt_ImportMap && $map->getDestObjectDefId() == $objDefId)
            ) {
                $arr[] = $map;
            }
        }
        unset($map);
        return $arr;
    }

    /**
     * Get list of conversion maps for given source definition.
     *
     * @param int $srcDefId
     *
     * @return Pt_ConversionMap[]
     * @throws Exception
     */
    public static function getConversionByDef($srcDefId) 
    {
        $arr = [];
        if ($srcDefId <= 0) {
            return $arr; 
        }
        $maps = Pt_Cache::getDataMaps();
        foreach ( $maps as $map) {
            if ($map->getSrcObjectDefId() == $srcDefId && $map instanceof Pt_ConversionMap) {
                $arr[] = $map;
            }
        }
        unset($map);
        return $arr;
    }

    /**
     * Get list of conversion maps for given destination and source definitions.
     *
     * @param int $srcDefId
     * @param int $destDefId
     *
     * @return Pt_ConversionMap[]
     * @throws Exception
     */
    public static function getConversionByDefs($srcDefId, $destDefId) 
    {
        $arr = [];
        if ($srcDefId <= 0 || $destDefId <= 0) {
            return $arr;
        }
        $maps = Pt_Cache::getDataMaps();
        foreach ( $maps as $map) {
            if (!($map instanceof Pt_ConversionMap)) {
                continue; 
            }
            if ($map->getSrcObjectDefId() == $srcDefId && $map->getDestObjectDefId() == $destDefId) {
                $arr[] = $map;
            }
        }
        unset($map);
        return $arr;
    }

    /**
     * Get list of import maps for given destination definition.
     *
     * @param int $destDefId
     *
     * @return Pt_ImportMap[]
     * @throws Exception
     */
    public static function getImportByDef($destDefId) 
    {
        $arr = [];
        if ($destDefId <= 0) {
            return $arr; 
        }
        $maps = Pt_Cache::getDataMaps();
        foreach ( $maps as $map) {
            if (!($map instanceof Pt_ImportMap)) {
                continue; 
            }
            if ($map->getDestObjectDefId() == $destDefId) {
                $arr[] = $map;
            }
        }
        unset($map);
        return $arr;
    }

    /**
     * Get import map by name
     *
     * @param string $name
     * @param int    $excludeId
     *
     * @return Pt_ImportMap|null
     * @throws Exception
     */
    public static function getImportByDisplayName($name, $excludeId=0) 
    {
        if (strlen($name)==0) {
            return null; 
        }
        $maps = Pt_Cache::getDataMaps();
        foreach ( $maps as $map) {
            if (!($map instanceof Pt_ImportMap)) {
                continue; 
            }
            if ($excludeId>0 && $map->getId()==$excludeId) {
                continue; 
            }
            if (isl_strcasecmp($map->__toString(), $name)===0) {
                return $map; 
            }
        }
        return null;
    }

    /**
     * Get map by id.
     *
     * @param int $id
     *
     * @return Pt_AbstractDataMap|null
     */
    public static function getById($id) 
    {
        if ($id <= 0) {
            return null; 
        }
        return Pt_Cache::getDataMap($id);
    }

    /**
     * Get AbstractDataMap by original id.
     *
     * @param string $origId
     *
     * @return Pt_AbstractDataMap
     * @throws Exception
     */
    public static function getByOriginalId($origId) 
    {
        if (!isset($origId)) {
            return null; 
        }
        $maps = Pt_Cache::getDataMaps();
        foreach ( $maps as $map) {
            if ($map->getOriginalId()==$origId) {
                return $map; 
            }
        }
        return null;
    }

    /**
     * Get conversion map by id.
     *
     * @param int $id
     *
     * @return Pt_ConversionMap|null
     */
    public static function getConversionById($id) 
    {
        $map = self::getById($id);
        return ($map instanceof Pt_ConversionMap ? $map : null);
    }

    /**
     * Get import map by id.
     *
     * @param int $id
     *
     * @return Pt_ImportMap|null
     */
    public static function getImportById($id) 
    {
        $map = self::getById($id);
        return ($map instanceof Pt_ImportMap ? $map : null);
    }

    /**
     * Save newly created data map.
     *
     * @param string             $mapName
     * @param Pt_AbstractDataMap $map
     * @param string             $origId
     * @param string             $appOriginalId
     *
     * @return Pt_AbstractDataMap
     * @throws Exception
     */
    public static function create($mapName, Pt_AbstractDataMap $map, $origId, $appOriginalId)
    {
        if (strlen($mapName) == 0) {
            throw new Pt_I18nException('PAAS-0495', "No map name provided");
        }

        $buff = '';
        $map->toXML($buff);

        $createdBy = GetMyUserid();
        $createdAt = util_now_gmt();
        $isImport = ($map instanceof Pt_ImportMap);
        $destDefId = $map->getDestObjectDefId();
        $srcDefId = $map->getSrcObjectDefId();

        $id = db_nextId('PT_DATA_MAP');
        // PHP8_NUMERIC_STRING_COMPARE; Priority: low; Behavior: same, Risk: low, Solution: cast
        if (!isset($origId) || (int) $origId <= 0) {
            $origId = GetMyCompany().'@'.$id; 
        }

        $map2 = ($isImport ?
            new Pt_ImportMap($id, $origId, $mapName, $destDefId, $srcDefId, $buff, $createdBy, $createdAt,
                             $createdBy, $createdAt, [ $appOriginalId]) :
            new Pt_ConversionMap($id, $origId, $mapName, $destDefId, $srcDefId, $buff, $createdBy, $createdAt,
                                 $createdBy, $createdAt, [ $appOriginalId])
        );

        db_exec([ self::SQL_INSERT, GetMyCompany(), $id, $map2->getOriginalId(), db_TF($isImport), db_str($mapName, 50),
                  $destDefId, $srcDefId, $buff, $createdBy, $createdBy ]);
        $objDefId = $isImport ? $destDefId : $srcDefId;
        Pt_AppLinkManager::create(self::OBJECTTYPE, $id, $appOriginalId, $objDefId);

        Pt_Cache::setDataMap($map2);

        $token = [
            'id' => "IA.MAP_MAP_NAME_HAS_BEEN_CREATED",
            'placeHolders' => [
                ['name' => 'MAP_NAME', 'value' => "$mapName"],
            ],
        ];

        $objDefId = ($map2 instanceof Pt_ConversionMap ? $map2->getSrcObjectDefId() : $map2->getDestObjectDefId());
        Pt_ActivityTrailManager::createI18N(-$objDefId, $token);
        return $map2;
    }

    /**
     * Update existing data map.
     *
     * @param string             $mapName
     * @param Pt_AbstractDataMap $map
     * @param string             $appOrigId
     *
     * @throws Exception
     */
    public static function update($mapName, Pt_AbstractDataMap $map, $appOrigId)
    {
        if (strlen($mapName) == 0) {
            throw new Pt_I18nException('PAAS-0496', "No map name provided");
        }

        $updatedBy = GetMyUserid();
        $updatedAt = util_now_gmt();

        $buff = '';
        $map->toXML($buff);

        $mapId = $map->getId();
        db_exec([self::SQL_UPDATE, db_str($mapName, 50), $buff, $updatedBy, GetMyCompany(), $mapId]);
        $objDefId = ($map instanceof Pt_ConversionMap ? $map->getSrcObjectDefId() : $map->getDestObjectDefId());
        Pt_AppLinkManager::update(self::OBJECTTYPE, $mapId, $appOrigId, $objDefId);

        $map->updateAbstractDataMap($mapName, $updatedBy, $updatedAt, $appOrigId);
        Pt_Cache::setDataMap($map);

        $token = [
            'id' => "IA.MAP_MAP_NAME_HAS_BEEN_UPDATED",
            'placeHolders' => [
                ['name' => 'MAP_NAME', 'value' => "$mapName"],
            ],
        ];

        Pt_ActivityTrailManager::createI18N(-$objDefId, $token);
    }

    /**
     * Delete existing conversion map.
     *
     * @param Pt_AbstractDataMap $map
     *
     * @throws Exception
     */
    public static function delete(Pt_AbstractDataMap $map)
    {
        $mapId = $map->getId();
        db_exec([ self::SQL_DELETE, GetMyCompany(), $mapId ]);
        Pt_AppLinkManager::deleteByRecord(self::OBJECTTYPE, $mapId);
        Pt_Cache::removeDataMap($mapId);

        $token = [
            'id' => "IA.MAP_MAP_NAME_HAS_BEEN_DELETED",
            'placeHolders' => [
                ['name' => 'MAP_NAME', 'value' => "$map"],
            ],
        ];

        $objDefId = ($map instanceof Pt_ConversionMap ? $map->getSrcObjectDefId() : $map->getDestObjectDefId());
        Pt_ActivityTrailManager::createI18N(-$objDefId, $token);
    }

    /**
     * Delete all unlinked maps for object definition.
     *
     * @param Pt_DataObjectDef $objDef
     *
     * @throws Exception
     */
    public static function deleteObjDef(Pt_DataObjectDef $objDef) 
    {
        $objDefId = $objDef->getId();
        db_exec([self::SQL_DELETE_DEF, GetMyCompany(), $objDefId, $objDefId]);

        $ids = [];
        foreach ( self::getByObjectDef($objDefId) as $map) {
            $mapId = $map->getId();
            Pt_Cache::removeDataMap($mapId);
            $ids[] = $mapId;
        }
        Pt_AppLinkManager::deleteByRecords(self::OBJECTTYPE, $ids);
        unset($map);
    }

    /**
     * Get list of all data maps for given customer.
     *
     * @return Pt_AbstractDataMap[]
     * @throws Exception
     */
    public static function loadCache() 
    {
        $resultset = db_query([self::SQL_SELECT_ALL, GetMyCompany()]);
        $appOrigIds = Pt_AppLinkManager::getByObjectType(self::OBJECTTYPE);
        $arr = [];
        $tokens = ["IA.ERROR_LOADING_MAP"];
        $textMap = getIntlTextMap($tokens, []);
        foreach ($resultset as $row) {
            if ($row === false) {
                break;
            }
            try {
                $map = self::getDataMap($row, $appOrigIds[$row['RECORD#']] ?? []);
                $arr[$map->getId()] = $map;
            } catch (Exception $ex) {
                eppp(GT($textMap, "IA.ERROR_LOADING_MAP") .$ex->getMessage());
            }
        }
        return $arr;
    }

    /**
     * Select data map data from ResultSet.
     *
     * @param string[] $row
     * @param string[] $appOriginalIds
     *
     * @return Pt_AbstractDataMap
     * @throws Exception
     */
    private static function getDataMap($row, $appOriginalIds)
    {
        $id = intval($row['RECORD#']);
        $origId = $row['ORIGINAL_ID'];
        $isImport = db_bool($row['IS_IMPORT']);

        if ($isImport) {
            return new Pt_ImportMap(
                $id,
                $origId,                        // origId
                $row['MAP_NAME'],               // mapName
                intval($row['DEST_DEF_ID']),    // destDefId
                intval($row['SRC_DEF_ID']),     // srcDefId
                $row['XML_STRING'],             // xmlString
                intval($row['CREATED_BY']),     // createdBy
                $row['CREATED_AT'],             // createdAt
                intval($row['UPDATED_BY']),     // updatedBy
                $row['UPDATED_AT'],             // updatedAt
                $appOriginalIds
            ); 
        }
        else {
            return new Pt_ConversionMap(
                $id,
                $origId,                        // origId
                $row['MAP_NAME'],               // mapName
                intval($row['DEST_DEF_ID']),    // destDefId
                intval($row['SRC_DEF_ID']),     // srcDefId
                $row['XML_STRING'],             // xmlString
                intval($row['CREATED_BY']),     // createdBy
                $row['CREATED_AT'],             // createdAt
                intval($row['UPDATED_BY']),     // updatedBy
                $row['UPDATED_AT'],             // updatedAt
                $appOriginalIds
            ); 
        }
    }

    /**
     * @return string
     */
    public static function getObjectType() : string
    {
        return self::OBJECTTYPE;
    }

}
