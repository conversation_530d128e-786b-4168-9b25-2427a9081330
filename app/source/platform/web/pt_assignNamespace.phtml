<?
/**
 * Namespace assign (launched by pt_appEdit.phtml)
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2021 Intacct Corporation All, Rights Reserved
 *
 *  Intacct Corporation Proprietary Information.
 *  This document contains trade secret data that belongs to Intacct
 *  corporation and is protected by the copyright laws. Information herein
 *  may not be used, copied or disclosed in whole or part without prior
 *  written consent from Intacct Corporation.
 */

try {
    include 'pt_setupHeader.inc';
    
    // include_once "Pt_SimplePickItem.cls";
    
    $emptyOrFormControl = trim(Pt_SetupComponents::$emptyOrFormControl);
    $boldOrEmpty = Pt_SetupComponents::$boldOrEmpty;
    $emptyOrBtnPrimaryClass = Pt_SetupComponents::$emptyOrBtnPrimaryClass;
    $emptyOrSectionClass = Pt_SetupComponents::$emptyOrSectionClass;
    $rbs_lightsilverTableOrEmpty = Pt_SetupComponents::$rbs_lightsilverTableOrEmpty;

    $tokens = [ "IA.ERROR.ERROR_PLEASE_PROVIDE_A_NAMESPACE", "IA.ERROR.ERROR_THE_NAMESPACE_CONTAINS_AN_ILLEGAL_C",
                "IA.ERROR.ERROR_INVALID_DATA_REVIEW_ERROR_MESSAGES", "IA.ERROR.ERROR_THE_NAMESPACE_IS_NOT_AVAILABLE",
                "IA.NAMESPACE_AVAILABLE", "IA.NAMESPACE_UNAVAILABLE_TRY_ANOTHER",
                "IA.ERROR.ERROR_AN_ERROR_OCCURED_PLEASE_RETRY", "IA.APPLICATION_NAMESPACE", "IA.SAVE", "IA.CANCEL",
                "IA.ENTER_A_NAMESPACE", "IA.CHECK_AVAILABILITY" ];
    $placeholderTokens = [];
    $textMap = getIntlTextMap($tokens, $placeholderTokens);
    ?>
    
    <script language="JavaScript">
        function rbf_checkInput() {
            with ( document.theForm ) {

                rbf_clearErrors();
                var hasError = false;

                if ( name.value == '' ) {
                    rbf_activateError('name', '<?= GT($textMap, "IA.ERROR.ERROR_PLEASE_PROVIDE_A_NAMESPACE"); ?>');
                    hasError = true;
                } else {
                    re = new RegExp('^[A-Za-z]+[A-Za-z0-9_]*$');
                    if ( !re.test(name.value) ) {
                        rbf_activateError('name', '<?= GT($textMap, "IA.ERROR.ERROR_THE_NAMESPACE_CONTAINS_AN_ILLEGAL_C"); ?>');
                        hasError = true;
                    }
                }

                if ( hasError ) {
                    showInfoMessage('<?= GT($textMap, "IA.ERROR.ERROR_INVALID_DATA_REVIEW_ERROR_MESSAGES"); ?>', true);
                    return false;
                }

                name.value = name.value.toString().toUpperCase();
            }

            return true;
        }

        function rbf_doSave() {
            if ( !checkNamespaceAvailability() ) {
                document.theForm.saveBtn.disabled = true;
                rbf_clearErrors();
                if ( !response ) {
                    rbf_activateError('name', '<?= GT($textMap, "IA.ERROR.ERROR_THE_NAMESPACE_IS_NOT_AVAILABLE"); ?>');
                }

                return false;
            }
            namespace = document.theForm.name.value;
            if ( opener ) {
                opener.assignNamespace(namespace);
            } else {
                parent.assignNamespace(namespace);
            }

            return rbf_doCancel();
        }

        function rbf_doCancel() {
            if ( opener ) {
                window.close();
            } else {
                <? if ( Pt_SetupComponents::$isQuixote ) { ?>
                if ( parent.closeQxDialog ) {
                    parent.closeQxDialog(window.frameElement);
                } else {
                    parent.hidePopWin();
                }
                <? } else { ?>
                parent.hidePopWin();
                <? } ?>
            }

            return true;
        }

        function checkNamespaceAvailability() {
            if ( !rbf_checkInput() ) {
                document.theForm.saveBtn.disabled = true;
                return false;
            }
            jq.ajax({
                        url: '<?= Pt_WebUtil::url(BaseUrl() . 'pt_Ajax.phtml',
                                                  OP_SETUP) ?>&<?= FIELD_CMD ?>=<?= NAMESPACE_CHECK ?>&<?= NAMESPACE_PARAM ?>=' +
                             document.theForm.name.value,
                        type: 'GET',
                        dataType: 'json',
                        headers: { 'X-XSRF_TOKEN': getAjaxCsrfToken() },
                        success: function(response) {
                            document.theForm.saveBtn.disabled = !response;
                            rbf_activateOK('name', '<?= GT($textMap, "IA.NAMESPACE_AVAILABLE"); ?>');
                            if ( !response ) {
                                rbf_activateError('name', '<?= GT($textMap, "IA.NAMESPACE_UNAVAILABLE_TRY_ANOTHER"); ?>');
                            }
                        },
                        error: function(response) {
                            document.theForm.saveBtn.disabled = true;
                            rbf_clearErrors();
                            rbf_activateError('name', '<?= GT($textMap, "IA.ERROR.ERROR_AN_ERROR_OCCURED_PLEASE_RETRY"); ?>');
                        }
                    });

            return true;
        }

        function getAjaxCsrfToken() {
            return '<?= Pt_WebUtil::generateCsrfToken() ?>';
        }
    
    </script>
    
    <form method='post' name='theForm' id='theForm' enctype='multipart/form-data'>
        <?= Pt_WebUtil::hidden(); ?>
        
        <table class="rbs_mainComponentTable" cellpadding=0 cellspacing=0>
            
            <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
            <table class="<?= $rbs_lightsilverTableOrEmpty . $emptyOrSectionClass ?>">
                <tr>
                    <td class='center'>
                        <table>
                            <tr>
                                <td>&nbsp;</td>
                                <td class='rbs_PageTopicWide'><?= GT($textMap, "IA.APPLICATION_NAMESPACE"); ?></td>
                                <td class='rbs_recordActionCol' nowrap>
                                    <input type="submit" id="saveBtn"
                                           class='<?= $boldOrEmpty . $emptyOrBtnPrimaryClass ?>'
                                           value=" <?= GT($textMap, "IA.SAVE"); ?> " onClick="return rbf_doSave()" disabled>&nbsp;&nbsp;
                                    <input type="submit" id="cancelBtn"
                                           class="<?= $emptyOrBtnPrimaryClass ?>"
                                           value=" <?= GT($textMap, "IA.CANCEL"); ?> " onClick="return rbf_doCancel()">&nbsp;&nbsp;
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>
            
            <tr>
                <td>
                    <table height='10' class='wide'>
                        <tr>
                            <td></td>
                        </tr>
                    </table>
                    
                    <?= Pt_WebUtil::getContainerBegin($emptyOrSectionClass); ?>
                    
                    <table class='wide' cellpadding=0 cellspacing=0>
                        <tr>
                            <td id='name_label' class='rbs_rightLabelRequired' nowrap><?= GT($textMap, "IA.ENTER_A_NAMESPACE"); ?></td>
                            <td class='rbs_leftDataColWide' id='name_field'>
                                <input type='text'
                                       class='<?= $emptyOrFormControl ?>' name='name' size="15" maxlength='15'/>
                                <br>
                                <input type="button" class="<?= $emptyOrBtnPrimaryClass ?>" name="checkNamespace"
                                       value="<?= GT($textMap, "IA.CHECK_AVAILABILITY"); ?>"
                                       id="checkNamespace"
                                       onClick='checkNamespaceAvailability()'>
                                <br>
                                <span class='rbs_alertMsg' id='name_error'></span>
                            </td>
                        </tr>
                        <tr>
                        
                        </tr>
                    </table>
                    <?= Pt_WebUtil::getContainerEnd(''); ?>
                    
                    <table height='10' class='wide'>
                        <tr>
                            <td></td>
                        </tr>
                    </table>
                    
                    <table height='10' class='wide'>
                        <tr>
                            <td></td>
                        </tr>
                    </table>
                
                </td>
            </tr>
            
            <?= Pt_SetupComponents::$trHeight10OrEmpty ?>
        </table>
    </form>
    
    <script language='JavaScript'>
        document.theForm.name.focus();
        rbf_addOnLoadMethod(rbf_initializeValidationField('name'));
    </script>
    <?
    include 'pt_setupFooter.inc';
} catch ( Exception $ex ) {
    error($ex);
}
