<?
try {
    include 'pt_setupHeader.inc';

    $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
    $objDef = Pt_DataObjectDefManager::getById($objDefId);
    $title2 = ($objDef!=null ? $objDef->__toString() : "");
    $parentId = http_getIntParameter(FIELD_PARENT_ID);
    $isStandardObj = $objDef instanceof Pt_StdDataObjectDef;

    $view      = http_getParameter(FIELD_VIEW);
    $title     = '';
    $items     = null;
    $returnURL = "pt_objectView.phtml";
    $helpID    = 'About_Platform_Services';

    $tokens = ["IA.CANCEL", "IA.SAVE", "IA.LIST_AND_ADD_MENU_ITEMS_ARE_ALWAYS_DISPLAYED_TO", "IA.REORDER_ACTIONS",
        "IA.REORDER_STATUSES", "IA.REORDER_PROCESSES", "IA.REORDER_VIEWS", "IA.REORDER_MENUS"
        ];
    $placeholderTokens = [];
    $textMap = getIntlTextMap($tokens, $placeholderTokens);

    switch ($view) {
    case 'action':
        $title = GT($textMap, "IA.REORDER_ACTIONS");
        $items = Pt_ActionManager::getByObjectDef($objDefId);
        $helpID = 'Reordering_Workflow_Actions';
        break;

    case 'status':
        $title = GT($textMap, "IA.REORDER_STATUSES");
        $items = Pt_StatusManager::getByObjectDef($objDefId);
        $helpID = 'Reordering_Workflow_Statuses';
        break;

    case 'process':
        $title = GT($textMap, "IA.REORDER_PROCESSES");
        $items = Pt_ProcessManager::getByObjectDef($objDefId);
        $helpID = 'Reordering_Workflow_Processes';
        break;

    case 'view':
        $title = GT($textMap, "IA.REORDER_VIEWS");
        $items = Pt_ListDefManager::getByObjectDef($objDefId);
        $helpID = 'Reordering_Views';
        break;

    case 'menu':
        $title = GT($textMap, "IA.REORDER_MENUS");
        $parentMenu = Pt_MenuManager::getById($parentId);
        $title2 = ($parentMenu!=null ? $parentMenu->__toString() : '');
        $items = Pt_MenuManager::getByParent($parentMenu->getId());
        $hiddenItemsIds = [];
        for ( $i = 0; $i < 2; $i++ ) {
            if ( isset($items[$i]) ) {
                $hiddenItemsIds[] = $items[$i]->getId();
                unset($items[$i]);
            }
        }
        $returnURL = Pt_WebUtil::url('pt_menuView.phtml')."&parentId=" . rawurlencode($parentId);
        break;

    default:
        throw new Pt_I18nException('PAAS-0745', "Unknown view type $view", [ 'VIEW' => "$view" ]);
    }

    $emptyOrSectionClass = Pt_SetupComponents::$emptyOrSectionClass;
    $rbs_lightsilverTableOrEmpty = Pt_SetupComponents::$rbs_lightsilverTableOrEmpty;
    $rbs_roundedTableOrEmpty = Pt_SetupComponents::$rbs_roundedTableOrEmpty;
    $emptyOrBtnPrimaryClass = Pt_SetupComponents::$emptyOrBtnPrimaryClass;
    $boldOrEmpty = Pt_SetupComponents::$boldOrEmpty;
?>

<script language="JavaScript" src="../resources/js/platform/pt_checkBoxes.js"></script>

<INPUT type="hidden" id="hlp" name="hlp" value="<?=$helpID?>">

<table class="wide" cellpadding=0 cellspacing=0>
<tr>
<td>

<script language="JavaScript" src="../resources/js/platform/pt_moveItems.js"></script>

<script language="JavaScript">
function rbf_checkInput() {
    with (document.theForm) {
        var opts = assignedList.options;
        var tmp = "";
        <? if ( isset($hiddenItemsIds) ) { ?>
            tmp = tmp + "<?= implode(',', $hiddenItemsIds) ?>,"; <?
        } ?>
        for (var k=0; k<opts.length; k++) {
            tmp = tmp + opts[k].value+",";
        }
        assigned.value=tmp;

        act.value = '<?=$view?>Reorder';
    }

    return true;
}

</script>

<form action='<?=$returnURL?>' method='post' name='theForm' onSubmit='rbf_disableAllButtons()'>
<?=Pt_WebUtil::hidden()?>
<input type='hidden' name='act' value=''>
<input type='hidden' name='assigned' value=''>
<? if ($objDefId > 0) { ?><input type='hidden' name='objDefId' value='<?=$objDefId?>'><? 
} ?>

<table class="rbs_mainComponentTable" cellpadding=0 cellspacing=0>

<tr>
    <td>

        <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
        <table class="<?= $rbs_lightsilverTableOrEmpty . $emptyOrSectionClass ?>">
            <tr>
                <td class='center'>
                    <table>
                        <tr>
                            <td>&nbsp;</td>
                            <td class='rbs_PageTopicWide'>
                                <?= util_encode($title2) ?>:&nbsp;<?= util_encode($title) ?>&nbsp;&nbsp;
                            </td>
                            <td class='rbs_recordActionCol' nowrap>
                                <input type="submit"
                                       class='<?= $boldOrEmpty . $emptyOrBtnPrimaryClass ?>'
                                       value=" <?= GT($textMap, "IA.SAVE"); ?> " onClick='return rbf_checkInput()'>
                                <input type="submit" class="<?= $emptyOrBtnPrimaryClass ?>"
                                       value=" <?= GT($textMap, "IA.CANCEL"); ?> ">&nbsp;&nbsp;
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>

    <table height='10' class='wide'><tr><td></td></tr></table>

        <?= Pt_WebUtil::getContainerBegin($emptyOrSectionClass); ?>
        <?= Pt_WebUtil::getThinSectionTop($title) ?>

        <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>

            <? if ( $view == 'menu' ) { ?>
                <tr>
                    <td>
                        <table class='wide right' cellspacing=0 cellpadding=0>
                            <tr>
                                <td class='right'>
                                    <table class='<?= Pt_SetupComponents::$rbs_infoMessageOrEmpty
                                                      . Pt_SetupComponents::$emptyOrEditorMessage ?>' cellpadding=0
                                           cellspacing=0>
                                        <?= Pt_SetupComponents::$messageTopOrEmpty ?>
                                        <tr class="<?= Pt_SetupComponents::$emptyOrHelpWarning ?>">
                                            <td nowrap>&nbsp;&nbsp;&nbsp;</td>
                                            <td class='bold'><?= GT($textMap, "IA.LIST_AND_ADD_MENU_ITEMS_ARE_ALWAYS_DISPLAYED_TO"); ?></td>
                                            <?= Pt_SetupComponents::$mesageAfterSpacing ?>
                                        </tr>
                                        <?= Pt_SetupComponents::$messageBottomOrEmpty ?>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            <? } ?>

            <tr height='10'>
                <td></td>
            </tr>

            <tr <?= ( $view == 'menu' ) ? "style='display: block;'" : "" ?> >
                <td width='<?= Pt_SetupComponents::$width120 ?>'></td>
                <td>
                    <table border=0 cellspacing=2 cellpadding=0>
                        <tr>
                            <td>
                                <table border=0 cellspacing=2 cellpadding=0>
                                    <tr>
                                        <td>
                                            <select class='<?= trim(Pt_SetupComponents::$emptyOrFormControl) ?>' size='24'
                                                    name='assignedList' multiple>
                                                <? foreach ( $items as $item ) {
                                                    $itemLabel = $item->__toString();
                                                    echo Pt_WebUtil::getOption($item->getSelectValue(), $itemLabel, null);
                                                } ?>
                                            </select>
                                        </td>
                                        <td class='tall'>
                                            <table class='tall' border=0 cellspacing=0 cellpadding=5>
                                                <tr>
                                                    <td>
                                                        <input class='<?= Pt_SetupComponents::$emptyOrBtnSecondaryClass ?>'
                                                               type="submit" value=" /\ "
                                                               onClick="return moveUp(document.theForm.assignedList);"><br><input
                                                                class='<?= Pt_SetupComponents::$emptyOrBtnSecondaryClass ?>'
                                                                type="submit" value=" \/ "
                                                                onClick="return moveDown(document.theForm.assignedList);">&nbsp;&nbsp;
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

        </table>
        <?= Pt_WebUtil::getContainerEnd('') ?>

    <table height='10' class='wide'><tr><td></td></tr></table>

        <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
        <table class="<?= $rbs_lightsilverTableOrEmpty . $emptyOrSectionClass ?>">
            <tr>
                <td class='center'>
                    <table>
                        <tr>
                            <td>&nbsp;</td>
                            <td class='rbs_PageTopicWide'></td>
                            <td class='rbs_recordActionCol' nowrap>

                                <input type="submit"
                                       class='<?= $boldOrEmpty . $emptyOrBtnPrimaryClass ?>'
                                       value=" <?= GT($textMap, "IA.SAVE"); ?> " onClick='return rbf_checkInput()'>
                                <input type="submit" class="<?= $emptyOrBtnPrimaryClass ?>"
                                       value=" <?= GT($textMap, "IA.CANCEL"); ?> ">&nbsp;&nbsp;
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>

    </td>
</tr>

</form>
</table>

</td>
</tr>

<?= Pt_SetupComponents::$trHeight10OrEmpty ?>
</table>
<?
include 'pt_setupFooter.inc';
}
catch (Exception $ex) {
    error($ex);
}
