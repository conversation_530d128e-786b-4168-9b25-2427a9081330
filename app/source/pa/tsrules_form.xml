<?xml version="1.0" encoding="ISO-8859-1"?>
<ROOT>
    <entity>tsrules</entity>
    <title>IA.TIMESHEET_RULES_INFORMATION</title>
    <view system="true">
        <pages>
            <page id="mainPage" title="IA.RULES" columnCount="1">
                <section>
                    <field>
                        <path>RECORDNO</path>
                    </field>
                    <field>
                        <path>NAME</path>
                    </field>
                    <field hidden="true">
                        <path>BUSINESSDAYS</path>
                    </field>
                    <field hidden="true">
                        <path>WEEKENDS</path>
                    </field>
                    <field>
                        <path>STARTDATE</path>
                        <helpText>IA.TIMESHEET_RULE_VALID_FROM</helpText>
                    </field>
                    <field>
                        <path>ENDDATE</path>
                        <helpText>IA.TIMESHEET_RULE_VALID_TO</helpText>
                    </field>
                    <field>
                        <path>STATUS</path>
                    </field>
                </section>
                <section columnCount="1">
                    <row label="IA.WORK_DAYS">
                        <field rightSideLabel="1">
                            <path>BD_SUNDAY</path>
                            <events change="toggleDays(this, 'WE_SUNDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>BD_MONDAY</path>
                            <events change="toggleDays(this, 'WE_MONDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>BD_TUESDAY</path>
                            <events change="toggleDays(this, 'WE_TUESDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>BD_WEDNESDAY</path>
                            <events change="toggleDays(this, 'WE_WEDNESDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>BD_THURSDAY</path>
                            <events change="toggleDays(this, 'WE_THURSDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>BD_FRIDAY</path>
                            <events change="toggleDays(this, 'WE_FRIDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>BD_SATURDAY</path>
                            <events change="toggleDays(this, 'WE_SATURDAY')"/>
                        </field>
                    </row>
                    <row label="IA.WEEKENDS">
                        <field rightSideLabel="1">
                            <path>WE_SUNDAY</path>
                            <events change="toggleDays(this, 'BD_SUNDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>WE_MONDAY</path>
                            <events change="toggleDays(this, 'BD_MONDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>WE_TUESDAY</path>
                            <events change="toggleDays(this, 'BD_TUESDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>WE_WEDNESDAY</path>
                            <events change="toggleDays(this, 'BD_WEDNESDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>WE_THURSDAY</path>
                            <events change="toggleDays(this, 'BD_THURSDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>WE_FRIDAY</path>
                            <events change="toggleDays(this, 'BD_FRIDAY')"/>
                        </field>
                        <field rightSideLabel="1">
                            <path>WE_SATURDAY</path>
                            <events change="toggleDays(this, 'BD_SATURDAY')"/>
                        </field>
                    </row>
                    <field>
                        <path>MINHOURSPERTSDAY</path>
                        <helpText>IA.MINIMUM_NUMBER_OF_HOURS_PER_DAY_REQUI</helpText>
                    </field>
                    <field>
                        <path>MAXHOURSPERTSDAY</path>
                        <helpText>IA.MAXIMUM_NUMBER_OF_HOURS_PER_DAY_ALLOW</helpText>
                    </field>
                    <field>
                        <path>MINHOURSPERTS</path>
                        <helpText>IA.MINIMUM_NUMBER_OF_HOURS_REQUIRED_ON_T</helpText>
                    </field>
                    <field>
                        <path>MAXHOURSPERTS</path>
                        <helpText>IA.MAXIMUM_NUMBER_OF_HOURS_ALLOWED_ON_TI</helpText>
                    </field>
                    <field>
                        <path>MINHOURSPERWETS</path>
                        <helpText>IA.MINIMUM_NUMBER_OF_HOURS_PER_DAY_WEEKE</helpText>
                    </field>
                    <field>
                        <path>MAXHOURSPERWETS</path>
                        <helpText>IA.MAXIMUM_NUMBER_OF_HOURS_PER_DAY_WEEKE</helpText>
                    </field>
                    <field>
                        <path>REQUIRENOTESONTS</path>
                        <helpText>IA.NOTES_WILL_BE_REQUIRED_FOR_EVERY_TIME</helpText>
                    </field>
                    <field>
                        <path>DONOTALLOWHOLIDAYS</path>
                        <helpText>IA.PREVENT_TIME_ENTRY_ON_HOLIDAYS</helpText>
                    </field>
                </section>
            </page>
            <page id="holidayPage" title="IA.HOLIDAYS">
                <section>
                    <child>
                        <grid minLines="1">
                            <showAdd>true</showAdd>
                            <showDelete>true</showDelete>
                            <readonly>true</readonly>
                            <entity>holidayschedule</entity>
                            <path>HOLIDAYSCHEDULE</path>
                            <title>IA.HOLIDAY_SCHEDULES</title>
                            <column>
                                <field>
                                    <path>NAME</path>
                                    <fullname>IA.NAME</fullname>
                                </field>
                            </column>
                            <column>
                                <link fullname="IA.VIEW" href="javascript:void(0);" path="VIEWSCHEDULE">
                                    <events>
                                        <click>popupHS(this); false;</click>
                                    </events>
                                </link>
                            </column>
                        </grid>
                    </child>
                </section>
            </page>
            <page id="assignmentPage" title="IA.RULE_ASSIGNMENT">
                <section>
                    <child>
                        <field>
                            <path>TSRULESEMPLOYEE</path>
                        </field>
                    </child>
                </section>
            </page>
        </pages>
    </view>
    <helpfile>Adding_Editing_Viewing_Timesheet_Rules</helpfile>
</ROOT>
