<?php

/**
 * Entity for Project GL Summary data
 * 
 * <AUTHOR>
 * @copyright 2000-2019 Intacct Corporation, All Rights Reserved
 */

$kSchemas['projectglsummary'] = array(
    'children' => array(
        'project' => array(
            'fkey' => 'projectkey',
            'invfkey' => 'record#',
            'table' => 'project',
            'join' => 'inner'
        ),
        'entity' => array(
            'fkey' => 'entity#',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'location'
        ),
    ),
    'nexus' => array(
        'project' => array(
            'object' => 'project',
            'relation' => ONE2MANY,
            'field' => 'projectkey'
        ),
        'entity' => array(
            'object' => 'location',
            'relation' => MANY2ONE,
            'field' => 'ENTITY#',
            'printas' => 'IA.ENTITY'
        ),
    ),
    'object' => array(
        'PROJECTKEY',
        'ENTITY#',
        'TOTALPAYMENTS',
        'TOTALDEFERREDREVENUE',
        'TOTALREVENUE',
        'TOTALEXPENSES',
        'TOTALCOST',
        'TOTALWAGES',
        'TOTALGROSSPROFIT',
        'TOTALNETINCOME',
    ),
    'schema' => array(
        'PROJECTKEY' => 'projectkey',
        'ENTITY#' => 'entity#',
        'TOTALPAYMENTS' => 'totalpayments',
        'TOTALDEFERREDREVENUE' => 'totaldeferredrevenue',
        'TOTALREVENUE' => 'totalrevenue',
        'TOTALEXPENSES' => 'totalexpenses',
        'TOTALCOST' => 'totalcost',
        'TOTALWAGES' => 'totalwages',
        'TOTALGROSSPROFIT' => 'totalgrossprofit',
        'TOTALNETINCOME' => 'totalnetincome',
    ),
    'publish' => array(
        'TOTALPAYMENTS',
        'TOTALDEFERREDREVENUE',
        'TOTALREVENUE',
        'TOTALEXPENSES',
        'TOTALCOST',
        'TOTALWAGES',
        'TOTALGROSSPROFIT',
        'TOTALNETINCOME',
    ),
    'fieldinfo' => array(
        array(
            'path' => 'PROJECTKEY',
            'fullname' => 'IA.PROJECT_KEY',
            'type' => array(
                'type' => 'integer',
                'format' => $gRecordNoFormat,
            ),
            'id' => 1
        ),
        array(
            'path' => 'ENTITY#',
            'fullname' => 'IA.ENTITY_NUMBER',
            'type' => array(
                'type' => 'integer',
                'format' => $gRecordNoFormat,
            ),
            'id' => 1
        ),
        array(
            'path' => 'TOTALPAYMENTS',
            'fullname' => 'IA.TOTAL_PAYMENTS',
            'type' => array(
                'ptype' => 'currency',
                'format' => $gCurrencyFormat
            ),
            'id' => 5
        ),
        array(
            'path' => 'TOTALDEFERREDREVENUE',
            'fullname' => 'IA.TOTAL_DEFERRED_REVENUE',
            'type' => array(
                'ptype' => 'currency',
                'format' => $gCurrencyFormat
            ),
            'id' => 6
        ),
        array(
            'path' => 'TOTALREVENUE',
            'fullname' => 'IA.TOTAL_REVENUE',
            'type' => array(
                'ptype' => 'currency',
                'format' => $gCurrencyFormat
            ),
            'id' => 7
        ),
        array(
            'path' => 'TOTALEXPENSES',
            'fullname' => 'IA.TOTAL_EXPENSES',
            'type' => array(
                'ptype' => 'currency',
                'format' => $gCurrencyFormat
            ),
            'id' => 8
        ),
        array(
            'path' => 'TOTALCOST',
            'fullname' => 'IA.TOTAL_COST_OF_GOODS',
            'type' => array(
                'ptype' => 'currency',
                'format' => $gCurrencyFormat
            ),
            'id' => 9
        ),
        array(
            'path' => 'TOTALWAGES',
            'fullname' => 'IA.TOTAL_WAGES',
            'type' => array(
                'ptype' => 'currency',
                'format' => $gCurrencyFormat
            ),
            'id' => 10
        ),
        array(
            'path' => 'TOTALGROSSPROFIT',
            'fullname' => 'IA.GROSS_PROFIT',
            'type' => array(
                'ptype' => 'currency',
                'format' => $gCurrencyFormat
            ),
            'id' => 11
        ),
        array(
            'path' => 'TOTALNETINCOME',
            'fullname' => 'IA.NET_INCOME_LOSS',
            'type' => array(
                'ptype' => 'currency',
                'format' => $gCurrencyFormat
            ),
            'id' => 12
        ),
    ),
    'table' => 'v_projectgltotals',
    'printas' => 'IA.PROJECT_GL_SUMMARY',
    'pluralprintas' => 'IA.PROJECT_GL_SUMMARY',
    'vid' => 'PROJECTKEY',
    'module' => 'pa',
    'dontGenerateQueries' => true,
    'description' => 'IA.PROJECT_GL_DATA_PER_LOCATION_AGGREGATED_ACROSS_DIM',
    'api' => array(
        'PERMISSION_READ_BY_QUERY' => 'NONE',
        'PERMISSION_READ_API' => 'NONE',
        'PERMISSION_READ_BY_NAME' => 'NONE',
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
    ),
);
