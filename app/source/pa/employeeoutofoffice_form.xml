<?xml version="1.0" encoding="ISO-8859-1"?>
<ROOT>
    <entity>employeeoutofoffice</entity>
    <title>IA.OUT_OF_OFFICE_SCHEDULE</title>
    <helpfile>Adding_Editing_Viewing_Out_of_Office</helpfile>
    <view system="true">
    <pages>
        <page>
            <section>
                <field>
                    <path>EMPLOYEEID</path>
                    <events change="checkOutOfOfficeExistence();" />
                </field>
             </section>
             <grid noDragDrop="true">
                 <path>OUTOFOFFICEITEMS</path>
                 <column>
                     <field>
                         <path>STARTDATE</path>
                         <events change='setOutOfOfficeEndDate(this);'/>
                     </field>
                 </column>
                 <column>
                     <field>
                         <path>ENDDATE</path>
                     </field>
                 </column>
                 <column>
                     <field>
                         <path>HOURSPERDAY</path>
                     </field>
                 </column>
                 <column>
                     <field>
                         <path>DESCRIPTION</path>
                     </field>
                 </column>
             </grid>
        </page>
    </pages>
    </view>
</ROOT>
