<?php
/**
 * Entity for the Project Application Setup object
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2013 Intacct Corporation All, Rights Reserved
 */
$kSchemas['pasetup'] = array(
    'fake' => true,
    'schema'    => array(
        'ENABLEPROJRESSCHED'          => 'enableprojressched',
        'TSADMINLEVEL'                => 'tsadminlevel',
        'TSENABLEAPPROVAL'            => 'tsenableapproval',
        'EMAILNOTIFICATIONS'          => 'emailnotifications',
        'NEEDSREAPPROVAL'             => 'needsapproval',
        'ADMINAPPROVER'               => 'adminapprover',
        'TSAPPROVAL1'                 => 'tsapproval_1',
        'TSAPPROVAL2'                 => 'tsapproval_2',
        'TSAPPROVAL3'                 => 'tsapproval_3',
        'TSAPPROVAL4'                 => 'tsapproval_4',
        'TSAPPROVAL5'                 => 'tsapproval_5',
        'TSDAYS'                      => 'tsdays',
        'TSUOM'                       => 'tsuom',
        'TSHOURSINDAY'                => 'tshoursinday',
        'TSGETITEMFROMCOSTTYPE'       => 'tsgetitemfromcosttype',
        'TSSHOWCUSTPROJ'              => 'tsshowcustproj',
        'TSSHOWDEPTLOC'               => 'tsshowdeptloc',
        'TSSHOWTIMETYPE'              => 'tsshowtimetype',
        'TSSHOWLABORCLASS'            => 'tsshowlaborclass',
        'TSLABORCLASSREQUIRED'        => 'tslaborclassrequired',
        'TSSHOWLABORSHIFT'            => 'tsshowlaborshift',
        'TSLABORSHIFTREQUIRED'        => 'tslaborshiftrequired',
        'TSSHOWLABORUNION'            => 'tsshowlaborunion',
        'TSLABORUNIONREQUIRED'        => 'tslaborunionrequired',
        'TSSHOWEMPPOSITION'          => 'tsshowempposition',
        'TSEMPPOSITIONREQUIRED'      => 'tsemppositionrequired',
        'TSALLOWALLTASKS'             => 'tsallowalltasks',
        'MINHOURSPERTS'               => 'minhoursperts',
        'MAXHOURSPERTS'               => 'maxhoursperts',
        'MINHOURSPERTSDAY'            => 'minhourspertsday',
        'MAXHOURSPERTSDAY'            => 'maxhourspertsday',
        'MINHOURSPERWETS'             => 'minhoursperwets',
        'MAXHOURSPERWETS'             => 'maxhoursperwets',
        'REQUIRENOTESONTS'            => 'requirenotesonts',
        'ENABLEEMBEDDEDSIT'           => 'enableembeddedsit',
        'DONOTALLOWHOLIDAYS'          => 'donotallowholidays',
        'VALIDATEPRJ'                 => 'validateprj',
        'VALIDATEEMP'                 => 'validateemp',
        'VALIDATETASK'                => 'validatetask',
        'VALIDATETASK2'               => 'validatetask2',
        'BILLABLE_MODULE'             => 'billable_module',
        'TSBILLUACCT'                 => 'tsbilluacct',
        'TSBILLNUACCT'                => 'tsbillnuacct',
        'TSNONBILLUACCT'              => 'tsnonbilluacct',
        'TSNONBILLNUACCT'             => 'tsnonbillnuacct',
        'TSJOURNAL'                   => 'tsjournal',
        'TSPOSTSPLIT'                 => 'tspostsplit',
        'TIMETYPEEARNINGTYPE'         => 'timetypeearningtype',
        'TSJOURNAL_LABOR'             => 'tsjournal_labor',
        'TSOFFACCT_LABOR'             => 'tsoffacct_labor',
        'TSVARACCT_LABOR'             => 'tsvaracct_labor',
        'TSPOSTSPLIT_LABOR'           => 'tspostsplit_labor',
        'LIMITEDAPPROVALVIEW'         => 'limitedapprovalview',
        'PAYMENTSACCOUNTGROUP'        => 'paymentsaccountgroup',
        'DEFERREDREVENUEACCOUNTGROUP' => 'deferredrevenueaccountgroup',
        'REVENUEACCOUNTGROUP'         => 'revenueaccountgroup',
        'EXPENSESACCOUNTGROUP'        => 'expensesaccountgroup',
        'COSTACCOUNTGROUP'            => 'costaccountgroup',
        'WAGESACCOUNTGROUP'           => 'wagesaccountgroup',
        'GROSSPROFITACCOUNTGROUP'     => 'grossprofitaccountgroup',
        'NETINCOMEACCOUNTGROUP'       => 'netincomeaccountgroup',
        'ALLOWDUPLICATES'             => 'allowduplicates',
        'ALLOWLINEDUPLICATES'         => 'allowlineduplicates',
        'BILLINGDATESOURCE'           => 'billingdatesource',
        'PROJSEQUENCEID'              => 'projsequenceid',
        'TASKSEQUENCEID'              => 'tasksequenceid',
        'PROJECTCONTRACT_SEQUENCEID'  => 'projectcontract_sequenceid',
        'MODULE_CONFIGURED'           => 'module_configured',
        'TSGLFREQUENCY'               => 'tsglfrequency',
        'CLASSASTASKDIMENSION'        => 'classastaskdimension',
        'REQUIREGLACCOUNT_COSTTYPE'   => 'requireglaccount_costtype',
        'REQUIREITEM_COSTTYPE'        => 'requireitem_costtype',
        'AUTOPOPULATECLASSFORTASK'    => 'autopopulateclassfortask',
        'CLASSREQUIREDFORTASK'        => 'classrequiredfortask',
        'AUTORESTARTGENINVOICE'       => 'autorestartgeninvoice',
        'TRIMTASKOVERRUNS'            => 'trimtaskoverruns',
        'SUMMARYCAPTIONS'             => 'summarycaptions',
        'ALLOWGENINVOICEINACTIVEDIM'  => 'allowgeninvoiceinactivedim',
        'TSENTRYFIRSTFIELD'           => 'tsentryfirstfield',
        'TSREQUIRENOTESWHEN'          => 'tsrequirenoteswhen',
        'IGNOREUSERRESTRICTIONS'      => 'ignoreuserrestrictions',
        'BILLINGRATESOURCE'           => 'billingratesource',
        'LABORCOSTRATESOURCE'         => 'laborcostratesource',
        'DONOTLOGHISTORICALREVRECINFO'=> 'donotloghistoricalrevrecinfo',
        'RESOURCEDATESDEFAULTBLANK'   => 'resourcedatesdefaultblank',
        'ENABLEPROJBILLING'           => 'enableprojbilling',
        'AUTODEFAULTNOTES'            => 'autodefaultnotes',
        'SUPPRESSBILLABLEHOURSINAPPROVALS' => 'suppressbillablehoursinapprovals',
        'TSWARNONCLOSEPERIOD'         => 'timesheetdates',
        'DEFAULT_GLBUDGETID'          => 'default_glbudgetid',
        'PJESTIMATE_SEQUENCEID'         => 'pjestimate_sequenceid',
        'PJESTIMATE_ALLOW_ACCT_OVERRIDE' => 'pjestimate_allow_acct_override',
        'CHANGEREQUEST_SEQUENCEID'          => 'changerequest_sequenceid',
        'CHANGEREQUEST_ALLOW_ACCT_OVERRIDE' => 'changerequest_allow_acct_override',
        'PROJECTCHANGEORDER_SEQUENCEID'     => 'projectchangeorder_sequenceid',
        'ENABLE_GRANT_FIELDS'         => 'enable_grant_fields',
        'BILLABLEFIELDCAPTION'         => 'billablefieldcaption',
        'WIP_COST_ACCTS'              => 'wip_cost_accts',
        'WIP_REVENUE_ACCTS'           => 'wip_revenue_accts',
        'WIP_OVERBILLING_ACCT'        => 'wip_overbilling_acct',
        'WIP_UNDERBILLING_ACCT'       => 'wip_underbilling_acct',
        'WIP_OFFSET_ACCT'             => 'wip_offset_acct',
        'WIP_OVERBILLING_OFFSET_ACCT' => 'wip_overbilling_offset_acct',
        'WIP_UNDERBILLING_OFFSET_ACCT'=> 'wip_underbilling_offset_acct',
        'WIP_POSTING_JOURNAL'         => 'wip_posting_journal',
        'WIP_RECONCILE_METHOD'        => 'wip_reconcile_method',
        'WIP_PM_FORECAST_DETAIL_LEVEL'=> 'wip_pm_forecast_detail_level',
        'WIP_AUTOCREATE_NEXT_PERIOD'  => 'wip_autocreate_next_period',
        'PS_SHOWBUDGETCOLUMN'           => 'ps_showbudgetcolumn',
        'PS_DEFAULTGLBUDGETID'          => 'ps_defaultglbudgetid',
        'PS_DEFAULTACCTGROUPFORBUDGET'  => 'ps_defaultacctgroupforbudget',
        'PS_SHOWBUDGETDIFFCOLUMN'       => 'ps_showbudgetdiffcolumn',
        'PS_BUDGETCOMPCALC'             => 'ps_budgetcompcalc',

        'PROJECT_SUMMARY_TAB'           => 'project_summary_tab',
        'PST_FINANCIAL_SUMMARY'         => 'pst_financial_summary',
        'PST_PROJECTS_PROJECTION'       => 'pst_projects_projection',
        'PST_HOURS'                     => 'pst_hours',
        'PST_EMPLOYEES'                 => 'pst_employees',
        'RESOURCES_AND_PRICING_TAB'     => 'resources_and_pricing_tab',
        'INVOICE_OPTIONS_TAB'           => 'invoice_options_tab',
        'TASKS_TAB'                     => 'tasks_tab',
        'ESTIMATES_TAB'                 => 'estimates_tab',
        'PURCHASING_COMMITMENTS_TAB'    => 'purchasing_commitments_tab',
        'CHANGE_MANAGEMENT_TAB'         => 'change_management_tab',
        'TIMESHEETSYNC'                 => 'timesheet_sync',
        'PROJECT_CONTRACTS_TAB'         => 'project_contracts_tab',
        'PROJECT_INVOICES_TAB'          => 'project_invoices_tab',

        'ENABLE_WIP_RELIEF'             => 'enable_wip_relief',
        'WIPRELIEFJOURNAL'              => 'wipreliefjournal',
        'WIPACCT1KEY' => 'wipacct1key',
        'WIPACCT2KEY' => 'wipacct2key',
        'WIPACCT3KEY' => 'wipacct3key',
        'WIPACCT4KEY' => 'wipacct4key',
        'WIPACCT5KEY' => 'wipacct5key',
        'WIPRELIEFACCT1KEY' => 'wipreliefacct1key',
        'WIPRELIEFACCT2KEY' => 'wipreliefacct2key',
        'WIPRELIEFACCT3KEY' => 'wipreliefacct3key',
        'WIPRELIEFACCT4KEY' => 'wipreliefacct4key',
        'WIPRELIEFACCT5KEY' => 'wipreliefacct5key',
    ),
    'valueTranslateFields' => [
        'TSAPPROVAL1',
        'TSAPPROVAL2',
        'TSAPPROVAL3',
        'TSAPPROVAL4',
        'TSAPPROVAL5',
    ],
    'fieldinfo' => array(
        array (
            'path'     => 'ENABLEPROJRESSCHED',
            'fullname' => 'IA.ENABLE_PROJECT_AND_RESOURCE_MANAGEMENT',
            'type'     => $gBooleanType,
            // [AG, 8/4/2016] leave commented out until billing can actually happen
            //'helpText'  => 'IA.BY_ENABLING_THIS_APPLICATION_YOU_MAY_BE_CHARGED',
        ),
        array(
            'path'            => 'TSADMINLEVEL',
            'fullname'        => 'IA.MANAGE_FROM_THE',
            'type'            => array(
                'ptype'       => 'radio',
                'type'        => 'radio',
                'validlabels' => array('IA.TOP_LEVEL', 'IA.ENTITY'),
                'validvalues' => array('R', 'E'),
            ),
            'showlabelalways' => true,
        ),
        array(
            'path'            => 'TSSHOWCUSTPROJ',
            'fullname'        => 'IA.TRACK_TIME_BY',
            'type'            => array(
                'ptype'       => 'radio',
                'type'        => 'radio',
                'validlabels' => array( 'IA.CUSTOMER_AND_ITEM', 'IA.PROJECT_AND_ITEM', 'IA.PROJECT_AND_TASK', 'IA.CUSTOMER_PROJECT_AND_TASK' ),
                'validvalues' => array(
                    'CI',
                    'PI',
                    'PT',
                    'CPT'
                ),
            ),
            'showlabelalways' => true,
        ),
        array(
            'path'            => 'TSDAYS',
            'fullname'        => 'IA.TIMESHEET_DURATION',
            'type' => array(
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => array('IA.DAILY', 'IA.WEEKLY', 'IA.BI_WEEKLY', 'IA.FIRSTST_15TH_16TH_END_OF_THE_MONTH'),
                'validvalues' => array('1', '7', '14', '15'),
            ),
            'showlabelalways' => true,
            'default' => 7,
        ),
        array(
            'path'            => 'TSUOM',
            'fullname'        => 'IA.UNITS',
            'required'        => true,
            'type'            => array(
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => array('IA.SELECT', 'IA.HOURS_AND_MINUTES', 'IA.DAYS'),
                'validvalues' => array('', 'HM', 'D'),
            ),
            'showlabelalways' => true,
        ),
        array(
            'path'     => 'TSHOURSINDAY',
            'fullname' => 'IA.NUMBER_OF_WORKDAY_HOURS',
            'nocalc'   => true,
            'required' => true,
            'type'     => array(
                'ptype' => 'decimal',
                'type'  => 'decimal',
            ),
        ),
        array(
            'path'     => 'NUMOFHOURS_LABEL',
            'fullname' => '',
            'readonly' => true,
            'type' => array(
                'ptype' => 'text',
                'type'  => 'text',
            ),
        ),
        array(
            'path'     => 'TSGETITEMFROMCOSTTYPE',
            'fullname' => 'IA.RETRIEVE_ITEM_FROM_COST_TYPE',
            'type'     => $gBooleanType,
            'hidden'   => true, // will be shown conditionally
        ),
        array(
            'path'     => 'NUMOFHOURS_MIN',
            'fullname' => 'IA.MINIMUM',
            'nocalc'   => true,
            'type'     => array(
                'ptype'     => 'decimal',
                'type'      => 'decimal',
                'maxlength' => 5,
            ),
        ),
        array(
            'path'     => 'NUMOFHOURS_MAX',
            'fullname' => 'IA.MAXIMUM',
            'nocalc'   => true,
            'type'     => array(
                'ptype'     => 'decimal',
                'type'      => 'decimal',
                'maxlength' => 5,
            ),
        ),
        array(
            'path'     => 'ENABLEEMBEDDEDSIT',
            'fullname' => 'IA.ENABLE_SIT_EMBEDDING',
            'hidden'   => true,
            'type'     => $gBooleanType,
        ),
        array(
            'path'     => 'EMBEDDEDSITPROVISIONED',
            'fullname' => 'IA.ENABLE_SIT_EMBEDDING',
            'hidden'   => true,
            'type'     => $gBooleanType,
        ),
        array(
            'path'     => 'TIMESHEETSYNC',
            'fullname' => 'IA.ENABLE_TIMESHEET_SYNC',
            'type'     => $gBooleanType,
        ),
        array(
            'path'     => 'DONOTALLOWHOLIDAYS',
            'fullname' => 'IA.DISABLE_HOLIDAY_ENTRIES',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
        ),
        array(
            'path'     => 'REQUIRENOTESONTS',
            'fullname' => 'IA.REQUIRE_NOTES',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
        ),
        array(
            'path'     => 'VALIDATEPRJ',
            'fullname' => 'IA.PROJECT',
            'type'     => $gBooleanType,
        ),
        array(
            'path'     => 'VALIDATEEMP',
            'fullname' => 'IA.EMPLOYEE',
            'type'     => $gBooleanType,
        ),
        array(
            'path'     => 'VALIDATETASK',
            'fullname' => 'IA.BEFORE_OR_EQUAL_TO_THE_END_DATE_OF_THE_TASK_RESOUR',
            'type'     => $gBooleanType,
        ),
        array(
            'path'     => 'VALIDATETASK2',
            'fullname' => 'IA.EQUAL_TO_OR_AFTER_THE_START_DATE_OF_THE_TASK_RESOU',
            'type'     => $gBooleanType,
        ),
        array(
            'path'     => 'ALLOWDUPLICATES',
            'fullname' => 'IA.MY_TIMESHEETS',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
        ),
        array(
            'path'     => 'ALLOWLINEDUPLICATES',
            'fullname' => 'IA.LINES_IN_A_TIMESHEET',
            'type'     => array(
                'ptype'         => 'boolean',
                'type'          => 'boolean',
                'validvalues'   => array('true', 'false'),
                '_validivalues' => array('true', 'false')
            ),
        ),
        array(
            'path'     => 'TSSHOWDEPTLOC',
            'fullname' => 'IA.SHOW_DEPARTMENTS_AND_LOCATIONS',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
        ),
        array(
            'path'     => 'TSSHOWTIMETYPE',
            'fullname' => 'IA.SHOW_TIME_TYPE',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
        ),
        array(
            'path'     => 'TSSHOWLABORCLASS',
            'fullname' => 'IA.USE_LABOR_CLASS',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
            'hidden' => true,
        ),
        array(
            'path'     => 'TSLABORCLASSREQUIRED',
            'fullname' => 'IA.REQUIRED',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
            'hidden' => true,
        ),
        array(
            'path'     => 'TSSHOWLABORSHIFT',
            'fullname' => 'IA.USE_LABOR_SHIFT',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
            'hidden' => true,
        ),
        array(
            'path'     => 'TSLABORSHIFTREQUIRED',
            'fullname' => 'IA.REQUIRED',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
            'hidden' => true,
        ),
        array(
            'path'     => 'TSSHOWLABORUNION',
            'fullname' => 'IA.USE_LABOR_UNION',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
            'hidden' => true,
        ),
        array(
            'path'     => 'TSLABORUNIONREQUIRED',
            'fullname' => 'IA.REQUIRED',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
            'hidden' => true,
        ),
        array(
            'path'     => 'TSSHOWEMPPOSITION',
            'fullname' => 'IA.USE_EMPLOYEE_POSITION',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
            'hidden' => true,
        ),
        array(
            'path'     => 'TSEMPPOSITIONREQUIRED',
            'fullname' => 'IA.REQUIRED',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
            'hidden' => true,
        ),
        array(
            'path'            => 'TSALLOWALLTASKS',            
            'fullname'        => 'IA.USER_RESTRICTIONS',
            'type'            => array(
                'ptype'       => 'radio',
                'type'        => 'radio',
                'validlabels' => array( 'IA.ANY_USER', 'IA.ONLY_USERS_ASSIGNED_TO_THE_PROJECT', 'IA.ONLY_USERS_ASSIGNED_TO_THE_PROJECT_AND_TASK' ),
                'validvalues' => array(
                    'P',
                    'true',
                    'false'
                ),
            ),
            'showlabelalways' => true,
        ),
        array(
            'path'            => 'TSGLFREQUENCY',
            'fullname'        => 'IA.GL_SUMMARY_FREQUENCY',
            'type' => array(
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => array('IA.TIMEPERIOD_ONLY', 'IA.TIMEPERIOD_AND_EMPLOYEE'),
                'validvalues' => array('T', 'E'),
            ),
            'showlabelalways' => true,
            'default' => 'T',
        ),
        array(
            'path'     => 'TSENABLEAPPROVAL',
            'fullname' => 'IA.ENABLE_APPROVAL',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
        ),
        array(
            'path'     => 'EMAILNOTIFICATIONS',
            'fullname' => 'IA.EMAIL_NOTIFICATION',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
        ),
        array(
            'path'     => 'LIMITEDAPPROVALVIEW',
            'fullname' => 'IA.LIMIT_APPROVAL',
            'type'     => $gBooleanType,
        ),
        array(
            'path'     => 'ADMINAPPROVER',
            'fullname' => 'IA.UNRESTRICTED',
            'type'     => array(
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'timesheetadminapproverpick',
                'maxlength' => 200,
                'pick_url'  => 'picker.phtml?',
            ),
        ),
        array(
            'path'     => 'TSAPPROVAL1',
            'fullname' => 'IA.FIRST',
            'type'     => array(
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'timesheetapproverpick',
                'maxlength' => 200,
            ),
        ),
        array(
            'path'     => 'TSAPPROVAL2',
            'fullname' => 'IA.SECOND',
            'type'     => array(
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'timesheetapproverpick',
                'maxlength' => 200,
            ),
        ),
        array(
            'path'     => 'TSAPPROVAL3',
            'fullname' => 'IA.THIRD',
            'type'     => array(
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'timesheetapproverpick',
                'maxlength' => 200,
            ),
        ),
        array(
            'path'     => 'TSAPPROVAL4',
            'fullname' => 'IA.FOURTH',
            'type'     => array(
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'timesheetapproverpick',
                'maxlength' => 200,
            ),
        ),
        array(
            'path'     => 'TSAPPROVAL5',
            'fullname' => 'IA.FIFTH',
            'type'     => array(
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'timesheetapproverpick',
                'maxlength' => 200,
            ),
        ),
        array (
            'path'     => 'ENABLEPROJBILLING',
            'fullname' => 'IA.ENABLE_PROJECT_BILLING',
            'type'     => $gBooleanType,
            // [AG, 8/4/2016] leave commented out until billing can actually happen
            //'helpText'  => 'IA.BY_ENABLING_THIS_APPLICATION_YOU_MAY_BE_CHARGED',
        ),
        array(
            'path'     => 'BILLINGDATESOURCE',
            'fullname' => 'IA.DETERMINE_BILLING_RATE_FROM',
            'type'     => array(
                'ptype'       => 'radio',
                'type'        => 'radio',
                'validlabels' => array('IA.THE_INVOICE_DATE_FROM_GENERATE_INVOICES', 'IA.ACTUAL_DATE_OF_WORK'),
                'validvalues' => array('I', 'T'),
            ),
            'layout'   => 'landscape',
            'default'  => 'I',
            'showlabelalways' => true,
        ),
        array (
            'path'     => 'PROJSEQUENCEID',
            'fullname' => 'IA.PROJECT_ID_SEQUENCE',
            'type'     => array (
                'ptype'     => 'ptr',
                'entity'    => 'seqnum',
                'type'      => 'ptr',
                'maxlength' => 20,
            ),
        ),
        array (
            'path'     => 'TASKSEQUENCEID',
            'fullname' => 'IA.TASK_ID_SEQUENCE',
            'type'     => array (
                'ptype'     => 'ptr',
                'entity'    => 'seqnum',
                'type'      => 'ptr',
                'maxlength' => 20,
            ),
        ),
        array (
            'path'     => 'PROJECTCONTRACT_SEQUENCEID',
            'fullname' => 'IA.PROJECT_CONTRACT_ID_SEQUENCE',
            'type'     => array (
                'ptype'     => 'ptr',
                'entity'    => 'seqnum',
                'type'      => 'ptr',
                'maxlength' => 20,
            ),
        ),
        array(
            'path'     => 'BILLABLE_MODULE',
            'fullname' => 'IA.DISPLAY_THE_BILLABLE_OPTION_IN',
            'type'     => array(
                'type'        => 'multipick',
                'ptype'       => 'multipick',
                'validlabels' => array( 'IA.ACCOUNTS_PAYABLE_BILLS', 'IA.EMPLOYEE_EXPENSES', 'IA.PURCHASING_TRANSACTIONS', 'IA.TIMESHEET_ENTRIES', 'IA.GENERAL_LEDGER_TRANSACTIONS', 'IA.CREDIT_CARD_TRANSACTIONS' ),
                'validvalues' => array(
                    'ap',
                    'ee',
                    'po',
                    'pa',
                    'gl',
                    'cm'
                ),
                'delimiter' => '#~#',
            ),
            'rowcount' => 4,
        ),
        array(
            'path'     => 'CONVERSIONOPTION',
            'fullname' => ' ',
            'isHTML'   => true,
            'type'     => array(
                'ptype' => 'textlabel',
                'type'  => 'textlabel',
            ),
        ),
        array(
            'path'     => 'TSJOURNAL_DISP',
            'fullname' => 'IA.STATISTICAL',
            'type'     => array(
                'type'      => 'ptr',
                'ptype'     => 'ptr',
                'entity'    => 'statjournal',
                'maxlength' => 106,
            ),
        ),
        array(
            'path'     => 'ACCOUNTS_LABEL',
            'fullname' => '',
            'readonly' => true,
            'type' => array(
                'ptype' => 'text',
                'type'  => 'text',
            ),
        ),
        array(
            'path'     => 'ACCOUNTS_U',
            'fullname' => 'IA.UTILIZED',
            'type'     => array(
                'type'       => 'ptr',
                'ptype'      => 'ptr',
                'pickentity' => 'stataccountpick',
                'entity'     => 'stataccount',
                'maxlength'  => 106
            ),
        ),
        array(
            'path'     => 'ACCOUNTS_NU',
            'fullname' => 'IA.NONUTILIZED',
            'type'     => array(
                'type'       => 'ptr',
                'ptype'      => 'ptr',
                'pickentity' => 'stataccountpick',
                'entity'     => 'stataccount',
                'maxlength'  => 106
            ),
        ),
        array(
            'path'            => 'TSPOSTSPLIT',
            'fullname'        => 'IA.STATISTICAL',
            'type'            => array(
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => array('IA.BY_ACCOUNTING_PERIOD', 'IA.BY_CALENDAR_PERIOD', 'IA.NO_SPLIT'),
                'validvalues' => array('A', 'C', 'N'),
            ),
            'showlabelalways' => true,
        ),
        array(
            'path'     => 'TIMETYPEEARNINGTYPE',
            'fullname' => 'IA.ENABLE_EARNING_TYPE_AT_TIME_TYPE_AND_TASK_LEVEL',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
        ),
        array(
            'path'     => 'TSJOURNAL_LABOR_DISP',
            'fullname' => 'IA.LABOR_COST',
            'type'     => array(
                'type'       => 'ptr',
                'ptype'      => 'ptr',
                'entity'     => 'journal',
                'pickentity' => 'gljournalpick',
                'maxlength'  => 106
            ),
        ),
        array(
            'path'     => 'TSOFFACCT_LABOR_DISP',
            'fullname' => 'IA.LABOR_COST_OFFSET',
            'type'     => array(
                'type'       => 'ptr',
                'ptype'      => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity'     => 'glaccount',
                'maxlength'  => 106
            ),
        ),
        array(
            'path'     => 'TSVARACCT_LABOR_DISP',
            'fullname' => 'IA.VARIANCE',
            'type'     => array(
                'type'       => 'ptr',
                'ptype'      => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity'     => 'glaccount',
                'maxlength'  => 106
            ),
        ),
        array(
            'path'            => 'TSPOSTSPLIT_LABOR',
            'fullname'        => 'IA.LABOR',
            'type'            => array(
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => array('IA.BY_ACCOUNTING_PERIOD', 'IA.BY_CALENDAR_PERIOD', 'IA.NO_SPLIT'),
                'validvalues' => array('A', 'C', 'N'),
            ),
            'showlabelalways' => true,
        ),
        array(
            'path'     => 'PAYMENTSACCOUNTGROUP',
            'fullname' => 'IA.PAYMENTS',
            'type'     => array(
                'ptype'      => 'ptr',
                'pickentity' => 'acctgrppick',
                'entity'     => 'glacctgrp',
            ),
            'nonew'    => true,
            'noview'   => true,
        ),
        array(
            'path'     => 'DEFERREDREVENUEACCOUNTGROUP',
            'fullname' => 'IA.DEFERRED_REVENUE',
            'type'     => array(
                'ptype'      => 'ptr',
                'pickentity' => 'acctgrppick',
                'entity'     => 'glacctgrp',
            ),
            'nonew'    => true,
            'noview'   => true,
        ),
        array(
            'path'     => 'REVENUEACCOUNTGROUP',
            'fullname' => 'IA.REVENUE',
            'type'     => array(
                'ptype'      => 'ptr',
                'pickentity' => 'acctgrppick',
                'entity'     => 'glacctgrp',
            ),
            'nonew'    => true,
            'noview'   => true,
        ),
        array(
            'path'     => 'EXPENSESACCOUNTGROUP',
            'fullname' => 'IA.EXPENSES',
            'type'     => array(
                'ptype'      => 'ptr',
                'pickentity' => 'acctgrppick',
                'entity'     => 'glacctgrp',
            ),
            'nonew'    => true,
            'noview'   => true,
        ),
        array(
            'path'     => 'COSTACCOUNTGROUP',
            'fullname' => 'IA.COST',
            'type'     => array(
                'ptype'      => 'ptr',
                'pickentity' => 'acctgrppick',
                'entity'     => 'glacctgrp',
            ),
            'nonew'    => true,
            'noview'   => true,
        ),
        array(
            'path'     => 'WAGESACCOUNTGROUP',
            'fullname' => 'IA.WAGES',
            'type'     => array(
                'ptype' => 'ptr',
                'pickentity' => 'acctgrppick',
                'entity' => 'glacctgrp',
            ),
            'nonew'    => true,
            'noview'   => true,
        ),
        array(
            'path'     => 'GROSSPROFITACCOUNTGROUP',
            'fullname' => 'IA.GROSS_PROFITS',
            'type'     => array(
                'ptype'      => 'ptr',
                'pickentity' => 'acctgrppick',
                'entity'     => 'glacctgrp',
            ),
            'nonew'    => true,
            'noview'   => true,
        ),
        array(
            'path'     => 'NETINCOMEACCOUNTGROUP',
            'fullname' => 'IA.NET_INCOME_LOSS',
            'type'     => array(
                'ptype'      => 'ptr',
                'pickentity' => 'acctgrppick',
                'entity'     => 'glacctgrp',
            ),
            'nonew'    => true,
            'noview'   => true,
        ),
        array(
            'path'     => 'NEEDSREAPPROVAL',
            'fullname' => 'IA.MODIFICATION_REQUIRES_REAPPROVAL',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
        ),
        array(
            'path'     => 'MODULE_CONFIGURED',
            'fullname' => 'IA.MODULE_CONFIGURED',
            'type'     => array(
                'ptype'         => 'enum',
                'type'          => 'boolean',
                'validvalues'   => array('T', 'F'),
                '_validivalues' => array('T', 'F'),
            ),
            'default'  => 'T',
        ),
        array(
            'path'     => 'CLASSASTASKDIMENSION',
            'fullname' => 'IA.ASSOCIATE_CLASS_WITH_A_TASK',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
            'default' => 'false',
            'renameable' => true,
        ),
        array(
            'path'     => 'AUTOPOPULATECLASSFORTASK',
            'fullname' => 'IA.AUTOPOPULATE_THE_CLASS_FIELD_WITH_THE_TASK_NAME_DU',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
            'default' => 'false',
            'renameable' => true,
        ),
        array(
            'path'     => 'CLASSREQUIREDFORTASK',
            'fullname' => 'IA.CLASS_IS_REQUIRED_FOR_EVERY_TASK',
            'type'     => array(
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => array('true', 'false'),
            ),
            'default' => 'false',
            'renameable' => true,
        ),
        array(
            'path'     => 'AUTORESTARTGENINVOICE',
            'fullname' => 'IA.AUTORESTART_THE_GENERATE_INVOICE_PROCESS_WHEN_LIMI',
            'type'     => $gBooleanType,
            'default' => 'false',
        ),
        array(
            'path'     => 'TRIMTASKOVERRUNS',
            'fullname' => 'IA.TRIM_TASK_OVERRUNS_FOR_PROJECT_LEVEL_REVENUE_RECOG',
            'type'     => $gBooleanType,
            'default' => 'false',
        ),
        array(
            'path'     => 'SUMMARYCAPTIONS',
            'fullname' => 'IA.USE_ACCOUNT_GROUP_DISPLAY_AS_NAMES_INSTEAD_OF_AB',
            'type'     => $gBooleanType,
            'default' => 'false',
        ),
        array(
            'path'     => 'ALLOWGENINVOICEINACTIVEDIM',
            'fullname' => 'IA.ENABLE_INVOICING_FOR_INACTIVE_DIMENSIONS',
            'type'     => $gBooleanType,
            'default' => 'false',
        ),
        array(
            'path'      => 'TSENTRYFIRSTFIELD',
            'fullname'  => '',
            'type'      => array(
                'ptype'         => 'radio',
                'type'          => 'radio',
                'validlabels' => array( 'IA.SHOW_DESCRIPTION_FIRST', 'IA.SHOW_NOTES_FIRST', ),
                'validvalues'   => array('DESC','NOTE'),
            ),
            'default'   => 'DESC',
        ),
        array(
            'path'      => 'TSREQUIRENOTESWHEN',
            'fullname'  => '',
            'type'      => array(
                'ptype'         => 'radio',
                'type'          => 'radio',
                'validlabels' => array( 'IA.FOR_ALL_LINE_ITEMS', 'IA.ONLY_FOR_BILLABLE_LINE_ITEMS', ),
                'validvalues'   => array('ALL','BILLABLE'),
            ),
            'default'   => 'ALL',
        ),
        array(
            'path'      => 'AUTODEFAULTNOTES',
            'fullname'  => 'IA.AUTO_DEFAULT_NOTES_TO',
            'type'      => array(
                'ptype'         => 'radio',
                'type'          => 'radio',
                'validlabels'   => array(
                                    'IA.TASK_DESCRIPTION',
                                    //_('Entry date + task description'),  <- commenting out for now, will be implemented later
                                    'IA.TASK_WBS_CODE',
                                    'IA.DO_NOT_DEFAULT_NOTES',
                                ),
                'validvalues'   => array(
                                    'TD',
                                    //'ED_TD',  <- commenting out for now, will be implemented later
                                    'WBS',
                                    'ND'),
            ),
            'default'   => 'ND',
            'showlabelalways' => true,
        ),
        array (
            'path'        => 'IGNOREUSERRESTRICTIONS',
            'fullname'    => 'IA.VALIDATE_ONLY_WHEN_TIMESHEET_OR_EXPENSE_IS_SAVED',
            'type'        =>  $gBooleanType,
            'default'    => 'false',
            'helpText'  => 'IA.USEFUL_FOR_COMPANIES_WITH_LARGE_NUMBERS_OF',
        ),
        array (
            'path'        => 'BILLINGRATESOURCE',
            'fullname'    => 'IA.OBTAIN_BILLING_RATE_FROM',
            'type'      => array(
                'ptype'         => 'radio',
                'type'          => 'radio',
                'validlabels' => array( 'IA.INTACCT', 'IA.TIMESHEET_IMPORTED_VIA_THE_API', ),
                'validvalues'   => array('INT','EXT'),
            ),
            'layout'     => 'landscape',
            'showlabelalways' => true,
            'default'   => 'INT',
        ),
        array (
            'path'        => 'LABORCOSTRATESOURCE',
            'fullname'    => 'IA.OBTAIN_LABOR_COST_RATE_FROM',
            'type'      => array(
                'ptype'         => 'radio',
                'type'          => 'radio',
                'validlabels' => array( 'IA.INTACCT', 'IA.TIMESHEET_IMPORTED_VIA_THE_API', ),
                'validvalues'   => array('INT','EXT'),
            ),
            'layout'     => 'landscape',
            'showlabelalways' => true,
            'default'   => 'INT',
        ),
        array(
            'path'     => 'DONOTLOGHISTORICALREVRECINFO',
            'fullname' => 'IA.SKIP_UPDATING_THE_REV_REC_SCHEDULE_ENTRY_TASK_HIST',
            'type'     => $gBooleanType,
            'default'  => 'false',
        ),
        array(
            'path'       => 'RESOURCEDATESDEFAULTBLANK',
            'fullname'   => 'IA.LEAVE_START_AND_END_DATES_BLANK_IN_RESOURCE_SCHEDU',
            'type'       => $gBooleanType,
        ),
        array(
            'path'      => 'MINHOURSPERTS',
            'fullname'  => 'IA.MINIMUM_HOURS_PER_TIMESHEET',
            'readonly'  => true,
            'showaudit' => true,
            'type' => array( 'ptype' => 'text', 'type'  => 'text'),
        ),
        array(
            'path'      => 'MAXHOURSPERTS',
            'fullname'  => 'IA.MAXIMUM_HOURS_PER_TIMESHEET',
            'readonly'  => true,
            'showaudit' => true,
            'type' => array( 'ptype' => 'text', 'type'  => 'text'),
        ),
        array(
            'path'      => 'MINHOURSPERTSDAY',
            'fullname'  => 'IA.MINIMUM_HOURS_PER_WEEKDAY',
            'readonly'  => true,
            'showaudit' => true,
            'type' => array( 'ptype' => 'text', 'type'  => 'text'),
        ),
        array(
            'path'      => 'MAXHOURSPERTSDAY',
            'fullname'  => 'IA.MAXIMUM_HOURS_PER_WEEKDAY',
            'readonly'  => true,
            'showaudit' => true,
            'type' => array( 'ptype' => 'text', 'type'  => 'text'),
        ),
        array(
            'path'      => 'MINHOURSPERWETS',
            'fullname'  => 'IA.MINIMUM_HOURS_PER_WEEKEND_DAY',
            'readonly'  => true,
            'showaudit' => true,
            'type' => array( 'ptype' => 'text', 'type'  => 'text'),
        ),
        array(
            'path'      => 'MAXHOURSPERWETS',
            'fullname'  => 'IA.MAXIMUM_HOURS_PER_WEEKEND_DAY',
            'readonly'  => true,
            'showaudit' => true,
            'type' => array( 'ptype' => 'text', 'type'  => 'text'),
        ),
        array(
            'path'     => 'SUPPRESSBILLABLEHOURSINAPPROVALS',
            'fullname' => 'IA.HIDE_BILLABLE_HOURS_COLUMNS_ON_APPROVE_TIMESHEETS',
            'type'     => $gBooleanType,
        ),
        array(
            'path'      => 'TSWARNONCLOSEPERIOD',
            'fullname'  => 'IA.IF_TIMESHEET_DATES_FALL_WITHIN_A_CLOSED_PERIOD',
            'type'      => array(
                'ptype'         => 'radio',
                'type'          => 'radio',
                'validlabels' => array( 'IA.AUTOMATICALLY_POST_TO_THE_NEXT_OPEN_PERIOD', 'IA.WARN_BEFORE_POSTING_TO_THE_NEXT_OPEN_PERIOD', 'IA.DO_NOT_ALLOW_ENTRY','IA.ALLOW_ENTRY_OF_TIMESHEET_DATES_TO_A_CLOSED', ),
                'validvalues'   => array(
                    'AP',   // Automatically Post
                    'WP',   // Warn
                    'DA',   // Don't Allow
                    'SO',   // Statistical Only
                ),
            ),
            'default'   => 'DA',
            'showlabelalways' => true,
        ),
        array(
            'path' => 'DEFAULT_GLBUDGETID',
            'fullname' => 'IA.DEFAULT_GL_BUDGET_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'budgetheader',
                'restrict' => array(
                    array('pickField' => 'ISPABUDGET', 'value' => 'true'),
                ),
            ),
        ),
        array (
            'path'     => 'PJESTIMATE_SEQUENCEID',
            'fullname' => 'IA.ESTIMATE_ID_SEQUENCE',
            'type'     => array (
                'ptype'     => 'ptr',
                'entity'    => 'seqnum',
                'type'      => 'ptr',
                'maxlength' => 20,
            ),
        ),
        array(
            'path' => 'PJESTIMATE_ALLOW_ACCT_OVERRIDE',
            'fullname' => 'IA.OVERRIDE_GL_ACCOUNT_AT_ESTIMATE_LINE',
            'type'     => $gBooleanType,
        ),
        array (
            'path'     => 'CHANGEREQUEST_SEQUENCEID',
            'fullname' => 'IA.CHANGE_REQUEST_ID_SEQUENCE',
            'type'     => array (
                'ptype'     => 'ptr',
                'entity'    => 'seqnum',
                'type'      => 'ptr',
                'maxlength' => 20,
            ),
        ),
        array (
            'path'     => 'PROJECTCHANGEORDER_SEQUENCEID',
            'fullname' => 'IA.PROJECT_CHANGE_ORDER_ID_SEQUENCE',
            'type'     => array (
                'ptype'     => 'ptr',
                'entity'    => 'seqnum',
                'type'      => 'ptr',
                'maxlength' => 20,
            ),
        ),
        array(
            'path' => 'CHANGEREQUEST_ALLOW_ACCT_OVERRIDE',
            'fullname' => 'IA.OVERRIDE_GL_ACCOUNT_AT_CHANGE_REQUEST_LINE',
            'type'     => $gBooleanType,
        ),
        array(
            'path' => 'REQUIREGLACCOUNT_COSTTYPE',
            'fullname' => 'IA.REQUIRE_GL_ACCOUNT_FOR_EVERY_COST_TYPE',
            'type'     => $gBooleanType,
        ),
        array(
            'path' => 'REQUIREITEM_COSTTYPE',
            'fullname' => 'IA.REQUIRE_AN_ITEM_FOR_EVERY_COST_TYPE',
            'type'     => $gBooleanType,
        ),
        array(
            'path'     => 'ENABLE_GRANT_FIELDS',
            'fullname' => 'IA.ENABLE_GRANT_FIELDS',
            'type'     => $gBooleanType,
        ),
        array (
            'path'        => 'BILLABLEFIELDCAPTION',
            'fullname'    => 'IA.BILLABLE_FIELD_CAPTIONING',
            'type'      => array(
                'ptype'         => 'radio',
                'type'          => 'radio',
                'validlabels' => array( 'IA.BILLABLE_PROJECT_BILLABLE', 'IA.BILLABLE_GRANT_QE', ),
                'validvalues'   => array('PB','QE'),
            ),
            'layout'     => 'landscape',
            'showlabelalways' => true,
            'default'   => 'PB',
        ),
        array(
            'path' => 'WIP_COST_ACCTS',
            'fullname' => 'IA.WIP_COST_ACCOUNTS',
            'type' => array(
                'type' => 'multipick',
                'ptype' => 'multipick',
                'pickentity' => 'glaccountpick',
                'entity' => 'glaccount',
                'delimiter' => '#~#',
            ),
            'rowcount' => 4,
            'isHTML'   => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_WIP_REPORT'],
        ),
        array(
            'path' => 'WIP_REVENUE_ACCTS',
            'fullname' => 'IA.WIP_REVENUE_ACCOUNTS',
            'type' => array(
                'type' => 'multipick',
                'ptype' => 'multipick',
                'pickentity' => 'glaccountpick',
                'entity' => 'glaccount',
                'delimiter' => '#~#',
            ),
            'rowcount' => 4,
            'isHTML'   => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_WIP_REPORT'],
        ),
        array(
            'path' => 'WIP_OVERBILLING_ACCT',
            'fullname' => 'IA.WIP_OVERBILLING_ACCT',
            'type' => array(
                'maxlength'     => 24,
                'ptype'         => 'ptr',
                'type'          => 'ptr',
                'pickentity'    => 'glaccountpick',
                'entity'        => 'glaccount',
            ),
            'isHTML'   => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_WIP_REPORT'],
        ),
        array(
            'path' => 'WIP_UNDERBILLING_ACCT',
            'fullname' => 'IA.WIP_UNDERBILLING_ACCT',
            'type' => array(
                'maxlength'     => 24,
                'ptype'         => 'ptr',
                'type'          => 'ptr',
                'pickentity'    => 'glaccountpick',
                'entity'        => 'glaccount',
            ),
            'isHTML'   => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_WIP_REPORT'],
        ),
        array(
            'path' => 'WIP_OFFSET_ACCT',
            'fullname' => 'IA.WIP_OFFSET_ACCT',
            'type'     => array(
                'maxlength'     => 24,
                'ptype'         => 'ptr',
                'type'          => 'ptr',
                'pickentity'    => 'glaccountpick',
                'entity'        => 'glaccount',
            ),
            'isHTML'   => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_WIP_REPORT'],
        ),
        array(
            'path' => 'WIP_OVERBILLING_OFFSET_ACCT',
            'fullname' => 'IA.WIP_OVERBILLING_OFFSET_ACCT',
            'type'     => array(
                'maxlength'     => 24,
                'ptype'         => 'ptr',
                'type'          => 'ptr',
                'pickentity'    => 'glaccountpick',
                'entity'        => 'glaccount',
            ),
            'isHTML'   => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_WIP_REPORT'],
        ),
        array(
            'path' => 'WIP_UNDERBILLING_OFFSET_ACCT',
            'fullname' => 'IA.WIP_UNDERBILLING_OFFSET_ACCT',
            'type'     => array(
                'maxlength'     => 24,
                'ptype'         => 'ptr',
                'type'          => 'ptr',
                'pickentity'    => 'glaccountpick',
                'entity'        => 'glaccount',
            ),
            'isHTML'   => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_WIP_REPORT'],
        ),
        array(
            'path'      => 'WIP_POSTING_JOURNAL',
            'fullname'  => 'IA.WIP_POSTING_JOURNAL',
            'type'      => array(
                'maxlength'     => 24,
                'ptype'         => 'ptr',
                'type'          => 'ptr',
                'pickentity'    => 'gljournalpick',
                'entity'        => 'journal',
            ),
            'isHTML'   => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_WIP_REPORT'],
        ),
        array(
            'path'      => 'WIP_RECONCILE_METHOD',
            'fullname'  => 'IA.WIP_RECONCILE_METHOD',
            'type'      => array(
                'ptype'         => 'radio',
                'type'          => 'radio',
                'validlabels'   => [ 'IA.WIP_RECONCILE_AUTO_REVERSE_BEGINNING_NEXT_PERIOD', 'IA.WIP_RECONCILE_AUTO_REVERSE_END_NEXT_PERIOD' ],
                'validvalues'   => [ 'autoReverseBegin', 'autoReverseEnd' ],
            ),
            'showlabelalways' => true,
            'default' => 'autoReverseBegin',
            'isHTML'   => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_WIP_REPORT'],
        ),
        array(
            'path'      => 'WIP_PM_FORECAST_DETAIL_LEVEL',
            'fullname'  => 'IA.WIP_PM_FORECAST_DETAIL_LEVEL',
            'type'      => array(
                'ptype'         => 'radio',
                'type'          => 'radio',
                'validlabels'   => [ 'IA.WIP_LEVEL_SCHEDULE_PROJECT', 'IA.WIP_LEVEL_PROJECT',
                                     'IA.WIP_LEVEL_COST_CODE', 'IA.WIP_LEVEL_COST_CODE_AND_COST_TYPE' ],
                'validvalues'   => [ 'wipScheduleProject', 'project', 'costCode', 'costCodeAndCostType' ],
            ),
            'showlabelalways' => true,
            'default' => 'wipScheduleProject',
            'isHTML'   => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_WIP_REPORT'],
        ),
        array(
            'path' => 'WIP_AUTOCREATE_NEXT_PERIOD',
            'fullname' => 'IA.WIP_AUTOCREATE_NEXT_PERIOD',
            'type' => $gBooleanType,
            'hidden' => false,
            'default' => 'true',
            'isHTML'   => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_WIP_REPORT'],
        ),
        [
            'path'      => 'PS_SHOWBUDGETCOLUMN',
            'fullname'  => 'IA.SHOW_BUDGET_COLUMN',
            'type'      => $gBooleanType,
        ],
        [
            'path'      => 'PS_DEFAULTGLBUDGETID',
            'fullname'  => 'IA.DEFAULT_GLBUDGET_ID_PS',
            'type'      => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'budgetheader',
                'pickfield' => ['BUDGETID', 'DEFAULT_BUDGET'],
                'restrict' => array(
                    array('pickField' => 'ISCONSOLIDATED', 'value' => 'false'),
                ),
            ],
            'nonew'  => true,
        ],
        [
            'path'      => 'PS_DEFAULTACCTGROUPFORBUDGET',
            'fullname'  => 'IA.DEFAULT_ACCOUNT_GROUP_FOR_BUDGET',
            'type'      => [
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => [
                    'IA.PAYMENTS',
                    'IA.DEFERRED_REVENUE',
                    'IA.REVENUE',
                    'IA.EXPENSES',
                    'IA.COST',
                    'IA.WAGES',
                    'IA.GROSS_PROFITS',
                    'IA.NET_INCOME_LOSS',
                ],
                'validvalues' => [
                    'P', 'DR', 'R', 'E', 'C', 'W', 'GP', 'NIL',
                ],
            ],
            'showlabelalways' => true,
        ],
        [
            'path'      => 'PS_SHOWBUDGETDIFFCOLUMN',
            'fullname'  => 'IA.SHOW_BUDGET_DIFF_COLUMN',
            'type'      => $gBooleanType,
        ],
        [
            'path'            => 'PS_BUDGETCOMPCALC',
            'fullname'        => 'IA.BUDGET_COMPARISON_CALCULATION',
            'type'            => [
                'ptype'       => 'radio',
                'type'        => 'radio',
                'validlabels' => ['IA.BUDGET_MINUS_ACCOUNT_GROUP', 'IA.ACCOUNT_GROUP_MINUS_BUDGET'],
                'validvalues' => ['B-AG', 'AG-B'],
            ],
            'showlabelalways' => true,
        ],
        [
            'path'     => 'PROJECT_SUMMARY_TAB',
            'fullname' => 'IA.ENABLE_PROJECT_SUMMARY_TAB',
            'type'     => $gBooleanType,
            'default' => 'true',
        ],
        [
            'path'     => 'PST_FINANCIAL_SUMMARY',
            'fullname' => 'IA.ENABLE_PST_FINANCIAL_SUMMARY',
            'type'     => $gBooleanType,
            'default' => 'true',
        ],
        [
            'path'     => 'PST_PROJECTS_PROJECTION',
            'fullname' => 'IA.ENABLE_PST_PROJECTS_PROJECTION',
            'type'     => $gBooleanType,
            'default' => 'true',
        ],
        [
            'path'     => 'PST_HOURS',
            'fullname' => 'IA.ENABLE_PST_HOURS',
            'type'     => $gBooleanType,
            'default' => 'true',
        ],
        [
            'path'     => 'PST_EMPLOYEES',
            'fullname' => 'IA.ENABLE_PST_EMPLOYEES',
            'type'     => $gBooleanType,
            'default' => 'true',
        ],
        [
            'path'     => 'RESOURCES_AND_PRICING_TAB',
            'fullname' => 'IA.ENABLE_RESOURCES_AND_PRICING_TAB',
            'type'     => $gBooleanType,
            'default' => 'true',
        ],
        [
            'path'     => 'INVOICE_OPTIONS_TAB',
            'fullname' => 'IA.ENABLE_INVOICE_OPTIONS_TAB',
            'type'     => $gBooleanType,
            'default' => 'true',
        ],
        [
            'path'     => 'TASKS_TAB',
            'fullname' => 'IA.ENABLE_TASKS_TAB',
            'type'     => $gBooleanType,
            'default' => 'true',
        ],
        [
            'path'     => 'ESTIMATES_TAB',
            'fullname' => 'IA.ENABLE_ESTIMATES_TAB',
            'type'     => $gBooleanType,
        ],
        [
            'path'     => 'PURCHASING_COMMITMENTS_TAB',
            'fullname' => 'IA.ENABLE_PURCHASING_COMMITMENTS_TAB',
            'type'     => $gBooleanType,
        ],
        [
            'path'     => 'CHANGE_MANAGEMENT_TAB',
            'fullname' => 'IA.ENABLE_CHANGE_MANAGEMENT_TAB',
            'type'     => $gBooleanType,
        ],
        [
            'path'     => 'PROJECT_CONTRACTS_TAB',
            'fullname' => 'IA.ENABLE_PROJECT_CONTRACTS_TAB',
            'type'     => $gBooleanType,
        ],
        [
            'path'     => 'PROJECT_INVOICES_TAB',
            'fullname' => 'IA.ENABLE_PROJECT_INVOICES_TAB',
            'type'     => $gBooleanType,
        ],
        [
            'path'     => 'ENABLE_WIP_RELIEF',
            'fullname' => 'IA.ENABLE_WIP_RELIEF',
            'type'     => $gBooleanType,
        ],
        [
            'path' => 'WIPRELIEFJOURNAL',
            'fullname' => 'IA.WIP_RELIEF_JOURNAL',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'opalljournalpick',
                'restrict' => [
                    // we will allow only 'Accrual' journals
                    ['pickField' => 'BOOKTYPE', 'operand' => 'NOT IN', 'value' => ['C','GC', 'TC', 'UC']],
                    ['pickField' => 'BOOKID', 'value' => 'ACCRUAL'],
                    ['pickField' => 'STATISTICAL', 'operand' => '=', 'value' => 'F'],
                    ['pickField' => 'STATUS', 'operand' => '=', 'value' => 'T']
                ],
                'pickfield' => ['SYMBOL'],
                'addlPickFields' => ['TITLE'],
            ),
        ],
        [
            'path'     => 'WIP_ACCT',
            'fullname' => 'IA.WIP_ACCOUNT',
            'type'     => [
                'type'       => 'ptr',
                'ptype'      => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity'     => 'glaccount',
                'restrict' => [
                    ['pickField' => 'CLOSEABLE', 'operand' => 'NOT IN', 'value' => ['C', 'R']],
                    ['pickField' => 'ACCOUNT_TYPE', 'value' => 'N'],
                    ['pickField' => 'NORMAL_BALANCE', 'value' => '1'],
                    ['pickField' => 'STATUS', 'value' => 'active'],
                ],
            ],
        ],
        [
            'path'     => 'WIP_RELIEF_ACCT',
            'fullname' => 'IA.WIP_RELIEF_ACCOUNT',
            'type'     => [
                'type'       => 'ptr',
                'ptype'      => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity'     => 'glaccount',
                'restrict' => [
                    ['pickField' => 'NORMAL_BALANCE', 'value' => '1'],
                    ['pickField' => 'STATUS', 'value' => 'active'],
                ],
            ],
        ],
    ),
    'table'              => 'modulepref',
    'vid'                => 'modulekey',
    'module'             => 'pa',
    'hasdimensions'      => true,
    'followgldimensions' => true,
    'renameable'         => true,
    'api' => array('PERMISSION_READ'    => 'services/modules/view',
                   'PERMISSION_MODULES' => array('co', 'mp')),
    'dontGenerateQueries' => true,
);
