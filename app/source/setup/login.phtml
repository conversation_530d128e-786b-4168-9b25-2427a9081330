<?php
//=============================================================================
//
//  FILE:           login.phtml
//  AUTHOR:
//  DESCRIPTION:
//
//  (C)2000, Intacct Corporation, All Rights Reserved
//
//  Intacct Corporation Proprietary Information.
//  This document contains trade secret data that belongs to Intacct
//  corporation and is protected by the copyright laws. Information herein
//  may not be used, copied or disclosed in whole or part without prior
//  written consent from Intacct Corporation.
//
//=============================================================================

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// initialize vars
$_needsauth = false;
$_cpa =  false;
$_assn = false;
$doingAutoLogin = false;
$userEnteredPassword = '';
$co_rec = null;

require_once 'util.inc';
require_once 'login_util.inc';
require_once 'CsrfUtils.cls';
require_once "MFAuthManager.cls";
require_once 'XgResources.inc';

// to prevent JS/SQL injections
Request::FilterRequest();
// Explicit request filtering for XSS check
FilterLoginLogoutRequest();
IACspPolicyManager::setForceEnforceMode();

$_ret    = Request::$r->_ret;
$_msg    = Request::$r->_msg;
$_waitTime = Request::$r->_waitTime;
$_reason = Request::$r->_reason;
$hlpfile = &Request::$r->hlpfile;
$_logout = Request::$r->_logout;
$_passwd = Request::$r->_passwd;
$_rememberme = &Request::$r->_rememberme;
$_uesp       = Request::$r->_uesp;
$fromLogout  = &Request::$r->fromLogout;
$inArgs      = &Request::$r->inArgs;
$loginSpec   = &Request::$r->loginSpec;
$_oid        = &Request::$r->_oid;
$client_ip_address = &Request::$r->client_ip_address;
$client_user_agent = &Request::$r->client_user_agent;

if ( Request::$r->_topper ) {
    // Generate the hidden topping iframe
    loginToppingIFramePage();
    exit();
}

// $_ret is only supposed to be set when being POSTed from the login page.  Unfortunately AddError
// sets the global $_ret to "" so let's grab the value early.
$executingLoginPost = isset($_ret);

// Log basic login information for troubleshooting purposes (in case of DB failure)
if ($executingLoginPost
    && isset($_REQUEST['_login']) && $_REQUEST['_login'] != ''
    && isset($_REQUEST['_company']) && $_REQUEST['_company'] != ''
) {
    LogToFile(
        sprintf(
            "%s::%s:%d %s ATTEMPT: %s / %s @ %s OID=%s\n",
            LoginManager::LOG_PREFIX, basename(__FILE__), __LINE__, ($_REQUEST['_logout'] ? 'LOGOUT' : 'LOGIN'),
            $_REQUEST['_company'], $_REQUEST['_login'], getIP(), Request::$r->_oid
        )
    );
}

InitBase();
InitGlobals();

$_done = &Request::$r->_done;
$_cpaassoc = Request::$r->_cpaassoc;
$_cpacny   = &Request::$r->_cpacny;
$_sess = Session::getKey();
$_ssoIdp = Request::$r->_ssoIdp;
$_headerColor = Request::$r->_headerColor;
$_hasCustomLogo = Request::$r->_hasCustomLogo;

$cotitle = GetValueForIACFGProperty("IA_COMPANYTITLE");
$privacypolicylink = GetValueForIACFGProperty("IA_PRIVACYLINK");
$samplemode = GetValueForIACFGProperty("IS_SAMPLE_COMPANY_ENVIRONMENT") === "1";


if ( $_cpa && $_assn) {
    $_cpacny = LookupCompany(urldecode($_cpa));
    if ( !empty($_cpacny)) {
        $_cpaassoc = $_assn . '@' . $_cpacny['RECORD#'];
    }
}

$request  = Request::$r;
$theme    = Request::$r->_theme;
$_login   = &Request::$r->_login;
$_company = &Request::$r->_company;
$cpalogo  = Request::$r->_cpalogo;
$sso  = Request::$r->_issso;
$rootCompany = &Request::$r->rootCompany;
$_authcode   = Request::$r->_authcode;

/**
 * Sanity/Security checks
 *
 * - _login must be a string (IA-146238) or blank (null). Array can be passed by using query "...?_login[]=name1&_login[]=name2"
 *
 * NOTE: more sanity tests can be made, by using the variable $sanityCheck
 */
$sanityCheck = true;
if ( !is_string($_login ?? '') ) {
    $sanityCheck = false;
    $type = gettype($_login ?? null);
    LogToFile(
        sprintf(
            "%s::%s:%d Security: login attempt with bad input type for param '_login': %s. \n",
            LoginManager::LOG_PREFIX, basename(__FILE__), __LINE__, $type
        )
    );
    $_login = null; // NULL this out
}
/**
 * // Candidate code to sanitize input, but I fear it may break valid login strings using non-English characters
   else {
      // Sanitize, this may return a blank string if it contains invalid characters
      $_login = htmlspecialchars($_login, ENT_COMPAT);
  }
*/
if ( !$sanityCheck ) {
    LogToFile(sprintf("%s::%s::SANITY CHECK FAILED REDIRECT TO LOGIN \n", LoginManager::LOG_PREFIX, __FILE__));
    // Redirect with 400 response code (Bad Request)
    header("Location: " . FwdUrl("login.phtml", "frameset.phtml"), true, 400);
    // Cannot exit here as it causes the browser to not redirect
}

//this is the client ip address. we need to pass it to curl using post parameters to be pickedup by the profile handler
$client_ip_address = getIP();

//  If we are processing a navop link, check to see if the company/user is SSO.
// Make sure that when logging in or out we clear the role set by the sample company role switcher.
// I wish there was a cleaner way to do this.
SampleCompanyManager::clearRoleOverride();

// Clear the lastPage cookie if it exists.  We don't want the previous page persisting through a login.
// We have another mechanism for that.  Autoslide's get-me-back.
// Don't use a path for unsetting lastPage cookie, given that when set no path is used
INTACCTunsetcookie('lastPage', '', 0, '', '', false);
INTACCTunsetcookie('currentMenuItemRefNo', '', 0, '', '', false);

// If we're not logging out
// and not processing the submit from the login form
// and there is a .company parameter
// Then attempt auto-login and set up for auto-slide
// If there's a reason or message we still need to get into the autoslide code in order to
// generate the return url and fix up the company name for the login form.

$loginManager = Globals::$g->gLoginManager;
$podManager = Globals::$g->gPODManager;

// $oauthFlowLoginError = true identifies the case when the login page is accessed AFTER providing
// incorrect credentials in oauth login flow
$oauthFlowLoginError =
    ($_msg === 'IA.SIGN_IN_INFORMATION_IS_INCORRECT' || $_msg === 'IA.WAIT_X_SECONDS_BEFORE_LOGGING_AGAIN')
    && isset(Request::$r->_done) && retrieveParamFromUrl(Request::$r->_done, '.access_token') === 'true'
    && substr(Request::$r->_done, 0, 17) === 'oauth_token.phtml';

if ((!( $executingLoginPost && isset($_logout)))
    && (!($executingLoginPost && ($_POST['_login'] != '' || $_GET['_login'] != '' )))
    && '' !== ($company = crackArray($_GET, '_company', ''))
    && ! (isset(Request::$r->_noAutoLogin) && Request::$r->_noAutoLogin)
    && !(isset(Request::$r->_ssoIdp)
         && Request::$r->_ssoIdp === 'sso_idp'
         && isset(Request::$r->_done)
         && substr(Request::$r->_done, 0, 19) === 'sso_idp_login.phtml'
    )
    && !$oauthFlowLoginError
) {
    // Brute Force Defense pre-login check, if false this prevents a login attempt until some time has elapsed
    if ( !$podManager->checkBruteForcePreLogin($login_results, logPrefix: LoginManager::LOG_PREFIX) ) {
        Redirect($login_results['NEXT_URL']);
    }

    // If .company specified see if there's a valid session and attach to that
    //we need to make sure that this phtml works only with global database. The following code will strip
    //all the database connection other than glabal db. Any code which access owner db must be written in
    //such a way that they execute only on POD not the global instance.
    Database::setGlobalDatabasesOnly(true);

    $loginSpec = $login = crackArray($_GET, '_login', '');
    $fromLogout = crackArray($_GET, '_fl', '') || crackArray($_GET, '_loggingout', '')  ? true : false;

    // Remove login-specific args, the rest are to be passed on to frameset
    $inArgs = preg_replace(

        '/(\.company|\.login|\.cpaassoc|\.theme|\.affiliation|\.cpalogo|\.fl|\.asx|\.sess|\.loggingout)=[^&]*&?/', 
        '', 
        $_SERVER['QUERY_STRING']
    );

    $companies = explode('|', $company);
    $rootCompany = $companies[0];
    $oid = crackArray($_COOKIE, '_oid', '');

    $cookieCollection = array();
    $cookieCollection['.oid'] = $_oid;
    LogToFile(sprintf("%s::%s:%d OID: %s", LoginManager::LOG_PREFIX, basename(__FILE__), __LINE__, $_oid));
    foreach ( $_COOKIE as $cookieName => $cookieValue ) {
        if ( IASessionHandler::isSessionCookie($cookieName) ) {
            $cookieCollection[$cookieName] = $cookieValue;
        }
    }

    $curl_parameters = array();
    $curl_parameters['_handlePODLogin'] = 'true';
    $curl_parameters['_autoSlide'] = 'true';
    $curl_parameters['companyPath'] = $company;
    $curl_parameters['loginSpec'] = $loginSpec;
    $curl_parameters['inArgs'] = $inArgs;
    $curl_parameters['fromLogout'] = $fromLogout;
    $curl_parameters['_company'] = $_company;
    $curl_parameters['_login'] = $_login;
    $curl_parameters['_authcode'] = $_authcode;
    $curl_parameters['_rememberme'] = $_rememberme;
    $curl_parameters['_passwd'] = URLCleanParams::insert('.passwd', $userEnteredPassword);
    $curl_parameters['_sso'] = $sso;
    $curl_parameters['http_host'] = $_SERVER['HTTP_HOST'];
    $curl_parameters['rootCompany'] = $rootCompany;
    $curl_parameters['client_ip_address'] = $client_ip_address;
    $curl_parameters['previousLoginMessage'] = $_msg;
    $curl_parameters['_ssoIdp'] = Request::$r->_ssoIdp;

    $pod_information = $podManager->getPODURL($rootCompany, 'title');
    $curl_url = $pod_information['POD_URL'];
    $podExternalHost = $pod_information['POD_EXTHOST'];
    $timeout  = $pod_information['TIMEOUT'];

    Util::httpCall(
        $curl_url,
        $curl_parameters,
        $login_result,
        false,
        null,
        $cookieCollection,
        true,
        null,
        null,
        null,
        true,
        $dummy_theRetHeaders,
        false,
        $curlInfo,
        $timeout,
        logPrefix: LoginManager::LOG_PREFIX
    );

    // Detect if there is a timeout. If so give the user a generic error message so they try again.
    // (its possible that pod_login is taking a while to create platform cache)
    if ( ($curlInfo['curl_errno'] ?? 0) === CURLE_OPERATION_TIMEOUTED ) {
        $_msg = 'IA.REQUEST_TIMED_OUT_TRY_AGAIN';
        LogToFile(
            sprintf(
                "%s::%s:%d request timed out. Redirecting to login page from IP: '%s' with SERVER[UNIQUE_ID]: '%s', OID: '%s'.",
                LoginManager::LOG_PREFIX, basename(__FILE__), __LINE__, getIP(), (string) $_SERVER['UNIQUE_ID'], Request::$r->_oid
            )
        );
        redirectToLoginPage(is_string($_company) ? $_company : '', is_string($_login) ? $_login : '', $_msg);
    }

    //eppp_p($login_result);
    //dieFL();
    $podManager->postProcessPODResults(
        $login_result, $podExternalHost, $_cpaassoc, $_msg, $_company, $_login, $_passwd, $co_rec,
        logPrefix: LoginManager::LOG_PREFIX
    );
    //if this section of the code is reached it means that we intend to stay on global and render the login screen
    $_company = $rootCompany;
}

//  If this is invoked with a navop (e.g. from a link) and NOT an autologin or IS an SSO test, then we'll do SSO.
if ( ( $doingAutoLogin != true)
    && isset($_REQUEST['_ssoDone'])
    && isl_strpos(isl_trim($_REQUEST['_ssoDone']), "navop") !== false
    && !Util::php7eqEmptyStr($_company)
    && !Util::php7eqEmptyStr($_login)
) {
    if (GetUserid($_login, $_company, $user_rec)) {
        [$sso_userid, $sso_cny] = explode('@', $user_rec);
        if (isCompanySSO($sso_cny) && isUserSSO($sso_userid, $sso_cny)) {
            $ssoFO = true;
        }
    }
}

if ( $executingLoginPost && isset($_logout) ) {
    //call the utility method in login_util.inc
    $loginManager->processLogout($_reason, $_hasCustomLogo, $_headerColor);

} else if ( $executingLoginPost && ($_POST['_login'] != '' || $_GET['_login'] != '' ) ) {
    // CSRF token check
    $login_id = $_SESSION['login_id'] ?? '';
    if ($login_id === '') {
        $_msg = 'IA.INVALID_SESSION_TRY_AGAIN';
        LogToFile(
            sprintf(
                "%s::%s Error, CSRF TOKEN MISSING. ip= %s, UNIQUE_ID= %s, OID: %s.",
                LoginManager::LOG_PREFIX, basename(__FILE__), getIP(), (string) $_SERVER['UNIQUE_ID'], Request::$r->_oid
            )
        );
        redirectToLoginPage(is_string($_company) ? $_company : '', is_string($_login) ? $_login : '', $_msg);
    }

    //first check is when the redirect is coming from a login page
    if (
        isset($login_id)
        && !isset(Request::$r->_eSecReq)
        && !CsrfUtils::verifyInputToken("login" . $login_id)
    ) {
        $_msg = 'IA.INVALID_REQUEST_TRY_AGAIN';
        LogToFile(
            sprintf(
                "%s::%s verifyInputToken failed. Redirecting to login page, ip= %s, UNIQUE_ID= %s, OID: %s.",
                LoginManager::LOG_PREFIX, basename(__FILE__), getIP(), (string) $_SERVER['UNIQUE_ID'], Request::$r->_oid
            )
        );
        redirectToLoginPage(is_string($_company) ? $_company : '', is_string($_login) ? $_login : '', $_msg);
    }

    //if the Request value exists I'm coming from create company scenario
    if (isset(Request::$r->_eSecReq)) {
        $decryptedCreateCompanyCheckParams = URLEncryption::extractQueryParams(Request::$r->_eSecReq);

        if (
            $decryptedCreateCompanyCheckParams['_server_remote_adr'] === $_SERVER['REMOTE_ADDR']
            && !CsrfUtils::verifyInputToken("eSecReq" . Request::$r->_eSecReq)
        ) {
            $_msg = 'IA.INVALID_REQUEST_TRY_AGAIN';
            LogToFile(
                sprintf(
                    "%s::%s CreateCompanyCheck failed. Redirecting to login page, ip= %s, servRemoteAdr= %s, REMOTE_ADDR= %s.",
                    LoginManager::LOG_PREFIX, basename(__FILE__), getIP(), $decryptedCreateCompanyCheckParams['_server_remote_adr'],
                    (string) $_SERVER['REMOTE_ADDR']
                )
            );
            redirectToLoginPage(is_string($_company) ? $_company : '', is_string($_login) ? $_login : '', $_msg);
        }
    }

    // Brute Force Defense pre-login check, if false this prevents a login attempt until some time has elapsed
    if ( !$podManager->checkBruteForcePreLogin($login_results, logPrefix: LoginManager::LOG_PREFIX) ) {
        if (isset(Request::$r->_ssoIdp)
            && Request::$r->_ssoIdp === 'sso_idp'
            && isset(Request::$r->_done)
            && substr(Request::$r->_done, 0, 19) === 'sso_idp_login.phtml'
        ) {
            $login_results['NEXT_URL'] .= '&.ssoIdp=sso_idp&.done='.urlencode(Request::$r->_done);
        }
        Redirect($login_results['NEXT_URL']);
    }

    //we need to make sure that this phtml works only with global database. The following code will strip
    //all the database connection other than glabal db. Any code which access owner db must be written in
    //such a way that they execute only on POD not the global instance.
    Database::setGlobalDatabasesOnly(true);
    //check if sample companies are allowed in this environment
    if ($samplemode) {
        $statusOrRedirect = SampleCompanyManager::checkBirthOrDeath($_company, $_login, $_passwd);
        if ($statusOrRedirect !== true) {
            LogToFile(sprintf("%s::%s check sample company failed", LoginManager::LOG_PREFIX, __FILE__));
            Redirect($statusOrRedirect);
            exit();        // Just to be clear
        }
    }

    $_fooverride = '';
    if (isl_strpos(isl_trim($_done), "navop")) {
        $_fooverride = $_done;
    }

    $_rememberme = (isset($_rememberme) && $_rememberme == 'on') ? true : false;

    $userEnteredPassword = Request::$r->_passwd;

    $uidFromAuthenticationPage = Request::$r->_uid;
    if ( $uidFromAuthenticationPage != null  && $userEnteredPassword == '' ) {
        $mem_cache =  CacheClient::getInstance();
        $userEnteredPassword = $mem_cache->get("loginAuthP".$uidFromAuthenticationPage);
        $mem_cache->delete($uidFromAuthenticationPage);
    }
    //if remember me is checked then create cookies for IACOMPANY and IAUSER
    //otherwise clear the cookies, this code is taken from backend_misc
    if ($_rememberme) {
        INTACCTsetcookie('IACOMPANY', $_company, time() + 365 * 24 * 60 * 60);
        INTACCTsetcookie('IAUSER', $_login, time() + 365 * 24 * 60 * 60);
        //  If not, remove the possible Intacct SSO cookie (which is set in SSOLogin).
    } else {
        INTACCTunsetcookie('IACOMPANY');
        INTACCTunsetcookie('IAUSER');
    }

    //  Get the cookies to pass to curl call.
    $cookieCollection = getCookiesForLogin($_oid);

    //we need to pass the HTTP_USER_AGENT as separate post parameter to the curl call
    //this is needed as CURL call changes the user agent for security purposes
    //the borowser.inc code will check for the POST variable to get the user agent before
    //checking the $_SERVER
    $client_user_agent = $_SERVER['HTTP_USER_AGENT'];

    //curl call
    //notice that all the parameters are passed as POST
    $curl_parameters = array();
    $curl_parameters['_handlePODLogin'] = 'true';
    $curl_parameters['_company'] = $_company;
    $curl_parameters['_login'] = $_login;
    $curl_parameters['_authcode'] = $_authcode;
    $curl_parameters['_rememberme'] = $_rememberme;
    $curl_parameters['_passwd'] = URLCleanParams::insert('.passwd', $userEnteredPassword);
    $curl_parameters['_sso'] = $sso;
    $curl_parameters['_done'] = insertDoneUnEnc($_done, 600);
    $curl_parameters['http_host'] = $_SERVER['HTTP_HOST'];
    $curl_parameters['client_ip_address'] = $client_ip_address;
    $curl_parameters['client_user_agent'] = $client_user_agent;
    $curl_parameters['_metro'] = Request::$r->_metro;
    $curl_parameters['_usep'] = $_POST['_usep'] ?? '';
    $curl_parameters['_ret'] = $_POST['_ret'];
    $curl_parameters['_deprecatedTLS'] = supportedTransportLayerSecurityVersion([], false) ? 0 : 1;
    $curl_parameters['_ssoIdp'] = Request::$r->_ssoIdp;

    $pod_information = $podManager->getPODURL($_company, 'title');
    $curl_url = $pod_information['POD_URL'];
    $podExternalHost = $pod_information['POD_EXTHOST'];
    $timeout  = $pod_information['TIMEOUT'];

    Util::httpCall(
        $curl_url,
        $curl_parameters,
        $login_result,
        false,
        null,
        $cookieCollection,
        true,
        null,
        null,
        true,
        true,
        $dummy_theRetHeaders,
        false,
        $curlInfo,
        $timeout,
        logPrefix: LoginManager::LOG_PREFIX
    );

    // Detect if there is a timeout. If so give the user a generic error message so they try again.
    // (its possible that pod_login is taking a while to create platform cache)
    if ( ($curlInfo['curl_errno'] ?? 0) === CURLE_OPERATION_TIMEOUTED ) {
        $sandbox = $_SERVER['SCRIPT_URI'] ?? '';
        $messageToLog = sprintf(
            "%s::%s:%d HTTP call to %s timed out after %s seconds.",
            LoginManager::LOG_PREFIX, basename(__FILE__), __LINE__, $curl_url, $timeout
        );
        if ( !Globals::$g->islive ) {
            // add additional context information only in development
            $messageToLog .=  "User: $_login, Company: $_company, Sandbox: $sandbox";
        }

        logToFileError($messageToLog);
        $_msg = 'IA.REQUEST_TIMED_OUT_TRY_AGAIN';
        redirectToLoginPage(is_string($_company) ? $_company : '', is_string($_login) ? $_login : '', $_msg);
    }

    $podManager->postProcessPODResults(
        $login_result, $podExternalHost, $_cpaassoc, $_msg, $_company, $_login, $userEnteredPassword, $co_rec,
        logPrefix: LoginManager::LOG_PREFIX
    );
}

// if there is no done set in the url, lets default it
if ( !isset($_done) || $_done == '' ) {
    $_done = "frameset.phtml";
}

$kCompany = $cotitle;
$kDomain = 'intacct';
$title = "$kCompany Login";

if ($_needsauth) {
    $hlpfile = 'SignUp_Account_Activation';
} else {
    $hlpfile = 'Sign_In';
}

if (!isset($_company) && !isset($_login) && isset($_COOKIE['IACOMPANY']) && isset($_COOKIE['IAUSER'])){
    $_company = urldecode($_COOKIE['IACOMPANY']);
    $_login = urldecode($_COOKIE['IAUSER']);
}

// Check if request is from an OAuth flow
$oauth = false;
$oauthAppName = '';

$partnerMgr = new imsPartnerInfoManager();
$clientId = retrieveParamFromUrl(Request::$r->_done, 'client_id');

if ($clientId) {
    $oauthAppName = imsPartnerInfoManager::getPartnerAppName($clientId);
    if ($oauthAppName) {
        $oauth = true;
    }
}

$loginUtil = new LoginUtil();
$loginUtil->translateErrorToken((string)$_msg, (int)$_waitTime);

if ( $_needsauth ) {  // authenticate after signup page
    ShowAuthentication($loginUtil, $_cpaassoc, $_company, $_login, $userEnteredPassword, $co_rec);
} elseif (GetValueForIACFGProperty("IA_LOGIN_SHOW_OLDPAGE") === "1"
    || (isset($_cpaassoc) && $_cpaassoc != '')
    || (isset($_ssoIdp) && $_ssoIdp === 'sso_idp')
) {
    // 9363: If we're about to display the login page we do not have a session no matter what might be in $_sess.
    // Clear it out so we don't put the old session in the frameset redirect of the login form.
    $_GET['_sess'] = $_REQUEST['_sess'] = $_sess = '';
    if (isMobileAccess()) {
        ShowMobileLoginPage($loginUtil, $_company, $_login, $_uesp);
        Shutdown();
    } else {
        ShowLogin($loginUtil, $_cpaassoc, $_company, $_login, $_uesp, $sso, $_headerColor, $_hasCustomLogo);
    }
} else {
    // 9363: If we're about to display the login page we do not have a session no matter what might be in $_sess.
    // Clear it out so we don't put the old session in the frameset redirect of the login form.
    $_GET['_sess'] = $_REQUEST['_sess'] = $_sess = '';

      // Generic login screen
    if (( SampleCompanyManager::supported() && $samplemode) || (Request::$r->_sample)) {
        $template = "sample";
    } else {
        $template = "normal";
    }
    
    MFAuthManager::unsetCookieTamperingAtLogin();
    if (isMobileAccess()) {
        ShowMobileLoginPage($loginUtil, $_company, $_login, $_uesp);
        Shutdown();
    } else {
        ShowFullLoginPage($loginUtil, $_company, $_login, $_uesp, $template, $sso, $oauth, $oauthAppName);
    }
}
?>
    <link href="../resources/nextgen/css/<?= XgResources::$nextgenCss; ?>" rel="preload" as="style">
    <link href="../resources/nextgen/js/<?= XgResources::$nextgenJs; ?>" rel="modulepreload" as="script">
    <link href="../resources/nextgen/css/<?= XgResources::$chunkVendorsCss; ?>" rel="preload" as="style">
    <link href="../resources/nextgen/js/<?= XgResources::$appJs; ?>" rel="modulepreload" as="script">
    <link href="../resources/nextgen/js/<?= XgResources::$chunkVendorsJs; ?>" rel="modulepreload" as="script">
    <link href="../resources/nextgen/custom/customFunctions.js" rel="modulepreload" as="script">
<?php
