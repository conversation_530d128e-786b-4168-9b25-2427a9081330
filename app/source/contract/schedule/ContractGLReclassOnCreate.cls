<?php
/**
 * File ContractGLReclassOnCreate.cls contains the class ContractGLReclassOnCreate
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * 'On Contract' event handler
 *
 * Class ContractGLReclassOnContract
 */
class ContractGLReclassOnCreate extends ContractRevenueGLReclassEvent implements IContractGLReclassUnbilledEvent
{
    /* @var int $contractDetailKey */
    private $contractDetailKey;
    /* @var float|int $exchRate */
    private $exchRate;
    /* @var string $transactionDate */
    private $transactionDate;
    /* @var string $jrnlCode */
    private $jrnlCode;

    /**
     * @param   int     $contractDetailKey  Contract detail key
     * @param   float   $amount             Amount
     * @param   float   $baseAmount         Base amount
     * @param   float   $exchRate           Exchange rate
     * @param   string  $transactionDate    Transaction date
     * @param   string  $jrnlCode           J1 or J2
     * @param   string  $historicalFlag     ACP reclass 'historical flag'.
     */
    public function __construct($contractDetailKey, $amount, $baseAmount, $exchRate, $transactionDate,
                                $jrnlCode, $historicalFlag)
    {
        $this->contractDetailKey = $contractDetailKey;
        $this->exchRate = !$exchRate ? 1 : $exchRate;
        $this->transactionDate = $transactionDate;
        $this->jrnlCode = $jrnlCode;

        parent::__construct($amount, $baseAmount, $historicalFlag);
    }

    /**
     * Runs business logic for 'onContract' event
     *
     * @thros IAException
     */
    public function execute()
    {
        // Do not post affect balances for 0 amount line
        if ($this->amount == 0) {
            return;
        }

        // Increase Unbilled Deferred Revenue

        if ($this->getHistoricalFlag() == 'true') {
            // Skip Unbilled accounting for ACP reclass.

            $glBatchKey = null;
        }
        else {
            $glBatchKey = $this->doUnbilledAccounting();
        }

        $this->affectUnbilledBalances($glBatchKey);
    }

    /**
     * Finds existing balance amounts, computes delta value for affecting balances and calculates base versions
     *
     * @throws IAException
     */
    public function calculateAmounts()
    {
        // Amounts are passed in constructor. No processing required.
    }

    /**
     * Affects unbilled accounts
     *
     * @return  int     GL Batch Key
     *
     * @throws IAException
     */
    public function doUnbilledAccounting()
    {
        $cnDetail = $this->getRequiredContractDetailFields($this->contractDetailKey);
        $unBilledARAcctRec = $cnDetail['ARUNBILLEDACCTKEY'];
        if (!$unBilledARAcctRec) {
            throw IAException::newIAException('CN-1678',"Cannot post to GL. Unbilled AR Account is not mapped.");
        }

        $unBilledDRAcctRec = $cnDetail['DRUNBILLEDACCTKEY'];
        if (!$unBilledDRAcctRec) {
            throw IAException::newIAException('CN-1679',"Cannot post to GL. Unbilled Deferred Account is not mapped.");
        }

        // DB Unbilled AR and CR Unblled Deferred Revenue
        $glBatchKey = $this->postToGL(
            $cnDetail,
            $this->amount,
            $this->baseAmount,
            $this->exchRate,
            $this->transactionDate,
            $unBilledARAcctRec,
            $unBilledDRAcctRec,
            $this->jrnlCode,
            self::EVENTTYPE_ONCONTRACT,
            '',
            true
        );

        return $glBatchKey;
    }

    /**
     * Affect unbilled balances
     *
     * @param   int     $glBatchKey         GL batch key
     *
     * @throws IAException
     */
    public function affectUnbilledBalances($glBatchKey)
    {
        $jrnlType = $this->getJrnlType($this->jrnlCode);

        $cnDetail = $this->getRequiredContractDetailFields($this->contractDetailKey);

        // Increase Unbilled DR
        $values = [
            'TYPE'              => $this->getResolveTypeFixedOrTime(),
            'JOURNALTYPE'       => $jrnlType,
            'BALANCETYPE'       => self::BALANCETYPE_REVENUE,
            'EVENTTYPE'         => self::EVENTTYPE_ONCONTRACT,
            'POSTINGTYPE'       => self::POSTINGTYPE_DEFERREDREVENUE,
            'CLASSIFICATION'    => self::CLASSIFICATION_UNBILLED,
            'TR_TYPE'           => $this->getTrTypeMultiplierIncrease(),
            'AMOUNT'            => $this->amount,
            'BASEAMOUNT'        => $this->baseAmount,
            'EXCHANGE_RATE'     => $this->exchRate,
            'TRANSACTIONDATE'   => $this->transactionDate,
            'CONTRACTKEY'       => $cnDetail['CONTRACTKEY'],
            'CONTRACTDETAILKEY' => $this->contractDetailKey,
            'GLBATCHKEY'        => $glBatchKey,
            'HISTORICAL'        => $this->getHistoricalFlag()
        ];

        $ok = $this->addContractResolve($values);

        if (!$ok) {
            throw IAException::newIAException('CN-1680',
                "Unable to increase Unbilled Deferred Revenue by $this->amount in $jrnlType.",
                ['AMOUNT'=>$this->amount, 'JRNL_TYPE'=> $jrnlType]);
        }

        // Increase Unbilled AR
        $values = [
            'TYPE'              => $this->getResolveTypeFixedOrTime(),
            'JOURNALTYPE'       => $jrnlType,
            'BALANCETYPE'       => self::BALANCETYPE_AR,
            'EVENTTYPE'         => self::EVENTTYPE_ONCONTRACT,
            'POSTINGTYPE'       => self::POSTINGTYPE_AR,
            'CLASSIFICATION'    => self::CLASSIFICATION_UNBILLED,
            'TR_TYPE'           => $this->getTrTypeMultiplierIncrease(),
            'AMOUNT'            => $this->amount,
            'BASEAMOUNT'        => $this->baseAmount,
            'EXCHANGE_RATE'     => $this->exchRate,
            'TRANSACTIONDATE'   => $this->transactionDate,
            'CONTRACTDETAILKEY' => $this->contractDetailKey,
            'CONTRACTKEY'       => $cnDetail['CONTRACTKEY'],
            'GLBATCHKEY'        => $glBatchKey,
            'HISTORICAL'        => $this->getHistoricalFlag()
        ];

        $ok = $this->addContractResolve($values);

        if (!$ok) {
            throw IAException::newIAException('CN-1680',
                "Unable to increase Unbilled Deferred Revenue by $this->amount in $jrnlType.",
                ['AMOUNT'=>$this->amount, 'JRNL_TYPE'=> $jrnlType]);
        }
    }

    /**
     * @return int|null
     */
    protected function getLineKey()
    {
        return $this->contractDetailKey;
    }

    /**
     * @return int|null
     */
    protected function getSourceFK1()
    {
        return null;
    }

    /**
     * @return int|null
     */
    protected function getSourceFK2()
    {
        return null;
    }
}
