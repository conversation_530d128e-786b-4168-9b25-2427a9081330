<?php
/**
 * File ContractRevenueGLReclassEngine.cls contains the class ContractRevenueGLReclassEngine
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Contract Revenue GL re-classification handler
 *
 * Class ContractRevenueGLReclassEngine
 */
class ContractRevenueGLReclassEngine extends BaseContractGLReclassEngine implements IContractRevenueGLReclassEngine
{
    /* @var array $originalAmounts  Used to record original amount passed along with the event */
    protected $originalAmounts                      = [];
    /** @var array $originalAmountBillingScheduleEntries */
    protected $originalAmountBillingScheduleEntries = [];
    /* @var array $paymentAndReversalAmountFromCNSub  Used to record original amount passed along with the event from contract resolve  */
    protected $paymentAndReversalAmountFromCNSub = [];

    /** @var array $paymentAndReversalBSEKeys */
    protected $paymentAndReversalBSEKeys = [];
    /** @var array $meaDistributionCache */
    protected $meaDistributionCache                 = [];
    /** @var array  $executedClearTransactions*/
    protected $executedClearTransactions            = [];
    /** @var array $executedEvents */
    protected $executedEvents                       = [];

    /* @var array $scheduleLinksToUse  */
    protected $scheduleLinksToUse                   = [];

    /** @var float[] $postedExchRates  */
    protected $postedExchRates                      = [];

    /** @var array $lineInstructions */
    protected $lineInstructions;

    /** @var int[] $minOnRecognitionForMEADelete   TODO when ReclassEngine becomes singleton we should change this to non-static */
    private static $minOnRecognitionForMEADelete    = [];

    /**
     * @var array $isCnResolvePostedInOpenPeriodFlag  */
    private $isCnResolvePostedInOpenPeriodFlag  =  [];

    /** @var array $meaDistributionCacheOldUpdated */
    protected $meaDistributionCacheOldUpdated = null;
    /* @var array $scheduleLinksToUseOldUpdated  */
    protected $scheduleLinksToUseOldUpdated = null;
    protected ?string $bundleType = null;
    protected ?int $bundleKey = null;
    /* @var string $bundleEventType */
    protected $bundleEventType = ContractGLReclassEvent::EVENTTYPE_ONMEA;
    /* @var string $bundleEntity */
    protected $bundleEntity = 'contractmeabundle';
    /* @var string $bundleEntryEntity */
    protected $bundleEntryEntity = 'contractmeabundleentry';
    
    
    
    /**
     * Protected
     */
    protected function __construct()
    {
    }

    /**
     * Record balance with for contract line
     *
     * @param  int    $contractDetailKey  Contract detail key
     *
     * @throws IAException
     */
    public function onCreate($contractDetailKey)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        // TODO: We fetch contractdetail again inside event handler
        $cnDetail = $this->getContractDetail($contractDetailKey);

        $amount = $cnDetail['TOTALFLATAMOUNT'];
        $baseAmount = $cnDetail['TOTALBASEFLATAMOUNT'];
        $exchRate = $cnDetail['EXCHANGE_RATE'];

        $transactionDate = $cnDetail['GLPOSTINGDATE'];
        if (!$transactionDate) {
            $msg = "Missing GL Posting date:" . $contractDetailKey;
            throw IAException::newIAException( 'CN-1557', $msg, ['CONTRACT_DETAIL_KEY' => $contractDetailKey]);
        }

        if (!$baseAmount) {
            $baseAmount = $amount;
        }

        if (!$exchRate) {
            $exchRate = "1";
        }

        $processJ1 = false;
        if ($cnDetail['REVENUETEMPLATEKEY'] || ( ContractDetailManager::isKit($cnDetail) && ContractUtil::isShowJournal1() ) ) {
            $processJ1 = true;
            if ($this->forcedJrnlCode && $this->forcedJrnlCode != ContractGLReclassEvent::JOURNALCODE_J1) {
                $processJ1 = false;
            }
        }

        $processJ2 = false;
        if ($cnDetail['REVENUE2TEMPLATEKEY'] || ( ContractDetailManager::isKit($cnDetail) && ContractUtil::isShowJournal2() ) ) {
            $processJ2 = true;
            if ($this->forcedJrnlCode && $this->forcedJrnlCode != ContractGLReclassEvent::JOURNALCODE_J2) {
                $processJ2 = false;
            }
        }

        if ($processJ1) {
            $this->rePostContractGLReclassOnCreate($contractDetailKey, $amount, $baseAmount, $exchRate, $transactionDate, ContractGLReclassEvent::JOURNALCODE_J1);
        }

        if ($processJ2) {
            $this->rePostContractGLReclassOnCreate($contractDetailKey, $amount, $baseAmount, $exchRate, $transactionDate, ContractGLReclassEvent::JOURNALCODE_J2);
        }
    }

    /**
     * Deletes balance records for contract line
     *
     * @param   int     $contractDetailKey  Contract detail key
     *
     * @throws IAException
     */
    public function onDelete($contractDetailKey)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $cnDetail = $this->getContractDetail($contractDetailKey);

        // Allow delete only if there are 2 contractresolve records (AR and Revenue)
        $maxCount = 2;
        
        // For kits allow 4 contractresolve records
        if (ContractDetailManager::isKitOrComponent($cnDetail)) {
            $maxCount = 4;
        }

        if ($cnDetail['REVENUESCHEDULEKEY'] || ContractDetailManager::isKit($cnDetail)) {
            $count = $this->getResolveCount($contractDetailKey, ContractGLReclassEvent::JOURNALCODE_J1);
            if ($count <= $maxCount) {
                $this->deleteResolveRecordsAndGLBatches($contractDetailKey, ContractGLReclassEvent::JOURNALCODE_J1);
            } else {
                $msg = "Contract line ".$cnDetail['LINENO']." has posted transactions.";
                throw IAException::newIAException( 'CN-1558', $msg, ['CN_DETAIL_LINENO' => $cnDetail['LINENO']]);
            }
        }

        if ($cnDetail['REVENUE2SCHEDULEKEY'] || ContractDetailManager::isKit($cnDetail)) {
            $count = $this->getResolveCount($contractDetailKey, ContractGLReclassEvent::JOURNALCODE_J2);
            if ($count <= $maxCount) {
                $this->deleteResolveRecordsAndGLBatches($contractDetailKey, ContractGLReclassEvent::JOURNALCODE_J2);
            } else {
                $msg = "Contract line ".$cnDetail['LINENO']." has posted transactions.";
                throw IAException::newIAException( 'CN-1558', $msg, ['CN_DETAIL_LINENO' => $cnDetail['LINENO']]);
            }
        }
    }

    /**
     * Does gl re-class on Invoice generation
     *
     * @param   int     $contractBillSchEntryKey    Billing schedule entry key
     * @param   string  $transactionDate            Actual transaction date
     * @param   bool    $redoing                    'true' when re-executing the event
     * @param   int     $contractDetailKey          Contract detail key
     *
     * @throws IAException
     */
    public function onInvoice($contractBillSchEntryKey, $transactionDate, $redoing=false, $contractDetailKey=null)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        // Get billing schedule entry record.

        $entry = ContractUtil::getBillingScheduleEntryById($contractBillSchEntryKey);

        $qry =
            "select rs.type, sum(csr.amount) amount, sum(csr.billbaseamount) baseamount, listagg(rs.contractdetailkey,',') revenuecontractdetailkeys
             from contractschedulesresolve csr
              inner join contractscheduleentry rse on rse.cny#=csr.cny# and rse.record#=csr.revschentrykey
              inner join contractrevenueschedule rs on rs.cny#=rse.cny# and rs.record#=rse.schedulekey
             where csr.cny#=:1 and csr.BILLSCHENTRYKEY=:2 and csr.amount != 0  
             group by rs.type";

        $results = QueryResult([$qry, GetMyCompany(), $contractBillSchEntryKey]);

        $revenueContractDetailKeys = [];

        if (arrayCount($results) > 0) {
            $revBaseAmount = $results[0]['BASEAMOUNT'];
            $revenueContractDetailKeys[$results[0]['TYPE']] = explode(",", $results[0]['REVENUECONTRACTDETAILKEYS']);

            if (arrayCount($results) > 1) {
                $revenueContractDetailKeys[$results[1]['TYPE']] = explode(",", $results[1]['REVENUECONTRACTDETAILKEYS']);

                if ($results[1]['BASEAMOUNT'] != $revBaseAmount) {
                    $msg = "Revenue base amounts are different for the two journals ($revBaseAmount vs " . $results[1]['BASEAMOUNT'];
                    throw IAException::newIAException( 'CN-1559', $msg, ['REV_BASE_AMOUNT' => $revBaseAmount, 'BASEAMOUNT' => $results[1]['BASEAMOUNT']]);
                }
            }
        } else {
            $revBaseAmount = null;
        }

        $glPostingDate = $entry['CNDETAILGLPOSTINGDATE'];

        // Invoice date cannot be prior to Contract detail gl posting date
        if (DateCompare($transactionDate, $glPostingDate) == -1) {
            $msg = "Invoice date ($transactionDate) cannot be prior to Contract detail gl posting date of ".$glPostingDate;
            throw IAException::newIAException( 'CN-1560', $msg, ['TRANSACTION_DATE' => FormatDateForDisplay($transactionDate), 'GL_POSTING_DATE' => FormatDateForDisplay($glPostingDate)]);
        }

        $amount = $entry['AMOUNT'];

        // 0 amount invoice is getting created
        if ($amount == 0) {
            // TODO: Naveen S and/or Niraj needs to review this.
            // DE22750: Only return when there is no link to revenue schedule entries (i.e. the contractschedulesresolve query
            //          above returns no result which means $revBaseAmount is null).  When there are revenue schedule entries
            //          we still need to move balances of those revenue even when $amount is 0.
            if ($revBaseAmount === null) {
                return;
            }
        }

        $exchRate = $entry['EXCHANGE_RATE'];
        if (!$exchRate) {
            $exchRate = "1";
        }

        $postedExchRate = $entry['POSTEDEXCHANGE_RATE'];
        if (!$postedExchRate) {
            $postedExchRate = "1";
        }

        $baseAmount = $revBaseAmount ?? $entry['BASEAMOUNT'];
        if (!$baseAmount) {
            $baseAmount = $amount;
        }

        $postedBaseAmount = $entry['POSTEDBASEAMOUNT'];
        if (!$postedBaseAmount) {
            $postedBaseAmount = $baseAmount;
        }

        $contractKey = $entry['CONTRACTKEY'];

        // $contractDetailKey and $entry['CONTRACTDETAILKEY'] may not match in some MEA scenarios
        if (!$contractDetailKey) {
            $contractDetailKey = $entry['CONTRACTDETAILKEY'];
            LogToFile('CNRECLASS contractDetailKey ASSIGNED FROM entry: ' . $contractDetailKey);
        }

        if ($this->sourceContractDetailKey == null) {
            $this->sourceContractDetailKey = $contractDetailKey;
        }

        $processJ1 = false;
        if ($entry['REVENUETEMPLATEKEY']) {
            //if billing amount is zero and if its giving revenue(revenue possible as negative and positive) to self only then no need to do re-class
            if ($amount == 0 && count(array_diff($revenueContractDetailKeys[ContractSchedule::TYPE_REVENUE_CODE] ?? [],
                                                 [$entry['CONTRACTDETAILKEY']])) == 0) {
                $processJ1 = false;
            } else {

                $processJ1 = true;
                if ($this->forcedJrnlCode && $this->forcedJrnlCode != ContractGLReclassEvent::JOURNALCODE_J1) {
                    $processJ1 = false;
                }
            }
        }

        $processJ2 = false;
        if ($entry['REVENUE2TEMPLATEKEY']) {
            //if billing amount is zero and if its giving revenue(revenue possible as negative and positive) to self only then no need to do re-class
            if ($amount == 0 && count(array_diff($revenueContractDetailKeys[ContractSchedule::TYPE_REVENUE2_CODE] ?? [],
                                                 [$entry['CONTRACTDETAILKEY']])) == 0) {
                $processJ2 = false;
            } else {
                $processJ2 = true;
                if ($this->forcedJrnlCode && $this->forcedJrnlCode != ContractGLReclassEvent::JOURNALCODE_J2) {
                    $processJ2 = false;
                }
            }
        }

        [$processJ1, $processJ2] = $this->getChildComponentProcessJournals($entry, $processJ1, $processJ2);

        if ($processJ1) {
            if (!$redoing) {
                $this->checkForFutureMEAs($contractDetailKey, $transactionDate, ContractGLReclassEvent::JOURNALCODE_J1);
            }

            $noPendingFutureMEAAtTheBeginningJ1 = !$this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J1];

            $this->onInvoiceOuter(
                $contractBillSchEntryKey,
                $transactionDate,
                $contractKey,
                $contractDetailKey,
                $amount,
                $baseAmount,
                $exchRate,
                $postedBaseAmount,
                $postedExchRate,
                ContractGLReclassEvent::JOURNALCODE_J1,
                $redoing
            );

            // This is the first event which started it all
            if ($noPendingFutureMEAAtTheBeginningJ1 && $this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J1]) {
                $this->runFutureMEAEvent(ContractGLReclassEvent::JOURNALCODE_J1, $contractDetailKey);

                $this->runFutureMEASyncedRedoEvents(ContractGLReclassEvent::JOURNALCODE_J1);
            }
    
            if (!$redoing) {
                ContractGLAssertionsUtil::assertInvoiceAmountEqualsReclassAmount($contractKey, ContractGLReclassEvent::JOURNALCODE_J1);
            }
        }

        if ($processJ2) {
            if (!$redoing) {
                $this->checkForFutureMEAs($contractDetailKey, $transactionDate, ContractGLReclassEvent::JOURNALCODE_J2);
            }

            $noPendingFutureMEAAtTheBeginningJ2 = !$this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J2];

            $this->onInvoiceOuter(
                $contractBillSchEntryKey,
                $transactionDate,
                $contractKey,
                $contractDetailKey,
                $amount,
                $baseAmount,
                $exchRate,
                $postedBaseAmount,
                $postedExchRate,
                ContractGLReclassEvent::JOURNALCODE_J2,
                $redoing
            );

            // This is the first event which started it all
            if ($noPendingFutureMEAAtTheBeginningJ2 && $this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J2]) {

                $this->runFutureMEAEvent(ContractGLReclassEvent::JOURNALCODE_J2, $contractDetailKey);

                $this->runFutureMEASyncedRedoEvents(ContractGLReclassEvent::JOURNALCODE_J2);
            }
            
            if (!$redoing) {
                ContractGLAssertionsUtil::assertInvoiceAmountEqualsReclassAmount($contractKey, ContractGLReclassEvent::JOURNALCODE_J2);
            }
        }
    }

    /**
     * Invoice generation re-class with and without MEA
     *
     * @param   int     $contractBillSchEntryKey    Billing schedule entry key
     * @param   string  $transactionDate            Actual transaction date
     * @param   int     $contractKey                Contract key
     * @param   int     $contractDetailKey          Contract detail key
     * @param   float   $amount                     Billing schedule entry amount
     * @param   float   $baseAmount                 Billing schedule entry base amount
     * @param   float   $exchRate                   Exchange rate
     * @param   float   $postedBaseAmount           Invoice base amount
     * @param   float   $postedExchRate             Invoice exchange rate
     * @param   string  $jrnlCode                   J1 or J2
     * @param   bool    $redoing                    'true' when re-executing the event
     *
     * @throws IAException
     */
    protected function onInvoiceOuter($contractBillSchEntryKey, $transactionDate, $contractKey, $contractDetailKey,
                                      $amount, $baseAmount, $exchRate, $postedBaseAmount, $postedExchRate, $jrnlCode,
                                      $redoing)
    {

        if (!$this->eventAlreadyExecuted(
            ContractGLReclassEvent::EVENTTYPE_ONINVOICE,
            $jrnlCode,
            $contractDetailKey,
            $contractBillSchEntryKey
        )) {


            $this->onEventPreProcessing($contractDetailKey, $transactionDate, $jrnlCode);
            $this->dumpMethodCall(__FUNCTION__, func_get_args());
            $orderedEvents = null;

            if (!$this->clearAlreadyExecuted($contractDetailKey, $jrnlCode)) {
                $clearTransactionDate = $transactionDate;
                // Trigger re-evaluation of all future events for the line when redoing events
                if (ContractUtil::isPostConResolveInOpenPeriod()) {
                    //$effectiveDate  is current open period which would be the different from mea date
                    //need to get actual transaction date of MEA to fetchj data from MEA bundle
                    $clearTransactionDate = ContractUtil::getEventActTranDate_ConSubPostInOpenPeriod(
                        ContractGLReclassEvent::EVENTTYPE_ONINVOICE,
                        $jrnlCode,
                        ['BILLINGSCHENTRYKEY' => $contractBillSchEntryKey],
                        $transactionDate
                    );
                }
                // Delete self along with future events when redoing unless it is source contract detail
                if ($redoing) {
                    $identifierAttributes = [
                        'EVENTTYPE' => [ ContractGLReclassEvent::EVENTTYPE_ONINVOICE,  ContractGLReclassEvent::EVENTTYPE_ONPAYMENT],
                        'BILLINGSCHENTRYKEY' => $contractBillSchEntryKey,
                    ];

                    $this->clearExistingReclassTransactionsIncludingSelf(
                        $identifierAttributes,
                        $contractDetailKey,
                        $clearTransactionDate,
                        $jrnlCode,
                        $orderedEvents
                    );

                } else {
                    $this->clearExistingReclassTransactions(
                        $clearTransactionDate,
                        $contractDetailKey,
                        $jrnlCode,
                        $orderedEvents
                    );
                }
            }

            $runReclass = true;
            // Check if MEA exists in the past
            if (ContractUtil::contractDetailHasMEA($contractDetailKey, $jrnlCode, $transactionDate)) {

                // If future also has a MEA then schedules resolve as of invoice date is not correct
                // We need to fetch distribution from future MEA's blob contents
                if ($orderedEvents) {

                    $this->processFutureEventsForMEA($contractDetailKey, $jrnlCode, $orderedEvents, $hasFutureMEA);
                }

                // Keep local state of schedule links updated for future MEA to correctly undo of current MEA
                // Just presense means we are operating on old MEA
                if ($this->scheduleLinksToUse[$jrnlCode] ?? false) {
                    $this->updateLocalScheduleLinks($jrnlCode, null, $contractBillSchEntryKey, null, true, null);
                }

                $newAmountsResult = [];
                $newAmountsResultMap = [];

                $this->getMEADistribution($contractBillSchEntryKey, $jrnlCode, $newAmountsResult, $newAmountsResultMap);

                //this will make sure that id billing is not sharing revenue then do not call payment inner mea
                if ($this->isBillingDistributing($contractBillSchEntryKey, $newAmountsResultMap)) {

                    if (ContractGLEventRegistry::registerLinkedReclass($redoing) && !$this->eventAlreadyExecuted(
                            ContractGLReclassEvent::EVENTTYPE_ONINVOICE_REGISTRATION,
                            $jrnlCode, '',
                            $contractBillSchEntryKey
                        )) {

                        $this->recordEvent(
                            ContractGLReclassEvent::EVENTTYPE_ONINVOICE_REGISTRATION,
                            $jrnlCode, $amount, '',
                            $contractBillSchEntryKey
                        );

                        foreach ($newAmountsResult as $eachResult) {
                            $contractDetailKeyForNextRelass = $eachResult['CONTRACTDETAILKEY'];
                            if ($contractDetailKeyForNextRelass != $contractDetailKey) {
                                ContractGLEventRegistry::registerOtherLinesForReclassInRegistry(
                                    $contractDetailKeyForNextRelass,
                                    __FUNCTION__,
                                    $jrnlCode,
                                    [
                                        'BILLSCHEDULEENTRYKEY' => $contractBillSchEntryKey,
                                        'TRANSACTIONDATE' => $transactionDate,
                                        'CONTRACTKEY' => $contractKey,
                                        'AMOUNT' => $amount,
                                        'BASEAMOUNT' => $baseAmount,
                                        'EXCHRATE' => $exchRate,
                                        'POSTEDBASEAMOUNT' => $postedBaseAmount,
                                        'POSTEDEXCHRATE' => $postedExchRate,
                                        'JRNLCODE' => $jrnlCode,
                                        'REDOING' => $redoing,
                                        'CONTRACTDETAILKEY' => $contractDetailKeyForNextRelass
                                    ]
                                );
                            }
                        }
                    }


                    $this->onInvoiceMEAInner(
                        $contractBillSchEntryKey,
                        $transactionDate,
                        $contractKey,
                        $contractDetailKey,
                        $newAmountsResult,
                        $postedExchRate,
                        $jrnlCode,
                        $redoing
                    );
                }
                $result = $newAmountsResultMap[$contractDetailKey] ?? [];

                $amount = $result['AMOUNT'] ?? 0;
                $baseAmount = $result['BASEAMOUNT'] ?? 0;
                $postedBaseAmount = ibcmul($amount, $postedExchRate, ContractUtil::AMOUNT_PRECISION, true);

                if ($amount == 0) {
                    $runReclass = false;
                }
            }


            if ($runReclass) {
                $this->onInvoiceInner(
                    $contractBillSchEntryKey,
                    $transactionDate,
                    $contractKey,
                    $contractDetailKey,
                    $amount,
                    $baseAmount,
                    $exchRate,
                    $postedBaseAmount,
                    $postedExchRate,
                    $jrnlCode,
                    $redoing
                );
            }

            if ($orderedEvents && count($orderedEvents) > 0) {
                // Important to run reclass for other invoices and payments for this journal alone
                $this->forcedJrnlCode = $jrnlCode;

                $this->redoEvents($orderedEvents, $contractDetailKey, $jrnlCode);
            }

            if (!$orderedEvents && !$redoing) {
                $this->runNextContractDetailReclass($jrnlCode);
            }
        }
    }

    /**
     * Invoice generation re-class event handler
     *
     * @param   int     $contractBillSchEntryKey    Billing schedule entry key
     * @param   string  $transactionDate            Actual transaction date
     * @param   int     $contractKey                Contract key
     * @param   int     $contractDetailKey          Contract detail key
     * @param   float   $amount                     Billing schedule entry amount
     * @param   float   $baseAmount                 Billing schedule entry base amount
     * @param   float   $exchRate                   Exchange rate
     * @param   float   $postedBaseAmount           Invoice base amount
     * @param   float   $postedExchRate             Invoice exchange rate
     * @param   string  $jrnlCode                   J1 or J2
     * @param   bool    $redoing                    true or false
     *
     * @throws IAException
     */
    protected function onInvoiceInner($contractBillSchEntryKey, $transactionDate, $contractKey, $contractDetailKey,
                                      $amount, $baseAmount, $exchRate, $postedBaseAmount, $postedExchRate, $jrnlCode, $redoing)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $this->recordEvent(
            ContractGLReclassEvent::EVENTTYPE_ONINVOICE,
            $jrnlCode,
            $amount,
            $contractDetailKey,
            $contractBillSchEntryKey
        );

        // Get 'historical flag' against billing schedule entry.

        $entry = ContractUtil::getBillingScheduleEntryById($contractBillSchEntryKey);

        if (isArrayValueTrue($entry, 'HISTORICAL')) {
            // 'Historical flag' is set against billing schedule entry.
            // ACP reclass 'On Invoice' event.
            // Move revenue from Unbilled to Paid bucket by skipping Billed bucket.

            $historicalFlag = 'true';
        }
        else {
            // Normal Reclass 'On Invoice' event.
            // Move revenue from Unbilled to Billed bucket.

            $historicalFlag = 'false';
        }

        $event = new ContractGLReclassOnInvoice(
            $contractBillSchEntryKey,
            $amount,
            $baseAmount,
            $postedBaseAmount,
            $exchRate,
            $postedExchRate,
            $transactionDate,
            $contractKey,
            $contractDetailKey,
            $jrnlCode,
            $historicalFlag
        );

        $event->setRedoing($redoing);
        $event->execute();
        // Assert on final JEs collected so far

        if($event->getHistoricalFlag() == 'false') {
            ContractGLAssertionsUtil::assert(
                $amount,
                ContractGLReclassEvent::EVENTTYPE_ONINVOICE,
                $jrnlCode,
                $contractDetailKey,
                $contractBillSchEntryKey
            );
        }
        ContractGLAssertionsUtil::clearTransactionJEs(ContractGLReclassEvent::EVENTTYPE_ONINVOICE, $jrnlCode , $contractDetailKey, $contractBillSchEntryKey);
    }

    /**
     * Invoice creation after MEA
     *
     * @param   int     $contractBillSchEntryKey    Billing schedule entry key
     * @param   string  $transactionDate            Actual transaction date
     * @param   int     $contractKey                Contract key
     * @param   int     $contractDetailKey          Contract detail key
     * @param   float   $exchRate                   Exchange rate
     * @param   float   $postedExchRate             Posted exchange rate
     * @param   bool    $jrnlCode                   J1 or J2
     * @param   bool    $redoing                    'true' when re-executing the event
     *
     * @throws IAException
     */
    protected function onInvoiceMEAInner($contractBillSchEntryKey, $transactionDate, $contractKey, $contractDetailKey,
                                         $newAmountsResult, $postedExchRate, $jrnlCode, $redoing)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $entry = ContractUtil::getBillingScheduleEntryById($contractBillSchEntryKey);

        if (isArrayValueTrue($entry, 'HISTORICAL')) {
            // 'Historical flag' is set against billing schedule entry.
            // ACP reclass 'On Invoice MEA' event.
            // Skip Billed accounting for ACP reclass.
            $historicalFlag = 'true';
        }
        else {
            // Normal Reclass 'On Invoice MEA' event.
            $historicalFlag = 'false';
        }

        // Adjust wrongly posted JEs by invoice for current contract line
        $event = new ContractGLReclassOnInvoiceMEA(
            $contractBillSchEntryKey,
            $newAmountsResult,
            $transactionDate,
            $contractKey,
            $contractDetailKey,
            $postedExchRate,
            $jrnlCode,
            $historicalFlag
        );

        $event->setRedoing($redoing);
        $event->execute();


    }

    /**
     * Resolves amount for prentry key
     *
     * @param   int    $prEntryKey  PR entry key
     *
     * @return string
     *
     * @throws IAException
     */
    protected function getPREntryAmount($prEntryKey)
    {
        $qry = "SELECT trx_amount, amount FROM prentry WHERE cny#=:1 AND record#=:2";
        $prEntryRec = QueryResult([$qry, GetMyCompany(), $prEntryKey]);

        if (!($prEntryRec && $prEntryRec[0])) {
            $msg = "Invalid Payment PR Entry key:".$prEntryKey;
            throw IAException::newIAException( 'CN-1561', $msg, ['PR_ENTRY_KEY' => $prEntryKey]);
        }

        return $prEntryRec[0]['TRX_AMOUNT'] ?: $prEntryRec[0]['AMOUNT'];
    }

    /**
     * Returns split amount by contract line according to MEA distribution
     *
     * @param float $amount Amount to split
     * @param int $billingScheduleEntryKey Contract billing schedule entry
     * @param string $jrnlCode J1 or J2
     * @param string $transactionDate
     *
     * @return array
     * @throws IAException
     */
    protected function splitAmountByMEADistribution($amount, $billingScheduleEntryKey, $jrnlCode, string $transactionDate): array
    {
        $this->getMEADistribution($billingScheduleEntryKey, $jrnlCode, $amountsByCNLine, $amountsByCNLineMap);

        $totalAmount = 0;
        foreach($amountsByCNLine as $amountDetails) {
            $totalAmount = ibcadd($totalAmount, $amountDetails['AMOUNT']);
        }

        /**
         * Partial payment handling start
         */
        $originalContractDetailKeyOfBilling = ContractUtil::getBillingScheduleEntryById($billingScheduleEntryKey)['CONTRACTDETAILKEY'];
        $lastMEAEffectiveDateInCnResolve = ContractUtil::getLastMEAEffectiveDateInCnResolve($originalContractDetailKeyOfBilling, $jrnlCode);
        $billingEntryAmount = ContractUtil::getBillingScheduleEntryById($billingScheduleEntryKey)['AMOUNT'];
        
        //$billingEntryAmount !=$amount -> IF THIS IS PARTIAL PAYMENT
        if ($lastMEAEffectiveDateInCnResolve && DateCompare($lastMEAEffectiveDateInCnResolve, $transactionDate) <= 0  && $billingEntryAmount !=$amount ) {
            $this->handlePartialPaymentBeforeMEA($billingScheduleEntryKey, $originalContractDetailKeyOfBilling, $lastMEAEffectiveDateInCnResolve, $jrnlCode, $amountsByCNLine, $totalAmount);
        }
        /**
         * Partial payment handling end
         */
        
        

        $newAmounts = [];
        $runningTotal = 0;
        $arr_size = count($amountsByCNLine);
        foreach($amountsByCNLine as $idx => $amountDetails) {
            $cnDetailKey = $amountDetails['CONTRACTDETAILKEY'];

            if ($idx == $arr_size-1) {

                $newAmount = ibcsub($amount, $runningTotal);
                $newAmounts[$cnDetailKey] = $newAmount;

            } else {

                $newAmount = ContractUtil::computeAmountRatio(
                    $amount,
                    $amountDetails['AMOUNT'],
                    $totalAmount
                );

                $newAmounts[$cnDetailKey] = $newAmount;
                $runningTotal = ibcadd($runningTotal, $newAmount);
            }
        }

        return $newAmounts;
    }

    /**
     * @param int $billingScheduleEntryKey
     * @param int $originalContractDetailKeyOfBilling
     * @param string $lastMEAEffectiveDateInCnResolve
     * @param string $jrnlCode
     * @param array $amountsByCNLine
     * @param float $totalAmount
     *
     * @throws IAException
     */
    protected function handlePartialPaymentBeforeMEA(
        $billingScheduleEntryKey,
        $originalContractDetailKeyOfBilling,
        $lastMEAEffectiveDateInCnResolve,
        $jrnlCode,
        &$amountsByCNLine,
        &$totalAmount
    ): void {
        $billingEntryAmount = ContractUtil::getBillingScheduleEntryById($billingScheduleEntryKey)['AMOUNT'];

        // get the partial paid amount for the billing schedule entry done before last MEA present in the cn resolve
        $amountPaidBeforeMEAByBillingKey =
            ContractUtil::getPaymentAmountBeforeLastMEAByBilling($billingScheduleEntryKey, $originalContractDetailKeyOfBilling, $jrnlCode);

        //there is possible that there is no partial payment done before MEA
        if ($amountPaidBeforeMEAByBillingKey != 0.0) {
            $amountToBePaidAfterMEAByBillingKey = (string) ibcsub($billingEntryAmount, $amountPaidBeforeMEAByBillingKey);
            $totalUnPaidToGivenToDestination = 0;

            // Get the unpaid amount for billing entry for the distributed Line
            $cnMeaInstructionMgr = Globals::$g->gManagerFactory->getManager('contractmeainstruction');
            $params = [
                'selects' => ['AMOUNT', 'BILLINGSCHENTRYKEY', 'CONTRACTDETAILKEY', 'ISSOURCE'],
                'filters' => [
                    [
                        ['BILLINGSCHENTRYKEY', '=', $billingScheduleEntryKey],
                        ['CLASSIFICATION', '=', ContractGLReclassEvent::CLASSIFICATION_BILLED],
                        ['EFFECTIVEDATE', '=', $lastMEAEffectiveDateInCnResolve],
                    ],
                ],
            ];
            $cnMeaInstruction = $cnMeaInstructionMgr->GetList($params);

            if (!empty($cnMeaInstruction)) {
                $unPaidBillingEntryAmountByLine = array_column($cnMeaInstruction, null, 'CONTRACTDETAILKEY');

                //It is is possible that $amountsByCNLine contain the amount by billing which is paid. This will happen whne partial payment is done before MEA.
                //So, update the unpaid amount  obtain from contract mea instruction.
                foreach ($amountsByCNLine as &$lineLine) {
                    $contractDetailKey = $lineLine['CONTRACTDETAILKEY'];

                    if ($unPaidBillingEntryAmountByLine[$contractDetailKey]['AMOUNT'] < 0) {
                        //if amount is negative then source ve rerse as per mea instruction
                        if ($unPaidBillingEntryAmountByLine[$contractDetailKey]['ISSOURCE'] == 'false') {
                            $unPaidBillingEntryAmountByLine[$contractDetailKey]['ISSOURCE'] = 'true';
                        } else {
                            $unPaidBillingEntryAmountByLine[$contractDetailKey]['ISSOURCE'] = 'false';
                        }
                    }

                    if (
                        isset($unPaidBillingEntryAmountByLine[$contractDetailKey])
                        && $contractDetailKey != $originalContractDetailKeyOfBilling
                    ) {
                        ContractUtil::assert($unPaidBillingEntryAmountByLine[$contractDetailKey]['ISSOURCE'] == 'false');

                        $lineLine['AMOUNT'] = $unPaidBillingEntryAmountByLine[$contractDetailKey]['AMOUNT'];
                        $totalUnPaidToGivenToDestination = ibcadd($totalUnPaidToGivenToDestination, $lineLine['AMOUNT']);
                    }
                }
                unset($lineLine);

                foreach ($amountsByCNLine as &$lineLine) {
                    $contractDetailKey = $lineLine['CONTRACTDETAILKEY'];
                    if ($contractDetailKey == $originalContractDetailKeyOfBilling) {
                        // Unpaid amount of original line of billing entry
                        $lineLine['AMOUNT'] =
                            ibcsub(ibcsub($billingEntryAmount, $amountPaidBeforeMEAByBillingKey), $totalUnPaidToGivenToDestination);
                    }
                }
                unset($lineLine);

                $totalAmount = (float) $amountToBePaidAfterMEAByBillingKey;
            }
        }
    }

    /**
     * Returns MEA distribution by contract line
     *
     * @param   int     $contractBillSchEntryKey    Contract billing schedule entry
     * @param   string  $jrnlCode                   J1 or J2
     * @param   array   $newAmountsResult           New amounts with contract detail
     * @param   array   $newAmountsResultMap        New amounts by contract detail
     */
    protected function getMEADistribution($contractBillSchEntryKey, $jrnlCode, &$newAmountsResult,&$newAmountsResultMap)
    {
        if (!$this->meaDistributionCache[$jrnlCode][$contractBillSchEntryKey]) {

            $results = ContractGLReclassOnMEA::getMEADistribution($jrnlCode, $contractBillSchEntryKey);

            $this->meaDistributionCache[$jrnlCode][$contractBillSchEntryKey] = $results;
        }

        // Generate map from results
        if ($this->meaDistributionCache[$jrnlCode][$contractBillSchEntryKey]
                && !$this->meaDistributionCache[$jrnlCode][$contractBillSchEntryKey."_map"]) {

            $map = [];
            foreach ($this->meaDistributionCache[$jrnlCode][$contractBillSchEntryKey] as $result) {
                $map[$result['CONTRACTDETAILKEY']] = $result;
            }
            $this->meaDistributionCache[$jrnlCode][$contractBillSchEntryKey."_map"] = $map;
        }

        $newAmountsResult = $this->meaDistributionCache[$jrnlCode][$contractBillSchEntryKey];
        $newAmountsResultMap = $this->meaDistributionCache[$jrnlCode][$contractBillSchEntryKey."_map"];
        ContractGLAssertionsUtil::collectAssertedMEADistributionCache($this->meaDistributionCache);
    }

    /**
     * Checks if MEA exists on a future date
     *
     * @param   int     $contractDetailKey  Contract detail key
     * @param   string  $jrnlCode           Journal code
     * @param   string  $transactionDate    Transaction date
     * @param   string  $meaDate            MEA effective date
     *
     * @return bool
     */
    protected function contractDetailHasFutureMEA($contractDetailKey, $jrnlCode, $transactionDate, &$meaDate)
    {
        $hasMEA = false;
        $eventType = self::getEventType((int) $contractDetailKey);

        $qry = "
            SELECT MIN(record#) meacount FROM contractresolve WHERE cny#=:1 AND contractdetailkey = :2 AND eventtype = :3
             AND journaltype = :4 AND transactiondate > :5
        ";

        $qryOuter = "SELECT transactiondate FROM contractresolve WHERE record# IN ($qry) AND cny#=:1";
        $qparams = [$qryOuter, GetMyCompany(), $contractDetailKey, $eventType, $jrnlCode, $transactionDate];

        $res = QueryResult($qparams);
        if ($res !== false && $res[0]['TRANSACTIONDATE'] !== null) {
            $hasMEA = true;
            $meaDate = $res[0]['TRANSACTIONDATE'];
        }

        return $hasMEA;
    }


    /**
     * Re-calculates re-class on Invoice deletion
     *
     * @param int $contractBillSchEntryKey Billing schedule entry key
     *
     * @param array|null $allDeletedInvoiceBillSchEntryKeys
     * @throws IAException
     */
    public function onInvoiceDelete($contractBillSchEntryKey, $allDeletedInvoiceBillSchEntryKeys=null)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $schEntry = ContractUtil::getBillingScheduleEntryById($contractBillSchEntryKey);

        if (empty($schEntry['AMOUNT'])){
            // There is NO affect on balances
            return;
        }

        $contractDetailKey = $schEntry['CONTRACTDETAILKEY'];
        $contractKey = $schEntry['CONTRACTKEY'];
        $invoiceDate = $schEntry['ACTUALPOSTINGDATE'];

        if ($this->sourceContractDetailKey == null) {
            $this->sourceContractDetailKey = $contractDetailKey;
        }
    
        $processJ1 = !empty($schEntry['REVENUETEMPLATEKEY']);
        $processJ2 = !empty($schEntry['REVENUE2TEMPLATEKEY']);
        [$processJ1, $processJ2] = $this->getChildComponentProcessJournals($schEntry, $processJ1, $processJ2);
    
        if ($processJ1) {
            $this->checkForFutureMEAs($contractDetailKey, $invoiceDate, ContractGLReclassEvent::JOURNALCODE_J1);

            $this->onInvoiceDeleteOuter(
                $contractBillSchEntryKey,
                $contractKey,
                $contractDetailKey,
                $invoiceDate,
                ContractGLReclassEvent::JOURNALCODE_J1,
                $allDeletedInvoiceBillSchEntryKeys
            );

            // This is the first event which started it all
            if ($this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J1]) {

                $this->runFutureMEAEvent(ContractGLReclassEvent::JOURNALCODE_J1, $contractDetailKey);

                $this->runFutureMEASyncedRedoEvents(ContractGLReclassEvent::JOURNALCODE_J1);
            }
        }

        if ($processJ2) {
            $this->checkForFutureMEAs($contractDetailKey, $invoiceDate, ContractGLReclassEvent::JOURNALCODE_J2);

            $this->onInvoiceDeleteOuter(
                $contractBillSchEntryKey,
                $contractKey,
                $contractDetailKey,
                $invoiceDate,
                ContractGLReclassEvent::JOURNALCODE_J2,
                $allDeletedInvoiceBillSchEntryKeys
            );

            // This is the first event which started it all
            if ($this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J2]) {

                $this->runFutureMEAEvent(ContractGLReclassEvent::JOURNALCODE_J2, $contractDetailKey);

                $this->runFutureMEASyncedRedoEvents(ContractGLReclassEvent::JOURNALCODE_J2);
            }
        }
    }

    /**
     * Re-executes events on invoice delete for a journal. Takes care of MEA
     *
     * @param int $contractBillSchEntryKey Billing schedule entry key
     * @param int $contractKey Contract key
     * @param string $contractDetailKey Contract detail key
     * @param string $invoiceDate Invoice date
     * @param string $jrnlCode J1 or J2
     *
     * @param array|null $allDeletedInvoiceBillSchEntryKeys
     * @throws IAException
     * @retun void
     *
     */
    public function onInvoiceDeleteOuter($contractBillSchEntryKey, $contractKey, $contractDetailKey, $invoiceDate,
                                              $jrnlCode, $allDeletedInvoiceBillSchEntryKeys=null)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        if (ContractUtil::contractDetailHasMEA($contractDetailKey, $jrnlCode, $invoiceDate)) {

            $affectedCNDKeys = $this->getUniqueContractDetailKeys($jrnlCode, $contractBillSchEntryKey);

            // Trigger redo for all lines including self
            foreach ($affectedCNDKeys as $cndKey) {
                $this->onInvoiceDeleteInner($contractBillSchEntryKey, $cndKey, $jrnlCode, true, $allDeletedInvoiceBillSchEntryKeys);
            }

        } else {
            $this->onInvoiceDeleteInner($contractBillSchEntryKey, $contractDetailKey, $jrnlCode, false, $allDeletedInvoiceBillSchEntryKeys);
        }
    }

    /**
     * Re-executes events on invoice delete for a journal
     *
     * @param int $contractBillSchEntryKey Billing schedule entry key
     * @param string $contractDetailKey Contract detail key
     * @param string $jrnlCode J1 or J2
     * @param bool $hasMEA
     *
     * @param array|null $allDeletedInvoiceBillSchEntryKeys
     * @throws IAException
     * @retun void
     *
     */
    public function onInvoiceDeleteInner($contractBillSchEntryKey, $contractDetailKey, $jrnlCode, $hasMEA=false, $allDeletedInvoiceBillSchEntryKeys= null)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        // 1. Get the starting resolve record associated with this billing sch. entry
        $startResolveRec = $this->getStartResolveRecOnInvoiceDelete(
            $contractBillSchEntryKey,
            $contractDetailKey,
            $jrnlCode
        );

        if ($startResolveRec == -1) {
            return;
        }

        // 2. Get all resolve records with unique events ang re-class gl batches
        if(!in_array($contractBillSchEntryKey, $allDeletedInvoiceBillSchEntryKeys)){
            $allDeletedInvoiceBillSchEntryKeys[] = $contractBillSchEntryKey;
        }
        $this->getEventsInOrderOnInvoiceDelete(
            $startResolveRec,
            $allDeletedInvoiceBillSchEntryKeys,
            $contractDetailKey,
            $jrnlCode,
            $orderedEvents,
            $reclassGLBatches
        );

        // 3. Delete resolves
        $this->deleteResolve($startResolveRec, $contractDetailKey, $jrnlCode, ['A', 'R']);

        // 4. Explicitly delete re-class glbatchs
        if (!empty($reclassGLBatches)) {
            $this->deleteGLBatches($reclassGLBatches);
        }

        $this->setExecutedClearTransactions($contractDetailKey, $jrnlCode, $orderedEvents);

        // If line has MEA in the past
        if ($hasMEA && $orderedEvents && ContractUtil::contractDetailHasMEA($contractDetailKey, $jrnlCode)) {
            $this->processFutureEventsForMEA($contractDetailKey, $jrnlCode, $orderedEvents, $hasFutureMEA);

            if ($hasFutureMEA) {
                $this->updateLocalScheduleLinks($jrnlCode, null, $contractBillSchEntryKey, null, false, null);
            }
        }

        if (!empty($orderedEvents) ) {

            $this->forcedJrnlCode = $jrnlCode;

            // 5. Re-calculate resolves
            $this->redoEvents($orderedEvents, $contractDetailKey, $jrnlCode);
        }
    }

    /**
     * Does gl re-class on recognition
     *
     * @param   int     $contractRevSchEntryKey     Revenue schedule entry key
     * @param   string  $transactionDate            Actual transaction date
     * @param   bool    $redoing                    'true' when re-executing the event
     * @param   int     $contractDetailKey          Contract detail key
     *
     * @throws IAException
     */
    public function onRecognition($contractRevSchEntryKey, $transactionDate, $redoing=false, $contractDetailKey=null)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $entry = $this->getRevenueScheduleEntry($contractRevSchEntryKey);

        // Recognition date cannot be prior to Contract gl posting date
        if (DateCompare($transactionDate, $entry['CNDETAILGLPOSTINGDATE']) == -1) {
            $msg = "Recognition date ($transactionDate) cannot be prior to Contract detail gl posting date of " .
                   $entry['CNDETAILGLPOSTINGDATE'];
            throw IAException::newIAException( 'CN-1562', $msg,  ['TRANSACTION_DATE' => FormatDateForDisplay($transactionDate), 'ENTRY_CNDETAILGLPOSTINGDATE' => FormatDateForDisplay($entry['CNDETAILGLPOSTINGDATE'])]);
        }

        $revenueType = $entry['TYPE'];
        $contractKey = $entry['CONTRACTKEY'];
        $contractId = $entry['CONTRACT.CONTRACTID'];
        $scheduledPostingDate = $entry['POSTINGDATE'];

        $jrnlCode = ($revenueType == ContractSchedule::TYPE_REVENUE) ?
            ContractGLReclassEvent::JOURNALCODE_J1 : ContractGLReclassEvent::JOURNALCODE_J2;

        // Ignoring passed $contractDetailKey, since it will never differ from revenue schedule entry's detail key
        $contractDetailKey = $entry['CONTRACTDETAILKEY'];

        $this->onEventPreProcessing($contractDetailKey, $transactionDate, $jrnlCode);

        if ($this->contractDetailHasFutureMEA($contractDetailKey, $jrnlCode, $transactionDate, $futureMEADate)) {

            // In such cases if scheduled date was before MEA effective date its ok to allow posting
            // otherwise attempt is to post something which was due in future wrt MEA effective in the past
            if (DateCompare($scheduledPostingDate, $futureMEADate) >= 0) {
                $msg = "This revenue amount is an adjustment resulting from the MEA allocation effective $futureMEADate and can't be posted prior to that date. Specify a posting date on or after $futureMEADate and try posting the revenue again.";
                throw IAException::newIAException( 'CN-1563', $msg, ['FUTURE_DATE' => FormatDateForDisplay($futureMEADate), 'FUTURE_MEA_DATE' => FormatDateForDisplay($futureMEADate)]);
            }
        }

        $origPostingDate = $entry['POSTINGDATE'];

        if ($this->sourceContractDetailKey == null) {
            $this->sourceContractDetailKey = $contractDetailKey;
        }

        $cnDetailLineType = $entry['CONTRACTDETAIL.LINETYPE'];
        $amount = $entry['AMOUNT'];
        $baseAmount = $entry['BASEAMOUNT'];
        $exchRate = !$entry['EXCHANGE_RATE'] ? 1 : $entry['EXCHANGE_RATE'];

        if ($this->forcedJrnlCode && $this->forcedJrnlCode != $jrnlCode) {
            $msg = "Re-class for $jrnlCode is triggered when we are executing re-class for ".
                   $this->forcedJrnlCode. ". This is not expected.";
            throw IAException::newIAException( 'CN-1564', $msg, ['JRNL_CODE' => $jrnlCode, 'FORCED_JRNL_CODE' => $this->forcedJrnlCode]);
        }

        if (!$this->forcedJrnlCode) {
            $this->forcedJrnlCode = $jrnlCode;
        }

        if (!$redoing) {
            $this->checkForFutureMEAs($contractDetailKey, $transactionDate, $jrnlCode);
        }

        if (ContractUtil::contractDetailHasMEA($contractDetailKey, $this->forcedJrnlCode)) {
            // Posting date cannot be before linked BSE's line's create date
            if(!$redoing) {
                $linkedMaxStartDate = $this->getLinkedLineMaxStartDate($contractRevSchEntryKey, $lineno);
                if ($linkedMaxStartDate && DateCompare($linkedMaxStartDate, $transactionDate) > 0) {
                    $msg = "Revenue ($contractRevSchEntryKey) is linked to $contractId line $lineno, which is has a contract line start date of $linkedMaxStartDate. The revenue posting date must to be on or after $linkedMaxStartDate.";
                    throw IAException::newIAException('CN-1565', $msg, ['CONTRACT_REV_SCH_ENTRY_KEY' => $contractRevSchEntryKey, 'CONTRACT_ID' => $contractId, 'LINENO' => $lineno,
                                                                        'LINKED_MAX_START_DATE' => FormatDateForDisplay($linkedMaxStartDate)]);
                }
            }

            $meaDate = ContractUtil::getLastMEAEffectiveDateInCnResolve($contractDetailKey, $this->forcedJrnlCode);

            // If original posting date is before MEA effective date, then transaction date cannot be on or after it
            if ($meaDate && DateCompare($origPostingDate, $meaDate) < 0 && DateCompare($transactionDate, $meaDate) > 0){
                $contractID = $entry['CONTRACT.CONTRACTID'];
                $lineNo = $entry['CONTRACTDETAIL.LINENO'];
                $msg = "The revenue entry for $contractID - line $lineNo that is scheduled to post on $origPostingDate " .
                       "is associated with an MEA allocation dated $meaDate. The posting date for this entry must be " .
                       "on or before $meaDate. It cannot be posted on $transactionDate.";
                throw IAException::newIAException( 'CN-1566', $msg, ['CONTRACTID' => $contractID, 'LINE_NO' => $lineNo, 'ORIG_POSTING_DATE' => FormatDateForDisplay($origPostingDate),
                                                                    'MEA_DATE' => FormatDateForDisplay($meaDate), 'TRANSACTION_DATE' => FormatDateForDisplay($origPostingDate)]);
            }
        }

        $clearTransactionDate = $transactionDate;
        if (ContractUtil::isPostConResolveInOpenPeriod()) {
            //$effectiveDate  is current open period which would be the different from mea date
            //need to get actual transaction date of MEA to fetchj data from MEA bundle
            $clearTransactionDate =  ContractUtil::getEventActTranDate_ConSubPostInOpenPeriod(ContractGLReclassEvent::EVENTTYPE_ONRECOGNITION, $jrnlCode, ['REVENUESCHENTRYKEY' => $contractRevSchEntryKey ] , $transactionDate);
        }

        $this->clearExistingReclassTransactions(
            $clearTransactionDate,
            $contractDetailKey,
            $this->forcedJrnlCode,
            $orderedEvents
        );

        $cnDetailSign = $cnDetailLineType == ContractDetailManager::LINETYPE_REGULAR_DB ? 1 : -1;
        $rseSign = $amount < 0 ? -1 : 1;

        $eventClass = ContractGLReclassOnRecognition::class;

        // Scenario where line amount is positive and rev. schedule entry amount is negative or vice versa
        // It is more common scenario in project rev. rec.
        if ($cnDetailSign != $rseSign) {
            $eventClass = ContractGLReclassOnUndoRecognition::class;

            // Make schedule entry's sign same as contract detail's sign
            $amount = -1 * $amount;
            $baseAmount = -1 * $baseAmount;
        }

        $noPendingFutureMEAAtTheBeginning = !$this->futureMEAEvent[$jrnlCode];

        // Has MEA status flag. // check MEA in past
        $hasMEAFlag = ContractUtil::contractDetailHasMEA($contractDetailKey, $jrnlCode);

        if (isArrayValueTrue($entry, 'HISTORICAL')) {
            // 'Historical flag' is set against revenue schedule entry.
            // ACP reclass 'On Recognition' event.
            // Skip Unbilled and Billed accounting for ACP reclass.

            $historicalFlag = 'true';
        }
        else {
            // Normal Reclass 'On Recognition' event.

            $historicalFlag = 'false';
        }

        /** @var ContractGLReclassOnRecognition $event */
        $event = new $eventClass(
            $contractRevSchEntryKey,
            $transactionDate,
            $revenueType,
            $contractKey,
            $contractDetailKey,
            $amount,
            $baseAmount,
            $exchRate,
            $hasMEAFlag,
            $historicalFlag
        );

        $event->setRedoing($redoing);
        $event->execute();

        // Assert JEs
        if ($event->getHistoricalFlag() == 'false') {
            if( $event instanceof ContractGLReclassOnUndoRecognition ){
                ContractGLAssertionsUtil::assert(
                    $amount,
                    ContractGLReclassEvent::SUBEVENTTYPE_ONRECOGNITION,
                    $jrnlCode,
                    $contractDetailKey,
                    $contractRevSchEntryKey
                );
            }else{
                ContractGLAssertionsUtil::assert(
                    $amount,
                    ContractGLReclassEvent::EVENTTYPE_ONRECOGNITION,
                    $jrnlCode,
                    $contractDetailKey,
                    $contractRevSchEntryKey
                );
            }

        }

        // If line is MEA in the past
        if ($orderedEvents && $hasMEAFlag) {
            $this->processFutureEventsForMEA($contractDetailKey, $jrnlCode, $orderedEvents, $hasFutureMEA);

            if ($hasFutureMEA) {
                $this->updateLocalScheduleLinks($jrnlCode, $contractRevSchEntryKey, null, true, null, null);
            }
        }

        if ($orderedEvents && count($orderedEvents) > 0) {

            // Re-calculate resolves
            $this->redoEvents($orderedEvents, $contractDetailKey, $jrnlCode);
        }

        // This is the first event which started it all
        if ($noPendingFutureMEAAtTheBeginning && $this->futureMEAEvent[$jrnlCode]) {

            $this->runFutureMEAEvent($jrnlCode, $contractDetailKey);

            $this->runFutureMEASyncedRedoEvents($jrnlCode);
        }

        ContractGLAssertionsUtil::assertInvoiceAmountEqualsReclassAmount($contractKey, 'J1');
        ContractGLAssertionsUtil::assertInvoiceAmountEqualsReclassAmount($contractKey, 'J2');
    }

    /**
     * Re-calculates re-class on recognition delete
     *
     * @param   int     $contractRevSchEntryKey     Revenue schedule entry key
     *
     * @throws IAException
     */
    public function onRecognitionDelete($contractRevSchEntryKey)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $revSchEntry = $this->getRevenueScheduleEntry($contractRevSchEntryKey);

        if ($revSchEntry['AMOUNT'] == 0 && !$revSchEntry['GLBATCHKEY']) {
            // There is NO affect on balances, but if there is entry in contract resolve for this revenue schedule entry
            // need to delete that
            $this->deleteZeroValueRecCnResolveEntry($contractRevSchEntryKey);
            return;
        }

        $this->forcedJrnlCode = $revSchEntry['TYPE'] == ContractSchedule::TYPE_REVENUE ?
            ContractGLReclassEvent::JOURNALCODE_J1 : ContractGLReclassEvent::JOURNALCODE_J2;

        $contractDetailKey = $revSchEntry['CONTRACTDETAILKEY'];
        $actualPostingDate = $revSchEntry['ACTUALPOSTINGDATE'];

        if (!$this->canDeleteRecognition($contractRevSchEntryKey, $contractDetailKey, $this->forcedJrnlCode, $futureMEADate)) {
            $msg = "The revenue posted on $actualPostingDate is tied to the adjustment resulting from the MEA allocation effective $futureMEADate. To clear this amount, clear the MEA allocation. See 'MEA allocations' in the online help for details.";
            throw IAException::newIAException( 'CN-1567', $msg, ['ACTUAL_POSTING_DATE' => FormatDateForDisplay($actualPostingDate), 'FUTURE_DATE' => FormatDateForDisplay($futureMEADate)]);
        }

        // Do we still need this check?? We are outright not allowing unposting if there is even one MEA
        $this->checkForFutureMEAs($contractDetailKey, $actualPostingDate, $this->forcedJrnlCode);

        // 1. Get the starting resolve record associated with this rev. sch. entry
        $startResolveRec = $this->getStartResolveRecOnRecognitionDelete($contractRevSchEntryKey, $resolveRecs);

        // 2. Get all resolve records with unique events and re-class gl batches
        if ($startResolveRec != -1) {

            $this->getEventsInOrderOnRecognitionDelete(
                $startResolveRec,
                $contractRevSchEntryKey,
                $contractDetailKey,
                $this->forcedJrnlCode,
                $orderedEvents,
                $reclassGLBatches
            );

            // 3. Delete resolves
            if (ContractUtil::clearingMEA()) {
                // Only delete the revenue reclass entries.  The redo of subsequent events will be done as part of onMEADelete
                $this->deleteResolveByRecNos($resolveRecs);
                $this->setCacheMinOnRecognitionForMEADelete($this->forcedJrnlCode, $startResolveRec);
            } else {
                $this->deleteResolve($startResolveRec, $contractDetailKey, $this->forcedJrnlCode, [ 'A', 'R' ]);
            }

            // 4. Explicitly delete re-class glbatchs
            if ( !empty($reclassGLBatches) ) {
                $this->deleteGLBatches($reclassGLBatches);
            }

            $this->setExecutedClearTransactions($contractDetailKey, $this->forcedJrnlCode, $orderedEvents);

            // If line has MEA in the past
            if ( $orderedEvents && ContractUtil::contractDetailHasMEA($contractDetailKey, $this->forcedJrnlCode)) {
                $this->processFutureEventsForMEA($contractDetailKey, $this->forcedJrnlCode, $orderedEvents,
                                                 $hasFutureMEA);

                if ( $hasFutureMEA ) {
                    $this->updateLocalScheduleLinks($this->forcedJrnlCode, $contractRevSchEntryKey, null, false, null,
                                                    null);
                }
            }

            if ( !empty($orderedEvents) ) {
                // 5. Re-calculate resolves
                $this->redoEvents($orderedEvents, $contractDetailKey, $this->forcedJrnlCode);
            }
        }

        // This is the first event which started it all
        if ($this->futureMEAEvent[$this->forcedJrnlCode]) {

            $this->runFutureMEAEvent($this->forcedJrnlCode, $contractDetailKey);

            $this->runFutureMEASyncedRedoEvents($this->forcedJrnlCode);
        }
    }

    /**
     * @param string $jrnlCode
     * @param int    $startResolveRec
     */
    private function setCacheMinOnRecognitionForMEADelete($jrnlCode, $startResolveRec)
    {
        $curMin = $this->getCacheMinOnRecognitionForMEADelete($jrnlCode);
        if ($curMin === null || $startResolveRec < $curMin) {
            self::$minOnRecognitionForMEADelete[$jrnlCode] = $startResolveRec;
        }
    }

    /**
     * @param string $jrnlCode
     *
     * @return int|null
     */
    private function getCacheMinOnRecognitionForMEADelete($jrnlCode)
    {
        return self::$minOnRecognitionForMEADelete[$jrnlCode] ?? null;
    }

    private function clearCacheMinOnRecognitionForMEADelete()
    {
        self::$minOnRecognitionForMEADelete = [];
    }

    /**
     * Start resolve record#
     *
     * @param int      $contractRevSchEntryKey Contract Revenue Schedule Entry key
     * @param string[] $resolveRecs            All resolve record numbers for the recognition event
     *
     * @return int
     *
     */
    protected function getStartResolveRecOnRecognitionDelete($contractRevSchEntryKey, &$resolveRecs)
    {
        $resolveRec = -1;
        $resolveRecs = [];

        $cnResolve = EntityManager::GetListQuick(
            'contractresolve',
            ['RECORDNO', 'JOURNALTYPE', 'EVENTTYPE', 'TRANSACTIONDATE'],
            ['REVENUESCHENTRYKEY' => $contractRevSchEntryKey],
            [['RECORDNO']]
        );

        if ($cnResolve !== false && isset($cnResolve[0]['RECORDNO'])) {
            // There will be 2 taking the first as start rec
            $resolveRec = $cnResolve[0]['RECORDNO'];
            $resolveRecs = array_column($cnResolve, 'RECORDNO');
        }

        return $resolveRec;
    }

    /**
     * Start resolve record#
     *
     * @param   int     $contractBillingSchEntryKey     Contract Billing Schedule Entry key
     * @param   int     $contractDetailKey              Contract detail key
     * @param   string  $jrnlCode                       Journal code
     *
     * @return int
     *
     * @throws IAException
     */
    protected function getStartResolveRecOnInvoiceDelete($contractBillingSchEntryKey, $contractDetailKey, $jrnlCode)
    {
        $jrnlType = $jrnlCode == ContractGLReclassEvent::JOURNALCODE_J1 ?
            ContractGLReclassEvent::JOURNALTYPE_J1 : ContractGLReclassEvent::JOURNALTYPE_J2;

        $filters = [
            ['BILLINGSCHENTRYKEY', '=', $contractBillingSchEntryKey],
            ['JOURNALTYPE', '=', $jrnlType],
            ['CONTRACTDETAILKEY', '=', $contractDetailKey],
            ['EVENTTYPE', '=', ContractGLReclassEvent::EVENTTYPE_ONINVOICE]

        ];

        $cnResolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');

        $params = [
            'selects' => ['RECORDNO', 'JOURNALTYPE', 'EVENTTYPE', 'TRANSACTIONDATE'],
            'filters' => [$filters],
            'orders' => [['RECORDNO']]
        ];

        $cnResolve = $cnResolveMgr->GetListActive($params);

        // It can happen when template 2 gets added after invoice is created with template 1 schedule
        if (!$cnResolve[0]) {

            $resolveRec = -1;

        } else {

            // There will be 4 (2 AR + 2 R) taking the first as start rec
            $resolveRec = $cnResolve[0]['RECORDNO'];
        }

        return $resolveRec;
    }

    /**
     * Start resolve record#
     *
     * @param   array   $contractDetailKeys     Contract detail keys
     * @param   string  $jrnlCode               Journal code
     *
     * @param null|string $effectiveDate
     * @return int
     *
     * @throws IAException
     */
    protected function getStartResolveRecOnMEADelete($contractDetailKeys, $jrnlCode, $effectiveDate = null)
    {
        if (!$contractDetailKeys) {
            return null;
        }

        $jrnlType = $jrnlCode == ContractGLReclassEvent::JOURNALCODE_J1 ?
            ContractGLReclassEvent::JOURNALTYPE_J1 : ContractGLReclassEvent::JOURNALTYPE_J2;

        $cnResolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');

        $params = [
            'selects'   => [
                [
                    'fields' => array('RECORDNO'),
                    'function' => 'MIN(${1})'
                ]
            ],
            'columnaliases' => array('MINRESOLVEREC'),
            'filters'   => [[
                ['CONTRACTDETAILKEY',   'IN',   $contractDetailKeys],
                ['JOURNALTYPE',         '=',    $jrnlType],
                ['EVENTTYPE',           '=',    $this->bundleEventType]
            ]],
        ];

        if($effectiveDate){
           $params['filters'][0][] = ['TRANSACTIONDATE', '>=', $effectiveDate];
        }
        $onMEAResolves = $cnResolveMgr->GetListActive($params);

        $minMEARec = $onMEAResolves && $onMEAResolves[0] ? $onMEAResolves[0]['MINRESOLVEREC'] : null;

        $minRecognitionRec = $this->getCacheMinOnRecognitionForMEADelete($jrnlCode);
        if ($minRecognitionRec) {
            if ($minMEARec) {
                $retVal = min($minMEARec, $minRecognitionRec);
            } else {
                $retVal = $minRecognitionRec;
            }
        } else {
            $retVal = $minMEARec;
        }


        return $retVal;
    }

    /**
     * Returns summarized events from 'contractresolve' table
     *
     * @param   int     $startResolveRec        Start resolve record#
     * @param   int     $contractRevSchEntryKey Contract Revenue Schedule Entry key
     * @param   int     $contractDetailKey      Contract detail key
     * @param   string  $jrnlCode               J1 or J2
     * @param   array   $orderedEvents          Time ordered events - returned by ref.
     * @param   array   $reclassGLBatches       Re-class glbatches involved - returned by ref.
     *
     * @throws IAException
     */
    protected function getEventsInOrderOnRecognitionDelete($startResolveRec, $contractRevSchEntryKey,
                                                           $contractDetailKey, $jrnlCode, &$orderedEvents,
                                                           &$reclassGLBatches)
    {
        $jrnlType = $jrnlCode == ContractGLReclassEvent::JOURNALCODE_J1 ?
            ContractGLReclassEvent::JOURNALTYPE_J1 : ContractGLReclassEvent::JOURNALTYPE_J2;

        $cnResolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');
        $selects = $this->getResolveMgrGetFields($cnResolveMgr);

        $params = [
            'selects'   => $selects,
            'filters'   => [[
                ['CONTRACTDETAILKEY',   '=', $contractDetailKey],
                ['JOURNALTYPE',         '=', $jrnlType],
                ['RECORDNO',            '>=', $startResolveRec],
                ['BALANCETYPE',         'IN', [ContractGLReclassEvent::BALANCETYPE_AR,
                                                    ContractGLReclassEvent::BALANCETYPE_REVENUE]],
                ['EVENTTYPE',           'NOT IN', [ContractGLReclassEvent::EVENTTYPE_ONCANCEL ]],
                ['TYPE',                'NOT IN', [ContractGLReclassEvent::RESOLVETYPE_USAGEBASED ,ContractGLReclassEvent::RESOLVETYPE_ADJUST ]]
            ]],
            'orders' => [['TRANSACTIONDATE'], ['RECORDNO']]
        ];

        if (ContractUtil::clearingMEA()) {
            $params['filters'][0][] = ['REVENUESCHENTRYKEY', '=', $contractRevSchEntryKey];
        }

        $allCNResolves = $cnResolveMgr->GetListActive($params);

        // TODO: Let the query filter these
        $newAllCNResolves = [];
        $reclassGLBatches = [];
        $paymentBSEKeys = [];
        foreach ($allCNResolves as $resolve) {

            // Always take all glbatches involved in re-class
            if ($resolve['GLBATCHKEY'] && !in_array($resolve['GLBATCHKEY'], $reclassGLBatches)) {
                $reclassGLBatches[] = $resolve['GLBATCHKEY'];
            }

            if($resolve['BILLINGSCHENTRYKEY'] && $resolve['PAYMENTPRENTRYKEY'] ) {
                $paymentDate = $resolve['ACTUALTRANSACTIONDATE']??$resolve['TRANSACTIONDATE'];
                ContractUtil::collectPaymentAndReversalDataVersion($resolve['BILLINGSCHENTRYKEY'], $resolve['PAYMENTPRENTRYKEY'], $resolve['PARENTPYMTRECORDKEY'], $paymentDate, $resolve['DATAVERSIONKEY']);
            }

            if ($resolve['REVENUESCHENTRYKEY'] == $contractRevSchEntryKey) {
                continue;
            }

            if ($resolve['BILLINGSCHENTRYKEY'] && $resolve['PAYMENTPRENTRYKEY'] &&
                !in_array($resolve['BILLINGSCHENTRYKEY'], $paymentBSEKeys)) {
                $paymentBSEKeys[] = $resolve['BILLINGSCHENTRYKEY'];
            }

            $newAllCNResolves[] = $resolve;
        }

        if (!empty($paymentBSEKeys)) {
            $this->cachePaymentAmountSplitByBillingScheduleEntry($paymentBSEKeys);
        }

        $orderedEvents = $this->summarizeResolvesByEvent($newAllCNResolves);
    }

    /**
     * Returns summarized events from 'contractresolve' table
     *
     * @param   int     $startResolveRec            Start resolve record#
     * @param   array     $allDeletedInvoiceBillSchEntryKeys Contract Billing Schedule Entry key
     * @param   int     $contractDetailKey          Contract detail key
     * @param   string  $jrnlCode                   J1 or J2
     * @param   array   $orderedEvents              Time ordered events - returned by ref.
     * @param   array   $reclassGLBatches           Re-class glbatches involved - returned by ref.
     *
     * @throws IAException
     */
    protected function getEventsInOrderOnInvoiceDelete($startResolveRec, $allDeletedInvoiceBillSchEntryKeys,
                                                       $contractDetailKey, $jrnlCode, &$orderedEvents,
                                                       &$reclassGLBatches)
    {
        $jrnlType = $jrnlCode == ContractGLReclassEvent::JOURNALCODE_J1
            ? ContractGLReclassEvent::JOURNALTYPE_J1 : ContractGLReclassEvent::JOURNALTYPE_J2;

        $cnResolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');
        $selects = $this->getResolveMgrGetFields($cnResolveMgr);

        $params = [
            'selects'   => $selects,
            'filters'   => [[
                ['CONTRACTDETAILKEY',   '=', $contractDetailKey],
                ['JOURNALTYPE',         '=', $jrnlType],
                ['RECORDNO',            '>=', $startResolveRec],
                ['BALANCETYPE',         'IN', [ContractGLReclassEvent::BALANCETYPE_AR,
                                                ContractGLReclassEvent::BALANCETYPE_REVENUE]],
                ['EVENTTYPE',           'NOT IN', [ContractGLReclassEvent::EVENTTYPE_ONCANCEL ]],
                ['TYPE',                'NOT IN', [ContractGLReclassEvent::RESOLVETYPE_USAGEBASED  ,ContractGLReclassEvent::RESOLVETYPE_ADJUST]]
            ]],
            'orders' => [['TRANSACTIONDATE'], ['RECORDNO']]
        ];

        $allCNResolves = $cnResolveMgr->GetListActive($params);

        // TODO: Let the query filter these
        $newAllCNResolves = [];
        $reclassGLBatches = [];
        $paymentBSEKeys = [];

        //$allDeletedInvoiceBillSchEntryKeys will be for same invoice, so we are fetching DOCID with 1st entry key
        $bsEntry = ContractUtil::getBillingScheduleEntryById($allDeletedInvoiceBillSchEntryKeys[0]);
        $docPaidStatusOfDeletingInvoice = false;
        //deleted BSE doc is paid or not
        $docHdrKey = DocumentManager::getRecordNoFromKey($bsEntry['DOCID'] ?? '');
        if(DocumentManager::isDocumentPaid($docHdrKey)){
            $docPaidStatusOfDeletingInvoice = true;
        }

        foreach ($allCNResolves as $resolve) {

            // When we edit the invoice,if inline-payment is applied which is actually not-paid, but in resolve payment is present
            // so, we introduced $docPaidStatusOfDeletingInvoice

            ContractUtil::assert(!(in_array($resolve['BILLINGSCHENTRYKEY'], $allDeletedInvoiceBillSchEntryKeys) && $resolve['EVENTTYPE'] == ContractGLReclassEvent::EVENTTYPE_ONPAYMENT && $docPaidStatusOfDeletingInvoice));

            ContractUtil::assert(!(in_array( $resolve['BILLINGSCHENTRYKEY'], $allDeletedInvoiceBillSchEntryKeys) && $resolve['EVENTTYPE'] == ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL));

            // Collect all gl batches from resolve
            if ($resolve['GLBATCHKEY'] && !in_array($resolve['GLBATCHKEY'], $reclassGLBatches)) {
                $reclassGLBatches[] = $resolve['GLBATCHKEY'];
            }

            if($resolve['BILLINGSCHENTRYKEY'] && $resolve['PAYMENTPRENTRYKEY']) {
                $paymentDate = $resolve['ACTUALTRANSACTIONDATE']??$resolve['TRANSACTIONDATE'];
                ContractUtil::collectPaymentAndReversalDataVersion($resolve['BILLINGSCHENTRYKEY'], $resolve['PAYMENTPRENTRYKEY'], $resolve['PARENTPYMTRECORDKEY'], $paymentDate, $resolve['DATAVERSIONKEY']);
            }

            // Ignore all events resulted from current billing schedule entry, this will make them not re-execute
            if (  in_array( $resolve['BILLINGSCHENTRYKEY'], $allDeletedInvoiceBillSchEntryKeys) && $resolve['EVENTTYPE'] == ContractGLReclassEvent::EVENTTYPE_ONINVOICE) {
                continue;
            }

            if ($resolve['BILLINGSCHENTRYKEY'] && $resolve['PAYMENTPRENTRYKEY'] &&
                !in_array($resolve['BILLINGSCHENTRYKEY'], $paymentBSEKeys)) {
                $paymentBSEKeys[] = $resolve['BILLINGSCHENTRYKEY'];
            }

            $newAllCNResolves[] = $resolve;
        }

        if (!empty($paymentBSEKeys)) {
            $this->cachePaymentAmountSplitByBillingScheduleEntry($paymentBSEKeys);
        }

        $orderedEvents = $this->summarizeResolvesByEvent($newAllCNResolves);
    }

    /**
     * Returns summarized events from 'contractresolve' table when clearing MEA
     *
     * @param   int     $startResolveRec    Start resolve record#
     * @param   array   $contractDetailKeys Contract detail keys
     * @param   string  $jrnlCode           J1 or J2
     * @param   array   $orderedEvents      Time ordered events - returned by ref.
     * @param   array   $reclassGLBatches   Re-class glbatches involved - returned by ref.
     *
     * @param null|string $effectiveDate
     * @throws IAException
     */
    protected function getEventsInOrderOnMEADelete($startResolveRec, $contractDetailKeys, $jrnlCode, &$orderedEvents,
                                                   &$reclassGLBatches , $effectiveDate = null)
    {
        $jrnlType = $jrnlCode == ContractGLReclassEvent::JOURNALCODE_J1
            ? ContractGLReclassEvent::JOURNALTYPE_J1 : ContractGLReclassEvent::JOURNALTYPE_J2;

        $cnResolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');
        $selects = $this->getResolveMgrGetFields($cnResolveMgr);
        $selects[] = 'BILLINGSCHENTRY.BILLINGSCH.CONTRACTDETAILKEY';

        $params = [
            'selects'   => $selects,
            'filters' => [[
                ['CONTRACTDETAILKEY',   'IN',   $contractDetailKeys],
                ['JOURNALTYPE',         '=',    $jrnlType],
                ['RECORDNO',            '>=',   $startResolveRec],
                ['BALANCETYPE',         'IN',   [
                    ContractGLReclassEvent::BALANCETYPE_AR,
                    ContractGLReclassEvent::BALANCETYPE_REVENUE
                    ]],
                ['EVENTTYPE',           'NOT IN', [ContractGLReclassEvent::EVENTTYPE_ONCANCEL ,ContractGLReclassEvent::EVENTTYPE_ONCONTRACT  ]],
                ['TYPE',                '!=', ContractGLReclassEvent::RESOLVETYPE_USAGEBASED]
            ]],
            'orders' => [['TRANSACTIONDATE'], ['RECORDNO']]
        ];
        if($effectiveDate){
            $params['filters'][0][] = ['TRANSACTIONDATE', '>=', $effectiveDate];
        }
        $allCNResolves = $cnResolveMgr->GetListActive($params);

        $newAllCNResolves = [];
        $reclassGLBatches = [];
        $paymentBSEKeys = [];
        foreach ($allCNResolves as $resolve) {

            // Collect all gl batches from resolve
            if ($resolve['GLBATCHKEY'] && !in_array($resolve['GLBATCHKEY'], $reclassGLBatches)) {
                $reclassGLBatches[] = [
                    'GLBATCHKEY'        => $resolve['GLBATCHKEY'],
                    'TRANSACTIONDATE'   => $resolve['TRANSACTIONDATE']
                ];
            }

            if($resolve['BILLINGSCHENTRYKEY'] && $resolve['PAYMENTPRENTRYKEY']) {
                $paymentDate = $resolve['ACTUALTRANSACTIONDATE']??$resolve['TRANSACTIONDATE'];
                ContractUtil::collectPaymentAndReversalDataVersion($resolve['BILLINGSCHENTRYKEY'], $resolve['PAYMENTPRENTRYKEY'], $resolve['PARENTPYMTRECORDKEY'], $paymentDate, $resolve['DATAVERSIONKEY']);
            }

            // Ignore self
            if ($resolve['EVENTTYPE'] == $this->bundleEventType) {
                continue;
            }

            if ($resolve['BILLINGSCHENTRYKEY'] && $resolve['PAYMENTPRENTRYKEY'] &&
                !in_array($resolve['BILLINGSCHENTRYKEY'], $paymentBSEKeys)) {
                $paymentBSEKeys[] = $resolve['BILLINGSCHENTRYKEY'];
            }

            $newAllCNResolves[] = $resolve;
        }

        if (!empty($paymentBSEKeys)) {
            $this->cachePaymentAmountSplitByBillingScheduleEntry($paymentBSEKeys);
        }

        $orderedEvents = $this->summarizeResolvesByEvent($newAllCNResolves);
    }

    /**
     * Handles business logic on payment
     *
     * @param int $billingScheduleEntryKey Schedule entry key
     * @param int $paymentPREntrykey Payment's prentry key
     * @param int $parentPymtRecordKey
     * @param float $amount Amount
     * @param string $transactionDate Transaction date
     * @param bool $redoing 'true' when re-executing the event
     * @param int|null $contractDetailKey Contract detail key
     * @throws IAException
     */
    public function onPayment($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $amount, $transactionDate, $redoing = false, $contractDetailKey = null)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        // We expect 0 value during redo in MEA scenario - case#12912937
        if (!$redoing && $amount == 0) {
            $msg = "Payment amount cannot be 0. Billing schedule entry key:".$billingScheduleEntryKey.
                    " and Pay line key:".$paymentPREntrykey;
            throw IAException::newIAException( 'CN-1568', $msg, ['BILLING_SCHEDULE_ENTRY_KEY' => $billingScheduleEntryKey, 'PAYMENT_ENTRYKEY' => $paymentPREntrykey]);
        }

        // Preventing certain payment cases
        if (!$redoing) {
            $this->validatePaymentDate($billingScheduleEntryKey, $transactionDate);
            ContractUtil::collectPaymentAndReversalDataVersion($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $transactionDate, ContractUtil::AR_SUBLEDGER_PAYMENTDATE_IS_RECLASSDATE);
        }

        $entry = ContractUtil::getBillingScheduleEntryById($billingScheduleEntryKey);

        if ($this->isMCPEnabled() && !$entry['POSTEDEXCHANGE_RATE']) {
            $msg = "Missing posted exchange rate in billing schedule entry:" . $billingScheduleEntryKey;
            throw IAException::newIAException( 'CN-1569', $msg, ['BILLING_SCHEDULE_ENTRY_KEY' => $billingScheduleEntryKey]);
        }

        $contractKey = $entry['CONTRACTKEY'];

        // $contractDetailKey and $entry['CONTRACTDETAILKEY'] may not match in some MEA scenarios
        if (!$contractDetailKey) {
            $contractDetailKey = $entry['CONTRACTDETAILKEY'];
        }

        $postedExchRate = $entry['POSTEDEXCHANGE_RATE'];

        if ($this->sourceContractDetailKey == null) {
            $this->sourceContractDetailKey = $contractDetailKey;
        }

        $processJ1 = false;
        if ($entry['REVENUETEMPLATEKEY']) {
            $processJ1 = true;
            if ($this->forcedJrnlCode && $this->forcedJrnlCode != ContractGLReclassEvent::JOURNALCODE_J1) {
                $processJ1 = false;
            }
        }

        $processJ2 = false;
        if ($entry['REVENUE2TEMPLATEKEY']) {
            $processJ2 = true;
            if ($this->forcedJrnlCode && $this->forcedJrnlCode != ContractGLReclassEvent::JOURNALCODE_J2) {
                $processJ2 = false;
            }
        }
    
        [$processJ1, $processJ2] = $this->getChildComponentProcessJournals($entry, $processJ1, $processJ2);
    
        if ($processJ1) {
            ContractGLAssertionsUtil::clearTransactionJEs(ContractGLReclassEvent::EVENTTYPE_ONPAYMENT, ContractGLReclassEvent::JOURNALCODE_J1, $contractDetailKey, $billingScheduleEntryKey, $paymentPREntrykey);
            if (!$redoing) {
                $this->checkForFutureMEAs($contractDetailKey, $transactionDate, ContractGLReclassEvent::JOURNALCODE_J1);
            }

            $noPendingFutureMEAAtTheBeginningJ1 = !$this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J1];

            $this->onPaymentOuter(
                $billingScheduleEntryKey,
                $paymentPREntrykey,
                $parentPymtRecordKey,
                $amount,
                $postedExchRate,
                $transactionDate,
                $contractKey,
                $contractDetailKey,
                ContractGLReclassEvent::JOURNALCODE_J1,
                $redoing
            );

            // This is the first event which started it all
            if ($noPendingFutureMEAAtTheBeginningJ1 && $this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J1]) {

                $this->runFutureMEAEvent(ContractGLReclassEvent::JOURNALCODE_J1, $contractDetailKey);

                $this->runFutureMEASyncedRedoEvents(ContractGLReclassEvent::JOURNALCODE_J1);
            }
        }

        if ($processJ2) {
            ContractGLAssertionsUtil::clearTransactionJEs(ContractGLReclassEvent::EVENTTYPE_ONPAYMENT, ContractGLReclassEvent::JOURNALCODE_J2, $contractDetailKey, $billingScheduleEntryKey, $paymentPREntrykey);
            if (!$redoing) {
                $this->checkForFutureMEAs($contractDetailKey, $transactionDate, ContractGLReclassEvent::JOURNALCODE_J2);
            }

            $noPendingFutureMEAAtTheBeginningJ2 = !$this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J2];

            $this->onPaymentOuter(
                $billingScheduleEntryKey,
                $paymentPREntrykey,
                $parentPymtRecordKey,
                $amount,
                $postedExchRate,
                $transactionDate,
                $contractKey,
                $contractDetailKey,
                ContractGLReclassEvent::JOURNALCODE_J2,
                $redoing
            );

            // This is the first event which started it all
            if ($noPendingFutureMEAAtTheBeginningJ2 && $this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J2]) {

                $this->runFutureMEAEvent(ContractGLReclassEvent::JOURNALCODE_J2, $contractDetailKey);

                $this->runFutureMEASyncedRedoEvents(ContractGLReclassEvent::JOURNALCODE_J2);
            }
        }
    }


    /**
     * Validates payment date
     *
     * @param   int     $billingScheduleEntryKey    Billing schedule entry key
     * @param   string  $transactionDate            Transaction date
     *
     * @throws IAException
     */
    protected function validatePaymentDate($billingScheduleEntryKey, $transactionDate)
    {
        $qry = "SELECT DISTINCT vl.paymentkey, vl.voidpaymentkey,cr.paymentprentrykey,cr.transactiondate,crv.paymentprentrykey revpaymentprentrykey, ";
        $qry .= "crv.transactiondate reversedate, cr.journaltype FROM contractresolve cr, prentry pre, voidlink vl, contractresolve crv, prentry prev ";
        $qry .= "WHERE cr.cny#=:1 AND pre.cny#=:1 AND vl.cny#=:1 AND  crv.cny#=:1 AND prev.cny#=:1 AND cr.paymentprentrykey = pre.record# ";
        $qry .= "AND pre.recordkey = vl.paymentkey AND cr.billingschentrykey = :2 AND cr.eventtype = 'P' ";
        $qry .= "AND cr.transactiondate <= :3 AND crv.transactiondate > :3 AND vl.voidpaymentkey = prev.recordkey ";
        $qry .= "AND prev.record# = crv.paymentprentrykey ORDER BY crv.transactiondate";

        $res = QueryResult([$qry, GetMyCompany(), $billingScheduleEntryKey, $transactionDate]);

        // We will get results only if there is gap between payment and reversal
        if ($res && count($res) > 0) {

            // Take the greatest such reversal date
            $reverseDate = $res[count($res)-1]['REVERSEDATE'];
            $msg = "Payment date (".$transactionDate.") needs to be on or after last".
                   " payment reversal date (".$reverseDate.").";
            throw IAException::newIAException( 'CN-1570', $msg, ['TRANSACTION_DATE' => FormatDateForDisplay($transactionDate),
                                                                 'REVERSE_DATE' => FormatDateForDisplay($reverseDate)]);
        }
    }

    /**
     * Validates usage payment date
     *
     * @param   int     $usageBillingId
     * @param   string  $transactionDate  Transaction date
     *
     * @throws IAException
     */
    protected function validateUsagePaymentDate($usageBillingId, $transactionDate)
    {
        // Validates if payment date is between existing payment and reversal dates
        $qry = "SELECT MIN(transactiondate) AS minreversaldate FROM CONTRACTRESOLVE WHERE cny#=:1 AND ";
        $qry .= "eventtype = 'V' AND transactiondate > :2 AND billablecontractusagebillingid = :3";
        $resReversal = QueryResult([$qry, GetMyCompany(), $transactionDate, $usageBillingId]);

        if ($resReversal && $resReversal[0]
            && $resReversal[0]['MINREVERSALDATE']
        ) {
            $msg = "Payment date (" . $transactionDate
                   . ") needs to be on or after last" .
                   " payment reversal date (" . $resReversal[0]['MINREVERSALDATE']
                   . ").";
            throw IAException::newIAException( 'CN-1570', $msg, ['TRANSACTION_DATE' => FormatDateForDisplay($transactionDate),
                                                                 'REVERSE_DATE' => FormatDateForDisplay($resReversal[0]['MINREVERSALDATE'])]);
        }
    }

    /**
     * @param int $billingScheduleEntryKey
     * @param array|null $split
     * @return bool
     * @throws IAException
     */
    public function isBillingDistributing(int $billingScheduleEntryKey, array|null $split)
    {
        $srcContractDetailKey = $this->getContractDetailKey($billingScheduleEntryKey);

        if ((count($split ?? []) === 1 && isset($split[$srcContractDetailKey])) || count($split ?? []) === 0) {
            return false;
        }

        return true;
    }

    /**
     * On payment event handler with and without MEA
     *
     * @param int $billingScheduleEntryKey Schedule entry key
     * @param int $paymentPREntrykey Payment's prentry key
     * @param int $parentPymtRecordKey
     * @param float $amount Amount
     * @param float $postedExchRate Invoice's exchange rate
     * @param string $transactionDate Transaction date
     * @param int $contractKey Contract key
     * @param int $contractDetailKey Contract detail key
     * @param string $jrnlCode J1 or J2
     * @param bool $redoing 'true' when re-executing the event
     *
     * @throws IAException
     */
    protected function onPaymentOuter($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $amount, $postedExchRate,
                                           $transactionDate, $contractKey, $contractDetailKey, $jrnlCode, $redoing)
    {

        if (!$this->eventAlreadyExecuted(
            ContractGLReclassEvent::EVENTTYPE_ONPAYMENT,
            $jrnlCode,
            $contractDetailKey,
            $billingScheduleEntryKey,
            $paymentPREntrykey, $parentPymtRecordKey, $transactionDate
        )) {

            $this->onEventPreProcessing($contractDetailKey, $transactionDate, $jrnlCode);

            $this->dumpMethodCall(__FUNCTION__, func_get_args());
            $orderedEvents = null;

            if (!$this->clearAlreadyExecuted($contractDetailKey, $jrnlCode)) {
                $clearTransactionDate = $transactionDate;
                if (ContractUtil::isPostConResolveInOpenPeriod()) {
                    //$effectiveDate  is current open period which would be the different from mea date
                    //need to get actual transaction date of MEA to fetchj data from MEA bundle
                    $clearTransactionDate = ContractUtil::getEventActTranDate_ConSubPostInOpenPeriod(ContractGLReclassEvent::EVENTTYPE_ONPAYMENT, $jrnlCode, ['BILLINGSCHENTRYKEY' => $billingScheduleEntryKey, 'PAYMENTPRENTRYKEY' => $paymentPREntrykey], $transactionDate);
                }
                // Delete self along with future events when redoing unless it is source contract detail
                if ($redoing) {

                    $identifierAttributes = [
                        'EVENTTYPE' => ContractGLReclassEvent::EVENTTYPE_ONPAYMENT,
                        'PAYMENTPRENTRYKEY' => $paymentPREntrykey,
                        'BILLINGSCHENTRYKEY' => $billingScheduleEntryKey,
                    ];

                    $this->clearExistingReclassTransactionsIncludingSelf(
                        $identifierAttributes,
                        $contractDetailKey,
                        $clearTransactionDate,
                        $jrnlCode,
                        $orderedEvents
                    );
                } else {

                    $this->clearExistingReclassTransactions(
                        $clearTransactionDate,
                        $contractDetailKey,
                        $jrnlCode,
                        $orderedEvents
                    );
                }
            }

            $runReclass = true;
            // Check if MEA exists in the past
            if (ContractUtil::contractDetailHasMEA($contractDetailKey, $jrnlCode, $transactionDate)) {
                if ($orderedEvents) {
                    $this->processFutureEventsForMEA($contractDetailKey, $jrnlCode, $orderedEvents, $hasFutureMEA);
                }

                // Keep local state of schedule links updated for future MEA to correctly undo of current MEA
                // Just presense of it means we are operating on old MEA
                if ($this->scheduleLinksToUse[$jrnlCode] ?? false) {
                    $this->updateLocalScheduleLinks(
                        $jrnlCode, null, $billingScheduleEntryKey,
                        null, null, true
                    );
                }

                $originalMEASplit = $this->getPaymentMEaSplit(
                    $billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $amount, $transactionDate,
                    $jrnlCode, $redoing
                );


                //this will make sure that id billing is not sharing revenue then do not call payment inner mea
                if ($this->isBillingDistributing($billingScheduleEntryKey, $originalMEASplit)) {

                    if (ContractGLEventRegistry::registerLinkedReclass($redoing) && !$this->eventAlreadyExecuted(
                            ContractGLReclassEvent::EVENTTYPE_ONPAYMENT_REGISTRATION,
                            $jrnlCode, '',
                            $billingScheduleEntryKey,
                            $paymentPREntrykey, $parentPymtRecordKey, $transactionDate)
                    ) {

                        $this->recordEvent(
                            ContractGLReclassEvent::EVENTTYPE_ONPAYMENT_REGISTRATION,
                            $jrnlCode, $amount, '',
                            $billingScheduleEntryKey,
                            $paymentPREntrykey, $parentPymtRecordKey, $transactionDate
                        );

                        foreach ($originalMEASplit as $newContractDetailKey => $newAmount) {

                            if ($newContractDetailKey != $contractDetailKey) {
                                ContractGLEventRegistry::registerOtherLinesForReclassInRegistry(
                                    $newContractDetailKey,
                                    __FUNCTION__,
                                    $jrnlCode,
                                    [
                                        'BILLINGSCHEDULEENTRYKEY' => $billingScheduleEntryKey,
                                        'PAYMENTPRENTRYKEY' => $paymentPREntrykey,
                                        'PARENTPYMTRECORDKEY' => $parentPymtRecordKey,
                                        'AMOUNT' => $amount,
                                        'POSTEDEXCHRATE' => $postedExchRate,
                                        'TRANSACTIONDATE' => $transactionDate,
                                        'CONTRACTKEY' => $contractKey,
                                        'JRNLCODE' => $jrnlCode,
                                        'REDOING' => $redoing,
                                        'CONTRACTDETAILKEY' => $newContractDetailKey
                                    ]
                                );
                            }
                        }
                    }


                    $this->onPaymentMEAInner(
                        $billingScheduleEntryKey,
                        $paymentPREntrykey,
                        $parentPymtRecordKey,
                        $originalMEASplit,
                        $transactionDate,
                        $contractKey,
                        $contractDetailKey,
                        $postedExchRate,
                        $jrnlCode,
                        $redoing
                    );
                }
                $amount = $originalMEASplit[$contractDetailKey] ?? 0;
                if ($amount == 0) {
                    $runReclass = false;
                }

            } elseif ($redoing) {
                $arAmount = $this->getPaymentAmount($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $transactionDate, $jrnlCode);
                if ($arAmount === null) {
                    $msg = "Error in retrieving stored original payment amount. Billing schedule entry:" .
                        $billingScheduleEntryKey . ", Payment prentry key:" . $paymentPREntrykey;
                    throw IAException::newIAException('CN-1571', $msg, ['BILLING_SCHEDULE_ENTRY_KEY' => $billingScheduleEntryKey, 'PAYMENT_ENTRYKEY' => $paymentPREntrykey]);
                }
                $amount = $arAmount;
            }


            if ($runReclass) {
                $this->onPaymentInner(
                    $billingScheduleEntryKey,
                    $paymentPREntrykey,
                    $parentPymtRecordKey,
                    $amount,
                    $postedExchRate,
                    $transactionDate,
                    $contractKey,
                    $contractDetailKey,
                    $jrnlCode,
                    $redoing
                );
            }


            if ($orderedEvents && count($orderedEvents) > 0) {

                // Important to run reclass for other invoices and payments for this journal alone
                $this->forcedJrnlCode = $jrnlCode;

                $this->redoEvents($orderedEvents, $contractDetailKey, $jrnlCode);
            }

            if (!$orderedEvents && !$redoing) {
                $this->runNextContractDetailReclass($jrnlCode);
            }

        }
    }

    /**
     * On payment reversal handler after MEA
     *
     * @param int $billingScheduleEntryKey Schedule entry key
     * @param int $paymentPREntrykey Payment's prentry key
     * @param int $parentPymtRecordKey
     * @param string $transactionDate Transaction date
     * @param int $contractKey Contract key
     * @param int $contractDetailKey Contract detail key
     * @param float $postedExchRate Invoice's exchange rate
     * @param string $jrnlCode J1 or J2
     * @param bool $redoing 'true' when re-executing the event
     *
     * @throws IAException
     */
    protected function onPaymentReversalMEAInner($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $originalMEASplit,
                                                 $transactionDate, $contractKey, $contractDetailKey, $postedExchRate,
                                                 $jrnlCode, $redoing)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());


        $cnResolveDataVersionKey = ContractUtil::getPaymentAndReversalDataVersionKey(
            $billingScheduleEntryKey,
            $paymentPREntrykey,
            $parentPymtRecordKey,
            $jrnlCode,
            $transactionDate,
            ContractRevenueGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL
        );
        // Adjust wrongly posted JEs by payment reversal for current line
        $event = new ContractGLReclassOnPaymentReversalMEA(
            $billingScheduleEntryKey,
            $paymentPREntrykey,
            $parentPymtRecordKey,
            $originalMEASplit,
            $transactionDate,
            $contractKey,
            $contractDetailKey,
            $postedExchRate,
            $jrnlCode,
            $cnResolveDataVersionKey
        );

        $event->setRedoing($redoing);
        $event->execute();
    }

    /**
     * @param $billingScheduleEntryKey
     * @param $paymentPREntrykey
     * @param $parentPymtRecordKey
     * @param $paymentAmount
     * @param $transactionDate
     * @param $jrnlCode
     * @param $redoing
     * @return array|void
     * @throws IAException
     */
    private function getPaymentMEaSplit(
        $billingScheduleEntryKey, $paymentPREntrykey,
        $parentPymtRecordKey, $paymentAmount, $transactionDate,
        $jrnlCode, $redoing
    )
    {
        // Remember original payment amount since we need original split for 'On Payment MEA' logic below
        // This cannot be retrieved from $paymentPREntrykey

        if ($redoing) {

            // When redoing, get cached summed up amounts from resolve table
            $originalPayment = $this->getPaymentAmount(
                $billingScheduleEntryKey,
                $paymentPREntrykey,
                $parentPymtRecordKey,
                $transactionDate,
                $jrnlCode
            );
            if ($originalPayment === null) {
                $msg = "Error in retrieving stored original payment amount. Billing schedule entry:" .
                    $billingScheduleEntryKey . ", Payment prentry key:" . $paymentPREntrykey;
                throw IAException::newIAException(
                    'CN-1571',
                    $msg,
                    ['BILLING_SCHEDULE_ENTRY_KEY' => $billingScheduleEntryKey, 'PAYMENT_ENTRYKEY' => $paymentPREntrykey]
                );
            }

        } else {

            // When not redoing use what is passed and cache it
            $originalPayment = $this->getPaymentAmount(
                $billingScheduleEntryKey,
                $paymentPREntrykey,
                $parentPymtRecordKey,
                $transactionDate,
                $jrnlCode
            );

            if (!$originalPayment) {
                $originalPayment = $paymentAmount;
                $this->setPaymentAmount(
                    $originalPayment,
                    $billingScheduleEntryKey,
                    $paymentPREntrykey,
                    $parentPymtRecordKey,
                    $transactionDate
                );
            }
        }

        // Figure out split of original payment amount by MEA distribution
        $originalMEASplit = $this->splitAmountByMEADistribution(
            $originalPayment, $billingScheduleEntryKey,
            $jrnlCode, $transactionDate
        );

        return $originalMEASplit;

    }

    /**
     * @param $billingScheduleEntryKey
     * @param $paymentPREntrykey
     * @param $parentPymtRecordKey
     * @param $reversalAmount
     * @param $transactionDate
     * @param $jrnlCode
     * @param $redoing
     * @return array
     * @throws IAException
     */
    private function getPaymentReversalMEaSplit(
        $billingScheduleEntryKey, $paymentPREntrykey,
        $parentPymtRecordKey, $reversalAmount,
        $transactionDate, $jrnlCode, $redoing
    ){


        // Remember original payment amount since we need original split for 'On Payment MEA' logic below
        // This cannot be retrieved from $paymentPREntrykey

        if ($redoing) {

            // When redoing, get cached summed up amounts from resolve table
            $originalReversal = $this->getPaymentReversalAmount(
                $billingScheduleEntryKey,
                $paymentPREntrykey,
                $parentPymtRecordKey,
                $transactionDate,
                $jrnlCode
            );

            if (!$originalReversal) {
                $msg = "Error in retrieving stored original payment reversal amount. ".
                    "Billing schedule entry:". $billingScheduleEntryKey.
                    ", Payment reversal prentry key:".$paymentPREntrykey;
                throw IAException::newIAException(
                    'CN-1572',
                    $msg,
                    ['BILLING_SCHEDULE_ENTRY_KEY' => $billingScheduleEntryKey, 'PAYMENT_ENTRYKEY' => $paymentPREntrykey]);
            }

        } else {
            $originalReversal = $this->getPaymentReversalAmount(
                $billingScheduleEntryKey,
                $paymentPREntrykey,
                $parentPymtRecordKey,
                $transactionDate,
                $jrnlCode
            );

            if (!$originalReversal) {
                $originalReversal = $reversalAmount;

                $this->setReversalAmount(
                    $originalReversal,
                    $billingScheduleEntryKey,
                    $paymentPREntrykey,
                    $parentPymtRecordKey,
                    $transactionDate
                );
            }
        }

        // Figure out split of original payment amount by MEA distribution
        $originalMEASplit = $this->splitAmountByMEADistribution(
            $originalReversal,
            $billingScheduleEntryKey,
            $jrnlCode,
            $transactionDate
        );

        return $originalMEASplit;

    }

    /**
     * On payment handler after MEA
     *
     * @param   int $billingScheduleEntryKey Schedule entry key
     * @param   int $paymentPREntrykey Payment's prentry key
     * @param   int $parentPymtRecordKey
     * @param   float $paymentAmount Payment amount
     * @param   string $transactionDate Transaction date
     * @param   int $contractKey Contract key
     * @param   int $contractDetailKey Contract detail key
     * @param   float $postedExchRate Invoice's exchange rate
     * @param   string $jrnlCode J1 or J2
     * @param   bool $redoing 'true' when re-executing the event
     *
     * @throws IAException
     */
    protected function onPaymentMEAInner($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $originalMEASplit, $transactionDate,
                                         $contractKey, $contractDetailKey, $postedExchRate, $jrnlCode, $redoing)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());


        $cnResolveDataVersionKey = ContractUtil::getPaymentAndReversalDataVersionKey($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $jrnlCode, $transactionDate, ContractRevenueGLReclassEvent::EVENTTYPE_ONPAYMENT);
        // Adjust wrongly posted JEs by payment for current line
        $event = new ContractGLReclassOnPaymentMEA(
            $billingScheduleEntryKey,
            $paymentPREntrykey,
            $parentPymtRecordKey,
            $originalMEASplit,
            $transactionDate,
            $contractKey,
            $contractDetailKey,
            $postedExchRate,
            $jrnlCode,
            $cnResolveDataVersionKey
        );

        $event->setRedoing($redoing);
        $event->execute();


        // Update paid flag
        // Just presense of it means we are operating on old MEA
        if ($this->scheduleLinksToUse[$jrnlCode] ?? false) {
            $this->updateLocalScheduleLinks($jrnlCode, null, $billingScheduleEntryKey, null, null, true);
        }

    }

    /**
     * Returns original amount stored during event call
     *
     * @param int $bseKey
     * @param int $paymentPREntryKey
     * @param int $parentPymtRecordKey
     * @param string $paymentDate
     * @param string $jrnlCode
     * @return float|null
     * @throws IAException
     */

    protected function getPaymentAmount($bseKey, $paymentPREntryKey, $parentPymtRecordKey, $paymentDate, $jrnlCode)
    {
        $cnResolveDataVersion = ContractUtil::getPaymentAndReversalDataVersionKey($bseKey, $paymentPREntryKey, $parentPymtRecordKey, $jrnlCode, $paymentDate, ContractRevenueGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL);

        $oldLogicKey = "OnPayment_{$bseKey}_{$paymentPREntryKey}_{$jrnlCode}";
        $keyARSub = ContractUtil::generateCacheKeyForPayment($bseKey, $paymentPREntryKey, $parentPymtRecordKey, $paymentDate);

        //in case of old data which has data version null will fecth payment amount from contract resolve, if it does not get amount in contract resolve
        //then it will fetch from AR subledger. This is Grand-fathering logic.
        if ($cnResolveDataVersion !== ContractUtil::AR_SUBLEDGER_PAYMENTDATE_IS_RECLASSDATE) {

            return $this->paymentAndReversalAmountFromCNSub[$oldLogicKey];

        } else {

            return ContractUtil::getPaymentAmountFromARSub($keyARSub);

        }

    }


    /**
     * Returns original amount stored during event call
     *
     * @param int $bseKey
     * @param int $paymentPREntryKey
     * @param int $parentPymtRecordKey
     * @param string $paymentReversalDate
     *
     * @param string $jrnlCode
     * @return float|null
     * @throws IAException
     */

    protected function getPaymentReversalAmount($bseKey, $paymentPREntryKey, $parentPymtRecordKey, $paymentReversalDate, $jrnlCode)
    {

        $cnResolveDataVersion = ContractUtil::getPaymentAndReversalDataVersionKey($bseKey, $paymentPREntryKey, $parentPymtRecordKey, $jrnlCode, $paymentReversalDate, ContractRevenueGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL);

        $oldLogicKey = "OnPaymentReversal_{$bseKey}_{$paymentPREntryKey}";
        $keyARSub = ContractUtil::generateCacheKeyForPaymentReversal($bseKey, $paymentPREntryKey, $parentPymtRecordKey, $paymentReversalDate);

        if ($cnResolveDataVersion !== ContractUtil::AR_SUBLEDGER_PAYMENTDATE_IS_RECLASSDATE && isset($this->paymentAndReversalAmountFromCNSub[$oldLogicKey]) && $this->paymentAndReversalAmountFromCNSub[$oldLogicKey] !== null) {

            $amount = $this->paymentAndReversalAmountFromCNSub[$oldLogicKey];

        } else {

            $amount = ContractUtil::getPaymentReversalAmountFromARSub($keyARSub);
            $billinScheduleEntryInfo = ContractUtil::getBillingScheduleEntryById($bseKey);
            if($billinScheduleEntryInfo && $billinScheduleEntryInfo['AMOUNT'] >= 0){
                $amount = $amount !== null ? abs($amount) : null;
            }
            return $amount;

        }

        return $amount;
    }

    /* @param float $amount
     * @param int $bseKey
     * @param int $paymentPREntryKey
     * @param int $parentPymtRecordKey
     * @param string $paymentDate
     */
    protected function setPaymentAmount($amount, $bseKey, $paymentPREntryKey, $parentPymtRecordKey, $paymentDate)
    {

        $key = ContractUtil::generateCacheKeyForPayment($bseKey, $paymentPREntryKey, $parentPymtRecordKey, $paymentDate);

        ContractUtil::setPaymentAndReversalAmountsFromARSub($key, $amount);

    }

    /**
     * @param float $amount
     * @param int $bseKey
     * @param int $paymentPREntryKey
     * @param int $parentPymtRecordKey
     * @param string $paymentDate
     */
    protected function setReversalAmount($amount, $bseKey, $paymentPREntryKey, $parentPymtRecordKey, $paymentDate)
    {
        $key = ContractUtil::generateCacheKeyForPaymentReversal($bseKey, $paymentPREntryKey, $parentPymtRecordKey,  $paymentDate);

        ContractUtil::setPaymentAndReversalAmountsFromARSub($key, $amount);

    }

    /**
     * On payment event handler
     *
     * @param   int     $billingScheduleEntryKey    Schedule entry key
     * @param   int     $paymentPREntrykey          Payment's prentry key
     * @param int $parentPymtRecordKey
     * @param   float   $amount                     Amount
     * @param   float   $postedExchRate             Invoice's exchange rate
     * @param   string  $transactionDate            Transaction date
     * @param   int     $contractKey                Contract key
     * @param   int     $contractDetailKey          Contract detail key
     * @param   string  $jrnlCode                   J1 or J2
     * @param   bool    $redoing                    true or false
     *
     * @throws IAException
     */
    protected function onPaymentInner($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $amount, $postedExchRate,
                                      $transactionDate, $contractKey, $contractDetailKey, $jrnlCode, $redoing)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $this->recordEvent(
            ContractGLReclassEvent::EVENTTYPE_ONPAYMENT,
            $jrnlCode,
            $amount,
            $contractDetailKey,
            $billingScheduleEntryKey,
            $paymentPREntrykey,
            $parentPymtRecordKey,
            $transactionDate
        );

        $cnResolveDataVersionKey = ContractUtil::getPaymentAndReversalDataVersionKey($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $jrnlCode, $transactionDate, ContractRevenueGLReclassEvent::EVENTTYPE_ONPAYMENT);
        $event = new ContractGLReclassOnPayment(
            $billingScheduleEntryKey,
            $paymentPREntrykey,
            $parentPymtRecordKey,
            $amount,
            $postedExchRate,
            $transactionDate,
            $contractKey,
            $contractDetailKey,
            $jrnlCode,
            $cnResolveDataVersionKey
        );

        $event->setRedoing($redoing);
        $event->execute();


        // Assert on final JEs collected so far
        if($event->getHistoricalFlag() == 'false') {
            ContractGLAssertionsUtil::assert(
                $amount,
                ContractGLReclassEvent::EVENTTYPE_ONPAYMENT,
                $jrnlCode,
                $contractDetailKey,
                $billingScheduleEntryKey,
                $paymentPREntrykey,
                $transactionDate
            );
        }

    }


    /**
     * Handles business logic on payment reversal
     *
     * @param int $billingScheduleEntryKey Schedule entry key
     * @param int $paymentPREntrykey Payment's prentry key
     * @param int $parentPymtRecordKey
     * @param float $amount Amount
     * @param string $transactionDate Transaction date
     * @param bool $redoing 'true' when re-executing the event
     * @param int|null $contractDetailKey Contract detail key
     * @throws IAException
     */
    public function onPaymentReversal($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $amount, $transactionDate, $redoing = false, $contractDetailKey = null)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        // We expect 0 value during redo in MEA scenario - case#12912937
        if (!$redoing && $amount == 0) {
            $msg = "Payment reversal amount cannot be 0. Billing schedule entry key:".
                   $billingScheduleEntryKey." and Pay line key:".$paymentPREntrykey;
            throw IAException::newIAException( 'CN-1575', $msg, ['BILLING_SCHEDULE_ENTRY_KEY' => $billingScheduleEntryKey, 'PAYMENT_ENTRYKEY' => $paymentPREntrykey]);
        }

        if (!$redoing) {

            ContractUtil::collectPaymentAndReversalDataVersion($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $transactionDate, ContractUtil::AR_SUBLEDGER_PAYMENTDATE_IS_RECLASSDATE);
        }

        $entry = ContractUtil::getBillingScheduleEntryById($billingScheduleEntryKey);

        $contractKey = $entry['CONTRACTKEY'];

        // $contractDetailKey and $entry['CONTRACTDETAILKEY'] may not match in some MEA scenarios
        if (!$contractDetailKey) {
            $contractDetailKey = $entry['CONTRACTDETAILKEY'];
        }

        $postedExchRate = $entry['POSTEDEXCHANGE_RATE'];

        if ($this->sourceContractDetailKey == null) {
            $this->sourceContractDetailKey = $contractDetailKey;
        }

        $processJ1 = false;
        if ($entry['REVENUETEMPLATEKEY']) {
            $processJ1 = true;
            if ($this->forcedJrnlCode && $this->forcedJrnlCode != ContractGLReclassEvent::JOURNALCODE_J1) {
                $processJ1 = false;
            }
        }

        $processJ2 = false;
        if ($entry['REVENUE2TEMPLATEKEY']) {
            $processJ2 = true;
            if ($this->forcedJrnlCode && $this->forcedJrnlCode != ContractGLReclassEvent::JOURNALCODE_J2) {
                $processJ2 = false;
            }
        }
    
        [$processJ1, $processJ2] = $this->getChildComponentProcessJournals($entry, $processJ1, $processJ2);

        if ($processJ1) {
            if (!$redoing) {
                $this->checkForFutureMEAs($contractDetailKey, $transactionDate, ContractGLReclassEvent::JOURNALCODE_J1);
            }

            $noPendingFutureMEAAtTheBeginningJ1 = !$this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J1];

            $this->onPaymentReversalOuter(
                $billingScheduleEntryKey,
                $paymentPREntrykey,
                $parentPymtRecordKey,
                $amount,
                $postedExchRate,
                $transactionDate,
                $contractKey,
                $contractDetailKey,
                ContractGLReclassEvent::JOURNALCODE_J1,
                $redoing
            );

            // This is the first event which started it all
            if ($noPendingFutureMEAAtTheBeginningJ1 && $this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J1]) {

                $this->runFutureMEAEvent(ContractGLReclassEvent::JOURNALCODE_J1, $contractDetailKey);

                $this->runFutureMEASyncedRedoEvents(ContractGLReclassEvent::JOURNALCODE_J1);
            }
        }

        if ($processJ2) {

            if (!$redoing) {
                $this->checkForFutureMEAs($contractDetailKey, $transactionDate, ContractGLReclassEvent::JOURNALCODE_J2);
            }

            $noPendingFutureMEAAtTheBeginningJ2 = !$this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J2];

            $this->onPaymentReversalOuter(
                $billingScheduleEntryKey,
                $paymentPREntrykey,
                $parentPymtRecordKey,
                $amount,
                $postedExchRate,
                $transactionDate,
                $contractKey,
                $contractDetailKey,
                ContractGLReclassEvent::JOURNALCODE_J2,
                $redoing
            );

            // This is the first event which started it all
            if ($noPendingFutureMEAAtTheBeginningJ2 && $this->futureMEAEvent[ContractGLReclassEvent::JOURNALCODE_J2]) {

                $this->runFutureMEAEvent(ContractGLReclassEvent::JOURNALCODE_J2, $contractDetailKey);

                $this->runFutureMEASyncedRedoEvents(ContractGLReclassEvent::JOURNALCODE_J2);
            }
        }
    }

    /**
     * On payment reversal event handler with and without MEA
     *
     * @param int $billingScheduleEntryKey Schedule entry key
     * @param int $paymentPREntrykey Payment's prentry key
     * @param int $parentPymtRecordKey
     * @param float $amount Amount
     * @param float $postedExchRate Invoice's exchange rate
     * @param string $transactionDate Transaction date
     * @param int $contractKey Contract key
     * @param int $contractDetailKey Contract detail key
     * @param string $jrnlCode J1 or J2
     * @param bool $redoing 'true' when re-executing the event
     *
     * @throws IAException
     */
    protected function onPaymentReversalOuter($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $amount, $postedExchRate,
                                              $transactionDate, $contractKey, $contractDetailKey, $jrnlCode, $redoing)
    {


        if (!$this->eventAlreadyExecuted(
            ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL,
            $jrnlCode,
            $contractDetailKey,
            $billingScheduleEntryKey,
            $paymentPREntrykey, $parentPymtRecordKey, $transactionDate
        )) {

            $this->onEventPreProcessing($contractDetailKey, $transactionDate, $jrnlCode);

            $this->dumpMethodCall(__FUNCTION__, func_get_args());
            $orderedEvents = null;

            if (!$this->clearAlreadyExecuted($contractDetailKey, $jrnlCode)) {
                $clearTransactionDate = $transactionDate;
                if (ContractUtil::isPostConResolveInOpenPeriod()) {
                    //$effectiveDate  is current open period which would be the different from mea date
                    //need to get actual transaction date of MEA to fetchj data from MEA bundle
                    $clearTransactionDate = ContractUtil::getEventActTranDate_ConSubPostInOpenPeriod(ContractGLReclassEvent::EVENTTYPE_ONINVOICE, $jrnlCode, ['BILLINGSCHENTRYKEY' => $billingScheduleEntryKey, 'PAYMENTPRENTRYKEY' => $paymentPREntrykey], $transactionDate);
                }
                // Delete self along with future events when redoing unless it is source contract detail
                if ($redoing) {

                    $identifierAttributes = [
                        'EVENTTYPE' => ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL,
                        'PAYMENTPRENTRYKEY' => $paymentPREntrykey,
                    ];

                    $this->clearExistingReclassTransactionsIncludingSelf(
                        $identifierAttributes,
                        $contractDetailKey,
                        $clearTransactionDate,
                        $jrnlCode,
                        $orderedEvents
                    );

                } else {

                    $this->clearExistingReclassTransactions(
                        $clearTransactionDate,
                        $contractDetailKey,
                        $jrnlCode,
                        $orderedEvents
                    );
                }
            }

            $runReclass = true;
            if (ContractUtil::contractDetailHasMEA($contractDetailKey, $jrnlCode, $transactionDate)) {
                if ($orderedEvents) {
                    $this->processFutureEventsForMEA($contractDetailKey, $jrnlCode, $orderedEvents, $hasFutureMEA);
                }

                // Keep local state of schedule links updated for future MEA to correctly undo of current MEA
                // Just presense of it means we are operating on old MEA
                if ($this->scheduleLinksToUse[$jrnlCode] ?? false) {
                    $this->updateLocalScheduleLinks(
                        $jrnlCode,
                        null,
                        $billingScheduleEntryKey,
                        null,
                        null,
                        false
                    );
                }

                $originalMEASplit = $this->getPaymentReversalMEaSplit(
                    $billingScheduleEntryKey,
                    $paymentPREntrykey,
                    $parentPymtRecordKey,
                    $amount,
                    $transactionDate,
                    $jrnlCode,
                    $redoing
                );


                //this will make sure that id billing is not sharing revenue then do not call payment inner mea
                if ($this->isBillingDistributing($billingScheduleEntryKey, $originalMEASplit)) {

                    //this will make sue that reclass register for other line one only
                    //only run reclass for other line for which orignal transaction effecting
                    if (ContractGLEventRegistry::registerLinkedReclass($redoing) && !$this->eventAlreadyExecuted(
                            ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL_REGISTRATION,
                            $jrnlCode,
                            '',
                            $billingScheduleEntryKey,
                            $paymentPREntrykey, $parentPymtRecordKey, $transactionDate
                    )) {

                        $this->recordEvent(
                            ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL_REGISTRATION,
                            $jrnlCode,
                            $amount, '',
                            $billingScheduleEntryKey,
                            $paymentPREntrykey, $parentPymtRecordKey, $transactionDate
                        );

                        foreach ($originalMEASplit as $newContractDetailKey => $newAmount) {

                            if ($newContractDetailKey != $contractDetailKey) {
                                ContractGLEventRegistry::registerOtherLinesForReclassInRegistry(
                                    $newContractDetailKey,
                                    __FUNCTION__,
                                    $jrnlCode,
                                    [
                                        'BILLINGSCHEDULEENTRYKEY' => $billingScheduleEntryKey,
                                        'PAYMENTPRENTRYKEY' => $paymentPREntrykey,
                                        'PARENTPYMTRECORDKEY' => $parentPymtRecordKey,
                                        'AMOUNT' => $amount,
                                        'POSTEDEXCHRATE' => $postedExchRate,
                                        'TRANSACTIONDATE' => $transactionDate,
                                        'CONTRACTKEY' => $contractKey,
                                        'JRNLCODE' => $jrnlCode,
                                        'REDOING' => $redoing,
                                        'CONTRACTDETAILKEY' => $newContractDetailKey
                                    ]
                                );
                            }
                        }
                    }

                    $this->onPaymentReversalMEAInner(
                        $billingScheduleEntryKey,
                        $paymentPREntrykey,
                        $parentPymtRecordKey,
                        $originalMEASplit,
                        $transactionDate,
                        $contractKey,
                        $contractDetailKey,
                        $postedExchRate,
                        $jrnlCode,
                        $redoing
                    );

                }
                $amount = $originalMEASplit[$contractDetailKey] ?? 0;
                if ($amount == 0) {
                    $runReclass = false;
                }

            } else {

                if ($redoing) {
                    // When redoing, get cached summed up amounts from resolve table
                    $arAmount = $this->getPaymentReversalAmount(
                        $billingScheduleEntryKey,
                        $paymentPREntrykey,
                        $parentPymtRecordKey,
                        $transactionDate,
                        $jrnlCode
                    );
                    if ($arAmount === null) {
                        $msg = "Error in retrieving stored original payment reversal amount. Billing schedule entry:" .
                            $billingScheduleEntryKey . ", Payment prentry key:" . $paymentPREntrykey;
                        throw IAException::newIAException(
                            'CN-1576',
                            $msg,
                            ['BILLING_SCHEDULE_ENTRY_KEY' => $billingScheduleEntryKey, 'PAYMENT_ENTRYKEY' => $paymentPREntrykey]);
                    }

                    $amount = $arAmount;
                }
            }

            if ($runReclass) {
                $this->onPaymentReversalInner(
                    $billingScheduleEntryKey,
                    $paymentPREntrykey,
                    $parentPymtRecordKey,
                    $amount,
                    $postedExchRate,
                    $transactionDate,
                    $contractKey,
                    $contractDetailKey,
                    $jrnlCode,
                    $redoing
                );
            }

            if ($orderedEvents && count($orderedEvents) > 0) {

                // Important to run reclass for other invoices and payments for J1 alone
                $this->forcedJrnlCode = $jrnlCode;

                $this->redoEvents($orderedEvents, $contractDetailKey, $jrnlCode);
            }

            if (!$orderedEvents && !$redoing) {
                $this->runNextContractDetailReclass($jrnlCode);
            }
        }
    }

    /**
     * Handles payment reversal event
     *
     * @param   int     $billingScheduleEntryKey    Schedule entry key
     * @param   int     $paymentPREntrykey          Payment's prentry key
     * @param int $parentPymtRecordKey
     * @param   float   $postedExchRate             Invoice's exchange rate
     * @param   float   $reversalAmount             Reversal amount
     * @param   string  $transactionDate            Transaction date
     * @param   int     $contractKey                Contract key
     * @param   int     $contractDetailKey          Contract detail key
     * @param   string  $jrnlCode                   J1 or J2
     * @param   bool    $redoing                    true or false
     *
     * @throws IAException
     */
    protected function onPaymentReversalInner($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $reversalAmount,
                                              $postedExchRate, $transactionDate, $contractKey, $contractDetailKey,
                                              $jrnlCode, $redoing)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $this->recordEvent(
            ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL,
            $jrnlCode,
            $reversalAmount,
            $contractDetailKey,
            $billingScheduleEntryKey,
            $paymentPREntrykey,
            $parentPymtRecordKey,
            $transactionDate
        );

        $cnResolveDataVersionKey = ContractUtil::getPaymentAndReversalDataVersionKey($billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $jrnlCode, $transactionDate, ContractRevenueGLReclassEvent::EVENTTYPE_ONPAYMENT);
        $event = new ContractGLReclassOnPaymentReversal(
            $billingScheduleEntryKey, $paymentPREntrykey, $parentPymtRecordKey, $reversalAmount, $postedExchRate, $transactionDate, $contractKey, $contractDetailKey, $jrnlCode, $cnResolveDataVersionKey
        );

        $event->setRedoing($redoing);
        $event->execute();

      //   Assert on final JEs collected so far
        if($event->getHistoricalFlag() == 'false') {
            ContractGLAssertionsUtil::assert(
                $reversalAmount,
                ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL,
                $jrnlCode,
                $contractDetailKey,
                $billingScheduleEntryKey,
                $paymentPREntrykey
            );
        }

    }

    /**
     * Does gl adjustments on MEA
     *
     * @param   int     $contractKey        Contract detail key
     * @param   string  $effectiveDate      MEA effective date
     * @param   array   $addlData           Additional data stored with resolve record
     * @param   bool    $redoing            'true' when re-executing the event
     * @param   string  $jrnlCode           Journal code
     * @param   array   $cnDetailKeys       Array contract detail keys
     *
     * @throws IAException
     */
    public function onMEA($contractKey, $effectiveDate, $addlData, $redoing, $jrnlCode, $cnDetailKeys=null)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args(), [5 => 'IMPLODE']);

        if (!($contractKey && $effectiveDate)) {
            throw new IAException("Contract key and MEA effective date is required.", 'CN-1577');
        }

        if (!$jrnlCode) {
            throw new IAException("Journal is required to run MEA. This is not expected.", 'CN-1578');
        }

        // Check against GL posting date
        if (!$redoing) {
            $this->checkMEAAgainstGLPostingdate($cnDetailKeys, $effectiveDate);
        }

        $existingMEAVersion = null;
        $meaVersion = null;
        $hasExistingMEA = false;
        ContractUtil::getMEAVersion($contractKey, $jrnlCode, $hasExistingMEA, $meaVersion, $existingMEAVersion, $redoing, $effectiveDate);

        if ( $meaVersion ==  ContractGLReclassEvent::CONTRACT_HAS_MEA_V2 ) {

            // Figure out affected contract detail keys for the journal always in v2
            $cndKeyToAlloc = $this->getMEAIncludedContractDetailKeysForJournal_v2(
                $effectiveDate,
                $contractKey,
                $jrnlCode
            );

            $affectedDestinationCNDetailKeys = array_keys($cndKeyToAlloc);

            $affectedCNDetailKeys = $this->getSourceAndDestinationCNDetailKeys(
                $affectedDestinationCNDetailKeys, $addlData
            );

            $this->onMEAInner(
                $contractKey,
                $effectiveDate,
                $addlData,
                $redoing,
                $jrnlCode,
                $affectedCNDetailKeys,
                $cndKeyToAlloc
            );

            return;
        }

        // We know specific MEA to run
        if ($jrnlCode != null && $cnDetailKeys != null) {

            $this->onMEAInner($contractKey, $effectiveDate, $addlData, $redoing, $jrnlCode, $cnDetailKeys);

        } else  if ($jrnlCode) {

            // We are re-running MEA for a journal, figure out affected contract detail keys the journal
            $affectedDestinationCNDetailKeys = $this->getMEAIncludedContractDetailKeysForJournal(
                $effectiveDate,
                $contractKey,
                $jrnlCode
            );

            $affectedCNDetailKeys = $this->getSourceAndDestinationCNDetailKeys(
                $affectedDestinationCNDetailKeys, $addlData
            );

            $this->onMEAInner(
                $contractKey,
                $effectiveDate,
                $addlData,
                $redoing,
                $jrnlCode,
                $affectedCNDetailKeys
            );

        } else {

            throw new IAException("MEA can only be re-executed with a specific journal. This is not expected.", 'CN-1579');

        }
    }

    /**
     * @param int[] $affectedDestinationCNDetailKeys
     * @param array $addlData
     *
     * @return int[]
     */
    protected function getSourceAndDestinationCNDetailKeys($affectedDestinationCNDetailKeys, $addlData)
    {
        if (!$addlData['SCHEDULELINKS']) {
            $links = ContractGLReclassOnMEA::getScheduleLinks($affectedDestinationCNDetailKeys, $this->forcedJrnlCode);
        } else {
            $links = $addlData['SCHEDULELINKS'];
        }

        $cndKeys = $affectedDestinationCNDetailKeys;
        foreach ($links as $link) {
            if (in_array($link['DESTDETAILKEY'], $affectedDestinationCNDetailKeys)) {
                $cndKeys[] = $link['SRCDETAILKEY'];
            }
        }

        return  array_unique($cndKeys);
    }


    /**
     * Retrieves contract detail keys included in MEA for a journal
     *
     * @param  string|null   $effectiveDate  MEA effective date
     * @param  int      $contractKey    Contract key
     * @param  int      $jrnlCode       Journal code
     *
     * @return array
     *
     * @throws IAException
     */
    protected function getMEAIncludedContractDetailKeysForJournal($effectiveDate, $contractKey, $jrnlCode)
    {
        if (ContractUtil::isPostConResolveInOpenPeriod()) {
            //$effectiveDate  is current open period which would be the different from mea date
            //need to get actual transaction date of MEA to fetchj data from MEA bundle

            $effectiveDate = ContractUtil::getEventActTranDate_ConSubPostInOpenPeriod(
                $this->bundleEventType,
                $jrnlCode,
                ['CONTRACTKEY' => $contractKey],
                $effectiveDate
            );
        }

        $filters = ['CONTRACTKEY' => $contractKey, 'EFFECTIVEDATE' => $effectiveDate];

        if ($jrnlCode == 'J1') {
            $filters['APPLYTOJOURNAL1'] = 'true';
        } else {
            $filters['APPLYTOJOURNAL2'] = 'true';
        }
    
        $meaBundles = EntityManager::GetListQuick($this->bundleEntity, ['RECORDNO'], $filters,[['RECORDNO', 'ASC']]);
    
        if ($meaBundles === false || !isset($meaBundles[0]['RECORDNO'])) {
            // TODO: tokenize for i18n
            if ($this->bundleType === ContractBundleManager::BUNDLE_TYPE_KIT) {
                $msg = "Kit bundle not found for effective date $effectiveDate (Contract key:$contractKey, Journal: $jrnlCode)";
                throw IAException::newIAException( 'CN-1580', $msg, ['EFFECTIVE_DATE' => FormatDateForDisplay($effectiveDate), 'CONTRACT_KEY' => $contractKey, 'JRNL_CODE' => $jrnlCode]);
            } else {
                $msg = "MEA bundle not found for effective date $effectiveDate (Contract key:$contractKey, Journal: $jrnlCode)";
                throw IAException::newIAException( 'CN-1581', $msg, ['EFFECTIVE_DATE' => FormatDateForDisplay($effectiveDate), 'CONTRACT_KEY' => $contractKey, 'JRNL_CODE' => $jrnlCode]);
            }
        }

        $bundleKey = $meaBundles[count($meaBundles)-1]['RECORDNO'];

        $meaBundleEntries = EntityManager::GetListQuick(
            $this->bundleEntryEntity,
            ['CONTRACTDETAILKEY'],
            ['BUNDLEKEY' => $bundleKey],
            [['CONTRACTDETAILKEY', 'ASC']]
        );

        $contractDetailKeys = [];
        foreach ($meaBundleEntries as $entry) {
            $contractDetailKeys[] = $entry['CONTRACTDETAILKEY'];
        }

        return $contractDetailKeys;
    }

    /**
     * Retrieves contract detail keys to allocation keys map included in MEA for a journal
     *
     * @param  string|null   $effectiveDate  MEA effective date
     * @param  int      $contractKey    Contract key
     * @param  int      $jrnlCode       Journal code
     *
     * @return array
     *
     * @throws IAException
     */
    protected function getMEAIncludedContractDetailKeysForJournal_v2($effectiveDate, $contractKey, $jrnlCode)
    {
        if (ContractUtil::isPostConResolveInOpenPeriod()) {
            //$effectiveDate is current open period which would be the different from mea date
            //need to get actual transaction date of MEA to fetch data from MEA bundle
            $effectiveDate =  ContractUtil::getEventActTranDate_ConSubPostInOpenPeriod(
                $this->bundleEventType,
                $jrnlCode,
                ['CONTRACTKEY' => $contractKey],
                $effectiveDate);
        }

        $filters = ['CONTRACTKEY' => $contractKey, 'EFFECTIVEDATE' => $effectiveDate];

        if ($jrnlCode == 'J1') {
            $filters['APPLYTOJOURNAL1'] = 'true';
        } else {
            $filters['APPLYTOJOURNAL2'] = 'true';
        }

        $meaBundles = EntityManager::GetListQuick($this->bundleEntity, ['RECORDNO'], $filters,[['RECORDNO', 'ASC']]);

        if ($meaBundles === false || !isset($meaBundles[0]['RECORDNO'])) {
            // TODO: tokenize for i18n
            if ($this->bundleType === ContractBundleManager::BUNDLE_TYPE_KIT) {
                $msg = "Kit bundle not found for effective date $effectiveDate (Contract key:$contractKey, Journal: $jrnlCode)";
                throw IAException::newIAException( 'CN-1580', $msg, ['EFFECTIVE_DATE' => FormatDateForDisplay($effectiveDate), 'CONTRACT_KEY' => $contractKey, 'JRNL_CODE' => $jrnlCode]);
            } else {
                $msg = "Kit bundle not found for effective date $effectiveDate (Contract key:$contractKey, Journal: $jrnlCode)";
                throw IAException::newIAException( 'CN-1580', $msg, ['EFFECTIVE_DATE' => FormatDateForDisplay($effectiveDate), 'CONTRACT_KEY' => $contractKey, 'JRNL_CODE' => $jrnlCode]);
            }
        }

        // We are using the latest MEA on the effective date
        $bundleKey = $meaBundles[count($meaBundles)-1]['RECORDNO'];

        $meaAllocDetails = EntityManager::GetListQuick(
            'contractallocationdetail',
            ['RECORDNO','CONTRACTDETAILKEY', 'TOTALFLATAMOUNT', 'TOTALFLATBASEAMOUNT', 'MEAAMOUNT', 'MEABASEAMOUNT'],
            ['BUNDLEKEY' => $bundleKey],
            [['CONTRACTDETAILKEY', 'ASC']]
        );

        $cndKeyToAlloc = [];
        foreach ($meaAllocDetails as $alloc) {
            $cndKeyToAlloc[$alloc['CONTRACTDETAILKEY']] = $alloc;
        }

        return $cndKeyToAlloc;
    }

    /**
     * Note: Not used for now since it returns no lines when subsequent MEA reverts to original line amounts. Keeping the method for reference
     *
     * Retrieves contract detail keys affected by MEA for a journal
     *
     * @param  int  $contractKey    Contract key
     * @param  int  $jrnlCode       Journal code
     *
     * @return array
     *
     * @throws IAException
     */
    protected function getMEAAffectedContractDetailKeysForJournal($contractKey, $jrnlCode)
    {
        // Figure out
        // 1. Affected schedule type (schedule 1 or 2?)
        // 2. Affected lines

        // Get amount for each line
        $amountsMap = [];
        $qry = "SELECT totalflatamount, record# FROM contractdetail WHERE cny#=:1 AND contractkey = :2";
        $result = QueryResult([$qry, GetMyCompany(), $contractKey]);
        foreach ($result as $res) {
            $amountsMap[$res['RECORD#']] = $res['TOTALFLATAMOUNT'];
        }

        $schType = $jrnlCode == ContractGLReclassEvent::JOURNALCODE_J1 ? 'R' : 'R2';

        // Get sum of rev schedule entries by schedule type and line
        // Only if its different from line amount
        $affectedCNDetailKeys = [];
        $qry = "SELECT SUM(csr.amount) amount, cs.contractdetailkey
                FROM contractschedulesresolve csr, contractscheduleentry cse, contractschedule cs, contractdetail cd
                WHERE csr.cny#=:1 AND cse.cny#=:1 AND cs.cny#=:1 AND cd.cny#=:1 AND csr.revschentrykey = cse.record#
                AND cse.schedulekey = cs.record# AND cs.contractdetailkey = cd.record# AND cd.contractkey = :2
                AND cs.type = :3
                GROUP BY cs.contractdetailkey";
        $result = QueryResult([$qry, GetMyCompany(), $contractKey, $schType]);
        foreach ($result as $res) {
            if (!isset($amountsMap[$res['CONTRACTDETAILKEY']])) {
                $msg = "Could not find flat amount from contract detail key: ".
                       $res['CONTRACTDETAILKEY'];
                throw IAException::newIAException( 'CN-1582', $msg, ['RES_CONTRACTDETAILKEY' => $res['CONTRACTDETAILKEY']]);
            }

            $lineAmount = $amountsMap[$res['CONTRACTDETAILKEY']];
            if ($res['AMOUNT'] != $lineAmount) {
                $affectedCNDetailKeys[] = $res['CONTRACTDETAILKEY'];
            }
        }

        return $affectedCNDetailKeys;
    }

    /**
     * Retrieves contract detail keys affected by MEA
     *
     * @param  int  $contractKey    Contract key
     * @return array
     * @throws IAException
     */
    protected function getMEAAffectedContractDetailKeys($contractKey)
    {
        // Figure out
        // 1. Affected lines
        // 2. Affected journal (schedule 1 or 2?)

        // Get amount for each line
        $amountsMap = [];
        $qry = "SELECT totalflatamount, record# FROM contractdetail WHERE cny#=:1 AND contractkey = :2";
        $result = QueryResult([$qry, GetMyCompany(), $contractKey]);
        foreach ($result as $res) {
            $amountsMap[$res['RECORD#']] = $res['TOTALFLATAMOUNT'];
        }

        $schTypeToJrnlTypeMap = [
            'R'     => ContractGLReclassEvent::JOURNALCODE_J1,
            'R2'    => ContractGLReclassEvent::JOURNALCODE_J2
        ];

        // Get sum of rev schedule entries by schedule type and line
        // Only if its different from line amount
        $affectedCNDetailsByJournalCode = [];
        $qry = "SELECT SUM(csr.amount) amount, cs.type, cs.contractdetailkey
                FROM contractschedulesresolve csr, contractscheduleentry cse, contractschedule cs, contractdetail cd
                WHERE csr.cny#=:1 AND cse.cny#=:1 AND cs.cny#=:1 AND cd.cny#=:1 AND csr.revschentrykey = cse.record#
                AND cse.schedulekey = cs.record# AND cs.contractdetailkey = cd.record# AND cd.contractkey = :2
                GROUP BY cs.type, cs.contractdetailkey";
        $result = QueryResult([$qry, GetMyCompany(), $contractKey]);
        foreach ($result as $res) {
            if (!isset($amountsMap[$res['CONTRACTDETAILKEY']])) {
                $msg ="Could not find flat amount from contract detail key: ".
                    $res['CONTRACTDETAILKEY'];
                throw IAException::newIAException( 'CN-1582', $msg, ['RES_CONTRACTDETAILKEY' => $res['CONTRACTDETAILKEY']]);
            }

            $lineAmount = $amountsMap[$res['CONTRACTDETAILKEY']];
            if ($res['AMOUNT'] != $lineAmount) {
                $schType = $res['TYPE'];

                $jrnlCode = $schTypeToJrnlTypeMap[$schType];
                $affectedCNDetailsByJournalCode[$jrnlCode][] = $res['CONTRACTDETAILKEY'];
            }
        }

        // Ensure J1 is first - helps debug by a journal
        ksort($affectedCNDetailsByJournalCode);

        return $affectedCNDetailsByJournalCode;
    }

    /**
     * @param   int         $contractKey        Contract detail key
     * @param   string      $effectiveDate      MEA effective date
     * @param   array|null  $addlData           Additional data stored with resolve record
     * @param   bool        $redoing            'true' when re-executing the event
     * @param   string      $jrnlCode           Journal code
     * @param   array       $cnDetailKeys       Array contract detail keys
     * @param   array       $cndKeyToAlloc      Map of cnd keys and allocation detail
     *
     * @throws IAException
     */
    protected function onMEAInner($contractKey, $effectiveDate, $addlData, $redoing, $jrnlCode, $cnDetailKeys, $cndKeyToAlloc=null)
    {

        $previousMEACnDetailKeys = ContractUtil::meaContractDetailKeysOnOrBeforeDate($contractKey, $effectiveDate, $jrnlCode, ContractGLReclassEvent::EVENTTYPE_ONMEA);

        // it has all cn detail key which has been removed from current mea or added in th current MEA
        $pastAndCurrentDetailKeys = array_unique(array_merge($previousMEACnDetailKeys, $cnDetailKeys)); 
            
        foreach ($pastAndCurrentDetailKeys as $cnDetailKey) {
            $this->onEventPreProcessing($cnDetailKey, $effectiveDate, $jrnlCode);
        }

        $this->dumpMethodCall(__FUNCTION__, func_get_args(), [5 => 'IMPLODE']);

        $redoEventsByCNDetailKey = [];

        // When redoing, delete existing events for all lines but source
        if ($redoing) {
            $clearTransactionDate = $effectiveDate;
            // Trigger re-evaluation of all future events for the line when redoing events
            if (ContractUtil::isPostConResolveInOpenPeriod()) {
                //$effectiveDate  is current open period which would be the different from mea date
                //need to get actual transaction date of MEA to fetchj data from MEA bundle
                $clearTransactionDate =  ContractUtil::getEventActTranDate_ConSubPostInOpenPeriod(
                    $this->bundleEventType,
                    $jrnlCode,
                    ['CONTRACTKEY' => $contractKey ],
                    $effectiveDate
                );
            }
            foreach ($pastAndCurrentDetailKeys as $cnDetailKey) {
                if ($this->sourceContractDetailKey && $this->sourceContractDetailKey == $cnDetailKey) {
                    // Make sure pass source line's future redo events
                    if ($this->futureMEASyncedRedoEvents[$jrnlCode][$cnDetailKey]) {
                        $redoEventsByCNDetailKey[$cnDetailKey] = $this->futureMEASyncedRedoEvents[$jrnlCode][$cnDetailKey];
                    }

                    continue;
                }

                $identifierAttributes = ['EVENTTYPE' => $this->bundleEventType, 'TRANSACTIONDATE' => $effectiveDate];

                $orderedEvents = null;
                $this->clearExistingReclassTransactionsIncludingSelf(
                    $identifierAttributes,
                    $cnDetailKey,
                    $clearTransactionDate,
                    $jrnlCode,
                    $orderedEvents
                );

                if ($orderedEvents) {
                    $redoEventsByCNDetailKey[$cnDetailKey] = $orderedEvents;
                }
            }
        } else {
            foreach ($pastAndCurrentDetailKeys as $cnDetailKey) {
                $orderedEvents = null;
                $this->clearExistingReclassTransactions(
                    $effectiveDate,
                    $cnDetailKey,
                    $jrnlCode,
                    $orderedEvents
                );

                if ($orderedEvents) {
                    $redoEventsByCNDetailKey[$cnDetailKey] = $orderedEvents;
                }
            }
        }

        $existingMEAVersion = null;
        $meaVersion = null;
        $hasExistingMEA = false;
        ContractUtil::getMEAVersion($contractKey, $jrnlCode, $hasExistingMEA, $meaVersion, $existingMEAVersion, $redoing, $effectiveDate);

        if ($this->isACPFlowLocal($effectiveDate)) {
            // 'Historical flag' is set against schedule entry.
            // ACP reclass 'On MEA' event.
            // Skip Unbilled and Billed accounting for ACP reclass.

            $historicalFlag = 'true';
        } else {
            // Normal Reclass 'On MEA' event.

            $historicalFlag = 'false';
        }

        // Undo old MEA
        if ($hasExistingMEA) {
            // If we maintain schedule links in the state then it has correct flags compared to one in $addlData
            $oldSchLinks =  $this->scheduleLinksToUse[$jrnlCode] ?: $addlData['OLD_SCHEDULELINKS'];

            $oldMEADist = $addlData['OLD_MEADISTRIBUTION'];

            // If last MEA has reset amounts back to original then we will not have any old schedulelinks
            if ($oldSchLinks && $oldMEADist) {

                // Undo old
                $undoSchLinks = $this->swapSourceAndDestination($oldSchLinks, $oldCNDetailKeys);
                
                $undoAddlData['SCHEDULELINKS'] = $undoSchLinks;

                if ( $existingMEAVersion === ContractGLReclassEvent::CONTRACT_HAS_MEA_V2 ) {
                    $undoMEAEvent = new ContractGLReclassOnUndoMEAv2(
                        $this->bundleType,
                        $this->bundleKey,
                        $contractKey,
                        $effectiveDate,
                        $undoAddlData,
                        $jrnlCode,
                        $oldCNDetailKeys,
                        $redoEventsByCNDetailKey,
                        $oldSchLinks,
                        $cnDetailKeys,
                        $oldMEADist,
                        $historicalFlag,
                        $redoing
                    );
                } else {
                    $undoMEAEvent = new ContractGLReclassOnUndoMEA(
                        $this->bundleType,
                        $this->bundleKey,
                        $contractKey,
                        $effectiveDate,
                        $undoAddlData,
                        $jrnlCode,
                        $oldCNDetailKeys,
                        $redoEventsByCNDetailKey,
                        $oldSchLinks,
                        $cnDetailKeys,
                        $oldMEADist,
                        $historicalFlag
                    );
                }

                $undoMEAEvent->setRedoing($redoing);
                $undoMEAEvent->execute();

                // Avoid fetching new schedule links again
                $addlData['SCHEDULELINKS'] = $undoMEAEvent->getAddlDataNew();
                
                $this->updateRedoEventBasedLinking($redoEventsByCNDetailKey, $addlData['SCHEDULELINKS']);
            }

        } else {
            // Do not use cached links this is the only MEA. Data in the table is current
            $addlData = null;
        }

        if ( $meaVersion === ContractGLReclassEvent::CONTRACT_HAS_MEA_V2 ) {
            $undoInstructions = null;
            if ( $hasExistingMEA && isset($undoMEAEvent) ) {
                $undoInstructions = $undoMEAEvent->getInstructions();
            }

            // Do new MEA
            $event = new ContractGLReclassOnMEAv2(
                $this->bundleType,
                $this->bundleKey,
                $contractKey,
                $effectiveDate,
                $addlData,
                $jrnlCode,
                $cnDetailKeys,
                $redoEventsByCNDetailKey,
                $historicalFlag,
                $redoing || $hasExistingMEA, // Update ADDLDATA with MEA instructions
                $undoInstructions,
                $cndKeyToAlloc,
                !$hasExistingMEA
            );
        } else {
            // Do new MEA
            $event = new ContractGLReclassOnMEA(
                $this->bundleType,
                $this->bundleKey,
                $contractKey,
                $effectiveDate,
                $addlData,
                $jrnlCode,
                $cnDetailKeys,
                $redoEventsByCNDetailKey,
                $historicalFlag
            );
        }

        $event->setRedoing($redoing);
        $event->execute();

        if($event->getHistoricalFlag() == 'false') {
            ContractGLAssertionsUtil::assertMEA($this->bundleEventType, $jrnlCode, $contractKey, $cnDetailKeys);
        }
        $this->forcedJrnlCode = $jrnlCode;

        if ( ContractUtil::enableMEAReclassV2() ) {
            // For now unset only if v2 is not enabled
            // We need updated cache for next invocation of reclass engine

            // Remember the last updated links to be passed as an input the next instance of reclass engine

            $this->meaDistributionCacheOldUpdated[$contractKey][$jrnlCode] = $this->meaDistributionCache[$jrnlCode];

            $this->scheduleLinksToUseOldUpdated[$contractKey][$jrnlCode] = $this->scheduleLinksToUse[$jrnlCode];
        }

        // Unset cached MEA distribution so that future events retrieve fresh distribution
        $this->meaDistributionCache[$jrnlCode] = [];

        // No need to maintain this schedule links after MEA is executed, this is stored in DB now
        $this->scheduleLinksToUse[$jrnlCode] = [];

        foreach ($redoEventsByCNDetailKey as $cnDetailKey => $events) {
            // Source lines' redo events will be run by redo stack of source event
            if ($this->sourceContractDetailKey && $this->sourceContractDetailKey == $cnDetailKey) {
                continue;
            }

            foreach ($events as $singleEvents){

                if($singleEvents['EVENTTYPE'] == ContractGLReclassEvent::EVENTTYPE_ONMEA && $singleEvents['TRANSACTIONDATE'] != $effectiveDate ){
                    $cnDetail = EntityManager::GetListQuick('contractdetail', ['CONTRACTID', 'LINENO'], ['RECORDNO' => $singleEvents['CONTRACTDETAILKEY']]);
                    $cnId = $cnDetail[0]['CONTRACTID'];
                    $lineNo = $cnDetail[0]['LINENO'];
                    $lastMEADate = $effectiveDate;
                    $currMEADate = $singleEvents['TRANSACTIONDATE'];

                    $msg = "$cnId $lineNo is participating in MEA allocations on $lastMEADate and $currMEADate, transactions cannot be posted before $lastMEADate. Clear MEA allocations, post the transaction, and then re-create the MEA allocation.";
                    throw IAException::newIAException( 'CN-1583', $msg, ['CN_ID' => $cnId, 'LINE_NO' => $lineNo, 'LAST_DATE' => FormatDateForDisplay($lastMEADate), 'CURR_DATE' => FormatDateForDisplay($currMEADate)]);
                }
            }

            $this->redoEvents($events, $cnDetailKey, $jrnlCode);
        }
    }

    /**
     * @param array|null $redoEventsByLine
     * @param array|null $linking
     *
     * @return void
     */
    private function updateRedoEventBasedLinking(?array &$redoEventsByLine, ?array $linking)
    {
        if ( ! empty($redoEventsByLine) && ! empty($linking) ) {

            $billingdistributionKeyStore= [];
            //create key for billing schedule entry and source line
            foreach ( $linking as $oneLink ) {
                $billingdistributionKeyStore[] = $oneLink['BILLSCHENTRYKEY'] . '_' . $oneLink['SRCDETAILKEY'];
            }

            //update redo event
            foreach ( $redoEventsByLine as &$oneLineRedoEvent ) {
                foreach ( $oneLineRedoEvent as $index  => $redoEvent ) {
                    if ( $redoEvent['EVENTTYPE'] == [ ContractGLReclassEvent::EVENTTYPE_ONINVOICE ] ) {
                        $key = $redoEvent['FK'] . '_' . $redoEvent['CONTRACTDETAILKEY'];

                        if ( ! in_array($key, $billingdistributionKeyStore) ) {
                            unset($oneLineRedoEvent[$index]);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Swaps source and destination contract detail keys
     *
     * @param array $oldSchLinks
     * @param int[] $cnDetailKeys
     *
     * @return array
     */
    protected function swapSourceAndDestination($oldSchLinks, &$cnDetailKeys)
    {
        $swappedSchLinks = [];
        $cnDetailKeys = [];
        foreach ($oldSchLinks as $link) {
            $srcCNDKey = $link['SRCDETAILKEY'];
            $destCNDKey = $link['DESTDETAILKEY'];

            $swappedLink = $link;

            $swappedLink['SRCDETAILKEY'] = $destCNDKey;
            $swappedLink['DESTDETAILKEY'] = $srcCNDKey;

            $swappedSchLinks[] = $swappedLink;
            $cnDetailKeys[] = $destCNDKey;
            $cnDetailKeys[] = $srcCNDKey;
        }

        $cnDetailKeys = array_unique($cnDetailKeys);

        return $swappedSchLinks;
    }

    /**
     * Does gl adjustments on MEA delete
     *
     * @param int[]       $contractDetailKeys
     * @param bool        $journal1
     * @param bool        $journal2
     * @param null|string $effectiveDate
     * @throws IAException
     */
    public function onMEADelete($contractDetailKeys, $journal1 = false, $journal2= false, $effectiveDate=null)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args(), ['IMPLODE', 'IMPLODE']);
        
        $addlDataKeys = $this->getAddlDataKeys($contractDetailKeys, $effectiveDate);

        ContractGLEventRegistry::forceRgisterLinkedReclass();

        if ($journal1) {
            $this->onMEADeleteInner(
                $contractDetailKeys,
                ContractGLReclassEvent::JOURNALCODE_J1,
                $effectiveDate
            );
        }

        if ($journal2) {
            $this->onMEADeleteInner(
                $contractDetailKeys,
                ContractGLReclassEvent::JOURNALCODE_J2,
                $effectiveDate
            );
        }

        // Delete addldata rows
        $this->deleteMEAAddlData($addlDataKeys);

        // Clear the cached minOnRecognitionForMEADelete for next onMEADelete (if any).
        $this->clearCacheMinOnRecognitionForMEADelete();
    }

    /**
     * @param int[]       $addlDataKeys
     * @throws IAException
     */
    protected function deleteMEAAddlData($addlDataKeys)
    {
        if (!empty($addlDataKeys)) {
            $addlDataMgr = Globals::$g->gManagerFactory->getManager('contractrslvaddldata');

            // For old records there may not be any contractrslvaddldata records
            foreach ($addlDataKeys as $addlDataKey) {
                $ok = $addlDataMgr->Delete($addlDataKey);
                if (!$ok) {
                    $msg = "Error deleting contractrslvaddldata record: $addlDataKey";
                    throw IAException::newIAException( 'CN-1584', $msg, ['ADDL_DATA_KEY' => $addlDataKey]);
                }
            }
        }
    }

    /**
     * Handles MEA delete for a journal
     *
     * @param   array   $contractDetailKeys
     * @param   string  $jrnlCode
     * @param   string  $effectiveDate
     *
     * @throws IAException
     *
     */
    protected function onMEADeleteInner($contractDetailKeys, $jrnlCode, $effectiveDate='')
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args(), ['IMPLODE']);

        $resolveStartRec = $this->getStartResolveRecOnMEADelete($contractDetailKeys, $jrnlCode, $effectiveDate);
        if (!$resolveStartRec) {
            return;
        }

        $orderedEvents = [];
        $reclassGLBatches = [];
        $this->getEventsInOrderOnMEADelete(
            $resolveStartRec,
            $contractDetailKeys,
            $jrnlCode,
            $orderedEvents,
            $reclassGLBatches,
            $effectiveDate
        );

        foreach ($contractDetailKeys as $contractDetailKey) {
            $this->deleteResolve($resolveStartRec, $contractDetailKey, $jrnlCode, ['A', 'R'] , $effectiveDate);
        }

        if ($reclassGLBatches) {
            $this->undoOnMEAReclassGLBatches($reclassGLBatches, $effectiveDate);
        }

        if ($this->bundleType == ContractBundleManager::BUNDLE_TYPE_KIT) {
            // Do not redo events for Kit delete since they will fail
            // OnInvoice does not work for Kit component lines since they have no billing schedule
            // OnRecognize does not work for Kit lines since they have no revenue schedule
            // These events only work for a Kit bundle
            // We can skip redoing events since Kit bundles are only temporarily deleted and then recreated automatically
            $newOrderedEventsByLine = [];
        } else {
            // Events for a line will distributed across mulitple lines due to MEA
            // This needs to straightened i.e. move events to correct line
            $newOrderedEventsByLine = $this->correctEventsOnMEADelete($orderedEvents);
        }

        foreach ($newOrderedEventsByLine as $contractDetailKey => $newOrderedEvents) {
            $this->setExecutedClearTransactions($contractDetailKey, $jrnlCode, $newOrderedEvents);

            if (!empty($newOrderedEvents)) {
                $this->forcedJrnlCode = $jrnlCode;
                $this->redoEvents($newOrderedEvents, $contractDetailKey, $jrnlCode);
            }
        }
    }

    /**
     * Fix Contract line reference and summarize by BSE and Payment PR Entry key
     *
     * @param array $orderedEvents
     *
     * @return array
     *
     * @throws IAException
     */
    private function correctEventsOnMEADelete($orderedEvents)
    {
        $newOrderedEvents = [];

        $invoiceEvents = [];
        $paymentEvents = [];

        foreach ($orderedEvents as $event) {

            if (in_array($event['EVENTTYPE'], [
                ContractGLReclassEvent::EVENTTYPE_ONPAYMENT,
                ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL
            ])) {

                // Correct contract line
                $cnDetailKey = $event['ORIG_CONTRACTDETAILKEY'];

                // Summarize by BSE and PRENTRY key to make it one payment

                $bseKey = $event['BILLINGSCHENTRYKEY'];
                $paymentPREntryKey = $event['FK'];

                $key = $event['EVENTTYPE']."_{$bseKey}_{$paymentPREntryKey}";
                if ($paymentEvents[$key]) {

                    $paymentEvents[$key]['AMOUNT'] += $event['AMOUNT'];

                } else {

                    $event['CONTRACTDETAILKEY'] = $cnDetailKey;
                    $paymentEvents[$key] = $event;
                }

            } else if ($event['EVENTTYPE'] == ContractGLReclassEvent::EVENTTYPE_ONINVOICE) {

                // Correct contract line
                $cnDetailKey = $event['ORIG_CONTRACTDETAILKEY'];

                // Add it only ones
                $bseKey = $event['FK'];
                if (!$invoiceEvents[$bseKey]) {

                    $event['CONTRACTDETAILKEY'] = $cnDetailKey;
                    $invoiceEvents[$bseKey] = $event;

                }

            }

            // Recognition events may exist in re-MEA scenario
            // i.e. if posted RSE exists between 1st and 2nd MEA effective dates
        }

        // TODO: Optimize
        $paymentEventIncluded = [];
        $invoiceEventIncluded = [];

        // Group by contract line in order
        foreach ($orderedEvents as $event) {

            // Correct contract line
            $cnDetailKey = $event['ORIG_CONTRACTDETAILKEY']
                ?: $event['CONTRACTDETAILKEY'];

            if (in_array($event['EVENTTYPE'],
                [ContractGLReclassEvent::EVENTTYPE_ONPAYMENT, ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL])
            ) {

                // Group by BSE and PRENTRY key to make it one payment

                $bseKey = $event['BILLINGSCHENTRYKEY'];
                $paymentPREntryKey = $event['FK'];

                $key = $event['EVENTTYPE']."_{$bseKey}_{$paymentPREntryKey}";
                if (!$paymentEventIncluded[$key]) {
                    $newOrderedEvents[$cnDetailKey][] = $paymentEvents[$key];
                    $paymentEventIncluded[$key] = true;
                }

            } else if ($event['EVENTTYPE'] == ContractGLReclassEvent::EVENTTYPE_ONINVOICE) {

                // Add it only ones
                $key = $event['FK'];
                if (!$invoiceEventIncluded[$key]) {
                    $newOrderedEvents[$cnDetailKey][] = $invoiceEvents[$key];
                    $invoiceEventIncluded[$key] = true;
                }

            } else {

                $newOrderedEvents[$cnDetailKey][] = $event;
            }
        }

        return $newOrderedEvents;
    }

    /**
     * Removes effect of old MEA from GL
     *
     * @param   array   $reclassGLBatches
     * @param   string  $effectiveDate
     *
     * @throws IAException
     *
     */
    private function undoOnMEAReclassGLBatches($reclassGLBatches, $effectiveDate='')
    {
        $toReverseGLBatches = [];
        $toDeleteGLBatches = [];

        foreach ($reclassGLBatches as $glBatchDetails) {
            $glBatchKey = $glBatchDetails['GLBATCHKEY'];
            $glDate = $glBatchDetails['TRANSACTIONDATE'];

            if ($effectiveDate != '') {

                // Reverse only if gl date is in past wrt MEA effective date
                if (DateCompare($glDate, $effectiveDate) < 0) {
                    $toReverseGLBatches[] = $glBatchKey;
                } else {
                    $toDeleteGLBatches[] = $glBatchKey;
                }

            } else {
                // If effective date is not set we always delete
                $toDeleteGLBatches[] = $glBatchKey;
            }
        }

        if ($toReverseGLBatches) {
            $toReverseGLBatches = array_unique($toReverseGLBatches);
            ContractUtil::reverseGLBatches($toReverseGLBatches, $effectiveDate);
        }

        if ($toDeleteGLBatches) {
            $toDeleteGLBatches = array_unique($toDeleteGLBatches);
            $this->deleteGLBatches($toDeleteGLBatches);
        }
    }

    /**
     * Does gl re-class on Usage invoice generation
     *
     * @param   int     $usageBillingId     Usage billing id
     * @param   float   $amount             Amount
     * @param   float   $baseAmount         Base amount
     * @param   float   $exchRate           Exchange rate
     * @param   string  $transactionDate    Invoice date
     * @param   int     $contractKey        Contract key
     * @param   int     $contractDetailKey  Contract detail key
     *
     * @throws IAException
     */
    public function onUsageInvoice($usageBillingId, $amount, $baseAmount, $exchRate, $transactionDate, $contractKey,
                                   $contractDetailKey)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $cnDetail = $this->getContractDetail($contractDetailKey);

        if ($cnDetail['REVENUETEMPLATEKEY']) {
            $this->onUsageInvoiceInner($usageBillingId, $amount, $baseAmount, $exchRate, $transactionDate, $contractKey,
                $contractDetailKey, ContractGLReclassEvent::JOURNALCODE_J1);
        }

        if ($cnDetail['REVENUE2TEMPLATEKEY']) {
            $this->onUsageInvoiceInner($usageBillingId, $amount, $baseAmount, $exchRate, $transactionDate, $contractKey,
                $contractDetailKey, ContractGLReclassEvent::JOURNALCODE_J2);
        }
    }

    /**
     * Does gl re-class on Usage invoice generation
     *
     * @param   int     $usageBillingId     Usage billing id
     * @param   float   $amount             Amount
     * @param   float   $baseAmount         Base amount
     * @param   float   $exchRate           Exchange rate
     * @param   string  $transactionDate    Invoice date
     * @param   int     $contractKey        Contract key
     * @param   int     $contractDetailKey  Contract detail key
     * @param   string  $jrnlCode           J1 or J2
     *
     * @throws IAException
     */
    public function onUsageInvoiceInner($usageBillingId, $amount, $baseAmount, $exchRate, $transactionDate,
                                        $contractKey, $contractDetailKey, $jrnlCode)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        if ($this->isACPFlowLocal($transactionDate)) {
            // 'Historical flag' is set against billing schedule entry.
            // ACP reclass 'On Usage Invoice' event.
            // Skip accounting for ACP reclass.

            $historicalFlag = 'true';
        }
        else {
            // Normal Reclass 'On Usage Invoice' event.

            $historicalFlag = 'false';
        }

        $event = new ContractGLReclassOnUsageInvoice(
            $usageBillingId,
            $amount,
            $baseAmount,
            $exchRate,
            $transactionDate,
            $contractKey,
            $contractDetailKey,
            $jrnlCode,
            $historicalFlag
        );

        $event->execute();
    }

    /**
     * Re-calculates re-class on Usage invoice deletion
     *
     * @param   int     $usageBillingID    Usage billing id
     *
     * @throws IAException
     */
    public function onUsageInvoiceDelete($usageBillingID)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $usageInfo = EntityManager::GetListQuick(
            'contractusagebilling',
            ['BILLEDDATE'],
            ['USAGEBILLINGID' => $usageBillingID]
        );

        $isACP = false ;
        if(!empty($usageInfo)) {
            $isACP = $this->isACPFlowLocal($usageInfo[0]['BILLEDDATE']);
        }

        if(!$isACP) {

            if ($this->hasPaidUsage($usageBillingID, ContractGLReclassEvent::JOURNALCODE_J1)) {
                throw new IAException("Usage has paid balance for Journal 1, invoice cannot be deleted.", 'CN-1585');
            }

            if ($this->hasPaidUsage($usageBillingID, ContractGLReclassEvent::JOURNALCODE_J2)) {
                throw new IAException("Usage has paid balance for Journal 2, invoice cannot be deleted.", 'CN-1586');
            }
        }

        $glBatches = $this->getUsageGLBatches($usageBillingID);
        $this->deleteGLBatches($glBatches);

        $this->deleteUsageResolve($usageBillingID);
    }

    /**
     * Does gl re-class on usage payment
     *
     * @param   int     $usageBillingId             Usage billing id
     * @param   int     $paymentPREntrykey          Payment's prentry key
     * @param   float   $amount                     Amount
     * @param   string  $transactionDate            Transaction date
     *
     * @throws IAException
     */
    public function onUsagePayment($usageBillingId, $paymentPREntrykey, $amount, $transactionDate)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $this->validateUsagePaymentDate($usageBillingId, $transactionDate);

        self::getUsageBillingContractKeys($usageBillingId, $contractDetailKey, $contractKey);

        $exchRate = $this->getUsageBillingExchangeRate($usageBillingId);

        $cnDetail = $this->getContractDetail($contractDetailKey);

        if ($cnDetail['REVENUETEMPLATEKEY']) {
            $this->onUsagePaymentInner($usageBillingId, $paymentPREntrykey, $amount, $exchRate, $transactionDate,
                $contractKey, $contractDetailKey, ContractGLReclassEvent::JOURNALCODE_J1);
        }

        if ($cnDetail['REVENUE2TEMPLATEKEY']) {
            $this->onUsagePaymentInner($usageBillingId, $paymentPREntrykey, $amount, $exchRate, $transactionDate,
                $contractKey, $contractDetailKey, ContractGLReclassEvent::JOURNALCODE_J2);
        }
    }

    /**
     * Usage payment reclass logic by journal
     *
     * @param   int     $usageBillingId             Usage billing id
     * @param   int     $paymentPREntrykey          Payment PR entry key
     * @param   float   $amount                     Invoice amount
     * @param   float   $exchRate                   Contract detail exchange rate
     * @param   string  $transactionDate            Transaction date
     * @param   int     $contractKey                Contract key
     * @param   int     $contractDetailKey          Contract detail key
     * @param   string  $jrnlCode                   J1 or J2

     *
     * @throws IAException
     */
    protected function onUsagePaymentInner($usageBillingId, $paymentPREntrykey, $amount, $exchRate, $transactionDate,
                                $contractKey, $contractDetailKey, $jrnlCode)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $balances = $this->getUsageBalances($usageBillingId, $jrnlCode, $transactionDate);
        // Make sure there is enough in Billed balances is available be moved to the Paid balances;  It is possible
        // that Billed balances and paid amount could be both negative if there is credit and a portion of the
        // credit is being applied for payment
        if (!$balances['B'] || ($balances['B'] < $amount && !($balances['B'] < 0 && $amount < 0))
            || ($balances['B'] > $amount && $balances['B'] < 0 && $amount < 0)) {
            $msg = "Not enough balance in Billed Sales for usage billing id : $usageBillingId.
                This is not expected.";
            throw IAException::newIAException( 'CN-1587', $msg, ['USAGE_BILLING_ID' => $usageBillingId]);
        }

        $event = new ContractGLReclassOnUsagePayment(
            $usageBillingId,
            $paymentPREntrykey,
            $amount,
            $exchRate,
            $transactionDate,
            $contractKey,
            $contractDetailKey,
            $jrnlCode
        );

        $event->execute();
    }

    /**
     * Does gl re-class on payment reversal
     *
     * @param   int     $usageBillingId             Usage billing id
     * @param   int     $paymentPREntrykey          Payment's prentry key
     * @param   float   $amount                     Amount
     * @param   string  $transactionDate            Transaction date
     *
     * @throws IAException
     */
    public function onUsagePaymentReversal($usageBillingId, $paymentPREntrykey, $amount, $transactionDate)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        self::getUsageBillingContractKeys($usageBillingId, $contractDetailKey, $contractKey);

        $exchRate = $this->getUsageBillingExchangeRate($usageBillingId);

        $cnDetail = $this->getContractDetail($contractDetailKey);

        if ($cnDetail['REVENUETEMPLATEKEY']) {
            $this->onUsagePaymentReversalInner($usageBillingId, $paymentPREntrykey, $amount, $exchRate, $transactionDate,
                $contractKey, $contractDetailKey, ContractGLReclassEvent::JOURNALCODE_J1);
        }

        if ($cnDetail['REVENUE2TEMPLATEKEY']) {
            $this->onUsagePaymentReversalInner($usageBillingId, $paymentPREntrykey, $amount, $exchRate, $transactionDate,
                $contractKey, $contractDetailKey, ContractGLReclassEvent::JOURNALCODE_J2);
        }
    }


    /**
     * Usage payment reversal reclass logic by journal
     *
     * @param   int     $usageBillingId             Usage billing id
     * @param   int     $paymentPREntrykey          Payment PR entry key
     * @param   float   $amount                     Invoice amount
     * @param   float   $exchRate                   Contract detail exchange rate
     * @param   string  $transactionDate            Transaction date
     * @param   int     $contractKey                Contract key
     * @param   int     $contractDetailKey          Contract detail key
     * @param   string  $jrnlCode                   J1 or J2

     *
     * @throws IAException
     */
    protected function onUsagePaymentReversalInner($usageBillingId, $paymentPREntrykey, $amount, $exchRate, $transactionDate,
                                           $contractKey, $contractDetailKey, $jrnlCode)
    {
        $this->dumpMethodCall(__FUNCTION__, func_get_args());

        $balances = $this->getUsageBalances($usageBillingId, $jrnlCode, $transactionDate);
        $trType = 1;

        if (!empty($balances['P']) && $balances['P'] < 0 && $amount < 0) {
            $trType = -1;
        }

        if (!$balances['P'] || (($balances['P'] * $trType) < ($amount * $trType))) {
            $msg = "Not enough balance in Paid Sales for usage billing id : $usageBillingId.
                This is not expected.";
            throw IAException::newIAException( 'CN-1588', $msg, ['USAGE_BILLING_ID' => $usageBillingId]);
        }

        $event = new ContractGLReclassOnUsagePaymentReversal(
            $usageBillingId,
            $paymentPREntrykey,
            $amount,
            $exchRate,
            $transactionDate,
            $contractKey,
            $contractDetailKey,
            $jrnlCode
        );

        $event->execute();
    }

    /**
     * Retrieves exchange from docentry
     *
     * @param  int    $usageBillingId     Usage billing id
     *
     * @return string
     *
     * @throws IAException
     */
    protected function getUsageBillingExchangeRate($usageBillingId)
    {
        $docEntry = EntityManager::GetListQuick(
            'sodocumententry',
            ['EXCHRATE'],
            ['BILLABLECONTRACTUSAGEBILLINGID' => $usageBillingId]
        );

        if (!($docEntry && $docEntry[0])) {
            $msg = "Could not find docentry record for usage billing id: $usageBillingId";
            throw IAException::newIAException( 'CN-1589', $msg, ['USAGE_BILLING_ID' => $usageBillingId]);
        }

        return $docEntry[0]['EXCHRATE'];
    }

    /**
     * Retrieves contract and contract detail keys for usage billing
     *
     * @param  int    $usageBillingId     Usage billing id
     * @param  int    $contractDetailKey  Contract detail key
     * @param  int    $contractKey        Contract key
     *
     * @throws IAException
     */
    public static function getUsageBillingContractKeys($usageBillingId, &$contractDetailKey, &$contractKey)
    {
        $qry = "SELECT cu.contractdetailkey, cnd.contractkey FROM contractusagebilling ub, contractdetail cnd,
                contractusage cu WHERE ub.cny#=:1 AND cnd.cny#=:1 AND cu.cny#=:1 AND ub.usagebillingid=:2
                AND ub.contractusagekey=cu.record# AND cu.contractdetailkey = cnd.record#";
        $res = QueryResult([$qry, GetMyCompany(), $usageBillingId]);
        if (!($res && $res[0])) {
            $msg =  "Could not find contract information for usage billing id: ".$usageBillingId;
            throw IAException::newIAException( 'CN-1590', $msg, ['USAGE_BILLING_ID' => $usageBillingId]);
        }

        $contractDetailKey = (int)$res[0]['CONTRACTDETAILKEY'];
        $contractKey = (int)$res[0]['CONTRACTKEY'];
    }

    /**
     * Deletes usage resolve records
     *
     * @param   int     $usageBillingID     Usage billing id
     *
     * @throws IAException
     */
    protected function deleteUsageResolve($usageBillingID)
    {
        $qry = "DELETE FROM contractresolve WHERE cny#=:1 AND billablecontractusagebillingid=:2";
        $ok = ExecStmt([$qry, GetMyCompany(), $usageBillingID]);
        if (!$ok) {
            $msg = "Error deleting resolve records for billablecontractusagebillingid: ".
                   $usageBillingID;
            throw IAException::newIAException( 'CN-1591', $msg, ['USAGE_BILLING' => $usageBillingID]);
        }
    }

    /**
     * Returns all reclass glbatches associated with usage billing id
     *
     * @param   int     $usageBillingID     Usage billing id
     *
     * @return array
     */
    protected function getUsageGLBatches($usageBillingID)
    {
        $qry = "SELECT glbatchkey FROM contractresolve WHERE cny#=:1 AND glbatchkey IS NOT NULL AND type='U'
                AND billablecontractusagebillingid =:2 AND balancetype='R'";
        $result = QueryResult([$qry, GetMyCompany(), $usageBillingID]);

        $glBatches = [];
        foreach ($result as $res) {
            $glBatches[] = $res['GLBATCHKEY'];
        }

        return $glBatches;
    }

    /**
     * Checks if usage as paid balance
     *
     * @param   int     $usageBillingID     Usage billing id
     * @param   string  $jrnlCode           J1 or J2
     *
     * @return bool
     */
    protected function hasPaidUsage($usageBillingID, $jrnlCode)
    {
        $balances = $this->getUsageBalances($usageBillingID, $jrnlCode);
        return isset($balances['P']) && $balances['P'] > 0;
    }

    /**
     * Retrieves usage balances
     *
     * @param   int     $usageBillingID     Usage billing id
     * @param   string  $jrnlCode           J1 or J2
     * @param   string    $asOfDate           As of date
     *
     * @return array
     */
    protected function getUsageBalances($usageBillingID, $jrnlCode, $asOfDate='')
    {
        $qry = "SELECT SUM(tr_type*amount) balance, classification FROM contractresolve WHERE cny#=:1 AND type in ('U', 'A')
                AND billablecontractusagebillingid =:2 AND journaltype=:3 AND balancetype='R'";

        if ($asOfDate != '') {
            $qry .= " AND transactiondate <= :4";
        }

        $qry .= " GROUP BY classification";
        $qparams = [$qry, GetMyCompany(), $usageBillingID, $jrnlCode];

        if ($asOfDate != '') {
            $qparams[] = $asOfDate;
        }
        $result = QueryResult($qparams);

        $map = [];
        foreach ($result as $res) {
            $map[$res['CLASSIFICATION']] = $res['BALANCE'];
        }

        return $map;
    }

    /**
     * Checks if amount can be updated for a contract detail
     *
     * @param   int     $contractDetailKey  Contract detail key
     *
     * @return mixed
     */
    public function isUpdatable($contractDetailKey)
    {
        $isUpdatable = true;
        if ($this->hasBilledOrSalesBalance($contractDetailKey, 'J1') ||
            $this->hasBilledOrSalesBalance($contractDetailKey, 'J2')
        ) {
            $isUpdatable = false;
        }

        return $isUpdatable;
    }

    /**
     * Returns true if the line referenced by $contractDetailKey has ever been billed or recognized
     *
     * @param int    $contractDetailKey
     * @param string $jrnlType
     *
     * @return bool
     */
    private function hasBilledOrSalesBalance($contractDetailKey, string $jrnlType) : bool
    {
        $retVal = false;
        if ( $this->balanceExists($contractDetailKey, 'R', $jrnlType) ) {
            $balances = self::getBalances($contractDetailKey, 'R', $jrnlType, '', false);
            $hasDeferredBilledOrPaid = $balances[0]['BILLED'] != 0 || $balances[0]['PAID'] != 0;
            $hasSales = $balances[1]['TOTAL'] != 0;     // true if any recognition happened already, regardless of UNBILLED, BILLED, or PAID
            if ( $hasDeferredBilledOrPaid || $hasSales ) {
                $retVal = true;
            }
        }
        return $retVal;
    }

    /**
     * Actually deletes all balances from DB for contract detail except expense re-class events.
     *
     * @param   int      $contractDetailKey Contract detail key
     * @param   int|null $rowcount
     *
     * @throws IAException
     */
    public function clearBalances($contractDetailKey, &$rowcount=null)
    {
        $selQry = "SELECT glbatchkey FROM v_contractresolvefixed WHERE cny#=:1 AND contractdetailkey=:2 AND balancetype <> 'E'";
        $resGLBatches = QueryResult([$selQry, GetMyCompany(), $contractDetailKey]);

        $glBatchKeys = [];
        foreach ($resGLBatches as $res) {
            if ($res['GLBATCHKEY'] && !in_array($res['GLBATCHKEY'], $glBatchKeys)) {
                $glBatchKeys[] = $res['GLBATCHKEY'];
            }
        }

        // 1. Delete all contractresolve for this line
        $qry = "DELETE FROM contractresolve WHERE cny#=:1 AND contractdetailkey=:2 AND balancetype <> 'E' AND type in ('".ContractGLReclassEvent::RESOLVETYPE_FIXED_V ."','" .ContractGLReclassEvent::RESOLVETYPE_TIME_V."')";
        $ok = ExecStmtEx(array($qry, GetMyCompany(), $contractDetailKey), $rowcount, 1);
        if (!$ok) {
            $msg = "Error removing contractresolve records for contractdetailkey:" . $contractDetailKey;
            throw IAException::newIAException( 'CN-1592', $msg, ['CONTRACT_DETAIL_KEY' => $contractDetailKey]);
        }

        // 2. Explicitly delete gl batches
        $this->deleteGLBatches($glBatchKeys);
    }

    /**
     * Checks if balance records exist for contract detail
     *
     * @param   int     $contractDetailKey  Contract detail key
     * @param   string  $balanceType        AR or Revenue
     * @param   string  $jrnlType           Journal 1 or Journal 2
     *
     * @return bool
     */
    public function balanceExists($contractDetailKey, $balanceType, $jrnlType)
    {
        $qry = "SELECT COUNT(1) as count FROM v_contractresolvefixed WHERE cny# = :1 AND contractdetailkey = :2 AND
                balancetype = :3 AND journaltype = :4";

        $res = QueryResult(array($qry, GetMyCompany(), $contractDetailKey, $balanceType, $jrnlType));

        return $res[0]['COUNT'] > 0;
    }

    /**
     * Checks if resolve records after transaction date exist in the system for contract line and journal
     *
     * @param   string  $transactionDate    Transaction date
     * @param   int     $contractDetailKey  Contract detail key
     * @param   string  $jrnlCode           J1 or J2
     *
     * @return bool
     */
    protected function hasExistingReclassTransactions($transactionDate, $contractDetailKey, $jrnlCode)
    {
        //Event type "N" stand for "oncancel, "L" stand for on revaluation, "J" for data adj
        $qry = "SELECT COUNT(1) AS count FROM v_contractresolvefixed WHERE contractdetailkey = :1 AND journaltype = :2
                  AND balancetype IN ('A', 'R') AND cny# = :4 AND eventtype not in ('N', 'L', 'J') and type != 'A'";

        if(ContractUtil::isPostConResolveInOpenPeriod()){
            $qry .= " and ( actualtransactiondate > :3 or (transactiondate > :3 and actualtransactiondate IS NULL ) ) ";
        }else{
            $qry .= " and transactiondate > :3 ";
        }
        $res = QueryResult([$qry, $contractDetailKey, $jrnlCode, $transactionDate, GetMyCompany()]);

        return $res[0] && $res[0]['COUNT'] > 0;
    }

    /**
     * Checks if recognition resolve records on or after transaction date exist in the system for the contract and
     * journal
     *
     * @param   string  $transactionDate    Transaction date
     * @param   int     $contractKey        Contract key
     * @param   string  $jrnlCode           J1 or J2
     *
     * @return bool
     */
    protected function hasPostedRevenueForContract($transactionDate, $contractKey, $jrnlCode)
    {
        $qry = "SELECT COUNT(1) AS count FROM v_contractresolvefixed WHERE contractkey = :1 AND journaltype = :2
                AND transactiondate >= :3  AND balancetype IN ('A', 'R') AND cny# = :4 AND eventtype = 'R'";
        $res = QueryResult([$qry, $contractKey, $jrnlCode, $transactionDate, GetMyCompany()]);

        return $res[0] && $res[0]['COUNT'] > 0;
    }
    
    /**
     * Deletes resolve records and associated gl batches posted after transaction date
     *
     * @param string     $transactionDate Transaction date
     * @param int        $contractDetailKey Contract detail key
     * @param string     $jrnlCode Journal code
     * @param array|null $orderedEvents Return array of ordered events to be re-executed
     *
     * @throws IAException
     */
    protected function clearExistingReclassTransactions($transactionDate, $contractDetailKey, $jrnlCode,
                                                        &$orderedEvents, $runOnCreate = false)
    {
        if (!$this->hasExistingReclassTransactions($transactionDate, $contractDetailKey, $jrnlCode)) {

            $this->setExecutedClearTransactions($contractDetailKey, $jrnlCode, []);

            return;
        }

        // 1. Get all resolve records after transaction date with unique events and re-class gl batches
        $this->getEventsInOrder(
            $transactionDate,
            $contractDetailKey,
            $jrnlCode,
            $startResolveRec,
            $orderedEvents,
            $reclassGLBatches,
            $resolveRecNos,
            $runOnCreate
        );

        // 2. Delete resolves
        $this->deleteResolveByRecNos($resolveRecNos);

        // 3. Explicitly delete re-class glbatchs
        if (!empty($reclassGLBatches)) {
            $this->deleteGLBatches($reclassGLBatches);
        }

        $this->setExecutedClearTransactions($contractDetailKey, $jrnlCode, $orderedEvents);
    }

    /**
     * @param string $transactionDate Transaction date
     * @param int $contractDetailKey Contract detail key
     * @param string $jrnlCode Journal code
     * @param int $startResolveRec First resolve record# after transaction date
     * @param array   &$orderedEvents Return array of ordered events to be re-executed
     * @param array   &$reclassGLBatches Return array of re-class glbatches to be deleted
     * @param array   &$resolveRecNos Return array of re-class glbatches to be deleted
     * @param bool $runOnCreate
     *
     * @throws IAException
     */
    protected function getEventsInOrder($transactionDate, $contractDetailKey, $jrnlCode, &$startResolveRec,
                                        &$orderedEvents, &$reclassGLBatches, &$resolveRecNos, $runOnCreate = false)
    {
        $jrnlType = $jrnlCode == ContractGLReclassEvent::JOURNALCODE_J1
            ? ContractGLReclassEvent::JOURNALTYPE_J1 : ContractGLReclassEvent::JOURNALTYPE_J2;

        $filters = [
            ['CONTRACTDETAILKEY', '=', $contractDetailKey],
            ['JOURNALTYPE', '=', $jrnlType],
            ['BALANCETYPE', 'IN', [
                ContractGLReclassEvent::BALANCETYPE_AR,
                ContractGLReclassEvent::BALANCETYPE_REVENUE]
            ],
            ['TYPE', 'NOT IN', [ContractGLReclassEvent::RESOLVETYPE_USAGEBASED, ContractGLReclassEvent::RESOLVETYPE_ADJUST]]
        ];

        $exclEventTypes = [ContractGLReclassEvent::EVENTTYPE_ONCANCEL, ContractGLReclassEvent::EVENTTYPE_DATA_ADJUSTMENT];
        if (!$runOnCreate) {
            $exclEventTypes[] = ContractGLReclassEvent::EVENTTYPE_ONCONTRACT;
        }
        $filters[] = ['EVENTTYPE', 'NOT IN', $exclEventTypes];

        if (ContractUtil::isPostConResolveInOpenPeriod()) {
            $filters[] = [
                'operator' => 'or',
                'filters' => [
                    ["ACTUALTRANSACTIONDATE", '>', $transactionDate],
                    [
                        'operator' => 'and',
                        'filters' =>
                            [
                                ['TRANSACTIONDATE', '>', $transactionDate],
                                ['ACTUALTRANSACTIONDATE', 'IS NULL'],
                            ],
                    ],
                ]
            ];
        } else {
            $filters[] = ['TRANSACTIONDATE', '>', $transactionDate];
        }
        $cnResolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');
        $selects = $this->getResolveMgrGetFields($cnResolveMgr);

        $params = [
            'selects' => $selects,
            'filters' => [$filters],
            'orders' => [['TRANSACTIONDATE'], ['RECORDNO']]
        ];

        $allCNResolves = $cnResolveMgr->GetListActive($params);
        if (empty($allCNResolves)) {
            $msg = "No reclass records exist after $transactionDate";
            throw IAException::newIAException( 'CN-1593', $msg, ['TRANSACTION_DATE' => FormatDateForDisplay($transactionDate)]);
        }

        $startResolveRec = $allCNResolves[0]['RECORDNO'];

        $newAllCNResolves = [];
        $reclassGLBatches = [];
        $paymentBSEKeys = [];
        foreach ($allCNResolves as $resolve) {

            // Always take all glbatches involved in re-class
            if ($resolve['GLBATCHKEY'] && !in_array($resolve['GLBATCHKEY'], $reclassGLBatches)) {
                $reclassGLBatches[] = $resolve['GLBATCHKEY'];
            }

            if ($resolve['BILLINGSCHENTRYKEY'] && $resolve['PAYMENTPRENTRYKEY']) {
                $paymentDate = $resolve['ACTUALTRANSACTIONDATE'] ?? $resolve['TRANSACTIONDATE'];
                ContractUtil::collectPaymentAndReversalDataVersion($resolve['BILLINGSCHENTRYKEY'], $resolve['PAYMENTPRENTRYKEY'], $resolve['PARENTPYMTRECORDKEY'], $paymentDate, $resolve['DATAVERSIONKEY']);
            }

            if ($resolve['BILLINGSCHENTRYKEY'] && $resolve['PAYMENTPRENTRYKEY'] &&
                !in_array($resolve['BILLINGSCHENTRYKEY'], $paymentBSEKeys)) {
                $paymentBSEKeys[] = $resolve['BILLINGSCHENTRYKEY'];
            }

            $resolveRecNos[] = $resolve['RECORDNO'];
            $newAllCNResolves[] = $resolve;
        }

        if (!empty($paymentBSEKeys)) {
            $this->cachePaymentAmountSplitByBillingScheduleEntry($paymentBSEKeys);
        }

        $orderedEvents = $this->summarizeResolvesByEvent($newAllCNResolves);
    }

    /**
     * Return first resolve rec matching the criteria
     *
     * @param   string  $transactionDate        Transaction date
     * @param   int     $contractDetailKey      Contract detail key
     * @param   string  $jrnlType               Journal type
     * @param   array   $uniqueAttributes       Attributes to uniquely identify events
     *
     * @return string
     */
    public function getResolveRec($contractDetailKey, $transactionDate, $jrnlType, $uniqueAttributes)
    {
        $filters = [
            ['CONTRACTDETAILKEY',   '=',            $contractDetailKey],
            ['JOURNALTYPE',         '=',            $jrnlType],
            ['BALANCETYPE',         'IN',           [ContractGLReclassEvent::BALANCETYPE_AR,
                ContractGLReclassEvent::BALANCETYPE_REVENUE]],
            ['EVENTTYPE',           'NOT IN',           [ ContractGLReclassEvent::EVENTTYPE_ONCONTRACT, ContractGLReclassEvent::EVENTTYPE_ONCANCEL ]],
            ['TYPE',                '!=',        ContractGLReclassEvent::RESOLVETYPE_USAGEBASED  ]
        ];

        if(ContractUtil::isPostConResolveInOpenPeriod()){
            $filters[]  = [
                'operator' => 'OR',
                'filters'  => [
                    ['TRANSACTIONDATE', '=', $transactionDate],
                    ['ACTUALTRANSACTIONDATE', '=', $transactionDate],
                ]
            ];
        }else{
            $filters[] =  ['TRANSACTIONDATE', '=',  $transactionDate];
        }
        foreach ($uniqueAttributes as $attribute => $value) {
            if(is_array($value)){
                $filters[] = [$attribute, 'in', $value];
            }else {
                $filters[] = [$attribute, '=', $value];
            }
        }

        $cnResolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');
        $selects = $cnResolveMgr->GetGetFields();
        $selects[] = 'ADDLDATA';
        $selects[] = 'ADDLDATAKEY';

        $params = [
            'usemst'    => true, // Since we are filtering on CONTRACTDETAILKEY we can use mst, which is faster
            'selects'   => $selects,
            'filters'   => [$filters],
            'orders'    => [['RECORDNO']]
        ];

        $resolves = $cnResolveMgr->GetListActive($params);

        return $resolves[0]['RECORDNO'];
    }

    /**
     * @param   int     $startResolveRec    Start resolve record#
     * @param   int     $contractDetailKey  Contract detail key
     * @param   string  $jrnlCode           J1 or J2
     * @param   array   $balanceTypes       List of balance types
     * @param   string  $transactionDate    Transaction date
     *
     * @throws IAException
     */
    public function deleteResolve($startResolveRec, $contractDetailKey, $jrnlCode, $balanceTypes,
                                     $transactionDate='')
    {
        $balanceTypesStr = "'" . implode("', '", $balanceTypes) . "'";

        if(ContractUtil::isPostConResolveInOpenPeriod()){

            $contractDetailInfo = EntityManager::GetListQuick('contractdetail', ['CONTRACTKEY'], ['RECORDNO' => $contractDetailKey]);
            $glOpenDate = ContractUtil::getCompanyOpenDate($contractDetailInfo[0]['CONTRACTKEY']);
            $this->markInactiveContractResolve($startResolveRec,$contractDetailKey,$jrnlCode, $balanceTypesStr, $glOpenDate);
            $transactionDate = $glOpenDate; // to delete from glopen period only as resolve need to post in open period
        }

        $qry = "DELETE FROM v_contractresolvefixed WHERE cny#=:1 AND record# >= :2 AND contractdetailkey = :3 AND
                journaltype = :4 AND balancetype IN ($balanceTypesStr) AND eventtype NOT IN ('C', 'N') AND type !='A'";

        if ($transactionDate != '') {
            $qry .= " AND transactiondate >= :5 ";
        }

        $qparams = [$qry, GetMyCompany(), $startResolveRec, $contractDetailKey, $jrnlCode];
        if ($transactionDate != '') {
            $qparams[] = $transactionDate;
        }

        $ok = ExecStmt($qparams);

        if (!$ok) {
            throw new IAException("Error deleting contract resolve records.", 'CN-1594');
        }
    }

    /**
     * @param int $startResolveRec
     * @param int $contractDetailKey
     * @param string $jrnlCode
     * @param string $balanceTypesStr
     * @param string $glOpenDate
     * @throws IAException
     */
    private function markInactiveContractResolve($startResolveRec,$contractDetailKey,$jrnlCode, $balanceTypesStr, $glOpenDate){
        $qry = "update contractresolve set inactive = 'T' WHERE cny#=:1 AND record# >= :2 AND contractdetailkey = :3 AND
                journaltype = :4 AND transactiondate < :5 AND balancetype IN ($balanceTypesStr) AND eventtype NOT IN ('C', 'N') AND type !='A'";

        $qparams = [$qry, GetMyCompany(), $startResolveRec, $contractDetailKey, $jrnlCode , $glOpenDate];

        $ok = ExecStmt($qparams);
        if (!$ok) {
            throw new IAException("Error marking  contract resolve inactive records.", 'CN-1595');
        }
    }
    /**
     * @param array $resolveRecNos Resolve record#'s array
     *
     * @throws IAException
     */
    protected function deleteResolveByRecNos($resolveRecNos)
    {
        if (arrayCount($resolveRecNos) > 0) {

            $transactionDate = '';
            if (ContractUtil::isPostConResolveInOpenPeriod()) {

                $contractDetailInfo = EntityManager::GetListQuick('contractresolve', ['CONTRACTKEY'], ['RECORDNO' => $resolveRecNos[0]]);
                $glOpenDate = ContractUtil::getCompanyOpenDate($contractDetailInfo[0]['CONTRACTKEY']);
                $this->markInactiveContractResolveByRecNos($resolveRecNos, $glOpenDate);
                $transactionDate = $glOpenDate;

            }
            $query = "delete from v_contractresolvefixed where cny#=:1 and " ;

            if ($transactionDate !== '') {
                $query .=" transactiondate >=: 2 and " ;
            }
            $stmtDesc = [$query, GetMyCompany()];

            if ($transactionDate !== '') {
                $stmtDesc[] = $transactionDate;
            }

            $stmtDesc = PrepINClauseStmt($stmtDesc, $resolveRecNos, 'record#');
            $ok = ExecStmt($stmtDesc);

            if (!$ok) {
                throw new IAException("Error deleting contract resolve records.", 'CN-1594');
            }
        }
    }

    /**
     * @param array $resolveRecNos
     * @param string $transactionDate
     * @throws IAException
     */
    private function markInactiveContractResolveByRecNos($resolveRecNos, $transactionDate)
    {

        $stmtDesc = ["update v_contractresolvefixed  set inactive = 'T' where cny#=:1 and transactiondate <:2 and ", GetMyCompany(), $transactionDate];
        $stmtDesc = PrepINClauseStmt($stmtDesc, $resolveRecNos, 'record#');

        $ok = ExecStmt($stmtDesc);
        if (!$ok) {
            throw new IAException("Error marking  contract resolve inactive records.", 'CN-1595');
        }
    }
    /**
     * Returns could of resolve records for contract detail key and journal
     * except expense re-class events.
     *
     * @param  int      $contractDetailKey  Contract detail key
     * @param  string   $jrnlCode           Journal code
     *
     * @return int
     */
    protected function getResolveCount($contractDetailKey, $jrnlCode)
    {
        $qry = "SELECT COUNT(1) NUMRECORDS FROM contractresolve
                WHERE cny#=:1 AND contractdetailkey=:2 and journaltype=:3 and eventtype <> 'E'";
        $res = QueryResult([$qry, GetMyCompany(), $contractDetailKey, $jrnlCode]);

        return $res[0]['NUMRECORDS'] ?: 0;
    }

    /**
     * Deletes all resolve records for contract detail key and journal
     *
     * @param  int      $contractDetailKey  Contract detail key
     * @param  string   $jrnlCode           Journal code
     *
     * @throws IAException
     */
    protected function deleteResolveRecordsAndGLBatches($contractDetailKey, $jrnlCode)
    {
        $selQry = "SELECT glbatchkey FROM contractresolve WHERE cny#=:1 AND contractdetailkey=:2
                    AND journaltype=:3 AND balancetype != 'E'";
        $results = QueryResult([$selQry, GetMyCompany(), $contractDetailKey, $jrnlCode]);

        $glBatchKeys = [];
        foreach ($results as $res) {
            if ($res['GLBATCHKEY'] != '') {
                $glBatchKeys[] = $res['GLBATCHKEY'];
            }
        }

        // 1. Remove reference from contractresolve records
        $qry = "UPDATE contractresolve SET glbatchkey = NULL WHERE cny#=:1 AND contractdetailkey=:2 AND journaltype=:3 AND balancetype != 'E'";
        $ok = ExecStmt(array($qry, GetMyCompany(), $contractDetailKey, $jrnlCode));
        if (!$ok) {
            $msg = "Error removing glbatch reference from contractresolve records".
                   " for contract detail key: $contractDetailKey and journal $jrnlCode";
            throw IAException::newIAException( 'CN-1596', $msg, ['CONTRACT_DETAIL_KEY' => $contractDetailKey, 'JRNL_CODE' => $jrnlCode]);
        }

        // 2. Explicitly delete gl batches
        $this->deleteGLBatches($glBatchKeys);

        // 3. Delete all contractresolve for this line
        $qry = "DELETE FROM contractresolve WHERE cny#=:1 AND contractdetailkey=:2 and journaltype=:3 AND balancetype != 'E'";
        $ok = ExecStmt([$qry, GetMyCompany(), $contractDetailKey, $jrnlCode]);

        if (!$ok) {
            throw new IAException("Error deleting existing balance records.", 'CN-1597');
        }
    }


    /**
     * Re-calculates balances and reclass JEs
     *
     * @param   int     $contractDetailKey      Contract detail key
     * @param   string  $transactionDate        Transaction date
     * @param   string  $jrnlCode               Journal code
     * @param   bool    $runOnCreate            If 'On Create' is also on the events past Transaction date, re-run it
     * @param   bool    $errorIfNoRedoEvents    Throw exception if there are no events to redo
     *
     * @throws IAException
     */
    public function recalculateReclass($contractDetailKey, $transactionDate, $jrnlCode, $runOnCreate, $errorIfNoRedoEvents=false, $runLinkedReclass = true)
    {
        if ($runLinkedReclass) {
            ContractGLEventRegistry::forceRgisterLinkedReclass();
        }

        if ($jrnlCode) {

            $this->recalculateReclassInner($contractDetailKey, $transactionDate, $jrnlCode, $runOnCreate, $errorIfNoRedoEvents);

        } else {

            $jrnlCodes = ['J1', 'J2'];
            foreach ($jrnlCodes as $jrnlCode) {
                $this->recalculateReclassInner($contractDetailKey, $transactionDate, $jrnlCode, $runOnCreate, $errorIfNoRedoEvents);
            }
        }
    }

    /**
     * Re-calculates balances and reclass JEs
     *
     * @param   int     $contractDetailKey      Contract detail key
     * @param   string  $transactionDate        Transaction date
     * @param   string  $jrnlCode               Journal code
     * @param   bool    $runOnCreate            If 'On Create' is also on the events past Transaction date, re-run it
     * @param   bool    $errorIfNoRedoEvents    Throw exception if there are no events to redo
     *
     * @throws IAException
     */
    public function recalculateReclassInner($contractDetailKey, $transactionDate, $jrnlCode, $runOnCreate, $errorIfNoRedoEvents=true)
    {
        $this->sourceContractDetailKey = $contractDetailKey;

        $this->clearExistingReclassTransactions($transactionDate, $contractDetailKey, $jrnlCode, $orderedEvents, $runOnCreate);

        if ($runOnCreate) {
            $redoEventList = [];
            if (is_array($orderedEvents)) {
                $redoEventList = array_column($orderedEvents, 'EVENTTYPE');
            }
            // this can be the case where on create does not exist in into the system
            if (!in_array(ContractGLReclassEvent::EVENTTYPE_ONCONTRACT, $redoEventList)) {
                $this->recalculateOnCreate($contractDetailKey, $transactionDate, $jrnlCode);
            }
        }

        $noPendingFutureMEAAtTheBeginning = !$this->futureMEAEvent[$jrnlCode];

        if (!empty($orderedEvents)) {

            $this->forcedJrnlCode = $jrnlCode;

            if (ContractUtil::contractDetailHasMEA($contractDetailKey, $jrnlCode)) {
                $this->processFutureEventsForMEA($contractDetailKey, $jrnlCode, $orderedEvents, $hasFutureMEA);
            }

            ContractGLEventRegistry::registerFutureEventFound($contractDetailKey, $jrnlCode, $orderedEvents);

            $this->redoEvents($orderedEvents, $contractDetailKey, $jrnlCode, $runOnCreate);

            // This is the first event which started it all
            if ($noPendingFutureMEAAtTheBeginning && $this->futureMEAEvent[$jrnlCode]) {

                $this->runFutureMEAEvent($jrnlCode, $contractDetailKey);

                $this->runFutureMEASyncedRedoEvents($jrnlCode);
            }
        } else {

            if ($errorIfNoRedoEvents) {
                $msg = "No transactions found after $transactionDate for journal $jrnlCode";
                throw IAException::newIAException( 'CN-1598', $msg, ['TRANSACTION_DATE' => FormatDateForDisplay($transactionDate)]);
            }
        }
    }

    /**
     * Deletes and re-creates 'On Create' event for both journals
     *
     * @param   int     $contractDetailKey  Contract detail key
     * @param   string  $transactionDate    Transaction date
     * @param   string  $jrnlCode           Journal code
     *
     * @throws IAException
     */
    public function recalculateOnCreate($contractDetailKey, $transactionDate, $jrnlCode)
    {
        $resolveRecs = [];
        $glbatchKeys = [];

        $this->validateOnCreate($contractDetailKey, $transactionDate, $jrnlCode, $resolveRecs, $glbatchKeys);

        if (!empty($resolveRecs)) {
            // Delete contract resolves.
            $this->deleteResolveRecs($resolveRecs);
        }

        if (!empty($glbatchKeys)) {
            // Delete GL batch keys.
            $this->deleteGLBatches($glbatchKeys);
        }

        // Re-create 'on create' events.

        $this->recalculateOnCreateInner($contractDetailKey, $jrnlCode);
    }

    /**
     * @param int[] $resolveRecs
     */
    private function deleteResolveRecs($resolveRecs)
    {
        $cnResolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');
        foreach ($resolveRecs as $rec) {
            $ok = $cnResolveMgr->Delete($rec);
            if (!$ok) {
                $msg = "Could not delete resolve record: $rec. ".Globals::$g->gErr->myToString(false);
                throw IAException::newIAException( 'CN-1599', $msg, ['REC' => $rec, 'GERR_MYTOSTRING' => Globals::$g->gErr->myToString(false)]);
            }
        }
    }

    /**
     * @param   int     $contractDetailKey
     * @param   string  $transactionDate
     * @param   string  $jrnlCode           Journal code
     * @param   int[]   $resolveRecs
     * @param   int[]   $glbatchKeys
     *
     * @throws IAException
     */
    private function validateOnCreate($contractDetailKey, $transactionDate, $jrnlCode, &$resolveRecs, &$glbatchKeys)
    {
        $ok = true;

        $filters = [
            ['CONTRACTDETAILKEY',   '=',            $contractDetailKey],
            ['JOURNALTYPE',         '=',            $jrnlCode == 'J1' ? ContractGLReclassEvent::JOURNALTYPE_J1 : ContractGLReclassEvent::JOURNALTYPE_J2],
            ['TRANSACTIONDATE',     '>',            $transactionDate],
            ['TYPE',                '!=',           ContractGLReclassEvent::RESOLVETYPE_USAGEBASED]
        ];

        $cnResolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');
        $selects = ['RECORDNO', 'EVENTTYPE', 'GLBATCHKEY'];

        $params = [
            'selects'   => $selects,
            'filters'   => [$filters],
        ];

        $resolves = $cnResolveMgr->GetListActive($params);
        if (!$resolves) {
            $ok = false;
        }

        $events = [];

        foreach ($resolves as $resolve) {
            $resolveRecs[] = $resolve['RECORDNO'];
            $events[] = $resolve['EVENTTYPE'];
            $glbatchKeys[] = $resolve['GLBATCHKEY'];
        }

        $createEvent = array_unique($events);

        if ($ok && count($createEvent) > 1) {
            $eventsVal = implode(', ', $events);
            $msg = "Multiple events found: ".$eventsVal;
            throw IAException::newIAException( 'CN-1600', $msg, ['EVENTS_VAL' => $eventsVal]);
        }

        if ($ok && $createEvent[0] !== ContractGLReclassEvent::EVENTTYPE_ONCONTRACT) {
            $msg = "Was expecting '".ContractGLReclassEvent::EVENTTYPE_ONCONTRACT."' found '".$createEvent."'";
            throw IAException::newIAException( 'CN-1601', $msg, ['EVENTTYPE_ONCONTRACT' => ContractGLReclassEvent::EVENTTYPE_ONCONTRACT, 'CREATE_EVENT' => $createEvent]);
        }

        $glbatchKeys = array_unique($glbatchKeys);
    }

    /**
     * @param int       $contractDetailKey
     * @param string    $jrnlCode
     *
     * @throws IAException
     */
    private function recalculateOnCreateInner($contractDetailKey, $jrnlCode)
    {
        $cnDetail = EntityManager::GetListQuick(
            'contractdetail',
            ['TOTALFLATAMOUNT', 'TOTALBASEFLATAMOUNT', 'EXCHANGE_RATE', 'GLPOSTINGDATE', 'REVENUETEMPLATEKEY',
                'REVENUE2TEMPLATEKEY'],
            ['RECORDNO' => $contractDetailKey]
        );

        $amount = $cnDetail[0]['TOTALFLATAMOUNT'];
        $baseAmount = $cnDetail[0]['TOTALBASEFLATAMOUNT'];
        $exchRate = $cnDetail[0]['EXCHANGE_RATE'];
        $transactionDate = $cnDetail[0]['GLPOSTINGDATE'];

        if (!$baseAmount) {
            $baseAmount = $amount;
        }

        if (!$exchRate) {
            $exchRate = "1";
        }

        $this->rePostContractGLReclassOnCreate(
            $contractDetailKey,
            $amount,
            $baseAmount,
            $exchRate,
            $transactionDate,
            $jrnlCode
        );
    }

    /**
     * Re-calculates balances and reclass JEs for a contract
     *
     * @param   int     $contractKey        Contract key
     * @param   string  $transactionDate    Transaction date
     * @param   string  $jrnlCode           Journal code
     * @param   bool    $runOnCreate        If 'On Create' is also on the events past Transaction date, re-run it
     *
     * @throws IAException
     */
    public function recalculateReclassForContract($contractKey, $transactionDate, $jrnlCode, $runOnCreate)
    {
        $cnDetails = EntityManager::GetListQuick('contractdetail', ['RECORDNO'], ['CONTRACTKEY' => $contractKey]);
        foreach ($cnDetails as $cnd) {
            $this->recalculateReclass($cnd['RECORDNO'], $transactionDate, $jrnlCode, $runOnCreate, false, false);
        }
    }

    /******************/
    /* Static methods */
    /******************/

    /**
     * Returns balances for whole contract
     *
     * @param int    $contractKey    Contract key
     * @param string $balanceType    A, R, E
     * @param int    $jrnlCode       J1 or J2
     * @param string $asOfDate       As of date
     * @param bool   $onlyInProgress Only include active contract lines
     *
     * @return mixed
     */
    public static function getContractBalances(
        $contractKey,
        $balanceType,
        $jrnlCode,
        $asOfDate = null,
        $onlyInProgress = false
    )
    {
        $postTypePlaceHolder = '__POSTING_TYPE__';
        $qry = "SELECT SUM(r.tr_type*r.amount) amount, SUM(r.tr_type*r.baseamount) baseamount, r.classification
                FROM contractresolvemst r, contractdetailmst d
                WHERE r.cny# = :1 AND d.cny# = :1 AND r.contractdetailkey = d.record#
                AND d.contractkey = :2  AND r.balancetype = :3
                $postTypePlaceHolder
                AND r.journaltype = :5";

        if ($asOfDate) {
            $qry .= " AND r.transactiondate <= :6";

            $query1 = "select actualtransactiondate from contractresolve where cny# =:1 and contractkey = :2 and actualtransactiondate is not null and transactiondate <=:3 and journaltype = :4 and rownum = 1 order by record# asc" ;
            $postInOpenPeriod = QueryResult([$query1, GetMyCompany(), $contractKey, $asOfDate, $jrnlCode]);

            if(!empty($postInOpenPeriod)){
                $qry .= " and NVL(inactive,'F') != 'T' ";
            }

        } else {
            $qry .= " and NVL(inactive,'F') != 'T' ";
        }

        if ($onlyInProgress) {
            $qry .= " and d.state = '" . ContractDetailState::STATE_INPROGRESS_CODE . "' ";
        }

        $qry .= " GROUP BY r.classification";

        $balances = [];

        if ($balanceType == 'R') {
            // Exclude postingType DR from DR balance row for Rev Rec on Invoice lines
            $postingTypeFilter = "AND r.postingtype = :4 AND d.rev_rec_on_invoice = 'F'";
            $qparams = [
                str_replace($postTypePlaceHolder, $postingTypeFilter, $qry),
                GetMyCompany(), $contractKey, 'R', 'D', $jrnlCode
            ];
            if ($asOfDate) {
                $qparams[] = $asOfDate;
            }
            $balances[] = self::queryContractBalances($qparams, 'IA.DEFERRED_REVENUE');

            // Include postingType DR in Sales balance row for Rev Rec on Invoice lines
            $postingTypeFilter = "AND (d.rev_rec_on_invoice = 'T' OR (r.postingtype = :4 AND d.rev_rec_on_invoice = 'F'))";
            $qparams = [
                str_replace($postTypePlaceHolder, $postingTypeFilter, $qry),
                GetMyCompany(), $contractKey, 'R', 'S', $jrnlCode
            ];
            if ($asOfDate) {
                $qparams[] = $asOfDate;
            }
            $balances[] = self::queryContractBalances($qparams, 'IA.SALES_REVENUE');

        } else if ($balanceType == 'A') {
            $postingTypeFilter = 'AND r.postingtype = :4';
            $qparams = [
                str_replace($postTypePlaceHolder, $postingTypeFilter, $qry),
                GetMyCompany(), $contractKey, 'A', 'A', $jrnlCode
            ];
            if ($asOfDate) {
                $qparams[] = $asOfDate;
            }
            $balances[] = self::queryContractBalances($qparams, 'IA.RECEIVABLES');
        }

        return $balances;
    }

    private static function queryContractBalances(array $qparams, string $revTypeToken) : array
    {
        $results = QueryResult($qparams);
        $unbilled = $billed = $paid = "0";
        $baseUnbilled = $baseBilled = $basePaid = "0";
        if (!empty($results)) {
            foreach ($results as $res) {
                switch ($res['CLASSIFICATION']) {
                    case 'U':
                        $unbilled += $res['AMOUNT'];
                        $baseUnbilled += $res['BASEAMOUNT'];
                        break;
                    case 'B':
                        $billed += $res['AMOUNT'];
                        $baseBilled += $res['BASEAMOUNT'];
                        break;
                    case 'P':
                        $paid += $res['AMOUNT'];
                        $basePaid += $res['BASEAMOUNT'];
                        break;
                }
            }
        }
        $total = $unbilled + $billed + $paid;
        $baseTotal = $baseUnbilled + $baseBilled + $basePaid;
        $textMap = static::getTextMap();
        $balance = [
            'REVENUETYPE'   => GT($textMap, $revTypeToken),
            'UNBILLED'      => $unbilled == 0 ? "0" : $unbilled,
            'BILLED'        => $billed == 0 ? "0" : $billed,
            'PAID'          => $paid == 0 ? "0" : $paid,
            'TOTAL'         => $total == 0 ? "0" : $total,
            'BASEUNBILLED'  => $baseUnbilled == 0 ? "0" : $baseUnbilled,
            'BASEBILLED'    => $baseBilled == 0 ? "0" : $baseBilled,
            'BASEPAID'      => $basePaid == 0 ? "0" : $basePaid,
            'BASETOTAL'     => $baseTotal == 0 ? "0" : $baseTotal,
        ];
        return $balance;
    }

    /**
     * Returns balances by journal and posting type
     *
     * @param int    $contractDetailKey Contract detail key
     * @param string $balanceType       A or E
     * @param string $jrnlCode          J1 or J2
     * @param string $postingtype       (optional) D or S
     * @param bool   $withUsage         Adds usage amounts
     * @param string $asOfDate          As of date
     * @param bool   $onlyInProgress    Only include In progress contract lines
     * @param bool   $includeAllPeriods Include all periods of Evergreen line
     * @param bool   $isRevRecOnInv
     *
     * @return mixed
     */
    public static function getBalances(
        $contractDetailKey,
        $balanceType,
        $jrnlCode,
        $postingtype = '',
        $withUsage = true,
        $asOfDate = null,
        $onlyInProgress = false,
        $includeAllPeriods = true,
        &$isRevRecOnInv = false,
    )
    {
        $textMap = static::getTextMap();

        $resolveTable = $withUsage ? "contractresolvemst" : "v_contractresolvefixed";

        $keyFilterCond = $includeAllPeriods ? "(d.renewedoriginaldetailkey = :2 OR d.record# = :2)" : "d.record# = :2";

        $qry = "SELECT SUM(tr_type*amount) amount, SUM(tr_type*baseamount) baseamount, classification, rev_rec_on_invoice 
                FROM $resolveTable r, contractdetailmst d
                WHERE r.cny# = :1 AND d.cny# = :1 AND r.contractdetailkey = d.record#
                AND $keyFilterCond AND balancetype = :3 
                AND journaltype = :4 AND postingtype = :5";

        if ($asOfDate) {
            $qry .= " AND transactiondate <= :6";
            if($withUsage) {

                $query1 = "select actualtransactiondate from contractresolve where cny# =:1 and contractdetailkey = :2 and actualtransactiondate is not null and transactiondate <=:3 and journaltype = :4 and rownum = 1 order by record# asc" ;
                $postInOpenPeriod = QueryResult([$query1, GetMyCompany(), $contractDetailKey, $asOfDate, $jrnlCode]);

                if(!empty($postInOpenPeriod)){
                    $qry .= "  and NVL(inactive,'F') != 'T' ";
                }
            }
        }else if ($withUsage){
            $qry .= "  and NVL(inactive,'F') != 'T' ";
        }

        if ( $onlyInProgress ) {
            $qry .= " and d.state = '" . ContractDetailState::STATE_INPROGRESS_CODE . "' ";
        }

        $qry .= " GROUP BY classification, rev_rec_on_invoice";

        $balances = array();

        if ($balanceType == 'A') {
            $arBalance = self::runQueryResultForGetBalances(
                $qry,
                $contractDetailKey,
                $balanceType,
                $jrnlCode,
                'A',
                $asOfDate,
                GT($textMap, 'IA.RECEIVABLES'),
                $isRevRecOnInv,
            );
            $balances[] = $arBalance;
            return $balances;
        }

        if ($postingtype == '' || $postingtype == 'D') {
            $deferredBalance = self::runQueryResultForGetBalances(
                $qry,
                $contractDetailKey,
                $balanceType,
                $jrnlCode,
                'D',
                $asOfDate,
                GT($textMap, 'IA.DEFERRED_REVENUE'),
                $isRevRecOnInv,
            );
            $balances[] = $deferredBalance;
        }

        if ($postingtype == '' || $postingtype == 'S') {
            $salesBalance = self::runQueryResultForGetBalances(
                $qry,
                $contractDetailKey,
                $balanceType,
                $jrnlCode,
                'S',
                $asOfDate,
                GT($textMap, 'IA.SALES_REVENUE'),
                $isRevRecOnInv,
            );
            $balances[] = $salesBalance;
        }

        return count($balances) == 1 ? $balances[0] : $balances;
    }

    /**
     * @param string $qry
     * @param int    $contractDetailKey
     * @param string $balanceType
     * @param string $jrnlCode
     * @param string $postingType
     * @param string $asOfDate
     * @param string $revenueType
     * @param bool   $isRevRecOnInv
     *
     * @return array
     */
    private static function runQueryResultForGetBalances($qry, $contractDetailKey, $balanceType, $jrnlCode, $postingType, $asOfDate, $revenueType, &$isRevRecOnInv = false)
    {
        $qparams = array($qry, GetMyCompany(), $contractDetailKey, $balanceType, $jrnlCode, $postingType);
        if ($asOfDate) {
            $qparams[] = $asOfDate;
        }
        $results = QueryResult($qparams);

        $unbilled = $billed = $paid = "0";
        $baseUnbilled = $baseBilled = $basePaid = "0";
        foreach ($results as $res) {
            switch ($res['CLASSIFICATION']) {
                case 'U':
                    $unbilled += $res['AMOUNT'];
                    $baseUnbilled += $res['BASEAMOUNT'];
                    break;
                case 'B':
                    $billed += $res['AMOUNT'];
                    $baseBilled += $res['BASEAMOUNT'];
                    break;
                case 'P':
                    $paid += $res['AMOUNT'];
                    $basePaid += $res['BASEAMOUNT'];
                    break;
            }
        }

        $total = $unbilled + $billed + $paid;
        $baseTotal = $baseUnbilled + $baseBilled + $basePaid;
        $balance = array(
            'REVENUETYPE'   => $revenueType,
            'UNBILLED'      => $unbilled == 0 ? "0" : $unbilled, // TODO: UI sake relook
            'BILLED'        => $billed == 0 ? "0" : $billed, // TODO: UI sake relook
            'PAID'          => $paid == 0 ? "0" : $paid, // TODO: UI sake relook
            'TOTAL'         => $total == 0 ? "0" : $total, // TODO: UI sake relook
            'BASEUNBILLED'  => $baseUnbilled == 0 ? "0" : $baseUnbilled, // TODO: UI sake relook
            'BASEBILLED'    => $baseBilled == 0 ? "0" : $baseBilled, // TODO: UI sake relook
            'BASEPAID'      => $basePaid == 0 ? "0" : $basePaid, // TODO: UI sake relook
            'BASETOTAL'     => $baseTotal == 0 ? "0" : $baseTotal, // TODO: UI sake relook
        );
        if (!empty($results)) {
            $isRevRecOnInv = ($results[0]['REV_REC_ON_INVOICE'] ?? 'F') === 'T';
        }
        return $balance;
    }

    /**
     * Returns balances by journal and posting type
     *
     * @param   int     $contractDetailKey  Contract detail key
     * @param   string  $jrnlCode           J1 or J2
     * @param   string  $postingType        D or S
     * @param   int     $trTypeMultiplier   1 or -1
     * @param   mixed   $netBilledBalance
     * @param   mixed   $netPaidBalance
     *
     * @return array
     */
    public static function getRevenueBalancesByRate($contractDetailKey, $jrnlCode, $postingType, $trTypeMultiplier, &$netBilledBalance, &$netPaidBalance)
    {
        $byBSE = self::doRecognitionByBSE($contractDetailKey, $jrnlCode);
        $colBSE =  $byBSE ? "billingschentrykey, " : "";

        $qry = "SELECT $colBSE SUM($trTypeMultiplier*tr_type*amount) amount,
                SUM($trTypeMultiplier*tr_type*baseamount) baseamount, classification, exchange_rate
                FROM v_contractresolvefixed
                WHERE cny# = :1 AND contractdetailkey = :2 AND balancetype = 'R' AND journaltype = :3
                AND postingtype = :4
                GROUP BY $colBSE classification, exchange_rate";
        $results = QueryResult(
            [$qry, GetMyCompany(), $contractDetailKey, $jrnlCode, $postingType]
        );


        $balances = [];

        $existsUnbilled = false;
        $unbilledExchRate = 1;
        $unbilledAmount = 0;
        $unbilledBaseAmount = 0;

        $netBilledBalance = 0.0;
        $netPaidBalance = 0.0;
        $exchangeRateMap = null;

        foreach ( $results as $res) {
            // Ignore 0 values
            if ($res['AMOUNT'] == 0) {
                continue;
            }

            if ($res['CLASSIFICATION'] == 'U') {
                $unbilledAmount = ibcadd($unbilledAmount, $res['AMOUNT']);
                $unbilledBaseAmount = ibcadd($unbilledBaseAmount, $res['BASEAMOUNT']);

                if ($byBSE) {
                    if ( ! $existsUnbilled ) {
                        $unbilledExchRate = $res['EXCHANGE_RATE'];
                    }
                    $existsUnbilled = true;
                } else {
                    $unbilledExchRate = "1";
                }
                $exchangeRateMap[$res['AMOUNT']] = $res['EXCHANGE_RATE'];
                continue;
            } else if ($res['CLASSIFICATION'] == 'B') {
                $classification = 'BILLED';
                $netBilledBalance = ibcadd($netBilledBalance, $res['AMOUNT']);
            } else if ($res['CLASSIFICATION'] == 'P') {
                $classification = 'PAID';
                $netPaidBalance = ibcadd($netPaidBalance, $res['AMOUNT']);
            } else {
                $msg = "Internall Error: Invalid CLASSIFICATION in contractresolve: {$res['CLASSIFICATION']}";
                throw IAException::newIAException( 'CN-1602', $msg, ['RES_CLASSIFICATION' => $res['CLASSIFICATION']]);
            }
            $exchangeRateMap[$res['AMOUNT']] = $res['EXCHANGE_RATE'];
            $balances[$classification][] = $res;
        }

        if ($byBSE) {
            // Order balances to the sequence in which they came in
            $balances = self::orderRevenueBalancesByBSEPostDate($jrnlCode, $balances);
        }

        if ( $unbilledAmount != 0 ) {
            $unbilledExchRate = ibcdiv($unbilledBaseAmount,$unbilledAmount,ContractUtil::RATE_PRECISION,true);
        }

        $balances['UNBILLED'][] = [
            'AMOUNT'        => $unbilledAmount,
            'BASEAMOUNT'    => $unbilledBaseAmount,
            'EXCHANGE_RATE' => $unbilledExchRate
        ];
        $balances['AMOUNT_TO_EXCHANGE_RATE'] = $exchangeRateMap;

        return $balances;
    }

    /**
     * @param string $jrnlCode
     * @param array  $balances
     *
     * @return array
     */
    private static function orderRevenueBalancesByBSEPostDate($jrnlCode, $balances)
    {
        $orderedBalancesAll = [];
        $billingSchEntryKeys = [];

        foreach ( $balances as $balancesForOneType ) {
            $billingSchEntryKeysForOneType = array_column($balancesForOneType, 'BILLINGSCHENTRYKEY');
            ContractUtil::assert(count($balancesForOneType) == count($billingSchEntryKeysForOneType));
            array_append($billingSchEntryKeys, $billingSchEntryKeysForOneType);
        }
        $billingSchEntryKeys = array_unique($billingSchEntryKeys);

        if ($billingSchEntryKeys) {
            $orderedBSEKeysAndRates = self::getOrderedBSEKeysAndRate($billingSchEntryKeys, $jrnlCode);

            if ($balances['BILLED']) {
                $orderedBalances = self::orderRevenueBalancesByBSEPostDate_inner($orderedBSEKeysAndRates, $balances['BILLED']);
                $orderedBalancesAll['BILLED'] = $orderedBalances;
            }

            if ($balances['PAID']) {
                $orderedBalances = self::orderRevenueBalancesByBSEPostDate_inner($orderedBSEKeysAndRates, $balances['PAID']);
                $orderedBalancesAll['PAID'] = $orderedBalances;
            }
        }

        return $orderedBalancesAll;
    }

    /**
     * @param array $orderedBSEKeysAndRates
     * @param array $rawBalances
     *
     * @return array
     */
    private static function orderRevenueBalancesByBSEPostDate_inner($orderedBSEKeysAndRates, $rawBalances)
    {
        $orderedBalances = [];
        $keyedBalances = [];

        foreach ($rawBalances as $balance) {
               $keyedBalances[$balance['BILLINGSCHENTRYKEY']][] = $balance;
        }


        foreach ($orderedBSEKeysAndRates as $bseKeyA) {

            if (isset($keyedBalances[$bseKeyA['BILLINGSCHENTRYKEY']])) {

                $orderedBalances = array_merge($orderedBalances,$keyedBalances[$bseKeyA['BILLINGSCHENTRYKEY']]) ;
            }
        }

        return $orderedBalances;
    }

    /**
     * @param string[] $billingSchEntryKeys
     * @param string   $jrnlCode
     *
     * @return array
     */
    private static function getOrderedBSEKeysAndRate($billingSchEntryKeys, $jrnlCode)
    {
        $unique = [];
        $qry = "
          SELECT 
            billingschentrykey, exchange_rate
          FROM 
            v_contractresolvefixed
          WHERE 
            cny# = :1 AND journaltype = :2 AND balancetype = 'R' AND eventtype  in ('I', 'M', 'K') AND classification in ('B', 'P')
        ";
        $stmt = [$qry, GetMyCompany(), $jrnlCode];
        $stmt = PrepINClauseStmt($stmt, $billingSchEntryKeys, 'AND billingschentrykey');
        $stmt[0] .= '
          ORDER BY 
            transactiondate, record#
        ';

        $results = QueryResult($stmt);

        $keys = [];
        foreach ($results as $result) {
            $key = $result['BILLINGSCHENTRYKEY'] ;

            if (!$keys[$key]) {
                $unique[] = $result;
                $keys[$key] = true;
            }
        }

        return $unique;
    }

    /**
     * @param int    $contractDetailKey
     * @param string $jrnlCode
     *
     * @return bool
     */
    public static function doRecognitionByBSE($contractDetailKey, $jrnlCode)
    {
        // Have we posted recognition that moved amount from Deferred Billed or Deferred Paid with BILLINGSCHENTRYKEY not set?
        // If yes, we cannot do On Recognition reclass using correct order of exchange rates

        $qry = "
          SELECT 
            COUNT(1) COUNT
          FROM 
            contractresolve 
          WHERE 
            cny#=:1 AND contractdetailkey=:2 AND journaltype=:3 
            AND eventtype = 'R' AND (classification = 'B' OR classification = 'P')
            AND billingschentrykey IS NULL
        ";

        $results = QueryResult([$qry, GetMyCompany(), $contractDetailKey, $jrnlCode]);

        return $results === false || $results[0]['COUNT'] == 0;
    }
    
    /**
     * Deletes existing re-class transactions including transaction that started it
     *
     * @param array      $identifierAttributes
     * @param int        $contractDetailKey
     * @param string     $transactionDate
     * @param string     $jrnlCode
     * @param array|null $orderedEvents
     *
     * @throws IAException
     *
     */
    private function clearExistingReclassTransactionsIncludingSelf($identifierAttributes, $contractDetailKey,
                                                                   $transactionDate, $jrnlCode, &$orderedEvents)
    {
        $jrnlType = $jrnlCode == ContractGLReclassEvent::JOURNALCODE_J1
            ? ContractGLReclassEvent::JOURNALTYPE_J1 : ContractGLReclassEvent::JOURNALTYPE_J2;

        $resolveRec = $this->getResolveRec($contractDetailKey, $transactionDate, $jrnlType, $identifierAttributes);

        // 2. Find all events in future


        if ($resolveRec) {
            $filters[] = [
                'operator' => 'OR',
                'filters' => [
                    [
                        'operator' => 'AND',
                        'filters' => [
                            ['RECORDNO', '>=', $resolveRec],
                            ['TRANSACTIONDATE', '=', $transactionDate]
                        ]
                    ],
                    ['TRANSACTIONDATE', '>', $transactionDate],
                ],
            ];
        } else {
            $filters[] = ['TRANSACTIONDATE', '>', $transactionDate];
        }


        $filters[] = ['CONTRACTDETAILKEY',  '=',    $contractDetailKey];
        $filters[] = ['JOURNALTYPE',        '=',    $jrnlType];
        $filters[] = ['EVENTTYPE',   'NOT IN', [ContractGLReclassEvent::EVENTTYPE_ONCONTRACT , ContractGLReclassEvent::EVENTTYPE_ONCANCEL] ];
        $filters[] = ['TYPE',        'NOT IN', [ContractGLReclassEvent::RESOLVETYPE_USAGEBASED  ,ContractGLReclassEvent::RESOLVETYPE_ADJUST ]];


        $cnResolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');
        $selects = $this->getResolveMgrGetFields($cnResolveMgr);

        $params = [
            'selects'   => $selects,
            'filters'   => [$filters],
            'orders'    => [['RECORDNO']]
        ];

        $allCNResolves = $cnResolveMgr->GetListActive($params);

        if (empty($allCNResolves) && !empty($resolveRec)) {
            $identifierStr = "";
            foreach ($identifierAttributes as $attr => $value) {
                $identifierStr .= " $attr = $value ";
            }
            $msg = "No re-class records exist for contract detail key:{$contractDetailKey} and ".
                   $identifierStr;
            throw IAException::newIAException( 'CN-1603', $msg, ['CONTRACT_DETAIL_KEY' => $contractDetailKey, 'IDENTIFIER_STR' => $identifierStr]);
        }

        $newAllCNResolves = [];
        $reclassGLBatches = [];
        $bseKeys = [];
        foreach ($allCNResolves as $resolve) {

            // Always take all glbatches involved in re-class
            if ($resolve['GLBATCHKEY'] && !in_array($resolve['GLBATCHKEY'], $reclassGLBatches)) {
                $reclassGLBatches[] = $resolve['GLBATCHKEY'];
            }

            if ($resolve['BILLINGSCHENTRYKEY'] && !in_array($resolve['BILLINGSCHENTRYKEY'], $bseKeys)) {
                $bseKeys[] = $resolve['BILLINGSCHENTRYKEY'];
            }

            if($resolve['BILLINGSCHENTRYKEY'] && $resolve['PAYMENTPRENTRYKEY']) {
                $paymentDate = $resolve['ACTUALTRANSACTIONDATE']??$resolve['TRANSACTIONDATE'];
                ContractUtil::collectPaymentAndReversalDataVersion($resolve['BILLINGSCHENTRYKEY'], $resolve['PAYMENTPRENTRYKEY'], $resolve['PARENTPYMTRECORDKEY'], $paymentDate, $resolve['DATAVERSIONKEY']);
            }

            // Ignore self
            $isSelf = true;
            foreach ($identifierAttributes as $attr => $value) {
                if ($resolve[$attr] != $value) {
                    $isSelf = false;
                    break;
                }
            }

            if ($isSelf) {
                continue;
            }

            $newAllCNResolves[] = $resolve;
        }

        if (!empty($bseKeys)) {
            $this->cachePaymentAmountSplitByBillingScheduleEntry($bseKeys);
        }

        $orderedEvents = $this->summarizeResolvesByEvent($newAllCNResolves);

        // 3. Delete resolves from starting resolve rec
        $startResolveRec = $allCNResolves[0]['RECORDNO'];
        $this->deleteResolve($startResolveRec, $contractDetailKey, $jrnlCode, ['A', 'R'], $transactionDate);

        // 4. Explicitly delete re-class gl batches
        if (!empty($reclassGLBatches)) {
            $this->deleteGLBatches($reclassGLBatches);
        }

        $this->setExecutedClearTransactions($contractDetailKey, $jrnlCode, $orderedEvents);
    }

    /**
     * Get payment amount distribution across contract lines by billing schedule entry
     *
     * @param   array   $bseKeys  Billing schedule entry keys
     */
    private function cachePaymentAmountSplitByBillingScheduleEntry($bseKeys)
    {
        if ( empty($this->paymentAndReversalBSEKeys)) {
            $this->paymentAndReversalBSEKeys = $bseKeys;
            $newBSEKeys = $bseKeys;
        } else {
            $newBSEKeys = array_diff($bseKeys, $this->paymentAndReversalBSEKeys);
            if (!empty($newBSEKeys)) {
                $this->paymentAndReversalBSEKeys =
                    array_merge($newBSEKeys, $this->paymentAndReversalBSEKeys);
            }
        }

        if ( empty($newBSEKeys) ) {
            return;
        }

        //**********************Older Code Start Here*****************//
        //get the billing schedule entry for which need to fetch payment data from contract resolve based on data version of payment in cn resolve

        $paymentAndReversalBSEForCNSub = ContractUtil::getPaymentAndReversalBSEForCNSub();

        $newBillingSchEntriesCNSub = array_intersect($newBSEKeys, $paymentAndReversalBSEForCNSub);

        if(!empty($newBillingSchEntriesCNSub)) {
            $inStr = implode(',', $newBillingSchEntriesCNSub);
            $qry = "SELECT SUM(tr_type * amount) AS amount, eventtype, journaltype , billingschentrykey, paymentprentrykey FROM";
            $qry .= " v_contractresolvefixed WHERE billingschentrykey IN ($inStr)";
            $qry .= " AND (eventtype = 'P' OR eventtype = 'V') AND balancetype = 'A'";
            $qry .= " AND (classification = 'P' OR (amount = 0 AND classification = 'B'))  AND cny#=:1 "; // AND amount > 0
            $qry .= " GROUP BY eventtype, journaltype, billingschentrykey, paymentprentrykey";
            $qparams = [$qry, GetMyCompany()];

            $results = QueryResult($qparams);
            foreach ($results as $res) {
                $cacheKey = $res['EVENTTYPE'] == 'P' ? "OnPayment" : "OnPaymentReversal";
                $reverseCacheKey = $res['EVENTTYPE'] == 'P' ? "OnPaymentReversal" : "OnPayment";

                $billingSchEntryKey = $res['BILLINGSCHENTRYKEY'];
                $paymentPRentryKey = $res['PAYMENTPRENTRYKEY'];
                $jrnlCode = $res['JOURNALTYPE'];

                $cacheKey = "{$cacheKey}_{$billingSchEntryKey}_{$paymentPRentryKey}_{$jrnlCode}";

                if (!$this->paymentAndReversalAmountFromCNSub[$cacheKey]) {
                    $this->paymentAndReversalAmountFromCNSub[$cacheKey] = $res['AMOUNT'];

                    //this code will be update the reverse amount in payment amount for same billingSchEntryKeya and paymentPRentryKey
                    if ($res['EVENTTYPE'] == 'P') {

                        $reverseCacheKey = "{$reverseCacheKey}_{$billingSchEntryKey}_{$paymentPRentryKey}";
                        if ($this->paymentAndReversalAmountFromCNSub[$reverseCacheKey]) {
                            $this->paymentAndReversalAmountFromCNSub[$cacheKey] = ibcadd($this->paymentAndReversalAmountFromCNSub[$cacheKey], $this->paymentAndReversalAmountFromCNSub[$reverseCacheKey]);
                        }
                    } else {

                        $reverseCacheKey = "{$reverseCacheKey}_{$billingSchEntryKey}_{$paymentPRentryKey}";
                        if ($this->paymentAndReversalAmountFromCNSub[$reverseCacheKey]) {
                            $this->paymentAndReversalAmountFromCNSub[$reverseCacheKey] = ibcadd($this->paymentAndReversalAmountFromCNSub[$cacheKey], $this->paymentAndReversalAmountFromCNSub[$reverseCacheKey]);
                        }
                    }
                }
            }
        }
        //**********************OLDER CODE END*************************//

        //**********************New code********************************//

        // Payment amount from AR will be fecth for all billing schedule
        if($newBSEKeys ) {
            ContractUtil::fetchPaymentAndReverseAmountFromARSub($newBSEKeys);
        }

        //********************New code end*******************//
    }
    
    /**
     * Records the fact that clear transactions has been executed for line and journal
     *
     * @param string     $contractDetailKey Contract detail key
     * @param string     $jrnlCode Journal code
     * @param array|null $orderedEvents Ordered events to be re-executed
     */
    protected function setExecutedClearTransactions($contractDetailKey, $jrnlCode, $orderedEvents)
    {
        $this->executedClearTransactions["{$contractDetailKey}_{$jrnlCode}"] = $orderedEvents;
    }

    /**
     * Checks if clear is already executed for line and journal
     *
     * @param   string  $contractDetailKey  Contract detail key
     * @param   string  $jrnlCode           Journal code
     *
     * @return bool
     */
    protected function clearAlreadyExecuted($contractDetailKey, $jrnlCode)
    {
        $key = "{$contractDetailKey}_{$jrnlCode}";
        return isset($this->executedClearTransactions[$key]);
    }

    /**
     * Checks if event with given parameters has already been run. If not, then records this run
     *
     * @param   string       $eventType                  Event type
     * @param   string       $jrnlCode                   Journal code
     * @param   int          $contractDetailKey          Contract detail key
     * @param   int|null     $billingScheduleEntryKey    Billing schedule entry key
     * @param   int|null     $paymentPREntrykey          Payment PR entry key
     *
     * @param null|int $parentPymtRecordKey
     * @param string $transactionDate
     * @return bool
     */
    protected function eventAlreadyExecuted($eventType, $jrnlCode, $contractDetailKey, $billingScheduleEntryKey=null,
                                            $paymentPREntrykey=null, $parentPymtRecordKey=null, $transactionDate = null)
    {
        $eventKey = "{$eventType}_{$jrnlCode}_{$contractDetailKey}_{$billingScheduleEntryKey}";
        if ($paymentPREntrykey) {
            $eventKey .= "_{$paymentPREntrykey}_{$parentPymtRecordKey}_{$transactionDate}";
        }

        return isset($this->executedEvents[$eventKey]);
    }


    /**
     * Checks if event with given parameters has already been run. If not, then records this run
     *
     * @param   string       $eventType                  Event type
     * @param   string       $jrnlCode                   Journal code
     * @param   float|null   $amount                     Transaction amount
     * @param   int|null     $contractDetailKey          Contract detail key
     * @param   int|null     $billingScheduleEntryKey    Billing schedule entry key
     * @param   int|null     $paymentPREntrykey          Payment PR entry key
     *
     * @param   int|null $parentPymtRecordKey
     * @param   null|string $transactionDate
     * @throws IAException
     */
    protected function recordEvent($eventType, $jrnlCode, $amount, $contractDetailKey, $billingScheduleEntryKey=null,
                                   $paymentPREntrykey=null, $parentPymtRecordKey = null, $transactionDate = null)
    {
        $eventKey = "{$eventType}_{$jrnlCode}_{$contractDetailKey}_{$billingScheduleEntryKey}";
        if ($paymentPREntrykey) {
            $eventKey .= "_{$paymentPREntrykey}_{$parentPymtRecordKey}_{$transactionDate}";
        }

        if (isset($this->executedEvents[$eventKey])) {

            // Payment PR Entry key can repeat for adjustments i.e.
            // 1. Reversing adjustment based payment
            // 2. Repay using the same adjustment
            if ($paymentPREntrykey !== null && $this->isFromAdjustment($paymentPREntrykey)) {
                return;
            }

            $msg = "$eventKey is getting executed again. This is not expected.";
            throw IAException::newIAException( 'CN-1604', $msg, ['EVENT_KEY' => $eventKey]);
        }

        $this->executedEvents[$eventKey] = $amount === null ?? true;
    }

    /**
     * @param int $paymentPREntrykey
     *
     * @return bool
     */
    protected function isFromAdjustment($paymentPREntrykey)
    {
        $qry = "
            SELECT 
              pr.record# 
            FROM 
              prentry e, prrecord pr 
            WHERE 
              pr.cny#=:1 AND e.cny#=:1 AND e.recordkey = pr.record# AND e.record#=:2
              AND pr.recordtype = 'ra'
        ";

        $result = QueryResult([$qry, GetMyCompany(), $paymentPREntrykey]);

        return $result !== false && !empty($result);
    }

    /**
     * @param   string $eventType Event type
     * @param   string $jrnlCode Journal code
     * @param   int $contractDetailKey Contract detail key
     * @param   int $billingScheduleEntryKey Billing schedule entry key
     * @param   int $paymentPREntrykey Payment PR entry key
     *
     * @param null|string $transactionDate
     */
    protected function undoRecordEvent($eventType, $jrnlCode, $contractDetailKey, $billingScheduleEntryKey,
                                   $paymentPREntrykey=null, $transactionDate = null)
    {
        $eventKey = "{$eventType}_{$jrnlCode}_{$contractDetailKey}_{$billingScheduleEntryKey}";
        if ($paymentPREntrykey) {
            $eventKey .= "_{$paymentPREntrykey}_{$transactionDate}";
        }

        if (isset($this->executedEvents[$eventKey])) {
            unset($this->executedEvents[$eventKey]);
        }
    }

    /**
     * Clears recorded events history
     */
    protected function undoRecordEvents()
    {
        $this->executedEvents = [];
    }

    /**
     * @param int    $contractDetailKey
     * @param float  $amount
     * @param float  $baseAmount
     * @param float  $exchRate
     * @param string $transactionDate
     * @param string $journalCode
     */
    private function rePostContractGLReclassOnCreate($contractDetailKey, $amount, $baseAmount, $exchRate, $transactionDate, $journalCode)
    {
        if ($amount != 0) {
            // If there are only 2 contractresolve records (AR and Revenue)
            // Then delete them and re-create
            // Else throw an error since this is not expected
            $count = ContractUtil::getFixedTypeResolveCount($contractDetailKey, $journalCode);
            if ($count <= 2) {
                if ($count > 0) {
                    $this->deleteResolveRecordsAndGLBatches($contractDetailKey, $journalCode);
                }

                if (BaseContractGLReclassEngine::isACPFlow($transactionDate)) {
                    // 'Historical flag' is set against expense schedule entry.
                    // ACP reclass 'On Create' event.
                    // Skip Unbilled accounting for ACP reclass.

                    $historicalFlag = 'true';
                }
                else {
                    // Normal Reclass 'On Create' event.

                    $historicalFlag = 'false';
                }

                $event = new ContractGLReclassOnCreate(
                    $contractDetailKey,
                    $amount,
                    $baseAmount,
                    $exchRate,
                    $transactionDate,
                    $journalCode,
                    $historicalFlag
                );

                $event->execute();
            } else {
                // TODO: we should really throw an exception, but this is pending ticket 64785 being fixed properly
//            throw new IAException("Internal error. Attempting to repost onCreate event for contract detail key $contractDetailKey when there are $count resolve records.");
            }
        }
    }

    /**
     * On uncancel remove reclass postings.
     * Remove postings from contractresolve, glbatch and glentry.
     *
     * @param int $contractDetailKey
     * @param int $type Reclass posting type (Contract or Expense).
     * @param string $cancelDate
     *
     * @throws IAException
     */
    public function onUncancelRemoveReclassPostings($contractDetailKey,
                                                    $type = ContractResolveManager::POSTING_TYPE_CONTRACT,
                                                    $cancelDate = '')
    {
        $contractResolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');
        $cnResolveRecords = $contractResolveMgr->getCancelledContractResolveRecords(
            $contractDetailKey,
            $type
        );

        if(empty($cnResolveRecords)) {
            return;
        }

        // Accumulate contract-resolve-key and glbatch-key.

        $cnResolveKeys = array();
        $glBatchKeys = array();

        foreach($cnResolveRecords as $cnResolve) {

            $cnResolveKeys[] = $cnResolve["RECORDNO"];

            if(!empty($cnResolve["GLBATCHKEY"])) {
                $glBatchKeys[] = $cnResolve["GLBATCHKEY"];
            }
        }

        // Remove postings from contractresolve.

        if(!empty($cnResolveKeys)) {
            $this->deleteResolveRecs($cnResolveKeys);
        }

        // Remove postings from glbatch and glentry.
        // On delete of glbatch, GLBatchManager will auto delete glentry.

        if(!empty($glBatchKeys)) {

            try {
                $this->deleteGLBatches($glBatchKeys);
            }catch(IAException $e) {
                $err = Globals::$g->gErr;

                for($i = 0; $i < $err->GetErrorCount(); $i++) {
                    $errorInfo = $err->getErrorByIndex($i);

                    if($errorInfo['DESCRIPTION'] == 'Periods are closed.') {
                        // Company books are closed.

                        $msg = "The contract cannot be uncancelled as the cancellation date ({$cancelDate}) occurs in a closed period.";
                        throw IAException::newIAException( 'CN-1605', $msg, ['CANCEL_DATE' => FormatDateForDisplay($cancelDate)]);
                    }
                }

                // Re-throw the error.

                throw $e;
            }
        }
    }

    /**
     * On uncancel remove adjustments.
     * Remove postings from prrecord and prentry.
     * Remove postings from glbatch and glentry.
     *
     * @param string[] $prRecordKeys
     *
     * @throws IAException
     */
    public function onUncancelRemoveAdjustments($prRecordKeys)
    {
        if(!empty($prRecordKeys)) {
            $ok = true;
            $errMsgKeys = null;

            XACT_BEGIN(__METHOD__);

            foreach ( $prRecordKeys as $prRecordKey ) {
                // Remove postings from prrecord.
                // On delete of prrecord, PRRecordManager will auto delete prentry and glbatch.
                // On delete of glbatch, GLBatchManager will auto delete glentry.

                $arAdjMgr = Globals::$g->gManagerFactory->getManager('aradjustment');
                $ok = $arAdjMgr->Delete($prRecordKey);
                if (!$ok) {
                    $errMsgKeys = "key '{$prRecordKey}'";
                    break;
                }
            }

            $ok = $ok && XACT_COMMIT(__METHOD__);
            if (!$ok) {
                XACT_ABORT(__METHOD__);

                if (!$errMsgKeys) {
                    $errMsgKeys = "keys '" . implode(',', $prRecordKeys) . "'";
                }
                $msg = "Failed to remove adjustments with $errMsgKeys";
                // This is really not ideal since we are not reporting "root cause" error in $gErr.
                throw IAException::newIAException( 'CN-1606', $msg, ['ERR_MSG_KEYS' => $errMsgKeys]);
            }
        }
    }

    /**
     * @param int    $contractDetailKey
     * @param string $jrnlCode
     * @param array  $futrueEvents
     * @param bool   $hasFutureMEA
     */
    protected function processFutureEventsForMEA($contractDetailKey, $jrnlCode, $futrueEvents, &$hasFutureMEA)
    {
        $meaCount = 0;
        $hasFutureMEA = false;
        foreach ($futrueEvents as $event) {
            $eventType = $event['EVENTTYPE'];
            if ($eventType == $this->bundleEventType) {
                if (ContractUtil::clearingMEA()) {
                    continue;
                }

                $lastMEADate = $currMEADate??$event['TRANSACTIONDATE']; //if current date is assigned in previous loop, then  in the current loop that will become last mea date
                $currMEADate = $event['TRANSACTIONDATE'];
                
                
                if ($meaCount == 1) {
                    $cnDetail = EntityManager::GetListQuick('contractdetail', ['CONTRACTID', 'LINENO'], ['RECORDNO' => $contractDetailKey]);
                    if ($cnDetail && $cnDetail[0]) {
                        $cnId = $cnDetail[0]['CONTRACTID'];
                        $lineNo = $cnDetail[0]['LINENO'];
                    } else {
                        $cnId = null;
                        $lineNo = null;
                    }
                    
                    // Should never happen
                    assert($cnId);
                    assert($lineNo);
                    assert($lastMEADate);
                    assert($currMEADate);

                    $msg = "$cnId line $lineNo is participating in MEA allocations on $lastMEADate and $currMEADate, transactions cannot be posted before $lastMEADate. Clear MEA allocations, post the transaction, and then re-create the MEA allocation.";
                    throw IAException::newIAException( 'CN-1607', $msg, ['CN_ID' => $cnId, 'LINE_NO' => $lineNo, 'LAST_DATE' => FormatDateForDisplay($lastMEADate), 'CURR_DATE' => FormatDateForDisplay($currMEADate),
                                                                         'LAST_MEA_DATE' => FormatDateForDisplay($lastMEADate)]);
                }

                if ( !($this->meaDistributionCache[$jrnlCode] && $this->scheduleLinksToUse[$jrnlCode]) ) {

                    $cnDetail = ContractUtil::getRequiredContractDetailFields($contractDetailKey);

                    // Updated cache is passed from last invocation use it
                    // Only for v2 for now
                    if ( ContractUtil::enableMEAReclassV2() && isset($this->meaDistributionCacheOldUpdated[$cnDetail['CONTRACTKEY']][$jrnlCode])
                         && isset($this->scheduleLinksToUseOldUpdated[$cnDetail['CONTRACTKEY']][$jrnlCode]) ) {

                        $this->scheduleLinksToUse[$jrnlCode] = $this->scheduleLinksToUseOldUpdated[$cnDetail['CONTRACTKEY']][$jrnlCode];
                        $this->meaDistributionCache[$jrnlCode] = $this->meaDistributionCacheOldUpdated[$cnDetail['CONTRACTKEY']][$jrnlCode];

                    } else {

                        $rawAddlData = $event['ADDLDATA'];
                        $addlData = null;
                        if ( $rawAddlData ) {
                            $addlData = $this->getAddlDataForUse($rawAddlData);
                        }

                        if ( $addlData && $addlData['OLD_MEADISTRIBUTION'] && $addlData['OLD_SCHEDULELINKS'] ) {
                            $this->meaDistributionCache[$jrnlCode] = $addlData['OLD_MEADISTRIBUTION'];

                            // Set subsequent operations update this schedule links structure
                            // And finally make the redo of 'On MEA' use this for undo MEA operation
                            $this->scheduleLinksToUse[$jrnlCode] = $addlData['OLD_SCHEDULELINKS'];
                        }
                    }
                }

                $hasFutureMEA = true;
                $meaCount++;
            }
        }
    }

    /**
     * Schedule in the state indicates there is future MEA
     *
     * @param string $jrnlCode
     *
     * @return bool
     */
    protected function hasFutureMEA($jrnlCode)
    {
        return $this->scheduleLinksToUse[$jrnlCode] != null;
    }

    /**
     * @param int    $billingScheduleEntryKey
     * @param string $jrnlCode
     *
     * @return float
     */
    protected function getMEABSEAmount($billingScheduleEntryKey, $jrnlCode)
    {
        $this->getMEADistribution($billingScheduleEntryKey, $jrnlCode, $amountsByCNLine, $amountsByCNLineMap);

        $totalAmount = 0;
        foreach($amountsByCNLine as $amountDetails) {
            $totalAmount = ibcadd($totalAmount, $amountDetails['AMOUNT']);
        }

        return $totalAmount;
    }


    /**
     * @param string    $jrnlCode
     * @param int       $revScheduleEntryKey
     * @param int       $billingScheduleEntryKey
     * @param null|bool $revPosted
     * @param null|bool $billPosted
     * @param null|bool $pmtPosted
     *
     * @throws IAException
     */
    protected function updateLocalScheduleLinks($jrnlCode, $revScheduleEntryKey=null, $billingScheduleEntryKey=null, $revPosted=null, $billPosted=null, $pmtPosted=null)
    {
        if (!$this->scheduleLinksToUse[$jrnlCode]) {
            $msg = "Schedule links for $jrnlCode is not set in the state. Something went wrong.";
            throw IAException::newIAException( 'CN-1608', $msg, ['JRNL_CODE' => $jrnlCode]);
        }

        $size = count($this->scheduleLinksToUse[$jrnlCode]);
        for($i=0; $i<$size; $i++) {
            if ($billingScheduleEntryKey) {
                if ($this->scheduleLinksToUse[$jrnlCode][$i]["BILLSCHENTRYKEY"] == $billingScheduleEntryKey) {
                    if ($billPosted !== null) {

                        $this->scheduleLinksToUse[$jrnlCode][$i]["BILLPOSTED"] = $billPosted ? "T" : "F";

                        if ($billPosted) {

                            $exchRate = $this->getPostedExchRate($billingScheduleEntryKey);
                            $this->scheduleLinksToUse[$jrnlCode][$i]["BILLPOSTEDEXCHANGE_RATE"] = $exchRate;

                        } else {
                            $this->scheduleLinksToUse[$jrnlCode][$i]["BILLPOSTEDEXCHANGE_RATE"] = null;
                        }
                    }

                    if ($pmtPosted !== null) {
                        $this->scheduleLinksToUse[$jrnlCode][$i]["PMTPOSTED"] = $pmtPosted ? "T" : "F";
                    }
                }
            }

            if ($revScheduleEntryKey) {
                if ($this->scheduleLinksToUse[$jrnlCode][$i]["REVSCHENTRYKEY"] == $revScheduleEntryKey) {
                    if ($revPosted !== null) {
                        $this->scheduleLinksToUse[$jrnlCode][$i]["REVPOSTED"] = $revPosted ? "T" : "F";
                    }
                }
            }
        }
    }

    /**
     * @param string $jrnlCode
     * @param int    $bseKey
     *
     * @return bool
     */
    protected function isAffectedByLastMEA($jrnlCode, $bseKey)
    {
        $isAffected = false;
        $size = count($this->scheduleLinksToUse[$jrnlCode] ?? []);
        for($i=0; $i<$size; $i++) {
            if ($this->scheduleLinksToUse[$jrnlCode][$i]["BILLSCHENTRYKEY"] == $bseKey) {
                $isAffected = true;
            }
        }

        return $isAffected;
    }

    /**
     * @param int $billingScheduleEntryKey
     *
     * @return float
     */
    protected function getPostedExchRate($billingScheduleEntryKey)
    {
        if (!$this->postedExchRates[$billingScheduleEntryKey]) {

            $exchRate = EntityManager::GetListQuick('contractbillingscheduleentry', ['POSTEDEXCHANGE_RATE'], ['RECORDNO' => $billingScheduleEntryKey]);

            if ($exchRate && $exchRate[0] && $exchRate[0]['POSTEDEXCHANGE_RATE']) {
                $this->postedExchRates[$billingScheduleEntryKey] = $exchRate[0]['POSTEDEXCHANGE_RATE'];
            }
        }

        return $this->postedExchRates[$billingScheduleEntryKey];
    }

    /**
     * @param string $jrnlCode
     * @param int    $contractBillSchEntryKey
     *
     * @return int[]
     */
    protected function getUniqueContractDetailKeys($jrnlCode, $contractBillSchEntryKey)
    {
        $cndKeys = [];
        $qry = "SELECT UNIQUE contractdetailkey FROM v_contractresolvefixed WHERE cny#=:1 AND journaltype = :2 AND eventtype = :3 AND balancetype = :4 AND billingschentrykey =:5";
        $results = QueryResult([$qry, GetMyCompany(), $jrnlCode, 'I', 'R', $contractBillSchEntryKey]);
        foreach ($results as $res) {
            $cndKeys[] = $res['CONTRACTDETAILKEY'];
        }

        return $cndKeys;
    }

    /**
     * @param string $jrnlCode
     * @param string $triggeredFromCndKey
     */
    protected function runFutureMEAEvent($jrnlCode, $triggeredFromCndKey)
    {
        $meaEvent = $this->futureMEAEvent[$jrnlCode];
        if ($meaEvent) {

            // Only for v2 for now
            if ( ContractUtil::enableMEAReclassV2() ) {
                if ($this->eventAlreadyExecuted($this->bundleEventType, $jrnlCode, $triggeredFromCndKey)) {
                    return;
                }
            }

            $this->onMEA(
                $meaEvent['FK'],
                $meaEvent['TRANSACTIONDATE'],
                $this->getAddlDataForUse($meaEvent['ADDLDATA']),
                true,
                $jrnlCode
            );

            $this->futureMEAEvent[$jrnlCode] = null;

            if ( ContractUtil::enableMEAReclassV2() ) {
                $this->recordEvent($this->bundleEventType, $jrnlCode, null, $triggeredFromCndKey);
            }
        }
    }

    /**
     * @param string $jrnlCode
     *
     * @throws IAException
     */
    protected function runFutureMEASyncedRedoEvents($jrnlCode)
    {
        if ($this->futureMEASyncedRedoEvents[$jrnlCode]) {
            foreach ($this->futureMEASyncedRedoEvents[$jrnlCode] as $cndKey => $redoEvents) {
                $this->redoEvents($redoEvents, $cndKey, $jrnlCode);
            }

            $this->futureMEASyncedRedoEvents[$jrnlCode] = null;
        }
    }

    /**
     * @param int $contractRevSchEntryKey
     * @param int $lineno
     *
     * @return string|null
     *
     * @throws IAException
     */
    protected function getLinkedLineMaxStartDate($contractRevSchEntryKey, &$lineno)
    {
        $linkedLineQry = "SELECT UNIQUE billcontractdetailkey FROM v_contractschedulesresolve WHERE cny#=:1 AND revschentrykey=:2";
        $linkedLinesRes = QueryResult([$linkedLineQry, GetMyCompany(), $contractRevSchEntryKey]);

        $cndKeys = [];
        foreach ($linkedLinesRes as $res) {
            $cndKeys[] = $res['BILLCONTRACTDETAILKEY'];
        }

        $maxTxnDate = null;

        if ($cndKeys) {
            $inStr = implode(',', $cndKeys);
            $onCreateQry = "SELECT MAX(cr.transactiondate) maxtxndate, cnd.lineno FROM contractresolve cr, contractdetail cnd ";
            $onCreateQry .= "WHERE cr.cny#=:1 AND cnd.cny#=:1 AND cr.contractdetailkey IN ($inStr) AND cr.eventtype='C' AND cr.contractdetailkey=cnd.record# ";
            $onCreateQry .= "GROUP BY cnd.lineno ORDER BY maxtxndate DESC";

            $onCreateRes = QueryResult([$onCreateQry, GetMyCompany()]);

            if ($onCreateRes && $onCreateRes[0]) {
                $lineno = (int)$onCreateRes[0]['LINENO'];
                $maxTxnDate = $onCreateRes[0]['MAXTXNDATE'];
            }
        }

        return $maxTxnDate;
    }

    /**
     * @param int    $contractDetailKey
     * @param string $transactionDate
     * @param string $jrnlCode
     *
     * @throws IAException
     */
    protected function checkForFutureMEAs($contractDetailKey, $transactionDate, $jrnlCode)
    {
        if (!ContractUtil::clearingMEA()) {
            $eventType = self::getEventType((int) $contractDetailKey);

            $qry = "SELECT UNIQUE transactiondate FROM contractresolve WHERE cny#=:1 AND eventtype=:2 AND contractdetailkey=:3";
            $qry .= " AND journaltype=:4 AND transactiondate > :5 ORDER BY transactiondate ";

            $results = QueryResult([$qry, GetMyCompany(), $eventType, $contractDetailKey, $jrnlCode, $transactionDate]);

            if ($results && count($results) > 1) {

                $cnDetail = EntityManager::GetListQuick('contractdetail', ['CONTRACTID', 'LINENO'], ['RECORDNO' => $contractDetailKey]);
                if ($cnDetail && $cnDetail[0]) {
                    $cnId = $cnDetail[0]['CONTRACTID'];
                    $lineNo = $cnDetail[0]['LINENO'];
                } else {
                    $cnId = null;
                    $lineNo = null;
                }
                $lastMEADate = $results[0]['TRANSACTIONDATE'];
                $currMEADate = $results[1]['TRANSACTIONDATE'];

                // Should never happen
                assert($cnId);
                assert($lineNo);
                assert($lastMEADate);
                assert($currMEADate);

                $msg = "$cnId $lineNo is participating in MEA allocations on $lastMEADate and $currMEADate, transactions cannot be posted before $lastMEADate. Clear MEA allocations, post the transaction, and then re-create the MEA allocation.";
                throw IAException::newIAException( 'CN-1583', $msg, ['CN_ID' => $cnId, 'LINE_NO' => $lineNo, 'LAST_DATE' => FormatDateForDisplay($lastMEADate), 'CURR_DATE' => FormatDateForDisplay($currMEADate)]);
            }
        }
    }

    /**
     * @param int[]  $cnDetailKeys
     * @param string $effectiveDate
     *
     * @throws IAException
     */
    protected function checkMEAAgainstGLPostingdate($cnDetailKeys, $effectiveDate)
    {
        $cnDetailMgr = Globals::$g->gManagerFactory->getManager('contractdetail');
        $params = [
            'selects' => ['RECORDNO', 'GLPOSTINGDATE'],
            'filters' => [[['RECORDNO', 'IN', $cnDetailKeys]]]
        ];

        $cnDetails = $cnDetailMgr->GetList($params);

        foreach ($cnDetails as $cnDetail) {
            $glPostingDate = $cnDetail['GLPOSTINGDATE'];
            if (!isset($glPostingDate)) {
                $msg = "Could not find GL posting date for contract detail key: $glPostingDate";
                throw IAException::newIAException( 'CN-1609', $msg, ['GL_POSTING_DATE' => FormatDateForDisplay($glPostingDate)]);
            }

            if (DateCompare($effectiveDate, $glPostingDate) < 0) {
                $msg = "MEA effective date $effectiveDate cannot be before gl posting date $glPostingDate.";
                throw IAException::newIAException( 'CN-1610', $msg, ['EFFECTIVE_DATE' => FormatDateForDisplay($effectiveDate), 'GL_POSTING_DATE' => FormatDateForDisplay($glPostingDate)]);
            }
        }
    }

    /**
     * @param int     $revEntryKey
     * @param int     $contractDetailKey
     * @param string  $jrnlCode
     * @param string  $futureMEADate
     *
     * @return bool
     */
    protected function canDeleteRecognition($revEntryKey, $contractDetailKey, $jrnlCode, &$futureMEADate)
    {
        $canDelete = true;

        if (!ContractUtil::clearingMEA()) {
            $eventType = self::getEventType((int) $contractDetailKey);

            $qry = "
                SELECT
                  MIN(transactiondate) transactiondate
                FROM contractresolve
                  WHERE cny#=:1 AND contractdetailkey=:3 AND journaltype=:4 AND eventtype = :5
                  AND record# > (
                    SELECT MIN(record#)
                    FROM contractresolve
                    WHERE cny#=:1 AND revenueschentrykey=:2 AND contractdetailkey=:3 AND journaltype=:4 AND eventtype='R'
                  )
            ";

            $results = QueryResult([ $qry, GetMyCompany(), $revEntryKey, $contractDetailKey, $jrnlCode, $eventType ]);
            if ( $results !== false && $results[0]['TRANSACTIONDATE'] !== null ) {
                $canDelete = false;
                $futureMEADate = $results[0]['TRANSACTIONDATE'];
            }
        }

        return $canDelete;
    }

    /**
     * @param int               $paymentKey
     * @param false|string[][]  $fixedPriceLines
     * @param int[]             $usageBillingIds
     *
     * @throws IAException
     */
    public function onPaymentDelete($paymentKey, &$fixedPriceLines, &$usageBillingIds)
    {
        $fixedPriceLines = $this->onPaymentDeleteFixedPrice($paymentKey);

        $usageBillingIds = $this->onPaymentDeleteUsage($paymentKey);
    }

    /**
     * @param int $paymentKey
     *
     * @return false|string[][]
     * @throws IAException
     */
    protected function onPaymentDeleteFixedPrice($paymentKey)
    {
        $lines = $this->getPaymentAffectedLinesNonUsage($paymentKey);

        // Ignore if none found
        if ($lines) {
            $paymentPrentryKeys = $this->getPaymentPrentryKeys($paymentKey);


            foreach ($lines as $line) {

                $clearTransactionDate = $line['TRANSACTIONDATE'];
                $contractDetailKey = $line['CONTRACTDETAILKEY'];
                $jrnlCode = $line['JOURNALTYPE'];

                $identifierAttributes = [
                    'EVENTTYPE' => ContractGLReclassEvent::EVENTTYPE_ONPAYMENT,
                    'PAYMENTPRENTRYKEY' => $paymentPrentryKeys,
                    'CONTRACTDETAILKEY' => $contractDetailKey
                ];


                $this->clearExistingReclassTransactionsIncludingSelf(
                    $identifierAttributes,
                    $contractDetailKey,
                    $clearTransactionDate,
                    $jrnlCode,
                    $orderedEvents
                );


                if ($orderedEvents) {
                    foreach ($orderedEvents as $key => $oneOrder) {
                        if (!empty($oneOrder['PARENTPYMTRECORDKEY']) &&
                            $oneOrder['EVENTTYPE'] == ContractGLReclassEvent::EVENTTYPE_ONPAYMENT &&
                            $oneOrder['PARENTPYMTRECORDKEY'] == $paymentKey) {
                            unset($orderedEvents[$key]);
                        }
                    }
                    $orderedEvents = array_values($orderedEvents);
                }

                if ($orderedEvents && count($orderedEvents) > 0) {

                    // Important to run reclass for other invoices and payments for this journal alone
                    $this->forcedJrnlCode = $jrnlCode;

                    $this->redoEvents($orderedEvents, $contractDetailKey, $jrnlCode);
                }

                if (!$orderedEvents) {
                    $this->runNextContractDetailReclass($jrnlCode);
                }
            }
        } else {
            $lines = [];
        }

        return $lines;
    }

    /**
     * @param int $paymentKey
     *
     * @return int[]  the usage billing IDs
     * @throws IAException
     */
    protected function onPaymentDeleteUsage($paymentKey)
    {
        $usageBillingIds = $this->getPaidUsageBillingIds($paymentKey);

        if ($usageBillingIds) {
            $glBatchKeys = $this->getReclassGLBatchKeysForUsage($usageBillingIds);

            if ($glBatchKeys) {
                $this->deleteUsagePaymentResolve($glBatchKeys);
                $this->deleteGLBatches($glBatchKeys);
            }
        }

        return $usageBillingIds;
    }

    /**
     * @param int[] $glBatchKeys
     *
     * @throws IAException
     */
    protected function deleteUsagePaymentResolve($glBatchKeys)
    {
        $qry = "DELETE FROM contractresolve WHERE cny# = :1";
        $qparams = [$qry, GetMyCompany()];
        $qparams = PrepINClauseStmt($qparams, $glBatchKeys, " AND glbatchkey ");

        $ok = ExecStmt($qparams);
        if (!$ok) {
            $glBatchKeyValues =implode(", ",$glBatchKeys);
            $msg = "Error deleting usage payment reclass for GL batch keys: ".$glBatchKeyValues;
            throw IAException::newIAException( 'CN-1611', $msg, ['GL_BATCH_KEY_VALUES' => $glBatchKeyValues]);
        }
    }

    /**
     * @param int[] $usageBillingIds
     *
     * @return array
     */
    protected function getReclassGLBatchKeysForUsage($usageBillingIds)
    {
        $qry = "SELECT UNIQUE glbatchkey FROM contractresolve WHERE cny# = :1 AND glbatchkey IS NOT NULL ";
        $qparams = [$qry, GetMyCompany()];
        $qparams = PrepINClauseStmt($qparams, $usageBillingIds, " AND billablecontractusagebillingid ");

        $glBatchKeys = [];
        $results = QueryResult($qparams);
        foreach ($results as $result) {
            $glBatchKeys[] = $result['GLBATCHKEY'];
        }

        return $glBatchKeys;
    }

    /**
     * @param int $paymentKey
     *
     * @return array
     */
    protected function getPaidUsageBillingIds($paymentKey)
    {
        // get the paid usage billing ids including the usage billing ids that were used as payment (i.e.
        // those that were credits)
        $qry = "
            SELECT
              pe.billablecontractusagebillingid
            FROM
              prentrypymtrecs pymtrs, prentry pe
            WHERE
              pymtrs.cny# = :1 AND pe.cny# = :1
              AND pymtrs.paymentkey = :2
              AND (pymtrs.paiditemkey = pe.record#
                OR (pymtrs.payitemkey = pe.record# AND pe.recordtype = 'ri'))
              AND pe.billablecontractusagebillingid IS NOT NULL
        ";

        $usageBillingIds = [];

        $results = QueryResult([$qry, GetMyCompany(), $paymentKey]);

        foreach ($results as $result) {
            $usageBillingIds[] = $result['BILLABLECONTRACTUSAGEBILLINGID'];
        }

        return $usageBillingIds;
    }


    /**
     * @param int $paymentKey
     *
     * @return array
     *
     * @throws IAException
     */
    protected function getPaymentPrentryKeys($paymentKey)
    {
        $paymentPrentryKeysQry = "SELECT record# FROM prentry WHERE cny#=:1 AND recordkey=:2";

        $results = QueryResult([$paymentPrentryKeysQry, GetMyCompany(), $paymentKey]);

        $paymentPrentryKeys = [];
        foreach ($results as $result) {
            $paymentPrentryKeys[] = $result['RECORD#'];
        }
        return $paymentPrentryKeys;
    }

    /**
     * @param int $paymentKey
     *
     * @throws IAException
     */
    protected function deletePaymentResolve($paymentKey)
    {
        $delQry = "
            DELETE FROM contractresolve WHERE cny#=:1 AND paymentprentrykey IN (
              SELECT record# FROM prentry WHERE cny#=:1 AND recordkey=:2
            )
        ";

        $ok = ExecStmt([$delQry, GetMyCompany(), $paymentKey]);
        if (!$ok) {
            $msg = "Error deleting contractresolve records for paymentkey:$paymentKey";
            throw IAException::newIAException( 'CN-1613', $msg, ['PAYMENT_KEY' => $paymentKey]);
        }
    }

    /**
     * @param   int     $contractDetailKey      Contract detail key
     * @param   string  $transactionDate        Transaction date
     * @param   string  $jrnlCode               Journal code
     *
     * @throws IAException
     */
    private function validateFutureEventsForPaymentDelete($contractDetailKey, $transactionDate, $jrnlCode)
    {
        $jrnlType = $jrnlCode == 'J1' ? 'Journal 1' : 'Journal 2';
        $mgr = Globals::$g->gManagerFactory->getManager('contractresolve');
        $qrySpec = [
            'selects' => ['EVENTTYPE', 'TRANSACTIONDATE'],
            'distinct' => true,
            'filters' => [[
                ['CONTRACTDETAILKEY', '=', $contractDetailKey],
                ['TRANSACTIONDATE', '>=', $transactionDate],
                ['JOURNALTYPE', '=', $jrnlType],
                ['EVENTTYPE', 'in', [
                    ContractGLReclassEvent::EVENTTYPE_ONPAYMENT,
                    ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL,
                    ContractGLReclassEvent::EVENTTYPE_ONMEA,
                    ContractGLReclassEvent::EVENTTYPE_ONKIT
                ]],
            ]]
        ];
        $results = $mgr->GetList($qrySpec);
        if ($results) {
            throw new ValidateFutureEventsForDeleteIAException($transactionDate, $results);
        }
    }

    /**
     * @param int $paymentKey
     *
     * @return false|string[][]
     */
    protected function getPaymentAffectedLinesNonUsage($paymentKey)
    {
        $qry = "
          SELECT
            cr.journaltype, cr.contractdetailkey, MIN(cr.transactiondate) transactiondate
          FROM
            contractresolve cr, prentry pe
          WHERE
            cr.cny#=:1 AND pe.cny#=:1
            AND cr.paymentprentrykey=pe.record# AND pe.recordkey=:2
            and cr.type <> 'U'
          GROUP BY
            cr.journaltype, cr.contractdetailkey 
        ";

        return QueryResult([$qry, GetMyCompany(), $paymentKey]);
    }

    /**
     * @param int $scheduleKey
     * @return int|string
     */
    protected static function getTotalRevenueAmountByScheduleKey($scheduleKey){
        $params = array(
            'selects' => array(
                array(
                    'fields' => array('AMOUNT'),
                    'function' => 'sum(${1})'
                )
            ),
            'filters' => array(
                array(
                    array('schedulekey', '=', $scheduleKey),
                )
            )
        );
        $cnRevenueScheduleEntryMgr = Globals::$g->gManagerFactory->getManager('contractrevenuescheduleentry');
        $result = $cnRevenueScheduleEntryMgr->GetList($params);
        return    $result[0]['AMOUNT'];
    }

    /**
     * @param ContractResolveManager $cnResolveMgr
     *
     * @return string[]
     */
    protected function getResolveMgrGetFields($cnResolveMgr)
    {
        $selects = $cnResolveMgr->GetGetFields();
        $selects[] = 'ADDLDATA';
        $selects[] = 'ADDLDATAKEY';
        $selects[] = 'CONTRACTDETAILLINETYPE';
        $selects[] = 'ACTUALTRANSACTIONDATE';
        $selects[] = 'DATAVERSIONKEY';
        $selects[] = 'PARENTPYMTRECORDKEY';
        return $selects;
    }

    /**
     * @param int $contractDetailKey
     *
     * @return array
     * @throws IAException
     */
    protected function getContractDetail($contractDetailKey)
    {
        $cnDetail = EntityManager::GetListQuick(
            'contractdetail',
            ['LINENO', 'CONTRACTKEY', 'TOTALFLATAMOUNT', 'TOTALBASEFLATAMOUNT', 'EXCHANGE_RATE', 'GLPOSTINGDATE',
             'BEGINDATE', 'REVENUETEMPLATEKEY', 'REVENUE2TEMPLATEKEY', 'REVENUESCHEDULEKEY', 'REVENUE2SCHEDULEKEY',
             'ITEMTYPE', 'ITEMID', 'PARENTKEY', 'PARENTITEMTYPE',
            ],
            ['RECORDNO' => $contractDetailKey]
        );

        if (!$cnDetail[0]) {
            $msg = "Missing contract detail:" . $contractDetailKey;
            throw IAException::newIAException('CN-0699', $msg,
                                              ['CONTRACT_DETAIL_KEY' => $contractDetailKey]);
        }

        return $cnDetail[0];
    }
    
    /**
     * If there are no revenue templates then check child components to determine journals to process.
     *
     * @param array $entry
     * @param bool  $processJ1
     * @param bool  $processJ2
     *
     * @return array
     * @throws IAException
     */
    protected function getChildComponentProcessJournals($entry, $processJ1, $processJ2)
    {
        // If there are no revenue templates check child components to determine journals to process.
        if (!$entry['REVENUETEMPLATEKEY'] && !$entry['REVENUE2TEMPLATEKEY']) {
            $query = [
                'selects' => [
                    [
                        'fields' => ['REVENUETEMPLATEKEY'],
                        'function' => 'count(${1})'
                    ],
                    [
                        'fields' => ['REVENUE2TEMPLATEKEY'],
                        'function' => 'count(${1})'
                    ],
                ],
                'columnaliases' => ['REVENUE1', 'REVENUE2'],
                'filters' => [
                    [
                        ['PARENTKEY', '=', $entry['CONTRACTDETAILKEY']],
                    ]
                ]
            ];
        
            $mgr = Globals::$g->gManagerFactory->getManager('contractdetail');
            $result = $mgr->GetList($query);
        
            if (!$this->forcedJrnlCode || $this->forcedJrnlCode == ContractGLReclassEvent::JOURNALCODE_J1) {
                $processJ1 = $processJ1 || !empty($result[0]['REVENUE1']);
            }
        
            if (!$this->forcedJrnlCode || $this->forcedJrnlCode == ContractGLReclassEvent::JOURNALCODE_J2) {
                $processJ2 = $processJ2 || !empty($result[0]['REVENUE2']);
            }
        }
        
        return [$processJ1, $processJ2];
    }

    /**
     * @param int $contractBillSchEntryKey
     *
     * @return int
     * @throws IAException
     */
    protected function getContractDetailKey($contractBillSchEntryKey)
    {
        return ContractUtil::getContractDetailKey($contractBillSchEntryKey);
    }

    /**
     * @param int $contractRevSchEntryKey
     *
     * @return array
     * @throws IAException
     */
    protected function getRevenueScheduleEntry($contractRevSchEntryKey)
    {
        $entry = EntityManager::GetListQuick(
            'contractrevenuescheduleentry',
            ['AMOUNT', 'BASEAMOUNT', 'EXCHANGE_RATE', 'CONTRACTKEY', 'CONTRACTDETAILKEY', 'TYPE',
             'CONTRACTDETAIL.BEGINDATE', 'CNDETAILGLPOSTINGDATE', 'POSTINGDATE',
             'CONTRACT.CONTRACTID', 'CONTRACTDETAIL.LINENO', 'CONTRACTDETAIL.LINETYPE', 'POSTED', 'HISTORICAL',
             'CURRENCY', 'BASECURR', 'ACTUALPOSTINGDATE', 'GLBATCHKEY',
             'REVENUEJOURNALKEY', 'REVENUE2JOURNALKEY', 'ARUNBILLEDACCTKEY', 'ARBILLEDACCTKEY', 'DRUNBILLEDACCTKEY',
             'DRBILLEDACCTKEY', 'DRPAIDACCTKEY', 'SALESUNBILLEDACCTKEY', 'SALESBILLEDACCTKEY', 'SALESPAIDACCTKEY'],
            ['RECORDNO' => $contractRevSchEntryKey]
        );

        if (!$entry[0]) {
            $msg = "Missing revenue schedule entry:" . $contractRevSchEntryKey;
            throw IAException::newIAException( 'CN-1614', $msg, ['CONTRACT_REV_SCH_ENTRY_KEY' => $contractRevSchEntryKey]);
        }

        return $entry[0];
    }

    /**
     * @return bool
     */
    protected function isMCPEnabled()
    {
        return ContractUtil::isMCPEnabled();
    }

    /**
     * @return bool
     */
    protected function newMEA()
    {
        return ContractUtil::newMEA();
    }

    /**
     * @param string $effectiveDate
     *
     * @return bool
     */
    protected function isACPFlowLocal($effectiveDate)
    {
        return BaseContractGLReclassEngine::isACPFlow($effectiveDate);
    }

    /**
     * @param int $contractDetailKey
     * @param string $transactionDate
     * @param string $jrnlCode
     * @throws IAException
     */
    private function onEventPreProcessing($contractDetailKey, $transactionDate, $jrnlCode){


        if(!ContractUtil::isPostConResolveInOpenPeriod()) {

            //is any contract resolve is posted in open period and not in there orignal transaction date after this transaction date
            if (!isset($this->isCnResolvePostedInOpenPeriodFlag[$contractDetailKey][$jrnlCode])) {
                $this->isCnResolvePostedInOpenPeriodFlag[$contractDetailKey][$jrnlCode] = $this->isCnResolvePostedInOpenPeriod($transactionDate, $contractDetailKey, $jrnlCode);
            }

            if ($this->isCnResolvePostedInOpenPeriodFlag[$contractDetailKey][$jrnlCode]) {
                $msg = "Error deleting contract resolve records. There is reclass posted in open period after  $transactionDate. So Please post after this period.";
                throw IAException::newIAException( 'CN-1615', $msg, ['TRANSACTION_DATE' => FormatDateForDisplay($transactionDate)]);
            }
        }
        if(ContractUtil::isPostConResolveInOpenPeriod()) {
              if($this->isOpenRevSchEntryTillDate_ConSubPostInPeriod($contractDetailKey, $transactionDate, $jrnlCode)){
                  $msg = "Error in running reclass with contract subleder to post in open period. Please post all revenue for contract detailkey $contractDetailKey  till date $transactionDate";
                  throw IAException::newIAException( 'CN-1616', $msg, ['CONTRACT_DETAIL_KEY' => $contractDetailKey,'TRANSACTION_DATE' => FormatDateForDisplay($transactionDate)]);
              }
        }
    }

    /**
     * @param int $contractRevSchEntryKey
     * @throws IAException
     */
    private function deleteZeroValueRecCnResolveEntry($contractRevSchEntryKey){

        $cnResolve = EntityManager::GetListQuick(
            'contractresolve',
            ['RECORDNO', 'EVENTTYPE', 'AMOUNT', 'GLBATCHKEY'],
            ['REVENUESCHENTRYKEY' => $contractRevSchEntryKey, 'EVENTTYPE' => ContractRevenueGLReclassEvent::EVENTTYPE_ONRECOGNITION],
            [['RECORDNO']]
        );

        if ($cnResolve) {
            ContractUtil::assert(count($cnResolve)==2);
            ContractUtil::assert($cnResolve[0]['AMOUNT']==0);
            ContractUtil::assert($cnResolve[1]['AMOUNT']==0);

            $resolveRecs = array_column($cnResolve, 'RECORDNO');
            $reclassGLBatches = array_unique(array_column($cnResolve, 'GLBATCHKEY'));

            $this->deleteResolveByRecNos($resolveRecs);
            $this->deleteGLBatches($reclassGLBatches);
        }

    }

    /**
     * @return array
     */
    public function getCachedOldScheduleLinks()
    {
        // Can't $this->scheduleLinksToUse since it will be overridden by new MEA
        return $this->scheduleLinksToUseOldUpdated;
    }

    /**
     * @return array
     */
    public function getCachedOldMEADistribution()
    {
        // Can't $this->meaDistributionCache since it will be overridden by new MEA
        return $this->meaDistributionCacheOldUpdated;
    }

    /**
     * @param array $scheduleLinks
     * @param array $meaDistribution
     */
    public function setUpdatedMEALinksCache($scheduleLinks, $meaDistribution)
    {
        $this->scheduleLinksToUseOldUpdated = $scheduleLinks;
        $this->meaDistributionCacheOldUpdated = $meaDistribution;
    }
    
    public function setBundleType(string $bundleType)
    {
        $this->bundleType = $bundleType;

        if ($this->bundleType === ContractBundleManager::BUNDLE_TYPE_KIT) {
            $this->bundleEventType = ContractGLReclassEvent::EVENTTYPE_ONKIT;
            $this->bundleEntity = 'contractkitbundle';
            $this->bundleEntryEntity = 'contractkitbundleentry';
        } else {
            $this->bundleEventType = ContractGLReclassEvent::EVENTTYPE_ONMEA;
            $this->bundleEntity = 'contractmeabundle';
            $this->bundleEntryEntity = 'contractmeabundleentry';
        }
    }
    
    public function setBundleKey(?int $bundleKey)
    {
        $this->bundleKey = $bundleKey;
    }
    
    public function getBundleKey(): ?int
    {
        $bundleKey = null;
        
        if ($this->bundleType === ContractBundleManager::BUNDLE_TYPE_KIT) {
            $bundleKey = $this->bundleKey;
        }
        
        return $bundleKey;
    }

    /**
     * @param int $contractDetailKey
     * @param string $transactionDate
     * @param string $journalCode
     * @return void
     * @throws IAException
     */
    public function onOneTimeDatFixJournalDelete(int $contractDetailKey, string $transactionDate, string $journalCode){

        $identifierAttributes = [
            'EVENTTYPE' => ContractGLReclassEvent::EVENTTYPE_DATA_ADJUSTMENT
        ];

        $this->clearExistingReclassTransactionsIncludingSelf(
            $identifierAttributes,
            $contractDetailKey,
            $transactionDate,
            $journalCode,
            $orderedEvents
        );
        
        if (!empty($orderedEvents)){
            throw new IAException('Has future event', 'CN-1617');
        }

        $auditTrailSession = AuditTrailSession::getInstance();
        $auditTrailSession->addAuditEvent(
            'contractdetail',
            $contractDetailKey,
            AuditTrail::AUDITTRAIL_EVENT_DELETE,
            null,
            'onetimedatafix'
        );
    }
    
    private function getAddlDataKeys(array $contractDetailKeys, ?string $effectiveDate): array
    {
        $result = [];

        $resolveMgr = Globals::$g->gManagerFactory->getManager('contractresolve');
        $params = [
            'selects' => ['ADDLDATAKEY'],
            'filters' => [
                [
                    ['CONTRACTDETAILKEY', 'IN', $contractDetailKeys],
                    ['ADDLDATAKEY', 'IS NOT NULL']
                ],
            ],
        ];

        if (isset($effectiveDate)) {
            $params['filters'][0][] = ['TRANSACTIONDATE', '>=', $effectiveDate];
        }
        
        $resolves = $resolveMgr->GetList($params);
        if (!empty($resolves)) {
            $result = array_unique(array_column($resolves, 'ADDLDATAKEY'));
        }
    
        return $result;
    }
    
    protected static function getEventType(int $contractDetailKey): string
    {
        if (ContractDetailManager::isKitOrComponentFromKey($contractDetailKey)) {
            $eventType = 'K';
        } else {
            $eventType = 'M';
        }
        
        return $eventType;
    }
}

class ValidateFutureEventsForDeleteIAException extends IAException
{
    /** @var string $startTransactionDate */
    private $startTransactionDate;

    /** @var string[][] $futureTransactions */
    private $futureTransactions;

    /**
     * ValidateFutureEventsForDeleteIAException constructor.
     *
     * @param string     $startTransactionDate
     * @param string[][] $futureTrnsactions
     */
    public function __construct(string $startTransactionDate, array $futureTrnsactions)
    {
        $this->startTransactionDate = $startTransactionDate;
        $this->futureTransactions = $futureTrnsactions;
    }

    /**
     * @return string
     */
    public function getStartTransactionDate() : string
    {
        return $this->startTransactionDate;
    }

    /**
     * @return string[][]
     */
    public function getFutureTransactions() : array
    {
        return $this->futureTransactions;
    }
}