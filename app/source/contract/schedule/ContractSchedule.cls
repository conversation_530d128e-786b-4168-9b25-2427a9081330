<?php
/**
 * File ContractSchedule.cls contains the class ContractSchedule
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

import('ContractUtil');

/**
 * Abstract base class for all Contract schedules that implements schedule interface
 *
 * Class ContractSchedule
 */
abstract class ContractSchedule implements IContractSchedule
{
    const LOCAL_TOKENS = [
        'IA.TEMPLATE_FOR_CUSTOMER_WITH_TITLE_BATCH_DATE',
        'IA.CONTRACT_WITH_SCHEDULE_ENTRY',
        'IA.CONTRACT_AND_LINENO_WITH_SCHEDULE_ENTRY',
        'IA.PARTIAL_AMOUNT_LEFT_OPEN_AFTER_CANCELLATION',
        'IA.PARTIAL_AMOUNT_AFTER_CANCELLATION',
        'IA.MEA_ENTRY_POSTING_DATE_ADJUSTMENT_MEMO',
        'IA.ADJUSTMENT_FOR_PENNY_ROUNDING',
        'IA.ADJUSTMENT_FOR_PENNY_ROUNDING_MISSPELLED',
    ];

    const TYPE_BILLING = 'Billing';
    const TYPE_BILLING_CODE = 'B';
    const TYPE_PAYMENT = 'Payment';
    const TYPE_PAYMENT_CODE = 'P';
    const TYPE_EXPENSE = 'Expense';
    const TYPE_EXPENSE_CODE = 'E';
    const TYPE_EXPENSE2 = 'Expense2';
    const TYPE_EXPENSE2_CODE = 'E2';
    const TYPE_REVENUE = 'Revenue';
    const TYPE_REVENUE_CODE = 'R';
    const TYPE_REVENUE2 = 'Revenue2';
    const TYPE_REVENUE2_CODE = 'R2';

    const JOURNAL1 = 'J1';
    const JOURNAL2 = 'J2';

    // these are the DB values for the constants defined in ContractScheduleState
    const STATE_DRAFT = 'D';
    const STATE_INPROGRESS = 'I';
    const STATE_ONHOLD = 'H';
    const STATE_COMPLETED = 'C';
    const STATE_TERMINATED = 'T';
    const STATE_PENDINGDELIVERY = 'P';
    const STATE_PENDINGDELIVERYALL = 'A';
    const STATE_ESITMATE_COMPLETED = 'E';
    const STATE_RENEWAL_FORECAST = 'F';

    /* @var ContractScheduleCreateParams $createParams */
    protected $createParams;

    /* @var ContractScheduleInfo $contractScheduleInfo */
    protected $contractScheduleInfo;

    /* @var int $key */
    protected $key;

    /* @var array $entriesCache */
    protected $entriesCache;

    // Amount to be kept open on terminate
    /* @var float $expectedOpenAmountOnTerminate */
    protected $expectedOpenAmountOnTerminate = 0;
    /* @var bool $terminateAllOpen */
    protected $terminateAllOpen = false;

    /** @var int $trTypeMultiplier */
    private $trTypeMultiplier = 1;

    /** @var bool $respectGlPostingFlag */
    private $respectGlPostingFlag = true;

    /**
     * @var bool $changeState
     */
    private $changeState = true;

    /**
     * @var int $contractMELocKey
     */
    protected $contractMELocKey;
    
    /** @var array|bool|null $textMap */
    protected static $textMap = null;
    
    
    /**
     * @return bool
     */
    public function isChangeState(): bool
    {
        return $this->changeState;
    }

    /**
     * @param bool $changeState
     */
    public function setChangeState(bool $changeState): void
    {
        $this->changeState = $changeState;
    }


    /**
     * @return bool
     */
    public function isRespectGlPostingFlag()
    {
        return $this->respectGlPostingFlag;
    }

    /**
     * @param bool $respectGlPostingFlag
     */
    public function setRespectGlPostingFlag($respectGlPostingFlag)
    {
        $this->respectGlPostingFlag = $respectGlPostingFlag;
    }

    /**
     * @param int $key Schedule key
     */
    public function __construct($key = null)
    {
        if ($key) {
            $this->key = $key;
        }
        $this->contractScheduleInfo = new ContractScheduleInfo();
        self::loadTokenLabels();
    }
    
    /**
     * Load labels from tokens
     *
     * @throws APIInternalException
     */
    protected static function loadTokenLabels(): void
    {
        if (self::$textMap === null) {
            $errMsg = '';
            $tokens = self::LOCAL_TOKENS;
            $textMap = getLocalizedText(I18N::tokenArrayToObjectArray($tokens), $errMsg);
            if (!$textMap) {
                logToFileError(
                    get_called_class() . ':loadTokenLabels. Labels could not be loaded from text service. Error message: ' . $errMsg
                );
                self::$textMap = [];
            } else {
                self::$textMap = $textMap;
            }
        }
    }

    /**
     * Responsible for calculating schedule entries
     *
     * @param ContractScheduleCreateParams $params Value object of create params
     *
     * @return bool
     */
    abstract public function create(ContractScheduleCreateParams $params);

    /**
     * Schedule type i.e. revenue, expense etc.
     *
     * @return string
     */
    abstract public function getType();

    /**
     * Generates contract schedule record
     *
     * @return array
     */
    protected function generateSchedule()
    {
        $schedule = array(
            'CONTRACTKEY' => $this->createParams->getContractKey(),
            'CONTRACTDETAILKEY' => $this->createParams->getContractDetailKey(),
        );

        if ($this->createParams->isDraft()) {
            $schedule['STATE'] = ContractScheduleState::STATE_DRAFT;
            $this->contractScheduleInfo->setState(ContractScheduleState::STATE_DRAFT);
        } else if ($this->createParams->isRenewalForecast()) {
            $schedule['STATE'] = ContractScheduleState::STATE_RENEWAL_FORECAST;
            $this->contractScheduleInfo->setState(ContractScheduleState::STATE_RENEWAL_FORECAST);
        }

        $this->contractScheduleInfo->setContractKey($this->createParams->getContractKey());
        $this->contractScheduleInfo->setContractDetailKey($this->createParams->getContractDetailKey());
        return $schedule;
    }

    /**
     * Generates schedule entries.  Subclass needs to set $this->entries data member.
     *
     * @return array
     */
    abstract protected function generateScheduleEntries();

    /**
     * Creates Schedule record in DB
     *
     * @param array $schedule Schedule attributes
     * @param array $entries  Schedule entries
     *
     * @throws IAException
     */
    protected function createSchedule(&$schedule, &$entries)
    {
        //TODO fix this to use ContractScheduleEntry object from the ContractScheduleEntry array
        $scheduleEntries = array();
        $isObject = false;
        foreach ($entries as $entry) {
            if ($entry instanceof ContractScheduleEntry) {
                $isObject = true;
                $scheduleEntries[] = $entry->getValues();
            } else {
                if ($isObject == true) {
                    $msg = "Error creating Schedule. Please contact support.";
                    Globals::$g->gErr->addError('CN-0454', __FILE__ . ':' . __LINE__, $msg);
                    throw new IAException($msg, 'CN-1491');
                }
                $scheduleEntries[] = $entry;
            }
        }
        $schedule['SCHEDULEENTRY'] = &$scheduleEntries;

        $schMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntity());
        $ok = $schMgr->add($schedule);

        if (!$ok) {
            if (Globals::$g->gErr->hasErrors()) {

                // TODO: Check error handling
                $errs = Globals::$g->gErr->getErrors();
                throw new IAException(implode("\n", $errs)); // TODO: i18n {getErrors message not tokenized}
            } else {
                $msg = "Error creating Schedule. Please contact support.";
                Globals::$g->gErr->addError('CN-0454', __FILE__ . ':' . __LINE__, $msg);
                throw new IAException($msg, 'CN-1491');
            }
        }

        //TODO find a common place to do this for all objects
        //this is needed to update the recordno in the field array to be used by other area of the code
        foreach ($scheduleEntries as $id => $entry) {
            /* @var ContractScheduleEntry $entryObject */
            $entryObject = $entries[$id];
            $entryObject->setEmValues($entry);
        }

        $this->key = $schedule['RECORDNO'];
    }

    /**
     * Post and commit schedule entries for one contractId at a time.
     *
     * @param array                        $scheduleEntriesMap
     * @param string                       $postingDate
     * @param string                       $journalFilter
     * @param ContractSchedulePostResponse $contractSchedulePostResponse
     * @param bool                         $useScheduleDate
     *
     * @return bool
     */
    public static function postScheduleEntries($scheduleEntriesMap, $postingDate, $journalFilter, $contractSchedulePostResponse, $useScheduleDate = false)
    {
        $ok = true;
        $source = 'ContractSchedule::postScheduleEntries';
        $contractObjhandler = new ContractObjHandler();

        $scheduleEntriesMapBySchTypeAndContractId = self::groupScheduleEntriesMapBySchTypeContractId($scheduleEntriesMap);
        $contractSchedulePostResponse->setJournalType($journalFilter);

        foreach ($scheduleEntriesMapBySchTypeAndContractId as $schType => $scheduleEntriesMapByContractId) {
            foreach ($scheduleEntriesMapByContractId as $contractId => $scheduleEntriesArray) {

                $ok = Globals::$g->gQueryMgr->beginTrx($source);
                $numberOfSchedulesSkippedPerContract = 0;
                $numberOfSchedulesPostedPerContract = 0;
                $assertCnSchKeyList = [];

                foreach ($scheduleEntriesArray as & $entry) {
                    $sch = null;
                    try {
                        //reset the readtimegmt in the request to avoid getting caught in trigger
                        $ok = ExecStmt(array("begin TMproc('readtimegmt', :1); end;", ServeCurrentTimestampGMT(true)));
                        if ($ok == false) {
                            Globals::$g->gErr->addError('CN-0462', $source, 'Error in calling reset readtime');
                            $ok = false;
                            break;
                        }

                        $sch = $contractObjhandler->getContractScheduleWithTemplate($entry['SCHEDULEKEY'],$schType);
                        //for project based template use the scheduled date as posting date
                        if ($sch !== null && $useScheduleDate) {
                            if (isset($entry['POSTINGDATE'])) {
                                $postingDate = $entry['POSTINGDATE'];
                            }
                        }
                        $ok = $sch->post($entry['SCHEDULEENTRYKEY'], $postingDate);
                        $numberOfSchedulesPostedPerContract++;
                        // Collect contract schedule key for re-class missing events assertions.
                        $assertCnSchKeyList[] = $entry['SCHEDULEKEY'];

                    } catch (IAException $ex) {
                        if ($ex->getErrorCode() === ContractUtil::SCHEDULE_ALREADY_POSTED) {
                            $numberOfSchedulesSkippedPerContract++;
                            $ok = true;
                        } else {
                            if ($sch) {
                                self::setErrorDetails($sch, $schType, $contractSchedulePostResponse, $entry, $ex);
                            }
                            $ok = false;
                            break;
                        }
                    }
                }

                //$ok = $ok && !Globals::$g->gErr->hasErrors();

                if ($schType == self::TYPE_EXPENSE || $schType == self::TYPE_EXPENSE2) {
                    $ok = $ok && ContractMissingEventAssertionsUtil::assertByExpScheduleKey($assertCnSchKeyList);
                } else {
                    $ok = $ok && ContractMissingEventAssertionsUtil::assertByScheduleKey($assertCnSchKeyList);
                }

                $ok = $ok && Globals::$g->gQueryMgr->commitTrx($source);

                if (!$ok) {
                    Globals::$g->gQueryMgr->rollbackTrx($source);
                } else {
                    self::trackPostings($contractId, $schType, $contractSchedulePostResponse, $numberOfSchedulesSkippedPerContract, $numberOfSchedulesPostedPerContract);
                }
            }
        }

        return $ok;
    }

    /**
     * @param array $scheduleEntriesMap
     *
     * @return array
     */
    public static function groupScheduleEntriesMapBySchTypeContractId($scheduleEntriesMap)
    {
        $scheduleEntriesMapByContractId = [];
        foreach ($scheduleEntriesMap as $schType => $scheduleEntries) {

            $scheduleEntriesByContractId = [];
            foreach($scheduleEntries as $entry)
            {
                $contractId = $entry['CONTRACTNO'];
                $scheduleEntriesByContractId[$contractId][] = $entry;
            }
            $scheduleEntriesMapByContractId[$schType] = $scheduleEntriesByContractId;
        }

        return $scheduleEntriesMapByContractId;
    }

    /**
     * @param ContractSchedule             $sch
     * @param string                       $schType
     * @param array                        $entry
     * @param ContractSchedulePostResponse $contractSchedulePostResponse
     * @param IAException                  $exception
     */
    private static function setErrorDetails($sch, $schType, $contractSchedulePostResponse, $entry, $exception) {

        $contractScheduleInfo = $sch->getContractScheduleInfo();
        $contractScheduleEmValues = $contractScheduleInfo->getEmValues();

        $contractSchedulePostErrorResponse = new ContractSchedulePostErrorResponse();
        $contractSchedulePostErrorResponse->setContractId($contractScheduleEmValues['CONTRACTID']);
        $contractSchedulePostErrorResponse->setContractLineNumber($contractScheduleEmValues['LINENO']);
        $contractSchedulePostErrorResponse->setPostingDate($entry['POSTINGDATE']);
        $contractSchedulePostErrorResponse->setAmount($entry['AMOUNT']);
        $contractSchedulePostErrorResponse->setExceptionMessage($exception->getMessage());

        if ( $schType === self::TYPE_REVENUE || $schType === self::TYPE_REVENUE2) {
            $contractSchedulePostResponse->addContractRevSchedulePostErrorResponse($contractSchedulePostErrorResponse);
        } else if ( $schType === self::TYPE_EXPENSE || $schType === self::TYPE_EXPENSE2) {
            $contractSchedulePostResponse->addContractExpSchedulePostErrorResponse($contractSchedulePostErrorResponse);
        }
    }

    /**
     * @param string                       $contractId
     * @param string                       $schType
     * @param ContractSchedulePostResponse $contractSchedulePostResponse
     * @param int                          $numberOfSchedulesSkipped
     * @param int                          $numberOfSchedulesPosted
     */
    private static function trackPostings($contractId, $schType, $contractSchedulePostResponse, $numberOfSchedulesSkipped, $numberOfSchedulesPosted)
    {
        if ( $schType === self::TYPE_REVENUE || $schType === self::TYPE_REVENUE2) {

            $contractSchedulePostResponse->addToNumberOfRevenueSchedulesSkipped($contractId, $numberOfSchedulesSkipped);
            $contractSchedulePostResponse->addToNumberOfRevenueSchedulesPosted($contractId, $numberOfSchedulesPosted);

        } else if ( $schType === self::TYPE_EXPENSE || $schType === self::TYPE_EXPENSE2) {

            $contractSchedulePostResponse->addToNumberOfExpenseSchedulesSkipped($contractId, $numberOfSchedulesSkipped);
            $contractSchedulePostResponse->addToNumberOfExpenseSchedulesPosted($contractId, $numberOfSchedulesPosted);

        }
    }


    /**
     * Marks supplied schedule entry as 'Posted' and runs custom posting routine per schedule type
     *
     * @param   int     $entryKey       Schedule entry key
     * @param   int     $postingDate    Posting date
     * @param   bool    $skipPrecheck
     * @param   ?string $checkOnlyJournal
     *
     * @return bool
     *
     * @throws IAException
     */
    public function post($entryKey, $postingDate, bool $skipPrecheck = false, string $checkOnlyJournal = null)
    {
        $ok = true;
        
        if (!$entryKey) {
            $msg = "Nothing to post. Either the key is not passed or there is no due entry.";
            throw new IAException( $msg,'CN-1492');
        }

        $entry = $this->getEntry($entryKey);
        if ($entry) {
            if ($entry['STATE'] === ContractScheduleEntryState::STATE_ONHOLD) {
                $msg = "The schedule entry is on hold. The post process cannot continue.";
                throw new IAException( $msg,'CN-1493');
            }
            if ($entry['SCHEDULESTATE'] == 'Pending delivery') {
                $msg = "The schedule is pending delivery. The post process cannot continue.";
                throw new IAException( $msg,'CN-1494');
            }
        }

        $metricsObj = ContractDetailManager::startMetrics();

        //check for contract lock here and throw exception if locked
        $source = 'ContractManager::Set()';
        XACT_BEGIN($source);

        try {
            if (!$skipPrecheck) {
                // Pre-check for mismatching balances - caused by corrupt resolve records
                $ok = $ok && ContractMissingEventAssertionsUtil::checkJournalDataForId(
                    $entry['CONTRACTID'], 'IA.ON_RECOGNITION', false, $checkOnlyJournal);
            }
            $this->setLastUpdatedAt();
            $this->validatePostAndLockRecord($entryKey, $postingDate);

            // Important that POSTED flag is set first
            $this->markPosted($entryKey, $postingDate);
            $this->postTransactions($entryKey, $postingDate);
            //check for marking complete state on the schedule
            $this->markCompleted();

            // Post-check for mismatching balances - caused by corrupt resolve records
            $ok = $ok && ContractMissingEventAssertionsUtil::checkJournalDataForId(
                $entry['CONTRACTID'], 'IA.ON_RECOGNITION', true, $checkOnlyJournal);
            $ok = $ok && XACT_COMMIT($source);
        } catch(IAException $ex) {
            $ok = false;
        }
        
        $key = $objectType = null;
        $cndKey = $entry['CONTRACTDETAILKEY'] ?? null;
        $cnedKey = $entry['CONTRACTEXPENSEDETAILKEY'] ?? null;
        if ($cndKey) {
            $objectType = 'contractdetail';
            $key = $cndKey;
        } elseif ($cnedKey) {
            $objectType = 'contractexpense';
            $key = $cnedKey;
        }
        
        if ($objectType && $key) {
            $auditTrailSession = AuditTrailSession::getInstance();
            $ok = $ok && $auditTrailSession->addAuditEvent(
                    $objectType,
                    $key,
                    AuditTrail::AUDITTRAIL_EVENT_WORKFLOW,
                    null,
                    $this->getScheduleType() . ': post on ' . $postingDate
                );
        }
        

        if (!$ok) {
            XACT_ABORT($source);
        }
        
        if ($objectType && $key) {
            $metricsObj->setType($this->getScheduleType());
            ContractDetailManager::publishMetricsForObject($metricsObj, ContractUtil::METRIC_ACTION_POST, $objectType, $key, $ok);
        }
        
        if (!empty($ex)) {
            throw $ex;
        }
        
        return $ok;
    }

    /**
     * Change contract schedule state when revenues are manually posted
     * from Contracts -> Manage Contract Schedules link.
     *
     * @throws IAException
     */
    public function markCompleted()
    {
        $ok = true;
        $errorTxt = "";
        $scheduleKey = $this->getKey();

        if(empty($scheduleKey)) {
            $msg = "Schedule key is missing, unable to update contract schedule status.";
            throw new IAException($msg,'CN-1495');
        }

        $stateCount = $this->getScheduleEntriesCountByState($scheduleKey);

        if ( $stateCount === null ) {
            // The 'stateCount' object of type ContractScheduleEntryCounts
            // is null, return.

            return;
        }

        if ( $stateCount->getHasNonPostable() === true ) {
            // Non postable entries exists, return.

            return;
        }

        // Get revenue schedule entries state counts.

        $totalCount = $stateCount->getTotalEntriesCount() ?? 0;
        $openCount = $stateCount->getOpenEntriesCount() ?? 0;
        $terminatedCount = $stateCount->getTerminatedEntriesCount() ?? 0;
        $holdCount = $stateCount->getOnHoldEntriesCount() ?? 0;
        $postedCount = $stateCount->getPostedEntriesCount() ?? 0;

        if ( $openCount > 0 ) {
            // By default revenue schedule sate will be in 'In Progress'.
            // No need to change state, return.

            return;
        }

        if ( $totalCount == $terminatedCount ) {
            // All the entries are terminated.
            // No need to change state, return.

            return;
        }

        /* @var ContractScheduleManager $schMgr */
        $schMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntity());

        if ( $holdCount > 0 ) {
            // Set state to 'Hold'.

            $ok = $schMgr->setOnHold($scheduleKey);

            if ( !$ok ) {
                $errorTxt = "hold";
            }
        }
        else if ( $terminatedCount > 0 && $totalCount == ($postedCount + $terminatedCount) ) {
            // Set state to 'Cancelled'.

            $cnSch = $schMgr->get($scheduleKey);
            $ok = $schMgr->setTerminated($scheduleKey, $cnSch['CANCELDATE']);

            if ( !$ok ) {
                $errorTxt = "cancelled";
            }
        }
        else if ( $totalCount == $postedCount ) {
            // Set state to 'Completed'.

            $ok = $schMgr->setCompleted($scheduleKey);

            if ( !$ok ) {
                $errorTxt = "completed";
            }
        }

        if ( !$ok ) {
            $msg = "Could not set {$errorTxt} (schedule key: {$scheduleKey})";
            throw IAException::newIAException( 'CN-1496', $msg, ['ERROR_TXT' => $errorTxt, 'SCHEDULE_KEY' => $scheduleKey]);
        }
    }

    /**
     * when user marke the contract line as complete ,then mark schedule as esitmate completed and update complete date
     * @param string $completeDate
     * @return bool
     * @throws IAException
     */
    public function markEstimateCompleted($completeDate)
    {

        $errorTxt = "";
        $scheduleKey = $this->getKey();

        /* @var ContractScheduleManager $schMgr */
        $schMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntity());

        $stateCount = $this->getScheduleEntriesCountByState($this->getKey());

        if ( $stateCount !== null  && $stateCount->getOpenEntriesCount() !== null && $stateCount->getOpenEntriesCount() > 0 ) {
            //there are still open entries. The schedule cannot be marked as terminated
            $ok = $schMgr->setCancelDateInSchedule($this->getKey(), $completeDate);
            return $ok;
        }
        $schMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntity());
       // Set state to 'Estimate Completed'.
        $ok = $schMgr->setEstimateCompleted($scheduleKey , $completeDate);

        if ( !$ok ) {
            $errorTxt = "Estimate revalued";
        }
        if ( !$ok ) {
            $msg = "Could not set {$errorTxt} (schedule key: {$scheduleKey})";
            throw IAException::newIAException( 'CN-1496' , $msg, ['ERROR_TXT' => $errorTxt, 'SCHEDULE_KEY' => $scheduleKey]);
        }
        return $ok;
    }
    
    /**
     * Puts schedule on-hold
     *
     * @param int $entryKey Schedule entry key
     *
     * @throws IAException
     */
    public function unpost($entryKey)
    {
        $ok = true;

        assert($entryKey, "Schedule entry key is required.");
        
        $metricsObj = ContractDetailManager::startMetrics();

        $source = 'ContractManager::unpost()';
        XACT_BEGIN($source);

        try {
            $entry = $this->getEntry($entryKey);
            // Pre-check for mismatching balances - caused by corrupt resolve records
            $ok = $ok && ContractMissingEventAssertionsUtil::checkJournalDataForId($entry['CONTRACTID'], 'IA.ON_RECOGNITION', false);
            $this->setLastUpdatedAt();
            $this->validateUnpost($entryKey);

            $this->undoTransactions($entryKey);
            $this->undoPosted($entryKey);
            if ( $this->isScheduleOnHold() == false ) {
                $this->markAsInProgress($this->getKey(), $entryKey);
            }
            
            // Post-check for mismatching balances - caused by corrupt resolve records
            $ok = $ok && ContractMissingEventAssertionsUtil::checkJournalDataForId($entry['CONTRACTID'], 'IA.ON_RECOGNITION', true);
            $ok = $ok && XACT_COMMIT($source);
            
            $key = $objectType = null;
            $cndKey = $entry['CONTRACTDETAILKEY'] ?? null;
            $cnedKey = $entry['CONTRACTEXPENSEDETAILKEY'] ?? null;
            if ($cndKey) {
                $objectType = 'contractdetail';
                $key = $cndKey;
            } elseif ($cnedKey) {
                $objectType = 'contractexpense';
                $key = $cnedKey;
            }

            if ($objectType && $key) {
                $auditTrailSession = AuditTrailSession::getInstance();
                $ok = $ok && $auditTrailSession->addAuditEvent(
                        $objectType,
                        $key,
                        AuditTrail::AUDITTRAIL_EVENT_WORKFLOW,
                        null,
                        $this->getScheduleType() . ': clear'
                    );
            }
        } catch(IAException $ex) {
            $ok = false;
        }

        if (!$ok) {
            XACT_ABORT($source);
        }
        
        $key = $objectType = null;
        $cndKey = $entry['CONTRACTDETAILKEY'] ?? null;
        $cnedKey = $entry['CONTRACTEXPENSEDETAILKEY'] ?? null;
        if ($cndKey) {
            $objectType = 'contractdetail';
            $key = $cndKey;
        } elseif ($cnedKey) {
            $objectType = 'contractexpense';
            $key = $cnedKey;
        }

        if ($objectType && $key) {
            $metricsObj->setType($this->getScheduleType());
            ContractDetailManager::publishMetricsForObject($metricsObj, ContractUtil::METRIC_ACTION_CLEAR, $objectType, $key, $ok);
        }
        
        if (!empty($ex)) {
            throw $ex;
        }
    }

    /**
     * @param string $terminationDate
     *
     * @return bool
     *
     * @throws IAException
     */
    private function markTerminated($terminationDate)
    {
        /* @var ContractScheduleManager $schMgr */
        $schMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntity());
        
        $stateCount = $this->getScheduleEntriesCountByState($this->getKey());
        
        if ( $stateCount !== null  && $stateCount->getOpenEntriesCount() !== null && $stateCount->getOpenEntriesCount() > 0 ) {
            //there are still open entries. The schedule cannot be marked as terminated
            $ok = $schMgr->setCancelDateInSchedule($this->getKey(), $terminationDate);
            return $ok;
        }
        
        $ok = $schMgr->setTerminated($this->getKey(), $terminationDate);
    
        if (!$ok) {
            $msg = "Could not set in terminate (schedule key:".$this->getKey().")";
            throw IAException::newIAException( 'CN-1497', $msg, ['SCHEDULE_KEY' => $this->getKey()] );
        }

        return $ok;
    }

    /**
     * @return bool
     */
    public function setScheduleToInProgress()
    {
        $result = $this->markAsInProgress($this->getKey(), null);
        return $result;
        
    }

    /**
     * @return bool
     */
    public function setScheduleToDraft()
    {
        $result = $this->markAsDraft($this->getKey(), null);
        return $result;

    }
    
    /**
     * @param int      $scheduleKey
     * @param int|null $scheduleEntryKey
     *
     * @return bool
     *
     * @throws IAException
     */
    private function markAsInProgress($scheduleKey, $scheduleEntryKey)
    {
        $ok = $this->setScheduleStateForEntriesAsInProgressOrDraft($scheduleKey, $scheduleEntryKey, true);
        
        if (!$ok) {
            $msg = "Could not set in progress (schedule key:".$scheduleKey.")";
            throw IAException::newIAException( 'CN-1498', $msg, ['SCHEDULE_KEY' => $scheduleKey]);
        }

        return $ok;
    }
    
    /**
     * @param int      $scheduleKey
     * @param int|null $scheduleEntryKey
     *
     * @return bool
     *
     * @throws IAException
     */
    private function markAsDraft(int $scheduleKey, $scheduleEntryKey)
    {
        $ok = $this->setScheduleStateForEntriesAsInProgressOrDraft($scheduleKey, $scheduleEntryKey, false);

        if (!$ok) {
            $msg = "Could not set in draft (schedule key:".$scheduleKey.")";
            throw IAException::newIAException( 'CN-1499', $msg, ['SCHEDULE_KEY' => $scheduleKey]);
        }

        return $ok;
    }

    /**
     * @param int $scheduleKey
     * @param int $scheduleEntryKey
     * @param  bool $inProgress true for inprogress / false for draft
     *
     * @return bool
     *
     */
    private function setScheduleStateForEntriesAsInProgressOrDraft($scheduleKey, $scheduleEntryKey, $inProgress) {

        if ( $scheduleKey === null && $scheduleEntryKey === null ) {
            return false;
        }
        /* @var ContractScheduleManager $schMgr */
        $schMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntity());
        $value = false;

        if ( $scheduleKey === null && $scheduleEntryKey !== null ) {
            $entryEntity = $this->getScheduleEntryEntity();
            $scheduleEntryManager = Globals::$g->gManagerFactory->getManager($entryEntity);

            $schEntry = $scheduleEntryManager->get($scheduleEntryKey);
            if ( isset($schEntry) ) {
                $scheduleKey = $schEntry['SCHEDULEKEY'];
            }
        }

        $stateCount = $this->getScheduleEntriesCountByState($scheduleKey);
        if ( $stateCount !== null && $stateCount->getOpenEntriesCount() !== null && $stateCount->getOpenEntriesCount() > 0 ) {
            $value = true;
        }
        $ok = true;

        if ($value) {
            if ( $inProgress ) {
                $ok = $schMgr->setInProgress($scheduleKey);
            } else {
                $ok = $schMgr->setInDraft($scheduleKey);
            }
        }

        return $ok;
    }
    /**
     * Posts required transactions
     *
     * @param   int     $entryKey       Schedule entry key
     * @param   int     $postingDate    Posting date
     */
    abstract protected function postTransactions($entryKey, $postingDate=null);

    /**
     * Deletes posted transactions
     *
     * @param int $entryKey Schedule entry key
     */
    abstract protected function undoTransactions($entryKey);

    /**
     * @param   int    $entryKey    Schedule entry key
     *
     * @throws IAException
     */
    protected function validateUnpost($entryKey)
    {
        $entryValues = $this->getEntry($entryKey);
        ContractUtil::assert(!empty($entryValues), "Failed to get schedule entry for key: $entryKey");

        $prefs = $this->getContractLocationACPPref($entryValues['CONTRACTKEY']);
        if ($prefs) {
            $scheduledPostingDate = $entryValues['POSTINGDATE'];
            $goLiveDate = $prefs['ACPENT_GOLIVEDATE'];
            $acpConfigPpage = ContractACPRunManager::CONFIGURATION_PAGE_NAME;
            if (DateCompare($scheduledPostingDate, $goLiveDate) < 0) {
                ContractUtil::assert(isArrayValueTrue($entryValues, 'HISTORICAL'), "Schedule entry's posting date ($scheduledPostingDate) is prior to Go live date ($goLiveDate) but is not marked Historical ($entryKey)");
                if ($prefs['ACPENT_LOCKED']) {
                    $err = "Historical entries ($scheduledPostingDate) can't be cleared when the entity is locked ($entryKey). Unlock the entity in the {$acpConfigPpage} page and then try clearing the entry again.";
                    throw IAException::newIAException( 'CN-1500', $err, ['SCHEDULED_POSTING_DATE' => FormatDateForDisplay($scheduledPostingDate), 'ENTRY_KEY' => $entryKey, 'ACP_CONFIG_PPAGE' => $acpConfigPpage]);
                }
            } else {
                ContractUtil::assert(isArrayValueFalse($entryValues,'HISTORICAL'), "Schedule entry's posting date ($scheduledPostingDate) is on or after to Go live date ($goLiveDate) but is marked Historical ($entryKey)");
                if ( ! $prefs['ACPENT_LOCKED']) {
                    $err = "About to unpost non-Historical schedule entry ($scheduledPostingDate) while {$acpConfigPpage} is not Locked ($entryKey).  Please Lock the entity in {$acpConfigPpage}";
                    throw IAException::newIAException( 'CN-1501', $err, ['SCHEDULED_POSTING_DATE' => FormatDateForDisplay($scheduledPostingDate), 'ACP_CONFIG_PPAGE' => $acpConfigPpage, 'ENTRY_KEY' => $acpConfigPpage]);
                }
            }
        }
    }

    /**
     * Validations on new entry getting posted and lock is required so that parallel post request is blocked
     *
     * @param   int     $entryKey       Schedule entry key
     * @param   string  $postingDate    Actual posting date
     *
     * @throws IAException
     */
    public function validatePostAndLockRecord($entryKey, $postingDate)
    {
        ContractUtil::assert(Globals::$g->gTx->trxStarted(Globals::$g->conn));

        $selectLockQry = "SELECT * FROM ".$this->getScheduleEntryTable().
                            " WHERE record#=:1 AND cny#=:2 FOR UPDATE";
        $res = QueryResult([$selectLockQry, $entryKey, GetMyCompany()]);

        if ($res === false) {
            $msg = "Failed locking schedule entry key: $entryKey.";
            throw IAException::newIAException( 'CN-1502', $msg, ['ENTRY_KEY' => $entryKey]);
        }

        if (isEmptyArray($res)) {
            $msg = "Unable to update and lock schedule entry key: $entryKey. It is probably already deleted.";
            throw IAException::newIAException( 'CN-1503', $msg, ['ENTRY_KEY' => $entryKey]);
        }

        if ($res[0]['POSTED'] == 'T') {
            $msg = "Schedule entry key: $entryKey is already posted.";
            throw IAException::newIAException( ContractUtil::SCHEDULE_ALREADY_POSTED, $msg, ['ENTRY_KEY' =>$entryKey]);
        }

        if ($res[0]['STATE'] == 'T') {
            $msg = "Schedule entry key: $entryKey is terminated.";
            throw IAException::newIAException( 'CN-1505', $msg, ['ENTRY_KEY' => $entryKey]);
        }

        $entryValues = $this->getEntry($entryKey);
        ContractUtil::assert(!empty($entryValues), "Failed to get schedule entry for key: $entryKey");

        if ($entryValues['SCHEDULESTATE'] === ContractState::STATE_DRAFT) {
            $msg = "You can only post contract schedules for an \"In progress\" contract line. The contract line for which you are trying to post schedules is a \"Draft\" contract line.";
            throw new IAException($msg, 'CN-1506');
        }

        if (!empty($entryValues['CONTRACTDETAILKEY']) && !ContractUtil::lockContractDetail(
            $entryValues['CONTRACTDETAILKEY'], $entryValues['CONTRACTLINENO'], $entryValues['CONTRACTID'], false
        )) {
            $this->throwContractLineLockError($entryKey, $entryValues);
        }

        $prefs = $this->getContractLocationACPPref($entryValues['CONTRACTKEY']);
        if ($prefs) {
            $scheduledPostingDate = $entryValues['POSTINGDATE'];
            $goLiveDate = $prefs['ACPENT_GOLIVEDATE'];
            $acpObjectName = ContractACPRunManager::OBJECT_NAME;
            $acpConfigPage = ContractACPRunManager::CONFIGURATION_PAGE_NAME;
            $contractHandler = ContractHandler::getInstance();
            if (DateCompare($scheduledPostingDate, $goLiveDate) < 0) {
                ContractUtil::assert(isArrayValueTrue($entryValues, 'HISTORICAL'), "Schedule entry's posting date ($scheduledPostingDate) is prior to Go live date ($goLiveDate) but is not marked Historical ($entryKey)");
                ContractUtil::assert($contractHandler->isProcessingACP() || $contractHandler->isACPProcessingOverridden(), "Historical scehedule entry can only be posted through {$acpObjectName}");
                ContractUtil::assert($postingDate == $scheduledPostingDate, "Historical schedule entry posting date $scheduledPostingDate cannot be posted with a different posting date $postingDate ($entryKey)");

                if ($prefs['ACPENT_LOCKED']) {
                    $err = "You can't post one or more historical scheduled entries as contract schedules processing is locked. A user with Admin privileges can unlock the entity in $acpConfigPage.";
                    throw IAException::newIAException( 'CN-1507', $err, ['ACP_CONFIG_PAGE' => $acpConfigPage]);
                }
            } else {
                ContractUtil::assert(isArrayValueFalse($entryValues, 'HISTORICAL'), "Schedule entry's posting date ($scheduledPostingDate) is on or after to Go live date ($goLiveDate) but is marked Historical ($entryKey)");
                ContractUtil::assert(DateCompare($postingDate, $goLiveDate) >= 0, "Non-Historical schedule entry ($scheduledPostingDate) must be posted on or after Go live date $goLiveDate ($entryKey)");
                ContractUtil::assert(!$contractHandler->isProcessingACP(), "Non-Historical scehedule entry cannot be posted through {$acpObjectName}");

                if(!$prefs['ACPENT_LOCKED']) {
                    $err = "You can't generate invoices that post after the Go live date ($goLiveDate) until contract schedules processing is locked. A user with Admin privileges can set the lock in $acpConfigPage.";
                    throw IAException::newIAException( 'CN-1508', $err, ['GO_LIVE_DATE' => FormatDateForDisplay($goLiveDate), 'ACP_CONFIG_PAGE' => $acpConfigPage]);
                }
            }
        }

        $entryEntity = $this->getScheduleEntryEntity();
        $glPostingDate = $entryEntity == 'contractexpensescheduleentry' ? $entryValues['CONTRACTEXPENSEPOSTINGDATE'] : $entryValues['CNDETAILGLPOSTINGDATE'];

        if ($glPostingDate && DateCompare($postingDate, $glPostingDate) < 0) {
            $msg = "The GL posting date $postingDate must be on or after the contract line GL posting date $glPostingDate.";
            throw IAException::newIAException( 'CN-1509', $msg, ['POSTING_DATE' => FormatDateForDisplay($postingDate), 'GL_POSTING_DATE' => FormatDateForDisplay($glPostingDate)]);
        }

        // IA-116990 If feature ALLOW_CONTRACT_GL_POSTING_DATE_AFTER_CANCEL_DATE is enabled,
        // remove restriction to allow GL posting date to be after cancellation date.  This will allow
        // user to post open schedules in open period for a cancelled contract line whose cancel date is in close period
        if (!ContractUtil::isGLPostingDateAfterCancelDateAllowed()) {
            if ( $entryValues['CNDETAILCANCELDATE'] ) {
                if ( DateCompare($postingDate, $entryValues['CNDETAILCANCELDATE']) == 1 ) {
                    $errMsg = "The Contract line was cancelled on '" . $entryValues['CNDETAILCANCELDATE'] . "'. Posting date should be on or before Cancellation date. ";
                    throw IAException::newIAException( 'CN-1510', $errMsg, ['CNDETAILCANCELDATE' => FormatDateForDisplay($entryValues['CNDETAILCANCELDATE'])]);
                }
            } else if ( $entryValues['CNCANCELDATE'] ) {

                if ( DateCompare($postingDate, $entryValues['CNCANCELDATE']) == 1 ) {
                    $msg = "Posting date cannot be after contract cancellation date.";
                    throw new IAException($msg, 'CN-1511');
                }
            }
        }
    }

    /**
     * @param int $contractKey  Contract key
     *
     * @return null|string[]    ACP preferences of Contract's location
     */
    private function getContractLocationACPPref($contractKey)
    {
        if (!isset($this->contractMELocKey)) {
            $contractMgr = Globals::$g->gManagerFactory->getManager('contract');
            $res = $contractMgr->GetList([
                'selects' => [ 'MELOCATIONKEY' ],
                'filters' => [[[ 'RECORDNO', '=', $contractKey ]]],
                'usemst' => true,
            ]);
            $this->contractMELocKey = isset($res[0]) ? $res[0]['MELOCATIONKEY'] : null;
        }
        $cnSetup = Globals::$g->gManagerFactory->getManager('cnsetup');
        return $this->contractMELocKey == GetContextLocation() ?
            $cnSetup->getACPPreferences() : $cnSetup->getACPPreferencesForEntity($this->contractMELocKey);
    }

    /**
     * Marks schedule entry as 'Posted'
     *
     * @param   int     $entryKey       Schedule entry key
     * @param   string  $postingDate    Actual posting date
     *
     * @throws IAException
     */
    protected function markPosted($entryKey, $postingDate)
    {
        /* @var ContractScheduleEntryManager $entryMgr */
        $entryMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntryEntity());
        $ok = $entryMgr->markPosted($entryKey, $postingDate);

        if (in_array($this->getScheduleType(), [self::TYPE_REVENUE, self::TYPE_REVENUE2])) {
            $ok = $ok && ContractScheduleUtil::updateRevScheduleEntryPostedBaseAmount([$entryKey]);
        }
        if (!$ok) {
            $msg = "Could not update 'Posted' status (Schedule key:".$this->getKey() ." Schedule entry key:".$entryKey;
            throw IAException::newIAException( 'CN-1512', $msg, ['SCHEDULE_KEY' => $this->getKey(), 'ENTRY_KEY' => $entryKey]);
        }
    }

    /**
     * Reverts schedule entry to 'Open' again
     *
     * @param int $entryKey Schedule entry key
     *
     * @throws IAException
     */
    protected function undoPosted($entryKey)
    {
        /* @var ContractScheduleEntryManager $entryMgr */
        $entryMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntryEntity());
        $ok = $entryMgr->undoPosted($entryKey);

        if (in_array($this->getScheduleType(), [self::TYPE_REVENUE, self::TYPE_REVENUE2])) {
            $ok = $ok && ContractScheduleUtil::updateRevScheduleEntryPostedBaseAmount([$entryKey], 'UNPOSTED');
        }

        if (!$ok) {
            $msg = "Could not update 'Posted' status (Schedule key:".$this->getKey() ." Schedule entry key:".$entryKey;
            throw IAException::newIAException( 'CN-1512', $msg, ['SCHEDULE_KEY' => $this->getKey(), 'ENTRY_KEY' => $entryKey]);
        }
    }

    /**
     * Puts schedule on-hold
     *
     * @return bool
     * @throws IAException
     */
    public function setOnHold()
    {
        /* @var ContractScheduleManager $schMgr */
        $schMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntity());

        $ok = true;
        $ok = $ok && $schMgr->setOnHold($this->getKey());
        if (!$ok) {
            $msg = "Could not set on hold (schedule key:".$this->getKey().")";
            throw IAException::newIAException( 'CN-1513', $msg, ['SCHEDULE_KEY' => $this->getKey()]);
        }
        return $ok;
    }

    public function canResume()
    {
        if ( $this->getContractScheduleInfo()->getState() !== 'On hold' ) {
            $msg = "Contract schedule is not on hold";
            throw new IAException($msg, 'CN-1514');
        }
    }

    /**
     * implementation for resume functionality for Contract Schedule
     * @see IContractSchedule::resumeSchedule()
     *
     * @param string $resumeDate
     * @param bool   $postEntries
     * @param string $conversionDate if automatically posting entries, the date the schedule was converted to automatic
     * @param string $scheduleState  if revenue schedule, new state depends on delivery status and deferral status
     *
     * @return bool
     */
    public function resumeSchedule($resumeDate, $postEntries = true, $conversionDate = '', $scheduleState = ContractSchedule::STATE_INPROGRESS)
    {
        $ok = true;

        $scheduleStartDate = $this->getContractScheduleInfo()->getStartDate();
        if ( DateCompare( $resumeDate, $scheduleStartDate) < 0 ) {
            $msg = 'Resume date is before the schedule start date. The schedule cannot be resumed.';
            throw new IAException($msg, 'CN-1515');
        }

        // update schedule state appropriately
        $this->getContractScheduleInfo()->setState($scheduleState);

        // set the state in entries to in progress other than posted
        if ( $ok == true ) {
            /* @var ContractScheduleManager $schMgr */
            $schMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntity());

            $ok = $schMgr->setResume($this->getKey(), $scheduleState);
            $this->markCompleted();

            if (!$ok) {
                $msg = "Could not set resume on (schedule key: ". $this->getKey() .")";
                throw IAException::newIAException( 'CN-0772', $msg, ['KEY' => $this->getKey()]);
            }
        }

        if ($postEntries) {
            // if there are entries before the resume date then post them
            $scheduleEntries = $this->getContractScheduleInfo()->getEntries();
            foreach ( $scheduleEntries as $oneEntry ) {
                if ( !$oneEntry->getPosted() && !$oneEntry->isHistorical() ) {
                    $postingDate = $oneEntry->getPostingDate();
                    $pastDue = DateCompare($resumeDate, $postingDate) >= 0;
                    // if schedule was converted to automatic at some point, only auto-post those entries that occur after the conversion date
                    $isInAutomaticRange = $conversionDate != '' ? DateCompare($postingDate, $conversionDate) >= 0 : true;
                    if ($pastDue && $isInAutomaticRange >= 0) {
                        $ok = $ok && $this->post($oneEntry->getRecordNo(), $resumeDate);
                    }
                }
            }
        }

        return $ok;
    }
    
    /**
     * Returns entity associated with schedule
     *
     * @return string
     */
    abstract public function getScheduleEntity();

    /**
     * Returns entity associated with schedule entry
     *
     * @return string
     */
    abstract public function getScheduleEntryEntity();

    /**
     * Returns schedule key
     *
     * @return int
     */
    public function getKey()
    {
        return $this->key;
    }

    /**
     * Getter for ContractScheduleCreateParams passed in constructor
     *
     * @return ContractScheduleCreateParams
     */
    public function getCreateParams()
    {
        return $this->createParams;
    }

    /**
     * Returns schedule entry table
     *
     * @return string
     *
     * @throws Exception
     */
    public function getScheduleEntryTable()
    {
        $entryMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntryEntity());

        return $entryMgr->getTable();
    }
    /**
     * Returns cached entry record
     *
     * @param   int     $entryKey   Schedule entry key
     * @param   bool    $noCache    Fetch from DB if true
     *
     * @return array
     */
    public function getEntry($entryKey, $noCache=false)
    {
        if (!$this->entriesCache[$entryKey] || $noCache) {
            $entryEntity = $this->getScheduleEntryEntity();

            $glPostingDateField = $entryEntity == 'contractexpensescheduleentry' ? 'CONTRACTEXPENSEPOSTINGDATE' : 'CNDETAILGLPOSTINGDATE';

            $entry = EntityManager::GetListQuick(
                $entryEntity,
                [
                    $glPostingDateField, 'CNDETAILBEGINDATE', 'CNDETAILCANCELDATE', 'SCHEDULESTATE', 'POSTED', 'STATE',
                    'AMOUNT', 'BASEAMOUNT', 'EXCHANGE_RATE', 'POSTINGDATE', 'HISTORICAL', 'CONTRACTID', 'CONTRACTKEY',
                    'CONTRACTLINENO', 'CONTRACTDETAIL.REV_REC_ON_INVOICE', 'CONTRACTDETAILKEY', 'CONTRACTEXPENSEDETAILKEY'
                ],
                ['RECORDNO' => $entryKey]
            );
            $this->entriesCache[$entryKey] = $entry[0] ?? null;
        }

        return $this->entriesCache[$entryKey] ?? null;
    }

    /**
     * contractScheduleInfo
     * @return ContractScheduleInfo
     */
    public function getContractScheduleInfo()
    {
        return $this->contractScheduleInfo;
    }

    /**
     * contractScheduleInfo
     * @param ContractScheduleInfo $contractScheduleInfo
     */
    public function setContractScheduleInfo($contractScheduleInfo)
    {
        $this->contractScheduleInfo = $contractScheduleInfo;
    }
    
    /**
     * getSchduelType
     *
     * @return string
     */
    abstract protected function getScheduleType();

    /**
     * getScheduleEntryManager
     * 
     * @return ContractScheduleEntryManager
     */
    abstract protected function getScheduleEntryManager();

    /**
     * While cancelling a contract detail adjust the revenue schedule entry
     *
     * @param string                $terminationDate
     * @param ContractScheduleEntry $beforeEntry
     * @param ContractScheduleEntry $afterEntry
     */
    protected function addProratedScheduleEntry($terminationDate, $beforeEntry, $afterEntry)
    {
        // Do nothing by default
    }

    /**
     * Returns open amount as of date
     *
     * @param   string      $asOfDate       Open as of date
     * @param   float|null  $baseAmount     Base amount
     *
     * @return string
     */
    public function getOpenAmount($asOfDate, &$baseAmount = null)
    {
        $entryMgr = $this->getScheduleEntryManager();
        $openAmt = $entryMgr->getOpenAmount($this->key, $asOfDate, $baseAmount);

        return $openAmt;
    }

    /**
     * Returns toal amount as of date
     *
     * @return string
     */
    public function getTotalAmount()
    {
        $entryMgr = $this->getScheduleEntryManager();
        $openAmt = $entryMgr->getTotalAmount($this->key, $this->contractScheduleInfo->isRenewalForecast());
        return $openAmt;
    }

    /**
     * @return string
     */
    public function getTotalBaseAmount()
    {
        $entryMgr = $this->getScheduleEntryManager();
        $openAmt = $entryMgr->getTotalBaseAmount($this->key, $this->contractScheduleInfo->isRenewalForecast());
    
        return $openAmt;
    }
    
    /**
     * Amount to be kept open in schedule entries when terminating
     *
     * @param   float     $expectedOpenAmount
     */
    public function setExpectedOpenAmountOnTerminate($expectedOpenAmount)
    {
        $this->expectedOpenAmountOnTerminate = $expectedOpenAmount;
    }

    /**
     * Flag to indicate if all open schedule entries need to be deleted
     *
     * @param   bool  $terminateAllOpen
     */
    public function setTerminateAllOpen($terminateAllOpen)
    {
        $this->terminateAllOpen = $terminateAllOpen;
    }

    /**
     * terminate the schedule
     * 
     * @param string $terminationDate
     * 
     * @throws IAException
     */
    public function terminate($terminationDate)
    {
        $this->validateTerminate($terminationDate);

        $ok = $this->processtermination($terminationDate);

        $this->markTerminated($terminationDate);

        if (!$ok) {
            throw new IAException("Error terminating open schedule entries after cancellation date.", 'CN-1516');
        }
    }

    /**
     * @param string $terminationDate
     * @return bool
     * @throws IAException
     */
    private function processtermination($terminationDate){

        if ($this->expectedOpenAmountOnTerminate >= 0) {
            $this->handleExpectedOpenAfterTerminate($terminationDate);
        } else if ($this->supportsProrateOnTermination()) {
            //this section only applies to the expense schedule
            $this->prorateOnTermination($terminationDate);
        }

        $this->setLastUpdatedAt();

        /* @var ContractScheduleEntryManager $schEntryMgr */
        $schEntryMgr = $this->getScheduleEntryManager();

        if ($this->terminateAllOpen) {

            // Terminate all open
            $ok = $schEntryMgr->terminateOpen($this->key);

        } else {

            // Terminate all open on and after cancel date
            $ok = $schEntryMgr->terminateOpen($this->key, $terminationDate);
        }
        return $ok;
    }

    /**
     * Validates termination
     *
     * @param   string    $terminationDate  Termination date
     *
     * @throws IAException
     */
    protected function validateTerminate($terminationDate)
    {
        $entity = $this->getScheduleEntity();
        $sch = EntityManager::GetListQuick(
            $entity,
            ['STATE', 'CANCELDATE', 'CONTRACTKEY'],
            ['RECORDNO' => $this->getKey()]
        );

        if (!($sch && $sch[0])) {
            throw IAException::newIAException( 'CN-1517', "Invalid schedule key:".$this->getKey(), ['SCHEDULE_KEY' => $this->getKey()]);
        }

        if ($sch[0]['STATE'] == 'Terminated') {
            $msg = "Schedule (".$this->getKey().") is already cancelled on ".$sch[0]['CANCELDATE'];
            throw IAException::newIAException( 'CN-1518', $msg, ['SCHEDULE_KEY' => $this->getKey(), 'SCH_CANCELDATE' => FormatDateForDisplay($sch[0]['CANCELDATE'])]);
        }

        $prefs = $this->getContractLocationACPPref($sch[0]['CONTRACTKEY']);

        if ($prefs) {
            if (DateCompare($terminationDate, $prefs['ACPENT_GOLIVEDATE']) < 0 && $prefs['ACPENT_LOCKED']) {
                $msg = "Cannot cancel prior to Go live date when the entity is already Locked. Go live date is {$prefs['ACPENT_GOLIVEDATE']} and cancelation date is $terminationDate";
                throw IAException::newIAException( 'CN-1519', $msg, ['PREFS_ACPENT_GOLIVEDATE' => FormatDateForDisplay($prefs['ACPENT_GOLIVEDATE']), 'TERMINATION_DATE' => FormatDateForDisplay($terminationDate)]);
            }
        }
    }


    /**
     * Logic to handle expected open amount after termination
     *
     * @param   string    $terminationDate    Termination date
     */
    protected function handleExpectedOpenAfterTerminate($terminationDate)
    {
        // Nothing by default
    }

    /**
     * Checks of terminated schedule entry amount needs to be prorated on termination
     *
     * @return bool
     */
    protected function supportsProrateOnTermination()
    {
        return false;
    }

    /**
     * Logic to prorate amount when termination date is in the middle of period
     *
     * @param   string    $terminationDate    Termination date
     *
     * @throws IAException
     */
    protected function prorateOnTermination($terminationDate)
    {
        // Find all entries from last entry before cancel date
        $schInfo = $this->getContractScheduleInfo();
        $entries = $schInfo->getEntries();

        $proratePeriodStartDate = $schInfo->getStartDate();
        $proratePeriodEndDate = null;
        $cancellationPeriodEntry = null;

        // If cancellation date is before schedule start date, no proration is required
        if (DateCompare($terminationDate, $proratePeriodStartDate) == -1) {
            return;
        }

        foreach ($entries as $entry) {

            if (DateCompare($terminationDate, $entry->getPostingDate()) == 1) {
                $proratePeriodStartDate = $entry->getPostingDate();
                $proratePeriodStartDate = AddDays($proratePeriodStartDate, 1);
                continue;
            }

            $proratePeriodEndDate = $entry->getPostingDate();
            $cancellationPeriodEntry = $entry;
            break;
        }

        // No prorate required, terminate all schedule entries on and after cancel date
        if (DateCompare($proratePeriodStartDate, $proratePeriodEndDate) == 0) {
            return;
        }

        if (DateCompare($proratePeriodStartDate, $terminationDate) == 0) {
            return;
        }


        // If not found no balance adjustment is required
        if ($proratePeriodEndDate == null) {
            // Termination date is after last posting date
            // Nothing to terminate

            return;
        }

        // If last entry is posted, no need to adjust schedule entries
        // Just use the amount to reverse - Re-class will do this
        if ($cancellationPeriodEntry !== null && !$cancellationPeriodEntry->getPosted()) {
            $this->prorateEntry(
                $cancellationPeriodEntry,
                $proratePeriodStartDate,
                $proratePeriodEndDate,
                $terminationDate
            );
        }
    }

    /**
     * Prorates un-posted period
     *
     * @param   ContractScheduleEntry   $cancellationPeriodEntry    Cancellation period schedule entry info
     * @param   string                  $proratePeriodStartDate     Period start date
     * @param   string                  $proratePeriodEndDate       Period end date
     * @param   string                  $terminationDate            Termination date
     *
     * @throws IAException
     */
    protected function prorateEntry($cancellationPeriodEntry, $proratePeriodStartDate, $proratePeriodEndDate,
                                    $terminationDate)
    {
        $proratePeriodAmount = $cancellationPeriodEntry->getAmount();
        $exchRate = $cancellationPeriodEntry->getExchangeRate() ?: 1;

        // Calculate prorated amount for cancellation period
        $noOfDays = DateDiff($proratePeriodEndDate, $proratePeriodStartDate);

        // Including start date
        $noOfDays += 1;

        // Excluding termination date
        $prorateDays = DateDiff($terminationDate, $proratePeriodStartDate);
        $prorateAmount1 = ContractUtil::computeAmountRatio($proratePeriodAmount, $prorateDays, $noOfDays);

        $prorateAmount2 = ibcsub($proratePeriodAmount, $prorateAmount1, ContractUtil::AMOUNT_PRECISION, true);

        // Total period amount should not change. If any difference is there add to $prorateAmount2
        $prorateAmount2 += $proratePeriodAmount - ($prorateAmount1 + $prorateAmount2);

        $baseProrateAmount1 = ibcmul($prorateAmount1, $exchRate, ContractUtil::AMOUNT_PRECISION, true);
        $baseProrateAmount2 = ibcmul($prorateAmount2, $exchRate, ContractUtil::AMOUNT_PRECISION, true);

        /* @var ContractScheduleEntryManager $schEntryMgr */
        $schEntryMgr = $this->getScheduleEntryManager();

        // Schedule entry cannot be open on cancellation date
        $newPostingDate = AddDays($terminationDate, -1);

        // If last entry is open
        // 1. Create new entry equal to prorated amount, posting date as cancellation date
        $newEntry1 = [
            'SCHEDULEKEY'       => $this->key,
            'TYPE'              => $this->getType(),
            'SCHOPKEY'          => '', // TODO : For automatic type?
            'AMOUNT'            => $prorateAmount1,
            'BASEAMOUNT'        => $baseProrateAmount1,
            'EXCHANGE_RATE'     => $exchRate,
            'POSTINGDATE'       => $newPostingDate,
            'STATE'             => 'Open',
        ];

        $ok = $schEntryMgr->add($newEntry1);
        if (!$ok) {
            $msg = "Error creating new open revenue schedule entry for amount ". $prorateAmount1;
            throw IAException::newIAException( 'CN-1520', $msg, ['PRORATE_AMOUNT' => $prorateAmount1]);
        }

        // 2. Create 2nd new entry for the remaining amount, posting date as the period end date
        $newEntry2 = [
            'SCHEDULEKEY'       => $this->key,
            'TYPE'              => $this->getType(),
            'SCHOPKEY'          => '',
            'AMOUNT'            => $prorateAmount2,
            'BASEAMOUNT'        => $baseProrateAmount2,
            'EXCHANGE_RATE'     => $exchRate,
            'POSTINGDATE'       => $proratePeriodEndDate,
            'STATE'             => 'Open',
        ];

        $ok = $schEntryMgr->add($newEntry2);
        if (!$ok) {
            $msg = "Error creating new terminated revenue schedule entry for amount ". $prorateAmount2;
            throw IAException::newIAException( 'CN-1521', $msg, ['PRORATE_AMOUNT' => $prorateAmount2]);
        }

        // Delete original entry
        $ok = $schEntryMgr->Delete($cancellationPeriodEntry->getRecordNo());
        if (!$ok) {
            $msg = "Error deleting prorated schedule entry key: ". $cancellationPeriodEntry->getRecordNo();
            throw IAException::newIAException( 'CN-1522', $msg, ['CANCELLATIONPERIODENTRY_GETRECORDNO' => $cancellationPeriodEntry->getRecordNo()]);
        }
    }
    
    public function setLastUpdatedAt()
    {
        /* @var ContractScheduleManager $schMgr */
        $schMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntity());
        $ok = $schMgr->setLastUpdatedAt($this->key);
        if (!$ok) {
            throw new IAException( "Not able to update this schedule. The record is modified by someone else.", 'CN-1523');
        }
    }

    /**
     * @param int $scheduleKey
     *
     * @return ContractScheduleEntryCounts|null
     */
    public function getScheduleEntriesCountByState( $scheduleKey ) {
        $stateCounts = null;
        $entryEntity = $this->getScheduleEntryEntity();
        /* @var ContractScheduleEntryManager $scheduleEntryManager */
        $scheduleEntryManager = Globals::$g->gManagerFactory->getManager($entryEntity);
        $filterForSchedules = [[
            ['SCHEDULEKEY', '=', $scheduleKey],
            ['POSTABLE', '=' , 'false']
        ]];
        $params = [
        'filters' => $filterForSchedules
        ];

        if ( $scheduleEntryManager != null ) {
            $entriesSummary = $scheduleEntryManager->getEntriesCountByState($scheduleKey);
            if ( count($entriesSummary) > 0 ) {
                $stateCounts = new ContractScheduleEntryCounts();
                $nonPostableEntries = $scheduleEntryManager->GetList($params);
                if ( count($nonPostableEntries) > 0 ) {
                    $stateCounts->setHasNonPostable(true);
                }
                ///** @noinspection PhpUnusedLocalVariableInspection */
                //$totalCount = $entriesSummary['TOTAL'];
                
                $stateCounts->setTotalEntriesCount($entriesSummary['TOTAL']);
                if ( isset($entriesSummary['T']) ) {
                    $terminatedCount = $entriesSummary['T']['CT'];
                    $terminatedSum = $entriesSummary['T']['AMT'];
                    $terminatedBaseSum = $entriesSummary['T']['BASEAMT'];
                    $stateCounts->setTerminatedEntriesSum($terminatedSum);
                    $stateCounts->setTerminatedEntriesBaseSum($terminatedBaseSum);
                    $stateCounts->setTerminatedEntriesCount($terminatedCount);
                }
                if ( isset($entriesSummary['O']) ) {
                    $openCount = $entriesSummary['O']['CT'];
                    $openSum = $entriesSummary['O']['AMT'];
                    $openBaseSum = $entriesSummary['O']['BASEAMT'];
                    $stateCounts->setOpenEntriesSum($openSum);
                    $stateCounts->setOpenEntriesBaseSum($openBaseSum);
                    $stateCounts->setOpenEntriesCount($openCount);
                }
                if ( isset($entriesSummary['P'] ) ) {
                    $postedCount = $entriesSummary['P']['CT'];
                    $postedSum = $entriesSummary['P']['AMT'];
                    $postedBaseSum = $entriesSummary['P']['BASEAMT'];
                    $stateCounts->setPostedEntriesSum($postedSum);
                    $stateCounts->setPostedEntriesBaseSum($postedBaseSum);
                    $stateCounts->setPostedEntriesCount($postedCount);
                }
                if ( isset($entriesSummary['H'] ) ) {
                    $onHoldCount = $entriesSummary['H']['CT'];
                    $onHoldSum = $entriesSummary['H']['AMT'];
                    $onHoldBaseSum = $entriesSummary['H']['BASEAMT'];
                    $stateCounts->setOnHoldEntriesSum($onHoldSum);
                    $stateCounts->setOnHoldEntriesBaseSum($onHoldBaseSum);
                    $stateCounts->setOnHoldEntriesCount($onHoldCount);
                }
            }
        }
        return $stateCounts;
    }
    
    /**
     * Verify schedule is in 'On Hold' state
     * 
     * @return bool
     */
    function isScheduleOnHold()
    {
        $ok = false;
        if ( $this->getKey() == null ) {
            throw new IAException("Not able to get the state of the schedule.", 'CN-1524');
        }
        $filterForSchedules = [[
            ['RECORDNO', '=', $this->getKey()]
        ]];
        $params = [
            'selects' => array('STATE'),
            'filters' => $filterForSchedules
        ];
        $schMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntity());
        $schRec = $schMgr->GetList($params);
        if ( $schRec[0]['STATE'] == 'On hold' ) {
            $ok = true;
        }

        return $ok;
    }

    /**
     * Verify schedule is in 'On Hold' state
     *
     * @return bool
     */
    public function isScheduleCompleted()
    {
        $ok = false;
        $scheduleKey = $this->getKey();
        if ( $scheduleKey == null ) {
            throw new IAException("Not able to get the state of the schedule.", 'CN-1524');
        }
        $filterForSchedules = [[['RECORDNO', '=', $scheduleKey ]]];
        $params = [
            'selects' => array('STATE'),
            'filters' => $filterForSchedules
        ];
        $schMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntity());
        $schRec = $schMgr->GetList($params);
        if ( $schRec[0]['STATE'] == ContractScheduleState::STATE_COMPLETED ) {
            $ok = true;
        }

        return $ok;
    }

    /**
     * @return int
     */
    public function getTrTypeMultiplier()
    {
        return $this->trTypeMultiplier;
    }

    /**
     * @param int $trTypeMultiplier
     */
    public function setTrTypeMultiplier($trTypeMultiplier)
    {
        $this->trTypeMultiplier = $trTypeMultiplier;
    }

    /**
     * @param string $startDate
     * @param string $endDate
     * @param bool   $checkAdjustment
     * @param string $meaEffectiveDate
     *
     * @return bool
     */
    public function canReAllocate($startDate, $endDate, $checkAdjustment, /** @noinspection PhpUnusedParameterInspection */$meaEffectiveDate)
    {
        if ( $startDate == null || $startDate == '' ) {
            throw new IAException("Start date is a required field.", 'CN-1525');
        }
        if ( $endDate == null || $endDate == '' ) {
            throw new IAException("End date is a required field.", 'CN-1526');
        }

        $scheduleInfo = $this->getContractScheduleInfo();
        $lastPostedDate = $scheduleInfo->getLastPostedEntryDate();
        if ( DateCompare($startDate,$lastPostedDate) <= 0 ) {
            $msg = "The reallocation Start date must be after the last Scheduled posting date $lastPostedDate.";
            throw IAException::newIAException( 'CN-1527', $msg, ['LAST_POSTED_DATE' => FormatDateForDisplay($lastPostedDate)]);
        }

        if ($scheduleInfo->getState() === ContractScheduleState::STATE_TERMINATED ) {
            //$lastEntryDate = $this->getContractScheduleInfo()->getLastEntryDate();
            $cancellationDate = $scheduleInfo->getCancelDate();

            if ( DateCompare($endDate,$cancellationDate) > 0 ) {
                throw new IAException("Reallocation end date cannot be after the cancel date for a terminated schedule.", 'CN-1528');
            }
        }

        $checkAdjustment = $checkAdjustment ?? true;
        if ($checkAdjustment) {
            $entries = $scheduleInfo->getEntries();
            $firstSign = null;
            foreach ($entries as $entry) {
                if ($entry->getAmount() != 0) {
                    if ($firstSign === null) {
                        $firstSign = ($entry->getAmount() < 0) ? -1 : 1;
                    } else {
                        $sign = ($entry->getAmount() < 0) ? -1 : 1;
                        if ($sign != $firstSign) {
                            if (!$entry->isPosted()) {
                                $msg = sprintf("This revenue schedule has an unposted one-time adjustment dated %s. Post the one-time adjustment and then try reallocating the schedule again.", $entry->getPostingDate());
                                throw IAException::newIAException('CN-1529',$msg, ['POSTINGDATE' => FormatDateForDisplay($entry->getPostingDate())]);
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     *
     */
    public function postPastDatedEntries()
    {
        $scheduleEntries = $this->getContractScheduleInfo()->getEntries();
        $currentDate = GetCurrentDate();
        foreach ( $scheduleEntries as $oneEntry ) {
            if ( $oneEntry->isPosted() === false ) {
                if ( $oneEntry->getStateChanged() == null || $oneEntry->getStateChanged() !== ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_DELETED ) {
                    $postingDate = $oneEntry->getPostingDate();
                    if ( DateCompare($postingDate,$currentDate) < 0 ) {
                        $this->post($oneEntry->getRecordNo(),$postingDate);
                    }
                }
            }
        }
    }

    /**
     * @param int   $scheduleKey
     *
     * @return bool
     */
    public function convertBalancingEntryToOpenEntry($scheduleKey)
    {
        $updateQry = "update contractscheduleentry set postable = 'T' where cny# = :1 and schedulekey = :2 and postable = 'F'";
        $ok = ExecStmt(array($updateQry, GetMyCompany(), $scheduleKey));
        return $ok;
    }

    /**
     * @param int   $scheduleKey
     *
     * @return bool
     */
    public function convertOpenEntryToBalancingEntry($scheduleKey)
    {
        $updateQry = "update contractscheduleentry set postable = 'F' where cny# = :1 and schedulekey = :2 and approvedhours is null";
        $ok = ExecStmt(array($updateQry, GetMyCompany(), $scheduleKey));
        return $ok;
    }
    /**
     * complete the schedule
     *
     * @param string $completeDate
     *
     * @throws IAException
     */
    public function estimateRevaluedToZero($completeDate)
    {

        $this->validateRevaluedEstimate();
        try {
            $ok = $this->processtermination($completeDate);
            if($this->getType() == self::TYPE_BILLING ){
                $schEntryMgr = $this->getScheduleEntryManager(); // as common function preocesstermination does not terminating non postable schedule entry
                // but in case of project time revaluation we need to terminate non postable schedule entry also
                $ok = $ok && $schEntryMgr->terminateNonPostable($this->getKey());
            }
            if(!$ok){
                throw new IAException('Unable to revalued schedule entry', 'CN-1530');
            }
            $this->markEstimateCompleted($completeDate);
        }catch (Exception $ex){
            throw new IAException( $ex->getMessage());

        }
    }
    /**
     * Validates completation
     *
     *
     * @throws IAException
     */
    protected function validateRevaluedEstimate()
    {
        $entity = $this->getScheduleEntity();
        $sch = EntityManager::GetListQuick(
            $entity,
            ['STATE', 'COMPLETEDATE'],
            ['RECORDNO' => $this->getKey()]
        );
        if (!($sch && $sch[0])) {
            throw IAException::newIAException( 'CN-1517', "Invalid schedule key:".$this->getKey(), ['SCHEDULE_KEY' => $this->getKey()]);
        }
        if (!empty($sch[0]['COMPLETEDATE'])) {
            $msg = "Schedule (".$this->getKey().") is already estimate revalued on ".$sch[0]['COMPLETEDATE'];
            throw IAException::newIAException( 'CN-1531', $msg, ['SCHEDULE_KEY' => $this->getKey(), 'COMPLETEDATE' => FormatDateForDisplay($sch[0]['COMPLETEDATE'])]);
        }
    }

    public function postAllEntries()
    {
        // TODO: Implement postAllEntries() method.
        //NO OP functions
    }

    /**
     * @param array $entryValues
     * @param int $entryKey
     * @return mixed
     * @throws IAException
     */
    private function throwContractLineLockError(int $entryKey, array $entryValues)
    {

        $cndLineNumber = $entryValues['CONTRACTLINENO'];
        $cndKey = $entryValues['CONTRACTDETAILKEY'];
        $cnId = $entryValues['CONTRACTID'];

        $msg = "Update of Schedule entry $entryKey failed for contract line no $cndLineNumber ( key: $cndKey )"
            . " of contract $cnId.  One or more records is temporarily locked. Please try again after some time.";

        ContractAssertionsUtil::handleMetrics($msg, false, $entryValues);

        throw IAException::newIAException('CN-1841', $msg, [
            'ENTRYKEY' => $entryKey,
            'CONTRACTLINENO' => $cndLineNumber,
            'CONTRACTDETAILKEY' => $cndKey,
            'CONTRACTID' => $cnId
        ]);
    }
}

class ContractScheduleEntryCounts
{
    /* @var int $totalEntriesCount */
    private $totalEntriesCount;
    /* @var int $openEntriesCount */
    private $openEntriesCount;
    /* @var mixed $openEntriesSum */
    private $openEntriesSum;
    /* @var mixed $openEntriesBaseSum */
    private $openEntriesBaseSum;
    /* @var int $terminatedEntriesCount */
    private $terminatedEntriesCount;
    /* @var mixed $terminatedEntriesSum */
    private $terminatedEntriesSum;
    /* @var mixed $terminatedEntriesBaseSum */
    private $terminatedEntriesBaseSum;
    /* @var int $postedEntriesCount */
    private $postedEntriesCount;
    /* @var mixed $postedEntriesSum */
    private $postedEntriesSum;
    /* @var mixed $postedEntriesBaseSum */
    private $postedEntriesBaseSum;
    /* @var bool $hasNonPostable */
    private $hasNonPostable = false;
    /* @var int $onHoldEntriesCount */
    private $onHoldEntriesCount;
    /* @var mixed $onHoldEntriesSum */
    private $onHoldEntriesSum;
    /* @var mixed $onHoldEntriesBaseSum */
    private $onHoldEntriesBaseSum;

    /**
     * totalEntriesCount
     * @return int
     */
    public function getTotalEntriesCount()
    {
        return $this->totalEntriesCount;
    }

    /**
     * totalEntriesCount
     * @param int $totalEntriesCount
     */
    public function setTotalEntriesCount($totalEntriesCount)
    {
        $this->totalEntriesCount = $totalEntriesCount;
    }

    /**
     * openEntriesCount
     * @return int
     */
    public function getOpenEntriesCount()
    {
        return $this->openEntriesCount;
    }

    /**
     * openEntriesCount
     * @param int $openEntriesCount
     */
    public function setOpenEntriesCount($openEntriesCount)
    {
        $this->openEntriesCount = $openEntriesCount;
    }

    /**
     * terminateEntriesCount
     * @return int
     */
    public function getTerminatedEntriesCount()
    {
        return $this->terminatedEntriesCount;
    }

    /**
     * terminateEntriesCount
     * @param int $terminatedEntriesCount
     */
    public function setTerminatedEntriesCount($terminatedEntriesCount)
    {
        $this->terminatedEntriesCount = $terminatedEntriesCount;
    }

    /**
     * postedEntriesCount
     * @return int
     */
    public function getPostedEntriesCount()
    {
        return $this->postedEntriesCount;
    }

    /**
     * postedEntriesCount
     * @param int $postedEntriesCount
     */
    public function setPostedEntriesCount($postedEntriesCount)
    {
        $this->postedEntriesCount = $postedEntriesCount;
    }


    /**
     * hasNonPostable
     *
     * @return bool
     */
    public function getHasNonPostable()
    {
        return $this->hasNonPostable;
    }

    /**
     * hasNonPostable
     *
     * @param bool $hasNonPostable
     */
    public function setHasNonPostable($hasNonPostable)
    {
        $this->hasNonPostable = $hasNonPostable;
    }

    /**
     * @return mixed
     */
    public function getOpenEntriesSum()
    {
        return $this->openEntriesSum;
    }

    /**
     * @param mixed $openEntriesSum
     */
    public function setOpenEntriesSum($openEntriesSum)
    {
        $this->openEntriesSum = $openEntriesSum;
    }

    /**
     * @return mixed
     */
    public function getPostedEntriesSum()
    {
        return $this->postedEntriesSum;
    }

    /**
     * @param mixed $postedEntriesSum
     */
    public function setPostedEntriesSum($postedEntriesSum)
    {
        $this->postedEntriesSum = $postedEntriesSum;
    }

    /**
     * @return mixed
     */
    public function getOpenEntriesBaseSum()
    {
        return $this->openEntriesBaseSum;
    }

    /**
     * @param mixed $openEntriesBaseSum
     */
    public function setOpenEntriesBaseSum($openEntriesBaseSum)
    {
        $this->openEntriesBaseSum = $openEntriesBaseSum;
    }

    /**
     * @return mixed
     */
    public function getTerminatedEntriesSum()
    {
        return $this->terminatedEntriesSum;
    }

    /**
     * @param mixed $terminatedEntriesSum
     */
    public function setTerminatedEntriesSum($terminatedEntriesSum)
    {
        $this->terminatedEntriesSum = $terminatedEntriesSum;
    }

    /**
     * @return mixed
     */
    public function getTerminatedEntriesBaseSum()
    {
        return $this->terminatedEntriesBaseSum;
    }

    /**
     * @param mixed $terminatedEntriesBaseSum
     */
    public function setTerminatedEntriesBaseSum($terminatedEntriesBaseSum)
    {
        $this->terminatedEntriesBaseSum = $terminatedEntriesBaseSum;
    }

    /**
     * @return mixed
     */
    public function getPostedEntriesBaseSum()
    {
        return $this->postedEntriesBaseSum;
    }

    /**
     * @param mixed $postedEntriesBaseSum
     */
    public function setPostedEntriesBaseSum($postedEntriesBaseSum)
    {
        $this->postedEntriesBaseSum = $postedEntriesBaseSum;
    }

    /**
     * @return int
     */
    public function getOnHoldEntriesCount()
    {
        return $this->onHoldEntriesCount;
    }

    /**
     * @param int $onHoldEntriesCount
     */
    public function setOnHoldEntriesCount(int $onHoldEntriesCount)
    {
        $this->onHoldEntriesCount = $onHoldEntriesCount;
    }

    /**
     * @return mixed
     */
    public function getOnHoldEntriesSum()
    {
        return $this->onHoldEntriesSum;
    }

    /**
     * @param mixed $onHoldEntriesSum
     */
    public function setOnHoldEntriesSum(mixed $onHoldEntriesSum)
    {
        $this->onHoldEntriesSum = $onHoldEntriesSum;
    }

    /**
     * @return mixed
     */
    public function getOnHoldEntriesBaseSum()
    {
        return $this->onHoldEntriesBaseSum;
    }

    /**
     * @param mixed $onHoldEntriesBaseSum
     */
    public function setOnHoldEntriesBaseSum(mixed $onHoldEntriesBaseSum)
    {
        $this->onHoldEntriesBaseSum = $onHoldEntriesBaseSum;
    }
}