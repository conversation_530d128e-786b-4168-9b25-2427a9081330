<?php
/**
 *    FILE: ContractScheduleEntry.cls
 *    AUTHOR: <PERSON>
 *    DESCRIPTION:
 *
 *    (C) 2016, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

require_once "ContractUtil.cls";

class ContractScheduleEntry extends BaseObject implements IRoundingErrorEntry, IRoundingBaseAmountErrorEntry
{
    const CONTRACT_SCHEDULE_ENTRY_ADJUSTED = 'ADJUSTED';
    const CONTRACT_SCHEDULE_ENTRY_ADJUSTED_NO_UNPOST = 'ADJUSTED_NO_UNPOST';
    const CONTRACT_SCHEDULE_ENTRY_ADJUSTED_POST = 'ADJUSTED_POST';
    const CONTRACT_SCHEDULE_ENTRY_NEW = 'NEW';
    const CONTRACT_SCHEDULE_ENTRY_DELETED = 'DELETED';
    const CONTRACT_SCHEDULE_ENTRY_DELETED_NO_UNPOST = 'DELETED_NO_UNPOST';
    const CONTRACT_SCHEDULE_ENTRY_NO_ACTION = 'NO_ACTION';
    const CONTRACT_SCHEDULE_ENTRY_CREATE_AND_POST = 'CREATE_AND_POST';

    /* @var int $scheduleEntryKey */
    private $scheduleEntryKey;
    /* @var int $scheduleKey */
    private $scheduleKey;
    /* @var int $scheduleKey */
    private $schopKey;
    /* @var float $period */
    private $period;
    /* @var float $amount */
    private $amount;
    /* @var float $baseAmount */
    private $baseAmount;
    /* @var float $postedBaseAmount */
    private $postedBaseAmount;
    /* @var float $exchRate */
    private $exchRate;
    /* @var string $postingDate*/
    private $postingDate;
    /* @var string $actualPostingDate */
    private $actualPostingDate;
    /* @var string $derivedPostingDate */
    private $derivedPostingDate;
    /* @var bool $posted */
    private $posted = false;
    /* @var int $glBatchKey */
    private $glBatchKey;
    /* @var string $computationMemo */
    private $computationMemo;
    /* @var string $computationDetails */
    private $computationDetail;
    //this flag indicates that the value is adjusted and needs to be updated in the database
    /* @var bool $stateChanged */
    private $stateChanged;
    /* @var string $previousPostingDate */
    private $previousPostingDate;
    /* @var bool $postable */
    private $postable = true;
    /* @var string $state */
    private $state;
    /* @var string $isAdjusted */
    private $isAdjusted;
    /* @var string $type */
    private $type;
    /* @var int $billScheduleEntryKey */
    private $billScheduleEntryKey;  // For Payment, this is the billing entry key.  For Negative Billing entry, this is the positive billing entry key
    /* @var int $oriPmtScheduleEntryKey */
    private $oriPmtScheduleEntryKey;   // For Payment reversal only, this is the original pmt schedule entry key
    // TODO: Move these to a subclass of ContractScheduleEntry in ContractMEARevenueScheduleResolveHandler
    /* @var bool $movedFromOtherLine */
    private $movedFromOtherLine = false;    // This is only used during MEA reallocation by ContractMEARevenueScheduleResolveHandler
    /* @var SchedulesLinkGroup $linkGroup */
    private $linkGroup = null;    // This is only used during MEA reallocation by ContractMEARevenueScheduleResolveHandler
    /* @var SchedulesLinkGroup $linkGroup */
    private $oriLinkGroup = null;    // This is only used during MEA reallocation by ContractMEARevenueScheduleResolveHandler
    /* @var ContractScheduleEntry[] $negativeEntries */
    private $negativeEntries = null;    // This is only used during MEA reallocation by ContractMEARevenueScheduleResolveHandler
    /* @var ContractScheduleEntry[] $pmtEntries */
    private $pmtEntries = null;    // This is only used during MEA reallocation by ContractMEARevenueScheduleResolveHandler
    /* @var float $percentComplete */
    private $percentComplete = null;
    /* @var float $percentRecognized */
    private $percentRecognized = null;
    /* @var float $approvedHours */
    private $approvedHours;
    /* @var string|float $sourceQty */
    private $sourceQty;
    /** @var float $postedExchangeRate */
    private $postedExchangeRate;
    /** @var bool $historical */
    private $historical = false;
    /** @var int $paymentPREntryKey */
    private $paymentPREntryKey;
    /** @var int $paymentPRRecordKey */
    private $paymentPRRecordKey;
    /** @var string|null $servicePeriodStartDate */
    private $servicePeriodStartDate;
    /** @var string|null $servicePeriodEndDate */
    private $servicePeriodEndDate;

    /**
     * @var bool $deferredPosting
     */
    private $deferredPosting =  false;

    /**
     * @var string
     */
    private $postingDatedForDeferredPosting = null;
    
    /**
     * @var int $usageKey
     */
    private $usageKey = null;

    /**
     * @var float|null
     */
    private ?float $derivedAmount;

    /**
     * @param null|float $amount
     * @param null|float $baseAmount
     * @param null|float $exchangeRate
     * @param null|string $postingDate
     */
    public function __construct( $amount=null, $baseAmount=null, $exchangeRate=null, $postingDate=null )
    {
        $this->amount = $amount;
        $this->baseAmount = $baseAmount;
        $this->exchRate = $exchangeRate;
        $this->postingDate = $postingDate;
        $this->derivedPostingDate = $postingDate;
        $this->period = 1;
        $this->state = 'Open';
        $this->derivedAmount = $amount;
    }

    /**
     * @return float|null
     */
    public function getDerivedAmount(): ?float
    {
        return $this->derivedAmount;
    }



    /**
     * @param float|null $derivedAmount
     */
    public function setDerivedAmount(?float $derivedAmount): void
    {
        $this->derivedAmount = $derivedAmount;
    }



    /**
     * @param ContractScheduleEntry $a
     * @param ContractScheduleEntry $b
     *
     * @return int
     */
    public static function comparePostingDate($a, $b)
    {
        $retVal = DateDiff($a->getPostingDate(), $b->getPostingDate());
        if ($retVal == 0 && $a->getType() == ContractSchedule::TYPE_PAYMENT) {
            $retVal = $a->getBillScheduleEntryKey() - $b->getBillScheduleEntryKey();
        }
        if ($retVal == 0) {
            $retVal = $a->getRecordNo() - $b->getRecordNo();
        }
        return $retVal;
    }

    /**
     * @param ContractScheduleEntry $a
     * @param ContractScheduleEntry $b
     *
     * @return int
     */
    public static function compareOriginalPostingDate($a, $b)
    {
        $retVal = DateDiff($a->getOriginalPostingDate(), $b->getOriginalPostingDate());
        if ($retVal == 0 && $a->getType() == ContractSchedule::TYPE_PAYMENT) {
            $retVal = $a->getBillScheduleEntryKey() - $b->getBillScheduleEntryKey();
        }
        if ($retVal == 0) {
            $retVal = $a->getRecordNo() - $b->getRecordNo();
        }
        return $retVal;
    }

    /**
     * scheduleEntryKey
     * @return int
     */
    public function getScheduleEntryKey()
    {
        return $this->scheduleEntryKey ?? $this->getRecordNo();
    }

    /**
     * scheduleEntryKey
     * @param int|null $scheduleEntryKey
     */
    public function setScheduleEntryKey($scheduleEntryKey)
    {
        $this->scheduleEntryKey = $scheduleEntryKey;
        
    }

    /**
     * scheduleKey
     * @return int
     */
    public function getScheduleKey()
    {
        return $this->scheduleKey;
    }

    /**
     * scheduleKey
     * @param int $scheduleKey
     */
    public function setScheduleKey($scheduleKey)
    {
        $this->scheduleKey = $scheduleKey;
        
    }

    /**
     * schopKey
     * @return int
     */
    public function getSchopKey()
    {
        return $this->schopKey;
    }

    /**
     * schopKey
     * @param int $schopKey
     */
    public function setSchopKey($schopKey)
    {
        $this->schopKey = $schopKey;
    }

    /**
     * amount
     * @return float
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * amount
     * @param float $amount
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;
        
    }

    /**
     * postingDate
     * @return string
     */
    public function getPostingDate()
    {
        return $this->postingDate;
    }

    /**
     * postingDate
     * @param string $postingDate
     */
    public function setPostingDate($postingDate)
    {
        $this->postingDate = $postingDate;
    }
    
    /**
     * postingDate
     * @return string
     */
    public function getOriginalPostingDate()
    {
        return $this->derivedPostingDate ?? $this->postingDate;
    }

    /**
     * actualPostingDate
     * @return string
     */
    public function getActualPostingDate()
    {
        return $this->actualPostingDate;
    }

    /**
     * actualPostingDate
     * @param string|null $actualPostingDate
     */
    public function setActualPostingDate($actualPostingDate)
    {
        $this->actualPostingDate = $actualPostingDate;
        
    }

    /**
     * @return bool
     */
    public function getPosted()
    {
        assert(is_bool($this->posted));
        return $this->posted;
    }

    /**
     * posted
     *
     * @param bool|string $posted
     */
    public function setPosted($posted)
    {
        if (!is_bool($posted)) {
            if ($posted === 'true') {
                $posted = true;
            } else if ($posted === 'false' || $posted === '' || $posted === null) {
                $posted = false;
            } else {
                $msg = 'Invalid boolean value passed to ContractScheduleEntry::setPosted: ' . var_export($posted, true) ;
                throw IAException::newIAException( 'CN-1532', $msg, ['POSTED' => var_export($posted, true)]);
            }
        }
        $this->posted = $posted;
    }

    /**
     * @return bool
     */
    public function isHistorical() : bool
    {
        ContractUtil::assert(is_bool($this->historical));
        return $this->historical;
    }

    /**
     * @param bool $historical
     */
    public function setHistorical($historical)
    {
        if (!is_bool($historical)) {
            if ($historical === 'true') {
                $historical = true;
            } else if ($historical === 'false' || $historical === '' || $historical === null) {
                $historical = false;
            } else {
                $msg = 'Invalid boolean value passed to ContractScheduleEntry::setPosted: ' . var_export($historical, true) ;
                throw IAException::newIAException( 'CN-1532', $msg, ['POSTED' => var_export($historical, true)]);
            }
        }
        $this->historical = $historical;
    }

    /**
     * glBatchKey
     *
     * @return int
     */
    public function getGlBatchKey()
    {
        return $this->glBatchKey;
    }

    /**
     * glBatchKey
     *
     * @param int|null $glBatchKey
     */
    public function setGlBatchKey($glBatchKey)
    {
        $this->glBatchKey = $glBatchKey;
        
    }

    /**
     * @param bool $movedFromOtherLine
     */
    public function setMovedFromOtherLine($movedFromOtherLine)
    {
        $this->movedFromOtherLine = $movedFromOtherLine;
    }

    /**
     * @return bool
     */
    public function getMovedFromOtherLine()
    {
        return $this->movedFromOtherLine;
    }

    /**
     * @param SchedulesLinkGroup $linkGroup
     */
    public function setLinkGroup($linkGroup)
    {
        $this->linkGroup = $linkGroup;
    }

    /**
     * @return SchedulesLinkGroup
     */
    public function getLinkGroup()
    {
        return $this->linkGroup;
    }

    /**
     * @return SchedulesLinkGroup
     */
    public function getOriLinkGroup() : SchedulesLinkGroup
    {
        return $this->oriLinkGroup;
    }

    /**
     * @param SchedulesLinkGroup $oriLinkGroup
     */
    public function setOriLinkGroup(SchedulesLinkGroup $oriLinkGroup)
    {
        $this->oriLinkGroup = $oriLinkGroup;
    }

    /**
     * @param ContractScheduleEntry $entry
     */
    public function addNegativeEntry($entry)
    {
        if ($this->negativeEntries === null) {
            $this->negativeEntries = [];
        }
        $this->negativeEntries[] = $entry;
    }

    /**
     * @return ContractScheduleEntry[]|null
     */
    public function getNegativeEntries()
    {
        return $this->negativeEntries;
    }

    /**
     * @return float|string
     */
    public function getEffectiveAmount()
    {
        $amt = $this->getAmount();
        if ($this->negativeEntries !== null) {
            foreach ($this->negativeEntries as $entry) {
                $amt = ibcadd($amt, $entry->getAmount());
            }
        }
        return $amt;
    }

    /**
     * @return float|string
     */
    public function getEffectiveBaseAmount()
    {
        $amt = $this->getBaseAmount();
        if ($this->negativeEntries !== null) {
            foreach ($this->negativeEntries as $entry) {
                $amt = ibcadd($amt, $entry->getBaseAmount());
            }
        }
        return $amt;
    }

    /**
     * @return float|string
     */
    public function getEffectivePostedBaseAmount()
    {
        $amt = $this->getPostedBaseAmount();
        if ($this->negativeEntries !== null) {
            foreach ($this->negativeEntries as $entry) {
                if ($amt !== null) {
                    $amt = ibcadd($amt, $entry->getPostedBaseAmount());
                } else {
                    assert ($entry->getPostedBaseAmount() === null, 'Negative entry cannot be posted if its corresponding positive entry is not posted');
                }
            }
        }
        return $amt;
    }

    /**
     * @param ContractScheduleEntry $entry
     */
    public function addPaymentEntry($entry)
    {
        if ($this->pmtEntries === null) {
            $this->pmtEntries = [];
        }
        $this->pmtEntries[] = $entry;
    }

    public function sortPaymentEntries()
    {
        usort($this->pmtEntries, ['ContractScheduleEntry', 'comparePostingDate']);
    }

    /**
     * @return ContractScheduleEntry[]|null
     */
    public function getPaymentEntries()
    {
        return $this->pmtEntries;
    }


    /**
     * @param int $billScheduleEntryKey
     */
    public function setBillScheduleEntryKey($billScheduleEntryKey)
    {
        $this->billScheduleEntryKey = $billScheduleEntryKey;
    }

    /**
     * @return int
     */
    public function getBillScheduleEntryKey()
    {
        return $this->billScheduleEntryKey;
    }

    /**
     * @param int $oriPmtScheduleEntryKey
     */
    public function setOriPmtScheduleEntryKey($oriPmtScheduleEntryKey)
    {
        $this->oriPmtScheduleEntryKey = $oriPmtScheduleEntryKey;
    }

    /**
     * @return int
     */
    public function getOriPmtScheduleEntryKey()
    {
        return $this->oriPmtScheduleEntryKey;
    }

    /**
     * @param string $type
     */
    public function setType($type)
    {
        $this->type = $type;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string[] $emValues
     */
    public function setEmValues($emValues)
    {
        parent::setEmValues($emValues);
        $this->scheduleEntryKey = $this->getRawEntityValues('SCHEDULEENTRYKEY');
        $this->scheduleKey = $this->getRawEntityValues('SCHEDULEKEY');
        $this->schopKey = $this->getRawEntityValues('SCHOPKEY');
        $this->amount = $this->getRawEntityValues('AMOUNT');
        $this->baseAmount = $this->getRawEntityValues('BASEAMOUNT');
        $this->postedBaseAmount = $this->getRawEntityValues('POSTEDBASEAMOUNT');
        $this->postingDate = $this->getRawEntityValues('POSTINGDATE');
        $this->actualPostingDate = $this->getRawEntityValues('ACTUALPOSTINGDATE');
        $this->setPosted ($this->getRawEntityValues('POSTED'));
        $this->glBatchKey = $this->getRawEntityValues('GLBATCHKEY');
        $this->computationMemo = $this->getRawEntityValues('COMPUTATIONMEMO');
        $this->computationDetail = $this->getRawEntityValues('COMPUTATION_DETAIL');
        $this->exchRate = $this->getRawEntityValues('EXCHANGE_RATE');
        $this->state = $this->getRawEntityValues('STATE');
        $this->setPostable( 'false' !== ($emValues['POSTABLE'] ?? 'true') );
        $this->setBillScheduleEntryKey($emValues['BILLSCHEDULEENTRYKEY']);
        $this->setOriPmtScheduleEntryKey($emValues['ORIPMTSCHEDULEENTRYKEY'] ?? null);
        $this->setType($emValues['TYPE'] ?? '');
        $this->setDerivedPostingDate($this->getRawEntityValues('DERIVEDPOSTINGDATE'), false);
        $this->setApprovedHours($this->getRawEntityValues('APPROVEDHOURS'));
        $this->setSourceQty($this->getRawEntityValues('SOURCEQTY'));
        $this->setPercentRecognized($this->getRawEntityValues('PERCENTRECOGNIZED'));
        $this->postedExchangeRate = $this->getRawEntityValues('POSTEDEXCHANGE_RATE');
        $this->setIsAdjusted($this->getRawEntityValues("ADJUSTEDFOR"));
        $this->setHistorical($this->getRawEntityValues('HISTORICAL'));
        $this->setPaymentPREntryKey($this->getRawEntityValues('PAYMENTPRENTRYKEY'));
        $this->setPaymentPRRecordKey($this->getRawEntityValues('PAYMENTPRRECORDKEY'));
        $this->setUsageKey($this->getRawEntityValues('USAGEKEY'));
        $this->setDerivedAmount($this->getRawEntityValues('AMOUNT'));
        $this->setServicePeriodStartDate($this->getRawEntityValues('SERVICEPERIODSTARTDATE'));
        $this->setServicePeriodEndDate($this->getRawEntityValues('SERVICEPERIODENDDATE'));
    }

    /**
     * @return string[]
     */
    public function getValues()
    {
        $result = parent::getValues();
        $result['SCHEDULEENTRYKEY'] = $this->getScheduleEntryKey();
        $result['SCHEDULEKEY'] = $this->getScheduleKey();
        $result['SCHOPKEY'] = $this->getSchopKey();
        $result['AMOUNT'] = $this->getAmount();
        $result['BASEAMOUNT'] = $this->getBaseAmount();
        $result['POSTEDBASEAMOUNT'] = $this->getPostedBaseAmount();
        $result['POSTEDEXCHANGE_RATE'] = $this->getPostedExchangeRate();
        $result['EXCHANGE_RATE'] = $this->getExchangeRate();
        $result['POSTINGDATE'] = $this->getPostingDate();
        $result['ACTUALPOSTINGDATE'] = $this->getActualPostingDate();
        $result['POSTED'] = $this->getPosted() ? 'true' : 'false';
        $result['GLBATCHKEY'] = $this->getGlBatchKey();
        $result['COMPUTATIONMEMO'] = $this->getComputationMemo();
        $result['COMPUTATION_DETAIL'] = $this->getComputationDetail();
        $result['STATE'] = $this->getState();
        $result['POSTABLE'] = $this->isPostable() ? 'true' : 'false';
        $result['BILLSCHEDULEENTRYKEY'] = $this->getBillScheduleEntryKey();
        $result['ORIPMTSCHEDULEENTRYKEY'] = $this->getOriPmtScheduleEntryKey();
        $result['DERIVEDPOSTINGDATE'] = $this->getDerivedPostingDate();
        $result['APPROVEDHOURS'] = $this->getApprovedHours();
        $result['SOURCEQTY'] = $this->getSourceQty();
        $result['PERCENTRECOGNIZED'] = $this->getPercentRecognized();
        $result['ADJUSTEDFOR'] = $this->getIsAdjusted();
        $result['HISTORICAL'] = $this->isHistorical() ? 'true' : 'false';
        $result['PAYMENTPRENTRYKEY'] = $this->getPaymentPREntryKey();
        $result['PAYMENTPRRECORDKEY'] = $this->getPaymentPRRecordKey();
        $result['USAGEKEY'] = $this->getUsageKey();
        $result['SERVICEPERIODSTARTDATE'] = $this->getServicePeriodStartDate();
        $result['SERVICEPERIODENDDATE'] = $this->getServicePeriodEndDate();
        return $result;
    }

    /**
     * previousPostingDate
     * @return string
     */
    public function getPreviousPostingDate()
    {
        return $this->previousPostingDate;
    }

    /**
     * previousPostingDate
     * @param string $previousPostingDate
     */
    public function setPreviousPostingDate($previousPostingDate)
    {
        $this->previousPostingDate = $previousPostingDate;
    }


    /**
     * isAdjusted
     * @return string
     */
    public function getIsAdjusted()
    {
        return $this->isAdjusted;
    }

    /**
     * @return bool
     */
    public function isOneTimeAdjusted()
    {
        if ($this->getIsAdjusted() == 'MEA' && $this->getApprovedHours() == null) {
            return true;
        } else {
            return false;
        }
    }
    /**
     * isAdjusted
     * @param string $isAdjusted
     */
    public function setIsAdjusted($isAdjusted)
    {
        $this->isAdjusted = $isAdjusted;
    }


    /**
     * computationMemo
     * @return string
     */
    public function getComputationMemo()
    {
        return $this->computationMemo;
    }

    /**
     * computationMemo
     * @param string $computationMemo
     */
    public function setComputationMemo($computationMemo)
    {
        $this->computationMemo = $computationMemo;
    }
    
    /**
     * computationMemo
     * @param string $computationMemo
     */
    public function appendComputationMemo($computationMemo)
    {
        if (isset($this->computationMemo)) {
            $this->computationMemo .= ' ';
        }
        $this->computationMemo .= $computationMemo;
    }

    /**
     * period
     * @return float
     */
    public function getPeriod()
    {
        return $this->period;
    }

    /**
     * period
     * @param float $period
     */
    public function setPeriod($period)
    {
        $this->period = $period;

    }

    /**
     * baseAmount
     * @return float
     */
    public function getBaseAmount()
    {
        return $this->baseAmount;
    }

    /**
     * baseAmount
     * @param float $baseAmount
     */
    public function setBaseAmount($baseAmount)
    {
        $this->baseAmount = $baseAmount;
        
    }

    /**
     * @param float $exchRate
     */
    public function setExchRate($exchRate)
    {
        $this->exchRate = $exchRate;
    }

    /**
     * @return float
     */
    public function getExchRate()
    {
        return $this->exchRate;
    }

    /**
     * @param float|null $postedBaseAmount
     */
    public function setPostedBaseAmount($postedBaseAmount)
    {
        $this->postedBaseAmount = $postedBaseAmount;
    }

    /**
     * @return float
     */
    public function getPostedBaseAmount()
    {
        return $this->postedBaseAmount;
    }

    /**
     * Returns exchange rate
     *
     * @return float
     */
    public function getExchangeRate()
    {
        return $this->exchRate;
    }

    /**
     * Exchange rate
     *
     * @param float $exchRate
     *
     * @return ContractScheduleEntry
     */
    public function setExchangeRate($exchRate)
    {
        $this->exchRate = $exchRate;
        return $this;
    }

    /**
     * @return bool
     */
    public function isPostable()
    {
        return $this->postable;
    }
    /**
     * @param bool $postable
     */
    public function setPostable($postable)
    {
        $this->postable = $postable;
    }



    /**
     * state
     * @return string
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * state
     * @param string $state
     */
    public function setState($state)
    {
        $this->state = $state;
    }

    /**
     * @return bool
     */
    public function getStateChanged()
    {
        return $this->stateChanged;
    }

    /**
     * @param bool|null $stateChanged
     */
    public function setStateChanged($stateChanged)
    {
        $this->stateChanged = $stateChanged;
    }
    
    /**
     * @return float
     */
    public function getPercentComplete() {
        return $this->percentComplete;
    }
    
    /**
     * @param float $percentComplete
     */
    public function setPercentComplete($percentComplete) {
        $this->percentComplete = $percentComplete;
    }
    
    /**
     * @return float
     */
    public function getPercentRecognized() {
        return $this->percentRecognized;
    }
    
    /**
     * @param float $percentRecognized
     */
    public function setPercentRecognized($percentRecognized) {
        $this->percentRecognized = $percentRecognized;
    }

    /**
     * @return float
     */
    public function getApprovedHours()
    {
        return $this->approvedHours;
    }

    /**
     * @param float $approvedHours
     */
    public function setApprovedHours( $approvedHours )
    {
        $this->approvedHours = $approvedHours;
    }

    /**
     * @return string
     */
    public function getDerivedPostingDate()
    {
        if ( $this->derivedPostingDate == null ) {
            $result = $this->postingDate;
        } else {
            $result = $this->derivedPostingDate;
        }
        return $result;
    }

    /**
     * @param string    $derivedPostingDate
     * @param bool      $adjustMemo
     */
    public function setDerivedPostingDate($derivedPostingDate, $adjustMemo = true ) {
        $this->derivedPostingDate = $derivedPostingDate;
        $textMap = getLocalizedTextWithThrow(I18N::tokenArrayToObjectArray(['IA.CREATED_FOR_CLOSED_PERIOD']));
        //set the computation memo if the this date and posting date are different
        if ( $this->getPostingDate() !== null && DateCompare($derivedPostingDate,$this->getPostingDate()) !== 0 ) {
            if ( $adjustMemo === true ) {
                $memo = ContractUtil::GTP($textMap,'IA.CREATED_FOR_CLOSED_PERIOD',
                    ['POSTING_DATE' => ContractUtil::getDateForMemo($this->getDerivedPostingDate())]);
                $this->setComputationMemo($memo);
            }
        }
    }

    /**
     * @return bool
     */
    public function isPosted()
    {
        if ( $this->getState() === 'Posted' || $this->getState() === 'P' ) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @return string|float
     */
    public function getSourceQty()
    {
        return $this->sourceQty;
    }

    /**
     * @param string|float $sourceQty
     */
    public function setSourceQty($sourceQty)
    {
        $this->sourceQty = $sourceQty;
    }

    /**
     * @return string
     */
    public function getComputationDetail()
    {
        return $this->computationDetail;
    }

    /**
     * @param string $computationDetail
     */
    public function setComputationDetail($computationDetail)
    {
        if (strlen($computationDetail) > 4000) {
           $str = substr($computationDetail, 0, 3990) . '...';
        } else {
            $str = $computationDetail;
        }
        $this->computationDetail = $str;
    }
    /**
     * @param float|null $postedExchangeRate
     */
    public function setPostedExchangeRate($postedExchangeRate)
    {
        $this->postedExchangeRate = $postedExchangeRate;
    }

    /**
     * @return float
     */
    public function getPostedExchangeRate()
    {
        return $this->postedExchangeRate;
    }

    /**
     * @return int
     */
    public function getUsageKey(){
        return $this->usageKey;
    }

    /**
     * @param int $usageKey
     */
    public function setUsageKey($usageKey){
        $this->usageKey = $usageKey;
    }

    /**
     * @return int
     */
    public function getPaymentPREntryKey()
    {
        return $this->paymentPREntryKey;
    }

    /**
     * @param int|null $paymentPREntryKey
     */
    public function setPaymentPREntryKey($paymentPREntryKey)
    {
        $this->paymentPREntryKey = $paymentPREntryKey;
    }

    /**
     * @return int
     */
    public function getPaymentPRRecordKey()
    {
        return $this->paymentPRRecordKey;
    }

    /**
     * @param int|null $paymentPRRecordKey
     */
    public function setPaymentPRRecordKey($paymentPRRecordKey)
    {
        $this->paymentPRRecordKey = $paymentPRRecordKey;
    }

    public function isOpen() : bool
    {
        $ignoredStates = [
            ContractScheduleEntryState::STATE_POSTED,
            ContractScheduleEntryState::STATE_TERMINATED,
            ContractScheduleEntryState::STATE_RENEWAL_FORECAST,
        ];
        
        $entryState = $this->getState();
        $entryStateChanged = $this->getStateChanged();
        
        return (
            $this->isPosted() === false
            && ( ! in_array($entryState, $ignoredStates) )
            && (
                $entryStateChanged == null
                || $entryStateChanged !== self::CONTRACT_SCHEDULE_ENTRY_DELETED
            )
        );
    }
    
    public function isOnHold() : bool
    {
        return $this->state === ContractScheduleEntryState::STATE_ONHOLD;
    }
    
    public function isNonRecognizedOpenEntry(string $effectiveDate): bool
    {
        $postingDate = $this->getPostingDate();
        
        return $this->isOpen() && $this->isPostable() && DateCompare($postingDate, $effectiveDate) < 0;
    }
    
    public function isNonRecognizedInProgressPostableEntry(ContractScheduleInfo $scheduleInfo, string $effectiveDate): bool
    {
        return $this->isNonRecognizedOpenEntry($effectiveDate)
               && !$this->isOnHold()
               && $scheduleInfo->getState() === ContractScheduleState::STATE_INPROGRESS;
    }
    
    public function isDeleted() : bool
    {
        return ($this->getStateChanged() == self::CONTRACT_SCHEDULE_ENTRY_DELETED);
    }

    /**
     * Desc: this will set the posting as deferred and posting will not posted while creating or updating in table
     */
    public function  setDeferredPosting($flag): void
    {
            ContractUtil::assert(is_bool($flag));
            ContractUtil::assert($this->postingDatedForDeferredPosting); //posting date  must set before marking schedule as deferred posting
            $this->deferredPosting =  $flag;
    }

    /**
     * Check is schedule entry is not posted and marked as deferred
     * @return bool
     */
    public function isPostingDeferred()
    {
        return $this->deferredPosting;
    }

    /**
     * @param $postingDate
     */
    public function setPostingDateForDeferredPosting($postingDate): void
    {
            $this->postingDatedForDeferredPosting = $postingDate;
    }

    /**
     * @return string|null
     */
    public function getPostingDateForDeferredPosting(): ?string
    {
        return $this->postingDatedForDeferredPosting;
    }

    /**
     * @return string|null
     */
    public function getServicePeriodStartDate() : ?string
    {
        return $this->servicePeriodStartDate;
    }

    /**
     * @param string|null $servicePeriodStartDate
     */
    public function setServicePeriodStartDate(?string $servicePeriodStartDate) : void
    {
        $this->servicePeriodStartDate = $servicePeriodStartDate;
    }

    /**
     * @return string|null
     */
    public function getServicePeriodEndDate() : ?string
    {
        return $this->servicePeriodEndDate;
    }

    /**
     * @param string|null $servicePeriodEndDate
     */
    public function setServicePeriodEndDate(?string $servicePeriodEndDate) : void
    {
        $this->servicePeriodEndDate = $servicePeriodEndDate;
    }

    /**
     * This function will return date of schedule entry which will use for comparison and calculation in adjustment.
     * In schedule entry we have derived posting date which is  original system generated posting date and
     * we have posting date which can be modified, this posting date can or can not same as derived posting date.
     * For calculation and comparison we use system generated posting date.
     * @param string $effectiveDate
     * @return mixed|string|null
     */
    public function getPostingPeriodForCalculation(string $effectiveDate){

        $postingDate = $this->getOriginalPostingDate();

        if ($this->isPosted()) {
            /**
             * If system generated posting date is after MEA effective date and schedule is posted.
             * In this case we will cross verify that current posting date is before MEA or not.
             * If it is after MEA effective date then  we will use current posting date for culculation.
             * This is fix for CN-1833
             */
            if (DateCompare($postingDate, $effectiveDate) >= 0 &&
                DateCompare($this->getPostingDate(), $effectiveDate) <= 0
            ) {
                $postingDate = $this->getPostingDate();
            }
        }

        return $postingDate;
    }
}