<?
/**
 * Entity for Evergreen Template picker
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Sage Intacct Inc., All Rights Reserved
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

$kSchemas['evergreenmacropick'] = [
    'schema' => ['RECORDNO' => 'record#', 'MACROID'=> 'macroid', 'TRANSACTIONTYPE' => 'transactiontype', 'TERMLENGTH' => 'termlength', 'TERMPERIOD' => 'termperiod'],
    'object' => ['MACROID', 'TRANSACTIONTYPE', 'TERMLENGTH', 'TERMPERIOD'],
    'fieldinfo' => [
        [
            'fullname' => 'IA.EVERGREEN_TEMPLATE_PICK',
            'type' => ['ptype' => 'text', 'type' => 'text', 'maxlength' => 200],
            'required' => true,
            'path' => 'MACROID'
        ],
        [
            'path' => 'TRANSACTIONTYPE',
            'fullname' => 'IA.TRANSACTION_TYPE',
            'type' => [
                'ptype' => 'enum',
                'type' => 'enum',
                'validvalues' => ['Sales Transaction', 'Contract', 'Evergreen'],
                '_validivalues' => ['SO', 'CN', 'EG'],
                'validlabels' => ['IA.SALES_TRANSACTION', 'IA.CONTRACT', 'IA.EVERGREEN'],
            ],
            'default' => 'Evergreen',
        ],
        [
            'path' => 'TERMPERIOD',
            'fullname' => 'IA.DEFAULT_RENEWAL_TERM_PERIOD',
            'type' => [
                'ptype' => 'enum',
                'type' => 'text',
                'validvalues' => ['Years', 'Months', 'Days'],
                '_validivalues' => ['Y', 'M', 'D'],
                'validlabels' => ['IA.YEARS', 'IA.MONTHS', 'IA.DAYS'],
            ],
            'default' => 'Months',
        ],
    ],
    'table'     => 'renewalmacro',
    'vid'       => 'MACROID',
    'module'    => 'so',
    'dbfilters' => [['evergreenmacropick.latestversionkey', 'ISNULL'], ['evergreenmacropick.status', '=', 'T']],
    'printas'   => 'IA.EVERGREEN_TEMPLATES',
    'pluralprintas' => 'IA.EVERGREEN_TEMPLATES'
];

