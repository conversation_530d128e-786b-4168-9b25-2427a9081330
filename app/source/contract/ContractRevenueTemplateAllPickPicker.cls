<?php
/**
 * File ContractRevenueTemplateAllPickPicker.cls contains the class ContractRevenueTemplateAllPickPicker
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2024 Sage Inc.
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Sage Inc.
 */

/**
 * Revenue template custom picker
 *
 * Class ContractRevenueTemplateAllPickPicker
 */
class ContractRevenueTemplateAllPickPicker extends NPicker
{
    function __construct()
    {
        parent::__construct(
            [
                'entity' => 'contractrevenuetemplatepick',
                'title' => 'IA.CONTRACT_REVENUE_TEMPLATE',
                'fields' => ['RECORDNO', 'PICKID'],
                'pickfield' => 'PICKID',
            ]
        );
        
        $this->privateButton = false;
    }
}
