<?php

/**
 * Editor balances trait for Contract Detail
 * Contains contract detail balance related code, moved from the ContractDetailEditor class begining of 2024
 *
 * <AUTHOR>
 * @copyright 2000-2024 Sage Inc.
 */
class ContractDetailEditorBalances
{
    private bool $isMCPEnabled;
    private bool $isStandardRevRecEnabled;
    private bool $isExpenseEnabled;
    
    public function __construct(bool $isMCPEnabled, bool $isStandardRevRecEnabled, bool $isExpenseEnabled)
    {
        $this->isMCPEnabled = $isMCPEnabled;
        $this->isStandardRevRecEnabled = $isStandardRevRecEnabled;
        $this->isExpenseEnabled = $isExpenseEnabled;
    }
    
    protected function getEntityMgr(): EntityManager
    {
        return Globals::$g->gManagerFactory->getManager('contractdetail');
    }
    
    /**
     * Retrieves balances for display thru Ajax
     */
    public function getBalances(array $obj)
    {
        $scope = Request::$r->scope;
        
        $showBaseGrid = $this->baseGridRequired($obj);
        
        if (ContractManager::isEvergreenContract($obj, 'CONTRACT.TERMTYPE') && $obj['STATE'] == ContractDetailState::STATE_INPROGRESS) {
            $onlyInProgress = ($scope == ContractDetailState::STATE_INPROGRESS);
            $contractDetailId = $obj['RENEWEDORIGINALDETAILKEY'];
            $includeAllPeriods = true;
        } else {
            $onlyInProgress = false;
            $contractDetailId = Request::$r->contractDetailKey;
            $includeAllPeriods = false;
        }
        
        $asOfDate = Request::$r->asOfDate;
        
        self::setRevenueBalances($contractDetailId, 'J1', $showBaseGrid, $obj, $obj, "", $asOfDate, $onlyInProgress, $includeAllPeriods);
        self::setExpenseBalances($contractDetailId, 'J1', $showBaseGrid, $obj, "", $asOfDate, $onlyInProgress);
        self::setARBalances($contractDetailId, 'J1', $showBaseGrid, $obj, $obj, "", $asOfDate, $onlyInProgress, $includeAllPeriods);
        
        self::setRevenueBalances($contractDetailId, 'J2', $showBaseGrid, $obj, $obj, "", $asOfDate, $onlyInProgress, $includeAllPeriods);
        self::setExpenseBalances($contractDetailId, 'J2', $showBaseGrid, $obj, "", $asOfDate, $onlyInProgress);
        self::setARBalances($contractDetailId, 'J2', $showBaseGrid, $obj, $obj, "", $asOfDate, $onlyInProgress, $includeAllPeriods);
        
        echo json_encode($obj);
    }
    
    /**
     * Retrieves balances as of date to display
     *
     * @param EditorView $view
     * @param array $obj
     */
    public function setBalances($view, array &$obj)
    {
        $showBaseGrid = $this->baseGridRequired($obj);
        
        // Fetch additional
        $cndAddl = EntityManager::GetListQuick(
            'contractdetail',
            ['CONTRACT.BEGINDATE', 'CONTRACT.ENDDATE', 'CONTRACT.EXCH_RATE_TYPE_ID'],
            ['RECORDNO' => $obj['RECORDNO']]
        );
        
        if (!empty($cndAddl[0])) {
            $cndAddl = $cndAddl[0];
            $obj = array_merge($obj, $cndAddl);
        }
        
        // hide revenue recognition related fields if contract revrec is not subscribed...
        if ($this->isStandardRevRecEnabled) {
            //            self::setRevenueBalances($obj['RECORDNO'], 'J1', $showBaseGrid, $obj, $obj);
            
            $bal1Grid = [];
            $view->findComponents(
                ['path' => 'JRNL1_BALANCES'],
                EditorComponentFactory::TYPE_GRID,
                $bal1Grid
            );
            
            if ($bal1Grid[0]) {
                $bal1Grid[0]->setProperty('hidden', false);
            }
            
            if ($showBaseGrid) {
                $bal1GridBase = [];
                $view->findComponents(
                    ['path' => 'JRNL1_BALANCESBASE'],
                    EditorComponentFactory::TYPE_GRID,
                    $bal1GridBase
                );
                
                if ($bal1GridBase[0]) {
                    $bal1GridBase[0]->setProperty('hidden', false);
                }
            }
            
            //            self::setRevenueBalances($obj['RECORDNO'], 'J2', $showBaseGrid, $obj, $obj);
            
            $bal2Grid = [];
            $view->findComponents(
                ['path' => 'JRNL2_BALANCES'],
                EditorComponentFactory::TYPE_GRID,
                $bal2Grid
            );
            
            if ($bal2Grid[0]) {
                $bal2Grid[0]->setProperty('hidden', false);
            }
            
            if ($showBaseGrid) {
                $bal2GridBase = [];
                $view->findComponents(
                    ['path' => 'JRNL2_BALANCESBASE'],
                    EditorComponentFactory::TYPE_GRID,
                    $bal2GridBase
                );
                
                if ($bal2GridBase[0]) {
                    $bal2GridBase[0]->setProperty('hidden', false);
                }
            }
        }
        
        if ($this->isExpenseEnabled) {
            //            self::setExpenseBalances($obj['RECORDNO'], 'J1', $showBaseGrid, $obj, $obj);
            
            $expbal1Grid = [];
            $view->findComponents(
                ['path' => 'JRNL1_EXP'],
                EditorComponentFactory::TYPE_GRID,
                $expbal1Grid
            );
            
            if ($expbal1Grid[0]) {
                $expbal1Grid[0]->setProperty('hidden', false);
            }
            
            if ($showBaseGrid) {
                $expbal1GridBase = [];
                $view->findComponents(
                    ['path' => 'JRNL1_EXPBASE'],
                    EditorComponentFactory::TYPE_GRID,
                    $expbal1GridBase
                );
                
                if ($expbal1GridBase[0]) {
                    $expbal1GridBase[0]->setProperty('hidden', false);
                }
            }
            
            //            self::setExpenseBalances($obj['RECORDNO'], 'J2', $showBaseGrid, $obj, $obj);
            
            $expbal2Grid = [];
            $view->findComponents(
                ['path' => 'JRNL2_EXP'],
                EditorComponentFactory::TYPE_GRID,
                $expbal2Grid
            );
            
            if ($expbal2Grid[0]) {
                $expbal2Grid[0]->setProperty('hidden', false);
            }
            
            if ($showBaseGrid) {
                $expbal2GridBase = [];
                $view->findComponents(
                    ['path' => 'JRNL2_EXPBASE'],
                    EditorComponentFactory::TYPE_GRID,
                    $expbal2GridBase
                );
                
                if ($expbal2GridBase[0]) {
                    $expbal2GridBase[0]->setProperty('hidden', false);
                }
            }
        }
        
        // AR Balances for J1
        //        self::setARBalances($obj['RECORDNO'], 'J1', $showBaseGrid, $obj, $obj);
        
        $arbal1Grid = [];
        $view->findComponents(
            ['path' => 'JRNL1_AR'],
            EditorComponentFactory::TYPE_GRID,
            $arbal1Grid
        );
        
        if ($arbal1Grid[0]) {
            $arbal1Grid[0]->setProperty('hidden', false);
        }
        
        if ($showBaseGrid) {
            $arbal1GridBase = [];
            $view->findComponents(
                ['path' => 'JRNL1_ARBASE'],
                EditorComponentFactory::TYPE_GRID,
                $arbal1GridBase
            );
            
            if ($arbal1GridBase[0]) {
                $arbal1GridBase[0]->setProperty('hidden', false);
            }
        }
        
        // AR Balances for J2
        //        self::setARBalances($obj['RECORDNO'], 'J2', $showBaseGrid, $obj, $obj);
        
        $arbal2Grid = [];
        $view->findComponents(
            ['path' => 'JRNL2_AR'],
            EditorComponentFactory::TYPE_GRID,
            $arbal2Grid
        );
        
        if ($arbal2Grid[0]) {
            $arbal2Grid[0]->setProperty('hidden', false);
        }
        
        if ($showBaseGrid) {
            $arbal2GridBase = [];
            $view->findComponents(
                ['path' => 'JRNL2_ARBASE'],
                EditorComponentFactory::TYPE_GRID,
                $arbal2GridBase
            );
            
            if ($arbal2GridBase[0]) {
                $arbal2GridBase[0]->setProperty('hidden', false);
            }
        }
    }
    
    /**
     * Checks if base currency is involved
     *
     * @param array $obj
     *
     * @return  bool
     */
    protected function baseGridRequired($obj)
    {
        $showBaseGrid = false;
        if ($this->isMCPEnabled) {
            $baseCurrency = $obj['BASECURR'];
            $currency = $obj['CURRENCY'];
            
            $showBaseGrid = !($baseCurrency == $currency);
        }
        
        return $showBaseGrid;
    }
    
    /**
     * Retrieves and sets revenue balance in response
     *
     * @param int         $contractDetailKey Contract detail key
     * @param string      $jrnlCode Journal code
     * @param bool        $showBaseGrid 'true' if base currency balances are to be shown
     * @param array       $obj Source array
     * @param array       $destObj Destination array
     * @param string      $prefix Path prefix
     * @param string|null $asOfDate As of date
     * @param bool        $onlyInProgress Only include In progress contract lines
     * @param bool        $includeAllPeriods Include all periods of Evergreen line
     */
    public static function setRevenueBalances(
        $contractDetailKey,
        $jrnlCode,
        $showBaseGrid,
        $obj,
        &$destObj,
        $prefix = "",
        $asOfDate = null,
        $onlyInProgress = false,
        $includeAllPeriods = true
    ) {
        $jrnl1_balances = ContractRevenueGLReclassEngine::getBalances(
            $contractDetailKey,
            'R',
            $jrnlCode,
            '',
            true,
            $asOfDate,
            $onlyInProgress,
            $includeAllPeriods,
            $isRevRecOnInv
        );
        
        $unbilled = $jrnl1_balances[0]['UNBILLED'] + $jrnl1_balances[1]['UNBILLED'];
        $billed = $jrnl1_balances[0]['BILLED'] + $jrnl1_balances[1]['BILLED'];
        $paid = $jrnl1_balances[0]['PAID'] + $jrnl1_balances[1]['PAID'];
        
        $baseUnbilled = $jrnl1_balances[0]['BASEUNBILLED'] + $jrnl1_balances[1]['BASEUNBILLED'];
        $baseBilled = $jrnl1_balances[0]['BASEBILLED'] + $jrnl1_balances[1]['BASEBILLED'];
        $basePaid = $jrnl1_balances[0]['BASEPAID'] + $jrnl1_balances[1]['BASEPAID'];
        
        $total = $unbilled + $billed + $paid;
        $baseTotal = $baseUnbilled + $baseBilled + $basePaid;
        $revTypeTotalToken = 'IA.TOTAL';
        if ($isRevRecOnInv) {
            $jrnl1_balances = [];
            $revTypeTotalToken = 'IA.SALES_REVENUE';
        }
        $jrnl1_balances[] = [
            'REVENUETYPE' => $revTypeTotalToken,
            'UNBILLED' => $unbilled == 0 ? "0" : $unbilled,
            'BILLED' => $billed == 0 ? "0" : $billed,
            'PAID' => $paid == 0 ? "0" : $paid,
            'TOTAL' => $total == 0 ? "0" : $total,
            'BASEUNBILLED' => $baseUnbilled == 0 ? "0" : $baseUnbilled,
            'BASEBILLED' => $baseBilled == 0 ? "0" : $baseBilled,
            'BASEPAID' => $basePaid == 0 ? "0" : $basePaid,
            'BASETOTAL' => $baseTotal == 0 ? "0" : $baseTotal,
        ];
        
        // Convert amounts into drill-down links to glledger report
        self::linkifyAmounts('R', $asOfDate, $jrnlCode, $obj, $jrnl1_balances, $onlyInProgress);
        
        if ($jrnlCode == 'J1') {
            $balKey = "JRNL1_BALANCES{$prefix}";
            $baseBalKey = "JRNL1_BALANCESBASE{$prefix}";
        } else {
            $balKey = "JRNL2_BALANCES{$prefix}";
            $baseBalKey = "JRNL2_BALANCESBASE{$prefix}";
        }
        
        $destObj[$balKey] = $jrnl1_balances;
        if ($showBaseGrid) {
            $destObj[$baseBalKey] = $jrnl1_balances;
        }
    }
    
    /**
     * @param string $balanceType
     * @param string $asOfDate
     * @param string $jrnlCode
     * @param array  $curObj
     * @param array  $balances
     * @param bool   $onlyInProgress
     */
    private static function linkifyAmounts($balanceType, $asOfDate, $jrnlCode, $curObj, &$balances, $onlyInProgress)
    {
        $gllr_op = GetOperationId('gl/reports/glledger');
        
        $bookId = self::getBookId($jrnlCode, $curObj);
        
        $useContractLineDates = $onlyInProgress || $curObj['STATE'] == ContractDetailState::STATE_CLOSED;
        $startDate = $useContractLineDates ? $curObj['BEGINDATE'] : $curObj['CONTRACT.BEGINDATE'];
        if (!$asOfDate) {
            if ($useContractLineDates) {
                $asOfDate = $curObj['ENDDATE'];
            } else {
                $asOfDate = empty($curObj['CONTRACT.ENDDATE'])
                    ?    // Evergreen contract has no end date
                    self::getContractDetailMaxEndDate($curObj['CONTRACTKEY'], $curObj['LINENO'])
                    :
                    $curObj['CONTRACT.ENDDATE'];
            }
        }
        $endDate = $asOfDate;
        
        $locId = $curObj["LOCATIONID"];
        $itemId = $curObj["ITEMID"];
        $contractId = $curObj["CONTRACTID"];
        
        if ($balanceType == 'R') {
            self::linkifyRevenueAmounts(
                $gllr_op,
                $curObj,
                $balances,
                $bookId,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
        } elseif ($balanceType == 'A') {
            self::linkifyARAmounts(
                $gllr_op,
                $curObj,
                $balances,
                $bookId,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
        }
        /**
         * else if ($balanceType == 'E') {
         * self::linkifyExpenseAmounts($gllr_op, $curObj, $balances, $bookId, $startDate, $endDate, $locId, $itemId,
         * $contractId);
         * }
         * */
    }
    
    /**
     * @param string      $contractKey
     * @param string|null $lineNo
     *
     * @return string
     */
    public static function getContractDetailMaxEndDate($contractKey, $lineNo = null): string
    {
        $maxEndDate = '';
        $filters = [
            ['CONTRACTKEY', '=', $contractKey],
            ['STATE', '!=', ContractDetailState::STATE_RENEWAL_FORECAST],
        ];
        if ($lineNo) {
            $filters[] = ['LINENO', '=', $lineNo];
        }
        $query = [
            'selects' => ['MAX(ENDDATE) as MAXENDDATE'],
            'filters' => [$filters],
        ];
        $cnDetailMgr = Globals::$g->gManagerFactory->getManager('contractdetail');
        $result = $cnDetailMgr->GetList($query);
        if (!empty($result)) {
            $maxEndDate = $result[0]['MAXENDDATE'];
        };
        
        return $maxEndDate ?? '';
    }
    
    /**
     * @param string $jrnlCode
     * @param array  $curObj
     *
     * @return string
     */
    public static function getBookId($jrnlCode, $curObj)
    {
        /* @var ManagerFactory $gManagerFactory */
        $gManagerFactory = Globals::$g->gManagerFactory;
        
        /* @var JournalManager $jrnlMgr */
        $jrnlMgr = $gManagerFactory->getManager('journal');
        
        $jrnlSym = $jrnlCode == "J1" ? $curObj["REVENUEJOURNAL"] : $curObj["REVENUE2JOURNAL"];
        $bookId = $jrnlMgr->GetJournalBook($jrnlSym);
        
        // Really?? Yes
        if (strstr($bookId, ACCRUAL_BOOK)) {
            $bookId = str_replace(ACCRUAL_BOOK, "", $bookId);
        } elseif (strstr($bookId, CASH_BOOK)) {
            $bookId = str_replace(CASH_BOOK, "", $bookId);
        }
        
        return $bookId;
    }
    
    /**
     * @param string  $gllr_op
     * @param array   $curObj
     * @param array  &$balances
     * @param string  $bookId
     * @param string  $startDate
     * @param string  $endDate
     * @param string  $locId
     * @param string  $itemId
     * @param string  $contractId
     */
    private static function linkifyARAmounts(
        $gllr_op,
        $curObj,
        &$balances,
        $bookId,
        $startDate,
        $endDate,
        $locId,
        $itemId,
        $contractId
    ) {
        $accts = [$curObj['ARUNBILLEDACCTNO']];
        $balances[0]['UNBILLED'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[0]['UNBILLED'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $balances[0]['BASEUNBILLED'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[0]['BASEUNBILLED'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $accts = [$curObj['ARBILLEDACCTNO']];
        $balances[0]['BILLED'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[0]['BILLED'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $balances[0]['BASEBILLED'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[0]['BASEBILLED'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        // Paid - All cash accounts
        $accts = ContractEditorBalances::getCashAcctsForContractLines([$curObj['RECORDNO']]);
        $balances[0]['PAID'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[0]['PAID'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $balances[0]['BASEPAID'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[0]['BASEPAID'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $accts = [$curObj['ARUNBILLEDACCTNO'], $curObj['ARBILLEDACCTNO']];
        $balances[0]['TOTAL'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[0]['TOTAL'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $balances[0]['BASETOTAL'] =
            self::createGLReportLinkHTML(
                $gllr_op,
                $balances[0]['BASETOTAL'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
    }
    
    /**
     * @param string  $gllr_op
     * @param array   $curObj
     * @param array  &$balances
     * @param string  $bookId
     * @param string  $startDate
     * @param string  $endDate
     * @param string  $locId
     * @param string  $itemId
     * @param string  $contractId
     */
    private static function linkifyRevenueAmounts(
        $gllr_op,
        $curObj,
        &$balances,
        $bookId,
        $startDate,
        $endDate,
        $locId,
        $itemId,
        $contractId
    ) {
        $totalIdx = 2;
        if (count($balances) === 1) {
            // Hide DR and Sales rows for Rev Rec on Invoice lines, in which case only total will be shown
            $totalIdx = 0;
        } else {
            // 0 - Deferred
            $accts = [$curObj['DRUNBILLEDACCTNO']];
            $balances[0]['UNBILLED'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[0]['UNBILLED'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $balances[0]['BASEUNBILLED'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[0]['BASEUNBILLED'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $accts = [$curObj['DRBILLEDACCTNO']];
            $balances[0]['BILLED'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[0]['BILLED'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $balances[0]['BASEBILLED'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[0]['BASEBILLED'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $accts = [$curObj['DRPAIDACCTNO']];
            $balances[0]['PAID'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[0]['PAID'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $balances[0]['BASEPAID'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[0]['BASEPAID'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $accts = [$curObj['DRUNBILLEDACCTNO'], $curObj['DRBILLEDACCTNO'], $curObj['DRPAIDACCTNO']];
            $balances[0]['TOTAL'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[0]['TOTAL'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $balances[0]['BASETOTAL'] =
                self::createGLReportLinkHTML(
                    $gllr_op,
                    $balances[0]['BASETOTAL'],
                    [$bookId],
                    $accts,
                    $startDate,
                    $endDate,
                    $locId,
                    $itemId,
                    $contractId
                );
            
            // 1 - Sales
            $accts = [$curObj['SALESUNBILLEDACCTNO']];
            $balances[1]['UNBILLED'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[1]['UNBILLED'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $balances[1]['BASEUNBILLED'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[1]['BASEUNBILLED'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $accts = [$curObj['SALESBILLEDACCTNO']];
            $balances[1]['BILLED'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[1]['BILLED'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $balances[1]['BASEBILLED'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[1]['BASEBILLED'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $accts = [$curObj['SALESPAIDACCTNO']];
            $balances[1]['PAID'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[1]['PAID'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $balances[1]['BASEPAID'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[1]['BASEPAID'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $accts = [$curObj['SALESUNBILLEDACCTNO'], $curObj['SALESBILLEDACCTNO'], $curObj['SALESPAIDACCTNO']];
            $balances[1]['TOTAL'] = self::createGLReportLinkHTML(
                $gllr_op,
                $balances[1]['TOTAL'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
            
            $balances[1]['BASETOTAL'] =
                self::createGLReportLinkHTML(
                    $gllr_op,
                    $balances[1]['BASETOTAL'],
                    [$bookId],
                    $accts,
                    $startDate,
                    $endDate,
                    $locId,
                    $itemId,
                    $contractId
                );
        }
        
        // 2 - Total
        $accts = [$curObj['DRUNBILLEDACCTNO'], $curObj['SALESUNBILLEDACCTNO']];
        $balances[$totalIdx]['UNBILLED'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[$totalIdx]['UNBILLED'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $balances[$totalIdx]['BASEUNBILLED'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[$totalIdx]['BASEUNBILLED'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $accts = [$curObj['DRBILLEDACCTNO'], $curObj['SALESBILLEDACCTNO']];
        $balances[$totalIdx]['BILLED'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[$totalIdx]['BILLED'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $balances[$totalIdx]['BASEBILLED'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[$totalIdx]['BASEBILLED'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $accts = [$curObj['DRPAIDACCTNO'], $curObj['SALESPAIDACCTNO']];
        $balances[$totalIdx]['PAID'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[$totalIdx]['PAID'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $balances[$totalIdx]['BASEPAID'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[$totalIdx]['BASEPAID'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $accts = [$curObj['DRUNBILLEDACCTNO'], $curObj['DRBILLEDACCTNO'], $curObj['DRPAIDACCTNO'],
            $curObj['SALESUNBILLEDACCTNO'], $curObj['SALESBILLEDACCTNO'], $curObj['SALESPAIDACCTNO']];
        $balances[$totalIdx]['TOTAL'] = self::createGLReportLinkHTML(
            $gllr_op,
            $balances[$totalIdx]['TOTAL'],
            [$bookId],
            $accts,
            $startDate,
            $endDate,
            $locId,
            $itemId,
            $contractId
        );
        
        $balances[$totalIdx]['BASETOTAL'] =
            self::createGLReportLinkHTML(
                $gllr_op,
                $balances[$totalIdx]['BASETOTAL'],
                [$bookId],
                $accts,
                $startDate,
                $endDate,
                $locId,
                $itemId,
                $contractId
            );
    }
    
    /**
     * @param string   $gllr_op
     * @param float    $amount
     * @param string[] $bookIds
     * @param array    $accts
     * @param string   $startDate
     * @param string   $endDate
     * @param string   $locId
     * @param string   $itemId
     * @param string   $contractId
     *
     * @return string
     */
    public static function createGLReportLinkHTML(
        $gllr_op,
        $amount,
        $bookIds,
        $accts,
        $startDate,
        $endDate,
        $locId,
        $itemId,
        $contractId
    ) {
        $formattedAmount = Currency($amount, 2, 0, '', true, '');
        if (!$accts || empty($endDate)) {
            return $formattedAmount;
        }
        
        $sess = Session::getKey();
        
        $rpt_url = "reporteditor.phtml?.op=$gllr_op&.sess=$sess&.p=1&.type=_html&"
                   . "_obj__reportingBook=" . URLCleanParams::insert('_obj__reportingBook', 'ACCRUAL');
        $rpt_url .= "&_obj__INCLUDEREPBOOK=" . URLCleanParams::insert('_obj__INCLUDEREPBOOK', 'true')
                    . "&_obj__zeroBalance=" . URLCleanParams::insert('_obj__zeroBalance', "W")
                    . "&_obj__showdetail=" . URLCleanParams::insert('_obj__showdetail', "D")
                    . "&_obj__docColumnValue=" . URLCleanParams::insert('_obj__docColumnValue', "R");
        $rpt_url .= "&_obj__displayAllAccts=" . URLCleanParams::insert('_obj__displayAllAccts', "N")
                    . "&_obj__zeroAcctsWTrans=" . URLCleanParams::insert('_obj__zeroAcctsWTrans', "Y")
                    . "&_obj__filterTransactions=" . URLCleanParams::insert('_obj__filterTransactions', "ALL");
        $rpt_url .= "&.reporttitle=General%20Ledger%20Report";
        $rpt_url .= "&.drillfilter=1";
        
        $bookIds = array_unique($bookIds);
        $bookListHashStr = implode('%2523~%2523', $bookIds);
        $rpt_url .= "&_obj__ADJUSTMENTBOOK=" . urlencode(
                URLCleanParams::insert(
                    '_obj__ADJUSTMENTBOOK',
                    urldecode($bookListHashStr)
                )
            );
        
        if ($locId != '' && IsMCMESubscribed() && !GetContextLocation()) {
            $rpt_url .= "&_obj__loc=" . URLCleanParams::insert('_obj__loc', $locId)
                        . "&_obj__loc_SUBS=" . URLCleanParams::insert('_obj__loc_SUBS', "true");
        }
        
        $rpt_url .= "&_obj__CONTRACTID=" . URLCleanParams::insert('_obj__CONTRACTID', $contractId)
                    . "&_obj__CONTRACTID_SUBS=" . URLCleanParams::insert('_obj__CONTRACTID_SUBS', "true");
        if ($itemId) {
            $rpt_url .= "&_obj__ITEMID=" . URLCleanParams::insert('_obj__ITEMID', $itemId)
                        . "&_obj__ITEMID_SUBS=" . URLCleanParams::insert('_obj__ITEMID_SUBS', "true");
        }
        
        $startDate = FormatDateForDisplay($startDate);
        $endDate = FormatDateForDisplay($endDate);
        
        $rpt_url .= "&_obj__startdate=" . URLCleanParams::insert('_obj__startdate', $startDate);
        $rpt_url .= "&_obj__enddate=" . URLCleanParams::insert('_obj__enddate', $endDate);
        
        // #~# separator
        $accts = array_unique($accts);
        $newAccts = [];
        foreach ($accts as $acct) {
            $newAccts[] = "$acct->$acct";
        }
        $acctListHashStr = implode('#~#', $newAccts);
        $obfStr = URLCleanParams::insert('_obj__multiAcc', $acctListHashStr);
        $rpt_url .= "&_obj__multiAcc=" . str_replace('#', '%23', $obfStr);
        
        $rptURLLauncher = '<a href=\'#\' onclick=\'javascript:Launch( "' . $rpt_url
                          . '" , "glledger", 1200, 600);\' target1="_blank">' . $formattedAmount . '</a>';
        
        return $rptURLLauncher;
    }
    
    /**
     * Retrieves and sets expense balance in response
     *
     * @param int         $contractDetailKey Contract detail key
     * @param string      $jrnlCode Journal code
     * @param bool        $showBaseGrid 'true' if base currency balances are to be shown
     * @param array       $destObj Destination array
     * @param string      $prefix Path prefix
     * @param string|null $asOfDate As of date
     * @param bool        $onlyInProgress Only include In progress contract lines
     */
    public static function setExpenseBalances(
        $contractDetailKey,
        $jrnlCode,
        $showBaseGrid,
        &$destObj,
        $prefix = "",
        $asOfDate = null,
        $onlyInProgress = false
    ) {
        $jrnl1_exp = ContractExpenseGLReclassEngine::getContractDetailBalances(
            $contractDetailKey,
            $jrnlCode,
            $asOfDate,
            $onlyInProgress
        );
        
        // Convert amounts into drill-down links to glledger report
        //self::linkifyAmounts('E', $asOfDate, $jrnlCode, $obj, $jrnl1_exp);
        
        if ($jrnlCode == 'J1') {
            $balKey = "JRNL1_EXP{$prefix}";
            $baseBalKey = "JRNL1_EXPBASE{$prefix}";
        } else {
            $balKey = "JRNL2_EXP{$prefix}";
            $baseBalKey = "JRNL2_EXPBASE{$prefix}";
        }
        
        $jrnl1_exp[0]['TOTAL'] = glFormatCurrency($jrnl1_exp[0]['TOTAL']);
        $jrnl1_exp[0]['BASETOTAL'] = glFormatCurrency($jrnl1_exp[0]['BASETOTAL']);
        
        $destObj[$balKey] = $jrnl1_exp;
        if ($showBaseGrid) {
            $destObj[$baseBalKey] = $jrnl1_exp;
        }
    }
    
    /**
     * Retrieves and sets AR balance in response
     *
     * @param int         $contractDetailKey Contract detail key
     * @param string      $jrnlCode Journal code
     * @param bool        $showBaseGrid 'true' if base currency balances are to be shown
     * @param string      $prefix Path prefix
     * @param array       $obj Source array
     * @param array       $destObj Destination array
     * @param string|null $asOfDate As of date
     * @param bool        $onlyInProgress Only include In progress contract lines
     * @param bool        $includeAllPeriods Include all periods of Evergreen line
     */
    public static function setARBalances(
        $contractDetailKey,
        $jrnlCode,
        $showBaseGrid,
        $obj,
        &$destObj,
        $prefix = "",
        $asOfDate = null,
        $onlyInProgress = false,
        $includeAllPeriods = true
    ) {
        $jrnl1_ar = ContractRevenueGLReclassEngine::getBalances(
            $contractDetailKey,
            'A',
            $jrnlCode,
            '',
            true,
            $asOfDate,
            $onlyInProgress,
            $includeAllPeriods
        );
        
        // Convert amounts into drill-down links to glledger report
        self::linkifyAmounts('A', $asOfDate, $jrnlCode, $obj, $jrnl1_ar, $onlyInProgress);
        
        if ($jrnlCode == 'J1') {
            $balKey = "JRNL1_AR{$prefix}";
            $baseBalKey = "JRNL1_ARBASE{$prefix}";
        } else {
            $balKey = "JRNL2_AR{$prefix}";
            $baseBalKey = "JRNL2_ARBASE{$prefix}";
        }
        
        $destObj[$balKey] = $jrnl1_ar;
        if ($showBaseGrid) {
            $destObj[$baseBalKey] = $jrnl1_ar;
        }
    }
}
