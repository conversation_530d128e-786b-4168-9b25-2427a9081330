<?
/**
 * File ContractModuleNLister.cls contains the class ContractModuleNLister
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2024 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class ContractModuleNLister extends NLister
{

    /**
     * @param array $_params
     */
    public function __construct($_params)
    {
        parent::__construct($_params);
    }

    /**
     * @param string $modulePrefix
     *
     * @return string
     */
    protected function getXgConfigPrefixModuleOverride($modulePrefix): string
    {
        if($modulePrefix == 'cn'){
            $modulePrefix = 'contract';
        }
        return $modulePrefix;
    }
}
