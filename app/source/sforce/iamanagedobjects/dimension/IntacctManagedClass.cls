<?php

/**
 * Class IntacctManagedClass
 *
 * Implementation of the IntacctManagedObject for Class.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2017 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class IntacctManagedClass extends IntacctManagedRecursiveObject
{
    /**
     * return the EntityManager::getList query parameters used to query classes from
     * the Intacct database.
     *
     * @return array
     */
    public function getQueryParams()
    {
        return [
            'recursive' => [
                'start' => [
                    [ 'PARENTKEY', 'is null' ],
                ],
            ],
            'usemst'    => true ];
    }

    /**
     * Field pointing to the parent reference.
     *
     * @return string
     */
    public function getIntacctParentReferenceField()
    {
        return 'RECORDNO';
    }
    /**
     * Returns the name of the subscription object.
     *
     * @return string
     */
    public function getSubscriptionObject()
    {
        return 'CLASS';
    }

    /**
     * Return the class id as the key field to identify the class.
     *
     * @return string[]
     */
    protected function getSforceKeyFields()
    {
        return [SforceNamespaceHandler::getPrefix() . 'Class_ID__c'];
    }

    /**
     * Add the class Id to the list of the queried fields.
     *
     * @return string[]
     */
    protected function getAdditionalSalesforceFields()
    {
        return [SforceNamespaceHandler::getPrefix() . 'Class_ID__c'];
    }

    /**
     * Returns the field name referencing the parent object.
     *
     * @return string
     */
    public function getParentIdField()
    {
        return 'PARENTID';
    }

    /**
     * Finds the sforceParent field from the object field mapping
     * based on the intacct parent id field and Sets the sforceParentField.
     */
    public function adjustSforceParentField()
    {
        $sforceParentField = array_search ($this->getParentIdField(), $this->fieldMapping);
        $this->setSforceParentField($sforceParentField);
    }

}