<?php

/**
 * Class IntacctManagedSalesDocumentSyncService
 *
 * Provides implementation routine for syncing intacct managed Sales Document objects.
 *
 * <AUTHOR> lokesh <<EMAIL>>
 * @copyright 2000-2021 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class IntacctManagedSalesDocumentSyncService extends IntacctManagedObjectSyncService
{

    /* @var  SforceClient $sfClient */
    private $sfClient;

    /* @var  bool $isAdmin */
    private $isAdmin = false;

    /* @var  IMSSubscriptionManager $subMgr */
    protected $subMgr;

    /* @var  stdClass|false $sfopportunitylist */
    protected $sfopportunitylist;

    /* @var  bool $sfismcp */
    protected $sfismcp;

    /**
     * @param IMSSubscriptionManager $subMgr
     */
    public function setSubMgr(IMSSubscriptionManager $subMgr): void
    {
        $this->subMgr = $subMgr;
    }

    /**
     * @return SforceClient
     */
    protected function getSfClient()
    {
        if (!$this->sfClient) {
            $this->sfClient = new SforceClient($this->getIsAdmin());
            if(isset($this->syncProcessor)){
                $this->sfClient->setSyncAdapter($this->syncProcessor->getSyncAdapter());
            }
        }
        return $this->sfClient;
    }

    /**
     * @return mixed
     */
    public function getIsAdmin()
    {
        return $this->isAdmin;
    }

    /**
     * @param bool $isAdmin
     */
    public function initialize($isAdmin = false)
    {
        $this->isAdmin = $isAdmin;
        $this->setSubMgr(new IMSSubscriptionManager());
        $this->getOpportunitylistFromSf();

    }

    /**
     * @return false|stdClass
     */
    public function getOpportunitylistFromSf()
    {
        if (!isset($this->sfopportunitylist)) {
            try {
                //where type not equal to contract
                $soqlQuery = "SELECT Id,Name,AccountId from Opportunity where RecordType.Name !='Contract'";
                $this->sfopportunitylist = $this->getSfClient()->queryFull($soqlQuery, false);
            } catch (Exception $e) {
                Globals::$g->gErr->addIAError('SFDC-1188',
                    __FILE__ . '.' . __LINE__,
                    'Salesforce Error : '.$e->getCode() . " " . $e->getMessage(),["E_GETCODE" => $e->getCode(), "E_GETMESSAGE" => $e->getMessage()]);
            }
        }
        return $this->sfopportunitylist;
    }

    /**
     * @param array $args
     * @return mixed
     */
    protected function prepareValues($args)
    {
        $args = $this->processOpportunityId($args);
        $kSFORCE2id = Globals::$g->kSALESFORCE2id;
        $this->sfismcp = GetPreferenceForProperty($kSFORCE2id, 'SFORCEISMCP');
        $args = $this->prepSODocument($args);
        return $args;
    }


    /**
     * Adds opportunity ID in $args
     *
     * @param string[][] $args
     *
     * @return string[][]
     */
    protected function processOpportunityId($args)
    {
        // if SFORCEID is not set (The opportunity value), check to see if there is a source document
        // if so, check to see if the source document has a linked opportunity.
        if ((!array_key_exists('SFORCEID', $args) || $args['SFORCEID'] == '')) {
            $oppid = $this->getOpportunityID($args);
            // If no opportunity link found within Intacct subscription,
            // It may be due to some error during _CreateSODocument,
            // SF document gets created without linked opportunity, in this case,
            // Check if we've opportunity reference stored on Intacct side (dochdr.PONUMBER),
            // and on SF side, we've this opportunity record available,
            // If so, link this document to its referenced opportunity
            if ($oppid == '' && array_key_exists('PONUMBER', $args) && $args['PONUMBER'] != '') {
                // retrive opportunity Id from SFDC
                $sfAccountExtId = $this->subMgr->GetExternalId(
                    $args['CUSTVENDID'], 'CUSTOMER', SforceNamespaceHandler::SUBSCRIBER_NAME
                );
                $oppid = $this->getSFOpportunityIdByName($sfAccountExtId, $args['PONUMBER']);
            }

            // populate Opportunity Id
            $args['SFORCEID'] = $oppid;
        }
        return $args;
    }

    /**
     * Get the related opportunity for a sales document
     * @param string[][] $sodocValues
     *
     * @return false|string SFDC key for the related opportunity or false if none exists
     */
    protected function getOpportunityID($sodocValues)
    {
        // Get the SODOCUMENTOPP subscription, value will be available in case of update

        $oppid = $this->subMgr->GetExternalId(
            $sodocValues['RECORDNO'],
            'SODOCUMENTOPP', SforceNamespaceHandler::SUBSCRIBER_NAME
        );

        if (!$oppid || $oppid == '') {
            if (array_key_exists('CREATEDFROM', $sodocValues) && $sodocValues['CREATEDFROM'] != '') {
                $fargs = array('classname' => 'SODocumentManager',
                    'function' => 'GetList',
                    'includefile' => 'SODocumentManager.cls',
                    'fargs' => array(
                        'params' => array(
                            'selects' => array('RECORDNO'),
                            'filters' => array(array(array('DOCID', '=', $sodocValues['CREATEDFROM']))),
                        )
                    ),
                    'retvalue' => ''
                );

                // Get the SODOCUMENTOPP subscription
                if (isset($fargs['retvalue'][0]) && $fargs['retvalue'][0] != '') {
                    $oppid = $this->subMgr->GetExternalId(
                        $fargs['retvalue'][0]['RECORDNO'], 'SODOCUMENTOPP',
                        SforceNamespaceHandler::SUBSCRIBER_NAME
                    );
                }
            }

            // if we still don't have an oppid, see if we can trace back to a parent document via recurring transactions
            if ($oppid == '') {
                $fargs = array('classname' => 'RecurDocumentManager',
                    'function' => 'GetRecurParentRecordNo',
                    'includefile' => 'RecurDocumentManager.cls',
                    'fargs' => array('ID' => $sodocValues['DOCID']),
                    'retvalue' => '');

                // Get the SODOCUMENTOPP subscription
                $oppid = $this->subMgr->GetExternalId(
                    $fargs['retvalue'], 'SODOCUMENTOPP',
                    SforceNamespaceHandler::SUBSCRIBER_NAME
                );
            }
        }

        return $oppid;
    }

    /**
     * @param string $sfAccountExtId
     * @param string $oppname
     *
     * @return string
     */
    protected function getSFOpportunityIdByName($sfAccountExtId, $oppname)
    {
        // bad request
        if (!$sfAccountExtId || !isset($oppname) || $oppname == '') {
            return '';
        }

        $opp = '';
        $oppList = $this->getOpportunitylistFromSf();

        if ($oppList != false) {
            foreach ($oppList->records as $oppObj) {
                if ($oppObj->AccountId == $sfAccountExtId && $oppObj->Name == $oppname) {
                    $opp = $oppObj->Id;
                }
            }
        }
        return $opp;
    }

    /**
     * @param array $args
     *
     * @return array
     */
    protected function prepSODocument($args)
    {
        $args['TOTAL'] = $this->getTotal($args);
        $args['SUBTOTAL'] = $this->getSubtotal($args['ENTRIES']);
        if ($this->sfismcp) {
            $args['BASETOTAL'] = $this->getBaseTotal($args);
            $args['BASESUBTOTAL'] = $this->getBaseSubTotal($args['ENTRIES']);
        }

        return $args;
    }

    /**
     * @param array $args
     *
     * @return int
     */
    private function getTotal($args)
    {
        $subTot = $this->getSubtotal($args['ENTRIES']);

        if (is_array($args) && array_key_exists('SUBTOTALS', $args) && count($args['SUBTOTALS']) > 0) {
            $totVal = '0';
            foreach ($args['SUBTOTALS'] as $total) {
                if ($this->sfismcp) {
                    $totVal = bcadd($totVal, $total['TRX_TOTAL']);
                } else {
                    $totVal = bcadd($totVal, $total['TOTAL']);
                }
            }
            $subTot = $subTot + $totVal;
        }
        return $subTot;
    }

    /**
     * @param array $args
     *
     * @return int
     */
    private function getBaseTotal($args)
    {
        $baseSubTot = $this->getBaseSubTotal($args['ENTRIES']);

        if (array_key_exists('SUBTOTALS', $args) && count($args['SUBTOTALS']) > 0) {
            // look for the subtotal with 'TOTAL'
            $baseTotVal = '0';
            foreach ($args['SUBTOTALS'] as $total) {
                $baseTotVal = bcadd($baseTotVal, $total['TOTAL']);
            }
            $baseSubTot = $baseSubTot + $baseTotVal;
        }

        return $baseSubTot;
    }

    /**
     * @param array $entries
     *
     * @return int
     */
    private function getSubtotal($entries)
    {
        $total = 0;
        foreach ($entries as $entry) {
            if ($this->sfismcp) {
                $total += $entry['TRX_VALUE'];
            } else {
                $total += $entry['UIVALUE'];
            }
        }
        return $total;
    }

    /**
     * @param array $entries
     *
     * @return int
     */
    private function getBaseSubTotal($entries)
    {
        $total = 0;
        foreach ($entries as $entry) {
            $total += $entry['TOTAL'];
        }
        return $total;
    }
}
