<?php
class RollbackOnSagePeopleEvent implements Event
{
    /**
     * @var array
     */
    private array $values;

    public function __construct()
    {
    }

    /**
     * @param array $values
     */
    public function setValues(array $values): void
    {
        $this->values = $values;
    }

    public function execute(string $eventType): bool
    {
        $sagePeopleSetupManager = \Globals::$g->gManagerFactory->getManager('sagepeoplesetup');
        $sagePeopleSetupManager->rollbackOnSalesforce($this->values);
        return true;
    }

    public function save(string $eventType): ?array
    {
        return [
            "args" => json_encode([
                "values" => $this->values
            ])
        ];
    }

    public function restore(array $args, string $eventType): void
    {
        $this->values = $args['values'];
    }
}