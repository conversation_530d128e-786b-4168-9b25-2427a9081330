<?

//	FILE:			SforceSetupManager.cls
//	AUTHOR:			<PERSON>
//	DESCRIPTION:	
//
//	(C)2004, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This dohcument contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//

require_once 'ModuleSetupManager.cls';
require_once 'ims_include.inc';
require_once 'SforceConstants.inc';
require_once 'BulkApiConstants.inc';

if (!defined('PROXY_PASSWORD')) {
    define('PROXY_PASSWORD', '************');
}

/**
 * Class SalesforceSetupManager
 */
class SalesforceSetupManager extends ModuleSetupManager
{
    const ARRAY_SEPARATOR = '#~#';
    const DIR_IN = 'IN';
    const DIR_OUT = 'OUT';
    const SFORCEADMIN = 'SFORCEADMIN';
    const USESFSANDBOX = 'USESFSANDBOX';
    const SFORCESTATUS = 'SFORCESTATUS';

    const SALESTRANSACTION = 'Sales Transaction';
    const CONTRACT = 'Contract';
    const EVERGREEN = 'Evergreen';

    const TERMTYPE_OPTION = 'TERMTYPE_OPTION';
    /* @var bool|null $useNamespace */
    private $useNamespace = null;

    /**
     * @var array
     */
    private static $selectiveSyncObjs = [
        IntacctManagedObject::GLACCOUNT,
        IntacctManagedObject::GLJOURNAL,
        IntacctManagedObject::UDD,
        IntacctManagedObject::STATACCOUNT,
        IntacctManagedObject::STATJOURNAL
    ];

    /**
     * @param array $params
     */
    function __construct($params = array())
    {
        $params['moduleKey'] = '61.SFDC2';
        parent::__construct($params);
    }

    /**
     * @return string
     */
    protected function getNamespaceHandler()
    {
        return 'SforceNamespaceHandler';
    }

    /**
     * Returns a decrypted password.
     *
     * @return false|string
     */
    public function getClearPassword()
    {
        $encryptPass = $this->GetPreference('SFORCEPWD');
        $clearPwd = TwoWayDecrypt($encryptPass);
        return $clearPwd;
    }

    /**
     * Returns salesforce module preferences.
     *
     * @param string        $id
     * @param string[]|null $fields
     *
     * @return array|false
     */
    function get($id, $fields = null)
    {
        $prefs = parent::get($id);

        $glAccounts = $this->getGLAccountPrefs();
        if (!empty($glAccounts)) {
            $prefs['GLACCOUNTDETAILFIELDS'] = $glAccounts;
        }

        $this->translatePrefs($prefs);
        return $prefs;
    }

    /**
     * Wrapper method calling the convertArrayPrefValue().
     *
     * @param array  $prefs
     * @param string $property
     * @param string $dir
     */
    private function convertArrayPref(&$prefs, $property, $dir)
    {
        if (isset($prefs[$property])) {
            $prefs[$property] = $this->convertArrayPrefValue($prefs[$property], $dir);
        }
    }

    /**
     * Wrapper method to translate preferences.
     *
     * @param array  $prefs
     */
    protected function translatePrefs(&$prefs)
    {
        // If DB pref for 'Update Bill to and Ship to address' and
        // 'Post detail transaction to Salesforce' are not set,
        // then default them to 'true' - meaning,
        // we'll continue to update billto and shipto contacts,
        // we'll continue to post header/lines/subtotals to SF
        if (!isset($prefs['SFORCEBILLINGSHIPPING'])) {
            $prefs['SFORCEBILLINGSHIPPING'] = 'true';
        }
        if (!isset($prefs['SFORCEBILLTOSHIPTO'])) {
            $prefs['SFORCEBILLTOSHIPTO'] = 'true';
        }
        if (!isset($prefs['CONTACTCATOGERY'])) {
            $prefs['CONTACTCATOGERY'] = 'A';
        }
        if (!isset($prefs['POSTDETAILTRANS'])) {
            $prefs['POSTDETAILTRANS'] = 'true';
        }
        if (!isset($prefs['POSTBILLTOSHIPTO'])) {
            $prefs['POSTBILLTOSHIPTO'] = 'false';
        }
        if (!isset($prefs['POSTPAYMENTDETAILS'])) {
            $prefs['POSTPAYMENTDETAILS'] = 'false';
        }
        $this->convertArrayPref($prefs, 'ACCTLAYOUTSECTIONS', self::DIR_OUT);
        $this->convertArrayPref($prefs, 'ACCTLAYOUTFIELDS', self::DIR_OUT);
        $this->convertArrayPref($prefs, 'ACCTCOMPONENTINFOS', self::DIR_OUT);
        $this->convertArrayPref($prefs, 'ACCTFIELDINFOS', self::DIR_OUT);
        $this->convertArrayPref($prefs, 'ACCTVALIDFIELDS', self::DIR_OUT);

        // Set the value
        //$isMcpEnabled = SalesforceUtil::isMultiCurrencyEnabled();
        //$prefs['SFORCEISMCP'] = $isMcpEnabled ? 'true' : 'false';
    }

    /**
     * @return string
     */
    public function getGLAccountPrefs(): string
    {
        $glAccounts = $this->getGLAccountFieldsByPattern();
        if (!empty($glAccounts)) {
            $glAccounts = $this->convertAccountForModulePrefWrapper("GLACCOUNTDETAILFIELDS", $glAccounts);
        }
        return $glAccounts;
    }
    /**
     * @return string
     */
    public function getGLAccountFieldsByPattern(): string
    {
        global $kSALESFORCE2id;
        $glAccounts = '';
        $glAccountList = $this->getPreferencesByPattern('GLACCOUNTDETAILFIELDS', $kSALESFORCE2id, true);
        if (is_array($glAccountList)) {
            $total = count($glAccountList);
            $count = 0;
            foreach ($glAccountList as $key => $value) {
                $glAccounts .= $value['VALUE'];
                if ($count < $total - 1) {
                    $glAccounts .= self::ARRAY_SEPARATOR;
                }
                $count++;
            }
        }
        return $glAccounts;
    }

    /**
     * Bi-directional conversion of the passed parameter depending on the
     * conversion direction:
     * - direction -> IN,  var -> string[] - converts array to a string
     * - direction -> OUT, var -> string   - breaks down the string into
     *   the array.
     *
     * @param string|string[] $val
     * @param string          $dir
     *
     * @return array|string
     */
    private function convertArrayPrefValue($val, $dir)
    {
        $retVal = $val;

        if (isset($val)) {
            if ($dir == self::DIR_IN) {
                if (is_array($val)) {
                    $retVal = implode(self::ARRAY_SEPARATOR, $val);  // make it string
                }
            } else {
                if (!is_array($val)) {
                    $retVal = explode(self::ARRAY_SEPARATOR, $val);  // make it array
                }
            }
        }

        return $retVal;
    }

    /**
     * Reads the property configured for the salesforce module.
     *
     * @param string $property
     * @param string $mod
     * @param bool   $forUpdate
     *
     * @return array|bool|string
     */
    function GetPreference($property, $mod = '', $forUpdate = false)
    {
        if($property === 'GLACCOUNTDETAILFIELDS') {
           return $this->getGLAccountPrefs();
        }
        $val = parent::GetPreference($property, $mod, $forUpdate);
        $val = $this->convertPrefValue($property, $mod, $val, self::DIR_OUT);

        return $val;
    }

    /**
     * Stores the value of the passed property into the modulepref table. If
     * the property is an array, calls the function that compiles a string from
     * the elements of that array separated by the specific delimiter.
     *
     * @param string          $property
     * @param string|string[] $value
     * @param bool            $predelete
     * @param string          $mod
     * @param string          $ins_updt
     * @param bool   $forceRoot    if true will always write pref to the root rather than the current entity
     *
     * @return bool
     */
    function SetPreference($property, $value, $predelete = true, $mod = '', $ins_updt = '', $forceRoot=false)
    {
        $value = $this->convertPrefValue($property, $mod, $value, self::DIR_IN);
        return parent::SetPreference($property, $value, $predelete, $mod, $ins_updt, $forceRoot);
    }

    /**
     * @param string $property
     * @param string $mod
     * @param string|string[] $val
     * @param string $dir
     *
     * @return array|string
     */
    private function convertPrefValue($property, $mod, $val, $dir)
    {
        if ($mod === '' || $mod === $this->_moduleKey) {
            switch ($property) {
                case 'ACCTLAYOUTSECTIONS':
                case 'ACCTLAYOUTFIELDS':
                case 'ACCTCOMPONENTINFOS':
                case 'ACCTFIELDINFOS':
                case 'ACCTVALIDFIELDS':
                case 'UDDDETAILFIELDS':
                    $val = $this->convertArrayPrefValue($val, $dir);
                    break;
            }
            return $val;
        }
        return $val;
    }

    /**
     * Un-subscribes salesforce integration.
     * 1. Drop all field mappings
     * 2. If salesforce is reachable do the following:
     *    - Schedule to delete all Reference objects in salesforce
     *    - Delete Intacct Configuration
     *    Otherwise (if not reachable):
     *    - Delete all subscription records directly
     * 3. Deletes all mapped users from Intacct AND from salesforce
     *    if it is reachable.
     * 4. Deletes company mapping in intacct AND Intacct Company Setting
     *    custom setting on salesforce if it is reachable.
     * 5. Set the subscription status to DEINSTALLED and drop all other
     *    module preferences.
     *
     * @return bool
     * @throws Exception
     * @throws IAException
     */
    function unsubscribe()
    {
        $isReachable = $this->isSalesforceOrgReachable();

        $source = "SalesforceSetupManager::cleanSalesforceSubscriptionOnIntacct";
        $ok = $this->_QM->beginTrx($source);
        $isAutocommitSet = $ok;

        // 1. Drop all field mappings in Intacct
        $partnerFieldMapManager = Globals::$g->gManagerFactory->getManager('partnerfieldmap');
        $ok = $ok && $partnerFieldMapManager->deleteAllMappings(IntacctPartner::SFORCE_V2);
        // 1.1 Also need to drop all field mappings in salesforce
        if ($ok) {
            $partnerFieldMapManager->deleteAllMappingsOnPartner();
        }

        if ($isReachable) {
            $prefs = $this->get('');
            if ($prefs['INTEGRATIONSTATUS'] === 'true') {
                // 2.1 Schedule to delete all Reference objects in salesforce ONLY if the
                // ENABLE SYNCHRONIZATION check box is selected
                $ok = $ok && SforceMessagePublisher::publishDeleteOnUnsubscribe();
            } else {
                // If ENABLE SYNCHRONIZATION check box is NOT selected
                // just Delete all subscription records directly
                $ok = $ok && $this->deleteHistoricalMappedObjects();
            }

            // 2.2. Delete Intacct Configuration
            $client = SalesforceUtil::getDefaultSforceClient();
            $ok = $ok && $this->deleteIntacctConfiguration($client);

        } else {
            // Delete all subscription records directly
            $ok = $ok && $this->deleteHistoricalMappedObjects();
        }

        // 3. Delete all mapped users
        /* @var OAuthUserManager $oauthUserManager */
        $oauthUserManager = Globals::$g->gManagerFactory->getManager('oauthuser');
        //set the module ID
        $oauthUserManager->setCallerModule($this->getModule());
        $ok = $ok && $oauthUserManager->unmapAllUsers($isReachable);

        // 4. Unmap company
        $ok = $ok && $this->unmapCompany($isReachable);

        // Set the subscription status as de-installed
        if ($ok) {
            $this->setSforceConfiurationStatus(
                SforceConfigurationStatus::SFORCE_STATUS_DEINSTALLED);
        }

        $userrec = explode('@', Globals::$g->_userid);
        $userpolicy = new UserPolicy('so',$userrec[0]);
        $curr = $userpolicy->GetUserModulePolicies();
        if (array_key_exists("Salesforce Synchronization Report",$curr)){
            $pk = $userpolicy->GetPolicyKey("Salesforce Synchronization Report");
            $ok = $ok && $userpolicy->DeleteUserPolicy($pk);
            $ok = $ok && $userpolicy->UpdatePermissionsFromPolicy();
        }

        if ($isAutocommitSet) {
            if ($ok) {
                $ok = $this->_QM->commitTrx($source);
            } else {
                $this->_QM->rollbackTrx($source);
            }
        }

        return $ok;
    }

    /**
     * Verifies if the salesforce org is reachable from intacct. The org is not
     * reachable in the following conditions:
     * 1. Cannot connect to the salesforce org with integration user.
     * 2. Connection to salesforce org with integration user IS established
     *    but number of records is not 1 in intacct configuration object.
     *
     * @param SforceClient|null|false $client
     *
     * @return bool
     * @throws Exception
     */
    final protected function isSalesforceOrgReachable(SforceClient $client = null)
    {
        $isReachable = false;
        if ($client === null) {
            $client = $this->getDefaultPartnerClient();
        }

        if ($client) {
            /** @var  SforceNamespaceHandler $namespaceHandler */
            $namespaceHandler = $this->getNamespaceHandler();
            $intacctConfiguration = $namespaceHandler::getIaconfObjectName();
            $configuration =
                $client->query("SELECT Id, Name FROM $intacctConfiguration");
            if (isset($configuration->records) && $configuration->totalSize == 1) {
                $isReachable = true;
            }
        }

        return $isReachable;
    }

    /**
     * @return false|SforceClient|null
     * @throws Exception
     */
    protected function getDefaultPartnerClient()
    {
        return SalesforceUtil::getDefaultSforceClient();
    }

    /**
     * Breaks the link between Intacct company and Salesforce Org
     *
     * @param bool $deleteOnSalesforce
     *
     * @return bool
     * @throws Exception
     * @throws IAException
     */
    final protected function unmapCompany($deleteOnSalesforce = true)
    {
        $ok = true;

        global $gManagerFactory;
        $moduleKey = $this->getModule();
        /* @var OAuthClientManager $oauthClientManager */
        $oauthClientManager = $gManagerFactory->getManager('oauthclient');

        $queryParams = array(
            'selects' => array('RECORDNO', 'OAUTH_CLIENTID', 'PARTNER_ORGID'),
            'filters' => [[['ACCESSTYPE', '=', $moduleKey]]]
        );
        $result = $oauthClientManager->GetList($queryParams);
        if ($result && count($result) > 1) {
            throw new IAException("Your company has been registered more than once in Intacct!!!");
        }
        if (isset($result[0]['PARTNER_ORGID'])) {
            $recordNo = $result[0]['RECORDNO'];
            $ok = $oauthClientManager->Delete($recordNo);
        }

        if ($deleteOnSalesforce) {
            $client = $this->getDefaultPartnerClient();
            $ok = $this->deleteCompanyCustomSetting($client) && $ok;
        }

        return $ok;
    }

    /**
     * Called on salesforce subscription.
     *
     * @param array $values
     *
     * @return bool|string
     * @throws IAException
     */
    protected function regularAdd(&$values)
    {
        global $kSALESFORCE2id;

        $ok = true;

        $this->setUseNamespace(true);

        if (!isArrayValueProvided($values, 'DEPLOYMENT_SITE')) {
            throw new IAException("SalesforceSetupManager::Add: Invalid deployment site");
        }

        $closeTransaction = false;
        $transactionId = __METHOD__;
        try {
            $status = $this->GetPreference('SFORCESTATUS', $kSALESFORCE2id);
            $isOrgRegistered = $this->isSfdcOrgRegistered($values);

            if ((SforceConfigurationStatus::SFORCE_STATUS_DEINSTALLED == $status
                    || !$status)
                && !$isOrgRegistered
            ) {

                $closeTransaction = $this->_QM->beginTrx($transactionId);

                $rollbackOnSalesforceEvent = new RollbackOnSalesforceEvent();
                $rollbackOnSalesforceEvent->setValues($values);
                XACT_REGISTER_POSTROLLBACK_EVENT($transactionId, $rollbackOnSalesforceEvent);

                // Unconditionally drop all the historical mapping records
                // from the subscription table
                $this->deleteHistoricalMappedObjects();

                $this->updateFieldMapping();

                $ok = $closeTransaction && $this->subscribeToSalesforce($values);

                // install module
                $ok = $ok && InstallModule(array('MODULEID' => $kSALESFORCE2id));

                $ok = $ok && $this->postRegistrationAckEmail();

                $ok = $ok && $this->addSenderIdToWhitelist();

                if ($ok === true) {
                    $ok = $this->_QM->commitTrx($transactionId);
                } else {
                    $msg = "Was not able to subscribe to Salesforce Integration module";
                    Globals::$g->gErr->addError('SFDC-1111', __FILE__ . ':' . __LINE__, $msg);

                    if ($closeTransaction === true) {
                        $this->_QM->rollbackTrx($transactionId);
                    }
                }

            } else {
                if (SforceConfigurationStatus::SFORCE_STATUS_CONFIGURED === $status) {
                    Globals::$g->gErr->addError('SFDC-1117',
                        __FILE__ . '.' . __LINE__,
                        'Your company has been already subscribed with Advanced CRM Integration.');
                }
            }
        } catch (IAException $e) {

            if ($closeTransaction === true) {
                // Exception happened after the transaction has been started
                $this->_QM->rollbackTrx($transactionId);
            }

            Globals::$g->gErr->addError(
                "SFDC-1114", __FILE__ . ":" . __LINE__,
                "Invalid Salesforce setup options.",
                'Generic Salesforce Error : '. $e->getMessage());
            $ok = false;
        }

        return $ok;
    }

    /**
     * Unconditionally drops all the historical records from the subscription table.
     *
     * @return bool
     */
    protected function deleteHistoricalMappedObjects()
    {
        /* @var SubscriptionManager $subscriptionManager */
        $subscriptionManager = Globals::$g->gManagerFactory->getManager('subscription');
        /** @var  SforceNamespaceHandler|SagePeopleNamespaceHandler $namespaceHandler */
        $namespaceHandler = $this->getNamespaceHandler();
        $ok = $subscriptionManager->deleteAllSubscriptions($namespaceHandler::SUBSCRIBER_NAME);

        return $ok;
    }

    /**
     * Event handler triggered at database connection rollback.
     *
     * @param array $values
     *
     * @return bool
     * @throws Exception
     * @throws IAException
     */
    public function rollbackOnSalesforce(array $values)
    {
        try {
            $admin = $values[static::SFORCEADMIN];
            $useSandbox = SalesforceUtil::isSalesforceSandboxInUse($values[static::USESFSANDBOX]);
            $client = $this->getPartnerClient(true, $admin, $useSandbox);

            $this->deleteCompanyCustomSetting($client);

            $this->deleteIntacctConfiguration($client);
        } catch (Exception $e) {
            logDiagnostics("ROLLBACK_ON_SALESFORCE",
                'Error in rolling back in salesforce - ' . $e->getMessage());
        }

        return true;
    }

    /*** TO DO - Fix error message ***/
    /**
     * Get the sender ID used by the Salesforce integration from the config
     *
     * @return string
     * @throws IAException
     */
    private function getSenderId()
    {
        $senderId = SalesforceUtil::getRequiredConfigProperty($this->senderId());
        if ( $senderId === false ) {
            throw new IAException('System is not configured properly, missing Salesforce sender configuration');
        }

        return $senderId;
    }

    /**
     * @return string
     */
    protected function senderId()
    {
        return SFDC_V2_SENDER_ID;
    }

    /**
     * Add the Sender ID used by the Salesforce integration into the company's whitelist, if it is not already
     *
     * @return bool
     * @throws Exception
     * @throws IAException
     */
    final protected function addSenderIdToWhitelist()
    {
        $senderId = $this->getSenderId();
        $description = $this->getSenderIdDesc();

        $manager = Globals::$g->gManagerFactory->getManager('senderidwhitelist');
        $ok = $manager->addOrActivateSenderIdInWhitelist($senderId, $description);

        return $ok;
    }

    /**
     * @return string
     */
    protected function getSenderIdDesc()
    {
        return 'Automatically added for use by the Advanced CRM Integration subscription';
    }

    /**
     * Checks if the company (org) has been subscribed to the salesforce
     * integration module and implements the subscription routine if not.
     * 1. Registers the company in the SFDC
     * 2. Stores the provided SF admin user name into the company preferences
     * 3. Creates a new record for OAuth client
     *
     * @param array $values
     *
     * @return bool
     */
    public function subscribeToSalesforce(& $values)
    {
        global $gErr;

        $ok = false;

        $sfOrgInfo = $this->assignSfdcOrgInfo($values);
        if (!empty($sfOrgInfo)) {
            // Assign all subscription based permission sets to admin
            $permissionSets = SalesforceUtil::getPermissionSetsBySubscriptions(false, false, false, false);
            $ok = SalesforceUtil::enableMultiplePermissionSets($values['SFORCEADMIN'], true, $permissionSets);

            // Initialize Intacct configuration by creating an object in salesforce
            $ok = $ok && $this->initIntacctConfiguration($values);

            if ($ok) {
                // Copy all reference objects to salesforce
                $this->queueCopyOnInitialSubscription();
            }

            try {
                $ok = $ok && $this->persistSubscriptionPreferences($values);

                $ok = $ok && $this->insertOauthClient($sfOrgInfo);

                if ($ok) {
                    // Change the status to in-process to prevent accessing the setup page
                    $this->setSforceConfiurationStatus(
                        SforceConfigurationStatus::SFORCE_STATUS_IN_PROGRESS);
                }
            } catch (Exception $e) {
                $gErr->addError('SFDC-1115', __FILE__ . ':' . __LINE__,
                    'Generic Salesforce Error : '. $e->getMessage());
                $ok = false;
            }

        }

        return $ok;
    }

    /**
     * The method is being called only once at the time of the initial
     * subscription and stores the following user provided parameters in the
     * database:
     * - SFORCEADMIN - salesforce user loging name of the designated salesforce
     *   admin.
     * - SFORCESTATUS - the status of integration, set to CONFIGURED
     * - INTEGRATIONSTATUS - set to true indicating that the module has been
     *   subscribed.
     * - DEVELOPMENT_SANDBOX - user selection of the development sandbox
     * - DEPLOYMENT_SITE - user selection for the deployment site
     *   (intacct.com / partner / ...)
     *
     * @param array $values
     *
     * @return bool
     */
    final protected function persistSubscriptionPreferences(& $values)
    {
        // For API call typecasting boolean value to string
        $values['INTEGRATIONSTATUS'] =  ($values['INTEGRATIONSTATUS'] ?? false) ? 'true' : 'false';
        $values['ISFIELDMAP'] = ($values['ISFIELDMAP'] ?? false) ? 'true' : 'false';

        $module = $this->getModule();
        $ok = $this->SetPreference(
            static::SFORCEADMIN, $values[static::SFORCEADMIN], true, $module);

        $ok = $ok && $this->SetPreference(
                'INTEGRATIONSTATUS', $values['INTEGRATIONSTATUS'], true, $module);

        $ok = $ok && $this->SetPreference(
                'DEVELOPMENT_SANDBOX', $values['DEVELOPMENT_SANDBOX'], true, $module);

        $ok = $ok && $this->SetPreference(
                'DEPLOYMENT_SITE', $values['DEPLOYMENT_SITE'], true, $module);

        $ok = $ok && $this->SetPreference(
                static::USESFSANDBOX, $values[static::USESFSANDBOX], true, $module);

        $ok = $ok && $this->SetPreference(
                'ISFIELDMAP', $values['ISFIELDMAP'], true, $module);

        return $ok;
    }

    /**
     * The method deletes a record in the 'Intacct Company Settings'
     * protected custom setting in the salesforce database.
     *
     * @param SforceClient $client
     *
     * @return bool
     */
    public function deleteCompanyCustomSetting(SforceClient $client)
    {
        $ret = true;

        // Company setting name is always Intacct
        $companySettingName = SFORCE_INTACCT_CNY_SETTINGS_NAME;

        // Read clientId from the Salesforce database.
        $intacctInfo = $client->queryCustomSetting(
            SforceCustomSetting::CUSTOM_SETTING_COMPANY, $companySettingName);

        if ($intacctInfo !== false && $intacctInfo->success !== false && isset($intacctInfo[0])) {
            $clientId = $intacctInfo[0]->Id;

            $ret = $client->deleteCustomSetting(SforceCustomSetting::CUSTOM_SETTING_COMPANY, $clientId);
        } else {
            logDiagnostics("DELETE_COMPANY_CUSTOM_SETTING",
                'Company Intacct Configuration is not specified or specifed incorrectly!!!');
        }

        return $ret;
    }

    /**
     * Checks if the company has been registered in both Intacct and SFDC.
     *
     * @param array $values

     * @return bool  returns true if matching records can be found in both
     *               Intacct and Salesforce databases.
     *               returns false if no record can be found in either
     *               Intact or Salesforce databases.
     * @throws Exception
     * @throws IAException throws exception if following cases:
     *                     The company has been registered more than once
     *                     in Intacct.
     *                     - There are NON-matching records in both databases.
     *                     - In case when the record is found in one database
     *                     but not in the other.
     */
    public function isSfdcOrgRegistered($values)
    {
        global $gManagerFactory;
        $modulekey = $this->getModule();

        $sfdcAdminUser = $values[static::SFORCEADMIN];
        $isSforceSandboxInUse = SalesforceUtil::isSalesforceSandboxInUse($values[static::USESFSANDBOX]);

        $intacctStoredClientId = null;
        $sforceStoredClientId = null;

        // Read clientId from the Intacct database.
        /* @var OAuthClientManager $oauthClientManager */
        $oauthClientManager = $gManagerFactory->getManager('oauthclient');
        $queryParams = array(
            'selects' => array('OAUTH_CLIENTID'),
            'filters' => [[['ACCESSTYPE', '=', $modulekey]]]
        );
        $result = $oauthClientManager->GetList($queryParams);
        if ($result && count($result) > 1) {
            throw new IAException("Your company has been registered more than once in Intacct!!!");
        }
        if (isset($result[0]['OAUTH_CLIENTID'])) {
            $intacctStoredClientId = $result[0]['OAUTH_CLIENTID'];
        }

        $sfClient = $this->getPartnerClient(true, $sfdcAdminUser, $isSforceSandboxInUse);

        if (!$sfClient || !$sfClient->getSfUserSession()) {
            throw new IAException(
                "Invalid user or session. Cannot connect to Salesforce.",
                SforceError::INVALID_ADMIN);
        }

        // Company setting name is always Intacct
        $companySettingName = SFORCE_INTACCT_CNY_SETTINGS_NAME;

        // Read clientId from the Salesforce database.
        $intacctInfo = $sfClient->queryCustomSetting(
            SforceCustomSetting::CUSTOM_SETTING_COMPANY, $companySettingName);
        if (isset($intacctInfo->success) && !$intacctInfo->success) {
            // The query for a custom record returns NOT-SUCCESS
            if (SforceError::NOT_FOUND !== $intacctInfo->errors[0]->errorCode) {
                // Unsupported ERROR response received from salesforce
                throw new IAException($intacctInfo->errors[0]->message);
            }
        } else {
            // The query was successful. Check for returned clientId
            /** @var  SforceNamespaceHandler $namespaceHandler */
            $namespaceHandler = $this->getNamespaceHandler();
            $oathClientField = $namespaceHandler::getCnyOauthClientIdField();
            if (isset($intacctInfo[0]->$oathClientField)) {
                $sforceStoredClientId = $intacctInfo[0]->$oathClientField;
            }
        }

        if (!is_null($intacctStoredClientId) && !is_null($sforceStoredClientId)
            && $intacctStoredClientId == $sforceStoredClientId) {
            // positive use case. Matching records are found.
            $ret = true;
        }
        else if (is_null($intacctStoredClientId) && is_null($sforceStoredClientId)) {
            // negative use case. no record in either database
            $ret = false;
        }
        else if (!is_null($intacctStoredClientId) && !is_null($sforceStoredClientId)
            && $intacctStoredClientId != $sforceStoredClientId) {
            // ERROR CASE. There are NON-matching records in both databases
            throw new IAException("System is not configured properly, records at salesforce and Indacct do not match.");
        } else {
            // ERROR CASE. There is a record in Salesforce but not in Intacct
            throw new IAException(
                "SFDC org is not configured properly, no matching Intacct company has been found.",
                SforceError::NO_MATCHING_INTACCT);
        }

        return $ret;
    }

    /**
     * Inserts the record for OAuthClient into the database.
     *
     * @param string[] $sfOrgInfo SFDC org credentials:
     *                            - CLIENT_ID
     *                            - SFDC_ORG_SETTING_ID
     *
     * @return bool
     */
    final protected function insertOauthClient($sfOrgInfo)
    {
        if (is_null($sfOrgInfo) || empty($sfOrgInfo)) {
            throw new InvalidArgumentException(
                "SalesforceSetupManager::insertOauthClient: SFDC org info are not provided.");
        }
        if (!isset($sfOrgInfo['SFDC_ORG_SETTING_ID'])) {
            throw new InvalidArgumentException(
                "SalesforceSetupManager::insertOauthClient: SFDC org ID is not provided.");
        }
        if (!isset($sfOrgInfo['OAUTH_CLIENTID'])) {
            throw new InvalidArgumentException(
                "SalesforceSetupManager::insertOauthClient: SFDC client ID is not provided.");
        }
        global $gManagerFactory;
        $oauthClient = [];
        $oauthClient['OAUTH_CLIENTID'] = $sfOrgInfo['OAUTH_CLIENTID'];
        $oauthClient['PARTNER_ORGID'] = $sfOrgInfo['SFDC_ORG_SETTING_ID'];
        $oauthClient['ACCESSTYPE'] = $this->getModule();

        /* @var OAuthClientManager $oauthClientManager */
        $oauthClientManager = $gManagerFactory->getManager('oauthclient');
        $ok = $oauthClientManager->add($oauthClient);
        return $ok;
    }

    /**
     * The function is called upon subscribing to salesforce.com and implements
     * the following tasks:
     * 1. Generates client Id for company
     * 2. Pushes the generated id to the SFDC
     *
     * @param array $values
     *
     * @return string[] returns an array with generated client ID and SFDC org
     *                 record ID if successful and the empty array otherwise.
     */
    private function assignSfdcOrgInfo(& $values)
    {
        $ret = [];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $sfOrgId = null;
        $oauthClientId = OAuthBase::generateClientId();

        $sfdcAdminUser = $values['SFORCEADMIN'];
        $sforceSandboxOrg = $values['USESFSANDBOX'];

        $isSforceSandboxInUse = SalesforceUtil::isSalesforceSandboxInUse($sforceSandboxOrg);

        if (!$this->isSalesforceAdminUserValid($sfdcAdminUser, $isSforceSandboxInUse)) {
            global $gErr;

            $msg = "Was not able to register SFDC org. The admin user in not valid.";
            $gErr->addError('SFDC-1112', __FILE__ . ':' . __LINE__, $msg);
        } else {
            $recordId = $this->createSforceOrgInfo($values, $oauthClientId);
            if ($recordId) {
                // The salesforce org has been successfully mapped
                $this->persistDevSandbox($values, $oauthClientId);

                $this->setSalesforceOrgId($sfdcAdminUser, $isSforceSandboxInUse);

                $ret['OAUTH_CLIENTID'] = $oauthClientId;
                $ret['SFDC_ORG_SETTING_ID'] = $recordId;
            }
        }

        return $ret;
    }

    /**
     * Stores the user provided value for development sandbox to salesforce
     * and to Intacct database.
     *
     * @param array       $values
     * @param string|null $oauthClientId optional parameter. If not provided it
     *                                   has to be stored in the database
     *                                   previously.
     *
     * @return bool
     * @throws IAException
     */
    protected function persistDevSandbox($values, $oauthClientId = null)
    {
        global $kSALESFORCE2id;

        if (is_null($oauthClientId)) {
            $gManagerFactory = Globals::$g->gManagerFactory;
            // Read the salesforce internal record Id

            /* @var OAuthClientManager $oathClientManager */
            $oauthClientManager = $gManagerFactory->getManager('oauthclient');
            $queryParams = array(
                'selects' => array('PARTNER_ORGID', 'OAUTH_CLIENTID'),
                'filters' => [[['ACCESSTYPE', '=', $kSALESFORCE2id]]]
            );
            $result = $oauthClientManager->GetList($queryParams);
            if ($result && count($result) !== 1) {
                throw new IAException("Your company has not been subscribed to salesforce integration v.2!!!");
            }
            if (isset($result[0]['OAUTH_CLIENTID'])) {
                $oauthClientId = $result[0]['OAUTH_CLIENTID'];
            }
        }

        include 'development-sandboxes.inc';
        $devSandbox = $values['DEVELOPMENT_SANDBOX'];
        if (!isArrayValueProvided($values, 'DEVELOPMENT_SANDBOX')) {
            // Development sandbox is not provided. Drop it from preferences
            $this->DeletePreference('DEVELOPMENT_SANDBOX', $kSALESFORCE2id);
            $sfDevSandbox = '';
        } else {
            /** @noinspection PhpUndefinedVariableInspection */
            $sfDevSandbox = $sandboxes[$devSandbox]['xmlgw'];
        }

        if (self::isDevelopmentDeployment()) {
            // Updating xmlgw endpoint is only relevant for updating
            // setup configuration for DEVELOPMENT Deployment
            $deploymentSite = $values['DEPLOYMENT_SITE'];
            $xmlBaseApi = self::translateXmlBaseApi($deploymentSite);
            $deploymentSitePostingData =
                '", "' . SforceNamespaceHandler::getCnyXmlBaseApiField() . '": "' . $xmlBaseApi;
        } else {
            $deploymentSitePostingData = '';
        }

        $sfdcAdminUser = $values['SFORCEADMIN'];
        $isSforceSandboxInUse = SalesforceUtil::isSalesforceSandboxInUse($values['USESFSANDBOX']);
        $client = $this->getPartnerClient(true, $sfdcAdminUser, $isSforceSandboxInUse);

        $postingData = '{ "' . SforceNamespaceHandler::getCnyDevSandbox() . '" : "' . $sfDevSandbox
            . '", "' . SforceNamespaceHandler::getCnyOauthClientIdField() . '": "' . $oauthClientId
            . $deploymentSitePostingData
            . '" }';
        $result = $client->upsertCustomSetting(
            SforceCustomSetting::CUSTOM_SETTING_COMPANY, $postingData);
        $ok = $result->success;
        if (!$result->success) {
            global $gErr;
            $msg = "Cannot push development sandbox to the salesforce";
            $gErr->addError('SFDC-1113', __FILE__ . ':' . __LINE__, $msg);

            if (is_array($result->errors) && count($result->errors) > 0) {
                logToFileError("createSforceOrgInfo->errorCode: " . $result->errors[0]->errorCode);
                logToFileError("createSforceOrgInfo->errorMessage: " . $result->errors[0]->message);
            } else {
                logToFileError("createSforceOrgInfo: verify your salesforce org!");
            }
        }

        return $ok;
    }

    /**
     * The method creates creates a record in the 'Intacct Company Settings'
     * protected custom setting in the salesforce.
     *
     * @param array  $values
     * @param string $oauthClientId
     *
     * @return bool|string
     */
    public function createSforceOrgInfo($values, $oauthClientId)
    {
        $ret = false;

        /*$setupValues = $this->getSetupValues($values);

        $sfdcAdminUser = $setupValues['AdminUser'];
        $isSforceSandboxInUse = $setupValues['IsSandboxInUse'];
        $deploymentSite = $setupValues['DeploymentSite'];*/

        $sfdcAdminUser = $values[static::SFORCEADMIN];
        $isSforceSandboxInUse = SalesforceUtil::isSalesforceSandboxInUse($values[static::USESFSANDBOX]);
        $deploymentSite = $values['DEPLOYMENT_SITE'];

        // Create a client based on provided salesforce admin username
        $client = $this->getPartnerClient(true, $sfdcAdminUser, $isSforceSandboxInUse);
        if ($client) {
            // Read the endpoint for the user selection
            $xmlBaseApi = self::translateXmlBaseApi($deploymentSite);

            // Read the salesforce sender ID from the configuration file
            // and the corresponding shared secret from the database
            $senderId = $this->getSenderId();

            /* @var imsPartnerInfoManager $imsPartnerInfoManager */
            $imsPartnerInfoManager =
                Globals::$g->gManagerFactory->getManager('imspartnerinfo');

            $allCredentials = $imsPartnerInfoManager->get($senderId);
            if ($allCredentials) {
                $sharedSecret = TwoWayDecryptWithKey($allCredentials['SHAREDSECRECT'], "IA_INIT");

                $cnyCustomSettingName = SFORCE_INTACCT_CNY_SETTINGS_NAME;
                $companyName = GetMyCompanyTitle();
                /** @var  SforceNamespaceHandler $namespaceHandler */
                $namespaceHandler = $this->getNamespaceHandler();

                $postingData = '{ "' . SforceCompanySettings::SFORCE_CNY_NAME . '" : "' . $cnyCustomSettingName
                    . '", "' . $namespaceHandler::getCnyOauthClientIdField() . '": "' . $oauthClientId
                    . '", "' . $namespaceHandler::getCnyTitleField() . '": "' . $companyName
                    . '", "' . $namespaceHandler::getCnySenderIdField() . '": "' . $senderId
                    . '", "' . $namespaceHandler::getCnyXmlBaseApiField() . '": "' . $xmlBaseApi
                    . '", "' . $namespaceHandler::getCnySharedSecretField() . '": "' . $sharedSecret
                    . '" }';
                $result = $client->upsertCustomSetting(
                    SforceCustomSetting::CUSTOM_SETTING_COMPANY, $postingData);
                if ($result->success) {
                    $ret = $result->id;
                } else {
                    if (is_array($result->errors) && count($result->errors) > 0) {
                        LogToFile("createSforceOrgInfo->errorCode: " . $result->errors[0]->errorCode);
                        LogToFile("createSforceOrgInfo->errorMessage: " . $result->errors[0]->message);
                    } else {
                        LogToFile("createSforceOrgInfo: verify your salesforce org!");
                    }
                }
            } else {
                Globals::$g->gErr->addError("SFDC-1116",
                    __FILE__ . '.' . __LINE__,
                    'Cannot find a record for a partner with the given sender ID',
                    "Please ask your admin for help.");
            }
        }

        return $ret;
    }

    /**
     * @param array $values
     *
     * @return array
     */
    /*protected function getSetupValues($values)
    {
        $setupValues['AdminUser'] = $values['SFORCEADMIN'];;
        $setupValues['IsSandboxInUse'] = SalesforceUtil::isSalesforceSandboxInUse($values['USESFSANDBOX']);
        $setupValues['DeploymentSite']  = $values['DEPLOYMENT_SITE'];
        return $setupValues;

    }*/

    /**
     * Posts registration acknowledgement email.
     *
     * @return bool
     */
    final protected function postRegistrationAckEmail()
    {
        global $ims_live;
        $partnerName = $this->getPartnerName();
        $emailaddr = ($ims_live) ? "<EMAIL>" : "<EMAIL>";//"<EMAIL>";
        $emailsubj = "A Client has Subscribed to $partnerName integration";
        $emailtext = "The company " . GetMyCompanyTitle() . ": " . GetMyCompanyName() . " has subscribed to the $partnerName integration";
        $objEmail = new IAEmail($emailaddr);
        $objEmail->subject = $emailsubj;
        $objEmail->body = $emailtext;
        $ok = $objEmail->send();

        return $ok;
    }

    /**
     * @return string
     */
    protected function getPartnerName()
    {
        return 'Salesforce.com';
    }


    /**
     * Checks if the given salesforce username is a valid one for the
     * salesforce admin.
     *
     * @param string $sfAdminUser
     * @param bool   $sforceSandboxOrg
     *
     * @return bool
     */
    public function isSalesforceAdminUserValid($sfAdminUser, $sforceSandboxOrg)
    {
        $sforceSession = SforceInterface::validateAdminUser($sfAdminUser, $sforceSandboxOrg);
        if (is_string($sforceSession)) {
            $ret = false;
        } else {
            $ret = $sforceSession->getSession()->getSforceId() === $sfAdminUser;
        }
        return $ret;
    }

    /**
     * Returns salesforce posting transaction detail data for the
     * TD with the given docid.
     *
     * @param string $docid
     *
     * @return SforcePostingTxnDetails|false
     */
    public function getPostingDetailsForDocpar($docid)
    {
        $ret = false;
        $docPars = $this->getSforcePostingTxnDetails();
        /* @var SforcePostingTxnDetails $docPar */
        foreach ($docPars as $docPar) {
            if ($docPar->getTxnDocument() === $docid) {
                $ret = $docPar;
                break;
            }
        }
        return $ret;
    }

    /**
     * Returns the salesforce posting transaction details as a collection of
     * SforcePostingTxnDetails objects.
     *
     * Each transaction stored in the modulepref table under the property of
     * SYNCDOCUMENTTYPE#xxx (where xxx is just a number to make the proerty
     * unique) and SYNCDOCUMENTTYPEDETAIL#<transaction-name>, where
     * <transaction-name> matches the value of the SYNCDOCUMENTTYPE#xxx record.
     *
     * @return array
     */
    public function getSforcePostingTxnDetails()
    {
        global $kSALESFORCE2id;

        $docPars = $this->getPreferencesByPattern('SYNCDOCUMENTTYPE#', $kSALESFORCE2id, true);
        $docParsIncludeDetails = $this->getPreferencesByPattern('SYNCDOCUMENTTYPEDETAIL#', $kSALESFORCE2id, true);
        $docParsAllowFromSforce = $this->getPreferencesByPattern('ALLOWFROMSALESFORCE#', $kSALESFORCE2id, true);
        $docParsPostToSforce = $this->getPreferencesByPattern('POSTTOSALESFORCE#', $kSALESFORCE2id, true);

        $ret = self::getSforcePostingTxnDetailsInternal($docPars,
            $docParsIncludeDetails, $docParsAllowFromSforce, $docParsPostToSforce);

        return $ret;
    }

    /**
     * Returns salesforce posting transaction detail data for the
     * TD with the given docid.
     *
     * @param string $docid
     *
     * @return mixed
     */
    public function getPostingDetailsForSalesDocpar(string $docid) : mixed
    {
        $ret = false;
        $docPars = $this->getSforcePostingTxnSalesDocDetails();
        /* @var SforcePostingTxnDetails $docPar */
        foreach ($docPars as $docPar) {
            if ($docPar->getTxnDocument() === $docid) {
                $ret = $docPar;
                break;
            }
        }
        return $ret;
    }

    /**
     * Returns the salesforce posting transaction details as a collection of
     * SforcePostingTxnDetails objects.
     *
     * Each transaction stored in the modulepref table under the property of
     * V2SYNCDOCUMENTTYPE#xxx (where xxx is just a number to make the proerty
     * unique) and V2SYNCDOCUMENTTYPEDETAIL#<transaction-name>, where
     * <transaction-name> matches the value of the SYNCDOCUMENTTYPEV2#xxx record.
     *
     * @return array
     */
    public function getSforcePostingTxnSalesDocDetails() : array
    {
        $kSALESFORCE2id = Globals::$g->kSALESFORCE2id;

        $docPars = $this->getPreferencesByPattern('V2SYNCDOCUMENTTYPE#', $kSALESFORCE2id, true);
        $docParsIncludeDetails = $this->getPreferencesByPattern('V2SYNCDOCUMENTTYPEDETAIL#', $kSALESFORCE2id, true);
        $docParsAllowFromSforce = $this->getPreferencesByPattern('V2ALLOWFROMSALESFORCE#', $kSALESFORCE2id, true);
        $docParsPostToSforce = $this->getPreferencesByPattern('V2POSTTOSALESFORCE#', $kSALESFORCE2id, true);

        $ret = self::getSforcePostingTxnDetailsInternal($docPars,
            $docParsIncludeDetails, $docParsAllowFromSforce, $docParsPostToSforce);

        return $ret;
    }

    /**
     * @param string[][] $docPars
     * @param string[][] $docParsIncludeDetails
     * @param string[][] $docParsAllowFromSforce
     * @param string[][] $docParsPostToSforce
     *
     * @return SforcePostingTxnDetails[]
     */
    private static function getSforcePostingTxnDetailsInternal($docPars,
                                                               $docParsIncludeDetails, $docParsAllowFromSforce, $docParsPostToSforce)
    {
        $ret = [];
        if (!empty($docPars)) {
            foreach ($docPars as $docPar) {
                $tdName = $docPar['VALUE'];
                $includeLineItemDetail = self::getDocparProperty($docParsIncludeDetails, $tdName);
                $allowFromSforce = self::getDocparProperty($docParsAllowFromSforce, $tdName);
                $postToSforce = self::getDocparProperty($docParsPostToSforce, $tdName);

                $ret[] = new SforcePostingTxnDetails($tdName, $includeLineItemDetail, $allowFromSforce, $postToSforce);
            }
        }

        return $ret;
    }

    /**
     * Retrieves a specific document property by transaction definition name.
     *
     * @param array  $docparProperties collection of properties for all TDs.
     * @param string $tdName           a TD for which the property is being
     *                                 retrieved.
     *
     * @return bool
     */
    private static function getDocparProperty($docparProperties, $tdName)
    {
        $docparProperty = false;
        foreach ($docparProperties as $detail) {
            $detailArr = explode('#', $detail['PROPERTY']);
            if ($tdName === $detailArr[1] && 'T' === $detail['VALUE']) {
                $docparProperty = true;
                break;
            }
        }
        return $docparProperty;
    }

    /**
     * Stores user preferences for salesforce posting transaction details.
     *
     * @param string[][] $details
     *
     * @return bool
     */
    public function setSforcePostingTxnDetails($details)
    {
        global $kSALESFORCE2id;
        $ok = true;

        // delete all the existing TD ordinal numbers first
        $this->deletePreferencesLike("SYNCDOCUMENTTYPE%", $kSALESFORCE2id);

        $counter = 1;
        foreach ($details as $detail) {
            $ok = $ok && $this->SetPreference(
                    'SYNCDOCUMENTTYPE#' . $counter, $detail['SFORCESODOCPAR'], true,
                    $kSALESFORCE2id
                );

            $ok = $ok && $this->SetPreference(
                    'SYNCDOCUMENTTYPEDETAIL#' . $detail['SFORCESODOCPAR'],
                    ($detail['SFORCEINCLUDEDETAILS'] == 'true' ? 'T' : 'F'), true,
                    $kSALESFORCE2id
                );

            $ok = $ok
                && $this->SetPreference(
                    'ALLOWFROMSALESFORCE#' . $detail['SFORCESODOCPAR'],
                    ($detail['ALLOWFROMSALESFORCE'] == 'true' ? 'T' : 'F'),
                    true,
                    $kSALESFORCE2id
                );

            $ok = $ok
                && $this->SetPreference(
                    'POSTTOSALESFORCE#' . $detail['SFORCESODOCPAR'],
                    ($detail['POSTTOSALESFORCE'] == 'true' ? 'T' : 'F'), true,
                    $kSALESFORCE2id
                );

            $counter++;
        }
        return $ok;
    }

    /**
     * Stores user preferences for salesforce posting transaction details.
     *
     * @param string[][] $details
     *
     * @return bool
     */
    public function setSforcePostingTxnSalesDocV2Details(array $details) : bool
    {
        $kSALESFORCE2id = Globals::$g->kSALESFORCE2id;
        $ok = true;

        // delete all the existing TD ordinal numbers first
        $this->deletePreferencesLike("V2SYNCDOCUMENTTYPE%", $kSALESFORCE2id);

        $counter = 1;
        foreach ($details as $detail) {
            $ok = $ok && $this->SetPreference(
                    'V2SYNCDOCUMENTTYPE#' . $counter, $detail['V2SFORCESODOCPAR'], true,
                    $kSALESFORCE2id
                );

            $ok = $ok && $this->SetPreference(
                    'V2SYNCDOCUMENTTYPEDETAIL#' . $detail['V2SFORCESODOCPAR'],
                    ($detail['V2SFORCEINCLUDEDETAILS'] == 'true' ? 'T' : 'F'), true,
                    $kSALESFORCE2id
                );

            $ok = $ok
                && $this->SetPreference(
                    'V2ALLOWFROMSALESFORCE#' . $detail['V2SFORCESODOCPAR'],
                    ($detail['V2ALLOWFROMSALESFORCE'] == 'true' ? 'T' : 'F'),
                    true,
                    $kSALESFORCE2id
                );

            $ok = $ok
                && $this->SetPreference(
                    'V2POSTTOSALESFORCE#' . $detail['V2SFORCESODOCPAR'],
                    ($detail['V2POSTTOSALESFORCE'] == 'true' ? 'T' : 'F'), true,
                    $kSALESFORCE2id
                );

            if($ok === false){ break; }

            $counter++;
        }
        return $ok;
    }

    /**
     * Called on salesforce setup page submit.
     *
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(& $values)
    {
        $source = 'SalesforceSetupManager::Set';

        $this->preprocessPreferences($values);

        $validator = new SalesforceSetupValidator($this);
        $ok = $validator->validate($values);

        if ($ok) {
            $ok = $this->_QM->beginTrx($source);

            $needStatusUpdate = $ok && $this->queueCopyReferenceObjects($values);

            $ok = $ok && $this->persistSforceAdmin($values);

            $enableMigrationSOV2 = $this->GetPreference('ENABLEMIGRATIONSODOCV2');
            $ok = $ok && $this->persistPreferences($values);

            // if new flow is enabled and feature flag is off then switch back to olf flow
            $this->checkSODocV2Enabled();
            if (($values['ENABLESODOCV2SYNC'] ?? null) === "true") {
                $loopheaderAndLine = [[ 'INTACCTOBJECT'=>'SODOCUMENTV2'],[ 'INTACCTOBJECT'=>'SODOCUMENTENTRYV2']];
                foreach ($loopheaderAndLine as $loop){
                    $ok = $ok && $this->postFldMapSoDocV2Migration($values, $enableMigrationSOV2, $loop);
                }

                $ok = $ok && $this->setSforcePostingTxnSalesDocV2Details($values['NEW_SO_DOC_TXN_POSTING']);
            } else {
                $ok = $ok && $this->setSforcePostingTxnDetails($values['SFORCE_TXN_POSTING_DETAILS']);
            }

            $ok = $ok && $this->updateSalesforcePreferences($values);

            $ok = $ok && $this->persistDevSandbox($values);

            $this->addAuditLog($values); // Audit Log support

            if ($ok && $needStatusUpdate) {
                // Change the status to in-process to prevent accessing the setup page
                // 1. The ENABLE SYNCHRONIZATION check box is selected
                // 2. At least once eligible object is selected for synchronization for the first time.
                $this->setSforceConfiurationStatus(
                    SforceConfigurationStatus::SFORCE_STATUS_IN_PROGRESS);
            }

            $ok = $ok && $this->adjustUserPermissionSets($values);
            if($ok) {
                $this->persistFieldMapping('CUSTOMER', 'Account', 'SFORCE2', 'F');
                $this->persistFieldMapping('CONTACT', 'Contact', 'SFORCE2', 'F');
            }
            $ok = $ok && $this->_QM->commitTrx($source);
            if (!$ok) {
                $this->_QM->rollbackTrx($source);
            }
        }

        return $ok;
    }

    /**
     * Persist Fieldmapping details on Initial Sync.
     *
     * @param string $intacctObject
     * @param string $partnerObject
     * @param string $partner
     * @param string $master
     * @throws IAException
     */
    public function persistFieldMapping($intacctObject, $partnerObject, $partner, $master)
    {
        $partnerService = PartnerFieldMapService::getPartnerFieldMapService($partner);
        $partnerFieldMapManager = $partnerService->getPartnerFieldMapManager($partner);

        try {
            $filter = [
                'selects' => [
                    'INTACCTFIELD', 'PARTNERFIELD'
                ],
                'filters' => [
                    [
                        ['INTACCTOBJECT', '=', $intacctObject],
                        ['PARTNER', '=', $partner],
                        ['PARTNEROBJECT', '=', $partnerObject]
                    ]
                ]
            ];
            $mappedFields=$partnerFieldMapManager->GetList($filter);
            if(count($mappedFields)==0){
                $dataSource['MASTER'] = $master;
                $dataSource['PARTNER'] = $partner;
                $dataSource['INTACCTOBJECT'] = $intacctObject;
                $dataSource['INTACCT_OBJECT'] = $intacctObject;
                $dataSource['PARTNEROBJECT'] = $partnerObject;

                $partnerService->initializeMappingInitParams($dataSource);
                $dataSource['PARTNER_OBJECT'] = $partnerService->getMappingInitParams()->getPartnerObject();
                $dataSource['CUSTOM_MAPPING_DEFAULT_SYNC_RULE'] = SforceSyncRule::convertToSforceSyncRule($master);

                $partnerService->populateEntityData($dataSource, true);
                $this->configureStandardMappingSection($dataSource, $partnerFieldMapManager,$partnerService->getMappingInitParams()->getMaster());

                $partnerFieldMapManager->set($dataSource);
            }
        } catch (Exception $e) {
            Globals::$g->gErr->addIAError(
                "SFDC-1118", __FILE__ . ":" . __LINE__,
                "Failed to add fieldmapping in the Initial Sync for " . $intacctObject, ['intacctObject' => $intacctObject],
                'Generic Salesforce Error : '. $e->getMessage(), []
            );
        }
    }

    /**
     * Configure StandardMapping Section
     *
     * @param array $dataSource
     * @param string $master
     * @param PartnerFieldMapManager $partnerFieldMapManager
     */
    private function configureStandardMappingSection(&$dataSource, $partnerFieldMapManager,$master)
    {
        $index = 0;
        foreach ($dataSource['STANDARD_FIELDS'] as $field) {
            // Keep collecting available validlabes and validvalues
            $matches[0]['type']['validlabels'][] = $field['INTACCTFIELD'];
            $matches[0]['type']['validvalues'][] = $field['FULL_NAME'];

            // Select the proper item in the webcombo
            $dataSource['STANDARD_MAPPING'][$index++]['INTACCT_FIELD'] = $field['FULL_NAME'];
        }

        $index = 0;
        foreach ($dataSource['STANDARD_FIELDS'] as $field) {
            // Keep collecting available validlabes and validvalues
            $matches[0]['type']['validlabels'][] = $field['PARTNERFIELD'];
            $matches[0]['type']['validvalues'][] = $field['PARTNERFIELD'];

            // Select the proper item in the webcombo
            $dataSource['STANDARD_MAPPING'][$index++]['SALESFORCE_FIELD'] = $dataSource['AVAILABLE_SFORCE_FIELDS']['namedFields'][$field['PARTNERFIELD']]['label']
                ?: $field['PARTNERFIELD'];
        }

        $index = 0;
        foreach ($dataSource['STANDARD_FIELDS'] as $field) {
            if (isArrayValueProvided($field, 'MASTER')) {
                $master = $field['MASTER'];
            }
            $syncRule = SforceSyncRule::convertToSforceSyncRule($master);
            $dataSource['STANDARD_MAPPING'][$index]['SYNC_RULE'] = $syncRule;
            $dataSource['STANDARD_MAPPING'][$index]['STATUS'] = $field['STATUS'];
            $dataSource['STANDARD_MAPPING'][$index]['NILLABLE'] = $field['NILLABLE'];
            if (!empty($field['SUBSCRIPTIONOBJ'])) {
                $dataSource['STANDARD_MAPPING'][$index]['SUBSCRIPTIONOBJ'] = $partnerFieldMapManager->transformValidiValueToValidValue(
                    'SUBSCRIPTIONOBJ', $field['SUBSCRIPTIONOBJ']);
            }
            $index++;
        }
    }
    /**
     * Checks the status of subscription and turns it into CONFIGURED if the current
     * status is IN_PROGRESS
     */
    public function setStatusConfigured()
    {
        $prefs = $this->get('');

        if (isset($prefs[static::SFORCESTATUS]) && SforceConfigurationStatus::SFORCE_STATUS_IN_PROGRESS === $prefs[static::SFORCESTATUS]) {
            $this->setSforceConfiurationStatus(SforceConfigurationStatus::SFORCE_STATUS_CONFIGURED);
        }
    }

    /**
     * Sets the salesforce integration configuration status according to the passed parameter.
     *
     * @param string $status
     *
     * @return bool
     */
    public function setSforceConfiurationStatus(string $status)
    {
        $ok = $this->SetPreference(
            static::SFORCESTATUS,
            $status, true,
            $this->getModule());

        return $ok;
    }

    /**
     * For all Intacct to Salesforce mapped users adjusts the permission
     * - assigns missing permission sets.
     * - removes extra permission sets.
     *
     * @param array $values
     *
     * @return bool
     */
    private function adjustUserPermissionSets(& $values)
    {
        $client = SalesforceUtil::getDefaultSforceClient();

        $permissionsSets = SalesforceUtil::getPermissionSetsBySubscriptions(true,
            'true' === $values['SFORCECONTRACTSYNC'],
            'true' === $values['SYNCPROJECTTASK'],
            'true' === $values['SFORCEGENLEDGERSYNC']);

        $userLookup = $this->getUserLookupData($client);

        $existingPermissionSetAssignments = $this->getPermissionSetAssignments($client, $userLookup);

        foreach ($existingPermissionSetAssignments as $userSfdcId => $singleUserAssignments) {
            foreach ($singleUserAssignments as $permissionSetName) {
                if (!in_array($permissionSetName, $permissionsSets)) {
                    // delete the permission set with the given name assigned
                    // to the salesforce user with the given internal id
                    $client->getSfUserSession()->unassignPermissionSetFrom($permissionSetName, $userSfdcId);
                }
            }

            foreach ($permissionsSets as $requiredPermissionSetName) {
                if (!in_array($requiredPermissionSetName, $singleUserAssignments)) {
                    // Assign a required permission set to the user with
                    // the salesforce id.
                    $client->getSfUserSession()->assignPermissionSetNameTo(
                        $requiredPermissionSetName, $userSfdcId, $wasAssigned);
                }
            }
        }

        return true;
    }

    /**
     * For all intacct / salesforce mapped users creates a lookup map in the
     * following format:
     *    <user-salesforce-id> => [<salesforce-user-name>]
     *
     * @param SforceClient $client
     *
     * @return string[][]
     * @throws Exception
     */
    private function getUserLookupData(SforceClient $client)
    {
        $manager = Globals::$g->gManagerFactory->getManager('oauthuser');
        $oauthUsers = INTACCTarray_list2map(
            $manager->GetList(['selects' => ['PARTNER_USER']]),
            'PARTNER_USER'
        );

        $adminUser = $this->GetPreference('SFORCEADMIN');
        if (!isArrayValueProvided($oauthUsers, $adminUser)) {
            $oauthUsers[$adminUser] = [];
        }

        $soql = "Select Id, Name, UserName From User";
        /* @var stdClass $users */
        $users = $client->queryAll($soql);
        $ret = [];
        /* @var stdClass $userRecord */
        foreach ($users->records as $userRecord) {
            if (isArrayValueProvided($oauthUsers, $userRecord->Username)) {
                $ret[$userRecord->Id]['SFORCE_USER_ID'] = $userRecord->Username;
            }
        }

        return $ret;
    }

    /**
     * Queries the permission set assignments for all the mapped users.
     * Considers only permission sets specific for Intacct / Salesforce
     * integration.
     *
     * @param SforceClient $client
     * @param string[][] $userLookup
     *
     * @return array  collection of permission sets for each mapped user
     *                where user is identified by its internal salesforce id.
     */
    private function getPermissionSetAssignments(SforceClient $client, $userLookup)
    {
        $soql =
            "Select Name, Id From PermissionSet " .
            "Where isOwnedByProfile = false and name in (" .
            "     '" . implode("', '", SforcePermissionSetInternalName::getAllPermissionSets())  . "' )";
        /* @var stdClass $permissionSets */
        $permissionSets = $client->queryAll($soql);
        $permissionSetNameLookup = [];
        foreach ($permissionSets->records as $permissionSet) {
            $permissionSetNameLookup[$permissionSet->Id]  = $permissionSet->Name;
        }

        $soql =
            "Select p.Id, p.PermissionSetId, p.AssigneeId " .
            "From PermissionSetAssignment p " .
            "Where p.PermissionSetId in (" .
            "   Select Id From PermissionSet " .
            "   Where isOwnedByProfile = false and name in (" .
            "     '" . implode("', '", SforcePermissionSetInternalName::getAllPermissionSets()) . "' )" .
            ")";
        $psAssignments = $client->queryAll($soql);
        $ret = [];
        /* @var stdClass $psAssignment */
        foreach ($psAssignments->records as $psAssignment) {
            $assigneeId = $psAssignment->AssigneeId;
            if (isArrayValueProvided($userLookup, $assigneeId)) {
                $ret[$assigneeId][] = $permissionSetNameLookup[$psAssignment->PermissionSetId];
            }
        }

        return $ret;
    }

    /**
     * Returns the list of objects in the right order that need to be
     * synced to salesforce.
     *
     * @param array    $values
     *
     * @return array
     */
    private function getSyncObjectList($values) : array
    {
        static $infoMap = [
            [
                'check-box-field' => 'SFORCEPRODUCTSYNC',
                'already-copied-property' => 'PRODUCTREFERENCECOPIED',
                'check-for-sync-rule' => [
                    'sync-rule-field' => 'SFORCEPRODUCTMASTER',
                    'sync-rule-values' => ['T', 'true', 'Intacct to Salesforce']
                ],
                'intacct-objects' => [IntacctManagedObject::ITEM]
            ],
            [
                'check-box-field' => 'SFORCECONTRACTSYNC',
                'already-copied-property' => 'CONTRACTREFERENCECOPIED',
                'intacct-objects' => [
                    IntacctManagedObject::RENEWAL_MACRO,
                    IntacctManagedObject::CONTRACT_BILLING_TEMPLATE,
                    IntacctManagedObject::EVERGREEN_MACRO
                ]
            ],
            [
                'check-box-field' => 'SFORCEOEPRICELISTSYNC',
                'already-copied-property' => 'SOPRICELISTREFERENCECOPIED',
                'check-for-sync-rule' => [
                    'sync-rule-field' => 'SFORCEOEPRICELISTDIR',
                    'sync-rule-values' => ['T', 'true', 'Intacct', 'Intacct to Salesforce']
                ],
                'intacct-objects' => [
                    IntacctManagedObject::SO_PRICE_LIST,
                    IntacctManagedObject::SO_PRICE_LIST_ENTRY
                ]
            ],
            [
                'check-box-field' => 'SFORCEPRICELISTSYNC',
                'already-copied-property' => 'CONTRACTPRICELISTREFERENCECOPIED',
                'check-for-sync-rule' => [
                    'sync-rule-field' => 'SFORCEPRICINGOPT',
                    'sync-rule-values' => ['T', 'true', 'Intacct', 'Intacct to Salesforce']
                ],
                'intacct-objects' => [
                    IntacctManagedObject::CONTRACT_PRICE_LIST,
                    IntacctManagedObject::CONTRACT_ITEM_PRICE_LIST
                ]
            ],
        ];

        $syncTask = $this->GetPreference('SYNCTASK');
        if ($syncTask === 'false') {
            $projectTask = [
                'check-box-field' => 'SYNCPROJECTTASK',
                'already-copied-property' => 'PROJECTREFERENCECOPIED',
                'intacct-objects' => [
                    IntacctManagedObject::PROJECT
                ]
            ];
        } else {
            $projectTask = [
                'check-box-field' => 'SYNCPROJECTTASK',
                'already-copied-property' => 'PROJECTREFERENCECOPIED',
                'intacct-objects' => [
                    IntacctManagedObject::PROJECT,
                    IntacctManagedObject::TASK
                ]
            ];
        }
        $infoMap[] = $projectTask;

        $intacctObjects = [];
        $markedAsCopiedProperties = [];

        foreach ($infoMap as $info) {
            if (isset($values[$info['check-box-field']]) && 'true' === $values[$info['check-box-field']]) {
                if (isset($info['check-for-sync-rule'])) {
                    // If the object is marked to check to sync rule confirm
                    // that the user has selected a sync rule that instructs
                    // to copy reference objects from Intacct to Salesforce
                    $syncRuleField = $info['check-for-sync-rule']['sync-rule-field'];
                    if (!in_array($values[$syncRuleField], $info['check-for-sync-rule']['sync-rule-values'])) {
                        continue;
                    }
                }

                if (!isset($values[$info['already-copied-property']]) || 'true' !== $values[$info['already-copied-property']]) {
                    // The 'copied' property is either missing or not true
                    $intacctObjects = array_merge($intacctObjects, $info['intacct-objects']);
                    $markedAsCopiedProperties[] = $info['already-copied-property'];
                }
            }
        }

        $intacctSyncObjects = [];
        foreach ($intacctObjects as $io) {
            $syncFilter = null;
            if ($io === IntacctManagedObject::RENEWAL_MACRO) {
                $syncFilter = $this->getTemplateTransactionTypeSyncFilter(self::CONTRACT);
            }
            if ($io === IntacctManagedObject::EVERGREEN_MACRO) {
                $syncFilter = $this->getTemplateTransactionTypeSyncFilter(self::EVERGREEN);
            }
            $intacctSyncObjects[] = new IntacctSyncObject(new IntacctSyncObjectNode($io, $syncFilter));
        }

        return [
            'intacctObjects' => $intacctSyncObjects,
            'markedAsCopiedProperties' => $markedAsCopiedProperties];
    }

    /**
     * Checks for contract sync and if it is enabled and the contract related
     * objects have never been copied schedules an asynchronous copy.
     * Marks all the copied objects to avoid subsequent copy of the same
     * object at setup page submit.
     *
     * @param array $values
     *
     * @return bool returns true if there are objects selected to synchtonize
     *              and false otherwise
     * @throws Exception
     */
    private function queueCopyReferenceObjects($values)
    {
        $ret = false;

        if ('true' === $values['INTEGRATIONSTATUS']) {
            // Check for selected Enable xxx check boxes ONLY if the ENABLE SYNCHRONIZATION check box is selected
            $syncObjectList = $this->getSyncObjectList($values);
            if ( ! empty($syncObjectList) ) {
                $intacctObjects = $syncObjectList['intacctObjects'];
                if ( ! empty($intacctObjects) ) {
                    $intacctObjectNodes = IntacctManagedObjectSyncService::rearrangeIntacctObjects($intacctObjects);
                    $ret = SforceMessagePublisher::publishSyncObjects(
                        $intacctObjectNodes, BulkApiLogAction::SUBSCRIBE);

                    $markedAsCopiedProperties = $syncObjectList['markedAsCopiedProperties'];
                    foreach ( $markedAsCopiedProperties as $property ) {
                        $ret = $ret
                            && $this->SetPreference(
                                $property, 'true', Globals::$g->kSALESFORCE2id);
                    }
                }
            }
        }

        return $ret;
    }

    /**
     * Drops the Intacct Configuration record in the salesforce database.
     *
     * @param SforceClient $client
     *
     * @return bool
     * @throws Exception
     * @throws IAException
     */
    public function deleteIntacctConfiguration(SforceClient $client)
    {
        $ret = true;
        /** @var  SforceNamespaceHandler $namespaceHandler */
        $namespaceHandler = $this->getNamespaceHandler();
        $intacctConfiguration = $namespaceHandler::getIaconfObjectName();
        $configuration =
            $client->query("SELECT Id, Name FROM $intacctConfiguration");
        if (!isset($configuration->records) || count($configuration->records) !== 1) {
            logDiagnostics("DELETE_IA_CONFIGURATION",
                'Intacct Configuration is not specified or specifed incorrectly!!!');
        } else {
            $ret = $client->delete($intacctConfiguration . "/" . $configuration->records[0]->Id);
        }

        return $ret;
    }

    /**
     * @param string $sforceObj
     *
     * @return bool
     * @throws Exception
     */
    public function syncSforceObject($sforceObj)
    {
        $ok = false;
        if(!in_array($sforceObj, self::$selectiveSyncObjs)) {
            $syncFilter = null;
            if ($sforceObj == IntacctManagedObject::RENEWAL_MACRO) {
                $transactionType = [];
                /** @var ModulesManager $modulesMgr */
                $modulesMgr = Globals::$g->gManagerFactory->getManager('modules');
                if ($modulesMgr->isModuleSubscribed(Globals::$g->kSOid)) {
                    $transactionType[] = self::SALESTRANSACTION;
                }
                if ($this->isContractSyncEnabled()) {
                    $transactionType[] = self::CONTRACT;
                }
                $syncFilter = new SyncFilter();
                $syncFilter->addFilterRule(new SyncFilterRule('TRANSACTIONTYPE', SforceFilterSyncRule::IN, $transactionType));
            } else if ($sforceObj == IntacctManagedObject::EVERGREEN_MACRO) {
                if ($this->isContractSyncEnabled()) {
                    $syncFilter = $this->getTemplateTransactionTypeSyncFilter(self::EVERGREEN);
                }
            }
            $intacctSyncObjParent = new IntacctSyncObject(new IntacctSyncObjectNode($sforceObj, $syncFilter));
            if ($sforceObj == IntacctManagedObject::SO_PRICE_LIST) {
                $intacctSyncObjChild = new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::SO_PRICE_LIST_ENTRY));
                $intacctSyncObjParent->addPostSyncObject($intacctSyncObjChild);
            } else if ($sforceObj == IntacctManagedObject::CONTRACT_PRICE_LIST) {
                $intacctSyncObjChild = new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::CONTRACT_ITEM_PRICE_LIST));
                $intacctSyncObjParent->addPostSyncObject($intacctSyncObjChild);
            }
            $syncObjectList = [
                $intacctSyncObjParent
            ];
            $intacctObjectNodes = IntacctManagedObjectSyncService::rearrangeIntacctObjects($syncObjectList);
            $ok = SforceMessagePublisher::publishSyncObjects(
                $intacctObjectNodes, BulkApiLogAction::SYNCHRONIZE);
        } else {
            //Get detail fields of selective sync object from modulepref
            $objDetailFieldName = $sforceObj."DETAILFIELDS";
            if ($sforceObj == IntacctManagedObject::GLJOURNAL) {
                $objDetailFieldName = "JOURNALDETAILFIELDS";
            }
            $objDetailPref = $this->GetPreference($objDetailFieldName, Globals::$g->kSALESFORCE2id);
            if (is_array($objDetailPref)) {
                $objDetailPref = implode(self::ARRAY_SEPARATOR, $objDetailPref);  // make it string
            }
            switch ($sforceObj) {
                case IntacctManagedObject::GLACCOUNT:
                    $ok = $this->syncSforceGlAccounts($objDetailPref, false);
                    break;
                case IntacctManagedObject::GLJOURNAL:
                    $ok = $this->syncSforceJournals($objDetailPref, false);
                    break;
                case IntacctManagedObject::STATACCOUNT:
                    $ok = $this->syncSforceStatAccounts($objDetailPref, false);
                    break;
                case IntacctManagedObject::STATJOURNAL:
                    $ok = $this->syncSforceStatJournals($objDetailPref, false);
                    break;
                case IntacctManagedObject::UDD:
                    $ok = $this->syncSforceUdd($objDetailPref, false);
                    break;
            }
        }
        return $ok;
    }

    /**
     * @param string $journalValues
     * @return array
     */
    public function prepareJournalList($journalValues){
        $journalList = explode("#~#",$journalValues);
        return $journalList;
    }
    /**
     * @param string $glAccountValues
     * @return array
     */
    public function prepareGlAccountList($glAccountValues){
        $glAccountList = explode("#~#",$glAccountValues);
        return $glAccountList;
    }
    /**
     * @param string $uddValues
     * @return array
     */
    public function prepareUddList($uddValues){
        $uddList = explode("#~#",$uddValues);
        return $uddList;
    }
    /**
     * @param string $journalValues
     * @param bool $setPreference
     *
     * @return bool
     * @throws Exception
     */
    public function syncSforceJournals($journalValues, $setPreference = true)
    {
        global $gErr, $kSALESFORCE2id;
        try {
            $status = true;
            if($setPreference) {
                $status = $this->SetPreference('JOURNALDETAILFIELDS', $journalValues, true, $kSALESFORCE2id);
            }
            if($status) {

                /* @var SforceMassSyncService $sforceMassSyncService */
                $sforceMassSyncService = SforceMassSyncServiceFactory::getSforceMassSyncService(IntacctManagedObject::GLJOURNAL);
                if ($sforceMassSyncService === null) {
                    Globals::$g->gErr->addIAError(
                        'SFDC-1119',
                        __FILE__ . '.' . __LINE__,
                        'Mass sync is not supported for the provided object: ' . IntacctManagedObject::GLJOURNAL, ['IntacctManagedObject' => IntacctManagedObject::GLJOURNAL]);
                    $status = false;
                } else {
                    $journalList=$this->prepareJournalList($journalValues);
                    $intacctSyncList = $sforceMassSyncService->prepareIntacctSyncList($journalList);

                    if (!empty($intacctSyncList)) {
                        $syncNodeList = IntacctManagedObjectSyncService::rearrangeIntacctObjects($intacctSyncList);
                        $status = SforceMessagePublisher::publishSyncObjects($syncNodeList, BulkApiLogAction::SYNCHRONIZE);
                    }
                }
            }
            else{
                /** @noinspection PhpUndefinedVariableInspection */
                $gErr->addIAError('SFDC-1120', __FILE__ . ':' . __LINE__,
                    'Failed to store user selected preference ' . $field, [ 'FIELD' => $field ] );
            }
        } catch (IAException $e) {
            LogToFile('Synchronize exception trace: ' . ((string)$e));
            Globals::$g->gErr->addError(
                "SFDC-1121", __FILE__ . ":" . __LINE__,
                'Generic Salesforce Error : '. $e->getMessage());
            $status = false;
        }
        return $status;
    }

    /**
     * @param string $journalValues
     * @param bool $setPreference
     *
     * @return bool
     * @throws Exception
     */
    public function syncSforceStatJournals($journalValues, $setPreference = true)
    {
        global $gErr, $kSALESFORCE2id;
        try {
            $status = true;
            if($setPreference) {
                $status = $this->SetPreference('STATJOURNALDETAILFIELDS', $journalValues, true, $kSALESFORCE2id);
            }
            if($status) {

                /* @var SforceMassSyncService $sforceMassSyncService */
                $sforceMassSyncService = SforceMassSyncServiceFactory::getSforceMassSyncService(IntacctManagedObject::STATJOURNAL);
                if ($sforceMassSyncService === null) {
                    Globals::$g->gErr->addIAError(
                        'SFDC-1119',
                        __FILE__ . '.' . __LINE__,
                        'Mass sync is not supported for the provided object: ' . IntacctManagedObject::STATJOURNAL, ['IntacctManagedObject' => IntacctManagedObject::STATJOURNAL]);
                    $status = false;
                } else {
                    $journalList=$this->prepareJournalList($journalValues);
                    $intacctSyncList = $sforceMassSyncService->prepareIntacctSyncList($journalList);

                    if (!empty($intacctSyncList)) {
                        $syncNodeList = IntacctManagedObjectSyncService::rearrangeIntacctObjects($intacctSyncList);
                        $status = SforceMessagePublisher::publishSyncObjects($syncNodeList, BulkApiLogAction::SYNCHRONIZE);
                    }
                }
            }
            else{
                /** @noinspection PhpUndefinedVariableInspection */
                $gErr->addIAError('SFDC-1120', __FILE__ . ':' . __LINE__,
                    'Failed to store user selected preference ' . $field, [ 'FIELD' => $field ] );
            }
        } catch (IAException $e) {
            LogToFile('Synchronize exception trace: ' . ((string)$e));
            Globals::$g->gErr->addError(
                "SFDC-1121", __FILE__ . ":" . __LINE__,
                'Generic Salesforce Error : '. $e->getMessage());
            $status = false;
        }
        return $status;
    }

    /**
     * @param string $glAccountValues
     * @param bool $setPreference
     *
     * @return bool
     * @throws Exception
     */
    public function syncSforceGlAccounts($glAccountValues, $setPreference = true)
    {
        global $gErr, $kSALESFORCE2id;
        try {
            $status = true;
            if($setPreference) {
                $status = $this->persistGLAccountFields($glAccountValues);
            }
            if($status) {

                /* @var SforceMassSyncService $sforceMassSyncService */
                $sforceMassSyncService = SforceMassSyncServiceFactory::getSforceMassSyncService(IntacctManagedObject::GLACCOUNT);
                if ($sforceMassSyncService === null) {
                    Globals::$g->gErr->addIAError(
                        'SFDC-1119',
                        __FILE__ . '.' . __LINE__,
                        'Mass sync is not supported for the provided object: ' . IntacctManagedObject::GLACCOUNT, ['IntacctManagedObject' => IntacctManagedObject::GLACCOUNT]);
                    $status = false;
                } else {
                    $glAccountList=$this->prepareGlAccountList($glAccountValues);
                    $intacctSyncList = $sforceMassSyncService->prepareIntacctSyncList($glAccountList);

                    if (!empty($intacctSyncList)) {
                        $syncNodeList = IntacctManagedObjectSyncService::rearrangeIntacctObjects($intacctSyncList);
                        $status = SforceMessagePublisher::publishSyncObjects($syncNodeList, BulkApiLogAction::SYNCHRONIZE);
                    }
                }
            }
            else{
                /** @noinspection PhpUndefinedVariableInspection */
                $gErr->addIAError('SFDC-1120', __FILE__ . ':' . __LINE__,
                    'Failed to store user selected preference ' . $field, [ 'FIELD' => $field ] );
            }
        } catch (IAException $e) {
            LogToFile('Synchronize exception trace: ' . ((string)$e));
            Globals::$g->gErr->addError(
                "SFDC-1121", __FILE__ . ":" . __LINE__,
                'Generic Salesforce Error : '. $e->getMessage());
            $status = false;
        }
        return $status;
    }

    /**
     * @param string $glAccountValues
     * @param int $columnSize
     * @return bool
     */
    public function persistGLAccountFields(string $glAccountValues, int $columnSize = 4000): bool
    {
        global $kSALESFORCE2id;
        $currentChunk = '';
        $counter = 1;
        $ok = true;
        $this->deletePreferencesLike("GLACCOUNTDETAILFIELDS%", $kSALESFORCE2id);
        $glAccountList = $this->prepareGlAccountList($glAccountValues);

        foreach ($glAccountList as $glAccount) {
            $potentialSize = strlen($currentChunk) + strlen($glAccount) + strlen('#~#');

            if ($potentialSize < $columnSize) {
                $currentChunk .= ($currentChunk === '' ? '' : '#~#') . $glAccount;
            } else {
                $currentChunk = $this->convertAccountForModulePrefWrapper("GLACCOUNTDETAILFIELDS", $currentChunk, false);
                $ok = $ok && $this->SetPreference(
                        'GLACCOUNTDETAILFIELDS#' . $counter, $currentChunk, true,
                        $kSALESFORCE2id
                    );
                $currentChunk = $glAccount;
                $counter++;
            }
        }

        if (!empty($currentChunk)) {
            $currentChunk = $this->convertAccountForModulePrefWrapper("GLACCOUNTDETAILFIELDS", $currentChunk, false);
            $ok = $ok && $this->SetPreference(
                    'GLACCOUNTDETAILFIELDS#' . $counter, $currentChunk, true,
                    $kSALESFORCE2id
                );
        }
        return $ok;
    }

    /**
     * @param string $property
     * @param string $value
     * @param bool $toExternalVal
     * @return string
     */
    private function convertAccountForModulePrefWrapper($property, $value, bool $toExternalVal = true): string
    {
        global $kSALESFORCE2id;
        $modPrefdata = [$property => $value];
        if (convertAccountForModulePref($kSALESFORCE2id, $modPrefdata, $toExternalVal)) {
            $value = $modPrefdata[$property];
        }
        return $value;
    }

    /**
     * @param string $statAccountValues
     * @param bool $setPreference
     *
     * @return bool
     * @throws Exception
     */
    public function syncSforceStatAccounts($statAccountValues, $setPreference = true)
    {
        global $gErr, $kSALESFORCE2id;
        try {
            $status = true;
            if($setPreference) {
                $status = $this->SetPreference('STATACCOUNTDETAILFIELDS', $statAccountValues, true, $kSALESFORCE2id);
            }
            if($status) {

                /* @var SforceMassSyncService $sforceMassSyncService */
                $sforceMassSyncService = SforceMassSyncServiceFactory::getSforceMassSyncService(IntacctManagedObject::STATACCOUNT);
                if ($sforceMassSyncService === null) {
                    Globals::$g->gErr->addIAError(
                        'SFDC-1119',
                        __FILE__ . '.' . __LINE__,
                        'Mass sync is not supported for the provided object: ' . IntacctManagedObject::STATACCOUNT, ['IntacctManagedObject' => IntacctManagedObject::STATACCOUNT]);
                    $status = false;
                } else {
                    $statAccountList=$this->prepareGlAccountList($statAccountValues);
                    $intacctSyncList = $sforceMassSyncService->prepareIntacctSyncList($statAccountList);

                    if (!empty($intacctSyncList)) {
                        $syncNodeList = IntacctManagedObjectSyncService::rearrangeIntacctObjects($intacctSyncList);
                        $status = SforceMessagePublisher::publishSyncObjects($syncNodeList, BulkApiLogAction::SYNCHRONIZE);
                    }
                }
            }
            else{
                /** @noinspection PhpUndefinedVariableInspection */
                $gErr->addIAError('SFDC-1120', __FILE__ . ':' . __LINE__,
                    'Failed to store user selected preference ' . $field, [ 'FIELD' => $field ] );
            }
        } catch (IAException $e) {
            LogToFile('Synchronize exception trace: ' . ((string)$e));
            Globals::$g->gErr->addError(
                "SFDC-1121", __FILE__ . ":" . __LINE__,
                'Generic Salesforce Error : '. $e->getMessage());
            $status = false;
        }
        return $status;
    }

    /**
     * @param string $uddValues
     * @param bool $setPreference
     *
     * @return bool
     * @throws Exception
     */
    public function syncSforceUdd($uddValues, $setPreference = true)
    {
        try {
           // $oldUddDetailPref = $this->GetPreference("UDDDETAILFIELDS", Globals::$g->kSALESFORCE2id);
            $status = true;
            if($setPreference) {
                $status = $this->SetPreference('UDDDETAILFIELDS', $uddValues, true, Globals::$g->kSALESFORCE2id);
            }
            if($status) {
                $uddSyncList = $this->prepareUddList($uddValues);
                $intacctSyncObjParent = array();
                foreach ($uddSyncList as  $udd) {
                    $intacctSyncObjNode = new IntacctSyncObjectNode(IntacctManagedObject::UDD);
                    $intacctSyncObjNode->setDocumentType($udd);
                   // $intacctSyncObjNode->setOldUddDef($oldUddDetailPref);
                    $intacctSyncObjParent[] = new IntacctSyncObject($intacctSyncObjNode);
                }
                if (!empty($intacctSyncObjParent)) {
                    $syncNodeList = IntacctManagedObjectSyncService::rearrangeIntacctObjects($intacctSyncObjParent);
                    $status = SforceMessagePublisher::publishSyncObjects($syncNodeList, BulkApiLogAction::SYNCHRONIZE);
                }
            }
            else{
                /** @noinspection PhpUndefinedVariableInspection */
                $gErr->addIAError('SFDC-1120', __FILE__ . ':' . __LINE__,
                    'Failed to store user selected preference ' . $field, [ 'FIELD' => $field ] );
            }
        } catch (IAException $e) {
            LogToFile('Synchronize exception trace: ' . ((string)$e));
            Globals::$g->gErr->addError(
                "SFDC-1121", __FILE__ . ":" . __LINE__,
                'Generic Salesforce Error : '. $e->getMessage());
            $status = false;
        }
        return $status;
    }
    /**
     * Copies all Intacct reference objects to the salesforce using
     * integration user at the time of the initial subscription.
     *
     * @return bool
     * @throws Exception
     */
    protected function queueCopyOnInitialSubscription()
    {
        $syncObjectList = [
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::COMPANY)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::LOCATION_ENTITY)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::AR_TERM)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::PROJECT_TYPE)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::DEPARTMENT)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::TERRITORY)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::EXCHANGE_RATE_TYPE)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::CLAZZ)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::PROJECT_STATUS)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::LOCATION)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::EMPLOYEE)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::REVREC_TEMPLATE)),
            new IntacctSyncObject(new IntacctSyncObjectNode(IntacctManagedObject::CONTRACT_REVREC_TEMPLATE)),
        ];
        //Check if OE Module is installed, and add Sales Transaction RENEWALMACRO for Initial Sync
        if (IsModuleIdInstalled(Globals::$g->kSOid)) {
            $syncFilter = $this->getTemplateTransactionTypeSyncFilter(self::SALESTRANSACTION);

            $node =
                new IntacctSyncObjectNode(IntacctManagedObject::RENEWAL_MACRO, $syncFilter);
            $syncObjectList[] = new IntacctSyncObject($node);
        }
        $intacctObjectNodes = IntacctManagedObjectSyncService::rearrangeIntacctObjects($syncObjectList);

        $ok = SforceMessagePublisher::publishSyncObjects(
            $intacctObjectNodes, BulkApiLogAction::SUBSCRIBE);

        return $ok;
    }

    /**
     * Initializes Intacct Configuration for the given company in Salesforce.
     * Triggered upon Subscription to Salesforce integration v.2
     *
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    protected function initIntacctConfiguration(& $values)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ret = null;

        $sfdcAdminUser = $values['SFORCEADMIN'];
        $isSforceSandboxInUse = SalesforceUtil::isSalesforceSandboxInUse($values['USESFSANDBOX']);

        $client = $this->getPartnerClient(true, $sfdcAdminUser, $isSforceSandboxInUse);
        $intacctConfiguration = SforceNamespaceHandler::getIaconfObjectName();
        $configuration =
            $client->query("SELECT Id, Name FROM $intacctConfiguration");
        if (!isset($configuration->records) || count($configuration->records) !== 0) {
            throw new IAException('Incorrect setup for Intacct Configuration!!!');
        }

        $configurationName = IntacctConfiguration::DEFAULT_CONFIGURATION_NAME;
        $orgType = SalesforceUtil::getIntacctConfigurationOrgType();
        $packageType = $this->getIntacctConfigurationPackageType();
        $companyName = GetMyCompanyTitle();
        $baseCurrency = SalesforceUtil::getBaseCurrency();
        $isDbbSubscribed = SalesforceUtil::isDbbSubscribed() ? 'true' : 'false';

        $postingData = '{ "Name": "' . $configurationName
            . '", "' . SforceNamespaceHandler::getIaconfOrgType() . '": "' . $orgType
            . '", "' . SforceNamespaceHandler::getIaconfPackageType() . '": "' . $packageType
            . '", "' . SforceNamespaceHandler::getIaconfCompanyName() . '": "' . $companyName
            . '", "' . SforceNamespaceHandler::getIaconfErrorLogsExp() . '": "' . 30
            . '", "' . SforceNamespaceHandler::getIaconfBaseCurrency() . '": "' . $baseCurrency
            . '", "' . SforceNamespaceHandler::getIaconfigDbbEnabled() . '": "' . $isDbbSubscribed
            . '" }';
        $result = $client->post($intacctConfiguration, $postingData, $id);
        $ret = $result && isset($id);

        return $ret;
    }

    /**
     * Returns the Intacct configuration package type based on module
     * subscriptions.
     *
     * @return string
     * @throws Exception
     */
    private function getIntacctConfigurationPackageType()
    {
        global $kCNid;
        $gManagerFactory = Globals::$g->gManagerFactory;

        /* @var ModulesManager $modulesMgr */
        $modulesMgr = $gManagerFactory->getManager('modules');
        $cnSubscribed = $modulesMgr->isModuleSubscribed($kCNid);

        $packageType = 'Opportunity';
        if ($cnSubscribed) {
            $packageType = 'Opportunity & Contract';
        }

        return $packageType;
    }

    /**
     * Updates the salesforce integration v.2 preferences on salesforce.
     * Called upon Salesforce setup save.
     *
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    private function updateSalesforcePreferences(& $values)
    {
        $ok = true;
        $client = $this->getDefaultPartnerClient();

        // Query salesforce internal id from Intacct Configuration custom object
        // by company name
        $intacctConfiguration = SforceNamespaceHandler::getIaconfObjectName();
        $configuration =
            $client->query("SELECT Id, Name FROM $intacctConfiguration");

        if (isset($configuration->records)) {
            if (count($configuration->records) > 1) {
                // ERROR CONDITION: More than one Intacct configuration
                throw new IAException('More than one Intacct Configuration record is specified for a company!!!');
            } else {
                // Intacct configuration is correct. Get the SF internal ID
                $id = $configuration->records[0]->Id;

                // Specify the parameters coming from other pages
                $orgType = SalesforceUtil::getIntacctConfigurationOrgType();
                $packageType = $this->getIntacctConfigurationPackageType();
                $companyName = GetMyCompanyTitle();

                $addNewAccount = isArrayValueProvided($values, 'SFORCEADDNEWACCT') &&
                'true' === $values['SFORCEADDNEWACCT'] ? 'true' : 'false';
                $postBilltoShipto = isArrayValueProvided($values, 'POSTBILLTOSHIPTO') &&
                'true' === $values['POSTBILLTOSHIPTO'] ? 'true' : 'false';

                $productEnableSync = isArrayValueProvided($values, 'SFORCEPRODUCTSYNC') &&
                'true' === $values['SFORCEPRODUCTSYNC'] ? 'true' : 'false';

                $isSyncEnabled = isArrayValueProvided($values, 'INTEGRATIONSTATUS') &&
                'true' === $values['INTEGRATIONSTATUS'] ? 'true' : 'false';

                if (isArrayValueProvided($values, 'SFORCEPRODUCTMASTER')) {
                    if ('F' === $values['SFORCEPRODUCTMASTER']) {
                        // SF to Intacct
                        $productSalesforceToIntacct = 'true';
                        $productIntacctToSalesforce = 'false';
                    } else {
                        // Intacct to SF
                        $productIntacctToSalesforce = 'true';
                        $productSalesforceToIntacct = 'false';
                    }
                } else {
                    $productIntacctToSalesforce = 'false';
                    $productSalesforceToIntacct = 'false';
                }


                $cnEnableSync = isArrayValueProvided($values, 'SFORCECONTRACTSYNC') &&
                'true' === $values['SFORCECONTRACTSYNC'] ? 'true' : 'false';

                $cnpbEnableSync = isArrayValueProvided($values, 'SFORCEPRICELISTSYNC') &&
                'true' === $values['SFORCEPRICELISTSYNC'] ? 'true' : 'false';

                if (isArrayValueProvided($values, 'SFORCEPRICINGOPT')) {
                    if ('F' === $values['SFORCEPRICINGOPT']) {
                        // SF to Intacct
                        $cnpbSalesforceToIntacct = 'true';
                        $cnpbIntacctToSalesforce = 'false';
                    } else {
                        // Intacct to SF
                        $cnpbIntacctToSalesforce = 'true';
                        $cnpbSalesforceToIntacct = 'false';
                    }
                } else {
                    $cnpbIntacctToSalesforce = 'false';
                    $cnpbSalesforceToIntacct = 'false';
                }

                $oePriceSource = isArrayValueProvided($values, 'SFORCEOEPRICELISTSYNC') &&
                'T' === $values['SFORCEOEPRICINGOPT'] ? 'Intacct' : 'Salesforce';
                $oepbEnableSync = isArrayValueProvided($values, 'SFORCEOEPRICELISTSYNC') &&
                'true' === $values['SFORCEOEPRICELISTSYNC'] ? 'true' : 'false';
                if (isArrayValueProvided($values, 'SFORCEOEPRICELISTDIR')) {
                    if ('F' === $values['SFORCEOEPRICELISTDIR']) {
                        // SF to Intacct
                        $oepbSalesforceToIntacct = 'true';
                        $oepbIntacctToSalesforce = 'false';
                    } else {
                        // Intacct to SF
                        $oepbIntacctToSalesforce = 'true';
                        $oepbSalesforceToIntacct = 'false';
                    }
                } else {
                    $oepbIntacctToSalesforce = 'false';
                    $oepbSalesforceToIntacct = 'false';
                }
                $createContractAtTop = isArrayValueProvided($values, 'SFORCECREATECONTRACTATROOT')
                && 'true' === $values['SFORCECREATECONTRACTATROOT'] ? 'true' : 'false';
                $projectEnableSync = isArrayValueProvided($values, 'SFORCEPRODUCTSYNC')
                && 'true' === $values['SYNCPROJECTTASK'] ? 'true' : 'false';
                $createProjectAtTop = isArrayValueProvided($values, 'SFORCECREATEPROJECTATROOT')
                && 'true' === $values['SFORCECREATEPROJECTATROOT'] ? 'true' : 'false';
                $isDbbSubscribed = SalesforceUtil::isDbbSubscribed() ? 'true' : 'false';
                $syncAccountsFirst = isArrayValueProvided($values, 'SYNCACCOUNTSFIRST')
                && 'true' === $values['SYNCACCOUNTSFIRST'] ? 'true' : 'false';

                $glEnableSync = isArrayValueProvided($values, 'SFORCEGENLEDGERSYNC') &&
                'true' === $values['SFORCEGENLEDGERSYNC'] ? 'true' : 'false';

                $enableEnforceOneToOne = isArrayValueProvided($values, 'ENFORCE_ONE_TO_ONE_OE_TRX') &&
                'true' === $values['ENFORCE_ONE_TO_ONE_OE_TRX'] ? 'true' : 'false';

                $enableSoDocV2 = isArrayValueProvided($values, 'ENABLESODOCV2SYNC') &&
                'true' === $values['ENABLESODOCV2SYNC'] ? 'New' : 'Old';

                $accContactSyncOnlyOnce = isArrayValueProvided($values, 'ACCCONTACTSYNCONCE') &&
                'true' === $values['ACCCONTACTSYNCONCE'] ? 'true' : 'false';

                $contactSyncOnlyOnce = isArrayValueProvided($values, 'CONTACTSYNCONCE') &&
                'true' === $values['CONTACTSYNCONCE'] ? 'true' : 'false';

                $PaymentsSync = isArrayValueProvided($values, 'ARPAYMENTSPOSTDETAILS') &&
                'true' === $values['ARPAYMENTSPOSTDETAILS'] ? 'true' : 'false';

                $postingData = '{ "' . SforceNamespaceHandler::getIaconfAddNewAcctField() . '": "' . $addNewAccount
                    . '", "' . SforceNamespaceHandler::getIaconfRequireBilltoShiptoField() . '": "' . $postBilltoShipto
                    . '", "' . SforceNamespaceHandler::getIaconfProductEnableSync() . '": "' . $productEnableSync
                    . '", "' . SforceNamespaceHandler::getIaconfProductSyncFromIntacct() . '": "' . $productIntacctToSalesforce
                    . '", "' . SforceNamespaceHandler::getIaconfProductSyncToIntacct() . '": "' . $productSalesforceToIntacct
                    . '", "' . SforceNamespaceHandler::getIaconfContractEnableSync() . '": "' . $cnEnableSync
                    . '", "' . SforceNamespaceHandler::getIaconfContractPBEnableSync() . '": "' . $cnpbEnableSync
                    . '", "' . SforceNamespaceHandler::getIaconfContractPBSyncFromIntacct() . '": "' . $cnpbIntacctToSalesforce
                    . '", "' . SforceNamespaceHandler::getIaconfContractPBSyncToIntacct() . '": "' . $cnpbSalesforceToIntacct
                    . '", "' . SforceNamespaceHandler::getIaconfOEPBEnableSync() . '": "' . $oepbEnableSync
                    . '", "' . SforceNamespaceHandler::getIaconfOEPBSyncFromIntacct() . '": "' . $oepbIntacctToSalesforce
                    . '", "' . SforceNamespaceHandler::getIaconfOEPBSyncToIntacct() . '": "' . $oepbSalesforceToIntacct
                    . '", "' . SforceNamespaceHandler::getIaconfCreateContractAtTop() . '": "' . $createContractAtTop
                    . '", "' . SforceNamespaceHandler::getIaconfProjectEnableSync() . '": "' . $projectEnableSync
                    . '", "' . SforceNamespaceHandler::getIaconfIntacctSfdcSync() . '": "' . $isSyncEnabled
                    . '", "' . SforceNamespaceHandler::getIaconfOrgType() . '": "' . $orgType
                    . '", "' . SforceNamespaceHandler::getIaconfigDbbEnabled() . '": "' . $isDbbSubscribed
                    . '", "' . SforceNamespaceHandler::getIaconfigSyncAccountsFirst() . '": "' . $syncAccountsFirst
                    . '", "' . SforceNamespaceHandler::getIaconfPackageType() . '": "' . $packageType
                    . '", "' . SforceNamespaceHandler::getIaconfCompanyName() . '": "' . $companyName
                    . '", "' . SforceNamespaceHandler::getIaconfJournalEntryEnableSync() . '": "' . $glEnableSync
                    . '", "' . SforceNamespaceHandler::getIaconfEnforceOneToOneTrx() . '": "' . $enableEnforceOneToOne
                    . '", "' . SforceNamespaceHandler::getIaconfPriceSource() . '": "' . $oePriceSource
                    . '", "' . SforceNamespaceHandler::getIaconfCreateProjectAtTop() . '": "' . $createProjectAtTop
                    . '", "' . SforceNamespaceHandler::getSforceOrderEntryFlow() . '": "' . $enableSoDocV2
                    . '", "' . SforceNamespaceHandler::getSforceAccContactSyncOnce() . '": "' . $accContactSyncOnlyOnce
                    . '", "' . SforceNamespaceHandler::getSforceContactSyncOnce() . '": "' . $contactSyncOnlyOnce
                    . '", "' . SforceNamespaceHandler::getSforcePaymentsSyncOnce() . '": "' . $PaymentsSync
                    . '" }';
                $ok = $ok && $client->patch(
                        SforceNamespaceHandler::getIaconfObjectName() . "/$id", $postingData);

                // Update all sales transaction information details
                $this->updateSalesTransactionDefinitions(
                    $client,
                    Globals::$g->gManagerFactory->getManager('sodocumentparams'),
                    $values,
                    $id);

                //Update Dimension settings in Salesforce
                if ( $glEnableSync == 'true' ) {
                    $this->updateGLDimensions($client);
                }
            }
        } else {
            // ERROR CONDITION: No Intacct configuration
            throw new IAException('Intacct Configuration is not specified!!!');
        }

        if (!$ok) {
            global $gErr;
            $msg = "Cannot push Intacct Configuration to the salesforce";
            $gErr->addError('SFDC-1122', __FILE__ . ':' . __LINE__, $msg);
        }

        return $ok;
    }

    /**
     * Updates the salesforce integration v.2 preferences on salesforce for contract.
     * Called upon Contract config save.
     * @param array $values
     */
    public function updateSalesforcePreferencesContract($values) {
        $modulesMgr = Globals::$g->gManagerFactory->getManager('modules');
        $module = $this->getModule();
        if ($modulesMgr->isModuleSubscribed($module)
            && $this->GetPreference('INTEGRATIONSTATUS', $module) == 'true'
            && $this->GetPreference('SFORCECONTRACTSYNC', $module) == 'true') {

            $ok = true;
            $client = $this->getDefaultPartnerClient();

            // Query salesforce internal id from Intacct Configuration custom object
            // by company name
            $intacctConfiguration = SforceNamespaceHandler::getIaconfObjectName();
            $configuration =
                $client->query("SELECT Id, Name FROM $intacctConfiguration");

            if (isset($configuration->records)) {
                if (count($configuration->records) > 1) {
                    // just ignore
                    LogToFile('More than one Intacct Configuration record is specified for a company!!!');
                } else {
                    // Intacct configuration is correct. Get the SF internal ID
                    $id = $configuration->records[0]->Id;

                    $postingData = '{ "'
                        . SforceNamespaceHandler::getIaconfContractTermType()
                        . '": "'
                        . $this->getTermTypeByContractConfigValues($values)
                        . '" }';

                    $ok = $ok && $client->patch(
                            SforceNamespaceHandler::getIaconfObjectName() . "/$id", $postingData);

                }
            } else {
                LogToFile('Intacct Configuration is not specified!!!');
            }

            if (!$ok) {
                LogToFile('More than one Intacct Configuration record is specified for a company!!!');
            }
        }
    }

    /**
     * Converts Term Type from Contract value to Salesforce Value.
     * @param $values
     * @return string
     */
    private function getTermTypeByContractConfigValues($values){
        $termTypeArr = [ 'E' => 'EVERGREEN',
                         'T' => 'TERMED',
                         'B' => 'Both'
                        ];
        return $termTypeArr[$values[self::TERMTYPE_OPTION]];
    }

    /**
     * Updates the list of sales order transaction definitions on salesforce
     *
     * @param SforceClient            $client
     * @param SODocumentParamsManager $docparMgr
     * @param array                   $values
     * @param string                  $intacctConfigurationId
     *
     * @return bool
     * @throws Exception
     */
    public function updateSalesTransactionDefinitions(
        SforceClient $client, SODocumentParamsManager $docparMgr, $values,
        $intacctConfigurationId)
    {
        $ok = true;

        $tdLocationdSfdcIds = $docparMgr->getAllTdLocationSfdcIds();

        if($tdLocationdSfdcIds && count($tdLocationdSfdcIds) > 0){
            $tdSforceObjectName = SforceNamespaceHandler::getSforceTdObjectName();
            $tdSforceTdNewFlowVal = SforceNamespaceHandler::getSforceTdNewFlowVal();
            $tdV2SoDocSforceVal = $values['ENABLESODOCV2SYNC'] ?? 'false';
            $tdSoDocV2Query = "SELECT Id, Name From $tdSforceObjectName where $tdSforceTdNewFlowVal=$tdV2SoDocSforceVal";
            $sforceRecords = $client->query($tdSoDocV2Query);

            if (isset($sforceRecords) && countArray($sforceRecords->records) > 0) {
                foreach ($sforceRecords->records as $record) {
                    $recordId = $record->Id;
                    try {
                        $ok = $ok && $client->delete($tdSforceObjectName . "/$recordId");
                    } catch (Exception $e) {
                        // just ignore
                        LogToFile($e->getMessage());
                    }
                }
            }

            if ($ok) {
                if ($tdV2SoDocSforceVal === 'true') {
                    $ok = $ok && $this->pushTxnDetailsSoDocV2($values, $client, $tdV2SoDocSforceVal, $tdLocationdSfdcIds, $tdSforceObjectName, $intacctConfigurationId);
                } else {
                    $ok = $ok && $this->pushTxnDetails($values, $client, $tdV2SoDocSforceVal, $tdLocationdSfdcIds, $tdSforceObjectName, $intacctConfigurationId);
                }
                //$ok = $ok && $result;
            }
        }
        return $ok;
    }

    /**
     * Data cleanup before submit
     *
     * @param array $values
     */
    private function preprocessPreferences(& $values)
    {
        if (!isArrayValueProvided($values, 'SFORCEPRODUCTSYNC') || 'true' !== $values['SFORCEPRODUCTSYNC']) {
            unset($values['SFORCEPRODUCTMASTER']);
        }
        if (!isArrayValueProvided($values, 'SFORCEPRICELISTSYNC') || 'true' !== $values['SFORCEPRICELISTSYNC']) {
            unset($values['SFORCEPRICINGOPT']);
        }
        if (!isArrayValueProvided($values, 'DEVELOPMENT_SANDBOX')) {
            $values['DEVELOPMENT_SANDBOX'] = '';
        }

        $this->setSyncTaskPreference();
    }

    /**
     * Persist user selected preferences.
     *
     * @param array $values
     *
     * @return bool
     */
    final protected function persistPreferences(& $values)
    {
        global $gErr;
        $ok = true;
        $this->getMCPPreferences($values);

        $preferenceFields = $this->getPreferenceFields();
        $module = $this->getModule();
        foreach ($preferenceFields as $field) {
            if (isArrayValueProvided($values, $field)) {
                $ok = $this->SetPreference($field, $values[$field], true, $module);
                if (!$ok) {
                    break;
                }
            }
        }

        // Commenting the code as we want to get the ISFIELDMAP value from the user
        // Set the preferences that are not exposed on the page
        //$ok = $ok && $this->SetPreference('ISFIELDMAP', 'true', true, $kSALESFORCE2id);

        if (!$ok) {
            /** @noinspection PhpUndefinedVariableInspection */
            $msg = "Failed to store user selected preference $field!";
            $gErr->addIAError('SFDC-1120', __FILE__ . ':' . __LINE__, $msg, [ 'FIELD' => $field ]);

        }
        return $ok;
    }

    /**
     * @return array
     */
    protected function getPreferenceFields()
    {
        $preferenceFields = [
            'SFORCEADDNEWACCT',
            'POSTBILLTOSHIPTO',
            'SFORCECUSTSEQUENCE',
            'SFORCEDOCNUMBERING',
            'SFORCEPRODUCTSYNC',
            'SFORCEPRODUCTMASTER',
            'SFORCECONTRACTSYNC',
            'SFORCECREATECONTRACTATROOT',
            'SFORCECONTRACTAUTOPOST',
            'SFORCEPRICELISTSYNC',
            'SFORCEPRICINGOPT',
            'SYNCACCOUNTSFIRST',
            'SFORCEUPDOPPAMT',
            'SFORCEINCLUDETAXINQUOTE',
            'SFORCEREPQAOA',
            'SFORCEOEPRICELISTSYNC',
            'SFORCEOEPRICINGOPT',
            'SYNCPROJECTTASK',
            'POSTPAYMENTDETAILS',
            'DEVELOPMENT_SANDBOX',
            'SFORCEISMCP',
            'SFORCECORPCURRENCY',
            'ISFIELDMAP',
            'SFORCEEMAILRESULT',
            'SFORCEEMAILADDR',
            'SFORCEBILLINGSHIPPING',
            'SFORCEBILLTOSHIPTO',
            'CONTACTCATOGERY',
            'SFORCEUPDOPTIONS',
            'MAPSOLINETOOPP',
            'SHOWACCTDETAILFIELDS',
            'ACCTDETAILFIELDS',
            'ACCTLAYOUTSECTIONS',
            'ACCTLAYOUTFIELDS',
            'ACCTCOMPONENTINFOS',
            'ACCTFIELDINFOS',
            'ACCTVALIDFIELDS',
            'ACCTCURRENCYFIELD',
            'INTEGRATIONSTATUS',
            'INTACCTONLYMCP',
            'SFORCEGENLEDGERSYNC',
            'SFORCEUDDSYNC',
            'ENFORCE_ONE_TO_ONE_OE_TRX',
            'SFORCEOEPRICELISTDIR',
            'ENABLESODOCV2SYNC',
            'ENABLEMIGRATIONSODOCV2',
            'SFORCECREATEPROJECTATROOT',
            'ACCCONTACTSYNCONCE',
            'CONTACTSYNCONCE',
            'ARPAYMENTSPOSTDETAILS',
        ];

        // For development deployment also store the user selection of
        // the development sandbox and the deployment site
        if (self::isDevelopmentDeployment()) {
            $preferenceFields[] = 'DEVELOPMENT_SANDBOX';
            $preferenceFields[] = 'DEPLOYMENT_SITE';
        }
        return $preferenceFields;
    }

    /**
     * Persists the user provided salesforce admin.
     *
     * @param array $values
     *
     * @return bool
     */
    final protected function persistSforceAdmin(& $values)
    {
        global $gErr;
        $module = $this->getModule();

        $ret = true;
        // First check if the new admin is valid
        $isSforceSandboxInUse = SalesforceUtil::isSalesforceSandboxInUse($values[static::USESFSANDBOX]);
        $isAdminValid = $this->isSalesforceAdminUserValid($values[static::SFORCEADMIN], $isSforceSandboxInUse);
        if (!$isAdminValid) {
            // Provided salesforce admin is invalid
            $msg = "Provided salesforce admin user is invalid!";
            $gErr->addError('SFDC-1123', __FILE__ . ':' . __LINE__, $msg);
            $ret = false;

        } else {
            // Provided salesforce admin user name is VALID
            $oldStatus = $this->GetPreference(static::SFORCESTATUS, $module);

            if ($oldStatus !== SforceConfigurationStatus::SFORCE_STATUS_DEINSTALLED) {
                // The old status is something other than DEINSTALLED
                $oldAdmin = $this->GetPreference(static::SFORCEADMIN, $module);
                if ($oldAdmin !== $values[static::SFORCEADMIN]) {
                    // The salesforce admin has been changed; remove session from cache
                    SalesforceOAuthSession::purgeSessions(array($oldAdmin), $this->getConnectedApp());
                }
            }

            // Update the admin
            $this->SetPreference(
                static::SFORCEADMIN, $values[static::SFORCEADMIN], true, $module
            );

        }

        return $ret;
    }

    /**
     * @return string
     */
    protected function getConnectedApp()
    {
        return SforceConnectedApp::INTACCT_CONNECT;
    }

    /**
     * Returns the salesforce users marked as an admin / integration user
     *
     * @return array|bool|string
     */
    public function getAdmin()
    {
        global $kSALESFORCE2id;
        $admin = $this->GetPreference('SFORCEADMIN', $kSALESFORCE2id);

        return $admin;
    }

    /**
     * Returns the value of the cached user setting for Salesforce Sandbox
     * Org selection.
     *
     * @return bool
     * @throws IAException
     */
    public function getCachedSforceSandboxOrg()
    {
        static $cachedSforceSandboxOrg = null;
        if (is_null($cachedSforceSandboxOrg)) {
            $useSandbox = $this->GetPreference(static::USESFSANDBOX);
            if (!isset($useSandbox)) {
                throw new IAException('SalesforceSetupManager::getCachedSforceSandboxOrg: cachedSforceSandboxOrg is undefined.');
            }
            $cachedSforceSandboxOrg = ('true' === $useSandbox);
        }
        return $cachedSforceSandboxOrg;
    }

    /**
     * Public setter for the $useNamespace class variable
     *
     * @param bool $useNamespace
     */
    public function setUseNamespace($useNamespace) {
        $this->useNamespace = $useNamespace;
    }

    /**
     * Public getter for the $useNamespace class variable.
     *
     * @return bool|null
     */
    public function getUseNamespace()
    {
        return $this->useNamespace;
    }

    /**
     * Returns true if the company is subscribed to salesforce v.2
     * and false otherwise. The returned value is cached.
     *
     * @return bool|null
     * @throws Exception
     */
    public function isConnectedAppWithNamespace()
    {
        $withNamespace = false;
        if (!is_null($this->useNamespace)) {
            $withNamespace = $this->useNamespace;
        } else {
            static $sforceSubscribed = null;
            if (is_null($sforceSubscribed)) {
                global $gManagerFactory;
                /* @var ModulesManager $modulesMgr */
                $modulesMgr = $gManagerFactory->getManager('modules');
                $withNamespace = $modulesMgr->isModuleSubscribed($this->getModule());
            }
        }

        return $withNamespace;
    }

    /**
     * Returns the deployment status of the application.
     *
     * @return string
     * @throws IAException
     */
    public static function getDeployment()
    {
        if (self::isProductionDeployment()) {
            $ret = SalesforceSetupSettingsViewer::DEPLOYMENT_PRODUCTION;
        } else if (self::isDevelopmentDeployment()) {
            $ret = SalesforceSetupSettingsViewer::DEPLOYMENT_DEVELOPMENT;
        }  else if (self::isPreviewDeployment()) {
            $ret = SalesforceSetupSettingsViewer::DEPLOYMENT_PREVIEW;
        } else {
            throw new IAException("SalesforceSetupManager::getDeployment: Unknown deployment");
        }
        return $ret;
    }
    /**
     * Indicates if the deployment is in production mode.
     *
     * @return bool
     */
    public static function isProductionDeployment()
    {
        return Globals::$g->islive;
    }

    /**
     * Indicates if the deployment is in preview mode.
     *
     * @return bool
     */
    public static function isPreviewDeployment()
    {
        return self::isProductionDeployment();
    }

    /**
     * Indicates if the deployment is in development mode.
     *
     * @return bool
     */
    public static function isDevelopmentDeployment()
    {
        return !self::isProductionDeployment() && !self::isPreviewDeployment();
    }

    /**
     * Specify the XMLGW endpoint for the given deployment site:
     * - Production, read it from ia_init.cfg file
     * - Others, read from development-sandboxes.inc
     *
     * @param string $deploymentSite
     *
     * @return bool|mixed|null
     * @throws IAException
     */
    public static function translateXmlBaseApi($deploymentSite)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $xmlBaseApi = null;
        if (self::isProductionDeployment()){
            $xmlBaseApi = SalesforceUtil::getRequiredConfigProperty('IA_XMLGW_ENDPOINT');
        } else {
            include 'development-sandboxes.inc';
            /** @noinspection PhpUndefinedVariableInspection */
            $xmlBaseApi = $deploymentSites[$deploymentSite]['xmlgw'];
        }

        /*if (is_null($xmlBaseApi)) {
            throw new IAException ("SalesforceSetupManager::translateXmlBaseApi: Deploymet site is not supported.");
        }*/

        return $xmlBaseApi;
    }

    /**
     * Reads the list of transaction definition names specifies for top level
     * and all entities and populates the validvalues and validlabels fields
     * for the corresponding drop down list box.
     *
     * @throws Exception
     */
    public function MergeFieldInfo()
    {
        parent::MergeFieldInfo();

        $fieldInfomation = ['SFORCESODOCPAR', 'V2SFORCESODOCPAR'];
        foreach ($fieldInfomation as $fldInfo) {
            $fieldInfo = &$this->GetFieldInfo($fldInfo);

            $gManagerFactory = Globals::$g->gManagerFactory;
            /* @var SODocumentParamsManager $docparMgr */
            $docparMgr = $gManagerFactory->getManager('sodocumentparams');

            $trxNames = $docparMgr->getAllTransactionDefinitionsNames();

            $fieldInfo['type']['validlabels'] = $trxNames;
            $fieldInfo['type']['validvalues'] = $trxNames;
        }
    }

    /**
     * Reads the internal id of the salesforce org linked to the
     * company and stores it as a module preference.
     *
     * @param string $sfdcAdminUser
     * @param bool   $useSandbox
     *
     * @return bool
     */
    public function setSalesforceOrgId($sfdcAdminUser, $useSandbox)
    {
        if (!isset($sfdcAdminUser)) {
            throw new InvalidArgumentException(
                "SalesforceSetuManager::setSalesforceOrgId: Required sfdcAdminUser parameter is not provided.");
        }
        if (!isset($useSandbox)) {
            throw new InvalidArgumentException(
                "SalesforceSetuManager::setSalesforceOrgId: Required useSandbox parameter is not provided.");
        }

        $ok = false;

        $client = $this->getPartnerClient(true, $sfdcAdminUser, $useSandbox);

        if ($client && $client->getSfUserSession()) {
            $orgId = $client->getSfUserSession()->getSFOrgForSession();
            $ok = $this->SetPreference(
                'LINKED_SALESFORCE_ORG_ID', $orgId, true, $this->getModule());
        }

        return $ok;
    }

    /**
     * Checks if the salesforce v.2 has been subscribed.
     *
     * @return bool
     */
    function IsConfigured()
    {
        $ret = true;
        $prefs = $this->get('');
        $configStat = $prefs['SFORCESTATUS'];
        if (SforceConfigurationStatus::SFORCE_STATUS_CONFIGURED != $configStat) {
            Globals::$g->gErr->addIAError(
                "SFDC-1124", __FILE__ . ":" . __LINE__,
                'Incomplete Implementation ! Configuration Status  is set to '. $configStat, ['CONFIG_STAT' => $configStat],
            );
            $ret =  false;
        }
        return $ret;
    }

    /**
     * If the multi-currency is enabled for the company populates the MCP
     * fields in the preferences array.
     *
     * @param array $prefs
     */
    private function getMCPPreferences(& $prefs)
    {
        $prefs['INTACCTONLYMCP'] = 'false';
        $prefs['SFORCEISMCP'] = 'false';
        $prefs['SFORCECORPCURRENCY'] = '';
        $isMcpEnabled = SalesforceUtil::isMultiCurrencyEnabled();
        if( $isMcpEnabled ) {
            $sfAdmin = $prefs['SFORCEADMIN'];
            $isSforceSandboxInUse = SalesforceUtil::isSalesforceSandboxInUse($prefs['USESFSANDBOX']);

            $sfClient = $this->getPartnerClient(true, $sfAdmin, $isSforceSandboxInUse);
            $isSforceMCP = SforceSyncProcessorMisc::getCorporateCurrency($sfClient, $corpCurrency);
            if( $isSforceMCP ){
                //Intacct and Salesforce both are MCP
                $prefs['SFORCEISMCP'] = 'true';
                $prefs['SFORCECORPCURRENCY'] = $corpCurrency;
            } else {
                //Only Intacct is MCP
                $prefs['INTACCTONLYMCP'] = 'true';
            }
        }
    }

    /**
     * @param string $uddDefName
     *
     * @return bool
     */
    public function isSubscribedUDDbyName($uddDefName)
    {
        $selectedUDDforSync = $this->GetPreference('UDDDETAILFIELDS');
        return in_array($uddDefName, $selectedUDDforSync);
    }

    /**
     * @return bool
     */
    private function updateFieldMapping()
    {
        $partnerFieldMapManager = Globals::$g->gManagerFactory->getManager('partnerfieldmap');
        $cny = GetMyCompany();
        $ok= $partnerFieldMapManager->DoQuery('QRY_PARTNERFIELDMAP_UPDATE_STATUS', $cny);
        $ok = $ok && $partnerFieldMapManager->DoQuery('QRY_PARTNERFIELDMAP_UPDATE_PARTNEROBJECT_IA_MASTER', $cny);
        $ok = $ok && $partnerFieldMapManager->DoQuery('QRY_PARTNERFIELDMAP_UPDATE_PARTNEROBJECT_SF_MASTER', $cny);
        return $ok;
    }

    /**
     * @param SforceClient|null|false $client
     * @param array                   $glPrefs
     */
    public function updateGLDimensions(SforceClient $client = null, $glPrefs = null)
    {
        if (is_null($client)) {
            $client = SalesforceUtil::getDefaultSforceClient();
        }
        if (is_null($glPrefs)) {
            GetModulePreferences(Globals::$g->kGLid, $glPrefs);
        }
        $postData = [];
        // Clear all dimensions
        $client->deleteCustomSetting(CustomDimensions::CUSTOMER_SETTING_JE_BALANCE_VALIDATION);
        $validDimensionsPairs = CustomDimensions::validDimensionsPairs();
        foreach ($validDimensionsPairs as $sfDim => $glDim) {
            $postDataEle["Name"] = $sfDim;
            $postDataEle[SforceNamespaceHandler::getPrefix() . "Enabled__c"] = $glPrefs[$glDim] == 'T' ? 'true' : 'false';
            $postData[] = $postDataEle;
        }
        $jsonPostData = json_encode($postData);

        $result = $client->upsertCustomSetting(CustomDimensions::CUSTOMER_SETTING_JE_BALANCE_VALIDATION, $jsonPostData);
        if ( is_array($result) ) {
            $ret = $result[0]->success;
        } else {
            $ret = $result->success;
        }

        if (!$ret) {
            global $gErr;
            $gErr->AddWarning("Cannot push GL dimensions to the salesforce for validation", __FILE__ . ':' . __LINE__);
        }
    }

    /**
     * @param bool      $useAdminUser
     * @param string    $sfUser
     * @param null|bool $sforceSandboxOrg
     *
     * @return SforceClient
     */
    public function getPartnerClient($useAdminUser = false, $sfUser = null, $sforceSandboxOrg = null)
    {
        return new SforceClient($useAdminUser, $sfUser, $sforceSandboxOrg);
    }

    /**
     * @return string
     */
    public function getModule()
    {
        return Globals::$g->kSALESFORCE2id;
    }


    /**
     * Set SYNCTASK preference
     */
    protected function setSyncTaskPreference() {
        $module = $this->getModule();
        $isProjectInstalled = (IsModuleIdInstalled(Globals::$g->kPAid) ? 'true' : 'false');
        $this->SetPreference('SYNCTASK', $isProjectInstalled, true, $module);
    }

    /**
     * Update Selective sync values
     * GLACCOUNT, GLJOURNAL, STATJOURNAL, STATACCOUNT, UDD
     *
     * @param array $values Set of values to update
     *
     *
     * @return bool signifying success or failure
     */
    function setAdditionalPreferences(&$values)
    {
        $ok = false;
        $forceSelectiveSyncObj = false;

        if (($values['SFORCEGENLEDGERSYNC'] ?? null) === 'true') {
            if (isset($values['GLACCOUNTDETAILFIELDS'])) {
                $forceSelectiveSyncObj = IntacctManagedObject::GLACCOUNT;
            } else {
                $forceSelectiveSyncObj = IntacctManagedObject::GLJOURNAL;
            }
        } else if (($values['SFORCEUDDSYNC'] ?? null) === 'true') {
            $forceSelectiveSyncObj = IntacctManagedObject::UDD;
        } else if (($values['STATACCOUNTDETAILFIELDS'] ?? null) === 'true') {
            $forceSelectiveSyncObj = IntacctManagedObject::STATACCOUNT;
        } else if (($values['STATJOURNALDETAILFIELDS'] ?? null) === 'true') {
            $forceSelectiveSyncObj = IntacctManagedObject::STATJOURNAL;
        }

        if ($forceSelectiveSyncObj) {

            if (!in_array($forceSelectiveSyncObj, self::$selectiveSyncObjs)) {
                $ok = $this->syncSforceObject($forceSelectiveSyncObj);
            } else {
                //Get detail fields of selective sync object from modulepref
                $objDetailFieldName = $forceSelectiveSyncObj . "DETAILFIELDS";
                if ($forceSelectiveSyncObj === IntacctManagedObject::GLJOURNAL) {
                    $objDetailFieldName = "JOURNALDETAILFIELDS";
                }

                $objDetailPref = $values[$objDetailFieldName];
                switch ($forceSelectiveSyncObj) {
                    case IntacctManagedObject::GLACCOUNT:
                        $ok = $this->syncSforceGlAccounts($objDetailPref);
                        break;
                    case IntacctManagedObject::GLJOURNAL:
                        $ok = $this->syncSforceJournals($objDetailPref);
                        break;
                    case IntacctManagedObject::STATACCOUNT:
                        $ok = $this->syncSforceStatAccounts($objDetailPref);
                        break;
                    case IntacctManagedObject::STATJOURNAL:
                        $ok = $this->syncSforceStatJournals($objDetailPref);
                        break;
                    case IntacctManagedObject::UDD:
                        $ok = $this->syncSforceUdd($objDetailPref);
                        break;
                }
            }
        }
        return $ok;
    }

    /**
     * API Entry point for Set. Sub classes can override this method to provide special mapping
     * logic for inbound values
     *
     * @param array $values Set of values to update
     *
     *
     * @return bool signifying success or failure
     */
    function API_Set(&$values)
    {
        $preferenceFields = $this->getPreferenceFields();
        foreach ($preferenceFields as $field) {
            if (isset($values[$field])) {
                if ($values[$field] === true || $values[$field] === false) {
                    $values[$field] = $values[$field] ? 'true' : 'false';
                }
            }
        }

        $ok = $this->regularSet($values);
        $ok = $ok && $this->setAdditionalPreferences($values);
        return $ok;
    }

    /**
     * API Entry point for Add. Sub classes can override this method to provide special mapping
     * logic for inbound values
     *
     * @param array $values of values to Add
     *
     *
     * @return bool signifying success or failure
     */
    function API_Add(&$values)
    {
        return parent::API_Add($values);
    }

    /**
     * @param string $type
     * @return SyncFilter
     */
    private function getTemplateTransactionTypeSyncFilter(string $type)
    {
        $syncFilter = new SyncFilter();
        $syncFilter->addFilterRule(new SyncFilterRule('TRANSACTIONTYPE', SforceFilterSyncRule::EQUALS, $type));
        return $syncFilter;
    }

    /**
     * True, if contract sync is enabled.
     *
     * @return bool
     */
    private function isContractSyncEnabled()
    {
        return $this->GetPreference('SFORCECONTRACTSYNC', Globals::$g->kSALESFORCE2id) == 'true';
    }

    /**
     * Check if SODocument new flow feature flag for setup page SFV2 is Enabled
     *
     * @return bool
     * @throws Exception
     */
    public function checkSODocV2Enabled() : bool
    {
        $kSALESFORCE2id = Globals::$g->kSALESFORCE2id;
        $isSODocEnabled = FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('DISABLE_SODOCUMENT_SALESFORCE_SETUP_V2');
        if (!$isSODocEnabled) {
            $this->SetPreference('ENABLESODOCV2SYNC', 'false', true, $kSALESFORCE2id
            );
        }
        return $isSODocEnabled;
    }

    /**
     * Updates the list of sales order transaction definitions on salesforce
     *
     * @param array $values
     * @param SforceClient $client
     * @param string $tdV2SoDocSforceVal
     * @param array $tdLocationdSfdcIds
     * @param string $tdSforceObjectName
     * @param string $intacctConfigurationId
     *
     * @return mixed
     */
    private function pushTxnDetailsSoDocV2(array $values, SforceClient $client, string $tdV2SoDocSforceVal, array $tdLocationdSfdcIds, string $tdSforceObjectName, string $intacctConfigurationId) : mixed
    {
        $result = false;
        // Iterate over all configured TDs and push settings to salesforce New flow
        foreach ($values['NEW_SO_DOC_TXN_POSTING'] as $td) {
            $tdName = $td['V2SFORCESODOCPAR'];
            $allowFromSalesforce = $td['V2ALLOWFROMSALESFORCE'];
            $postToSalesforce = $td['V2POSTTOSALESFORCE'];
            $includeDetails = $td['V2SFORCEINCLUDEDETAILS'];
            $tdLocationSfdcId = $tdLocationdSfdcIds[$tdName];
            $postingData = '{ "Name": "' . $tdName
                . '", "' . SforceNamespaceHandler::getSforceTdAllowFromSF() . '": "' . $allowFromSalesforce
                . '", "' . SforceNamespaceHandler::getSforceTdPostToSF() . '": "' . $postToSalesforce
                . '", "' . SforceNamespaceHandler::getSforceTdIncludeLIDetails() . '": "' . $includeDetails
                . '", "' . SforceNamespaceHandler::getSforceTdIntacctConfigurationId() . '": "' . $intacctConfigurationId
                . '", "' . SforceNamespaceHandler::getSforceTdIntacctEntityId() . '": "' . $tdLocationSfdcId
                . '", "' . SforceNamespaceHandler::getSforceTdNewFlowVal() . '": "' . $tdV2SoDocSforceVal
                . '" }';

            $result = $client->post($tdSforceObjectName, $postingData, $id);
        }

        return $result;
    }

    /**
     * Updates the list of sales order transaction definitions on salesforce
     *
     * @param array $values
     * @param SforceClient $client
     * @param string $tdV2SoDocSforceVal
     * @param array $tdLocationdSfdcIds
     * @param string $tdSforceObjectName
     * @param string $intacctConfigurationId
     *
     * @return mixed
     */
    private function pushTxnDetails(array$values, SforceClient $client, string $tdV2SoDocSforceVal, array $tdLocationdSfdcIds, string $tdSforceObjectName, string $intacctConfigurationId) : mixed
    {
        $result = false;
        // Iterate over all configured TDs and push settings to salesforce Old flow
        foreach ($values['SFORCE_TXN_POSTING_DETAILS'] as $td) {
            $tdName = $td['SFORCESODOCPAR'];
            $allowFromSalesforce = $td['ALLOWFROMSALESFORCE'];
            $postToSalesforce = $td['POSTTOSALESFORCE'];
            $includeDetails = $td['SFORCEINCLUDEDETAILS'];
            $tdLocationSfdcId = $tdLocationdSfdcIds[$tdName];
            $postingData = '{ "Name": "' . $tdName
                . '", "' . SforceNamespaceHandler::getSforceTdAllowFromSF() . '": "' . $allowFromSalesforce
                . '", "' . SforceNamespaceHandler::getSforceTdPostToSF() . '": "' . $postToSalesforce
                . '", "' . SforceNamespaceHandler::getSforceTdIncludeLIDetails() . '": "' . $includeDetails
                . '", "' . SforceNamespaceHandler::getSforceTdIntacctConfigurationId() . '": "' . $intacctConfigurationId
                . '", "' . SforceNamespaceHandler::getSforceTdIntacctEntityId() . '": "' . $tdLocationSfdcId
                . '", "' . SforceNamespaceHandler::getSforceTdNewFlowVal() . '": "' . $tdV2SoDocSforceVal
                . '" }';

            $result = $client->post($tdSforceObjectName, $postingData, $id);
        }
        return $result;
    }

    /**
     * @param array $values
     * @param bool|NULL|string $enableMigrationSOV2
     * @param array $loop
     * @return bool
     * @throws IAException
     */
    public function postFldMapSoDocV2Migration(array $values, mixed $enableMigrationSOV2, array $loop): bool
    {
        $migrationValues = $values;
        $ok = true;
        if ($enableMigrationSOV2 === null || $enableMigrationSOV2 === false || $enableMigrationSOV2 === 'false') {
            $gManagerFactory = Globals::$g->gManagerFactory;
            /* @var SODocumentParamsManager $docparMgr */
            $docparMgr = $gManagerFactory->getManager('sodocumentparams');
            $allDocparIdFields = $docparMgr->getAllDocparIdFields();

            $dataSource['MASTER'] = 'B';
            $dataSource['INTACCTDOCUMENTTYPE'] = $allDocparIdFields[0]['DOCID'] ?? null; // To bypass validation from PartnerFieldMapService::initializeMappingInitParams
            $dataSource['MAPPED_PARTNER_OBJECT'] = $loop['INTACCTOBJECT'];

            if (empty($dataSource['INTACCTDOCUMENTTYPE'])) {
                $msg = "No transaction definitions found. Complete Order Entry setup.";
                Globals::$g->gErr->addError('SFDC-1256', __FILE__ . ':' . __LINE__, $msg);
                return false;
            }

            $mappings = $this->migrateMappings($migrationValues, $dataSource);
            $partnerFieldMapManager = new PartnerFieldMapManager();
            $partnerFieldMapManager->deleteMappingOnSalesforce($migrationValues['INTACCT_OBJECT']);

            foreach ($mappings as & $mapping) {
                $json = $partnerFieldMapManager->mapping2json($migrationValues['INTACCT_OBJECT'], $mapping, true, true);
                $sforceMappingList[] = $json;
            }
            if (!empty($sforceMappingList)) {
                $ok = $partnerFieldMapManager->postFieldMappingToSalesforce($migrationValues['INTACCT_OBJECT'], $sforceMappingList);
            }

            $ok = $ok && $this->SetPreference(
                'ENABLEMIGRATIONSODOCV2', 'true', Globals::$g->kSALESFORCE2id);
        }
        return $ok;
    }

    /**
     * @param array $migrationValues
     * @param array $dataSource
     * @return array
     * @throws IAException
     */
    public function migrateMappings(array &$migrationValues, array $dataSource): array
    {
        $partnerService = PartnerFieldMapService::getPartnerFieldMapService('SFORCE2');
        $partnerService->initializeMappingInitParams($dataSource);

        $migrationValues['INTACCT_OBJECT'] = $partnerService->getMappingInitParams()->getIaObject();
        $migrationValues['PARTNER_OBJECT'] = $partnerService->getMappingInitParams()->getPartnerObject();
        $master = $partnerService->getMappingInitParams()->getMaster();

        $viewer = PartnerFieldMapViewer::getInstance($partnerService->getMappingInitParams(), null);
        $isSyncRuleEditable = $viewer->isCustomMappingSyncRuleEditable();
        $partnerService->populateEntityData($migrationValues, $isSyncRuleEditable);

        $partnerFieldMapManager = $partnerService->getPartnerFieldMapManager($migrationValues['PARTNER_OBJECT']);
        $index = 0;
        foreach ($migrationValues['STANDARD_FIELDS'] as $field) {
            // Select the proper item in the webcombo
            $migrationValues['STANDARD_MAPPING'][$index]['INTACCT_FIELD'] = $field['FULL_NAME'];
            $migrationValues['STANDARD_MAPPING'][$index]['SALESFORCE_FIELD'] = $migrationValues['AVAILABLE_SFORCE_FIELDS']['namedFields'][$field['PARTNERFIELD']]['label']
                ?: $field['PARTNERFIELD'];
            $syncRule = isArrayValueProvided($field, 'MASTER') ? SforceSyncRule::convertToSforceSyncRule($field['MASTER'])
                : SforceSyncRule::convertToSforceSyncRule($master);
            $migrationValues['STANDARD_MAPPING'][$index]['SYNC_RULE'] = $syncRule;
            $migrationValues['STANDARD_MAPPING'][$index]['STATUS'] = $field['STATUS'];
            $migrationValues['STANDARD_MAPPING'][$index]['NILLABLE'] = $field['NILLABLE'];
            if (isArrayValueProvided($field, 'READONLYFLDS')) {
                $migrationValues['STANDARD_MAPPING'][$index]['READONLYFLDS'] = $field['READONLYFLDS'];
            }
            if (!empty($field['SUBSCRIPTIONOBJ'])) {
                $migrationValues['STANDARD_MAPPING'][$index]['SUBSCRIPTIONOBJ'] = $partnerFieldMapManager->transformValidiValueToValidValue(
                    'SUBSCRIPTIONOBJ', $field['SUBSCRIPTIONOBJ']);
            }
            $index++;
        }
        $partnerFieldMapManager = new PartnerFieldMapManager();
        $mappings = $this->stdMappings($partnerFieldMapManager, $migrationValues);
        return $mappings;
    }

    /**
     * @param PartnerFieldMapManager $partnerFieldMapManager
     * @param array $migrationValues
     * @return array
     */
    private function stdMappings(PartnerFieldMapManager $partnerFieldMapManager, array &$migrationValues): array
    {
        $mappings = $partnerFieldMapManager->prepareStandardMappings($migrationValues);
        return $mappings;
    }

    /**
     * @param PartnerFieldMapManager $partnerFieldMapManager
     * @param array $migrationValues
     * @return array
     */
    public function customMappings(PartnerFieldMapManager $partnerFieldMapManager, array &$migrationValues): array
    {
        $mappedCustomFields = $migrationValues['MAPPED_CUSTOM_FIELDS'];
        $index = 0;

        foreach ($mappedCustomFields as $mapping) {
            unset($migrationValues['CUSTOM_MAPPING'][$index]['__dummy']);
            unset($migrationValues['CUSTOM_MAPPING'][$index]['_isNewLine']);

            $migrationValues['CUSTOM_MAPPING'][$index]['INTACCT_FIELD'] = $mapping['FULL_NAME'];

            $partnerField = $mapping['PARTNERFIELD'];
            $partnerFieldValue =
                $migrationValues['AVAILABLE_SFORCE_FIELDS']['namedFields'][$partnerField]['label'];
            $migrationValues['CUSTOM_MAPPING'][$index]['SALESFORCE_FIELD'] = $partnerFieldValue;

            $migrationValues['CUSTOM_MAPPING'][$index]['SYNC_RULE'] =
                SforceSyncRule::convertToSforceSyncRule($mapping['MASTER']);

            $migrationValues['CUSTOM_MAPPING'][$index]['STATUS'] = $mapping['STATUS'];
            if ( !empty($mapping['SUBSCRIPTIONOBJ']) ) {
                $migrationValues['CUSTOM_MAPPING'][$index]['SUBSCRIPTIONOBJ'] = $mapping['SUBSCRIPTIONOBJ'];
            }

            $index++;
        }

        $mappings = $partnerFieldMapManager->prepareCustomMappings($migrationValues);
        return $mappings;
    }
}

/**
 * Represents a record in the Sales transactions synchronization table
 * of the Salesforce integration v.2 setup page
 */
class SforcePostingTxnDetails {

    /* @var  string $txnDocument */
    private $txnDocument;

    /* @var  bool $includeLineitemDetails */
    private $includeLineitemDetails;

    /* @var  bool $allowFromSalesforce */
    private $allowFromSalesforce;

    /* @var bool $postToSalesforce postToSalesforce */
    private $postToSalesforce;


    /**
     * @param string $txnDocument
     * @param bool $includeLineitemDetails
     * @param bool $allowFromSalesforce
     * @param bool $postToSalesforce
     */
    public function __construct(
        $txnDocument, $includeLineitemDetails, $allowFromSalesforce,
        $postToSalesforce)
    {
        $this->txnDocument = $txnDocument;
        $this->includeLineitemDetails = $includeLineitemDetails;
        $this->allowFromSalesforce = $allowFromSalesforce;
        $this->postToSalesforce = $postToSalesforce;
    }

    /**
     * @return bool
     */
    public function isIncludeLineitemDetails()
    {
        return $this->includeLineitemDetails;
    }

    /**
     * @param bool $includeLineitemDetails
     */
    public function setIncludeLineitemDetails($includeLineitemDetails)
    {
        $this->includeLineitemDetails = $includeLineitemDetails;
    }

    /**
     * @return string
     */
    public function getTxnDocument()
    {
        return $this->txnDocument;
    }

    /**
     * @param string $txnDocument
     */
    public function setTxnDocument($txnDocument)
    {
        $this->txnDocument = $txnDocument;
    }

    /**
     * @return bool
     */
    public function isAllowFromSalesforce()
    {
        return $this->allowFromSalesforce;
    }

    /**
     * @param bool $allowFromSalesforce
     */
    public function setAllowFromSalesforce($allowFromSalesforce)
    {
        $this->allowFromSalesforce = $allowFromSalesforce;
    }

    /**
     * @return bool
     */
    public function isPostToSalesforce()
    {
        return $this->postToSalesforce;
    }

    /**
     * @param bool $postToSalesforce
     */
    public function setPostToSalesforce($postToSalesforce)
    {
        $this->postToSalesforce = $postToSalesforce;
    }

}

