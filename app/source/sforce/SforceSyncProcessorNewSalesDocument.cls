<?php

/**
 * class IntacctSforceSyncProcessorSODocument to sync the Intacct object 'SODOCUMENT' from Intacct to Salseforce
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2023 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

require_once 'SforceConstants.inc';

class SforceSyncProcessorNewSalesDocument extends SforceSyncProcessorSalesDocument
{

    /**
     * @return string
     */
    protected function getAccoutFldName() : string
    {
        return SforceNamespaceHandler::getPrefix() . 'Account__c';
    }

    /**
     * This is for subscription table update
     *
     * @param array $objValues Intacct object values
     * @param string $externalId Salesforce external Id
     *
     * @return bool
     * @throws Exception
     */
    protected function updateSubscription(mixed $objValues, mixed $externalId) : bool
    {
        $ok = SforceSyncProcessor::updateSubscription($objValues, $externalId);
        return $ok;
    }

    /**
     * @param string[] $sfObj
     *
     * @return SyncProcessorResult
     */
    public function update(mixed $sfObj) : SyncProcessorResult
    {
        $msg = 'Sales Document is already synced with Intacct. Update sync is not supported. ';
        $this->syncResult->setSynMessage($msg);
        $this->syncResult->setStatus(true);
        return $this->syncResult;
    }

}