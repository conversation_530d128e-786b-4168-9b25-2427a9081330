<?
/**
 *    FILE:            ReportRowsManager.cls
 *    AUTHOR:            rs
 *    DESCRIPTION:    manager for gl reportrows object
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/

class ReportRowsManager extends EntityManager
{

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {

        foreach ( $values as $key => $value ) {
            $values[$key] = isl_trim($value);
        }
        
        return parent::regularAdd($values);
    
    }

    /**
     * @return bool
     */
    public function IsAuditEnabled()
    {
        return false;
    }

    /**
     * @param string $verb
     * @param string $key
     * @param mixed  $param1
     * @param mixed  $param2
     * @param array  $values
     *
     * @param bool   $fastUpdate
     *
     * @return bool
     */
    public function DoEvent($verb, $key, $param1 = null, $param2 = null, $values = [], $fastUpdate = false)
    {
        // disable user events
        return true;
    }
}


