<?php

/**
 * Manager class for Financial Report
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */
/**
 * Manager class for Financial Report
 */
define("LIMITEDADMINPRIV", "1");
define("FULLADMINPRIV", "2");

class FinancialReportManager extends ReportinfoManager
{

    /**
     * Delete     
     * 
     * @param string $ID Report's record no
     * 
     * @return bool
     */
    function Delete($ID)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $gErr = Globals::$g->gErr;

        /** @var FinancialReportLister $financialReportLister */
        $financialReportLister = Lister::getEntityLister('FinancialReport');
        $financialReportLister->VerifyPermissions($ID);

        $cny = GetMyCompany();

        $source = 'FinancialReportManager::Delete()';

        $memrec = $this->GetRaw($ID);

        // Check whether report is used in dashboard, if so delete them as well
        $usrBeanManager =  $gManagerFactory->getManager('userbeans');
        $dsbId = $usrBeanManager->searchReportUsage($ID, "R");

        $ok = $this->beginTrx($source);

        $schopkey = $memrec[0]['SCHOPKEY'];
        if ( $schopkey && $schopkey != '' ) {

            $schopMgr =  $gManagerFactory->getManager('scheduledoperation');
            $ok = $schopMgr->DoQuery('QRY_UPDATE_SCHEDULEDOPERATION_STATUS', array('F', $cny, $schopkey));

            $schMgr =  $gManagerFactory->getManager('schedule');
            $ok = $ok && $schMgr->DoQuery('QRY_UPDATE_SCHEDULE_STATUS', array('F', $cny, $cny, $schopkey));
            $gManagerFactory->DeleteManager('schedule');
        }

        foreach ( $dsbId as $v ) {
            $ok = $ok && $usrBeanManager->Delete($v);
        }

        if ( $ok ) {
            $ok = parent::Delete($ID); 
        }

        if ( $ok ) {
            // Delete the permissions of the financial report

            $delStmt = 'DELETE FROM DIR WHERE cny# =:1 and path=:2';
            $ok = $ok && ExecStmt(array($delStmt, GetMyCompany(), $memrec[0]['NAME']));
        }

        $ok = $ok && $this->commitTrx($source);
        if ( !$ok ) {
            $msg = "Could not delete financial reports record!";
            $gErr->addError('GL-3501', __FILE__ . ':' . __LINE__, $msg);
            $this->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    public function translate(&$values)
    {
        $locationIR = ( $values['DEPT_IR'] ?? 'N' ) === 'Y';
        $deptIR = ( $values['LOCATION_IR'] ?? 'N' ) === 'Y';
        $projectIR = ( $values['PROJECT_IR'] ?? 'N' ) === 'Y';

        if ( $locationIR || $deptIR ) {
            if ( $projectIR ) {
                $msg = "Individual Project is not supported with Individual location or Individual department";
                Globals::$g->gErr->addError('GL-7033', __FILE__ . ':' . __LINE__, $msg);

                return false;
            }
        }

        return parent::translate($values);
    }

    /**
     * Returns sub query used to filter list of financial reports based on user permissions.
     * If $recordNo of the report is sent as parameter, the sub query will reduce the list of reports to one or none
     * (in this case user don't have permissions to access it)
     *
     * @param null|string $recordNo
     *
     * @return string
     */
    public static function getPermissionSubQuery($recordNo = null)
    {
        // Give access to reports if the user is a full admin or Limited User.
        $myAdminLevel = GetMyAdminLevel();
        $isAdmin = ( $myAdminLevel == FULLADMINPRIV || $myAdminLevel == LIMITEDADMINPRIV );

        // If the user has no admin privilege then he can see only those reports for which he has access.
        // If he is admin we can see everything so we can just return the normal query spec
        if ($isAdmin || Profile::getUserCacheProperty("USERINFO", "CATEGORY") === UserInfoManager::CATEGORY_TEMPLATE_ADMIN) {
            return "";
        }

        // Get user_rec
        $_userid = Globals::$g->_userid;
        [$user_rec, $cny] = explode('@', $_userid);

        // Getting all the direct and inherited groups for this user.
        $mm = new UserGroupMembersManager();
        [ , $dirgrps, $inhgrps] = $mm->GetDirectAndInheritedGroupsForUser($user_rec);
        $grp_ids = INTACCTarray_merge($dirgrps, $inhgrps);

        $subquery = "(SELECT inqry.reportname FROM ";

        // Forming the query that returns just those reportnames for which the user has access to
        // 
        // The access is decided as follows:
        // 1) If user has an explicit Allow/Deny for a report then that permission applies and overrides any other
        //    permission for that report for that user
        // 2) Elseif the user has a Allow through atleast one group to which he belongs to then the report will be 
        //    accessible to the user provided there is no explict deny to the user on that report
        // 3) Elseif the report has an Allow to Group\Everyone, it will be accessible to the user provided that there 
        //    is no Deny to the user on that report directly or through any group to which he belongs to. 

        if (isset($recordNo)) {
            $innerclause
                = "( 
                          SELECT R.name reportname
					      FROM reportinfo R
						  WHERE R.userkey = $user_rec 
                          AND R.cny# = $cny
                          AND R.record# = $recordNo

						  UNION ALL

		                  SELECT reportname 
				          FROM v_glfinancialaccess 
						  WHERE u_o_gkey = $user_rec 
                          AND type = 'U' 
                          AND a_o_d = 'A'
                          AND cny# = $cny
                          AND report# = $recordNo";
        } else {
            $innerclause
                = "( 
                          SELECT R.name reportname
					      FROM reportinfo R
						  WHERE R.userkey = $user_rec 
                          AND R.cny# = $cny

						  UNION ALL

		                  SELECT reportname 
				          FROM v_glfinancialaccess 
						  WHERE u_o_gkey = $user_rec 
                          AND type = 'U' 
                          AND a_o_d = 'A'
                          AND cny# = $cny";
        }


        // If user belongs to one or more groups only then we need to check if there is Allow for any of the groups to
        //  which the user belongs to
        if ( count($grp_ids) ) {

            // Making sure that no records with user explicitly denied is selected.
            if (isset($recordNo)) {
                $innerclause1
                    = "SELECT reportname
							 FROM v_glfinancialaccess outtab
							 WHERE cny# = $cny 
							 AND report# = $recordNo
                             AND type = 'G' 
                             AND a_o_d = 'A' 
                             AND NOT EXISTS 
                             ( 
                                 SELECT reportname 
                                 FROM v_glfinancialaccess 
                                 WHERE cny# = $cny 
                                 AND report# = $recordNo
                                 AND u_o_gkey = $user_rec 
                                 AND type = 'U' 
                                 AND a_o_d = 'D' 
                                 AND reportname = outtab.reportname
                             )";
            } else {
                $innerclause1
                    = "SELECT reportname
							 FROM v_glfinancialaccess outtab
							 WHERE cny# = $cny 
                             AND type = 'G' 
                             AND a_o_d = 'A' 
                             AND NOT EXISTS 
                             ( 
                                 SELECT reportname 
                                 FROM v_glfinancialaccess 
                                 WHERE cny# = $cny 
                                 AND u_o_gkey = $user_rec 
                                 AND type = 'U' 
                                 AND a_o_d = 'D' 
                                 AND reportname = outtab.reportname
                             )";
            }
            $innerclause1 = PrepINClauseStmt(array($innerclause1), $grp_ids, "AND u_o_gkey ", false);
            $innerclause1 = $innerclause1[0];
        }

        if (isset($recordNo)) {
            $innerclause2
                = "SELECT reportname
                         FROM v_glfinancialaccess outtab
						 WHERE u_o_gkey = -1
                         AND type = 'G' 
                         AND a_o_d = 'A'
                         AND cny# = $cny
                         AND report# = $recordNo
                         AND NOT EXISTS
                         (
                            SELECT reportname 
                            FROM v_glfinancialaccess 
                            WHERE cny# = $cny
                            AND report# = $recordNo
                            AND ( (u_o_gkey = $user_rec AND type = 'U')";
        } else {
            $innerclause2
                = "SELECT reportname
                         FROM v_glfinancialaccess outtab
						 WHERE u_o_gkey = -1
                         AND type = 'G' 
                         AND a_o_d = 'A'
                         AND cny# = $cny
                         AND NOT EXISTS
                         (
                            SELECT reportname 
                            FROM v_glfinancialaccess 
                            WHERE cny# = $cny
                            AND ( (u_o_gkey = $user_rec AND type = 'U')";
        }


        if ( count($grp_ids) ) {
            $innerclause2 = PrepINClauseStmt(array($innerclause2), $grp_ids, " OR (u_o_gkey ", false);
            $innerclause2 = $innerclause2[0] . " AND type = 'G')";
        }

        $innerclause2 .= ") AND a_o_d = 'D' AND reportname = outtab.reportname)";

        if ( isset($innerclause1) && $innerclause1 != '' ) {
            $innerclause .= " UNION ALL " . $innerclause1;
        }

        $innerclause .= " UNION ALL " . $innerclause2;
        $innerclause .= ") inqry WHERE inqry.reportname = financialreport.name";

        $subquery .= $innerclause . ")";

        return $subquery;
    }

    /**
     * Column Preview Dates QRequest Handler
     *
     * @return string
     */
    public function getPreviewDatesByColumnParams(){
        if(!function_exists('getPreviewDatesByParams')){
            include_once 'FinancialReportWizard.inc';
        }
        $asofdate = FormatDateForStorage(Request::$r->_asofdate);
        $data = json_decode(Request::$r->_data, true);
        $location = Request::$r->_location;
        $isIr = Request::$r->_isIr;
        $useCache = count($data) > 1;
        foreach ($data as &$col){
            $col = array_merge($col, getPreviewDatesByParams($asofdate, $col['colPeriod'],
                                                                   $col['colPeriodOffset'], $col['colPeriodOffsetBy'], $isIr, $useCache, $location ));
        }
        return json_encode($data);
    }

    /**
     * QRequest Handler to preview Dates for Report Filters
     *
     * @return string
     */
    function getPreviewDatesByReport()
    {
        if ( ! function_exists('getPreviewDatesByRecordNo') ) {
            include_once 'FinancialReportWizard.inc';
        }
        $asOfDate = FormatDateForStorage(Request::$r->_asofdate);
        $location = Request::$r->_location;
        $dates = getPreviewDatesByRecordNo(Request::$r->_r, 5, $asOfDate, $location);

        return json_encode($dates);
    }

}
