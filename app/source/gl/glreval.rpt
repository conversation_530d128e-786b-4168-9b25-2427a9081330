<?
/**
*    FILE:        glreval.rpt
*    AUTHOR:        <PERSON>
*    DESCRIPTION:    rpt file for General Ledger Revaluation Report
*
*    (C) 2008, Intacct Corporation, All Rights Reserved
*
*    This document contains trade secret data that belongs to Intacct
*    Corporation and is protected by the copyright laws.  Information
*    herein may not be used, copied or disclosed in whole or in part
*    without prior written consent from Intacct Corporation.
*/

require_once 'util.inc';

/**
 * @var ExchangeRateTypesManager $exchMgr    WI-38714
 */
$exchMgr = Globals::$g->gManagerFactory->getManager('exchangeratetypes');
$rtype = $exchMgr->GetDefaultExchangeRateType(true);

$postDraftJEDef = postDraftJEReval::addDraftJEOptionAndButtonIfPermitted();
$kSchemas['glreval'] = array (
    'schema' => array (
        'REPORTINGPERIOD'    => 'reportingperiod',
        'LOCATION'        => 'location',
        'DEPARTMENT'        => 'department',
        'CURRENCY'        => 'currency',
    ),
    'individualreport' => array (
        'LOCATION'    => array ( 'default' => false ),
        'DEPARTMENT'    => array ( 'default' => false ),
    ),
    'promptonrun' => array (
        'REPORTINGTIMEPERIODFIELDS'    => array ( 'default' => false ),
        'LOCATION'            => array ( 'default' => false ),
        'DEPARTMENT'            => array ( 'default' => false ),
    ),
    'fieldgroup' => array (
        'REPORTINGTIMEPERIODFIELDS' => $gReportingTimePeriodFieldGroup,
        'STARTENDDATES' => $gStartDateEndDateFieldGroup,
        'SORTANDSUMMARYLINE' => array(
            'fields' => array(
                array(
                    'fullname' => 'IA.SORT_BY',
                    'type' => array(
                        'type' => 'enum',
                        'ptype' => 'enum',
                        'validvalues' => array('ACCT', 'DESCRIPTION, LINE'),
                        'validlabels' => array('IA.ACCOUNT', 'IA.DESCRIPTION_LINE'),
                    ),
                    'default' => 'ACCT',
                    'path' => 'SORTBY',
                ),
                array(
                    'fullname' => 'IA.SHOW_TRANSACTION_PRESENTATION',
                    'type' => array(
                        'ptype' => 'radio',
                        'type' => 'radio',
                        'validlabels' => array('IA.SUMMARY', 'IA.DETAILS'),
                        'validvalues' => array('S', 'D'),
                    ),
                    'path' => 'showdetail',
                    'default' => 'S'
                ),
            ),
        ),
    ),
    'fieldinfo' => array (
        'userprefs' => true,
        'lines' => array(
            $gTimePeriodGroupFieldInfo,
            array(
                'title' => 'IA.FILTERS',
                'fields' => array(
                    array (
                        'fullname' => 'IA.ACCOUNT_FROM',
                        'type' => array (
                            'ptype' => 'ptr',
                            'type' => 'ptr',
                            'entity' => "baseaccount",
                            'pickentity' => 'baseaccountpick',
                            'maxlength' => 40,
                            'format' => $gEntityIDFormat
                        ),
                        'assist' => 'fat',
                        'path' => 'STARTINGACCOUNTNUMBER',
                        'noview' => true,
                        'noedit' => true,
                        'nonew' => true,
                        'onchange' => "SetToValue(this, 'ENDINGACCOUNTNUMBER')",
                    ),
                    array (
                        'fullname' => 'IA.ACCOUNT_TO',
                        'type' => array (
                            'ptype' => 'ptr',
                            'type' => 'ptr',
                            'entity' => "baseaccount",
                            'pickentity' => 'baseaccountpick',
                            'maxlength' => 40,
                            'format' => $gEntityIDFormat
                        ),
                        'assist' => 'fat',
                        'path' => 'ENDINGACCOUNTNUMBER',
                        'noview' => true,
                        'noedit' => true,
                        'nonew' => true
                    ),
                    array (
                        'fullname' => 'IA.ACCOUNT_GROUP',
                        'type' => array (
                            'ptype' => 'ptr',
                            'type' => 'ptr',
                            'pick_url' => 'picker.phtml?.shownocomputation=1&.shownostatisticalgrpacct=1&.shownosysgroups=1',
                            'entity' => "acctgrppick",
                            'pickentity' => "acctgrppick",
                            'maxlength' => 20,
                            'format' => $gEntityIDFormat
                        ),
                        'assist' => 'fat',
                        'desc' => 'IA.GLACCTGRP',
                        'path' => 'GLACCTGRP',
                        'shownocomputation' => true,
                        'shownostatisticalgrpacct' => true,
                        'shownosysgroups' => true,
                        'noview' => true,
                        'noedit' => true,
                        'nonew' => true
                    ),
                    array (
                        'fullname' => 'IA.REVALUATION_AS_OF_DATE',
                        'type' => array (
                            'ptype' => 'date',
                            'type' => 'date',
                            'maxlength' => 12
                        ),
                        'required' => 'true',
                        'path' => 'REVALDATE',
                        'value' => GetCurrentDate()
                    ),
                    array ( 
                        'fullname' => 'IA.REVALUATION_EXCHANGE_RATE_TYPES',
                        'type' => array ( 
                            'ptype' => 'ptr',
                            'type' => 'ptr',
                            'entity' => 'exchangeratetypesall',
                            'pick_url' => 'picker.phtml?.hidecustom=1',
                            'comboCacheKey' => 'hidecustom',
                            'maxlength' => 40,
                        ),
                        'hidecustom' => true,
                        'default' => $rtype[0]['NAME'],
                        'required' => 'true',
                        'assist' => 'fat',
                        'path' => 'EXCHRATETYPE',
                        'noview' => true,
                        'noedit' => true,
                        'nonew' => true
                    ),
                    // $gCurrencyNoBaseFilter,
                    $gGLLocationPick,
                    $gDepartmentPick,
                    $gLocationSubFilter,
                ),
            ),
            $postDraftJEDef,
            array(
                'title' => 'IA.FORMAT',
                'fields' => array(
                        array(
                            'fullname' => '',
                            'type' => array('type' => 'fieldgroup'),
                            'path' => 'SORTANDSUMMARYLINE',
                            'group_separator' => true
                        ),
                        $gPageOrientation
                )
            )
        ),
    ),
    'controls' => $gInvRptControls,
    'layout' => 'frame',
    'layoutproperties' => $gInvRptLayoutProperties,
    'printas'=> 'IA.GENERAL_LEDGER_REVALUATION_REPORT',
    'xsl_file' => 'glreval',
    'module' => 'gl',
    'helpfile' => 'Running_GL_Revaluation_reports',
);


