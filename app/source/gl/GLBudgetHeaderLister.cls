<?

class GL<PERSON><PERSON><PERSON><PERSON>eader<PERSON>ister extends NLister
{

    /* @var bool $isMCMESubscribed */
    var $isMCMESubscribed;
    /* @var bool $paInstalled */
    var $paInstalled;
    /* @var bool $isPJBudgetFeatureEnabled */
    var $isPJBudgetFeatureEnabled;

    /**
     * @var array $internalVal
     */
    private $internalVal = [];

    /**
     * @var string[]
     */
    protected $additionalTokens = [
        'IA.BUDGET_DETAILS',
        'IA.THE_DETAILS_OF_THE_BUDGET_WILL_ALSO_BE_DELETED'
    ];

    function __construct()
    {
        $params = array(
        'entity'        =>  'glbudgetheader',
        'title'            =>    "IA.BUDGETS_REPOSITORY",
        'helpfile'        =>    'Viewing_and_Managing_the_Budgets_List',
        'fields'        =>    array('BUDGETID','DESCRIPTION','WHENCREATED','USER', 'DEFAULT_BUDGET', 'RECORDNO', 'SYSTEMGENERATED'),
        'nofilteronthesefields' => array('DEFAULT_BUDGET', 'ISPABUDGET', 'ISPCNBUDGET'),
        'entitynostatus'=>  true,
        'formmethod'    => 'POST',
        'nonencodedfields' => ["'GLBUDGETITEM'", "'VIEWEXCEL'"],
        );


        global $kATLASid;
        //Enable ISCONSOLIDATED and CURRENCY field in MCME companies or in multi entity companies having IGC installed
        $this->isMCMESubscribed = (IsMCMESubscribed() || (IsMultiEntityCompany() && IsModuleIdInstalled($kATLASid)));

        if ($this->isMCMESubscribed) {
            $params['fields'] = array(
             'BUDGETID','DESCRIPTION','WHENCREATED','USER', 'DEFAULT_BUDGET', 'ISCONSOLIDATED', 'CURRENCY',
             'RECORDNO', 'SYSTEMGENERATED'
            );
            $params['nofilteronthesefields'][] = 'ISCONSOLIDATED';
        }

        // set import button details
        $params['importtype']       = 'budget';
        $params['importperm']       = 'gl/lists/glbudgetitem/create';
        $params['importbuttonname'] = 'IA.IMPORT';
        $params['transEntity']      = 'glbudgetitem';

        if ( PASetupManager::projectEstimatesAvailable() ) {
            $params['fields'][] = 'ISPABUDGET';
        }
        // We will show ISPCNBUDGET field only if PA & CRE is subscribed and feature is enabled...
        $this->paInstalled = IsInstalled(Globals::$g->kPAid);
        $this->isPJBudgetFeatureEnabled = CRESetupManager::isCREInstalled();
        if ( $this->paInstalled && $this->isPJBudgetFeatureEnabled ) {
            $params['fields'][] = 'ISPCNBUDGET';
        }

        parent::__construct($params);
        $this->addLabelMapping('WHENCREATED', 'IA.CREATED_ON', true);
        $this->addLabelMapping('USER', 'IA.MODIFIED_BY', true);
        $this->addLabelMapping('ISCONSOLIDATED', 'IA.CONSOLIDATED', true);
    }

    /**
     * @return array
     */
    function BuildQuerySpec()
    {
        $querySpec = parent::BuildQuerySpec();

        // Filtering out PJestimate budgets if not available
        if ( !PASetupManager::projectEstimatesAvailable() ) {
            $querySpec['filters'][0][] = [
                'operator' => 'OR',
                'filters' => [
                    ['ISPABUDGET', 'IS NULL'],
                    ['ISPABUDGET', '=', 'false'],
                ]
            ];
        }

        // Filtering out PCN budgets if Construction is not enabled
        if (!$this->isPJBudgetFeatureEnabled) {
            $querySpec['filters'][0][] = [
                'operator' => 'OR',
                'filters' => [
                    ['ISPCNBUDGET', 'IS NULL'],
                    ['ISPCNBUDGET',  '=' ,'false'],
                ]
            ];
        }

        return $querySpec;
    }

    function BuildTable() 
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $_sess = Session::getKey();
        
        parent::BuildTable();
        $this->internalVal = $this->entityMgr->_TransformFlatValuesToExternal($this->values);
        $mod = Request::$r->_mod;
        $id  = GetOperationId($mod . '/lists/glbudgetitem');
        $repid  = GetOperationId($mod . '/reports/glbudget');
        /** @noinspection PhpUnusedLocalVariableInspection */
        $perm = GetOperationId('gl/lists/glbudgetheader/view');

        $budgetDetailsText = GT($this->textMap, 'IA.BUDGET_DETAILS');
        $exportText = GT($this->textMap, 'IA.EXPORT');

        for ($i = 0; $i < count($this->table); $i++) {
            $owner = $this->GetObjectOwnership($i);
            $ownerloc = $this->GetObjectOwnerLocation($i);
            /* BudgetPeriods */
            $budgetHeader = $this->table[$i]['BUDGETID'];
            $dst   = 'lister.phtml';            
            $param = "&.it=glbudgetitem&.op=$id&.budgetKey=".$this->table[$i]['RECORDNO']."&.budgetHeader=".urlencode($budgetHeader);
            $url = CallUrl(ExtendUrl($dst, $param));
            if($owner > 0 && $ownerloc ) { 
                $url = $this->_calcSlideUrl($url, $ownerloc);
            }
            $urlvar = '<a href="' . $url . '" ' . '>'.$budgetDetailsText.'</a>';
            $this->table[$i]["'GLBUDGETITEM'"] = $urlvar;

            $dst   = 'reporteditor.phtml';            
            $param = "&.op=$repid&import=true&showNotes=true&budgetKey=".$this->table[$i]['RECORDNO']."&total=H";
            $url = CallUrl(ExtendUrl($dst, $param));
            
            $target = 'target="expBudget"';
            if($owner > 0 && $ownerloc ) { 
                $url = $this->_calcSlideUrl($url, $ownerloc, true);
                $target = '';
            }
            $urlvar = '<a href="' . $url . '" '.$target.'>'.$exportText.'</a>';
            $this->table[$i]["'VIEWEXCEL'"] = $urlvar;

        }
        
        $flds = array("'GLBUDGETITEM'", 'BUDGETID','DESCRIPTION', 'WHENCREATED','USER', 'DEFAULT_BUDGET',"'VIEWEXCEL'");

        if ($this->isMCMESubscribed) {
            $flds = array(
                "'GLBUDGETITEM'", 'BUDGETID','DESCRIPTION', 'WHENCREATED','USER', 'DEFAULT_BUDGET',
                'ISCONSOLIDATED', 'CURRENCY', "'VIEWEXCEL'"
            );
        }

        if ( PASetupManager::projectEstimatesAvailable() ) {
            $flds[] = 'ISPABUDGET';
        }
        if ( $this->paInstalled && $this->isPJBudgetFeatureEnabled ) {
            $flds[] = 'ISPCNBUDGET';
        }

        $this->SetOutputFields($flds, []);
    }

    /**
     * Override as necessary in sub-class to provide alternate dst, args, text or tip
     * based on the current row
     *
     * @param int   $i
     * @param array $vals
     *
     * @return array
     */
    function calcDeleteUrlParms($i, $vals)
    {
        $ret = array();

        if ( $this->internalVal[$i]['ISPABUDGET'] == 'true' || $this->internalVal[$i]['ISPCNBUDGET'] == 'true' || $this->isSystemGeneratedBudget($i) ) {
            $ret['url'] = '';
            $ret['text'] = '';
            $ret['nolink'] = true;
        } else {
            $ret = parent::calcDeleteUrlParms($i, $vals);
        }

        return $ret;
    }

    /**
     * Override as necessary in sub-class to provide alternate dst, args, text or tip
     * based on the current row
     *
     * @param int   $i
     * @param array $vals
     *
     * @return array
     */
    function calcEditUrlParms($i, $vals)
    {
        $ret = array();
        if ( $this->isSystemGeneratedBudget($i) ) {
            $ret['url'] = '';
            $ret['text'] = '';
            $ret['nolink'] = true;
        } else {
            $ret = parent::calcEditUrlParms($i, $vals);
        }

        return $ret;
    }

    /**
     * @param int $i
     *
     * @return string
     */
    function calcDeleteMessage($i) 
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $p = &$this->_params;
        $t = &$this->table;

        $res = QueryResult(array("SELECT count(*) budgetcount	FROM glbudget WHERE BUDGETKEY = :1 AND cny# =:2 ",
            $t[$i]['RECORDNO'], GetMyCompany()));

        if($res[0]['BUDGETCOUNT'] && $res[0]['BUDGETCOUNT'] >= 1) {
            return I18N::getSingleToken("IA.THE_DETAILS_OF_THE_BUDGET_WILL_ALSO_BE_DELETED");
        } else {
            return parent::calcDeleteMessage($i);
        }
        
    }

    /**
     * @param int $i
     *
     * @return bool
     */
    protected function isSystemGeneratedBudget($i)
    {
        $ret = false;

        if ( $this->isMCMESubscribed && $this->internalVal[$i]['SYSTEMGENERATED'] == 'true' ) {
            $ret = true;
        }

        return $ret;
    }
}
 

