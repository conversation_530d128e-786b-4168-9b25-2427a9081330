<?
/**
 *    FILE:            opalljournalpick.ent
 *    AUTHOR:          ravichandra.pandiri
 *    DESCRIPTION:
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

$kSchemas['opalljournalpick'] = array(
    'schema' => array( 'PICKID'    => 'opalljournalpick',
        'SYMBOL' => 'symbol',
        'TITLE' => 'title',
        'STATISTICAL' => 'statistical',
        'BOOKID' => 'bookid',
        'ADJ'   => 'adj',
        'BOOKTYPE' => 'booktype',
        'STATUS' => 'status',
        'OPERATIONAL' => 'operational'),
    'object' => array( 'PICKID', 'SYMBOL', 'TITLE', 'STATISTICAL', 'BOOKID', 'ADJ', 'BOOKTYPE', 'STATUS', 'OPERATIONAL' ),
    'fieldinfo' => array(
        array (
            'fullname' => 'IA.JOURNAL',
            'type' => array ( 'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50 ),
            'path' => 'PICKID'
        ),
        array (
            'path' => 'OPERATIONAL',
            'fullname' => 'IA.MANAGEMENT_REPORTING',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 20,
        ),
    ),
    'table'     => 'v_opalljournalpick',
    'vid'         => 'PICKID',
    'module'     => 'gl',
    'printas'    => 'IA.JOURNAL',
    'pluralprintas'    => 'IA.JOURNALS'
);

