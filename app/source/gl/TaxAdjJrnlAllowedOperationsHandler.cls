<?php
/**
 * TaxAdjJrnlAllowedOperationsHandler
 *
 * <AUTHOR> Behere
 * @copyright Copyright (C)2024 Sage Intacct Corporation, All Rights Reserved
 *
 */

class TaxAdjJrnlAllowedOperationsHandler extends AllowedOperationsHandler
{

    public function __construct(EntityManager $entManager)
    {
        parent::__construct($entManager);
        $this->taxAdjJrnlMgr = Globals::$g->gManagerFactory->getManager('taxadjjrnl');
    }
    protected function canDelete(array $record, string $moduleKey = null) : bool
    {
        if ( IsMCMESubscribed() ) {
            return $this->canEditOrDeleteCNBook($record['SYMBOL']);
        }
        return true;
    }
    protected function canEdit(array $record, string $moduleKey = null) : bool
    {
        if ( IsMCMESubscribed() ) {
            return $this->canEditOrDeleteCNBook($record['SYMBOL']);
        }

        return true;
    }

    protected function canMemorize(array $record, string $moduleKey = null) : bool
    {
        // Inactive journals cannot be memorized
        $canMemorize = $record['STATUS'] === 'active' ? true : false;
        if ( IsMultiEntityCompany() ) {
            $canMemorize = $canMemorize && $this->canEditOrDeleteCNBook($record['SYMBOL']);
        }
        // Disable direct posting journals cannot be memorized
        $canMemorize = $canMemorize && !($this->taxAdjJrnlMgr->IsDirectJrnlPostingDisabled($record['SYMBOL']));
        
        return $canMemorize;
    }

    protected function getOperations() : array
    {
        $operations = parent::getOperations();
        $operations = array_merge($operations, [ 'canMemorize', 'canEdit', 'canDelete' ]);

        return $operations;
    }

    protected function getFieldsForPermissionChecks() : array
    {
        $fields = parent::getFieldsForPermissionChecks();
        $fields = array_merge($fields, [ 'SYMBOL', 'STATUS' ]);
        return $fields;
    }

    private function canEditOrDeleteCNBook(string $symbol)
    {
        if ( GLBookManager::IsConsolidationBook($this->taxAdjJrnlMgr->GetJournalBook($symbol), false) ) {
            return false;
        }

        return true;
    }
    /**
     * Override of parent method, necessary for non-megalized entity.
     *
     * @inheritDoc
     */
    protected function isValidOwner(array $record): bool
    {
        return true;
    }
}