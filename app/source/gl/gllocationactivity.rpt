<?
/**
 *    FILE:        gllocationactivity.rpt
 *    AUTHOR:        rs
 *    DESCRIPTION:    rpt file for comparative trial balance
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

$kSchemas["gllocationactivity"] = array(
    'schema' => array(
        array(
            'PERIOD' => 'Period',
            'ASOFDATE' => 'As of Date',
            'LOCATION' => 'Location',
            'GLACCTGRP' => 'Account Group',
            'INCLUDESTATACCT' => 'Include Statistical Accounts',
            'SHOWSUMMARY' => 'Show Summary Only',
            'SHOWSUBLOCATIONS' => 'Show by Location',
            'SHOWSUBDEPARTMENTS' => 'Show by Department',
            'REPORTINGBOOK' => 'Reporting Method',
            'FILTERTRANSACTIONS' => 'filtertransactions',
            'DISPLAYBREAKUP' => 'Display Department Location with Activity'

        )
    ),
    'individualreport' => array(
        'LOCATION' => array('default' => false),
    ),
    'promptonrun' => array(
        'REPORTINGTIMEPERIODFIELDS' => array('default' => false),
        'LOCATION' => array('default' => false)
    ),
    'fieldgroup' => array(
        'REPORTINGTIMEPERIODFIELDS' => $gReportingTimePeriodFieldGroup,
        "SHOWDEPLOCWITHACT" => array(
            'fields' => array(
                array(
                    'fullname' => 'IA.SHOW_DEPARTMENT_LOCATION_WITH_ACTIVITY',
                    'type' => array(
                        'ptype' => 'boolean',
                        'type' => 'char',
                        'validvalues' => $gBooleanValues,
                        '_validivalues' => $gBooleanIValues,
                    ),
                    'path' => 'DISPLAYBREAKUP',
                    'default' => 'false',
                    'udd' => false
                ),
                array(
                    'fullname' => 'IA.SHOW_BY_LOCATION',
                    'type' => array(
                        'ptype' => 'boolean',
                        'type' => 'char',
                        'validvalues' => $gBooleanValues,
                        '_validivalues' => $gBooleanIValues,
                    ),
                    'path' => 'SHOWSUBLOCATIONS',
                    'default' => 'true',
                    'udd' => false
                ),
                array(
                    'fullname' => 'IA.SHOW_BY_DEPARTMENT',
                    'type' => array(
                        'ptype' => 'boolean',
                        'type' => 'char',
                        'validvalues' => $gBooleanValues,
                        '_validivalues' => $gBooleanIValues,
                    ),
                    'path' => 'SHOWSUBDEPARTMENTS',
                    'default' => 'false',
                    'udd' => false
                ),
            )
        ),
        "LOCATIONGRPFIELD" => array(
            "fields" => array(
                $gAllLocationPick,
            )
        )
    ),
    'fieldinfo' => array(
        'userprefs' => true,
        'lines' => array(
            array(
                'title' => 'IA.TIME_PERIOD',
                'fields' => array(
                    $gReportingTimePeriodField
                ),
            ),
            array(
                'title' => 'IA.FILTERS',
                'fields' => array(
                    array(
                        'fullname' => 'IA.ACCOUNT_GROUP',
                        'type' => array(
                            'ptype' => 'ptr',
                            'type' => 'ptr',
                            'pick_url' => 'picker.phtml?.shownocomputation=1&.shownostatisticalgrpacct=1&.shownosysgroups=1',
                            'entity' => "acctgrppick",
                            'pickentity' => "acctgrppick",
                            'maxlength' => 20,
                            'format' => $gEntityIDFormat
                        ),
                        'assist' => 'fat',
                        'desc' => 'IA.GLACCTGRP',
                        'path' => 'GLACCTGRP',
                        'shownocomputation' => true,
                        'shownostatisticalgrpacct' => true,
                        'shownosysgroups' => true,
                        'noview' => true,
                        'noedit' => true,
                        'nonew' => true,
                        "group_separator" => true
                    ),
                    array(
                        'fullname' => 'IA.INCLUDE_STATISTICAL_ACCOUNTS_IN_OUTPUT',
                        'type' => array(
                            'ptype' => 'boolean',
                            'type' => 'char',
                            'validvalues' => $gBooleanValues,
                            '_validivalues' => $gBooleanIValues,
                        ),
                        'path' => 'INCLUDESTATACCT',
                        'default' => 'false',
                        "group_separator" => true
                    ),
                    array(
                        "fullname" => "",
                        'type' => array('type' => 'fieldgroup'),
                        "path" => "GLACCTGRPINCLUDESTATACCT",
                        "group_separator" => true
                    ),
                    array(
                        'fullname' => 'IA.DIMENSION_FILTERS',
                        '_func' => 'FieldSetLayout',
                        'title' => '',
                        'columns' => array(
                            $gLocationSubFilter,
                            array(
                                "fullname" => "",
                                'type' => array('type' => 'fieldgroup'),
                                "path" => "LOCATIONGRPFIELD",
                                "group_separator" => true
                            ),
                        )
                    ),
                )
            ),
            array(
                'title' => 'Format',
                'fields' => array(
                    array(
                        "fullname" => "",
                        'type' => array('type' => 'fieldgroup'),
                        "path" => "SHOWDEPLOCWITHACT",
                        "group_separator" => true
                    ),

                    array(
                        'fullname' => 'IA.SHOW_SUMMARY_ONLY',
                        'type' => array(
                            'ptype' => 'boolean',
                            'type' => 'char',
                            'validvalues' => $gBooleanValues,
                            '_validivalues' => $gBooleanIValues,
                        ),
                        'path' => 'SHOWSUMMARY',
                        'default' => 'false',
                    ),

                )
            ),

        )
    ),
    'controls' => array(
        kShowHTML,
        kShowPDF,
        kShowExcel,
        kShowCSV,
        kShowText,
        kShowBackground,
        kEmail,
        kAddtoDashboard,
        kMemorize,
    ),
    'layout' => 'frame',
    'popupfilterflds' => array(),
    'layoutproperties' => $gInvRptLayoutProperties,
    'xsl_file' => 'gllocationactivity',
    'printas' => 'IA.GENERAL_LEDGER_LOCATION_ACTIVITY_REPORT',
    'module' => 'gl',
    'helpfile' => 'Running_a_Location_Activity_Report',
);

