<?

/**
 * Picker class for User-Defined journals
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Picker class for UserAdjJrnlpickPicker object
 */
class UserAdjJrnlpickPicker extends NPicker
{

    function __construct()
    {
        parent::__construct(
            array(
                'entity' => 'useradjjrnlpick',
                'pickfield' => 'PICKID',
                'fields' => array('PICKID', 'STATUS'),
            )
        );
    }

    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     *
     * @bool $addYuiCss  include the YUI css files
     * @param bool $addYuiCss
     */
    function showScripts($addYuiCss = true)
    {
        $_refresh = Request::$r->_refresh;
        parent::showScripts($addYuiCss);
        UIUtils::PrintLayerSetupForBrowser();
        UIUtils::PrintSetField($_refresh);
    }

}


