<?php
/*
 * Entity definitaion for iaglacctgrp
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

$kSchemas['iaglacctgrp'] = array(
    'object' => array(
        'RECORDNO', 'NAME', 'NORMAL_BALANCE', 'ISLEAF', 'ASOF', 'TITLE',
        'TOTALTITLE', 'DBCR', 'MEMBERTYPE', 'INDUSTRYCODE'
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'NAME' => 'name',
        'NORMAL_BALANCE' => 'normal_balance',
        'ISLEAF' => 'isleaf',
        'ASOF' => 'asof',
        'TITLE' => 'title',
        'TOTALTITLE' => 'totaltitle',
        'DBCR' => 'dbcr',
        'MEMBERTYPE' => 'membertype',
        'INDUSTRYCODE' => 'industrycode',
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'PARENTKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'iaglacctgrpmember',
            'path' => 'GLACCTGRPS'
        ),
        array(
            'fkey' => 'PARENTKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'iaglcoacatmember',
            'path' => 'GLCATGRPS'
        ),
        array(
            'fkey' => 'PARENTKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'iaglcoacatmember',
            'path' => 'GLSTATCATGRPS'
        ),
        array(
            'fkey' => 'PARENTKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'iaglcompgrpmember',
            'path' => 'GLCOMPGRPS'
        ),
        array(
            'fkey' => 'PARENTKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'iagldimgrpmember',
            'path' => 'GLDIMGRPS'
        ),
     ),
    'fieldinfo' => array(
            array(
                'path' => 'HELPTEXT',
                'type' => array(
                    'type' => 'text',
                )
            ),
            array(
            'path' => 'GLACCTGRPS',
            'clazz'=> 'AGField',
            'hidden' => true,
            "noLabel" => true,
            'picklist1Name' => 'IA.ACCOUNT_GROUP',
            'readonlylabel' => 'IA.MEMBER_LIST',
            'addbuttonname' => 'IA.ADD_GROUP',
            "sortable" => true,
            'picksectionheader' => 'IA.SPECIFY_ACCOUNT_GROUPS_TO_INCLUDE',
            'listsectionheader' => 'IA.GROUP_MEMBERS',
            'picksectionhelp' => 'IA.GROUP_TYPE_CONTAINS_OTHER_ACCOUNT_GROUPS',
            'pickvaluekeyname' => 'CHILDNAME',


                'type' => array(
                    'type' => 'multipick',
                    'ptype' => 'acctgrpptr',
                    'entity' => 'iaglacctgrp',
                    'pickentity' => 'iaacctgrppick',
                    'restrict' => array(
                        array(
                            'field' => 'INDUSTRYCODE',
                            'context' => array('INDUSTRYCODE'),
                            'pickField' => 'INDUSTRYCODE',
                            'entityContext' => true
                        ),
                        array('value' => array('A', 'G', 'S', 'C', 'T', 'L'), 'pickField' => 'MEMBERTYPE'),
                    ),
                    'validvalues' => array(),
                    'validlabels' => array(),
                    'industry' => true,
                ),
            ),
            array(
            'path' => 'GLCATGRPS',
            'clazz'=> 'AGField',
            'hidden' => true,
            "noLabel" => true,
            'picklist1Name' => 'IA.CATEGORY',
            'readonlylabel' => 'IA.MEMBERS',
            'addbuttonname' => 'IA.ADD_TO_GROUP',
            "sortable" => true,
            'picksectionheader' => 'IA.SPECIFY_ACCOUNT_GROUPS_TO_INCLUDE', 
            'listsectionheader' => 'IA.GROUP_MEMBERS',
            'picksectionhelp' => 'IA.CATEGORY_GROUP_WILL_INCLUDE_ALL_ACCOUNTS_TAGGED',
            'pickvaluekeyname' => 'CATEGORYNAME',


            'type' => array (
            'type' => 'multipick',  
            'ptype' => 'acctgrpptr',
            'entity' => 'iacoacategory',
            'pickentity' => 'iacoacategory',
                
                'restrict' => array (
            array( 'value' => 'false',
            'pickField' => 'STATISTICAL',
            ),
            array(
            'field' => 'INDUSTRYCODE',
            'context' => array('INDUSTRYCODE'),
                        'pickField' => 'INDUSTRYCODE',
            'entityContext' => true
            ),
                ),
                'validvalues' => array(),
                'validlabels' => array(),
            ),
            ),
            array(
            'path' => 'GLSTATCATGRPS',
            'clazz'=> 'AGField',
            'hidden' => true,
            "noLabel" => true,
            'picklist1Name' => 'IA.CATEGORY',
            'readonlylabel' => 'IA.MEMBERS',
            'addbuttonname' => 'IA.ADD_TO_GROUP',
            "sortable" => true,
            'picksectionheader' => 'IA.SPECIFY_STAT_ACCOUNT_GROUPS_TO_INCLUDE',
            'listsectionheader' => 'IA.GROUP_MEMBERS',
            'picksectionhelp' => ' IA.CATEGORY_GROUP_WILL_INCLUDE_STASTICAL_ACCOUNTS',
            'pickvaluekeyname' => 'CATEGORYNAME',


            'type' => array (
            'type' => 'multipick',  
            'ptype' => 'acctgrpptr',
            'entity' => 'iacoacategory',
            'pickentity' => 'iacoacategory',
                'restrict' => array (
            array( 'value' => 'true',
            'pickField' => 'STATISTICAL',
            ),
            array(
            'field' => 'INDUSTRYCODE',
            'context' => array('INDUSTRYCODE'),
                        'pickField' => 'INDUSTRYCODE',
            'entityContext' => true
            ),
                ),
                'validvalues' => array(),
                'validlabels' => array(),
            ),
            ),
            array(
            'path' => 'GLCOMPGRPS',
            'clazz'=> 'AGCompField',
            'hidden' => true,
            "noLabel" => true,
            'readonlylabel' => 'Formula',
            
            'type' => array (
            'type' => 'multipick',  
            'ptype' => 'acctgrpptr',
            'entity' => 'iaglacctgrp',
            'pickentity' => 'iaacctgrppick',
            'validvalues' => array(),
            'validlabels' => array(),
            ),
            
            'accountGroup' => array (
            'type'=> array(
            'type' => 'multipick',  
            'ptype' => 'acctgrpptr',
            'entity' => 'iaglacctgrp',
                    'pickentity' => 'iaacctgrppick',
                    'restrict' => array (
                        array(
                            'field' => 'INDUSTRYCODE',
                            'context' => array('INDUSTRYCODE'),
                            'pickField' => 'INDUSTRYCODE',
                            'entityContext' => true
                        ),
                    ),
                    'validvalues' => array(),
                    'validlabels' => array(),
            ),
            ),
            
            'decimalType' => array (
            'ptype' => 'decimal',
            'type' => 'decimal',
            'precision' => 5,
            'maxlength' => 12,
            'format' => $gDecimalFormat,
            'size' => 15,
            ),
            
            'precissionType' => array (
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9'),
                'validvalues' => array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9'),
            ),
            
            'displayAsType' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.VALUE', 'IA.PERCENT', 'IA.RATIO_WITH_DECIMALS', 'IA.RATIO_WITHOUT_DECIMALS', 'IA.DAILY_AVERAGE', 'IA.WEEKLY_AVERAGE', 'IA.MONTHLY_AVERAGE', 'IA.QUARTERLY_AVERAGE'),
                'validvalues' => array('V', 'P', 'R', 'F', 'D', 'W', 'M', 'Q'),
            ),
            'operatortype' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.PLUS', 'IA.U2212', 'IA.U00D7', 'IA.U00F7'), 
                'validvalues' => array('A', 'S', 'M', 'D'),
            ),
            'periodType' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.FOR_PERIOD', 'IA.START_OF_PERIOD', 'IA.END_OF_PERIOD'),
                'validvalues' => array('P', 'B', 'E'),
            ),
            'alignType' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.LEFT', 'IA.RIGHT'),
                'validvalues' => array('1', '2'),
            ),
            ),
            $gRecordNoFieldInfo,
            array(
            'fullname' => 'IA.NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 80,
                'size' => 80,
                'format' => '/^.{1,80}$/'
            ),
            'required' => true,
            'desc' => 'IA.NAME',
            'path' => 'NAME'
            ),
            array(
            'fullname' => 'IA.CALCULATION_METHOD',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.FOR_PERIOD', 'IA.START_OF_PERIOD', 'IA.END_OF_PERIOD'),
                'validvalues' => array('P', 'B', 'E'),
            ),
            'desc' => 'IA.CALCULATION_METHOD',
            'path' => 'ASOF'
            ),
            array(
            'fullname' => 'IA.DEBIT_CREDIT',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.NO_FILTER', 'IA.DEBIT_ONLY', 'IA.CREDIT_ONLY'),
                'validvalues' => array('B', 'D', 'C'),
                '_validivalues' => array('B', 'D', 'C')
            ),
            'desc' => 'IA.DEBIT_CREDIT',
            'path' => 'DBCR'
            ),
            array(
            'fullname' => 'IA.DISPLAY_ON_REPORT_AS',
            'type' => array('ptype' => 'text', 'type' => 'text', 'maxlength' => 80),
            'required' => false,
            'desc' => 'IA.DISPLAY_ON_REPORT_AS',
            'path' => 'TITLE'
            ),
            array(
            'fullname' => 'IA.DISPLAY_TOTAL_LINE_AS',
            'type' => array('ptype' => 'text', 'type' => 'text', 'maxlength' => 80),
            'required' => false,
            'desc' => 'IA.DISPLAY_TOTAL_LINE_AS',
            'path' => 'TOTALTITLE'
            ),
            array(
            'fullname' => 'IA.NORMAL_BALANCE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.DEBIT', 'IA.CREDIT'),
                'validvalues' => array('debit', 'credit'),
                '_validivalues' => array('1', '-1')
            ),
            'required' => false,
            'desc' => 'IA.NORMAL_BALANCE',
            'path' => 'NORMAL_BALANCE'
            ),
            array(
            'fullname' => 'IA.TYPE_OF_ACCOUNT_GROUP',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array( 'IA.CATEGORY', 'IA.STATISTICAL_CATEGORY', 'IA.GROUPS', 'IA.COMPUTATION'),
                'validvalues' => array('Category', 'Statistical Category', 'Groups', 'Computation'),
                '_validivalues' => array('T', 'L', 'G', 'C'),
            ),
            'required' => true,
            'desc' => 'IA.TYPE_OF_ACCOUNT_GROUP',
            'path' => 'MEMBERTYPE'
            ),
            array(
                'fullname' => 'IA.STRUCTURE_TYPE',
                'type' => array(
                    'ptype' => 'enum',
                    'type' => 'enum',
                    'validlabels' => array( 'IA.GROUP_OF_ACCOUNT_GROUPS', 'IA.COMPUTATION', 'IA.CATEGORY', 'IA.STATISTICAL_CATEGORY', 'IA.DIMENSION_GROUPS', 'IA.GROUP_OF_DIMENSION_REPORT_STRUCTURES', ),
                    'validvalues' => array(
                        'Groups', 'Computation', 'Category', 'Statistical Category', 'Dimension Groups',
                        'Group of Dimension Components',
                    ),
                    '_validivalues' => array(
                        'G', 'C', 'T', 'L', 'DG', 'GD'
                    ),
                ),
            'required' => false,
            'desc' => 'IA.TYPE_OF_ACCOUNT_GROUP',
            'path' => 'DISPLAYMEMBERTYPE',
            ),         
            array(
            'path' => 'INDUSTRYCODE',
            'fullname' => 'IA.INDUSTRY',
            'desc' => 'IA.INDUSTRY',
            'required' => true,
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => [],
                'validvalues' => [],
                '_validivalues' => [],
            'forceCombo' => true,
            ),
            'showlabelalways' => true
            ),
        array(
            'path' => 'INCLUDECHILDAMT',
            'fullname' => 'IA.ROLL_UP_CHILD_AMOUNTS',
            'desc' => 'IA.ROLL_UP_CHILD_AMOUNTS',
            'type' => $gBooleanType,
        ),
    ),
    'table' => 'iaglacctgrp',
    'printas' => 'IA.GL_ACCOUNT_GROUP',
    'pluralprintas' => 'IA.GL_ACCOUNT_GROUPS',
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'module' => 'gl',
    'nosysview' => true,

    'global' => true
);
