<?php

/*
 * -License	LGPL (http://www.gnu.org/copyleft/lesser.html)
 * -Copyright	2001, Entity
 * -Author	author name, <EMAIL>
 */




/**
 * ButtonFooterTmpl Template Class.
 *
 * Generated by PHPMake Template
 */

class ButtonFooterTmpl
{
    /**
     * @var string[]
     */
    static $tokens = [
        ['id' => 'IA.VIEW'],
        ['id' => 'IA.POST'],
        ['id' => 'IA.EDIT'],
        ['id' => 'IA.SAVE'],
        ['id' => 'IA.SAVE_AND_CLOSE'],
        ['id' => 'IA.CLOSE'],
        ['id' => 'IA.OPEN'],
        ['id' => 'IA.ADD'],
        ['id' => 'IA.ADD_ROWS'],
        ['id' => 'IA.CANCEL'],
        ['id' => 'IA.PREVIOUS'],
        ['id' => 'IA.NEXT'],
        ['id' => 'IA.CONTINUE'],
        ['id' => 'IA.SAVE_AND_CONTINUE'],
        ['id' => 'IA.SAVE_AND_NEW'],
        ['id' => 'IA.DUPLICATE'],
        ['id' => 'IA.APPROVE'],
        ['id' => 'IA.UNAPPROVE'],
        ['id' => 'IA.DONE'],
        ['id' => 'IA.DELIVER'],
        ['id' => 'IA.SUBMIT'],
        ['id' => 'IA.CHECK'],
        ['id' => 'IA.SEND'],
        ['id' => 'IA.REVERSE'],
        ['id' => 'IA.PRINT_TO'],
        ['id' => 'IA.REFRESH'],
        ['id' => 'IA.RECONCILE'],
        ['id' => 'IA.SAVE_AND_PRINT'],
        ['id' => 'IA.PRINT'],
        ['id' => 'IA.VIEW_PDF'],
        ['id' => 'IA.VOID'],
        ['id' => 'IA.CONFIRM'],
    ];

    /**
     * This method accepts data and parameters by reference.
     * Those params and data are used to generate output.
     *
     * @param array $params
     * @param array $data
     *
     * @throws I18NException
     */
    function __construct(/** @noinspection PhpUnusedParameterInspection */ &$params, &$data)
    {
        $_done = Request::$r->_done;
        $_popup = Request::$r->_popup;
        // changing non-array arguments to array arguments - as required by PHP 4.0.6
        if (!is_array($data['buttons_ary'])) {
            $data['buttons_ary'] = array();
        }
        if (!is_array($data['jsargs'])) {
            $data['jsargs'] = array();
        }
        if (!QXCommon::isQuixote()) {
            I18N::addTokens(self::$tokens);
            $textMap = I18N::getText();

            $qxClose = "
                if (typeof window.parent.closeQxDialog === 'function' && window.frameElement) {
                    window.parent.closeQxDialog(window.frameElement);
                } else {
                    window.close();
                }
                ";
        ?>
        <table class="PAGEFOOTER c-inline-page-footer">
            <tr class="c-inline-page-footer-tr">
                <td class="c-inline-full-width">
                    <img src="../resources/images/ia-app/backgrounds/trans.gif" alt="" width="25" height="25" border="0">
                </td>
                <td class="c-inline-page-footer-btn-content">
                    <?
                    // Netscape can't handle an empty tag, so write a space in its place.
                    if ((count($data['buttons_ary']) < 1) && (($data['jsargs'] == '') || (count($data['jsargs']) < 1))) {
                        echo '&nbsp;';
                    }
                    if (count($data['buttons_ary']) >= 1) {
                        for ($i = 0; $i < count($data['buttons_ary']); $i++) {
                            switch ($data['buttons_ary'][$i]) {
                                case 'view_active': ?>
                                    <a href="#Skip" onmouseover="window.status='Lister status'; return true"
                                       onmouseout="window.status='';return true;" onclick="javascript:status1();"><img
                                            src="<?= IALayoutManager::getCSSButtonPath("view_active.gif"); ?>"
                                            border="0" align="absmiddle"></a>&nbsp;&nbsp;&nbsp;
                                    <?
                                    break;
                                case 'view_all': ?>
                                    <a href="#Skip" onmouseover="window.status='Lister status'; return true"
                                       onmouseout="window.status='';return true;" onclick="javascript:status1(1);"><img
                                            src="<?= IALayoutManager::getCSSButtonPath("view_all.gif"); ?>"
                                            border="0" align="absmiddle"></a>&nbsp;&nbsp;&nbsp;
                                    <?
                                    break;
                                case 'view': ?>
                                    <input class="nosavehistory" type="submit" name=".view" value="<?=GT($textMap, 'IA.VIEW');?>" onclick="doView();" disableonsubmit="true">
                                    <?
                                    break;
                                case 'post': ?>
                                    <input class="nosavehistory" type="submit" name=".post" value="<?=GT($textMap, 'IA.POST');?>" onclick="doPost();" disableonsubmit="true">
                                    <?
                                    break;
                                case 'edittxn': ?>
                                    <input class="nosavehistory" type="button" name=".edit" value="<?=GT($textMap, 'IA.EDIT');?>" onclick="editTxn();">
                                    <?
                                    break;
                                case 'save': ?>
                                    <input class="nosavehistory" type="submit" name=".save" value="<?=GT($textMap, 'IA.SAVE');?>" disableonsubmit="true">
                                    <?
                                    break;
                                case 'saveclose': ?>
                                    <input class="nosavehistory" type="submit" name=".save" value="<?=GT($textMap, 'IA.SAVE_AND_CLOSE');?>">
                                    <?
                                    break;
                                case 'close': ?>
                                    <input class="nosavehistory" type="Submit" name=".save" value="<?=GT($textMap, 'IA.CLOSE');?>" disableonsubmit="true">
                                    <?
                                    break;
                                case 'open': ?>
                                    <input class="nosavehistory" type="Submit" name=".save" value="<?=GT($textMap, 'IA.OPEN');?>" disableonsubmit="true">
                                    <?
                                    break;
                                case 'add': ?>
                                    <input class="nosavehistory" type="submit" name=".add" value="<?=GT($textMap, 'IA.ADD');?>">
                                    <?
                                    break;
                                case 'add_rows': ?>
                                    <input class="nosavehistory" type="Submit" name=".addrows" value="<?=GT($textMap, 'IA.ADD_ROWS');?>">
                                    <?
                                    break;
                                case 'cancel': ?>
                                    <? if ($_popup == 3) { ?>
                                        <input class="nosavehistory" type="submit" name=".cancel" value="<?=GT($textMap, 'IA.CANCEL');?>" onclick="parent.window.close();">
                                        <?
                                    } else if ($_popup >= 1) { ?>
                                        <input class="nosavehistory" type="submit" name=".cancel" value="<?=GT($textMap, 'IA.CANCEL');?>" onclick="<?= $qxClose; ?>">
                                        <?
                                    } else { ?>
                                        <input class="nosavehistory" type="submit" name=".cancel" value="<?=GT($textMap, 'IA.CANCEL');?>" onclick="return history.back();" disableonsubmit="true">
                                        <?
                                    } ?>
                                    <?
                                    break;
                                case 'quietcancel': ?>
                                    <input class="nosavehistory" type="Button" name=".quietcancel" value="<?=GT($textMap, 'IA.CANCEL');?>" onclick="window.close();" disableonsubmit="true">
                                    <?
                                    break;
                                case 'ret': ?>
                                    <? if ($_popup == 3) { ?>
                                        <input class="nosavehistory" type="Submit" name=".ret" value="<?=GT($textMap, 'IA.CANCEL');?>" onclick="parent.window.close();">
                                        <?
                                    } else if ($_popup >= 1) { ?>
                                        <input class="nosavehistory" type="Submit" name=".ret" value="<?=GT($textMap, 'IA.CANCEL');?>" onclick="window.close();">
                                        <?
                                    } else { ?>
                                        <input class="nosavehistory" type="Submit" name=".ret" value="<?=GT($textMap, 'IA.CANCEL');?>" disableonsubmit="true">
                                        <?
                                    } ?>
                                    <?
                                    break;
                                case 'quietclose': ?>
                                    <input class="nosavehistory" type="Button" name=".quietclose" value="<?=GT($textMap, 'IA.CLOSE');?>" onclick="(window.parent && typeof window.parent.closeQxDialog === 'function') ? window.parent.closeQxDialog(window.frameElement) : window.close()">
                                    <?
                                    break;
                                case 'prev': ?>
                                    <input class="nosavehistory" type="Submit" name=".prev" value="<?=GT($textMap, 'IA.PREVIOUS');?>" onclick="VerifyChanges(document.main.changed)">
                                    <?
                                    break;
                                case 'next': ?>
                                    <input class="nosavehistory" type="Submit" name=".next" value="<?=GT($textMap, 'IA.NEXT');?>" onclick="VerifyChanges(document.main.changed)">
                                    <?
                                    break;
                                case 'continue': ?>
                                    <input class="nosavehistory" type="Submit" name=".continue" value="<?=GT($textMap, 'IA.CONTINUE');?>">
                                    <?
                                    break;
                                case 'scontinue': ?>
                                    <input class="nosavehistory" type="submit" name=".scontinue" value="<?=GT($textMap, 'IA.SAVE_AND_CONTINUE');?>">
                                    <?
                                    break;
                                case 'snew': ?>
                                    <input class="nosavehistory" type="submit" name=".snew" value="<?=GT($textMap, 'IA.SAVE_AND_NEW');?>">
                                    <?
                                    break;
                                case 'dup': ?>
                                    <input class="nosavehistory" type="submit" name=".duplicate" value="<?=GT($textMap, 'IA.DUPLICATE');?>">
                                    <?
                                    break;
                                case 'approve': ?>
                                    <input class="nosavehistory" type="submit" name=".approve" value="<?=GT($textMap, 'IA.APPROVE');?>">
                                    <?
                                    break;
                                case 'unapprove': ?>
                                    <input class="nosavehistory" type="submit" name=".unapprove" value="<?=GT($textMap, 'IA.UNAPPROVE');?>">
                                    <?
                                    break;
                                case 'done': ?>
                                    <input class="nosavehistory" type="submit" name=".reload" value="<?=GT($textMap, 'IA.DONE');?>">
                                    <?
                                    break;
                                case 'deliver': ?>
                                    <input class="nosavehistory" type="submit" name=".deliver" value="<?=GT($textMap, 'IA.DELIVER');?>">
                                    <?
                                    break;
                                case 'submit': ?>
                                    <input class="nosavehistory" type="submit" name=".submit" value="<?=GT($textMap, 'IA.SUBMIT');?>" disableonsubmit="true">
                                    <?
                                    break;
                                case 'check': ?>
                                    <input class="nosavehistory" type="submit" name=".check" value="<?=GT($textMap, 'IA.CHECK');?>">
                                    <?
                                    break;
                                case 'send': ?>
                                    <input class="nosavehistory" type="submit" name=".send" value="<?=GT($textMap, 'IA.SEND');?>">
                                    <?
                                    break;
                                case 'reverse': ?>
                                    <input class="nosavehistory" type="Button" name=".reverse" value="<?=GT($textMap, 'IA.REVERSE');?>" onclick="CallReverseEntry(); ">
                                    <?
                                    break;
                                case 'printje': ?>
                                    <input class="nosavehistory" type="button" name=".printje" value="<?=GT($textMap, 'IA.PRINT_TO');?>" onclick="PrintTo();">
                                    <?
                                    break;
                                case 'refresh': ?>
                                    <input class="nosavehistory" type="Button" name=".refresh" value="<?=GT($textMap, 'IA.REFRESH');?>" onclick="document.forms[0].reload()">
                                    <?
                                    break;
                            }
                        }
                    }

                    if (count($data['jsargs']) >= 1) {
                        //this is for the buttons which process javascript functions on some events
                        for ($i = 0; $i < count($data['jsargs']); $i++) {
                            switch ($data['jsargs'][$i][0]) {
                                case 'reconcile': ?>
                                    <input class="nosavehistory" type="Submit" name=".reconcile" value="<?=GT($textMap, 'IA.RECONCILE');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'save': ?>
                                    <input class="nosavehistory" type="submit" name=".save" value="<?=GT($textMap, 'IA.SAVE');?>" <?= $data['jsargs'][$i][1]; ?> disableonsubmit="true">
                                    <?
                                    break;
                                case 'saveclose': ?>
                                    <input class="nosavehistory" type="submit" name=".save" value="<?=GT($textMap, 'IA.SAVE_AND_CLOSE');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'saveprint': ?>
                                    <input class="nosavehistory" type="submit" name=".saveprint" value="<?=GT($textMap, 'IA.SAVE_AND_PRINT');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'cancel':
                                    if (OptDone($_done) != '' && $data['jsargs'][$i][1] == 'yes') { ?>
                                        <input class="nosavehistory" type="Submit" name=".cancel" value="<?=GT($textMap, 'IA.CANCEL');?>" onclick="parent.rbf_dialogHide();"><!-- window.close('QuickAdds');  -->
                                        <?
                                    } else if (OptDone($_done) != '') {
                                        if ($_popup == 3) { ?>
                                            <input class="nosavehistory" type="Submit" name=".cancel" value="<?=GT($textMap, 'IA.CANCEL');?>" onclick="parent.window.close();">
                                            <?
                                        } else if ($_popup >= 1) { ?>
                                            <input class="nosavehistory 11" type="Submit" name=".cancel" value="<?=GT($textMap, 'IA.CANCEL');?>" onclick="window.close();">
                                            <?
                                        } else { ?>
                                            <input class="nosavehistory" type="Submit" name=".cancel" value="<?=GT($textMap, 'IA.CANCEL');?>" onclick="return history.back();" disableonsubmit="true">
                                            <?
                                        }
                                    }
                                    break;
                                case 'next': ?>
                                    <input class="nosavehistory" type="Submit" name=".next" value="<?=GT($textMap, 'IA.NEXT');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'ret':
                                    if ($_popup == 3) { ?>
                                        <input class="nosavehistory" type="Submit" name=".ret" value="<?=GT($textMap, 'IA.CANCEL');?>" <?= $data['jsargs'][$i][1]; ?>onclick="parent.window.close();">
                                        <?
                                    } else if ($_popup >= 1) { ?>
                                        <input class="nosavehistory" type="Submit" name=".ret" value="<?=GT($textMap, 'IA.CANCEL');?>" <?= $data['jsargs'][$i][1]; ?>onclick="window.close();">
                                        <?
                                    } else { ?>
                                        <input class="nosavehistory" type="Submit" name=".ret" value="<?=GT($textMap, 'IA.CANCEL');?>" <?= $data['jsargs'][$i][1]; ?> disableonsubmit="true">
                                        <?
                                    } ?>
                                    <?
                                    break;
                                case 'done': ?>
                                    <input class="nosavehistory" type="submit" name=".back" value="<?=GT($textMap, 'IA.DONE');?>" onclick="return history.back();">
                                    <?
                                    break;
                                case 'edittxn': ?>
                                    <input class="nosavehistory" type="button" name=".edit" value="<?=GT($textMap, 'IA.EDIT');?>" onclick="editTxn();">
                                    <?
                                    break;
                                case 'close': ?>
                                    <input class="nosavehistory" type="Submit" name=".close" value="<?=GT($textMap, 'IA.CLOSE');?>" onclick="window.close('<?= $data['jsargs'][$i][1]; ?>'); ">
                                    <?
                                    break;
                                case 'quick_pdf': ?>
                                    <input class="nosavehistory" type="Submit" name=".print" value="<?=GT($textMap, 'IA.PRINT');?>" <?= $data['jsargs'][$i][1]; ?> disableonsubmit="true">
                                    <?
                                    break;
                                case 'view_pdf': ?>
                                    <input class="nosavehistory" type="Submit" name=".print" value="<?=GT($textMap, 'IA.VIEW_PDF');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'void' : ?>
                                    <input class="nosavehistory" type="Submit" name=".void" value="<?=GT($textMap, 'IA.VOID');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'confirm': ?>
                                    <input class="nosavehistory" type="Submit" name=".confirm" value="<?=GT($textMap, 'IA.CONFIRM');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'continue': ?>
                                    <input class="nosavehistory" type="submit" name=".continue" value="<?=GT($textMap, 'IA.CONTINUE');?>" <?= $data['jsargs'][$i][1] ?>disableonsubmit="true">
                                    <?
                                    break;
                                case 'scontinue': ?>
                                    <input class="nosavehistory" type="submit" name=".scontinue" value="<?=GT($textMap, 'IA.SAVE_AND_CONTINUE');?>" <?= $data['jsargs'][$i][1] ?>>
                                    <?
                                    break;
                                case 'snew': ?>
                                    <input class="nosavehistory" type="submit" name=".snew" value="<?=GT($textMap, 'IA.SAVE_AND_NEW');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'deliver': ?>
                                    <input class="nosavehistory" type="submit" name=".deliver" value="<?=GT($textMap, 'IA.DELIVER');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'send': ?>
                                    <input class="nosavehistory" type="submit" name=".send" value="<?=GT($textMap, 'IA.SEND');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'printje': ?>
                                    <input class="nosavehistory" type="submit" name=".printje" value="<?=GT($textMap, 'IA.PRINT_TO');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'buttsave': ?>
                                    <input class="nosavehistory" type="button" name=".save" value="<?=GT($textMap, 'IA.SAVE');?>" <?= $data['jsargs'][$i][1]; ?> disableonsubmit="true">
                                    <?
                                    break;
                                case 'buttsnew': ?>
                                    <input class="nosavehistory" type="button" name=".snew" value="<?=GT($textMap, 'IA.SAVE_AND_NEW');?>" <?= $data['jsargs'][$i][1]; ?>disableonsubmit="true">
                                    <?
                                    break;
                                case 'dup': ?>
                                    <input class="nosavehistory" type="submit" name=".duplicate" value="<?=GT($textMap, 'IA.DUPLICATE');?>" <?= $data['jsargs'][$i][1]; ?>>
                                    <?
                                    break;
                                case 'refresh': ?>
                                    <input class="nosavehistory" type="button" name=".refresh" value="<?=GT($textMap, 'IA.REFRESH');?>" onclick="location.reload()">
                                    <?
                                    break;
                                // If the button is not found, it is a special case, use the default
                                // This way each button doesn't have to be hard coded here
                                // $jsarg[0] = button name  ( always appends a . before the name)
                                // $jsarg[1] = button value ( the text displayed in the html page)
                                // $jsarg[2] = the onclick javascript call
                                default:
                                    ?>
                                    <!-- done -->
                                    <? pp($data['jsargs']); ?>
                                    <? if ($data['jsargs'][$i][0]) { ?>
                                    <input class="nosavehistory" type="submit"
                                           name=".<?= $data['jsargs'][$i][0]; ?>"
                                           value="<?= $data['jsargs'][$i][1]; ?>"
                                           onclick="<?= $data['jsargs'][$i][2]; ?>" <?= $data['jsargs'][$i][3]; ?>>
                                    <? break;
                                }
                            }
                        }
                    }

                    ?>
                </td>
            </tr>
        </table>
        <?
        }
    }
}
