openapi: 3.0.0
info:
  title: admin.provisioning.create-users
  description: Create users API
  version: '1.0-internal'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: admin.provisioning.create-users
    description: Create admin, system and support users. Internal API used by the Provisioning domain service
paths:
  /services/admin/provisioning/create-users:
    post:
      summary: Create users
      description: Create admin, system and support users.
      tags:
        - admin.provisioning.create-users
      operationId: post-services-admin-provisioning-create-users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.provisioning.create-users-request'
            examples:
              example-1:
                value:
                  companyKey: '50000056987'
                  userId: 'admin'
                  userPassword: 'lFQO22lL17FqUko3ijP48g=='
                  userEmail: '<EMAIL>'
                  userFirstName: 'First-name'
                  userLastName: 'Last-name'
                  phone1: '*********'
              example-2:
                value:
                  companyKey: '00000001'
                  userId: 'admin'
                  userPassword: 'lFQO22lL17FqUko3ijP48g=='
                  userEmail: '<EMAIL>'
                  userFirstName: 'First-name'
                  userLastName: 'Last-name'
                  phone1: '*********'
              example-3:
                value:
                  companyKey: '10000698736'
                  userId: 'intacctCPAUserExtUser'
                  userPassword: 'lFQO22lL17FqUko3ijP48g=='
                  userEmail: '<EMAIL>'
                  userFirstName: ''
                  userLastName: ''
                  phone1: '08963121901'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.provisioning.create-users-response'
              examples:
                example-1:
                  value:
                    ia:result:
                      status: true
                      data:
                        message: 'Users were created successfully.'
                    ia:meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
                example-2:
                  value:
                    ia:result:
                      status: false
                      errors:
                        - code: '2'
                          message: "Company doesn't exist. Please choose another 'companyKey'."
                          supportId: 'SupportId-Hash1234'
                    ia:meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
                example-3:
                  value:
                    ia:result:
                      status: false
                      errors:
                        - code: '2'
                          message: "'userId' contains a reserved system string."
                          supportId: 'SupportId-Hash1234'
                        - code: '1'
                          message: "A value for 'userFirstName' is required."
                          supportId: 'SupportId-Hash1234'
                        - code: '1'
                          message: "A value for 'userLastName' is required."
                          supportId: 'SupportId-Hash1234'
                    ia:meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    admin.provisioning.create-users-request:
      description: Create users request
      type: object
      x-mappedTo: __custom__
      properties:
        companyKey:
          type: string
          description: Company cny
          x-mappedTo: companyKey
          example: '5000785691458'
        userId:
          type: string
          description: User id
          x-mappedTo: userId
          example: 'admin'
        userPassword:
          type: string
          description: User password
          x-mappedTo: userPassword
          example: 'lFQO22lL17FqUko3ijP48g=='
        userEmail:
          type: string
          description: User email
          x-mappedTo: userEmail
          example: '<EMAIL>'
        userFirstName:
          type: string
          description: User first name
          x-mappedTo: userFirstName
          example: 'First'
        userLastName:
          type: string
          description: User last name
          x-mappedTo: userLastName
          example: 'Last'
        phone1:
          type: string
          description: User phone1
          x-mappedTo: phone1
          example: '07896145263'
    admin.provisioning.create-users-response:
      description: Create users response
      type: object
      x-mappedTo: __custom__
      properties:
        status:
          type: boolean
          description: The status of the request
          x-mappedTo: status
          default: false
          example: true
        errors:
          type: array
          description: List of errors
          x-mappedTo: errors
          items:
            type: object
            properties:
              code:
                type: string
                description: Error code
                x-mappedTo: code
                example: 'CJ-1005'
              message:
                type: string
                description: Error message
                x-mappedTo: message
                example: 'Cannot create the admin user.'
              supportId:
                type: string
                description: Error support id
                x-mappedTo: supportId
                example: 'DV8796245EFgh08nalGT679nbIlVgR'
        data:
          type: object
          description: Success result payload
          x-mappedTo: data
          properties:
            message:
              type: string
              description: Success message
              x-mappedTo: data.message
              example: 'Users were created successfully.'
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
