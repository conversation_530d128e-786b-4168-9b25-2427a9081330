openapi: 3.0.0
info:
  title: core.system.get-configuration
  description: Configuration read service
  version: '1.0-internal'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: core.system.configuration
    description: Configuration access routines for domain services
paths:
  /services/core/system/configuration:
    get:
      summary: Read configuration
      description: Read configuration
      tags:
        - core.system.configuration
      operationId: get-services-core-system-configuration
      responses:
        '200':
          description: Executed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/core.system.configuration-resp'
              examples:
                example-1:
                  value:
                    ia:result:
                      databases:
                        global:
                          name: dev04
                          proxyHost: dev04
                          user: mega_global
                          password: "KEY_DEV_SECRETS:..."
                        schemas:
                          - name: dev08
                            imsName: dev08ims
                            proxyHost: dev08
                            user: tpls_user
                            password: "KEY_DEV_SECRETS:..."
                            reportUser: false
                            ownerId: TPLS_OWNER_01
                            multiEntityId: TPLS_ME_01
                            schemaIndex: "01"
                            charset: UTF8
                            hasTenants: true
                            hasTemplates: true
                            analyticsUser: tpls_user
                            id: "20"
                            acceptedTenantTypes:
                              template: "1"
                            podId: 0
                          - name: dev02
                            imsName: dev02ims
                            proxyHost: dev02
                            user: sltrunkci_user
                            password: "KEY_DEV_SECRETS:..."
                            reportUser: sltrunkci_user_rpt
                            ownerId: SLTRUNKCI_OWNER_01
                            multiEntityId: SLTRUNKCI_ME_01
                            schemaIndex: "01"
                            charset: UTF8
                            hasTenants: true
                            hasTemplates: false
                            analyticsUser: sltrunkci_user
                            id: "61277"
                            acceptedTenantsType:
                              production: "1"
                              sandbox: "2"
                            podId: 0
                        podGroups:
                          - id: "0"
                            version: "1.0"
                            region: US-West
                            host: dev09.intacct.com
                            externalHost: dev09.intacct.com
                            description: Dev US Main PG
                            pods:
                              - "0"
                          - id: "1"
                            version: null
                            region: US-West
                            host: www-az-i.intacct.com
                            externalHost: www-az.intacct.com
                            description: Dev US AZ PG
                            pods:
                              - "10"
                          - id: "2"
                            version: null
                            region: US-East
                            host: www-ny-i.intacct.com
                            externalHost: www-ny.intacct.com
                            description: Dev US NY PG
                            pods:
                              - "20"
                        pods:
                          - id: "0"
                            version: "1.0"
                            host: dev09.intacct.com
                            externalHost: dev11.intacct.com
                            description: "0"
                            apiEndpoint: api-dev.intacct.com
                            jppEndpoint: dev09.intacct.com
                            apiGWEndpoint: api-kong-az.intacct.com
                            apiAppEndpoint: api-ca-i.intacct.com
                            apiDSEndpoint: dev-us-0.ds.intacct.com
                            acceptCountries: US
                            location: AWS
                          - id: "10"
                            version: "1.0"
                            host: www-az-i.intacct.com
                            externalHost: www-az.intacct.com
                            description: "10"
                            apiEndpoint: api-dev.intacct.com
                            jppEndpoint: api-az-i.intacct.com
                            apiGWEndpoint: api-kong-az.intacct.com
                            apiAppEndpoint: api-az-i.intacct.com
                            apiDSEndpoint: dev-us-10.ds.intacct.com
                            acceptCountries: null
                            location: AZURE
                          - id: "20"
                            version: "1.0"
                            host: www-ny-i.intacct.com
                            externalHost: www-ny.intacct.com
                            description: "20"
                            apiEndpoint: api-dev.intacct.com
                            jppEndpoint: api-ny-i.intacct.com
                            apiGWEndpoint: api-kong-ny.intacct.com
                            apiAppEndpoint: api-ny-i.intacct.com
                            apiDSEndpoint: dev-us-20.ds.intacct.com
                            acceptCountries: Europe
                            location: AZURE,AWS
                        currentPOD: 0
                        currentPODGroup: 0
                        mongoDB:
                          shard:
                            defaultHost: random
                            globalHost: dev_rs1
                            baseDB: devdb1
                          options:
                            connectionTimeout: 2000
                            socketTimeout: 25000
                            username: intacct-rw-devdb-any
                            password: ...
                            encryptedPassword: ...
                            database: admin
                          hosts:
                            dev_rs1:
                              host:
                                - dev71.intacct.com:27001
                                - dev72.intacct.com:27001
                                - dev73.intacct.com:27001
                              acceptNew: true
                              options:
                                connectionTimeout: 2000
                                socketTimeout: 25000
                                username: intacct-rw-devdb-any
                                database: admin
                                replicaSet: dev_rs1
                              connString: mongodb://dev71.intacct.com:27001,dev72.intacct.com:27001,dev73.intacct.com:27001/
                              mongoDBClient: null
                              baseDB: devdb1
                            dev_rs2:
                              host:
                                - dev71.intacct.com:27002
                                - dev72.intacct.com:27002
                                - dev73.intacct.com:27002
                              acceptNew: true
                              options:
                                connectionTimeout: 2000
                                socketTimeout: 25000
                                username: intacct-rw-devdb-any
                                database: admin
                                replicaSet: dev_rs2
                              connString: mongodb://dev71.intacct.com:27002,dev72.intacct.com:27002,dev73.intacct.com:27002/
                              mongoDBClient: null
                              baseDB: devdb1
                            dev_rs3:
                              host:
                                - dev71.intacct.com:27003
                                - dev72.intacct.com:27003
                                - dev73.intacct.com:27003
                              acceptNew: true
                              options:
                                connectionTimeout: 2000
                                socketTimeout: 25000
                                username: intacct-rw-devdb-any
                                database: admin
                                replicaSet: dev_rs3
                              connString: mongodb://dev71.intacct.com:27003,dev72.intacct.com:27003,dev73.intacct.com:27003/
                              mongoDBClient: null
                              baseDB: devdb1
                          acceptHosts:
                            dev_rs1: 1
                            dev_rs2: 1
                            dev_rs3: 1
                        isLive: false
                    ia:meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError": 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    core.system.configuration-req:
      description: Read configuration request
      type: object
      x-mappedTo: __custom__
    core.system.configuration-resp:
      description: Read configuration response
      type: object
      x-mappedTo: __custom__
      properties:
        isLive:
          type: boolean
          x-mappedTo: isLive
          example: false
          default: false
        currentPOD:
          type: integer
          x-mappedTo: currentPOD
          example: 112
        currentPODGroup:
          type: integer
          x-mappedTo: currentPODGroup
          example: 34
        databases:
          type: object
          description: databases information
          properties:
            global:
              type: object
              properties:
                name:
                  type: string
                  x-mappedTo: db.global.server
                  example: dev04
                user:
                  type: string
                  x-mappedTo: db.global.userid
                  example: acct_global
                password:
                  type: string
                  description: IA_INIT encrypted password
                  x-mappedTo: db.global.passwd
                  example: "KEY_DEV_SECRETS:..."
                proxyHost:
                  type: string
                  x-mappedTo: db.global.proxyHost
                  example: dev04
            schemas:
              type: array
              x-mappedTo: db.schemas
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: Database ID
                    x-mappedTo: id
                    example: "12345"
                  name:
                    type: string
                    x-mappedTo: server
                    example: dev08
                  imsName:
                    type: string
                    x-mappedTo: serverIMS
                    example: dev08ims
                  user:
                    type: string
                    x-mappedTo: userid
                    example: main_user
                  password:
                    type: string
                    description: IA_INIT encrypted password
                    x-mappedTo: passwd
                    example: "KEY_DEV_SECRETS:..."
                  reportUser:
                    type: string
                    x-mappedTo: rpt_userid
                    example: main_user_rpt
                  analyticsUser:
                    type: string
                    x-mappedTo: obiee_userid
                    example: main_user_obiee
                  ownerId:
                    type: string
                    x-mappedTo: ownerid
                    example: main_owner_01
                  multiEntityId:
                    type: string
                    x-mappedTo: meglid
                    example: main_me_01
                  schemaIndex:
                    type: string
                    x-mappedTo: schemacnt
                    example: "01"
                  charset:
                    type: string
                    x-mappedTo: charset
                    example: UTF8
                  hasTenants:
                    type: boolean
                    x-mappedTo: companyDB
                    example: true
                    default: false
                  hasTemplates:
                    type: boolean
                    x-mappedTo: templateDB
                    example: false
                    default: false
                  acceptedTenantTypes:
                    type: object
                    x-mappedTo: acceptType
                  session:
                    type: array
                    x-mappedTo: session
                    items:
                      type: string
                    example: [param1, param2]
                  proxyHost:
                    type: string
                    x-mappedTo: proxyHost
                    example: dev08
                  podId:
                    type: string
                    x-mappedTo: podid
                    example: 0
        pods:
          type: array
          description: pods information
          x-mappedTo: pods
          items:
            type: object
            properties:
              id:
                type: string
                x-mappedTo: id
                example: "12"
              version:
                type: string
                x-mappedTo: version
                example: "1.0"
              host:
                type: string
                x-mappedTo: host
                example: pod12.intacct.com
              externalHost:
                type: string
                x-mappedTo: externalhost
                example: www.intacct.com
              description:
                type: string
                x-mappedTo: description
                example: "This is pod 12 in US-West"
              apiEndpoint:
                type: string
                x-mappedTo: apiendpoint
                example: api.intacct.com
              jppEndpoint:
                type: string
                x-mappedTo: jppendpoint
                example: jpp.intacct.com
              apiGWEndpoint:
                type: string
                x-mappedTo: apiGWEndpoint
                example: kong-pod12.intacct.com
              apiAppEndpoint:
                type: string
                x-mappedTo: apiAppEndpoint
                example: api-pod12.intacct.com
              apiDSEndpoint:
                type: string
                x-mappedTo: apiDSEndpoint
                example: pod12.ds.intacct.com
              excludeCountries:
                type: string
                x-mappedTo: excludeCountries
                example: UK
              acceptCountries:
                type: string
                x-mappedTo: acceptCountries
                example: US
              location:
                type: string
                x-mappedTo: location
                example: US-West
        podGroups:
          type: array
          description: podgroups information
          x-mappedTo: podGroups
          items:
            type: object
            properties:
              id:
                type: string
                x-mappedTo: id
                example: "2"
              version:
                type: string
                x-mappedTo: version
                example: "1.0"
              host:
                type: string
                x-mappedTo: host
                example: pod2.intacct.com
              externalHost:
                type: string
                x-mappedTo: externalhost
                example: www.intacct.com
              description:
                type: string
                x-mappedTo: description
                example: This is Pod Group 2
              region:
                type: string
                x-mappedTo: region
                example: US-East
              pods:
                type: array
                x-mappedTo: pods
                items:
                  type: string
                example:
                  - "12"

        mongoDB:
          type: object
          description: MongoDB configuration
          # the details below seem to be ignored by our yaml-2-json converter
          properties:
            hosts:
              type: object
              x-mappedTo: mongoDB.hosts
            shard:
              type: object
              properties:
                defaultHost:
                  type: string
                  x-mappedTo: mongoDB.shard.defaultHost
                  example: dev72
                globalHost:
                  type: string
                  x-mappedTo: mongoDB.shard.globalHost
                  example: dev71
                baseDB:
                  type: string
                  x-mappedTo: mongoDB.shard.baseDB
                  example: devdb1
            options:
              type: object
              properties:
                connectionTimeout:
                  type: integer
                  x-mappedTo: mongoDB.opt.connectTimeoutMS
                  example: 2000
                socketTimeout:
                  type: integer
                  x-mappedTo: mongoDB.opt.socketTimeoutMS
                  example: 3000
                username:
                  type: string
                  x-mappedTo: mongoDB.opt.username
                  example: mogodb_user
                password:
                  type: string
                  x-mappedTo: mongoDB.opt.password
                  example: pwd
                encryptedPassword:
                  type: string
                  x-mappedTo: mongoDB.opt.password-crypt
                  example: crypted pwd
                database:
                  type: string
                  x-mappedTo: mongoDB.opt.db
                  example: devdb1
            acceptHosts:
              type: object
              x-mappedTo: mongoDB.acceptHosts
        entitytoAPIMapping:
          type: object
          description: entity to api mapping
          x-mappedTo: entToApiMap
        permissions:
          type: array
          description: Permissions
          x-mappedTo: permissions
          items:
            type: object
            properties:
              key:
                type: integer
                x-mappedTo: id
                example: 7
              id:
                type: string
                x-mappedTo: key
                example: "co/lists/ajax"
        modules:
          type: array
          description: Intacct modules
          x-mappedTo: modules
          items:
            type: object
            properties:
              id:
                type: string
                x-mappedTo: IAMODULEID
                example: "13.PR"
              applicationName:
                type: string
                x-mappedTo: IAKEY
                example: "My Practice"
              nameToken:
                type: string
                x-mappedTo: NAME
                example: "IA.MY_PRACTICE"
              symbol:
                type: string
                x-mappedTo: SYMBOL
                example: "mp"
              provider:
                type: string
                x-mappedTo: COMPANY
                example: "Intacct"
              isInstallable:
                type: boolean
                x-mappedTo: INSTALLABLE
                example: false
              isPreInstalled:
                type: boolean
                x-mappedTo: PREINSTALLED
                example: true
              isConfigurable:
                type: boolean
                x-mappedTo: CONFIGURABLE
                example: false
              isRemovable:
                type: boolean
                x-mappedTo: REMOVABLE
                example: false
              permissionKey:
                type: integer
                x-mappedTo: PERMKEY
                example: 550
              loginTypeFilter:
                type: string
                x-mappedTo: APP
                example: "CM"

  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml