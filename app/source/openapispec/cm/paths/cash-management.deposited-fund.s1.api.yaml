openapi: 3.0.0
info:
  title: cash-management-deposited-fund
  description: cash-management.deposited-fund API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Deposited funds
    description: Transaction record for deposited fund. Deposited transaction that is deposited in bank account.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/cash-management/deposited-fund:
    get:
      summary: List deposited funds
      description: Returns a collection with a key, ID, and link for each deposited fund.
      tags:
        - Deposited funds
      operationId: list-cash-management-deposited-fund
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of deposited-fund objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of deposited funds:
                  value:
                    'ia::result':
                      - key: '201'
                        id: '201'
                        href: /objects/cash-management/deposited-fund/201
                      - key: '202'
                        id: '202'
                        href: /objects/cash-management/deposited-fund/202
                      - key: '203'
                        id: '203'
                        href: /objects/cash-management/deposited-fund/203
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  /objects/cash-management/deposited-fund/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the deposited fund.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a deposited fund
      description: Returns detailed information for a specified deposited fund.
      tags:
        - Deposited funds
      operationId: get-cash-management-deposited-fund-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the deposited fund
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/cash-management-deposited-fund'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a deposited fund:
                  value:
                    'ia::result':
                      id: '587'
                      key: '587'
                      deposit:
                        id: '291'
                        key: '291'
                        depositType: cd
                        href: /objects/cash-management/deposit/291
                      glAccount:
                        id: '33'
                        key: '33'
                        accountNumber: '1070'
                        name: Undeposited Funds
                        href: /objects/general-ledger/account/33
                      arAccountLabel:
                        label: null
                        id: null
                        key: null
                      amount: '1002.00'
                      txnAmount: '1002.00'
                      department:
                        key: null
                        id: null
                        name: null
                      location:
                        key: '1'
                        name: United States of America
                        href: /objects/company-config/location/1
                      baseLocation:
                        name: United States of America
                        key: null
                      description: Deposited fund from GoPro sale
                      exchangeRate:
                        date: '2022-01-23'
                        typeId: '-1'
                        rate: 1.0
                      currency: USD
                      baseCurrency: USD
                      status: active
                      audit:
                        createdDateTime: '2021-04-16T00:57:25Z'
                        modifiedDateTime: '2021-04-16T00:57:43Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/cash-management/deposit-line/587
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    cash-management-deposited-fund:
      $ref: ../models/cash-management.deposited-fund.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
