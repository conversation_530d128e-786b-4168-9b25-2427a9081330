title: expenses-expense-to-approve-line
x-mappedTo: approveexpensesitem
x-ownedBy: expenses/expense-to-approve
type: object
description: An individual expense line sent for approval on an employee expense report.
properties:
  key:
    type: string
    description: System assigned unique key for the employee expense line item to approval. 
    x-mappedTo: RECORDNO
    readOnly: true
    example: '312'
  id:
    type: string
    description: Unique identifier for the expense to approve line. This value is the same as the key for this object. 
    x-mappedTo: RECORDNO
    readOnly: true
    example: '312'
  entryDate:
    type: string
    format: date
    description: Date the expense line is filed.
    example: '2025-01-23'
    x-mappedTo: ENTRY_DATE
  baseCurrency:
    type: string
    description: Base currency of the expense.
    x-mappedTo: BASECURR
    readOnly: true
    example: USD
  baseAmount:
    type: string
    description: Amount of the expense in base currency.
    x-mappedTo: AMOUNT
    readOnly: true
    format: decimal-precision-2
    example: '123.45'
  reimbursementCurrency:
    type: string
    description: Reimbursement currency.
    x-mappedTo: CURRENCY
    readOnly: true
    example: CAD
  reimbursementAmount:
    type: string
    description: Reimbursement amount.
    x-mappedTo: TRX_AMOUNT
    readOnly: true
    format: decimal-precision-2
    example: '123.45'
  txnCurrency:
    type: string
    description: Transaction currency. For multi-currency companies.
    x-mappedTo: ORG_CURRENCY
    example: INR
  txnAmount:
    type: string
    description: Transaction amount.
    x-mappedTo: ORG_AMOUNT
    format: decimal-precision-2
    example: '123.45'
    nullable: true
  totalSelected:
    type: string
    description: Amount selected to pay in transaction currency.
    x-mappedTo: TRX_TOTALSELECTED
    readOnly: true
    format: decimal-precision-2
    example: '123.45'
  totalPaid:
    type: string
    description: Amount paid in transaction currency.
    x-mappedTo: TRX_TOTALPAID
    readOnly: true
    format: decimal-precision-2
    example: '123.45'
  quantity:
    type: string
    description: Quantity for a rate-based expense, for example mileage.
    x-mappedTo: QUANTITY
    format: decimal-precision-2
    example: '5.75'
  unitRate:
    type: string
    description: Monetary amount for a rate-based expense, such as mileage.
    x-mappedTo: UNITRATE
    format: decimal-precision-2
    example: '20.00'
  paidTo:
    type: string
    description: Notes regarding to whom you paid the amount.
    x-mappedTo: DESCRIPTION
    example: Hotel Westin
  paidFor:
    type: string
    description: Notes regarding what the expense was for.
    x-mappedTo: DESCRIPTION2
    example: Attending conference
  glAccount:
    type: object
    description: General ledger account associated with the line item. Used when no expense type is assigned.
    x-object: general-ledger/account
    x-mappedTo: glaccount
    properties:
      key:
        type: string
        description: Unique key for the GL account.
        x-mappedTo: ACCOUNTKEY
        example: '158'
      id:
        type: string
        description: Unique identifier for the GL account.
        x-mappedTo: ACCOUNTNO
        example: '6775.30'
      name:
        type: string
        description: Name of the GL account.
        x-mappedTo: GLACCOUNTTITLE
        readOnly: true
        example: Travel
      href:
        type: string
        description: Endpoint for the GL account object.
        readOnly: true
        example: /objects/general-ledger/account/23
  expenseType:
    type: object
    description: An expense type defined in the company.
    x-object: expenses/employee-expense-type
    x-mappedTo: acctlabel
    properties:
      key:
        type: string
        description: Unique key for the expense type.
        x-mappedTo: ACCOUNTLABELKEY
        example: '6000'
      id:
        type: string
        description: Unique identifier for the expense type.
        x-mappedTo: ACCOUNTLABEL
        example: Meals
      href:
        type: string
        description: Endpoint for the expense type object.
        readOnly: true
        example: /objects/expense-type/34
  lineNumber:
    type: number
    description: Line number of the employee expense.
    x-mappedTo: LINE_NO
    readOnly: true
    example: 1
  reimburseToBaseConversion:
    type: object
    description: Details of reimbursement currency to base currency conversion.
    properties:
      exchangeRateDate:
        type: string
        format: date
        description: Date the exchange rate conversion was applied.
        example: '2021-01-23'
        x-mappedTo: EXCH_RATE_DATE
        readOnly: true
      exchangeRateTypeId:
        type: string
        description: Type of the expense line exchange rate.
        example: '-1'
        x-mappedTo: EXCH_RATE_TYPE_ID
        readOnly: true
      exchangeRate:
        type: string
        description: Value of the expense line exchange rate.
        x-mappedTo: EXCHANGE_RATE
        readOnly: true
        format: decimal-precision-5
        example: '1.18999'
  transactionToReimburseConversion:
    type: object
    description: Details of the conversion to base currency.
    properties:
      exchangeRate:
        type: string
        description: Exchange rate for the transaction.
        x-mappedTo: ORG_EXCHRATE
        format: decimal-precision-5
        example: '65'
      exchangeRateDate:
        type: string
        format: date
        example: '2025-01-23'
        description: Date for the exchange rate.
        x-mappedTo: ORG_EXCHRATEDATE
      exchangeRateTypeId:
        type: string
        description: Exchange rate type.
        x-mappedTo: ORG_EXCHRATETYPE
        example: '-1'
  state:
    type: string
    description: Status of the employee expense report line.
    enum:
      - draft
      - submitted
      - partiallyApproved
      - partiallyDeclined
      - approved
      - posted
      - declined
      - reversalPending
      - reversed
      - reversal
      - paid
      - confirmed
      - voided
      - partiallyPaid
      - saved
      - notApplicable
      - pending
      - readyForApproval
      - null
    x-mappedToValues:
      - Draft
      - Submitted
      - Partially Approved
      - Partially Declined
      - Approved
      - Posted
      - Declined
      - Reversal Pending
      - Reversed
      - Reversal
      - Paid
      - Confirmed
      - Voided
      - Partially Paid
      - Saved
      - Not Applicable
      - Pending
      - Ready For Approval
      - ''
    default: submitted
    nullable: true
    x-mappedTo: STATE
    readOnly: true
    example: draft
  isBillable:
    type: boolean
    default: false
    description: Indicates whether a line item is billable.
    x-mappedTo: BILLABLE
    x-mappedToType: string
    example: 'false'
  isBilled:
    type: boolean
    default: false
    description: Indicates whether a line item was billed.
    x-mappedTo: BILLED
    x-mappedToType: string
    readOnly: true
    example: 'false'
  form1099:
    type: object
    description: Form 1099 information for the line item.
    properties:
      isForm1099:
        type: string
        description: If set to true, the line item amount is added to the form 1099.
        x-mappedTo: FORM1099
        example: 'true'
      type:
        type: string
        description: Type of form 1099.
        x-mappedTo: FORM1099TYPE
        example: MISC
      box:
        type: string
        description: Box value of form 1099.
        x-mappedTo: FORM1099BOX
        example: '3'
  paymentType:
    type: object
    x-object: expenses/employee-expense-payment-type
    x-mappedTo: expensepaymenttype
    properties:
      key:
        type: string
        description: Unique key for the expense payment type.
        x-mappedTo: EXPPMTTYPEKEY
        example: '1'
      id:
        type: string
        description: Unique identifier for the payment type.
        x-mappedTo: EXPPMTTYPEKEY
        example: '1'
      name:
        type: string
        description: Name of the expense payment type.
        x-mappedTo: EXPPMTTYPE
        example: Non-reimburse
      isNonReimbursable:
        type: boolean
        default: false
        description: Non-reimbursable expense.
        x-mappedTo: NONREIMBURSABLE
        example: 'false'
      href:
        type: string
        readOnly: true
        example: /objects/projects/employee-expense-payment-type/1
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  dimensions:
    type: object
    allOf:
      - $ref: ../../common/references/dimension-ref.s1.schema.yaml
      - type: object
        properties:
          location:
            type: object
            x-object: company-config/location
            properties:
              key:
                x-mappedTo: LOCATION#
                type: string
                description: Unique key for the location.
                example: '1'
                nullable: true
              id:
                x-mappedTo: LOCATIONID
                type: string
                description: Unique identifier for the location.
                example: '1'
                nullable: true
              name:
                x-mappedTo: LOCATIONNAME
                readOnly: true
                type: string
                description: Location name
                example: USA
                nullable: true
              href:
                type: string
                readOnly: true
                example: /objects/company-config/location/1
          department:
            type: object
            x-object: company-config/department
            properties:
              key:
                type: string
                description: Unique key for the department.
                x-mappedTo: DEPT#
                example: '1'
                nullable: true
              id:
                type: string
                description: Unique identifier for the department.
                x-mappedTo: DEPARTMENTID
                example: '1'
                nullable: true
              name:
                type: string
                description: Department name
                x-mappedTo: DEPARTMENTNAME
                readOnly: true
                example: IT
                nullable: true
              href:
                type: string
                readOnly: true
                example: /objects/company-config/department/1
  expenseToApprove:
    type: object
    x-mappedTo: eexpenses
    x-object: expenses/expense-to-approve
    properties:
      id:
        type: string
        description: Unique identifier for the expense to approve.
        x-mappedTo: RECORDKEY
        readOnly: true
        example: '1'
      key:
        type: string
        description: Unique key for the expense to approve.
        x-mappedTo: RECORDKEY
        readOnly: true
        example: '1'
    readOnly: true
