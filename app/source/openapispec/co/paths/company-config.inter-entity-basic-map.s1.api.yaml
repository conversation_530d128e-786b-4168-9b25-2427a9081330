openapi: 3.0.0
info:
  title: company-config-inter-entity-basic-map
  description: Inter entity basic map API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Inter entity basic map
    description: 'With Inter entity basic map, you provide one default inter-entity receivable (IER) account and one default inter-entity payable (IEP) account for each entity in a single Inter entity basic map object.'
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/company-config/inter-entity-basic-map:
    get:
      summary: List Inter entity basic map
      description: 'Returns a collection with a key, ID, and link for each Inter entity basic map.'
      operationId: list-company-config-inter-entity-basic-map
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of inter-entity-basic-map objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of Inter entity basic map:
                  value:
                    'ia::result':
                      - key: '1'
                        id: '1'
                        href: /objects/company-config/inter-entity-basic-map/1
                      - key: '2'
                        id: '2'
                        href: /objects/company-config/inter-entity-basic-map/2
                      - key: '3'
                        id: '3'
                        href: /objects/company-config/inter-entity-basic-map/3
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      tags:
        - Inter entity basic map
  /objects/company-config/inter-entity-basic-map/{key}:
    parameters:
      - name: key
        description: system-assigned unique key for the Inter entity basic map.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a/an Inter entity basic map
      description: Returns detailed information for a particular Inter entity basic map.
      tags:
        - Inter entity basic map
      operationId: get-company-config-inter-entity-basic-map-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the inter-entity-basic-map
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/company-config-inter-entity-basic-map'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the Inter entity basic map:
                  value:
                    'ia::result':
                      interEntityAccountMapping:
                        id: '1'
                        key: '1'
                        href: /objects/company-config/inter-entity-account-mapping/1
                      key: '1'
                      id: '1'
                      entity:
                        id: '1'
                        key: '1'
                        name: United States of America
                        status: active
                        href: /objects/company-config/entity/1
                      interEntityPayableGLAccount:
                        key: '10'
                        id: '1001'
                        name: CitiBank
                        href: /objects/general-ledger/account/10
                      interEntityReceivableGLAccount:
                        key: '9'
                        id: '1000'
                        name: Bank of America A/c.
                        href: /objects/general-ledger/account/9
                      href: /objects/company-config/inter-entity-basic-map/1
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a/an Inter entity basic map
      description: Deletes a/an Inter entity basic map.
      tags:
        - Inter entity basic map
      operationId: delete-company-config-inter-entity-basic-map-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    company-config-inter-entity-basic-map:
      $ref: ../models/company-config.inter-entity-basic-map.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
