openapi: 3.0.0
info:
  title: company.dimensions
  description: company dimension API
  version: '1.0'
  contact:
    name: <PERSON>
    email: <EMAIL>
tags:
  - name: Dimensions
    description: Dimensions are a classification system used to organize, sort, and report on company information in meaningful ways. You can think of each dimension as a category with a set of values that you can apply to transactions and entries. Every transaction can be tagged with multiple dimension values to identify and report on it. For example, you can tag transactions with pre-defined locations, and then generate reports to show sales by each location.
servers:
  - url: 'http://localhost:3000'
paths:
  /services/company-config/dimensions/list:
    get:
      summary: List dimensions
      description: Lists all standard and user-defined dimensions in a company along with integration information about each one.
      tags:
        - Dimensions
      operationId: get-company-config-dimensions-list
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: '#/components/schemas/company-config-dimensions-list-response'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Response:
                  value:
                    'ia::result':
                      - dimensionName: 'DEPARTMENT'
                        dimensionLabel: 'Department'
                        termName: 'Department'
                        isUserDefinedDimension: false
                        isEnabledInGL: true
                        dimensionEndpoint: '/objects/company-config/department'
                      - dimensionName: 'LOCATION'
                        dimensionLabel: 'Location'
                        termName: 'Loc'
                        isUserDefinedDimension: false
                        isEnabledInGL: true
                        dimensionEndpoint: '/objects/company-config/location'
                      - dimensionName: 'PROJECT'
                        dimensionLabel: 'Project'
                        termName: 'Project'
                        isUserDefinedDimension: false
                        isEnabledInGL: true
                        dimensionEndpoint: '/objects/projects/project'
                      - dimensionName: 'CUSTOMER'
                        dimensionLabel: 'Customer'
                        termName: 'Customer'
                        isUserDefinedDimension: false
                        isEnabledInGL: true
                        dimensionEndpoint: '/objects/accounts-receivable/customer'
                      - dimensionName: 'VENDOR'
                        dimensionLabel: 'Vendor'
                        termName: 'Vendor'
                        isUserDefinedDimension: false
                        isEnabledInGL: true
                        dimensionEndpoint: '/objects/accounts-payable/vendor'
                      - dimensionName: 'EMPLOYEE'
                        dimensionLabel: 'Employee'
                        termName: 'Employee'
                        isUserDefinedDimension: false
                        isEnabledInGL: true
                        dimensionEndpoint: '/objects/company-config/employee'
                      - dimensionName: 'ITEM'
                        dimensionLabel: 'Item'
                        termName: 'Item'
                        isUserDefinedDimension: false
                        isEnabledInGL: true
                        dimensionEndpoint: '/objects/inventory-control/item'
                      - dimensionName: 'CLASS'
                        dimensionLabel: 'Class'
                        termName: 'Class'
                        isUserDefinedDimension: false
                        isEnabledInGL: true
                        dimensionEndpoint: '/objects/company-config/class'
                      - dimensionName: 'CONTRACT'
                        dimensionLabel: 'Contract'
                        termName: 'Contract'
                        isUserDefinedDimension: false
                        isEnabledInGL: false
                        dimensionEndpoint: '/objects/contracts/contract'
                      - dimensionName: 'TASK'
                        dimensionLabel: 'Task'
                        termName: 'Task'
                        isUserDefinedDimension: false
                        isEnabledInGL: false
                        dimensionEndpoint: '/objects/projects/task'
                      - dimensionName: 'WAREHOUSE'
                        dimensionLabel: 'Warehouse'
                        termName: 'Warehouse'
                        isUserDefinedDimension: false
                        isEnabledInGL: false
                        dimensionEndpoint: '/objects/inventory-control/warehouse'
                      - dimensionName: 'COSTTYPE'
                        dimensionLabel: 'Cost type'
                        termName: 'Cost type'
                        isUserDefinedDimension: false
                        isEnabledInGL: false
                        dimensionEndpoint: '/objects/construction/cost-type'
                      - dimensionName: 'AFFILIATEENTITY'
                        dimensionLabel: 'Affiliate entity'
                        termName: 'Affiliate entity'
                        isUserDefinedDimension: false
                        isEnabledInGL: false
                        dimensionEndpoint: '/objects/company-config/affiliate-entity'
                      - dimensionName: 'FIXEDASSET'
                        dimensionLabel: 'Asset'
                        termName: 'Asset'
                        isUserDefinedDimension: false
                        isEnabledInGL: false
                        dimensionEndpoint: '/objects/fixed-assets/asset'
                      - dimensionName: 'CHANNEL'
                        dimensionLabel: 'Channel'
                        termName: 'Channel'
                        isUserDefinedDimension: true
                        isEnabledInGL: true
                        dimensionEndpoint: '/objects/platform-apps/nsp::channel'
                      - dimensionName: 'DACIA'
                        dimensionLabel: 'dacia'
                        termName: 'dacia'
                        isUserDefinedDimension: true
                        isEnabledInGL: true
                        dimensionEndpoint: '/objects/platform-apps/nsp::dacia'
                    ia::meta:
                      totalCount: '15'
                      totalSuccess: '15'
                      totalError: '0'
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    company-config-dimensions-list-response:
      description: Dimension information
      type: object
      x-mappedTo: __custom__
      properties:
        dimensionName:
          type: string
          description: Dimension name used for integrations.
          x-mappedTo: DIMENSIONNAME
          example: 'DEPARTMENT'
        dimensionLabel:
          type: string
          description: Default dimension label as it appears in the UI.
          x-mappedTo: DIMENSIONLABEL
          example: 'Department'
        termName:
          type: string
          description: Dimension label as defined in company terminology.
          x-mappedTo: TERMNAME
          example: 'Department'
        isUserDefinedDimension:
          type: boolean
          description: This is `true` for user-defined dimensions.
          x-mappedTo: ISUSERDEFINEDDIMENSION
          example: true
        isEnabledInGL:
          type: boolean
          description: This is `true` if the dimension is enabled in the General Ledger module for this company.
          x-mappedTo: ISENABLEDINGL
          example: true
        dimensionEndpoint:
          type: string
          description: URL endpoint of the dimension.
          x-mappedTo: DIMENSIONENDPOINT
          example: '/objects/company-config/department'
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml