openapi: 3.0.0
info:
  title: company-config-company-message
  description: company-config.company-message API
  version: '1.0'
  contact:
    name: Avadhut Yadav
    email: <EMAIL>
tags:
  - name: Company messages
    description: Use company messages to share short announcement or reminders with users. For example, remind employees to submit weekly timesheets or announce an upcoming company holiday.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/company-config/company-message:
    get:
      summary: List Company messages
      description: Returns a collection with a key, ID, and link for each Company Message.
      tags:
        - Company messages
      operationId: list-company-config-company-message
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of company message objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of Company messages:
                  value:
                    'ia::result':
                      - key: '42'
                        id: '42'
                        href: /objects/company-config/company-message/42
                      - key: '43'
                        id: '43'
                        href: /objects/company-config/company-message/43
                    'ia::meta':
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a Company Message
      description: Creates a new Company Message.
      tags:
        - Company messages
      operationId: create-company-config-company-message
      requestBody:
        description: Company Message to create
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/company-config-company-message'
                - $ref: '#/components/schemas/company-config-company-messageRequiredProperties'
            examples:
              Creates a Company Message:
                value:
                  subject: New update announcement
                  expirationDate: '2023-09-19'
                  viewLevel: allUsers
                  priority: high
                  message: We have added new features to our product.
                  status: active
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New company message
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                New Company Message:
                  value:
                    'ia::result':
                      key: '42'
                      id: '42'
                      href: /objects/company-config/company-message/42
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  /objects/company-config/company-message/{key}:
    parameters:
      - name: key
        description: system-assigned unique key for the Company Message.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a Company Message
      description: Returns detailed information for a particular Company Message.
      tags:
        - Company messages
      operationId: get-company-config-company-message-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the company message
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/company-config-company-message'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the Company Message:
                  value:
                    'ia::result':
                      key: '42'
                      id: '42'
                      subject: New update announcement
                      postedBy:
                        id: Admin
                        key: '1'
                        href: /objects/company-config/user/1
                      createdDate: '2023-08-30'
                      lastUpdatedDate: '2023-09-01'
                      expirationDate: '2024-09-19'
                      viewLevel: allUsers
                      message: We have added new features to our product.
                      priority: high
                      status: active
                      href: /objects/company-config/company-message/42
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a Company Message
      description: Updates an existing Company Message by setting field values. Any fields not provided remain unchanged.
      tags:
        - Company messages
      operationId: update-company-config-company-message-key
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/company-config-company-message'
            examples:
              Updates a Company Message:
                value:
                  subject: New update announcement
                  expirationDate: '2023-09-19'
                  viewLevel: allUsers
                  priority: high
                  message: We have added some new cool features to our product.
                  status: active
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated company message
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Updated Company Message:
                  value:
                    'ia::result':
                      key: '27'
                      id: '27'
                      href: /objects/company-config/company-message/27
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a Company Message
      description: Delete a Company Message.
      tags:
        - Company messages
      operationId: delete-company-config-company-message-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    company-config-company-message:
      $ref: ../models/company-config.company-message.s1.schema.yaml
    company-config-company-messageRequiredProperties:
      type: object
      required:
        - subject
        - message
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
