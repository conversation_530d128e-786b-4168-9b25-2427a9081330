openapi: 3.0.0
info:
  title: company-config-department
  description: company-config.department API
  version: '1.0'
  contact:
    email: <EMAIL>
    name: <PERSON>
tags:
  - name: Departments
    description: |-
      Department is an optional [dimension](https://www.intacct.com/ia/docs/en_US/help_action/Reporting/Setup/Dimensions/dimensions-overview-ns.htm) that can be used for tagging, filtering, and categorizing data in many areas of Sage Intacct.

      The list of departments is shared by all entities in a company. Users can be restricted to only work with and see specific departments, which might affect what data they can see in reports, transactions, and other data records.

      You can create a hierarchy of departments to reflect how departments are organized within a company. To set up a hierarchy, create the parent departments first and then specify a parent department when adding child departments.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/company-config/department:
    get:
      summary: List departments
      description: |
        Returns up to 100 department references from the collection with a key, ID, and link for each department. This operation is mostly for use in testing; use the query service to find departments that meet certain criteria and to specify the properties that are returned.
      x-documentationFlags:
        subscription: Company
        userPermissions:
          - userType: Business, Employee, Payment Approver
            permissions: List, View Departments
      tags:
        - Departments
      operationId: list-company-config-department
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of departments
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of departments:
                  value:
                    'ia::result':
                      - key: '11'
                        id: Eng
                        href: /objects/company-config/department/1
                      - key: '2'
                        id: Fin
                        href: /objects/company-config/department/2
                      - key: '3'
                        id: HR
                        href: /objects/company-config/department/3
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '201':
          description: Created
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a department
      description: Creates a new department.
      x-documentationFlags:
        subscription: Company
        userPermissions:
          - userType: Business user with admin privileges
            permissions: Add Department
      tags:
        - Departments
      operationId: create-company-config-department
      requestBody:
        description: Department to create
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/company-config-department'
                - $ref: '#/components/schemas/company-config-departmentRequiredProperties'
            examples:
              Create a department:
                value:
                  id: ENG
                  name: Engineering
                  reportTitle: Engineering
                  status: active
                  supervisor:
                    key: '16'
              Create a child department:
                value:
                  id: SW
                  name: Software
                  reportTitle: Software Development
                  status: active
                  parent:
                    key: '28'
                  supervisor:
                    key: '5'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New department
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                New department created:
                  value:
                    'ia::result':
                      key: '12'
                      id: ENG
                      href: /objects/company-config/department/12
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
  '/objects/company-config/department/{key}':
    parameters:
      - name: key
        description: System-assigned key for the department.
        in: path
        required: true
        example: '28'
        schema:
          type: string
    get:
      summary: Get a department
      description: |-
        Returns detailed information for a specified department.
      x-documentationFlags:
        subscription: Company
        userPermissions:
          - userType: Business, Employee, Payment Approver
            permissions: List, View Departments
      tags:
        - Departments
      operationId: get-company-config-department-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the department
                properties:
                  'ia::result':
                    $ref: ../models/company-config.department.s1.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Department details:
                  value:
                    'ia::result':
                      id: PS
                      key: '28'
                      name: Professional Services
                      parent:
                        key: '7'
                        id: CS--Client Services
                        name: Client Services
                        href: /objects/company-config/department/7
                      supervisor:
                        key: '5'
                        id: MGR1--PS Dept - Manager
                        name: PS Dept - Manager
                        href: /objects/company-config/employee/5
                      audit:
                        createdDateTime: '2023-01-08T11:28:12Z'
                        modifiedDateTime: '2023-01-08T11:28:12Z'
                        createdBy: '1'
                        modifiedBy: '95'
                      status: active
                      reportTitle: Professional Services
                      href: /objects/company-config/department/28
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a department
      description: |-
        Updates an existing department by setting field values. Any fields not provided remain unchanged.
      x-documentationFlags:
        subscription: Company
        userPermissions:
          - userType: Business user with admin privileges
            permissions: Edit Departments
      tags:
        - Departments
      operationId: update-company-config-department-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/company-config-department'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Change department supervisor:
                value:
                  supervisor:
                    key: '99'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated department
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Department updated:
                  value:
                    'ia::result':
                      key: '1'
                      id: SW
                      href: /objects/company-config/department/1
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a department
      description: |-
        Deletes a department. You can delete a department if it is not being used or referenced. Deleted departments cannot be recovered.
      x-documentationFlags:
        subscription: Company
        userPermissions:
          - userType: Business user with admin privileges
            permissions: Delete Departments
      tags:
        - Departments
      operationId: delete-company-config-department-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    company-config-department:
      $ref: ../models/company-config.department.s1.schema.yaml
    company-config-departmentRequiredProperties:
      type: object
      required:
        - id
        - name
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
