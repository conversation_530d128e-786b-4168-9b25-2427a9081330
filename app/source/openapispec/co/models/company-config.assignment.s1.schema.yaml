title: company-config-assignment
x-mappedTo: workingassignment
type: object
description: Used to store assignments data. Assignments are tasks assigned to individuals and can be managed within or without a checklist.
properties:
  key:
    type: string
    description: System-assigned key for the assignment.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Assignment ID (Same as the key)
    readOnly: true
    x-mutable: false
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: Endpoint for the assignment.
    readOnly: true
    example: /objects/company-config/assignment/23
  assignmentId:
    type: string
    description: Assignment ID.
    readOnly: true
    x-mappedTo: ASSIGNMENTID
    example: AP06
  name:
    type: string
    description: Assignment name.
    x-mappedTo: NAME
    example: Get The Sales Count
  lineNumber:
    type: integer
    description: line number associated with the checklist.
    readOnly: true
    x-mappedTo: LINE_NO
    example: 1
  comment:
    type: string
    description: Comment.
    x-mappedTo: COMMENT
    example: Get the correct count for 2023
  description:
    type: string
    description: Assignment description.
    x-mappedTo: DESCRIPTION
    example: Enter the sales count with the Financial value
  internalControlReference:
    type: string
    description: Internal control reference.
    x-mappedTo: INTERNALCONTROLREF
    example: HIPPA
  percentComplete:
    type: string
    description: Percent completed.
    x-mappedTo: PERCENTCOMPLETE
    format: decimal-precision-2
    example: '2.00'
  plannedStartDate:
    type: string
    description: Start date.
    x-mappedTo: PLANNEDSTARTDATE
    format: date
    example: '2023-01-10'
  dueDate:
    type: string
    description: Due date.
    x-mappedTo: PLANNEDENDDATE
    format: date
    example: '2023-01-23'
  actualEndDate:
    type: string
    description: Actual end date.
    x-mappedTo: ACTUALENDDATE
    format: date
    example: '2023-02-23'
  assignmentCategory:
    type: object
    x-mappedTo: wassignmentcategory
    x-object: company-config/assignment-category
    properties:
      key:
        type: string
        description: System-assigned key for the assignment category.
        x-mappedTo: CATEGORYKEY
        example: '5'
      id:
        type: string
        description: Location ID.
        x-mappedTo: CATEGORYKEY
        example: '5'
      name:
        type: string
        description: Assignment category.
        readOnly: true
        x-mappedTo: CATEGORYNAME
        example: 'Stores'
      href:
        type: string
        description: Endpoint for the Assignment category.
        readOnly: true
        example: /objects/company-config/assignment-category/5
  assignmentStatus:
    type: object
    x-mappedTo: wassignmentstatus
    x-object: company-config/assignment-status
    properties:
      key:
        type: string
        description: System-assigned key for the assignment status.
        x-mappedTo: STATUSKEY
        example: '3'
      id:
        type: string
        description: Assignment status ID.
        x-mappedTo: STATUSKEY
        example: '3'
      name:
        type: string
        description: Assignment status.
        x-mappedTo: STATUSNAME
        readOnly: true
        example: ACCOUNT 1
      href:
        type: string
        description: Endpoint for the Assignment status.
        readOnly: true
        example: /objects/company-config/assignment-status/3
  assignee:
    type: object
    x-mappedTo: contact
    x-object: company-config/contact
    properties:
      key:
        type: string
        description: Contact Key.
        x-mappedTo: ASSIGNEEKEY
        example: '451'
      id:
        type: string
        description: Contact ID.
        x-mappedTo: ASSIGNEE
        example: Abhi
      href:
        type: string
        description: Endpoint for the contact.
        readOnly: true
        example: /objects/company-config/contact/451
  attachment:
    type: object
    x-mappedTo: supportingdocuments
    x-object: company-config/attachment
    properties:
      key:
        type: string
        description: Attachment Key.
        x-mappedTo: SUPDOCKEY
        example: '12'
      id:
        type: string
        description: Attachment ID.
        x-mappedTo: SUPDOCID
        example: '1'
      name:
        type: string
        description: Attachment Name.
        readOnly: true
        x-mappedTo: SUPDOCNAME
        example: '1'
      href:
        type: string
        description: Endpoint for the attachment.
        readOnly: true
        example: /objects/company-config/attachment/12
  checklist:
    type: object
    x-mappedTo: workingchecklist
    x-object: company-config/checklist
    properties:
      key:
        type: string
        description: Associated Checklist Key.
        x-mappedTo: CHECKLISTKEY
        example: '12'
      id:
        type: string
        description: Associated Checklist ID.
        x-mappedTo: CHECKLISTKEY
        example: '12'
      name:
        type: string
        description: Associated checklist.
        readOnly: true
        x-mappedTo: CHECKLISTNAME
        example: office files
      href:
        type: string
        description: Endpoint for the workingchecklist.
        readOnly: true
        example: /objects/company-config/checklist/12
  constraints:
    type: array
    x-mappedTo: CONSTRAINTS
    x-object: company-config/assignment-constraint
    items:
      $ref: company-config.assignment-constraint.s1.schema.yaml
  dependents:
    type: array
    readOnly: true
    x-mappedTo: DEPENDANTS
    x-object: company-config/assignment-constraint
    items:
      $ref: company-config.assignment-constraint.s1.schema.yaml
  entity:
    $ref: ../../common/references/entity-ref.s1.schema.yaml
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml