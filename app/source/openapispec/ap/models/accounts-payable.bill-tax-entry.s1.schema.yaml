title: accounts-payable-bill-tax-entry
x-mappedTo: apbilltaxentry
x-ownedBy: accounts-payable/bill-line
type: object
description: For VAT enabled transactions, bill line items will have tax entries.
allOf:
  - $ref: ../../common/models/tax-entries.s1.schema.yaml
  - type: object
    properties:
      purchasingTaxDetail:
        type: object
        description: Purchasing Entry tax details describe a specific type of tax that applies to lines in Accounts Payable transactions.
        x-object: tax/purchasing-tax-detail
        properties:
          key:
            type: string
            description: System-assigned key for the tax detail.
            x-mappedTo: DETAILKEY
            example: '1'
          id:
            type: string
            description: Unique ID for the tax detail.
            x-mappedTo: DETAILID
            example: Alaska Tax Detail
          href:
            type: string
            description: URL endpoint for the tax detail object.
            readOnly: true
            example: /objects/tax/purchasing-tax-detail/1
      billLine:
        title: bill-line
        description: Line item that the tax entries are associated with.
        readOnly: true
        type: object
        x-mappedTo: apbillitem
        x-object: accounts-payable/bill-line
        properties:
          id:
            type: string
            description: Unique ID for the bill line object.
            example: '100'
            readOnly: true
            x-mappedTo: PARENTENTRY
          key:
            type: string
            description: Unique key for the bill line object.
            example: '100'
            readOnly: true
            x-mappedTo: PARENTENTRY
          href:
            type: string
            description: URL endpoint for the bill line object.
            readOnly: true
            example: /objects/accounts-payable/bill-line/100
      isPartialExemption:
        type: boolean
        description: |
          If `true`, this entry is eligible for partial exemption, which separates the recoverable and non-recoverable portions of the input value added tax (VAT).
        x-mappedTo: PARTIALEXEMPT
        x-mappedToType: string
        example: true
        default: false