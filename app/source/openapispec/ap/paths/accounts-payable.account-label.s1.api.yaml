openapi: 3.0.0
info:
  title: accounts-payable-account-label
  description: accounts-payable.account-label API
  version: '1.0'
  contact:
    email: <EMAIL>
    name: Manikanta Thota
tags:
  - name: Account labels
    description: Account labels give accounts more descriptive names that are displayed anywhere the accounts are referenced in the product and in reports. Account labels is an optional feature that must be enabled when configuring Accounts Payable.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/accounts-payable/account-label:
    get:
      summary: List account labels
      description: |
        Returns up to 100 object references from the collection with a key, ID, and link for each label. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business
            permissions: List account labels
      tags:
        - Account labels
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account-label objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List account labels:
                  value:
                    'ia::result':
                      - key: "32"
                        id: "Ref Matls"
                        href: "/objects/accounts-payable/account-label/32"
                      - key: "33"
                        id: "Rent"
                        href: "/objects/accounts-payable/account-label/33"
                      - key: "34"
                        id: "Repairs and Maintenance"
                        href: "/objects/accounts-payable/account-label/34"
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      operationId: list-accounts-payable-account-label
    post:
      summary: Create an account label
      description: Creates a new account label.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business
            permissions: Add account labels
      tags:
        - Account labels
      operationId: create-accounts-payable-account-label
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New account-label
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Create an account label:
                  value:
                    'ia::result':
                      key: '37'
                      id: Sales
                      href: /objects/accounts-payable/account-label/37
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-payable-account-label'
                - $ref: '#/components/schemas/accounts-payable-account-labelRequiredProperties'
            examples:
              Create an account label:
                value:
                  id: Sales
                  description: Sales team expenses
                  status: active
                  glAccount:
                    id: 4000--Sales
                  offsetGLAccount:
                    id: 4001--Miscellaneous - Sales
  /objects/accounts-payable/account-label/{key}:
    parameters:
      - schema:
          type: string
        name: key
        in: path
        required: true
        description: System-assigned key for the account label.
    get:
      summary: Get an account label
      description: Returns detailed information for a specified account label.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business
            permissions: List, Edit account labels
      operationId: get-accounts-payable-account-label-key
      tags:
        - Account labels
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account-label
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/accounts-payable-account-label'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get an account label:
                  value:
                    'ia::result':
                      key: '8'
                      id: Accounting Fees
                      description: Accounting Fees
                      glAccount:
                        id: 6600.01--Accounting Fees
                        key: '318'
                        href: /objects/general-ledger/account/318
                      status: active
                      offsetGLAccount:
                        id: null
                        key: null
                      form1099Type: DIV
                      form1099Box: 1B
                      href: /objects/accounts-payable/account-label/8
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update an account label
      operationId: update-accounts-payable-account-label-key
      description: Updates an existing account label by setting field values. Any fields not provided remain unchanged.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business
            permissions: List, Edit account labels
      tags:
        - Account labels
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated account-label
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Update an account label:
                  value:
                    'ia::result':
                      key: '37'
                      id: Sales
                      href: /objects/accounts-payable/account-label/37
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-payable-account-label'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update an account label:
                value:
                  description: This is Sales Account label
                  status: active
                  offsetGLAccount:
                    id: 4000.03--Sales-Others
    delete:
      summary: Delete an account label
      description: Deletes an account label.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business
            permissions: List, Delete account labels
      tags:
        - Account labels
      operationId: delete-accounts-payable-account-label-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    accounts-payable-account-label:
      $ref: ../models/accounts-payable.account-label.s1.schema.yaml
    accounts-payable-account-labelRequiredProperties:
      type: object
      required:
        - id
        - description
        - status
      properties:
        glAccount:
          type: object
          required:
            - id
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml