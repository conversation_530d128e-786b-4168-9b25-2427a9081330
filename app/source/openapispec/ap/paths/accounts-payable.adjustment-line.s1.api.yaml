openapi: 3.0.0
info:
  title: accounts-payable-adjustment-line
  description: accounts-payable.adjustment-line API
  version: '1.0'
  contact:
    email: <EMAIL>
    name: <PERSON><PERSON><PERSON> V Hegde
tags:
  - name: Adjustment lines
    description: Line items in an adjustment represent transactions captured in that adjustment.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/accounts-payable/adjustment-line:
    get:
      summary: List adjustment lines
      description: Returns a collection with a key, ID, and link for each adjustment line. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View Adjustments
      tags:
        - Adjustment lines
      operationId: list-accounts-payable-adjustment-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of adjustment-line objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List adjustment lines:
                  value:
                    'ia::result':
                      - key: "5336"
                        id: "5336"
                        href: /objects/accounts-payable/adjustment-line/5336
                      - key: "5348"
                        id: "5348"
                        href: /objects/accounts-payable/adjustment-line/5348
                      - key: "5296"
                        id: "5296"
                        href: /objects/accounts-payable/adjustment-line/5296
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create an adjustment line
      description: Creates a new adjustment line.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business
            permissions: List, View, Add Adjustment lines
      tags:
        - Adjustment lines
      operationId: create-accounts-payable-adjustment-line
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-payable-adjustment-line'
                - $ref: '#/components/schemas/accounts-payable-adjustment-lineRequiredProperties'
            examples:
              Create an adjustment line:
                value:
                  apAdjustment: {
                    key: '89'
                  }
                  accountLabel: {
                    id: 'Bank Fees'
                  }
                  txnAmount: '-1800.00'
                  totalTxnAmount: '-1800.00'
                  dimensions:
                    location:
                      id: '1'
                    department:
                      id: '4'
                  hasForm1099: 'true'
                  form1099:
                    type: 'NEC'
                    box: '7'
                  memo: 'Created memo for adjustment line charges'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New adjustment line
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new adjustment line:
                  value:
                    'ia::result':
                      key: '5'
                      id: '5'
                      href: /objects/accounts-payable/adjustment-line/5
                      'ia::meta':
                        totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
  /objects/accounts-payable/adjustment-line/{key}:
    parameters:
      - schema:
          type: string
        name: key
        example: '5296'
        in: path
        required: true
        description: System-assigned key for the adjustment line.
    get:
      summary: Get an adjustment line
      description: Returns detailed information for a specified adjustment line.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View Adjustments
      tags:
        - Adjustment lines
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the adjustment-line
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/accounts-payable-adjustment-line'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get an adjustment line:
                  value:
                    'ia::result':
                      id: '146'
                      key: '146'
                      apAdjustment:
                        id: '24'
                        key: '24'
                        href: /objects/accounts-payable/adjustment/24
                      glAccount:
                        key: '246'
                        id: '6000'
                        name: G&A Salaries
                        href: /objects/general-ledger/account/246
                      overrideOffsetGLAccount:
                        key: '109'
                        id: '2000'
                        name: Accounts Payable
                        href: /objects/general-ledger/account/109
                      accountLabel:
                        key: null
                        id: null
                      baseAmount: '-318.23'
                      txnAmount: '-210.00'
                      dimensions:
                        department:
                          key: '9'
                          id: '11'
                          name: Accounting
                          href: /objects/company-config/department/9
                        location:
                          key: '4'
                          id: '4'
                          name: Australia
                          href: /objects/company-config/location/4
                        project:
                          key: '8'
                          id: '8'
                          name: Client Services - Power Aerospace Materials
                          href: /objects/projects/project/8
                        customer:
                          key: '1'
                          id: '1'
                          name: Power Aerospace Materials
                          href: /objects/accounts-receivable/customer/1
                        vendor:
                          key: '43'
                          id: 1099 Int
                          name: 1099 Int
                          href: /objects/accounts-payable/vendor/43
                        employee:
                          key: '1'
                          id: '1'
                          name: Reser
                          href: /objects/company-config/employee/1
                        item:
                          key: '12'
                          id: '12'
                          name: PC Computer
                          href: /objects/inventory-control/item/12
                        class:
                          key: '21'
                          id: '3'
                          name: Health Care
                          href: /objects/company-config/class/21
                      memo: Adj for Bil -001
                      currency:
                        exchangeRate:
                          date: '2024-03-08'
                          typeId: '-1'
                          rate: '1.5154'
                        txnCurrency: USD
                        baseCurrency: AUD
                      lineNumber: '1'
                      paymentInformation:
                        totalBaseAmountPaid: '0.00'
                        txnTotalPaid: '0.00'
                        totalBaseAmountSelected: '0.00'
                        txnTotalSelected: '0.00'
                      adjustmentType: pa
                      audit:
                        modifiedBy: '1'
                        createdDateTime: '2024-03-09T07:42:01Z'
                        modifiedDateTime: '2024-03-09T07:52:39Z'
                        createdByUser: '1'
                      hasForm1099: 'false'
                      form1099:
                        type: null
                        box: null
                      href: /objects/accounts-payable/adjustment-line/146
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      operationId: get-accounts-payable-adjustment-line-key
    patch:
      summary: Update an adjustment line
      description: |
        Updates an existing adjustment line by setting field values. Any fields not provided remain unchanged.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business
            permissions: List, View, Edit Adjustment lines
      tags:
        - Adjustment lines
      operationId: update-accounts-payable-adjustment-line
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-payable-adjustment-line'
            examples:
              Update adjustment line with multiple fields:
                value:
                  txnAmount: '-12.00'
                  totalTxnAmount: '-12.00'
                  accountLabel:
                    id: 'Bank Interest Earned'
                  dimensions:
                    department:
                      id: '3'
                    location:
                      id: '2'
                  memo: 'Updated memo for adjustment-line'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated adjustment line
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Update an adjustment line:
                  value:
                    'ia::result':
                      key: "315783"
                      id: "315783"
                      href: "/objects/accounts-payable/adjustment-line/315783"
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0

        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete an adjustment line.
      description: Deletes an adjustment line.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business
            permissions: List, View, Delete Adjustment lines
      tags:
        - Adjustment lines
      operationId: delete-accounts-payable-adjustment-line
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    accounts-payable-adjustment-line:
      $ref: ../models/accounts-payable.adjustment-line.s1.schema.yaml
    accounts-payable-adjustment-lineRequiredProperties:
      type: object
      required:
        - apAdjustment
        - txnAmount
        - accountLabel
      properties:
        dimensions:
          type: object
          required:
            - location
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml