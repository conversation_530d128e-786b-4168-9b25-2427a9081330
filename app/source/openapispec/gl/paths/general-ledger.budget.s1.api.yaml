openapi: 3.0.0
info:
  title: general-ledger-budget
  description: general-ledger.budget API
  version: '1.0'
  contact:
    email: <EMAIL>
    name: <PERSON><PERSON>
tags:
  - name: Budgets
    description: |
      A budget is a plan to help estimate revenue and expenses for operations. The `budget` object is essentially a header that contains an array of `budget-detail` objects.
      Before you can create or import a budget, [several items in Sage Intacct must be set up](https://www.intacct.com/ia/docs/en_US/help_action/General_Ledger/Budgets_and_spending/Budgets/before-creating-your-budgets.htm):
      
      * Budgetable reporting periods: Reporting periods must exist and must be marked as Budgetable. Most often, budgetable periods are monthly.
      * Accounts: Each account for which you want to create a budget must exist in the Chart of Accounts.
      * Dimensions: Although using dimensions in budgets isn't required, dimensions such as Location, Department, or Employee can be included in a budget if they've been created in Intacct.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/general-ledger/budget:
    get:
      summary: List budgets
      description: |-
        Returns a collection with a key, ID, and link for each budget. This operation is mostly for use in testing; use the query service to find budgets that meet specific criteria and to specify the properties that you want in the response.
      x-documentationFlags:
        subscription: General Ledger
        userPermissions:
          - userType: Business, Employee, Approver
            permissions: List, View Budgets
      tags:
        - Budgets
      operationId: list-general-ledger-budget
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List budget objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List budgets:
                  value:
                    'ia::result':
                      - key: '1'
                        id: Std_Budget
                        href: /objects/general-ledger/budget/1
                      - key: '2'
                        id: KPI Budgets
                        href: /objects/general-ledger/budget/2
                    'ia::meta':
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a budget
      description: |-
        Creates a new budget. The API does not compute amounts for calculated budgets, so an `amount` must be provided when creating a budget-detail.
      x-documentationFlags:
        subscription: General Ledger
        userPermissions:
          - userType: Business User
            permissions: Add Budgets
        requirements: Reporting periods must exist and must be marked as Budgetable.
      tags:
        - Budgets
      operationId: create-general-ledger-budget
      requestBody:
        description: Budget to create
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-budget'
                - $ref: '#/components/schemas/general-ledger-budgetRequiredProperties'
            examples:
              Create a budget:
                value:
                  id: Budget1
                  description: First budget
                  isDefault: true
                  status: active
                  consolidateAmounts: false
                  currency: null
                  postProjectEstimate: false
                  lines:
                    - glAccount:
                        id: '1000'
                      dimensions:
                        location:
                          id: '1'
                      reportingPeriod:
                        id: Month ended January 2022
                      amount: '100'
                      budgetGrowth:
                        basedOn: budget
                        growBy: null
                        perPeriod: percentage
                    - glAccount:
                        id: '1000'
                      dimensions:
                        location:
                          id: '1'
                      reportingPeriod:
                        id: Month ended February 2022
                      amount: '150'
                      budgetGrowth:
                        basedOn: budget
                        growBy: null
                        perPeriod: percentage
                    - glAccount:
                        id: '1000'
                      dimensions:
                        location:
                          id: '1'
                      reportingPeriod:
                        id: Month ended March 2022
                      amount: '100'
                      budgetGrowth:
                        basedOn: budget
                        growBy: null
                        perPeriod: percentage
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New budget
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new budget:
                  value:
                    'ia::result':
                      key: '41'
                      id: Budget1
                      href: /objects/general-ledger/budget/41
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
  /objects/general-ledger/budget/{key}:
    parameters:
      - name: key
        description: System-assigned key for the budget.
        in: path
        schema:
          type: string
        required: true
        example: '291'
    get:
      summary: Get a budget
      description: |-
        Returns detailed information for a specified budget.
      x-documentationFlags:
        subscription: General Ledger
        userPermissions:
          - userType: Business, Employee, Approver
            permissions: List, View Budgets
      tags:
        - Budgets
      operationId: get-general-ledger-budget-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the budget
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/general-ledger-budget'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a budget:
                  value:
                    'ia::result':
                      key: '5'
                      id: Employee Expense Budget
                      description: Employee Expense Budget
                      isDefault: false
                      userName: Admin
                      status: active
                      audit:
                        createdDateTime: '2022-02-16T05:13:36Z'
                        modifiedDateTime: '2022-02-16T05:13:36Z'
                        createdBy: 'Admin'
                        modifiedBy: 'Admin'
                      consolidateAmounts: false
                      currency: USD
                      postProjectEstimate: false
                      postProjectContract: false
                      entity:
                        key: null
                        id: null
                        name: null
                      lines:
                        - id: '291'
                          key: '291'
                          budget:
                            key: '5'
                            id: Employee Expense Budget
                            href: /objects/general-ledger/budget/291
                          currency:
                            txnCurrency: USD
                          reportingPeriod:
                            key: '79'
                            id: Month Ended January 2022
                            name: Month Ended January 2022
                            href: /objects/reportingperiod/79
                          glAccount:
                            key: '9'
                            id: '4000'
                            name: Revenue
                            href: /objects/general-ledger/account/9
                          dimensions:
                            department:
                              key: '9'
                              id: '11'
                              name: Sales
                              href: /objects/company-config/department/9
                            location:
                              key: '1'
                              id: '1'
                              name: United States of America
                              href: /objects/company-config/location/1
                            employee:
                              key: '1'
                              id: '1'
                              name: Joe Smith
                              href: /objects/company-config/employee/10
                            item:
                              key: '101'
                              id: '101'
                              name: Widgets
                              href: /objects/inventory-control/item/101
                            class:
                              key: '2'
                              id: '2'
                              name: Mid Market
                              href: /objects/company-config/class/2
                            warehouse:
                              key: '1'
                              id: '1'
                              name: Main
                              href: /objects/inventory-control/warehouse/1
                            customer:
                              key: '1'
                              id: '1'
                              name: Big Buyer
                              href: /objects/accounts-receivable/customer/1
                            vendor:
                              key: '1'
                              id: '1'
                              name: Widget Wholesales
                              href: /objects/accounts-payable/vendor/1
                          amount: '100'
                          budgetGrowth:
                            basedOn: budget
                            growBy: null
                            perPeriod: percentage
                          notes: Projection for 2021
                          href: /objects/general-ledger/budget-detail/291
                      href: /objects/general-ledger/budget/5
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a budget
      description: |
        Updates an existing budget and its budget details by setting field values. Any fields not provided remain unchanged. 
        
        * To update an existing budget-detail line within a budget, provide the budget-detail `key` value. 
        * To add a new budget-detail line to a budget, do not include a `key` value.
        * To delete a budget-detail line, send a `DELETE /objects/general-ledger/budget-detail/{key}` request.
      x-documentationFlags:
        subscription: General Ledger
        userPermissions:
          - userType: Business User
            permissions: Edit Budgets
      tags:
        - Budgets
      operationId: update-general-ledger-budget-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-budget'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Set a budget to inactive:
                value:
                  status: inactive
              Update the amount in a budget detail line:
                value:
                  lines:
                    - key: "4120"
                      amount: '1229'
              Set a budget as default, update a line and add a line:
                value:
                  isDefault: true
                  lines:
                    - key: "135"
                      budgetGrowth:
                        basedOn: budget
                        growBy: 15
                        perPeriod: percentage
                    - glAccount:
                        id: '1000'
                      dimensions:
                        location:
                          id: '1'
                      reportingPeriod:
                        id: Month ended June 2022
                      amount: '100'
                      budgetGrowth:
                        basedOn: budget
                        growBy: 18
                        perPeriod: percentage
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Budget updated
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated budget:
                  value:
                    'ia::result':
                      key: '21'
                      id: 'Std_Budget'
                      href: /objects/general-ledger/budget/21
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete budget
      description: |-
        Deletes a budget. You cannot delete the default budget or any budget that is being used by a financial report.

        Carefully consider the implications before you delete a budget. After you delete a budget, you will no longer be able to use its data to create future budgets and it can no longer be used in financial reports.
      x-documentationFlags:
        subscription: General Ledger
        userPermissions:
          - userType: Business User
            permissions: Delete Budgets
      tags:
        - Budgets
      operationId: delete-general-ledger-budget-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    general-ledger-budget:
      $ref: ../models/general-ledger.budget.s1.schema.yaml
    general-ledger-budgetRequiredProperties:
      type: object
      required:
        - id
        - description
      properties:
        lines:
          type: array
          items:
            required:
              - glAccount
              - amount
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
