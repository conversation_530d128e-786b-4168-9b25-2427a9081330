openapi: 3.0.0
info:
  title: Reporting account set permissions
  description: Defines the permissions for general-ledger/reporting-account-set objects
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    email: jana<PERSON><PERSON><PERSON>.r<PERSON><PERSON><PERSON>@sage.com
tags:
  - name: Reporting account header permissions
    description: Defines the permissions for general-ledger/reporting-account-set objects
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/general-ledger/reporting-account-set-permission:
    get:
      summary: List reporting account set permissions
      description: Returns a collection with a key, ID, and link for each reporting account set permission.
      tags:
        - Reporting account set permissions
      operationId: list-general-ledger-reporting-account-set-permission
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of reporting-account-set-permission objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of reporting account header permissions:
                  value:
                    ia::result:
                      - key: '1'
                        id: '1'
                        href: "/objects/general-ledger/reporting-account-set-permission/1"
                      - key: '2'
                        id: '2'
                        href: "/objects/general-ledger/reporting-account-set-permission/2"
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next:
                      previous:
        '400':
          $ref: '#/components/responses/400error'
  '/objects/general-ledger/reporting-account-set-permission/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the reporting account set permission.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a reporting account set permission
      description: Returns detailed information for a specified reporting account set permission.
      tags:
        - Reporting account set permissions
      operationId: get-general-ledger-reporting-account-set-permission-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the reporting-account-set-permission
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/general-ledger-reporting-account-set-permission'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the reporting account set permission:
                  value:
                    ia::result:
                      id: '12'
                      key: '12'
                      reportingAccountSet:
                        id: '5'
                        key: '5'
                        href: "/objects/general-ledger/reporting-account-set/5"
                      permissionAppliesTo: user
                      user:
                        key: '1'
                        id: Admin
                        href: "/objects/company-config/user/1"
                      userGroup:
                        id:
                        key:
                      accessRights: deny
                      audit:
                        createdDateTime: '2024-06-25T06:37:44Z'
                        modifiedDateTime: '2024-06-25T06:37:44Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: "/objects/general-ledger/reporting-account-set-permission/12"
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a reporting account set permission
      description: Deletes a reporting account set permission.
      tags:
        - Reporting account set permissions
      operationId: delete-general-ledger-reporting-account-set-permission-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    general-ledger-reporting-account-set-permission:
      $ref: ../models/general-ledger.reporting-account-set-permission.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml