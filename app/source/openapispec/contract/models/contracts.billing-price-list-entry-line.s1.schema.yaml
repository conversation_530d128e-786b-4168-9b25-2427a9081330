title: contracts-billing-price-list-entry-line
x-mappedTo: contractitempricelistentry
x-ownedBy: contracts/billing-price-list-entry
type: object
description: Contract billing price list entry detail
properties:
  key:
    type: string
    description: System-assigned unique key for the billing price list entry line.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Billing price list entry line ID. Same as `key` for this object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: URL endpoint for the billing price list entry line.
    readOnly: true
    example: /objects/contracts/billing-price-list-entry-line/23
  startDate:
    type: string
    format: date
    description: The date on which the price becomes effective.
    x-mappedTo: STARTDATE
    example: '2024-01-01'
  flatAmount:
    type: string
    description: |
      Flat amount.

      * The established amount to bill each period. For example, if the item is text message, 5,000 text messages are included for a Flat amount of 10.00. Set this field to `0` to price the item only by the contracted quantity or quantity used.
      * The fixed price for items that don't include pricing by contracted quantity or quantity used. For example, the item is a service plan that is 300.00 for a one-year term.
    x-mappedTo: VALUE
    format: decimal-precision-2
    example: '999.99'
  variableUnitRate:
    type: string
    description: The amount to charge for each quantity range beyond the included units. Only applicable if `priceType` = `range`.
    x-mappedTo: VARUNITRATE
    example: '11.99'
    nullable: true
  includedUnits:
    type: string
    description: |
      The item quantity included in the `flatAmount`.

      Enter `0` in this field to:

      * Price the item only by the contracted quantity or quantity used
      * Set a fixed fee
      * Use the item with committed quantity billing. Included units must be 0.00 to save a committed quantity contract line.
    x-mappedTo: INCLUDEDUNITS
    example: '2000'
    nullable: true
  memo:
    type: string
    description: Notes.
    x-mappedTo: MEMO
    example: 'note to save'
    nullable: true
  billingPriceListEntry:
    type: object
    description: The `billing-price-list-entry` object that this line belongs to.
    x-mappedTo: contractitempricelist
    x-object: contracts/billing-price-list-entry
    readOnly: true
    properties:
      key:
        type: string
        description: Billing price list entry key.
        x-mappedTo: ALLOCITEMPRCLSTKEY
        readOnly: true
        example: '281'
      id:
        type: string
        description: Billing price list entry ID.
        x-mappedTo: ALLOCITEMPRCLSTKEY
        readOnly: true
        example: '281'
      href:
        type: string
        description: URL endpoint for the billing price list entry.
        readOnly: true
        example: /objects/contracts/billing-price-list-entry/281
  tiers:
    description: Pricing tiers. Only applicable if `priceType` = `tiered`.
    type: array
    x-mappedTo: PRICELISTENTRYTIER
    items:
      $ref: contracts.billing-price-list-entry-line-tier.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml