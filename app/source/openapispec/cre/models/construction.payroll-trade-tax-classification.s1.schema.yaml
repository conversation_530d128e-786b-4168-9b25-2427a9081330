title: construction-payroll-trade-tax-classification
x-mappedTo: payrollreporttradetaxclassification
x-ownedBy: construction/payroll-trade
type: object
description: Construction payroll trade tax classification object used for reporting.
properties:
  key:
    type: string
    description: System-assigned key for the payroll trade tax classification.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '17'
  id:
    type: string
    description: Unique identifier for the payroll trade tax classification. This value is the same as the `key` value for this object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '17'
  href:
    type: string
    description: URL endpoint for the payroll trade tax classification.
    readOnly: true
    example: /objects/construction/payroll-trade-tax-classification/17
  taxId:
    type: string
    description: Identifier for the tax associated with this payroll trade tax classification.
    x-mappedTo: TAXID
    x-mutable: false
    example: 'IL-SIT'
  classificationCode:
    type: string
    description: Code representing the classification for this payroll trade tax classification.
    x-mappedTo: CLASSIFICATIONCODE
    nullable: true
    example: '217'
  payrollTrade:
    type: object
    x-mappedTo: payrollreporttrade
    x-object: construction/payroll-trade
    description: Reference to the associated payroll trade.
    properties:
      key:
        type: string
        description: System-assigned key for the payroll trade.
        x-mappedTo: TRADEKEY
        x-mutable: false
        example: '14'
      id:
        type: string
        description: Unique identifier for the payroll trade.
        x-mappedTo: TRADEID
        example: 'Plumber'
      href:
        type: string
        description: URL endpoint for the payroll trade.
        readOnly: true
        example: /objects/construction/payroll-trade/14
  audit:
    $ref: ../../common/models/audit.s2.schema.yaml