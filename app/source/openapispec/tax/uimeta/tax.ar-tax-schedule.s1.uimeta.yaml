uiLabel: IA.TAX_SCHEDULE
fields:
    id:
        uiType: sequence
        uiLabel: IA.RECORD_NUMBER
    name:
        uiType: text
        uiLabel: IA.SCHEDULE_ID
    status:
        uiType: enum
        uiLabel: Status
        enumsLabels:
            -
                label: Active
                value: active
            -
                label: Inactive
                value: inactive
    description:
        uiType: multitext
        uiLabel: Description
    isSystemGenerated:
        uiType: enum
        uiLabel: IA.SYSTEM_GENERATED_FIELD
        enumsLabels:
            -
                label: IA.TRUE
                value: true
            -
                label: IA.FALSE
                value: false
refs:
    taxSolution:
        fields:
            key:
                uiType: sequence
                uiLabel: IA.TAX_SOLUTION_RECORD_NUMBER
            id:
                uiType: ptr
                uiLabel: IA.TAX_SOLUTION
