openapi: 3.0.0
info:
  title: purchasing-approval-policy
  description: purchasing.approval-policy API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Purchasing approval policies
    description: Implementing sound business practices with strong internal controls ensures that spending is approved by the appropriate individuals in your company. Setting up purchasing approvals helps maintain proper accountability for purchasing transactions.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/purchasing/approval-policy:
    get:
      summary: List purchasing approval policies
      description: Returns a collection with a key, ID, and link for each purchasing approval policy.
      tags:
        - Purchasing approval policies
      operationId: list-purchasing-approval-policy
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of approval policy objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of purchasing approval policies:
                  value:
                    ia::result:
                      - key: '34'
                        id: '34'
                        href: /objects/purchasing/approval-policy/34
                      - key: '33'
                        id: '33'
                        href: /objects/purchasing/approval-policy/33
                      - key: '21'
                        id: '21'
                        href: /objects/purchasing/approval-policy/21
                      - key: '20'
                        id: '20'
                        href: /objects/purchasing/approval-policy/20
                    ia::meta:
                      totalCount: 4
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a purchasing approval policy
      description: Creates a new purchasing approval policy.
      tags:
        - Purchasing approval policies
      operationId: create-purchasing-approval-policy
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-approval-policy'
                - $ref: '#/components/schemas/purchasing-approval-policyRequiredProperties'
            examples:
              Creates a purchasing approval policy:
                value:
                  documentType:
                    key: '23'
                  lines:
                    - ruleType: valueApprovalTransactionDepartment
                      approvalRuleSet:
                        id: Department Based
                    - ruleType: userLevel
                      approver:
                        key: '6'
                    - ruleType: valueApproval
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New purchasing approval policy
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new purchasing approval policy:
                  value:
                    ia::result:
                      id: '33'
                      key: '33'
                      href: /objects/purchasing/approval-policy/33
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  /objects/purchasing/approval-policy/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the purchasing approval policy.
        in: path
        required: true
        schema:
          type: string
        example: '33'
    get:
      summary: Get a purchasing approval policy
      description: Returns detailed information for a specified purchasing approval policy.
      tags:
        - Purchasing approval policies
      operationId: get-purchasing-approval-policy-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the purchasing approval policy
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/purchasing-approval-policy'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the purchasing approval policy:
                  value:
                    ia::result:
                      id: '33'
                      key: '33'
                      documentType:
                        key: '23'
                        id: Purchase Order
                        href: /objects/purchasing/txn-definition::Purchase%20Order/23
                      policyFor: Transaction Definition
                      audit:
                        createdDateTime: '2024-06-25T05:49:11Z'
                        modifiedDateTime: '2024-06-25T05:49:45Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      lines:
                        - id: '47'
                          key: '47'
                          sequenceNumber: 1
                          approvalPolicy:
                            id: '33'
                            key: '33'
                            href: /objects/purchasing/approval-policy/33
                          ruleType: valueApprovalTransactionDepartment
                          approver:
                            key: 'null'
                            id: 'null'
                          approverUserGroup:
                            key: 'null'
                            id: 'null'
                          approvalRuleSet:
                            key: '7'
                            id: Department Based
                            href: /objects/purchasing/approval-rule-set/7
                        - id: '48'
                          key: '48'
                          sequenceNumber: 2
                          approvalPolicy:
                            id: '33'
                            key: '33'
                            href: /objects/purchasing/approval-policy/33
                          ruleType: valueApprovalTransactionDepartment
                          approver:
                            key: '6'
                            id: 'John'
                            href: /objects/company-config/user/6
                          approverUserGroup:
                            key: 'null'
                            id: 'null'
                          approvalRuleSet:
                            key: 'null'
                            id: 'null'
                          href: /objects/purchasing/approval-policy-line/48
                        - id: '49'
                          key: '49'
                          sequenceNumber: 3
                          approvalPolicy:
                            id: '33'
                            key: '33'
                            href: /objects/purchasing/approval-policy/33
                          ruleType: valueApproval
                          approver:
                            key: 'null'
                            id: 'null'
                          approverUserGroup:
                            key: 'null'
                            id: 'null'
                          approvalRuleSet:
                            key: 'null'
                            id: 'null'
                          href: /objects/purchasing/approval-policy-line/49
                      href: /objects/purchasing/approval-policy/33
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a purchasing approval policy
      description: Updates an existing purchasing approval policy by setting field values. Any fields not provided remain unchanged.
      tags:
        - Purchasing approval policies
      operationId: update-purchasing-approval-policy-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-approval-policy'
                - type: object
            examples:
              Updates a purchasing approval policy:
                value:
                  lines:
                    - key: '47'
                      approvalRuleSet:
                        key: '8'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated purchasing approval policy
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated purchasing approval policy:
                  value:
                    ia::result:
                      id: '33'
                      key: '33'
                      href: /objects/purchasing/approval-policy/33
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a purchasing approval policy
      description: Deletes a purchasing approval policy.
      tags:
        - Purchasing approval policies
      operationId: delete-purchasing-approval-policy-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    purchasing-approval-policy:
      $ref: ../models/purchasing.approval-policy.s1.schema.yaml
    purchasing-approval-policyRequiredProperties:
      type: object
      required:
        - documentType
      properties:
        lines:
          type: array
          items:
            required:
            - ruleType
            example: 'valueApprovalTransactionDepartment'
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml