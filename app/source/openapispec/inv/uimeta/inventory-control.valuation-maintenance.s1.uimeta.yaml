fields:
  description:
    uiType: text
    uiLabel: IA.DESCRIPTION
  emailAddress:
    uiType: email
    uiLabel: IA.EMAIL_ADDRESS
#  id:
#    uiType: integer
#    uiLabel: IA.RECORD_NO
#  isScheduledOperation:
#    uiType: text
#    uiLabel: 'Scheduled maintenance'
#  key:
#    uiType: sequence
#    uiLabel: IA.RECORD_NO
  name:
    uiType: string
    uiLabel: IA.NAME
  state:
    uiType: string
    uiLabel: IA.STATE
  summaryResults:
    uiType: text
    uiLabel: IA.RESULTS
#  createdBy:
#    uiType: text
#    uiLabel: IA.CREATED_BY
groups:
  audit:
    fields:
      createdBy:
        uiType: text
        uiLabel: IA.CREATED_BY
      createdDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_CREATED
      modifiedBy:
        uiType: text
        uiLabel: IA.MODIFIED_BY
      modifiedDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_MODIFIED
    refs:
      createdByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.CREATED_BY
          id:
            uiType: text
            uiLabel: IA.CREATED_BY_LOGIN_ID
      modifiedByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.MODIFIED_BY
          id:
            uiType: text
            uiLabel: IA.MODIFIED_BY_LOGIN_ID
#  maintenanceOptions:
#    fields:
#      action:
#        uiType: text
#        uiLabel: 'Analyze or update'
#      asOfDate:
#        uiType: text
#        uiLabel: 'As of date'
#      costUpdatePeriods:
#        uiType: text
#        uiLabel: 'Open or closed periods'
#      transactionsToReport:
#        uiType: text
#        uiLabel: 'Show all transactions'
#      updateGL:
#        uiType: text
#        uiLabel: 'Update GL in open periods'
#  maintenanceSchedule:
#    fields:
#      endDate:
#        uiType: text
#        uiLabel: 'Repeat end date'
#      monthlyRepeatDay:
#        uiType: text
#        uiLabel: 'Repeat repetition'
#      numberOfOccurrences:
#        uiType: text
#        uiLabel: 'Repeat occurrences'
#      repeatEndType:
#        uiType: text
#        uiLabel: 'Repeat end type'
#      repeatInterval:
#        uiType: text
#        uiLabel: 'Repeat interval'
#      repeatPeriod:
#        uiType: text
#        uiLabel: 'Repeat period'
#      startDate:
#        uiType: text
#        uiLabel: 'Repeat start date'
#      weeklyRepeatDay:
#        uiType: text
#        uiLabel: 'Repeat week day'
#  valuationFilters:
#    fields:
#      costMethods:
#        uiType: text
#        uiLabel: 'Cost methods'
#    refs:
#      fromItem:
#        fields:
#          id:
#            uiType: text
#            uiLabel: 'From item id'
#          key:
#            uiType: text
#           uiLabel: 'From item key'
#      itemGroup:
#        fields:
#          id:
#            uiType: text
#            uiLabel: 'Item group'
#          key:
#            uiType: text
#            uiLabel: 'Item group'
#      toItem:
#        fields:
#          id:
#            uiType: text
#            uiLabel: 'To item id'
#          key:
#            uiType: text
#            uiLabel: 'To item key'
#      warehouse:
#        fields:
#          id:
#            uiType: text
#            uiLabel: 'Warhouse id'
#          key:
#            uiType: text
#            uiLabel: 'Warhouse id'