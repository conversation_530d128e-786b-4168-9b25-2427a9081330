openapi: 3.0.0
info:
  title: accounts-receivable-customer-account-group
  description: accounts-receivable.customer-account-group API
  version: 1.0.0
  contact:
    email: Sam<PERSON>.<EMAIL>
    name: <PERSON><PERSON> Jain
tags:
  - name: Customer account groups
    description: Organize customers into account groups to simplify reporting.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/accounts-receivable/customer-account-group:
    get:
      summary: List customer account groups
      tags:
        - Customer account groups
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of customer-account-group objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List customer account groups:
                  value:
                    'ia::result':
                      - key: '1'
                        id: HW VGL Group
                        href: /objects/accounts-receivable/customer-account-group/1
                      - key: '2'
                        id: OS VGL Group
                        href: /objects/accounts-receivable/customer-account-group/2
                      - key: '3'
                        id: Stationary VGL Group
                        href: /objects/accounts-receivable/customer-account-group/3
                      - key: '4'
                        id: Electronic VGL Group
                        href: /objects/accounts-receivable/customer-account-group/4
                      - key: '5'
                        id: Gardening VGL Group
                        href: /objects/accounts-receivable/customer-account-group/5
                      - key: '6'
                        id: Auto VGL Group
                        href: /objects/accounts-receivable/customer-account-group/6
                      - key: '7'
                        id: Inactive VGL Group
                        href: /objects/accounts-receivable/customer-account-group/7
                    'ia::meta':
                      totalCount: 7
                      start: 1
                      pageSize: 100
                      next: 0
                      previous: 0
        '400':
          $ref: '#/components/responses/400error'
      operationId: list-accounts-receivable-customer-account-group
      description: Returns a collection with a key, ID, and link for each customer account group.
    post:
      summary: Create a customer account group
      tags:
        - Customer account groups
      operationId: create-accounts-receivable-customer-account-group
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New customer-account-group
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new customer account group:
                  value:
                    'ia::result':
                      key: '18'
                      id: 1099 Customer Group
                      href: /objects/accounts-receivable/customer-account-group/18
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
      description: Creates a new customer account group.
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-receivable-customer-account-group'
                - $ref: '#/components/schemas/customerAccountGroupRequiredProperties'
            examples:
              Create a customer account group:
                value:
                  id: 1099 Customer Group
                  groupType: customer
                  status: active
  /objects/accounts-receivable/customer-account-group/{key}:
    parameters:
      - schema:
          type: string
        name: key
        in: path
        required: true
        description: System-assigned unique key for the customer account group.
    get:
      summary: Get a customer account group
      tags:
        - Customer account groups
      responses:
        '200':
          description: customer-account-group Found
          content:
            application/json:
              schema:
                type: object
                title: Details of the customer account group
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/accounts-receivable-customer-account-group'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a customer account group:
                  value:
                    'ia::result':
                      id: HW VGL Group
                      key: '1'
                      groupType: customer
                      status: inactive
                      href: /objects/accounts-receivable/customer-account-group/1
                    'ia::meta':
                      totalCount: 1
        '404':
          description: customer-account-group Not Found
      operationId: get-accounts-receivable-customer-account-group-key
      description: Returns detailed information for a specified customer account group.
    patch:
      summary: Update a customer account group
      operationId: update-accounts-receivable-customer-account-group-key
      description: Updates an existing customer account group by setting field values. Any fields not provided remain unchanged.
      tags:
        - Customer account groups
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated customer-account-group
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated customer account group:
                  value:
                    'ia::result':
                      key: '18'
                      id: 1099 Customer Group
                      href: /objects/accounts-receivable/customer-account-group/18
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-receivable-customer-account-group'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update a customer account group:
                value:
                  status: inactive
        description: ''
    delete:
      summary: Delete a customer account group
      description: Deletes a customer account group.
      tags:
        - Customer account groups
      operationId: delete-accounts-receivable-customer-account-group-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    customerAccountGroupRequiredProperties:
      type: object
      required:
        - id
    accounts-receivable-customer-account-group:
      $ref: ../models/accounts-receivable.customer-account-group.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
