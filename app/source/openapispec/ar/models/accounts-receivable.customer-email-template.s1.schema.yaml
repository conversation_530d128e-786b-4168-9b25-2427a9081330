title: accounts-receivable-customer-email-template
x-mappedTo: customeremailtemplate
type: object
x-ownedBy: accounts-receivable/customer
description: Customer email templates can be used to automate standard messages sent to multiple customers.
properties:
  key:
    type: string
    description: System-assigned unique key for the customer email template.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '1'
  id:
    type: string
    description: Identifier for the customer email template. This value is the same as `key` for this object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '1'
  href:
    type: string
    description: URL endpoint for the customer email template.
    readOnly: true
    example: /objects/accounts-receivable/customer-email-template/1
  customer:
    type: object
    description: |
      [Customer](accounts-receivable.customer) associated with the email template.
    x-object: accounts-receivable/customer
    x-mappedTo: customer
    title: customer
    properties:
      key:
        type: string
        description: System-assigned key for the customer.
        x-mappedTo: CUSTOMERKEY
        example: '127'
      id:
        type: string
        description: Unique identifier for the customer.
        x-mappedTo: CUSTOMERID
        example: '014'
      href:
        type: string
        description: URL endpoint for the customer.
        readOnly: true
        example: /objects/accounts-receivable/customer/127
  txnDefinition:
    type: object
    x-mappedTo: sodocumentparams
    x-object: order-entry/txn-definition
    description: Transaction definition, which is the template that contains accounting settings, workflow rules, and other configuration settings for a transaction.
    properties:
      href:
        type: string
        description: URL for the order-entry-txn-definition.
        readOnly: true
        example: /objects/order-entry/txn-definition/23
      key:
        type: string
        description: Document template Key.
        x-mappedTo: DOCPARKEY
        example: '23'
        readOnly: true
      id:
        type: string
        description: Document template ID.
        x-mappedTo: DOCPARID
        example: Customer Invoice  
  emailTemplate:
    type: object
    description: Associated email template.
    x-object: company-config/email-template
    x-mappedTo: emailtemplate
    title: emailTemplate
    properties:
      key:
        type: string
        description: System-assigned key for the email template.
        x-mappedTo: EMAILTEMPLATEKEY
        example: '1'
      id:
        type: string
        description: Unique identifier for the email template.
        x-mappedTo: EMAILTEMPLATEKEY
        example: '1'
      name:
        type: string
        description: Name of the email template.
        x-mappedTo: EMAILTEMPLATENAME
        readOnly: true
        example: 'Customer Trial'
      templateType:
        type: string
        description: Specify the type of transaction that will use the email template.
        x-mappedTo: EMAILTEMPLATETYPE
        enum:
          - 'arInvoice'
          - 'arStatement'
          - 'contract'
          - 'orderEntryTxn'
          - 'purchasingTxn'
        x-mappedToValues:
          - 'arinvoice'
          - 'arstatement'
          - 'contract'
          - 'sodocument'
          - 'podocument'
        example: arInvoice
        default: arInvoice
        readOnly: true
      href:
        type: string
        description: URL of the email template.
        readOnly: true
        example: /objects/company-config/email-template/1
