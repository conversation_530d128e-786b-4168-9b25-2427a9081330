title: asset-group
x-mappedTo: fixedassetgroup
type: object
description: Asset group object is to create a particular Asset groups as a dimensions group.
properties:
  key:
    type: string
    description: System-assigned key for the Asset-group.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '1'
  id:
    type: string
    description: Asset group ID.
    x-mutable: false
    x-mappedTo: ID
    example: A2K-10K
  href:
    type: string
    description: Endpoint for the Asset-group.
    readOnly: true
    example: /objects/fixed-assets/asset-group/1
  name:
    type: string
    description: Name of the Asset group.
    x-mappedTo: NAME
    example: Assets2K-10K
  description:
    type: string
    description: Description.
    x-mappedTo: DESCRIPTION
    nullable: true
    example: Assets that cost between $2000 and $10000.
  groupType:
    type: string
    description: Group type.
    x-mappedTo: GROUPTYPE
    enum:
      - all
      - specific
    x-mappedToValues:
      - ALL
      - SPECIFIC
    default: all
    example: all
  isDimensionStructure:
    type: boolean
    description: If this checked, creating a dimension structure from a group enables you to add the group to the rows or columns of a financial report.
    x-mappedTo: CREATEDIMCOMP
    default: false
    example: false
  memberFilter:
    type: object
    title: memberFilter
    description: One or more filters to select the vendors to include in the vendor group.
    allOf:
      - $ref: ../../common/models/member-filter.s1.schema.yaml
      - type: object
        x-mappedToType: memberFilter:fixed-assets/asset
        x-mappedTo: MEMBERFILTERS
  groupMembers:
    type: array
    x-mappedTo: MEMBERS
    x-object: fixed-assets/asset
    x-schemaOverride: true
    items:
      type: object
      properties:
        key:
          type: string
          description: Members.
          x-mappedTo: RECORDNO
          example: '1'
        id:
          type: string
          description: Members.
          x-mappedTo: RECORDNO
          example: '1'
        name:
          type: string
          x-mappedTo: ASSETNAME
          readOnly: true
          example: Laptop
        href:
          type: string
          description: Endpoint for the asset-group-member.
          readOnly: true
          example: /objects/fixed-assets/asset/1
        sortOrder:
          type: string
          description: Sort order.
          x-mappedTo: SORTORD
          readOnly: true
          example: '0'
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  entity:
    $ref: ../../common/references/entity-ref.s1.schema.yaml
