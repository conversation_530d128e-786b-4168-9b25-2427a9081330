openapi: 3.0.0
info:
  title: fixed-assets-bulk-action-run-summary
  description: The fixed-assets-bulk-action-run-summary shows the status of a bulk action request during processing and shows the results of the request.
  version: '1.0'
  contact:
    name: prash<PERSON><PERSON> kadubandi
    email: <EMAIL>
tags:
  - name: Bulk action run summary
    description: Bulk action run summary is used to display the run object details for depreciatoin schedule entries posted in bulk.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/fixed-assets/fa-bulk-action-run-summary:
    get:
      summary: List bulk action run summaries
      description: Returns a collection with a key, ID, and link for each bulk action run summary.
      tags:
        - Bulk action run summary
      operationId: list-fixed-assets-bulk-action-run-summary
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of bulk-action-run-summary objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of Run object summaries:
                  value:
                    'ia::result':
                      - key: '20'
                        id: '20'
                        href: /objects/fixed-assets/fa-bulk-action-run-summary/20
                      - key: '14'
                        id: '14'
                        href: /objects/fixed-assets/fa-bulk-action-run-summary/14
                      - key: '16'
                        id: '16'
                        href: /objects/fixed-assets/fa-bulk-action-run-summary/16
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  '/objects/fixed-assets/fa-bulk-action-run-summary/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the bulk-action-run-summary.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a bulk action run summary
      description: Returns detailed information for a particular bulk action run summary.
      tags:
        - Bulk action run summary
      operationId: get-fixed-assets-bulk-action-run-summary-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the bulk-action-run-summary
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/fixed-assets-bulk-action-run-summary'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the fa-bulk-action-run-summary:
                  value:
                    ia::result:
                      id: '299'
                      key: '299'
                      executionMode: offline
                      location:
                        key: '1'
                        id: '1'
                        name: United States of America
                        href: "/objects/company-config/location/1"
                      runCount:
                        inProgress: 0
                        failure: 1
                        success: 0
                        queued: 0
                        total: 1
                      audit:
                        createdBy: '1'
                        modifiedBy: '1'
                        createdDateTime: '2024-04-24T22:32:07Z'
                        modifiedDateTime: '2024-04-24T22:33:02Z'
                      href: "/objects/fixed-assets/fa-bulk-action-run-summary/299"
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    fixed-assets-bulk-action-run-summary:
      $ref: ../models/fixed-assets.fa-bulk-action-run-summary.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml