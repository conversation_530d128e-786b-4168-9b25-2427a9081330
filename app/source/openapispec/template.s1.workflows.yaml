info:
  title: <application-name>-<object>-workflows
  description: Workflows for <objectEnglishName>. <Add info about purpose of workflows, for example, "Actions to support vendor approval process.">
<application-name>-<object-name>-actions-<action-name>-request:
  type: object
  x-mappedTo: <object>
  properties:
    key:
      type: string
      description: System-assigned unique key of the <objectEnglishName>.
      x-mappedTo: RECORDNO
      example: '518'
    <property-name>:
      type: string
      description: Description of property required to complete the action.
      x-mappedTo: <ENT field>
      example: <example-value>
  required:
    - key
    - <property-name>
<application-name>-<object-name>-actions-<action-name>-response:
  type: object
  x-mappedTo: <object>
  properties:
    key:
      type: string
      description: System-assigned unique key of the <objectEnglishName>.
      x-mappedTo: RECORDNO
      example: '518'
    id:
      type: string
      description: Unique identifier of the <objectEnglishName>.
      x-mappedTo: <VID for object>
      example: 'V-00014'
    href:
      type: string
      description: URL of the <objectEnglishName>.
      example: /objects/<application-name>/<object>/518
    state:
      type: string
      description: <objectEnglishName> state
      x-mappedTo: STATE
      enum:
        - <state(s)>
      x-mappedToValues:
        - <ENT field(s)>
      example: approved
