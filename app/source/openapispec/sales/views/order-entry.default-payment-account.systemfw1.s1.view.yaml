key: order-entry/default-payment-account::systemDefaultPaymentAccountFW1
id: systemDefaultPaymentAccountFW1
object: order-entry/default-payment-account
name: All
description: Specifies all active order-entry/default-payment-account
query:
  object: order-entry/default-payment-account
  fields:
    - id
    - accountType
    - currency
    - paymentMethod
    - bankAccount.name
  orderBy:
    - id: asc
metadata:
  frozenColumnsCount: 2
  columns:
    - id: "id"
      format: "clip"
      size: 40
    - id: "accountType"
      format: "clip"
      size: 40
    - id: "currency"
      format: "clip"
      size: 40
    - id: "paymentMethod"
      format: "clip"
      size: 40
    - id: "bankAccount.name"
      format: "clip"
      size: 40
contexts:
  - __default
