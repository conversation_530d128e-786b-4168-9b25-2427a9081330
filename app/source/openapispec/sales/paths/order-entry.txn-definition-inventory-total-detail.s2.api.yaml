openapi: 3.0.0
info:
  title: order-entry-txn-definition-inventory-total-detail
  description: order-entry.txn-definition-inventory-total-detail API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Transaction definition inventory total details
    description: |
     Use this object to specify the inventory running total that will be affected by the transaction, whether to track quantity, value, or both, and whether the running total will increase or decrease when the user saves the transaction.

     For more information, see [Transaction definitions - Order Entry](https://www.intacct.com/ia/docs/en_US/help_action/Default.htm#cshid=Order_Entry_transaction_definitions) in the Sage Intacct Help Center.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/order-entry/txn-definition-inventory-total-detail:
    get:
      summary: List transaction definition inventory total detail objects
      description: Returns a collection with a key, ID, and link for each transaction definition inventory total detail object. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      x-documentationFlags:
        subscription: Order Entry
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View, transaction definitions
      tags:
        - Transaction definition inventory total details
      operationId: list-order-entry-txn-definition-inventory-total-detail
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of order-entry-txn-definition-inventory-total-detail objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List transaction definition inventory total detail objects:
                  value:
                    ia::result:
                        - key: '9'
                          id: '9'
                          href: "/objects/order-entry/txn-definition-inventory-total-detail/9"
                        - key: '12'
                          id: '12'
                          href: "/objects/order-entry/txn-definition-inventory-total-detail/12"
                        - key: '14'
                          id: '14'
                          href: "/objects/order-entry/txn-definition-inventory-total-detail/14"
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a transaction definition inventory total detail object
      description: Creates a new transaction definition inventory total detail object.
      x-documentationFlags:
        subscription: Order Entry
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View, Add transaction definitions
      tags:
        - Transaction definition inventory total details
      operationId: create-order-entry-txn-definition-inventory-total-detail
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/order-entry-txn-definition-inventory-total-detail'
                - $ref: '#/components/schemas/order-entry-txn-definition-inventory-total-detailRequiredProperties'
            examples:
              Create a transaction definition inventory total detail object:
                value:
                  txnDefinition:
                    key: '64'
                  maintainType: quantity
                  inventoryTotal:
                    id: DAMAGED
                  operation: add
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New order-entry-txn-definition-inventory-total-detail
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new transaction definition inventory total detail object:
                  value:
                    txnDefinition:
                      key: '96'
                    inventoryTotal:
                      id: DAMAGED
                    maintainType: quantity
                    operation: add
        '400':
          $ref: '#/components/responses/400error'
  /objects/order-entry/txn-definition-inventory-total-detail/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the transaction definition inventory total detail object.
        in: path
        example: '96'
        required: true
        schema:
          type: string
    get:
      summary: Get a transaction definition inventory total detail object
      description: Returns detailed information for a specified transaction definition inventory total detail object.
      x-documentationFlags:
        subscription: Order Entry
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View, transaction definitions
      tags:
        - Transaction definition inventory total details
      operationId: get-order-entry-txn-definition-inventory-total-detail-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the transaction definition inventory total detail object
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/order-entry-txn-definition-inventory-total-detail'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a transaction definition inventory total detail object:
                  value:
                    ia::result:
                      key: '125'
                      id: '125'
                      txnDefinition:
                        key: '64'
                        id: Sales Order
                        href: /objects/order-entry/txn-definition/64
                      maintainType: quantity
                      inventoryTotal:
                        key: '29'
                        id: DAMAGED
                        href: /objects/inventory-control/total/29
                      operation: add
                      href: /objects/order-entry/txn-definition-inventory-total-detail/125
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a transaction definition inventory total detail object
      description: Updates an existing transaction definition inventory total detail object by setting field values. Any fields not provided remain unchanged.
      x-documentationFlags:
        subscription: Order Entry
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View, Edit transaction definitions
      tags:
        - Transaction definition inventory total details
      operationId: update-order-entry-txn-definition-inventory-total-detail-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/order-entry-txn-definition-inventory-total-detail'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update a transaction definition inventory total detail object:
                value:
                  maintainType: quantityAndValue
                  operation: subtract
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated order-entry-txn-definition-inventory-total-detail
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated transaction definition inventory total detail object:
                  value:
                    ia::result:
                      key: '128'
                      id: '128'
                      href: /objects/order-entry/txn-definition-inventory-total-detail/128
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a transaction definition inventory total detail object
      description: Deletes a transaction definition inventory total detail object.
      x-documentationFlags:
        subscription: Order Entry
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View, Delete transaction definitions
      tags:
        - Transaction definition inventory total details
      operationId: delete-order-entry-txn-definition-inventory-total-detail-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    order-entry-txn-definition-inventory-total-detail:
      $ref: ../models/order-entry.txn-definition-inventory-total-detail.s2.schema.yaml
    order-entry-txn-definition-inventory-total-detailRequiredProperties:
      required:
        - txnDefinition
        - inventoryTotal
        - maintainType
        - operation
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml