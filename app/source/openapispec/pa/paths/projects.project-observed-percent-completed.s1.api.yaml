openapi: 3.0.0
info:
  title: project-observed-percent-completed
  description: project-observed-percent-completed API
  version: '1.0'
  contact:
    name: sure<PERSON> babu adiserla
    email: <EMAIL>
tags:
  - name: Project observed percent completed
    description: Indicates what percentage of a project has been completed by a certain date.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/projects/project-observed-percent-completed:
    get:
      summary: List
      description: This table is to enter your personal estimation of how complete the project is as of a specific date. It is used to track percent completion values and dates by entering "as of dates" and percentages as the project progresses.
      tags:
        - project observed percent completed
      operationId: list-projects-project-observed-percent-completed
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of project observed percent completed objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of Project observed percent completed:
                  value:
                    'ia::result':
                      - key: '1'
                        id: '1'
                        href: /objects/projects/project-observed-percent-completed/1
                      - key: '2'
                        id: '2'
                        href: /objects/projects/project-observed-percent-completed/2
                      - key: '3'
                        id: '3'
                        href: /objects/projects/project-observed-percent-completed/3
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: 101
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a project observed percent completed
      description: Creates a new project observed percent completed.
      tags:
        - project observed percent completed
      operationId: create-projects-project-observed-percent-completed
      requestBody:
        description: project observed percent completed
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/projects-project-observed-percent-completed'
                - $ref: '#/components/schemas/projects-project-observed-percent-completedRequiredProperties'
            examples:
              Creates a project observed percent completed:
                value:
                  project:
                    key: '109'
                    asOfDate: '2023-11-11'
                    percentComplete: '25'
                    notes: Completed on 11/11/23
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New project observed percent completed
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                New project observed percent completed:
                  value:
                    'ia::result':
                      key: '1'
                      id: '1'
                      href: /objects/projects/project-observed-percent-completed/1
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/projects/project-observed-percent-completed/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the project observed percent completed.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a project observed percent completed
      description: Returns detailed information for a particular project observed percent completed.
      tags:
        - project observed percent completed
      operationId: get-projects-project-observed-percent-completed-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the project observed percent completed
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/projects-project-observed-percent-completed'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the project observed percent completed:
                  value:
                    'ia::result':
                      project:
                        key: '109'
                        id: 22-001
                        name: Implementation Project
                        href: /objects/project/109
                      href: /objects/projects/project-observed-percent-completed/23
                      key: '23'
                      id: '23'
                      asOfDate: '2023-11-11'
                      percentComplete: '25'
                      notes: Completion on 11/11/23
                      audit:
                        createdDateTime: '2022-04-20T16:20:00Z'
                        modifiedDateTime: '2022-04-20T16:20:00Z'
                        createdBy: '1'
                        modifiedBy: '95'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a project observed percent completed
      description: Updates an existing project observed percent completed by setting field values. Any fields not provided remain unchanged.
      tags:
        - project observed percent completed
      operationId: update-projects-project-observed-percent-completed-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/projects-project-observed-percent-completed'
                - type: object
                  properties:
                    key:
                      readOnly: true
                    project:
                      readOnly: true
            examples:
              Updates a project observed percent completed:
                value:
                  notes: As of Dec.1, 2023
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated project observed percent completed
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Updated project observed percent completed:
                  value:
                    'ia::result':
                      key: '1'
                      id: '1'
                      href: /objects/projects/project-observed-percent-completed/1
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a project observed percent completed
      description: Deletes a project observed percent completed.
      tags:
        - project observed percent completed
      operationId: delete-projects-project-observed-percent-completed-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    projects-project-observed-percent-completed:
      $ref: ../models/projects.project-observed-percent-completed.s1.schema.yaml
    projects-project-observed-percent-completedRequiredProperties:
      type: object
      required:
        - project
        - asOfDate
        - percentComplete
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
