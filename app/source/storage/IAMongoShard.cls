<?php
//=============================================================================
//
//  FILE:     IAMongoShard.cls
//  AUTHOR:   <PERSON>
//  DESCRIPTION: Manage assignments between companies and their MongoDB Shard
//
//  (C)2000,2009 Intacct Corporation, All Rights Reserved
//
//  Intacct Corporation Proprietary Information.
//  This document contains trade secret data that belongs to Intacct
//  corporation and is protected by the copyright laws. Information herein
//  may not be used, copied or disclosed in whole or part without prior
//  written consent from Intacct Corporation.
//
//=============================================================================



/**
 * A integration class for handling the assignment of a MongoDB shard (replica set) to a company
 *
 * <AUTHOR>
 *
 */
class IAMongoShard
{
    const SHARD_PENDING = 'SHARD_PENDING';

    /**
     * Cache of cny# to their corresponding replica set
     * A key of 0 indicates a global context (and uses the global replica set)
     *
     * @var string[] $rsKeyMap
     */
    protected static $rsKeyMap = [];

    /**
     * Notes on usage of selectShard(), isShardCached() and isShardPending()
     *
     * If you intend to use MongoDB storage inside code that queries the underlying Oracle DB, then you must
     * be careful to not create an infinite recursion loop, since both depend on each other.
     *
     * Here is an example of how to do it:
     *
     * // Ensure that the shard is cached, so future mongoDB calls will not make Oracle DB calls
     * if ( !IAMongoShard::isShardCached() && !IAMongoShard::isShardPending() ) {
     *   // When selectShard is called, it may come back into this function.
     *   // If it does, then both conditions will be false, ensuring that it wont call itself back.
     *   IAMongoShard::selectShard();
     * }
     *
     * // Now call your MongoDB operation only if the shard is cached
     * if ( IAMongoShard::isShardCached() ) {
     *   // <call your mongo related code/functions>
     * }
     */
    
    /**
     * Retrieve the assigned shard for a company (or global). If one does not exist, it will assign one.
     *
     * @param MongoDBHostsManager|null $mdbhm
     *
     * @return string (replica set name)
     * @throws StorageException
     */
    public static function selectShard(MongoDBHostsManager $mdbhm=null)
    {
        // The company cny# is the key (0 represents a global context)
        $key = GetMyCompany() ?? 0;

        // Use an array cache to remember the replica set for this company/GLOBAL
        if ( !isset(self::$rsKeyMap[$key]) ) {
            // Set the state of this key to PENDING, so that callers will not attempt to call us again until finished
            // This prevents functions like QueryResult, which may be called in here, from calling us back again.
            self::$rsKeyMap[$key] = self::SHARD_PENDING;

            // Make sure we have a hosts manager
            if ( !isset($mdbhm) ) {
                $mdbhm = MongoDBEnvManager::getInstance()->getHostsManager();
            }

            $replicaSet = null;
            if ( 0 == $key ) {
                $replicaSet = $mdbhm->getShardHostGlobal();
            } else {
                // Use the profile if possible
                if ( Profile::exists() ) {
                    $replicaSet = Profile::getCompanyCacheProperty('COMPANYPREF', 'MONGODB_SHARD_HOST');
                }

                // This is a defensive check, in case the profile exists but is not populated
                if (!$replicaSet) {
                    // Try to get it directly from the database
                    $replicaSet = self::getCompanyPrefsForMongoDBSharding($key);
                }

                // This company doesn't have an assigned replica set yet. Find one.
                if (!$replicaSet) {
                    $replicaSet = $mdbhm->getShardHostNew();
                    $compPrefs['MONGODB_SHARD_HOST'] = $replicaSet;
                    SetCompanyPreferences(Globals::$g->_userid, $compPrefs);
                }
            }

            self::$rsKeyMap[$key] = $replicaSet;
        }

        // If the shard value is PENDING, then this function was called during a previous call of itself.
        // If we don't break out now, we will likely hit a stack overflow (function call recursive loop)
        if ( self::$rsKeyMap[$key] === self::SHARD_PENDING ) {
            throw new StorageException("Attempt to call " . __METHOD__ . " while shard processing is PENDING. Probable function call recursion.");
        }

        return self::$rsKeyMap[$key];
    }

    /**
     * Indicate if the shard information for the current context (company) is cached
     *
     * Call this function when attempting to use MongoDB within a DB query function.
     * If it returns false (and isShardPending() is also false), then call selectShard(...)
     *
     * @return bool
     */
    public static function isShardCached()
    {
        // The company cny# is the key (0 represents a global context)
        $key = GetMyCompany() ?? 0;
        return ( (self::$rsKeyMap[$key] ?? self::SHARD_PENDING) !== self::SHARD_PENDING );
    }

    /**
     * Indicate if the shard information is pending.
     * This means, selectShard() was called, but it has not finished
     *
     * @return bool
     */
    public static function isShardPending()
    {
        // The company cny# is the key (0 represents a global context)
        $key = GetMyCompany() ?? 0;
        return ( (self::$rsKeyMap[$key] ?? 'DUMMY') === self::SHARD_PENDING );
    }


    /**
     * Retrieve the assigned shard for a company (or global). If one does not exist, it will assign one.
     *
     * @param MongoDBHostsManager $mdbhm
     *
     * @return MongoDBClientBase
     */
    public static function selectClient(MongoDBHostsManager $mdbhm)
    {
        $rsKey = static::selectShard($mdbhm);
        return $mdbhm->getMDBClientByRSKey($rsKey);
    }

    /**
     * Retrieve MongoDB shard assignment for a company
     *
     * @param string|int $cny the company cny#
     *
     * @return string|null|false
     */
    public static function getCompanyPrefsForMongoDBSharding($cny)
    {
        $stmt = "SELECT value FROM companypref WHERE cny# = :1 AND property = 'MONGODB_SHARD_HOST'";
        $result = QueryResult([$stmt, $cny]);

        if ( !is_array($result) ) {
            return false;
        } else if ( empty($result) ) {
            return null;
        } else {
            return $result[0]['VALUE'];
        }
    }

    /**
     * Remove MongoDB shard assignment for a company
     *
     * @param string|int $cny the company cny#
     *
     * @return bool
     */
    public static function removeCompanyPrefsForMongoDBSharding($cny)
    {
        $stmt = "DELETE FROM companypref WHERE cny# = :1 AND property = 'MONGODB_SHARD_HOST'";
        return ExecStmt([$stmt, $cny]);
    }

    /**
     * Update the MongoDB Shard (replica set key) in company preferences
     *
     * @param string|int $cny the company cny#
     * @param string     $rsKey   replica set key name
     *
     * @return bool
     */
    public static function updateCompanyPrefsForMongoDBSharding($cny, $rsKey)
    {
        $stmt = "UPDATE companypref SET value = :1 WHERE cny# = :2 AND property = 'MONGODB_SHARD_HOST'";
        return ExecStmt([$stmt, $rsKey, $cny]);
    }

    /**
     * Drop a Mongo Database for a company
     *
     * @param string|int  $cny
     * @param string|null $rsKey
     *
     * @return bool
     */
    public static function deleteMongoDBData($cny, $rsKey=null)
    {
        if ( empty($rsKey) && ($key = self::getCompanyPrefsForMongoDBSharding($cny)) ) {
            /** @var string $key */
            $rsKey = $key;
        }

        if ( !empty($rsKey) ) {
            $mdbhm = MongoDBEnvManager::getInstance()->getHostsManager();
            $dbName = $mdbhm->getCompanyDBName($cny);
            return $mdbhm->getMDBClientByRSKey($rsKey)->dropDatabase($dbName);
        }

        return false;
    }
}


