<?
import('InventoryReporter');


class PurPriceVarianceReport extends InventoryReporter
{

    /**
     * @param array $_params
     */
    function __construct($_params)
    {
        parent::__construct(
            INTACCTarray_merge(
                $_params, array( 
                                'report' => 'purpricevariance', 
                                'ops'    => array(
                                            'po/lists/item/view',
                                            'po/lists/vendor/view'
                                            ),
                                '2stage'    => true,
                                'reportslide' => true,
                                ) 
            )
        );
    }


    /**
     * this function is overriden here only for the purpose of
     * at entity level if user doesnt give location we should see root as well as entities items
     * @param array  $params
     * @param string $loc
     *
     * @return string
     */
    function _getReportParamsLocationFilter(&$params, $loc)
    {
        return $loc;
    }


    /**
     * @return bool
     */
    function DoQuery() 
        {
        $ok = $this->ParamTweaks();
        if (!$ok) {
            return false;
        }

        $this->ValidateReport();
        $params                    = $this->params['safe'];
        $fromitemid                = $params['FROMITEMID'];
        $toitemid                = $params['TOITEMID'];
        $fromvendorid            = $params['FROMVENDORID'];
        $tovendorid                = $params['TOVENDORID'];
        $vendortype                = $params['VENDORTYPE'];
        
        // column selectors
        $ReportOn                = $this->params['REPORTON'];
        $fromupc                = $params['FROMUPC'];
        $toupc                    = $params['TOUPC'];
        $ItemOperator            = $this->params['ITEMOPERATOR'];
        $itemValues                = $this->params['ITEMVALUES'];
        $UPCOperator            = $this->params['UPCOPERATOR'];
        $upcValues                = $this->params['UPCVALUES'];
        
        $dt1                    = $params['dt1'];
        $dt2                    = $params['dt2'];
        
        // filter records based on ItemType
        $Inventory    = $this->params['INVENTORY'];
        $NonInv        = $this->params['NON_INV'];
        $NonInvPO    = $this->params['NON_INV_PO'];

        $cny = GetMyCompany();

        $filterlocation = $this->_getLocationClause($params, 'de');
        $filterdept = $this->_getDeptClause($params, 'de');

            $ipvitemupcargs = "";
            if ( $ReportOn == 'ITEMID') {
            if ( $fromitemid == VARCHAR2_MIN && $toitemid == VARCHAR2_MAX && $itemValues != '') {

                $this->GetFilterArgs($filterItemorUPC, $itemupcargs, $ItemOperator, 'it', 'itemid', $itemValues);
                $itemargtypes = array(  'text','date', 'date', 'text', 'text', 'integer');
                if( $itemupcargs !='') {
                    $ItemValue = $itemupcargs;

                    $args[] = $itemupcargs;
                    array_push($args, $dt1, $dt2, $fromvendorid, $tovendorid, $cny);
                    $itemupcargs = $args;

                    $ipvitemupcargs = $itemupcargs;
                    array_push($ipvitemupcargs, $ItemValue, $cny);
                }                
                $ipvitemargtypes = array( 'text','date', 'date', 'text', 'text', 'integer', 'text', 'integer');

            } else {
                $itemupcargs = array($fromitemid, $toitemid, $dt1, $dt2, $fromvendorid, $tovendorid, $cny);
                $filterItemorUPC = " it.itemid >= ? and it.itemid <= ? ";
                $itemargtypes = array( 'text', 'text','date', 'date', 'text', 'text', 'integer');

                $ipvitemupcargs = array($fromitemid, $toitemid, $dt1, $dt2, $fromvendorid, $tovendorid, $cny, $fromitemid, $toitemid,$cny);
                $ipvitemargtypes = array( 'text', 'text','date', 'date','text', 'text', 'integer','text', 'text', 'integer');
            }
        }else {
            if ( $fromupc == VARCHAR2_MIN && $toupc == VARCHAR2_MAX && $upcValues != '') {
                $this->GetFilterArgs($filterItemorUPC, $itemupcargs, $UPCOperator, 'it', 'upc', $upcValues);
                $itemargtypes = array( 'text','date', 'date','text', 'text', 'integer', 'integer', 'integer', 'integer');

                if( $itemupcargs !='') {
                    $ItemValue = $itemupcargs;

                    $args[] = $itemupcargs;
                    array_push($args, $dt1, $dt2, $fromvendorid, $tovendorid, $cny, $cny, $cny, $cny);
                    $itemupcargs = $args;

                    $ipvitemupcargs = $itemupcargs;
                    array_push($ipvitemupcargs, $ItemValue, $cny);
                }

                $ipvitemargtypes = array( 'text','date', 'date','text','text', 'integer','text', 'integer');
            } else {
                $itemupcargs = array($fromupc,$toupc, $dt1, $dt2, $fromvendorid, $tovendorid, $cny);
                $filterItemorUPC = " it.upc >= ? and it.upc <= ? ";
                $itemargtypes = array( 'text', 'text','date', 'date','text', 'text', 'integer');

                $ipvitemupcargs = array($fromupc,$toupc, $dt1, $dt2, $fromvendorid, $tovendorid, $cny, $fromupc,$toupc, $cny);
                $ipvitemargtypes = array( 'text', 'text','date', 'date','text','text', 'integer','text','text', 'integer');
            }
        }

        // filter records based on ItemType
        $itemtypesin = '';
        $itemtypesin.= $Inventory == 'true' ? "'I'," : '' ;
        $itemtypesin.= $NonInv == 'true' ? "'NI'," : '' ;
        $itemtypesin.= $NonInvPO == 'true' ? "'NP'," : '' ;
        $itemtypesin.= "''";
        $VendTypeQry = "";
        if ($vendortype != '') {
            $VendTypeQry = "and  vendtype.name = '".$vendortype."'";
        }
        
        if ($this->params['REPORTTYPE'] == 'PPV') {
            if($this->params['REPORTGROUPINGS']=='Vendor') {
                $order_by = " vendor.vendorid, it.itemid, ";
            } else {
                $order_by = " it.itemid, vendor.vendorid, ";
            }

            $itemcode = array(
                'QUERY'=> "SELECT  it.itemid, 
					it.upc,
					it.name itemname,
					it.standard_cost,
					dh.whencreated,
					dh.docid podocid,
					de.quantity quantity,
					(de.uiprice / uom.convfactor) unitprice,
                    (de.uiqty * de.uiprice)	ExtendedPrice,
					case 
                      when nvl(stdcost.standard_cost, 0) > 0 then
                        (de.uiqty * (de.uiprice - (stdcost.standard_cost * uom.convfactor)) )
                      else
                        (de.uiqty * (de.uiprice -( nvl(it.standard_cost,0) * uom.convfactor)))
                    end variance,
					stdcost.standard_cost whsestdcost,
                    icitemwhse.warehousekey,
					decode(it.status, 'T', 'Active', 'F', 'In Active', NULL) status,
					it.locationkey itownerloc,
					vendor.vendorid, vendor.name vendorname
				FROM dochdrmst dh
                    inner join docentry de on de.cny# = dh.cny# and de.dochdrkey = dh.record#
                    inner join docparmst dp on dp.cny# = dh.cny# and dp.record# = dh.docparkey and dp.updgl = 'G'
                    inner join vendormst vendor on vendor.cny# = dh.cny# and vendor.entity = dh.vendentity
                    left outer join vendtype on vendtype.cny# = vendor.cny# and vendtype.record# = vendor.vendtypekey
                    inner join icitem it on it.cny# = de.cny# and it.itemid = de.itemkey and it.producttype <> 'PROFSERVICE' 
                                and it.cost_method = 'S' and it.itemtype IN ($itemtypesin)
                    inner join docpartotals dpt on dpt.cny# = dp.cny# and dpt.docparkey = dp.record#
                    inner join ictotal on ictotal.cny# = dpt.cny# and ictotal.record# = dpt.totalkey 
                    left outer join icitemwhse on icitemwhse.cny# = de.cny# and icitemwhse.itemkey  = de.itemkey and icitemwhse.warehousekey = de.warehousekey
                    inner join icuom uom on uom.cny# = it.cny# and uom.grpkey = it.uomgrpkey and uom.unit = de.unit
                    left outer join icitemwhsestdcost stdcost on stdcost.cny# = icitemwhse.cny# and stdcost.itemwhsekey = icitemwhse.record# 
                    and stdcost.effective_start_date = (select max(effective_start_date) 
                                                        from icitemwhsestdcost itws 
                                                        where itws.cny# = icitemwhse.cny# and itws.itemwhsekey = icitemwhse.record# 
                                                        and itws.effective_start_date <= nvl(dh.whenposted, dh.whencreated))
                WHERE
                    $filterItemorUPC
                    $filterlocation 
                    $filterdept
                    $VendTypeQry
                    and dh.whencreated between to_date(?, 'mm/dd/yyyy') and to_date(?, 'mm/dd/yyyy')
                    and vendor.vendorid BETWEEN ? and ?
                    and dh.cny# = ?
                    ORDER BY {$order_by}  dh.whencreated, de.record#",
                'ARGTYPES' => $itemargtypes
            );
        } else {
            if($this->params['REPORTGROUPINGS']=='Vendor') {
                $order_by = " pi.vendorid, pi.itemid, ";
            } else {
                $order_by = " pi.itemid, pi.vendorid, ";
            }

            $itemcode = array(
                'QUERY' => "select pi.itemid, 
                    pi.itemname,
                    pi.standard_cost,
                    po.whencreated,
                    po.docid podocid,
                    po.quantity,
                    po.unitprice,
                    po.ExtendedPrice,
                    pi.whsestdcost whsestdcost,
                    pi.standard_cost pistandard_cost,
                    pi.docid pidocid,
                    pi.whencreated piwhencreated,
                    pi.quantity piquantity,
                    pi.unitprice piunitprice,
                    pi.ExtendedPrice piextendedprice,
                    case when po.unitprice is NOT NULL then
                      ((pi.unitprice - po.unitprice) * pi.quantity) 
                    else 
                        case when pi.whsestdcost is NOT NULL then
                          ((pi.unitprice - pi.whsestdcost) * pi.quantity)
                        else
                          ((pi.unitprice - nvl(pi.standard_cost, 0)) * pi.quantity)
                        end
                    end pivariance,
                    pi.vendorid, 
                    pi.vendorname
                  from                 
                  (SELECT distinct
                    it.itemid, 
                    it.upc,
                    it.name itemname,
                    it.standard_cost,
                    dh.whencreated,
                    dh.docid,
                    de.uiqty quantity,
                    de.uiprice unitprice,
                    de.uivalue ExtendedPrice,
                    decode(it.status, 'T', 'Active', 'F', 'In Active', NULL) status,
                    it.locationkey itownerloc,
          			stdcost.standard_cost whsestdcost,
                    stdcost.effective_start_date,
                    dh.createdfrom, 
                    dh.cny#, 
                    vendor.vendorid, 
                    vendor.name vendorname,
                    de.warehousekey, 
                    (SELECT record#
                          FROM docentry
                          WHERE cny# = de.cny#
                          AND EXISTS (
                                       SELECT 1
                                        FROM dochdrmst dochdr
                                        inner join docparmst docpar on docpar.cny# = dochdr.cny# and docpar.record# = dochdr.docparkey
                                        left outer join docpartotals on docpartotals.cny# = docpar.cny# and docpartotals.docparkey = docpar.record# 
                                        left outer join ictotal on ictotal.cny# = docpartotals.cny# and ictotal.record# = docpartotals.totalkey
                                        WHERE 
                                        dochdr.cny# = docentry.cny# AND docentry.dochdrkey = dochdr.record#
                                        AND ictotal.NAME = 'ONORDER')
                         START WITH cny# = de.cny# AND record# = de.record#
                         CONNECT BY cny# = de.cny#
                                AND record# = PRIOR source_doclinekey
                                AND rownum=1) parent_rec
                    FROM docentry de
                    inner join dochdrmst dh on dh.cny# = de.cny# and dh.record# = de.dochdrkey
                    inner join docparmst dp on dp.cny# = dh.cny# and dp.record# = dh.docparkey and dp.updgl = 'A'
                    inner join icitemmst it on it.cny# = de.cny# and it.itemid = de.itemkey and it.producttype <> 'PROFSERVICE' 
                                and it.cost_method = 'S' and it.itemtype IN ($itemtypesin)
                    inner join vendormst vendor on vendor.cny# = dh.cny# and vendor.entity = dh.vendentity
                    left outer join vendtype on vendtype.cny# = vendor.cny# and vendtype.record# = vendor.vendtypekey
                    left outer join icitemwhse on icitemwhse.cny# = de.cny# and icitemwhse.itemkey  = de.itemkey and icitemwhse.warehousekey = de.warehousekey
                    left outer join icitemwhsestdcost stdcost on stdcost.cny# = icitemwhse.cny# and stdcost.itemwhsekey = icitemwhse.record# 
                                    and stdcost.effective_start_date = (select max(effective_start_date) 
                                                                        from icitemwhsestdcost itws 
                                                                        where itws.cny# = icitemwhse.cny# and itws.itemwhsekey = icitemwhse.record# 
                                                                        and itws.effective_start_date <= nvl(dh.whenposted, dh.whencreated))
                WHERE   
                    $filterItemorUPC
					$filterlocation 
					$filterdept
					$VendTypeQry
					and dh.whencreated between to_date(?, 'mm/dd/yyyy') and to_date(?, 'mm/dd/yyyy')
					and vendor.vendorid BETWEEN ? and ?
                    and de.cny# = ?
                    ) PI,
                (SELECT  distinct
                    it.itemid, 
                    it.upc,
                    dh.whencreated,
                    dh.docid,
                    dh.record#, 
                    dh.cny#,
                    de.record# podecrec,
                    de.uiqty quantity,
                    de.uiprice unitprice,
                    de.uivalue ExtendedPrice,
                    de.warehousekey
                FROM docentry de
                inner join dochdrmst dh on dh.cny# = de.cny# and dh.record# = de.dochdrkey
                inner join docparmst dp on dp.cny# = dh.cny# and dp.record# = dh.docparkey
                inner join docpartotals dpt on dpt.cny# = dp.cny# and dpt.docparkey = dp.record# 
                inner join ictotal on ictotal.cny# = dpt.cny# and ictotal.record# = dpt.totalkey and ictotal.name = 'ONORDER'
                inner join icitemmst it on it.cny# = de.cny# and it.itemid = de.itemkey 
                          and it.producttype <> 'PROFSERVICE' and it.cost_method = 'S' and  it.itemtype IN ($itemtypesin)
                WHERE 
                $filterItemorUPC
				$filterlocation 
				$filterdept
                and de.cny# = ?
                ) PO
				where po.cny#(+) = pi.cny#
				AND po.podecrec(+) = pi.parent_rec
				and po.itemid(+) = pi.itemid
				ORDER BY {$order_by}  po.whencreated, po.record#",
                'ARGTYPES' => $ipvitemargtypes
            );

            $itemupcargs = $ipvitemupcargs;
        }

        // moved the query into here because the one in qry file is putting an or between the item and product line ids
        $items = $this->_QM->DoCustomQuery($itemcode, $itemupcargs, true);
        $this->_items = $items;

        return true;
    }

    /**
     * @return string[][]
     */
    function DoMap()
    {
        $itemsMap = array();
        $trans = array();

        $grp_1 = "";
        $grp_2 = "";
        $piqty = 0;
        if (!isset($this->params['REPORTGROUPINGS'])) {
            $this->params['REPORTGROUPINGS'] = 'Item';
        }
        if ($this->params['REPORTGROUPINGS'] == 'Vendor' ) {
            $grp_1 = 'VENDORID';
            $grp_2 = 'ITEMID';
        }elseif ($this->params['REPORTGROUPINGS'] == 'Item' ) {
            $grp_1 = 'ITEMID';
            $grp_2 = 'VENDORID';
        }

        if ($this->params['REPORTGROUPINGS'] == 'Vendor' || $this->params['REPORTGROUPINGS'] == 'Item') {
            $PreItemid = '';
            foreach($this->_items as $row){
                $qty = 0;
                $extprice = 0;
                $variance = 0;
                /** @noinspection PhpUndefinedVariableInspection */
                if( $PreItemid != $row[$grp_2] || $Pregrpid != $row[$grp_1]) {
                    $trans = array();

                    //totals for purchase invoice
                    $piqty = 0;
                    $piextprice = 0;

                    //totals for variance
                    $varqty = 0;
                    $varextentedprice = 0;
                }

                if ($this->params['REPORTGROUPINGS'] != 'Vendor') {
                    $itemID = urlencode(addslashes($row[$grp_2]));
                    $row['ITEM'] = $row[$grp_2] ;
                    $row['ITEMHREF']         = "javascript:drilldown(1,'$itemID', '$row[ITOWNERLOC]');";
                    $row['NAME']         = $row['VENDORNAME'] ;
                }else{
                    $itemID = urlencode(addslashes($row['ITEMID']));
                    $row['ITEM'] = $this->params['REPORTON'] == 'ITEMID' ? $row['ITEMID'] : $row['UPC'];
                    $row['ITEMHREF']         = "javascript:drilldown(0,'$itemID', '$row[ITOWNERLOC]');";
                    $row['NAME']         = $row['ITEMNAME'] ;
                }

                /** @noinspection PhpUndefinedVariableInspection */
                if ( $Pregrpid != $row[$grp_1]) {
                    $itemsMap[$row[$grp_1]]['GRPID'] = $row[$grp_1];
                    if ($this->params['REPORTGROUPINGS'] != 'Vendor') {
                        $itemsMap[$row[$grp_1]]['GRPNAME'] = $row['ITEMNAME'];
                    }else{
                        $itemsMap[$row[$grp_1]]['GRPNAME'] = $row['VENDORNAME'];
                    }
                }

                $itemsMap[$row[$grp_1]]["'".$row[$grp_2]."'"]['ITEMREC'][0] = array('ITEMID' => $itemID, 
                                                     'ITEM' => $row['ITEM'], 
                                                     'ITEMHREF' => $row['ITEMHREF'], 
                                                     'ITEMNAME' => $row['NAME'], 
                                                     'STANDARD_COST' => ibcmul($row['STANDARD_COST'], '1', 2));

                if ($this->params['REPORTTYPE'] == 'PPV') {
                    $trans[] = array('PODOCID' => $row['PODOCID'],
                     'POWHENCREATED' => $row['WHENCREATED'],
                     'POQUANTITY' => ibcmul($row['QUANTITY'], '1', 2),
                     'POUNITPRICE' => ibcmul($row['UNITPRICE'], '1', 2),
                     'POEXTENDEDPRICE' =>    ibcmul($row['EXTENDEDPRICE'], '1', 2),
                     'POVARIANCE' => ibcmul(iround($row['VARIANCE'], 2), '1', 2),
                     'WHSESTDCOST' => ibcmul($row['WHSESTDCOST'], '1', 2));

                }elseif ($this->params['REPORTTYPE'] == 'IPV') {    
                    $row['EXTENDEDPRICE'] = ibcmul($row['PIQUANTITY'], $row['UNITPRICE'], 2);
                    $trans[] = array('PODOCID' => $row['PODOCID'],
                     'POWHENCREATED' => $row['WHENCREATED'],
                     'POQUANTITY' => (isset($row['QUANTITY']) ? ibcmul($row['QUANTITY'], '1', 2) : ''),
                     'POUNITPRICE' => (isset($row['UNITPRICE']) ? ibcmul($row['UNITPRICE'], '1', 2) : ''),
                     'POEXTENDEDPRICE' => (isset($row['EXTENDEDPRICE']) ? ibcmul($row['EXTENDEDPRICE'], '1', 2) : ''),
                     'PIDOCID' => $row['PIDOCID'],
                     'PIWHENCREATED' => $row['PIWHENCREATED'],
                     'PIQUANTITY' => ibcmul($row['PIQUANTITY'], '1', 2),
                     'PIUNITPRICE' => ibcmul($row['PIUNITPRICE'], '1', 2),
                     'PIEXTENDEDPRICE' =>    ibcmul($row['PIEXTENDEDPRICE'], '1', 2),
                     'VARUNITPRICE' => ibcsub($row['PIUNITPRICE'], $row['UNITPRICE'], 2),
                     'VARQTY' => (isset($row['QUANTITY']) ? ibcsub($row['PIQUANTITY'], $row['QUANTITY'], 2) : 0),
                     'VAREXTENTEDPRICE' => ibcmul($row['PIVARIANCE'], '1', 2),
                     'WHSESTDCOST' => ibcmul($row['WHSESTDCOST'], '1', 2));
                }
                $PreItemid = $row[$grp_2];

                $Pregrpid = $row[$grp_1];
                
                $itemsMap[$row[$grp_1]]["'".$row[$grp_2]."'"]['ITEMREC'][0]['DETAILS'] = $trans;

                //sub totals
                //QTY
                if(isset($row['QUANTITY'])) {
                    $qty = ibcadd($qty, $row['QUANTITY'], 2);
                }
                $itemsMap[$row[$grp_1]]["'" . $row[$grp_2] . "'"]['ITEMREC'][0]['TOTALS'][0]['POQUANTITY'] = $qty;

                //extended price
                $extprice = ibcadd($extprice, $row['EXTENDEDPRICE'], 2);
                $itemsMap[$row[$grp_1]]["'".$row[$grp_2]."'"]['ITEMREC'][0]['TOTALS'][0]['POEXTENDEDPRICE'] = $extprice;

                //average unit price
                $unitprice = ibcdiv($extprice, $qty, 2);
                $itemsMap[$row[$grp_1]]["'".$row[$grp_2]."'"]['ITEMREC'][0]['TOTALS'][0]['POUNITPRICE'] = $unitprice;
                
                if ($this->params['REPORTTYPE'] == 'PPV') {
                    //variance
                    $variance = ibcadd($variance, iround($row['VARIANCE'], 2), 2);
                    $itemsMap[$row[$grp_1]]["'".$row[$grp_2]."'"]['ITEMREC'][0]['TOTALS'][0]['POVARIANCE'] = $variance;
                }elseif ($this->params['REPORTTYPE'] == 'IPV') {    
                    
                    //totals for purchase invoice
                    $piqty = ibcadd($piqty, $row['PIQUANTITY'], 2);
                    $itemsMap[$row[$grp_1]]["'".$row[$grp_2]."'"]['ITEMREC'][0]['TOTALS'][0]['PIQUANTITY'] = $piqty;

                    /** @noinspection PhpUndefinedVariableInspection */
                    $piextprice = ibcadd($piextprice, $row['PIEXTENDEDPRICE'], 2);
                    $itemsMap[$row[$grp_1]]["'".$row[$grp_2]."'"]['ITEMREC'][0]['TOTALS'][0]['PIEXTENDEDPRICE'] = $piextprice;

                    //pi average unit price
                    $piunitprice = ibcdiv($piextprice, $piqty, 2);
                    $itemsMap[$row[$grp_1]]["'".$row[$grp_2]."'"]['ITEMREC'][0]['PIUNITPRICE'] = $piunitprice;

                    //totals for variance
                    /** @noinspection PhpUndefinedVariableInspection */
                    $varqty = ibcadd($varqty, ibcsub($row['PIQUANTITY'], $row['QUANTITY'], 2), 2);
                    $itemsMap[$row[$grp_1]]["'".$row[$grp_2]."'"]['ITEMREC'][0]['TOTALS'][0]['VARQTY'] = $varqty;
                    /** @noinspection PhpUndefinedVariableInspection */
                    $varextentedprice = ibcadd($varextentedprice, ibcmul($row['PIVARIANCE'], '1', 2), 2);
                    $itemsMap[$row[$grp_1]]["'".$row[$grp_2]."'"]['ITEMREC'][0]['TOTALS'][0]['VAREXTENTEDPRICE'] = $varextentedprice;
                }

                //grand totals 
                /** @noinspection PhpUndefinedVariableInspection */
                $gt_qty = ibcadd($gt_qty, $row['QUANTITY'], 2);
                /** @noinspection PhpUndefinedVariableInspection */
                $gt_extprice = ibcadd($gt_extprice, $row['EXTENDEDPRICE'], 2);
                /** @noinspection PhpUndefinedVariableInspection */
                $gt_variance = ibcadd($gt_variance, iround($row['VARIANCE'], 2), 2);

                /** @noinspection PhpUndefinedVariableInspection */
                $gt_piqty = ibcadd($gt_piqty, $row['PIQUANTITY'], 2);
                /** @noinspection PhpUndefinedVariableInspection */
                $gt_piextprice = ibcadd($gt_piextprice, $row['PIEXTENDEDPRICE'], 2);
                /** @noinspection PhpUndefinedVariableInspection */
                $gt_varqty = ibcadd($gt_varqty, ibcsub($row['PIQUANTITY'], $row['QUANTITY'], 2), 2);
                /** @noinspection PhpUndefinedVariableInspection */
                $gt_varextentedprice = ibcadd($gt_varextentedprice, ibcmul($row['PIVARIANCE'], '1', 2), 2);


            }
        
            // adding grand totals
            if (count($itemsMap) >0) {
                /** @noinspection PhpUndefinedVariableInspection */
                $itemsMap[0]['GRANDTOTALS'][0]['POQUANTITY'] = $gt_qty;
                /** @noinspection PhpUndefinedVariableInspection */
                $itemsMap[0]['GRANDTOTALS'][0]['POEXTENDEDPRICE'] = $gt_extprice;
                $itemsMap[0]['GRANDTOTALS'][0]['POUNITPRICE'] = ibcdiv($gt_extprice, $gt_qty, 2);
                /** @noinspection PhpUndefinedVariableInspection */
                $itemsMap[0]['GRANDTOTALS'][0]['POVARIANCE'] = $gt_variance;
                /** @noinspection PhpUndefinedVariableInspection */
                $itemsMap[0]['GRANDTOTALS'][0]['PIQUANTITY'] = $gt_piqty;
                /** @noinspection PhpUndefinedVariableInspection */
                $itemsMap[0]['GRANDTOTALS'][0]['PIEXTENDEDPRICE'] = $gt_piextprice;
                $itemsMap[0]['GRANDTOTALS'][0]['PIUNITPRICE'] = ibcdiv($gt_piextprice, $gt_piqty, 2);
                /** @noinspection PhpUndefinedVariableInspection */
                $itemsMap[0]['GRANDTOTALS'][0]['VARQTY'] = $gt_varqty;
                /** @noinspection PhpUndefinedVariableInspection */
                $itemsMap[0]['GRANDTOTALS'][0]['VAREXTENTEDPRICE'] = $gt_varextentedprice;
            }
        }elseif($this->params['REPORTGROUPINGS'] == 'None') {
            foreach($this->_items as $row){

                $itemID = urlencode(addslashes($row['ITEMID']));
                $row['ITEM'] = $this->params['REPORTON'] == 'ITEMID' ? $row['ITEMID'] : $row['UPC'];
                $row['ITEMHREF'] = "javascript:drilldown(0,'$itemID', '$row[ITOWNERLOC]');";
                $row['NAME']     = $row['ITEMNAME'] ;

                if ($this->params['REPORTTYPE'] == 'PPV') {
                    $trans[] = array('ITEMID' => $itemID, 
                     'ITEM' => $row['ITEM'],
                     'ITEMHREF' => $row['ITEMHREF'], 
                     'ITEMNAME' => $row['NAME'], 
                     'VENDORID' => $row['VENDORID'], 
                     'VENDORNAME' => $row['VENDORNAME'], 
                     'STANDARD_COST' => ibcmul($row['STANDARD_COST'], '1', 2),
                     'PODOCID' => $row['PODOCID'],
                     'POWHENCREATED' => $row['WHENCREATED'],
                     'POQUANTITY' => ibcmul($row['QUANTITY'], '1', 2),
                     'POUNITPRICE' => ibcmul($row['UNITPRICE'], '1', 2),
                     'POEXTENDEDPRICE' =>    ibcmul($row['EXTENDEDPRICE'], '1', 2),
                     'POVARIANCE' => ibcmul($row['VARIANCE'], '1', 2),
                     'WHSESTDCOST' => ibcmul($row['WHSESTDCOST'], '1', 2));
                }elseif ($this->params['REPORTTYPE'] == 'IPV') {                
                    $trans[] = array('ITEMID' => $itemID, 
                     'ITEM' => $row['ITEM'],
                     'ITEMHREF' => $row['ITEMHREF'], 
                     'ITEMNAME' => $row['NAME'], 
                     'VENDORID' => $row['VENDORID'], 
                     'VENDORNAME' => $row['VENDORNAME'], 
                     'STANDARD_COST' => ibcmul($row['STANDARD_COST'], '1', 2),
                     'PODOCID' => $row['PODOCID'],
                     'POWHENCREATED' => $row['WHENCREATED'],
                     'POQUANTITY' => ibcmul($row['QUANTITY'], '1', 2),
                     'POUNITPRICE' => ibcmul($row['UNITPRICE'], '1', 2),
                     'POEXTENDEDPRICE' =>    ibcmul($row['EXTENDEDPRICE'], '1', 2),
                     'PIDOCID' => $row['PIDOCID'],
                     'PIWHENCREATED' => $row['PIWHENCREATED'],
                     'PIQUANTITY' => ibcmul($row['PIQUANTITY'], '1', 2),
                     'PIUNITPRICE' => ibcmul($row['PIUNITPRICE'], '1', 2),
                     'PIEXTENDEDPRICE' =>    ibcmul($row['PIEXTENDEDPRICE'], '1', 2),
                     'VARUNITPRICE' => ibcsub($row['PIUNITPRICE'], $row['UNITPRICE'], 2),
                     'VARQTY' => ibcsub($row['PIQUANTITY'], $row['QUANTITY'], 2),
                     'VAREXTENTEDPRICE' => ibcmul($row['PIVARIANCE'], '1', 2),
                     'WHSESTDCOST' => ibcmul($row['WHSESTDCOST'], '1', 2),
                                );
                }

                $itemsMap[0]['ITEMREC'][0]['DETAILS'] = $trans;

                //sub totals
                //QTY
                /** @noinspection PhpUndefinedVariableInspection */
                $qty = ibcadd($qty, $row['QUANTITY'], 2);
                $itemsMap[0]['ITEMREC'][0]['TOTALS'][0]['POQUANTITY'] = $qty;

                //extended price
                /** @noinspection PhpUndefinedVariableInspection */
                $extprice = ibcadd($extprice, $row['EXTENDEDPRICE'], 2);
                $itemsMap[0]['ITEMREC'][0]['TOTALS'][0]['POEXTENDEDPRICE'] = $extprice;

                //average unit price
                $unitprice = ibcdiv($extprice, $qty, 2);
                $itemsMap[0]['ITEMREC'][0]['TOTALS'][0]['POUNITPRICE'] = $unitprice;
                
                if ($this->params['REPORTTYPE'] == 'PPV') {
                    //variance
                    /** @noinspection PhpUndefinedVariableInspection */
                    $variance = ibcadd($variance, $row['VARIANCE'], 2);
                    $itemsMap[0]['ITEMREC'][0]['TOTALS'][0]['POVARIANCE'] = $variance;
                }elseif ($this->params['REPORTTYPE'] == 'IPV') {

                    //totals for purchase invoice
                    /** @noinspection PhpUndefinedVariableInspection */
                    $piqty = ibcadd($piqty, $row['PIQUANTITY'], 2);
                    $itemsMap[0]['ITEMREC'][0]['TOTALS'][0]['PIQUANTITY'] = $piqty;

                    /** @noinspection PhpUndefinedVariableInspection */
                    $piextprice = ibcadd($piextprice, $row['PIEXTENDEDPRICE'], 2);
                    $itemsMap[0]['ITEMREC'][0]['TOTALS'][0]['PIEXTENDEDPRICE'] = $piextprice;

                    //pi average unit price
                    $piunitprice = ibcdiv($piextprice, $piqty, 2);
                    $itemsMap[0]['ITEMREC'][0]['TOTALS'][0]['PIUNITPRICE'] = $piunitprice;

                    //totals for variance
                    /** @noinspection PhpUndefinedVariableInspection */
                    $varqty = ibcadd($varqty, ibcsub($row['PIQUANTITY'], $row['QUANTITY'], 2), 2);
                    $itemsMap[0]['ITEMREC'][0]['TOTALS'][0]['VARQTY'] = $varqty;
                    /** @noinspection PhpUndefinedVariableInspection */
                    $varextentedprice = ibcadd($varextentedprice, ibcmul($row['PIVARIANCE'], '1', 2), 2);
                    $itemsMap[0]['ITEMREC'][0]['TOTALS'][0]['VAREXTENTEDPRICE'] = $varextentedprice;
                }
            }
        }


        $itemsMap = array_values($itemsMap);

        if (count($itemsMap)==0) {
            $itemsMap['NODATA'][0]= array('NODATA'=>'1');
        }

        $lines = $this->InvCommonHeader();
        $lines['report'][0]['reporttype'] = $this->params['REPORTTYPE'] == 'PPV' ? 'PPV' : 'IPV';
        if ($this->params['REPORTGROUPINGS'] == 'Vendor' ) {
            $lines['report'][0]['reportgroupings'] = 'Vendor';
        }elseif ($this->params['REPORTGROUPINGS'] == 'Item' ) {
            $lines['report'][0]['reportgroupings'] = 'Item';
        }elseif ($this->params['REPORTGROUPINGS'] == 'None' ) {
            $lines['report'][0]['reportgroupings'] = 'None';
        }        
        $lines = $this->InvCommonBody($lines, $itemsMap); 
        $lines['report'][0]['term_Item'] = $this->params['REPORTON'] == 'UPC' ? 'UPC' : $lines['report'][0]['term_Item'];

        return $lines;
    }

    /**
     * @return bool
     */
    function ParamTweaks()
    {        
        global $gErr;
        
        parent::ParamTweaks(); 

        $params = $this->params;
        $p['VENDORTYPE']        = $params['VENDORTYPE'] ? isl_htmlspecialchars($params['VENDORTYPE']) : '';
        //$p['FROMVENDORID']			= $params['FROMVENDORID'] ? isl_trim(isl_htmlspecialchars($params['FROMVENDORID'])) : '';	
        //$p['TOVENDORID']			= $params['TOVENDORID'] ? isl_trim(isl_htmlspecialchars($params['TOVENDORID'])) : '';	

        if ($params['REPORTGROUPINGS'] == 'Vendor' ) {
            $p['REPORTGROUPINGS'] = 'Vendor';
        }elseif ($params['REPORTGROUPINGS'] == 'Item' ) {
            $p['REPORTGROUPINGS'] = 'Item';
        }elseif ($params['REPORTGROUPINGS'] == 'None' ) {
            $p['REPORTGROUPINGS'] = 'None';
        }else{
            $p['REPORTGROUPINGS'] = 'Item';
        }

        if (HasErrors()) { 
            if($gErr->ErrorCount) {
                return false;
            }
        }

        // remember our tweaked values
        $params['safe'] = INTACCTarray_merge($this->params['safe'], $p);
        $this->params = $params;

        return true;
    }

}


