<?
import('DocumentParamsManager');

class PODocumentParamsManager extends DocumentParamsManager
{
    const LEAVE_TRANSACTION_OPEN = "Leave Transaction Open";
    const CLOSE_ORIGINAL_AND_CREATE_BACKORDER = "Close original and create back order";
    const CLOSE_TRANSACTION = "Close Transaction";
    const NEVER = "Never";
    const ALWAYS = "Always";
    const ALL = "All";
    const BEFORE_PRINTING = "Before Printing";
    const NOEDIT = "No Edit";

    /**
     * @param array $params
     */
    public function __construct($params = array())
    {
        global $kPOid;
        $params['_mod'] = 'po';
        $params['_modID'] = $kPOid;
        $params['docclass'] = 'P';
        parent::__construct($params);
    }


    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $ok = true;
        $values['SALE_PUR_TRANS'] = 'Purchase';
        $values['CUST_VEND'] = 'Vendor';

        $ok = $ok && $this->convertingFromPrimaryDocTD($values);
        $ok = $ok && parent::validateTaxCapture($values);
        if($this->isChangeOrderSubtotalsEnabled()){
            $ok = $ok && $this->validateCCOSubtotals($values);
        }

        $ok = $ok && DocumentParamsManager::regularAdd($values);
        return $ok;

    }

    /**
     * @param array $values
     *
     * @return bool
     */
    public function translateContactTitles(&$values)
    {
        if ($values['CONTACTTITLE1'] == 'Pay to') {
            $values['CONTACTTITLE1'] = I18N::getSingleToken('IA.PAY_TO');
        }

        if ($values['CONTACTTITLE2'] == 'Return to') {
            $values['CONTACTTITLE2'] = I18N::getSingleToken('IA.RETURN_TO');
        }

        if ($values['CONTACTTITLE3'] == 'Deliver to') {
            $values['CONTACTTITLE3'] = I18N::getSingleToken('IA.DELIVER_TO');
        }

        return true;
    }

    /**
     * TranslateValues
     *
     * @param array &$values new values
     *
     * @return bool
     */
    protected function TranslateValues(&$values)
    {
        /**
         * Legacy code expect the ID fields in their respective name field in the backend.
         * So copy GLACCTID, GLOFFSETACCTID, GLACCTNUMBER etc fields to their respective NAME field .
         * */
        foreach ( $values['DOCPAR_SUBTOTAL'] as &$docParSubtotal ) {
            if ( isset($docParSubtotal['GLACCTID']) && $docParSubtotal['GLACCTID'] !== '' ) {
                $docParSubtotal['GLACCOUNT'] = $docParSubtotal['GLACCTID'];
            }
            if ( isset($docParSubtotal['GLOFFSETACCTID']) && $docParSubtotal['GLOFFSETACCTID'] !== '' ) {
                $docParSubtotal['GLOFFSETACCOUNT'] = $docParSubtotal['GLOFFSETACCTID'];
            }
        }
        foreach ( $values['DOCPAR_INVGL'] as &$docParInvGL ) {
            if ( isset($docParInvGL['GLACCTNUMBER']) && $docParInvGL['GLACCTNUMBER'] !== '' ) {
                $docParInvGL['GLACCOUNT'] = $docParInvGL['GLACCTNUMBER'];
            }
        }
        foreach ( $values['DOCPAR_PRGL'] as &$docParPRGL ) {
            if ( isset($docParPRGL['GLACCTNUMBER']) && $docParPRGL['GLACCTNUMBER'] !== '' ) {
                $docParPRGL['GLACCOUNT'] = $docParPRGL['GLACCTNUMBER'];
            }
        }
        foreach ( $values['DOCPAR_ENTITY_PROPS'] as &$docParEntity ) {
            if ( isset($docParEntity['ENTITY_LOCATION_NO']) && $docParEntity['ENTITY_LOCATION_NO'] !== '' ) {
                $docParEntity['ENTITY_NAME'] = $docParEntity['ENTITY_LOCATION_NO'];
            }
        }

        return parent::TranslateValues($values); 
    }

    /**
     * This function is the opposite of API_FormatObject, and allows the subclass to 'untransform' the outbound values
     * in a suitable PHP structure.  The API integration point is responsible
     * for formatting values -- Objects are responsible for formatting structures
     *
     * @param array $values Outbound object structure
     *
     * @return array unformatted structure
     */
    public function API_UnformatObject($values)
    {
        $values = parent::API_UnformatObject($values);

        /**
         * Legacy code expect GLACCOUNT field. So should remove the new GLACCTNUMBER field for the Update API to work.
         * */
        foreach ( $values as &$nextObject ) {
            foreach ( $nextObject['AR_ACCOUNTS']['AR_ACCOUNT'] as &$arAccount ) {
                unset($arAccount['GLACCTNUMBER']);
            }
            foreach ( $nextObject['INVGL_ACCOUNTS']['INVGL_ACCOUNT'] as &$invGLAccount ) {
                unset($invGLAccount['GLACCTNUMBER']);
            }
            foreach ( $nextObject['ADDGL_ACCOUNTS']['ADDGL_ACCOUNT'] as &$addlGLAccount ) {
                unset($addlGLAccount['GLACCTNUMBER']);
            }
        }

        return $values;
    }


    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $ok = true;
        $values['SALE_PUR_TRANS'] = 'Purchase';
        $values['CUST_VEND'] = 'Vendor';

        $ok = $ok && parent::validateTaxCapture($values);

        // Validate if PO Automation configured
        $ok = $ok && $this->validatePOAutomationParams($values);

        $setPrimaryKeys = false;
        $docsToCheck = [];
        // Need to check and preserve if the primary doc option was changed before the parent:regularSet()
        $primaryDocOptionChanged = $this->primaryDocOptionChanged($values, $setPrimaryKeys);
        $ok = $ok && $this->validatePrimaryDoc($values, $setPrimaryKeys, $docsToCheck);

        if ($primaryDocOptionChanged) {
            $ok = $ok && $this->validatePrimaryDocCompliance($values, $setPrimaryKeys);
        }

        $isSubtotalsInChangeOrderEnabled = false;
        if($this->isChangeOrderSubtotalsEnabled()){
            if($this->isChangeOrderSubtotalsEnabled() && !empty($values['ENABLECHANGEORDER']) && $values['ENABLECHANGEORDER'] == 'true' && $values['DOC_TYPE'] == 'Source Document' ){
                $isSubtotalsInChangeOrderEnabled = true;
                //check if any purchasing transaction is using the same transaction definition.
                $ok = $ok && $this->documentParamsHasTransaction($values['DOCID'], $values['SHOW_TOTALS']);
            }

            $ok = $ok && $this->validateCCOSubtotals($values);
        }

        $ok = $ok && DocumentParamsManager::regularSet($values);

        if ($primaryDocOptionChanged) {
            $ok = $ok && $this->updatePrimaryDocKeys($values, $setPrimaryKeys, $docsToCheck);
        }

        if($isSubtotalsInChangeOrderEnabled){
            $changeDocumentForSubtotals = $this->changeDocumentWithSameSubtotals($values['DOCID']);
            if(!empty($changeDocumentForSubtotals)){
                $ok = $ok && $this->syncRelatedChangeOrderDocParams($changeDocumentForSubtotals, $values);
            }
        }
        
        return $ok;

    }

    /**
     * Checks if the primary doc option was changed
     * @param array $values
     * @param bool $setPrimaryKeys
     * @return bool
     */

    protected function primaryDocOptionChanged(array $values, ?bool &$setPrimaryKeys = false) {

        $oldvalues = $this->get($values['RECORDNO']);

        if ($values['PRIMARYDOC'] !== $oldvalues['PRIMARYDOC']) {
            if ($values['PRIMARYDOC'] === "true") {
                $setPrimaryKeys = true;
            }
            return true;
        }
        return false;
    }

    /**
     * Primary doc TD validations
     * @param array $values
     * @param bool $setPrimaryKeys
     * @param array $docsToCheck
     *
     * @return bool
     */
    protected function validatePrimaryDoc(array $values, bool $setPrimaryKeys, array &$docsToCheck)
    {
        if (CRESetupManager::isPrimaryDocEnabled()) {

            if (!$this->convertingFromPrimaryDocTD($values)) {
                return false;
            }

            $docsToCheck = $this->getDocsToCheck($values['DOCID'], GetMyCompany());

            $disableDocHierarchyValidation = FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('DISABLE_PRIMARYDOC_HIERARCHY_VALIDATION');

            // If we have a downstream TD that has the primary doc feature enabled then prevent the user from enabling primary doc on the current TD
            if (!$disableDocHierarchyValidation && $setPrimaryKeys && !$this->isDownstreamTDPrimaryDocEnabled($docsToCheck, $values['DOCID'])) {
                return false;
            }

        }

        return true;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function convertingFromPrimaryDocTD(array $values)
    {
        $convertedFromTDs = $values['DOCPAR_RECALLS'];

        if (!empty($convertedFromTDs) && is_array($convertedFromTDs)) {
            foreach ($convertedFromTDs as $txnDef) {
                if (empty($txnDef['RECDOCPAR'])) {
                    continue;
                }
                if ($values['PRIMARYDOC'] == "true" && $this->isPrimaryDocEnabled($txnDef['RECDOCPAR'])) {
                    global $gErr;
                    $gErr->addIAError(
                        'PO-0065', __FILE__ . ':' . __LINE__,'',[],
                        "You cannot convert from a TD which has the primary doc feature enabled. "
                    );
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * @param array $values
     * @param bool $setPrimaryKeys
     * @param array $docsToCheck
     *
     * @return bool
     */
    protected function updatePrimaryDocKeys(array $values, bool $setPrimaryKeys, array $docsToCheck)
    {
        if (CRESetupManager::isPrimaryDocToolEnabled()) {
            $jobParams = [];
            $jobParams['DOCID'] = $values['DOCID'];
            $jobParams['ENABLEDOCCHANGE'] = $values['ENABLEDOCCHANGE'];
            $jobParams['PRIMARYDOC'] = $setPrimaryKeys;

            $jobArray = RunObjectHandler::getQueuedRunSummaryForKVPairEqual("PrimaryDocToolJobInProgress", "true",  GetMyCompany());

            if (count($jobArray) == 0) {
                $this->getPORun($jobParams, $docsToCheck);
            } else {
                global $gErr;
                $gErr->addIAError('PO-0066', __FILE__ . ':' . __LINE__,'',[],
                    'Please wait until the primary document workflow completes for another transaction definition and you receive the completion email.'
                );
                return false;
            }
        }

        return true;
    }

    /**
     * @param array $values
     * @param bool $setPrimaryKeys
     * @return bool
     */
    private function validatePrimaryDocCompliance($values, $setPrimaryKeys)
    {
        if (!CRESetupManager::isCREInstalled() || $setPrimaryKeys || $values['PRIMARYDOC'] == 'true') {
            return true;
        }
        
        // Validate when primaryDocOption has changed, and when it has been changed to false.
        // Make sure no ComplianceDefinitions are setup with this transaction definition
        $compliancedefassociationsManager = Globals::$g->gManagerFactory->getManager('compliancedefassociations');
        $params = [
            'selects' => ['COMPLIANCEDEFINITIONKEY'],
            'filters' => [[['DOCPARID', '=', $values['DOCID']]]]
        ];
        $result = $compliancedefassociationsManager->GetList($params);
        
        if (!isEmptyArray($result)) {
            // We found at least one compliance definition referencing this transaction definition.
            $complianceDefKeys = [];
            foreach ($result as $value) {
                $complianceDefKeys[] = $value['COMPLIANCEDEFINITIONKEY'];
            }
            
            $complianceDefinitionManager = Globals::$g->gManagerFactory->getManager('compliancedefinition');
            $params = [
                'selects' => ['COMPLIANCEDEFINITIONID'],
                'filters' => [[['RECORDNO', 'IN', $complianceDefKeys]]]
            ];
            $complianceDefinitions = $complianceDefinitionManager->GetList($params);
            
            if (is_array($complianceDefinitions)) {
                $complianceDefList = '';
                foreach ($complianceDefinitions as $aComplianceDefinition) {
                    if ($complianceDefList != '') {
                        $complianceDefList .= ', ';
                    }
                    $complianceDefList .= $aComplianceDefinition['COMPLIANCEDEFINITIONID'];
                }

                $msg = 
                    "This transaction definition can not be changed to disable primary document workflow because it is currently used in one or more Compliance Definitions. " .
                    $complianceDefList . ".";
                Globals::$g->gErr->addIAError('CRE-3212', __FILE__ . ':' . __LINE__, $msg, ['COMPLIANCEDEFLIST' => $complianceDefList]);
            }
            
            return false;
        }

        // Make sure no ComplianceRecords are setup with this transaction definition.
        $compliancerecordManager = Globals::$g->gManagerFactory->getManager('compliancerecord');
        $params = [
            'selects' => ['RECORDNO'],
            'filters' => [[['DOCPARID', '=', $values['DOCID']]]]
        ];
        $result = $compliancerecordManager->GetList($params);

        if (is_countable($result) && count($result) > 0) {
            $msg =
                "This transaction definition can not be changed to disable primary document workflow because it is currently used in one or more Compliance Records.";
            Globals::$g->gErr->addIAError('CRE-3311', __FILE__ . ':' . __LINE__, $msg);

            return false;
        }

        return true;
    }


    /**
     * @param string $docid
     *
     * @return mixed
     */
    public function GetFromDocumentBlob($docid)
    {
        global $gManagerFactory;

        /** @var PODocumentManager $docMgr */
        $docMgr = $gManagerFactory->getManager('podocument');
        $doc = $docMgr->Get($docid);
        $docpar = $doc['_DOCPAR'];

        return $docpar;
    }


    /**
     * For PO TD the updating Increases/Decreases Inventory and Billings
     * field automatically depends on if the Project Accounting module
     * has been subscribed for the given company or not.
     *
     * @return bool
     */
    protected function isUpdateInOutFieldAutomatically()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $modMgr = $gManagerFactory->getManager('modules');
        $ok = !$modMgr->isModuleSubscribed("48.PROJACCT");

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    public function isDocParUpdatesGL($values)
    {
        return $this->_isDocParUpdatesGL($values);
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function _isDocParUpdatesGL($values)
    {
        $updatesGL = false;
        if (($values['SHOW_TOTALS'] == 'true') && ($values['UPDATES_GL'] == 'A' || $values['UPDATES_GL'] == 'G')) {
            $updatesGL = true;
        }
        return $updatesGL;
    }

    /**
     * @param array $offsetAccts
     *
     * @return mixed
     */
    function GetAccountForSubledgerPosting($offsetAccts)
    {
        $poOffsetAcct = $offsetAccts['EXPENSEACCTKEY'];
        if (!isset($poOffsetAcct) || $poOffsetAcct == '') {
            $poOffsetAcct = $offsetAccts['INVACCTKEY'];
        }
        return $poOffsetAcct;
    }

    /**
     * @param string[] &$offsetAccts
     *
     * @param string $option
     *
     * @return string
     */
    function GetOffsetAccountForPosting(&$offsetAccts, $option = '')
    {
        //
        // check if the AP GL Account is selected in Item
        if (isset($offsetAccts['OFFSETPOGLACCOUNTKEY']) || $offsetAccts['OFFSETPOGLACCOUNTKEY'] != '') {
            $offsetAccts['OFF_GLACCOUNT'] = $offsetAccts['OFFSETPOGLACCOUNTKEY'];
            $offsetAccts['ISPOOE_OFFSET'] = true;
        } else {
            if (!isset($offsetAccts['OFF_GLACCOUNT']) || $offsetAccts['OFF_GLACCOUNT'] == '') {
                // get from PO Setup screen
                GetModulePreferences('9.PO', $prefs);
                $offsetAccts['OFF_GLACCOUNT'] = $prefs['OFF_GLACCOUNT'];
            }
            $offsetAccts['ISPOOE_OFFSET'] = false;
        }
        return $offsetAccts['OFF_GLACCOUNT'];
    }

    /**
     * @return bool
     */
    public static function hasAllocations()
    {
        $qry[0] = "select count(1) COUNT
                from docparmst
                where 
                cny# = :1
                AND sale_pur_trans = 'P'
                AND latestversionkey is null
                AND allow_allocations = 'T' ";
        $qry[1] = GetMyCompany();

        $docparCount = QueryResult($qry);
        $docparCount = $docparCount[0];

        return ($docparCount['COUNT'] > 0);
    }

    /*
    function GetOffsetAccountForCOGSPosting($offsetAccts) {
    return $offsetAccts['DIRECT_GLACCOUNT'];
    }
    */

    /**
     * @param string $docId
     * @return bool
     */
    public function isPrimaryDocEnabled($docId='')
    {
        $filters[] = ['PRIMARYDOC', '=', 'true'];
        if (!empty($docId)) {
            $filters[] = [ 'DOCID', '=', $docId ];
        }

        $filter = array(
            'selects' => array('RECORDNO'),
            'filters' => [$filters]
        );

        $result = $this->GetList($filter);
        if (isset($result[0])) {
            return true;
        }

        return false;
    }

    /**
     * @param array $values
     * @param array $docsToCheck
     */
    public function getPORun($values, $docsToCheck)
    {
        $ok = true;

        $runObject = [];
        $runObject['TYPE'] = RunObjectHandler::RUN_OBJECT_TYPE;
        $runObject['SUBTYPE'] = 'PO';
        $runObject['EXECUTIONMODE'] = 'Offline';
        $runObject['STATE'] = RunObjectHandler::RUN_STATE_QUEUED;
        $runObject['EMAIL'] = GetMyContactEmail();
        $runObject['MODULE'] = 'PO';
        $runObject['LOCATIONKEY'] = null;
        $runObject['RUNSUMMARY'] = sprintf('PO automation run');
        $parameters = [];
        $parameters['SUMMARY'] = 'PO automation run';
        $parameters['CONSUMER_HELPER_CLASS'] = 'PORunConsumerHelper';
        $parameters['CONSUMER_HELPER_PARAMS'] = [
            'DOCID'           => $values['DOCID'],
            'PRIMARYDOC'      => $values['PRIMARYDOC'],
            'ENABLEDOCCHANGE' => $values['ENABLEDOCCHANGE'],
        ];

        $runObjectGroups = [];
        $cny = GetMyCompany();
        $docId = $values['DOCID'];

        // Set a key/value in the cache to prevent jobs getting created concurrently
        $memCacheClient = CacheClient::getInstance();
        if ($memCacheClient->get($cny.$docId) == null) {
            $memCacheClient->set($cny . $docId, 1, 5);
        } else {
            logFL($cny.$docId. " found in memcache, PO run job is currently executing.");
            return $ok;
        }

        // create parallel jobs for each doc
        foreach ( $docsToCheck as $doc ) {

            $runObjectGroup = [];
            $runEntries = [];

            $runObjectGroup['STATE'] = RunObjectHandler::RUN_STATE_QUEUED;
            $runObjectGroup['TYPE'] = RunObjectHandler::RUN_TYPE_BULK;

            $runObjectEntry = [];
            $runObjectEntry['STATE'] = RunObjectHandler::RUN_STATE_QUEUED;
            $runObjectEntry['ACTIONSUMMARY'] = 'processing for po ';
            $runObjectEntry['HEADERENTITY'] = 'podocument';
            $runObjectEntry['HEADERKEY'] = $doc['RECORDNO'];
            $runEntries[] = $runObjectEntry;

            // Need to know back orders for processing appropriately in the consumer helper
            $parameters['CONSUMER_HELPER_PARAMS'][$doc['RECORDNO']] = $doc['BACKORDER'];

            $runObjectGroup['RUNENTRIES'] = $runEntries;
            $runObjectGroups[] = $runObjectGroup;
        }



        if (count($runObjectGroups) == 0) {
            return null;
        }

        $runObjectGroupCTRL = array();
        $runObjectGroupCTRL['STATE'] = RunObjectHandler::RUN_STATE_QUEUED;
        $runObjectGroupCTRL['TYPE'] = RunObjectHandler::RUN_TYPE_CNTRL;
        $runObjectGroups[] = $runObjectGroupCTRL;
        $runObject['PARAMETERS'] = json_encode($parameters);
        $runObject['RUNGROUPS']= $runObjectGroups;

        $ok = true;
        $gManagerFactory = Globals::$g->gManagerFactory;
        $runObjectManager = $gManagerFactory->getManager('runobject');
        $ok && $runObjectManager->add($runObject);
        // Set a key/value pair for this runobject which will help prevent another job from being triggered before this one is complete
        RunObjectHandler::setKVPairInRunObject("PrimaryDocToolJobInProgress", "true", $runObject['RECORDNO'], $cny);

        return $ok;
    }

    /**
     * @param string $docId
     * @param int $cny
     * @return array
     */
    public function getDocsToCheck($docId, $cny)
    {
        global $gQueryMgr;

        // Get all the TD versions for this given docId
        $docParQuery = array(
            'QUERY' => 'SELECT RECORD# FROM docpar WHERE docid =? AND cny# =?',
            'ARGTYPES' => array('string', 'integer')
        );
        $docKeys =  $gQueryMgr->DoCustomQuery($docParQuery, array($docId, $cny), true);
        $docKeys = array_column($docKeys, 'RECORD#');

        // Get all the corresponding docs for all the TD versions
        $params = array(
            'selects' => array('RECORDNO', 'BACKORDER'),
            'filters' => array(
                array(
                    array('DOCPARKEY', 'in', $docKeys),
                )
            ),
            'SUBS_TABLE' => array('dochdr' => 'dochdrmst')
        );
        $poDocMgr = Globals::$g->gManagerFactory->getManager('podocument');
        return $poDocMgr->GetList($params);
    }


    /**
     * @param array $docsToCheck
     * @param string $docId
     * @return bool
     *
     */
    public function isDownstreamTDPrimaryDocEnabled($docsToCheck, $docId)
    {
        foreach ( $docsToCheck as $doc ) {

            // Get all docs entries for this doc
            $params = [ 'filters' => [ [ [ 'DOCHDRNO', '=', $doc['RECORDNO'] ], ] ] ];

            $docentryMgr = Globals::$g->gManagerFactory->getManager('podocumententry', true, [ 'DOCTYPE' => $docId ]);
            $docLinesToCheck = $docentryMgr->GetList($params);

            if ( ! isset($docLinesToCheck[0]['RECORDNO']) || $docLinesToCheck[0]['RECORDNO'] == '' ) {
                return true;
            }

            foreach ( $docLinesToCheck as $entry ) {
                $query = 'select docparmst.cny# as cny#, docparmst.record#, subquery.source_dockey , subquery.source_doclinekey, subquery.docid, subquery.docparkey, docparmst.latestversionkey, docparmst.primarydoc from docparmst, 
                      (SELECT docparmst.cny# as cny#, docentrymst.record# , docentrymst.source_dockey as source_dockey, docentrymst.source_doclinekey as source_doclinekey, docparmst.docid as docid, docparmst.primarydoc as primarydoc, 
                      dochdrmst.docparkey as docparkey, docparmst.latestversionkey as latestversionkey FROM dochdrmst, docparmst, docentrymst WHERE  
					  dochdrmst.docparkey = docparmst.record#
                      AND docentrymst.dochdrkey = dochdrmst.record#
                      AND docparmst.cny# = :1
                      AND docentrymst.cny# = :1
                      AND dochdrmst.cny# = :1
                      AND docentrymst.record# in (SELECT distinct record# FROM  docentry START WITH record# = :2 and cny#=:1 CONNECT BY PRIOR record# = source_doclinekey and cny#=:1 union
                                                  SELECT distinct record# FROM  docentry START WITH record# = :2 and cny#=:1 CONNECT BY PRIOR source_doclinekey=record# and cny#=:1)) subquery
                      where subquery.latestversionkey = docparmst.record# and subquery.cny#=docparmst.cny#
                      union
                      (SELECT docparmst.cny# as cny#, docparmst.record#, docentrymst.source_dockey as source_dockey, docentrymst.source_doclinekey as source_doclinekey, docparmst.docid as docid, 
                      dochdrmst.docparkey as docparkey, docparmst.latestversionkey as latestversionkey, docparmst.primarydoc FROM dochdrmst, docparmst, docentrymst WHERE  
					  dochdrmst.docparkey = docparmst.record#
                      and docparmst.latestversionkey is null
                      AND docparmst.cny# = :1
                      AND docentrymst.dochdrkey = dochdrmst.record#
                      AND docentrymst.cny# = :1
                      AND dochdrmst.cny# = :1
                      AND docentrymst.record# in (SELECT distinct record# FROM  docentry START WITH record# = :2 and cny#=:1 CONNECT BY PRIOR record# = source_doclinekey and cny#=:1 union
                                                  SELECT distinct record# FROM  docentry START WITH record# = :2 and cny#=:1 CONNECT BY PRIOR source_doclinekey=record# and cny#=:1  ))';

                $results = QueryResult([$query, GetMyCompany(), $entry['RECORDNO']]);
                $primaryDocValues = array_filter(array_column($results,   'PRIMARYDOC', 'DOCID'));
                $key = array_search('T', $primaryDocValues);
                if ($key) {
                    global $gErr;
                    $gErr->addIAError(
                        'PO-0067', __FILE__ . ':' . __LINE__,'',[],_('Only one transaction definition can be enabled for the primary document workflow. Transaction definition ' . $key . ' already enabled.'),['KEY' => $key]);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Implementation of delete functionality on transaction definition.
     *
     * @param int|string $ID
     * @param bool $systemDelete
     *
     * @return bool
     */
    public function Delete($ID, $systemDelete = false)
    {
        $ok = true;

        if(!$systemDelete){
            $ok = $ok && $this->validatePOAutomationOnDelete($ID);
        }
        $ok = $ok && parent::Delete($ID, $systemDelete);

        return $ok;
    }
    /**
     * @param array $values
     *
     * @return bool
     */
    private function validatePOAutomationParams($values)
    {
        $ok = true;

        if (POSetupManager::isPOMatchEnabled()) {
            $oldvalues = $this->get($values['RECORDNO']);

            $gErr = Globals::$g->gErr;
            // All the validation logic goes here
            $poSetUpMgr = Globals::$g->gManagerFactory->getManager('posetup');
            $poAutomationDocTypeMap = $poSetUpMgr->getExistingPOMatchDocTypeMapping();

            // All the Target doc Type validation done here
            $targetDocTypes = array_column($poAutomationDocTypeMap, 'POMATCH_TARGET_DOCTYPE');
            $targetDocTypes = array_unique($targetDocTypes);

            if (!IsAdvancedConfiguration()) {
                $msg = "You cannot change the workflow because your company has Purchasing automation enabled. 
                    Turn off Purchasing automation and select save before you change the workflow.";
                $gErr->addIAError('PO-0200', __FILE__ . ':' . __LINE__, $msg);
                return false;
            }

            // Check if the TD is used in Target of PO Automation configuration.
            // If yes do the below validations.
            if(Util::countOrZero($targetDocTypes) != 0 && in_array($values['DOCID'], $targetDocTypes)){

                if (!empty($values['TD_CREATION_RULE'])
                    && ($values['TD_CREATION_RULE'] != $oldvalues['TD_CREATION_RULE'])) {
                    $msg = "Security configuration settings conflict with Transaction matching configuration.";
                    $corr = "Because this transaction definition is used for Transaction matching, you can't change the option 'Create transactions in'.";
                    $gErr->addIAError('PO-0185', __FILE__ . ':' . __LINE__, $msg,[], '',[], $corr, []);
                    $ok = false;
                }

                // Trying to change the Create policy [CREATETYPE].
                // It Should be always set to "New document or Convert", if not throw Error.
                /*if ($ok && !empty($values['CREATETYPE'])
                    && $values['CREATETYPE'] !=  CreationPolicyConstants::NEW_DOCUMENT_OR_CONVERT) {
                    //TODO: Need to remove unused constants in the future.
                    $newDocumentOrConvert = I18N::getSingleToken('IA.NEW_DOCUMENT_OR_CONVERT');
                    $createPolicy = I18N::getSingleToken('IA.CREATE_POLICY');
                    $msg = "Security configuration settings conflict with Transaction matching configuration.";
                    $corr = "Because this transaction definition is used for Transaction matching, you need to change '".$createPolicy."' to '".$newDocumentOrConvert."'";
                    $gErr->addIAError('PO-0157', __FILE__ . ':' . __LINE__, $msg,[], '',[], $corr, ['POMATCH_PLACE_HOLDER1' => $createPolicy, 'POMATCH_PLACE_HOLDER2' => $newDocumentOrConvert]);
                    $ok = false;
                }*/

                // Trying to change the Edit policy [EDITTYPE].
                // It Should be always set to "All", if not throw Error.
                // we need to remove below validations as per 2025 R1,
                // draft trx can be created irrespective to security config under TD
                /*if ($ok && $values['EDITTYPE'] == self::NOEDIT) {
                    //TODO: Need to remove unused constants in the future.
                    $noEdit = I18N::getSingleToken('IA.NO_EDIT');
                    $editPolicy = I18N::getSingleToken('IA.EDIT_POLICY');
                    $msg = "Security configuration settings conflict with Transaction matching configuration.";
                    $corr = "Because this transaction definition is used for Transaction matching, you can't change '".$editPolicy."' to '".$noEdit."'";
                    $gErr->addIAError('PO-0170', __FILE__ . ':' . __LINE__, $msg,[], '',[], $corr, ['POMATCH_PLACE_HOLDER1' => $editPolicy, 'POMATCH_PLACE_HOLDER2' => $noEdit]);
                    $ok = false;
                }*/

                if($ok){
                    // Trying to change the Can be created from [DOCPAR_RECALLS].
                    // It Should be always One document mapping.
                    // If they try to add new or delete the existing one, throw Error.
                    // Or If they change the existing, throw Error.
                    $docParRecalls = $values['DOCPAR_RECALLS'];
                    if ( Util::countOrZero($docParRecalls) == 0) {
                        $canBeCreated = I18N::getSingleToken('IA.CAN_BE_CREATED_FROM');
                        $msg = "Workflow settings conflict with Transaction matching configuration.";
                        $corr = "Because this transaction definition is used for Transaction matching, you need to provide a selection for '".$canBeCreated."'.";
                        $gErr->addIAError('PO-0159', __FILE__ . ':' . __LINE__, $msg,[], '',[], $corr, ['CANBECREATED' => $canBeCreated]);

                        $ok = false;
                    } else {
                        $canBeCreated = I18N::getSingleToken('IA.CAN_BE_CREATED_FROM');
                        $targetDocTypekey = array_search($values['DOCID'],
                            array_column($poAutomationDocTypeMap, 'POMATCH_TARGET_DOCTYPE'));
                        $sourceDocTypeMapped = $poAutomationDocTypeMap[$targetDocTypekey]['POMATCH_SOURCE_DOCTYPE'];
                        $sourceDocTypekey = array_search($sourceDocTypeMapped,
                            array_column($docParRecalls, 'RECDOCPAR'));
                        if($sourceDocTypekey === false){
                            $msg = "Workflow settings conflict with Transaction matching configuration.";
                            $corr = "Because this transaction definition is used for Transaction matching, you cannot change the value for '".$canBeCreated."'. Change '".$canBeCreated."' to '".$sourceDocTypeMapped."'.";
                            $gErr->addIAError('PO-0161', __FILE__ . ':' . __LINE__, $msg, [], '',[], $corr, ['CANBECREATED' => $canBeCreated, 'EXIST_DOCID' => $sourceDocTypeMapped]);
                            $ok = false;
                        }
                    }
                }

                // Trying to change the Entity setting and adding restriction [DOCPAR_ENTITY_PROPS].
                // If new entity restriction add, throw Error.
                /*if ($ok && Util::countOrZero($values['DOCPAR_ENTITY_PROPS']) > 1) {
                    $msg = "Entity settings conflict with Transaction matching configuration.";
                    $corr = "Because this transaction definition is used for Transaction matching, you cannot add a restriction in the Entity settings.";
                    $gErr->addIAError('PO-0164', __FILE__ . ':' . __LINE__, $msg,[], '',[], $corr, []);
                    $ok = false;
                }*/

                // Trying to change the Delete policy [DELTYPE].
                // It Should be always set to "All", if not throw Error.
                // we need to remove below validations as per 2025 R1,
                // draft trx can be created irrespective to security config under TD
                /*if ($ok && !in_array($values['DELTYPE'], [self::ALL, self::BEFORE_PRINTING]) ) {
                    //TODO: Need to remove unused constants in the future.
                    $all = I18N::getSingleToken('IA.ALL');
                    $beforePrinting = I18N::getSingleToken('IA.BEFORE_PRINTING');
                    $deletePolicy = I18N::getSingleToken('IA.DELETE_POLICY');
                    $msg = "Security configuration settings conflict with Transaction matching configuration.";
                    $corr = "Because this transaction definition is used for Transaction matching, you need to change '".$deletePolicy."' to either '".$all."' or '".$beforePrinting."'";
                    $gErr->addIAError('PO-0171', __FILE__ . ':' . __LINE__, $msg, [], '', [],
                                        $corr,
                                        [
                                            'POMATCH_PLACE_HOLDER1' => $deletePolicy,
                                            'POMATCH_PLACE_HOLDER2' => $all,
                                            'POMATCH_PLACE_HOLDER3' => $beforePrinting
                                        ]
                    );
                    $ok = false;
                }*/

            }

            if($ok){
                // All the Source doc Type validation done here
                $sourceDocTypes = array_column($poAutomationDocTypeMap, 'POMATCH_SOURCE_DOCTYPE');
                $sourceDocTypes = array_unique($sourceDocTypes);
                // Check if the TD is used in Source of PO Automation configuration.
                // If yes do the below validations.
                if(Util::countOrZero($sourceDocTypes) != 0 && in_array($values['DOCID'], $sourceDocTypes)){
                    // Trying to change the Partial conversion handling [CONVTYPE].
                    // It Should be always set to "New document or Convert", if not throw Error.
                    /*if (!empty($values['CONVTYPE'])
                        && !in_array($values['CONVTYPE'], [self::LEAVE_TRANSACTION_OPEN, self::CLOSE_TRANSACTION])) {
                        //TODO: Need to remove unused constants in the future.
                        $leaveTransactionOpen = I18N::getSingleToken('IA.LEAVE_TRANSACTION_OPEN');
                        $closeTransaction = I18N::getSingleToken('IA.CLOSE_TRANSACTION');
                        $partialcConversion = I18N::getSingleToken('IA.PARTIAL_CONVERSION_HANDLING');
                        $msg = "Workflow settings conflict with Transaction matching configuration.";
                        $corr = "Because this transaction definition is used for Transaction matching, you need to change '".$partialcConversion."' to '".$leaveTransactionOpen."' or '".$closeTransaction."'.";
                        $gErr->addIAError('PO-0162', __FILE__ . ':' . __LINE__, $msg,[], '',[], $corr, ['PARTIAL_CONVERSION' => $partialcConversion, 'LEAVE_TRANSACTION_OPEN' => $leaveTransactionOpen, 'CLOSE_TRANSACTION' => $closeTransaction]);
                        $ok = false;
                    }*/

                    if (!empty($values['TD_CREATION_RULE'])
                        && ($values['TD_CREATION_RULE'] != $oldvalues['TD_CREATION_RULE'])) {
                        $msg = "Security configuration settings conflict with Transaction matching configuration.";
                        $corr = "Because this transaction definition is used for Transaction matching, you can't change the option 'Create transactions in'.";
                        $gErr->addIAError('PO-0185', __FILE__ . ':' . __LINE__, $msg,[], '',[], $corr, []);
                        $ok = false;
                    }
                }
            }
        }


        return $ok;
    }

    /**
     * @param int $ID
     *
     * @return bool
     */
    private function validatePOAutomationOnDelete($ID)
    {
        $ok = true;

        if (POSetupManager::isPOMatchEnabled()) {

            $docTypeParams = [
                'selects' => ['DOCID'],
                'filters' => [[['RECORDNO', '=', $ID]]]
            ];
            $deleteDocTypeMap = $this->GetList($docTypeParams);
            $deleteDocID = $deleteDocTypeMap[0]['DOCID']?? '';

            if(!empty($deleteDocID)){
                $gErr = Globals::$g->gErr;
                // All the validation logic goes here
                $poSetUpMgr = Globals::$g->gManagerFactory->getManager('posetup');
                $poAutomationDocTypeMap = $poSetUpMgr->getExistingPOMatchDocTypeMapping();

                // All the Target doc Type
                $targetDocTypes = array_column($poAutomationDocTypeMap, 'POMATCH_TARGET_DOCTYPE');
                $targetDocTypes = array_unique($targetDocTypes);

                // All the Source doc Type
                $sourceDocTypes = array_column($poAutomationDocTypeMap, 'POMATCH_SOURCE_DOCTYPE');
                $sourceDocTypes = array_unique($sourceDocTypes);

                if(in_array($deleteDocID, $targetDocTypes) || in_array($deleteDocID, $sourceDocTypes)){
                    $msg = "Cannot delete transaction definition.";
                    $corr = "Because '".$deleteDocID."' is used for Transaction matching, you cannot delete it.";
                    $gErr->addIAError('PO-0163', __FILE__ . ':' . __LINE__, $msg, [], '',[], $corr, ['DOCID' => $deleteDocID]);
                    $ok = false;
                }
            }
        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    protected function translateXSLTemplate(&$values)
    {
        $ok = true;
        $values[':docxslkey'] = '';

        if ( $values['XSLTEMPLATE'] ) {
            $xslformatMgr = Globals::$g->gManagerFactory->getManager('xslformat');
            $params = [
                'selects' => [ 'RECORDNO' ],
                'filters' => [
                    [
                        [ 'DESCRIPTION', '=', $values['XSLTEMPLATE'] ],
                        [ 'MODULEID', '=', '9.PO' ],
                        [ 'DOCTYPE', '=', $values['DOCCLASS'] ],
                    ],
                ],
            ];
            $result = $xslformatMgr->GetList($params);
            if ( is_array($result) && isset($result[0]) && $result[0]['RECORDNO'] != '' ) {
                $values[':docxslkey'] = $result[0]['RECORDNO'];
            } else {
                Globals::$g->gErr->addIAError(
                    'INV-0045', __FILE__ . ':' . __LINE__,
                    "Incorrect value for the Printed Format.", []);
                $ok = false;
            }
        }

        return $ok;
    }

    /**
     * @param $oldVersionKey
     * @param $newVersionKey
     * @param $docid
     * @return bool
     */
    protected function SetDocumentValues($oldVersionKey, $newVersionKey, $docid)
    {
        $ok = parent::SetDocumentValues($oldVersionKey, $newVersionKey, $docid);
        $ok = $ok && $this->DoQuery('QRY_APPROVALPOLICY_DOCPARKEY_UPDATE_TO_LATEST', array($newVersionKey, $docid));
        return $ok;
    }

    /**
     * @return bool
     * @throws Exception
     */
    public function isChangeOrderSubtotalFeatureEnabled()
    {
        return FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('ENABLE_CCO_SUBTOTALS');
    }

    /**
     * FUNCTION TO CHECK IF CHANGE ORDER SUBTOTALS FEATURE IS ENABLED.
     *
     * @return bool
     */
    public function isChangeOrderSubtotalsEnabled() : bool
    {
        return $this->isChangeOrderEnabled() &&  $this->isChangeOrderSubtotalFeatureEnabled();
    }
}