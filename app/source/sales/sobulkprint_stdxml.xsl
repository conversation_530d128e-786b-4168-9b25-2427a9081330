<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">

<xsl:include href="../../private/xslinc/stylegroup_inc.xsl"/> 
<xsl:include href="../../private/xslinc/inventoryjs_inc.xsl"/> 

	<xsl:template match="/">
		<xsl:apply-templates/>			
	</xsl:template>
	
	<xsl:template match="reportdata">
		<xsl:apply-templates/>
	</xsl:template>
	
	<xsl:template match="report">

		<report
			department 		= "{@department}"
			location 		= "{@location}"
			orientation 	= "Landscape"
			report_date		= "{@reportdate}"
			report_time		= "{@reporttime}"
			align_currency 	= "left"
			page_number 	= "Y"
			action 			= "editor_multi_delivery.phtml"
			sess			= "{@sess}"
			done			= "{@done}"
			footer_allpages	= "F"
			print_deliver	= "Y"
		>
		<footer s="footer" lines="1"><xsl:value-of select="@titlecomment"/></footer>
		<header s="header">
			<hrow s="header">
				<hcol id="0" colspan="4" s="15"><xsl:value-of select="@title"/></hcol>
				<hcol id="0" colspan="4" s="15"><xsl:value-of select="@title2"/></hcol>
				<hcol id="0" s="18" colspan="4">
					<xsl:attribute name="submitbutton">1</xsl:attribute>
					<xsl:attribute name="submitbutton_value">IA.PRINT_EMAIL</xsl:attribute>
					<xsl:attribute name="submitbutton_name">.deliver</xsl:attribute>
					<xsl:attribute name="onclick"><xsl:text>javascript:if(!checkOffline(this.form)) {return false;}; disableButtons(this)</xsl:text></xsl:attribute>
					<xsl:attribute name="class">IA.TASK</xsl:attribute>
					<hiddenvars hiddenvars_name=".op" hiddenvars_value="{@deliverop}"/>
					<hiddenvars hiddenvars_name=".do" hiddenvars_value="view"/>
					<hiddenvars hiddenvars_name=".start_date" hiddenvars_value="{@start_date}"/>
					<hiddenvars hiddenvars_name=".end_date" hiddenvars_value="{end_date}"/>
					<hiddenvars hiddenvars_name=".start_customer" hiddenvars_value="{@start_customer}"/>
					<hiddenvars hiddenvars_name=".end_customer" hiddenvars_value="{@end_customer}"/>
					<hiddenvars hiddenvars_name=".period" hiddenvars_value="{@period}"/>
					<hiddenvars hiddenvars_name=".message" hiddenvars_value="{@messagetext}"/>
					<hiddenvars hiddenvars_name=".markettext" hiddenvars_value="{@marketngtext}"/>
					<hiddenvars hiddenvars_name=".nemail" hiddenvars_value="{@nemail}"/>
					<hiddenvars hiddenvars_name=".processed" hiddenvars_value="{@processed}"/>
					<hiddenvars hiddenvars_name=".xslformat" hiddenvars_value="{@xslformat}"/>
					<hiddenvars hiddenvars_name=".sendersemail" hiddenvars_value="{@sendersemail}"/>
					<hiddenvars hiddenvars_name=".sendersphone" hiddenvars_value="{@sendersphone}"/>
					<hiddenvars hiddenvars_name=".sendersname" hiddenvars_value="{@sendersname}"/>
					<hiddenvars hiddenvars_name=".messagetext" hiddenvars_value="{@messagetext}"/>
					<hiddenvars hiddenvars_name=".marketingtext" hiddenvars_value="{@marketingtext}"/>
					<hiddenvars hiddenvars_name=".agingon" hiddenvars_value="{@agingon}"/>
                    <hiddenvars hiddenvars_name=".prevprint" hiddenvars_value="{@prevprint}"/>
					<hiddenvars hiddenvars_name=".unpaid" hiddenvars_value="{@unpaid}"/>
				</hcol>
			</hrow>
			<hrow s="51">
				<hcol id="2" s="17">IA.CUSTOMER</hcol>
				<hcol id="0" s="17">IA.TRANSACTION</hcol>
				<hcol id="1" s="16">IA.DATE</hcol>
				<hcol id="5" s="16">IA.DUE_DATE</hcol>
				<hcol id="6" s="18">IA.TOTAL_AMOUNT</hcol>
				<xsl:call-template name="PrintCheckboxHcol">
					<xsl:with-param name="idtype">invoice</xsl:with-param>
				</xsl:call-template>
				<hcol s="17">IA.DOCUMENT_TEMPLATE</hcol>
                <hcol s="17">IA.EMAIL_TEMPLATE</hcol>
				<xsl:call-template name="EmailCheckboxHcol">
					<xsl:with-param name="idtype">invoice</xsl:with-param>
				</xsl:call-template>
				<hcol s="17">IA.CC_SEPARATED_BY_OR</hcol>
				<hcol s="17">IA.BCC_SEPARATED_BY_OR</hcol>
			</hrow>
		</header>
		<body s="body">
			<row s="12">
				<col  s="19" colspan="{/reportdata/report/@noofcolumns}" ></col>
			</row>
			<xsl:apply-templates/>
		</body>
        <body>
            <row>
                <col s="59" colspan="4">IA.TOTALS:</col>
                <col s="35"><xsl:value-of select="@grandtotal"/></col>
                <xsl:choose>
                    <xsl:when test="//reportdata/report/@MULTICURRENCY != ''">
                        <col s="59" colspan="5"></col>
                    </xsl:when>
                        <xsl:otherwise>
                            <col s="59" colspan="4"></col>
                    </xsl:otherwise>
                </xsl:choose>
            </row>
            <row>
                <col s="59" colspan="4">IA.TOTAL_DOCUMENTS</col>
                <col s="62"><xsl:value-of select="@numdocuments"/></col>                    
                <xsl:choose>
                    <xsl:when test="//reportdata/report/@MULTICURRENCY != ''">
                        <col s="59" colspan="5"></col>
                    </xsl:when>
                        <xsl:otherwise>
                            <col s="59" colspan="4"></col>
                    </xsl:otherwise>
                </xsl:choose>
            </row>
            <row>
                <col s="59" colspan="4">IA.DOCUMENTS_TO_BE_PRINTED_COLON</col>
                <col s="35">
                    <xsl:attribute name="span">1</xsl:attribute>
                    <xsl:attribute name="span_name"><xsl:text>print_counter</xsl:text></xsl:attribute>
                    <xsl:attribute name="span_id"><xsl:text>print_counter</xsl:text></xsl:attribute>
                </col>                
                <xsl:choose>
                    <xsl:when test="//reportdata/report/@MULTICURRENCY != ''">
                        <col s="59" colspan="5"></col>
                    </xsl:when>
                        <xsl:otherwise>
                            <col s="59" colspan="4"></col>
                    </xsl:otherwise>
                </xsl:choose>
            </row>
            <row>
                <col s="59" colspan="4">IA.DOCUMENTS_TO_BE_EMAILED_COLON</col>
                <col s="35">
                    <xsl:attribute name="span">1</xsl:attribute>
                    <xsl:attribute name="span_name"><xsl:text>email_counter</xsl:text></xsl:attribute>
                    <xsl:attribute name="span_id"><xsl:text>email_counter</xsl:text></xsl:attribute>
                </col>                
                <xsl:choose>
                    <xsl:when test="//reportdata/report/@MULTICURRENCY != ''">
                        <col id="2" s="22" colspan="5"></col>
                    </xsl:when>
                        <xsl:otherwise>
                            <col id="2" s="22" colspan="4"></col>
                    </xsl:otherwise>
                </xsl:choose>
            </row>
            <xsl:if test="//reportdata/report/@INCLUDE_ATTACHMENTS_HELP = 'true'">
                <row>
                    <col s="39" colspan="11">IA.INCLUDES_ANY_DOCUMENT_ATTACHMENTS_IN_THE_EMAIL</col>
                </row>
            </xsl:if>
         </body>
        
		<xsl:call-template name="stylegroups"/>
		<script language="javascript">
			<xsl:apply-templates select="@javascript"/>
			<xsl:call-template name="script"/>		
			<xsl:call-template name="doselect"/>		
			<xsl:call-template name="checkEmail"/>
			<xsl:call-template name="checkOffline"/>
            <xsl:call-template name="countDelivery"/>
		</script>
</report>

</xsl:template>



<xsl:template match="NODATA">
    <xsl:if test="string(@NODATA)=1">
		<row s="14">
			<col id="0" s="19" colspan="8">IA.NO_DATA_FOUND</col>
		</row>
    </xsl:if>
</xsl:template>

	<xsl:template match="PODOCS">
		<xsl:variable name="bgcol">
			 <xsl:choose>
				 <xsl:when test="number(position()) mod 4 = 0">53</xsl:when>
				 <xsl:otherwise>54</xsl:otherwise>
			 </xsl:choose>
		</xsl:variable>
		<row s="{$bgcol}">
			<col id="0" s="23">
				<xsl:attribute name="href">
					<xsl:value-of select="@ENTITYHREF"/>
				</xsl:attribute>
				<xsl:value-of select="@ENTITY"/>
				<hiddenvars hiddenvars_name=".invoice[{@INVOICECOUNTER}]" hiddenvars_value="{@DOCHDRKEY}"/>
				<hiddenvars hiddenvars_name=".doctype[{@DOCHDRKEY}]" hiddenvars_value="{@DOCTYPE}"/>
			</col>
			<col id="0" s="23">
				<xsl:attribute name="href">
					<xsl:value-of select="@DOCHREF"/>
				</xsl:attribute>
				<xsl:value-of select="@DOCNOKEY"/>
			</col>
			<col id="1" s="25"><xsl:value-of select="@DOCDATE"/></col>
			<col id="2" s="25"><xsl:value-of select="@WHENDUE"/></col>
			<col id="3" s="34"><xsl:value-of select="@TOTALAMT"/></col>
		   <col id="2" s="25">
			   <xsl:attribute name="checkbox">1</xsl:attribute>
				<xsl:if test="contains(@DELIVERYOPTIONS,'P')">
					<xsl:attribute name="checked">1</xsl:attribute>
				</xsl:if>
			   <xsl:attribute name="checkbox_value">
				   <xsl:text>P</xsl:text>
			   </xsl:attribute>
			   <xsl:attribute name="checkbox_name">
				   <xsl:text>.P[</xsl:text>
				   <xsl:value-of select="@DOCHDRKEY"/>
				  <xsl:text>]</xsl:text>
			  </xsl:attribute>
                <xsl:attribute name="class"><xsl:text>noborder</xsl:text></xsl:attribute>
                <xsl:attribute name="onclick">
                    <xsl:text>javascript:document.getElementById('print_counter').innerHTML=countDelivery(this.form, 'P');</xsl:text>
                </xsl:attribute>
			</col>
			<!-- Adding DocumentTemplate field-->
			<col id="2" s="25">
				<xsl:value-of  select="@DOCTEMPLATE"/>
			</col>
            <!-- Adding EmailTemplateID field and hidden variable -->
            <col id="2" s="25">
                <xsl:value-of select="@EMAILTEMPLATENAME"/>

                <xsl:if test="@EMAILTEMPLATE_INCLUDE_ATTACHMENTS = 'true'">
                    <xsl:text> *</xsl:text>
                </xsl:if>
                <hiddenvars hiddenvars_name=".emailtemplate[{@DOCHDRKEY}]" hiddenvars_value="{@EMAILTEMPLATEID}"/>
            </col>

            <col id="2" s="25">
			   <xsl:attribute name="checkbox">1</xsl:attribute>
				<xsl:if test="contains(@DELIVERYOPTIONS,'E')">
					<xsl:attribute name="checked">1</xsl:attribute>
				</xsl:if>
			   <xsl:attribute name="checkbox_value">
				   <xsl:text>E</xsl:text>
			   </xsl:attribute>
			   <xsl:attribute name="checkbox_name">
				   <xsl:text>.E[</xsl:text>
				   <xsl:value-of select="@DOCHDRKEY"/>
				   <xsl:text>]</xsl:text>
			   </xsl:attribute>
                <xsl:attribute name="class"><xsl:text>noborder</xsl:text></xsl:attribute>
                <xsl:attribute name="onclick">
                    <xsl:text>javascript:checkEmail(this.form, '</xsl:text><xsl:value-of select="@DOCHDRKEY"/><xsl:text>');document.getElementById('email_counter').innerHTML=countDelivery(this.form, 'E')</xsl:text>
                </xsl:attribute>
			</col>
			
			<col id="2" s="23">
				<xsl:attribute name="multitext">1</xsl:attribute>
				<xsl:attribute name="multitext_size">20</xsl:attribute>
				<xsl:attribute name="multitext_width">150</xsl:attribute>
				<xsl:attribute name="multitext_height">20px</xsl:attribute>
                <xsl:attribute name="multitext_cols">25</xsl:attribute>
				<xsl:attribute name="multitext_value"><xsl:value-of select="@EMAIL1"/></xsl:attribute>
				<xsl:attribute name="multitext_name">
					<xsl:text>.custemail[</xsl:text>
					<xsl:value-of select="@DOCHDRKEY"/>
					<xsl:text>]</xsl:text>
				</xsl:attribute>
				<xsl:attribute name="onblur">
					<xsl:text>javascript:checkEmail(this.form, '</xsl:text><xsl:value-of select="@DOCHDRKEY"/><xsl:text>')</xsl:text>
				</xsl:attribute>
	
			</col> 

            <!-- Adding Cc field -->
			<col id="2" s="23">
				<xsl:attribute name="multitext">1</xsl:attribute>
				<xsl:attribute name="multitext_size">20</xsl:attribute>
				<xsl:attribute name="multitext_width">150</xsl:attribute>
				<xsl:attribute name="multitext_height">20px</xsl:attribute>
                <xsl:attribute name="multitext_cols">25</xsl:attribute>
				<xsl:attribute name="multitext_value"><xsl:value-of select="@EMAILCC"/></xsl:attribute>
				<xsl:attribute name="multitext_name">
					<xsl:text>.custccemail[</xsl:text>
					<xsl:value-of select="@DOCHDRKEY"/>
					<xsl:text>]</xsl:text>
				</xsl:attribute>
				<xsl:attribute name="onblur">
					<xsl:text>javascript:checkEmail(this.form, '</xsl:text><xsl:value-of select="@DOCHDRKEY"/><xsl:text>')</xsl:text>
				</xsl:attribute>
			</col>

            <!-- Adding Bcc field -->
			<col id="2" s="23">
				<xsl:attribute name="multitext">1</xsl:attribute>
				<xsl:attribute name="multitext_size">20</xsl:attribute>
				<xsl:attribute name="multitext_width">150</xsl:attribute>
				<xsl:attribute name="multitext_height">20px</xsl:attribute>
                <xsl:attribute name="multitext_cols">25</xsl:attribute>
				<xsl:attribute name="multitext_value"><xsl:value-of select="@EMAILBCC"/></xsl:attribute>
				<xsl:attribute name="multitext_name">
					<xsl:text>.custbccemail[</xsl:text>
					<xsl:value-of select="@DOCHDRKEY"/>
					<xsl:text>]</xsl:text>
				</xsl:attribute>
				<xsl:attribute name="onblur">
					<xsl:text>javascript:checkEmail(this.form, '</xsl:text><xsl:value-of select="@DOCHDRKEY"/><xsl:text>')</xsl:text>
				</xsl:attribute>

			</col>


	</row>
	<row s="14">
		<col id="0" s="19"></col>
	</row>
</xsl:template>
</xsl:stylesheet>
