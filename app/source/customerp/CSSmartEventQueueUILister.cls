<?php
/**
 * File description
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Intacct Corporation, All Rights Reserved
 */

class CSSmartEventQueueUILister extends SmartEventJobQueueUILister
{
    /**
     * @return string
     */
    protected function getEntity() : string
    {
        return 'cssmarteventqueueui';
    }

    /**
     *
     * @return string the entity name
     */
    protected function getQueueEntity(): string
    {
        return 'smarteventjobqueue';
    }

    /**
     *
     *
     * @param string[] $fields
     * @param string[] $fieldLabels
     *
     */
    protected function prepareFields(&$fields, &$fieldLabels) : void
    {
        if (is_array($fields)) {
            array_unshift($fields, IMSPackageDetailManager::IMSPD_CNY, IMSPackageDetailManager::IMSPD_DB,
                IMSPackageDetailManager::IMSPD_CONTEXT);
        }
        if (is_array($fieldLabels)) {
            array_unshift($fieldLabels, '', 'DB', 'Context');
        }
    }

    /**
     *
     *
     * @param string $param
     *
     * @return string
     */
    protected function addMandatoryFilters(string $param): string
    {
        $filters = ['F_' . IMSPackageDetailManager::IMSPD_DB, 'F_' . IMSPackageDetailManager::IMSPD_CONTEXT];
        foreach ( $filters as $filter ) {
            if (Request::$r->$filter) {
                $param .= '&' . $filter . '=' . Request::$r->$filter;
            }
        }
        return $param;
    }

    function BuildTable() : void
    {
        $dbFilter = 'F_' . IMSPackageDetailManager::IMSPD_DB;
        $contextFilter = 'F_' . IMSPackageDetailManager::IMSPD_CONTEXT;
        $hasContextFilter = isset(Request::$r->$contextFilter) && Request::$r->$contextFilter != '' && isl_strpos(Request::$r->$contextFilter, '%') === false;
        $hasDBFilter = isset(Request::$r->$dbFilter) && Request::$r->$dbFilter != '';
        $hasForceDBFilter = isset(Request::$r->_forcedDB) && Request::$r->_forcedDB != '';
        if ( ! $hasContextFilter && ! $hasDBFilter && ! $hasForceDBFilter ) {
            echo "Please specify the company using the parameter $contextFilter=... or the DB using the parameter $dbFilter =...";
            exit();
        } else {
            if ( $hasContextFilter ) {
                $context = Request::$r->$contextFilter;
                SetDBSchema( $context );
                $val = $context;
                $filter = $contextFilter;
            } else {
                if ( $hasDBFilter ) {
                    $dbId = Request::$r->$dbFilter;
                } else {
                    $forcedDB = Request::$r->_forcedDB;
                    $dbId = $forcedDB;
                    Request::$r->$dbFilter = $dbId;
                }
                SetDBSchemaVars( $dbId );
                $val = $dbId;
                $filter = $dbFilter;
            }
            $conn = MyConnect();
            if ( ! $conn ) {
                echo "The value '$val' for the $filter parameter is not correct";
                exit();
            }
            Request::$r->_forcedDB = Globals::$g->gDBServerId;
        }

        parent::BuildTable();

        $table = &$this->table;

        $op = GetOperationId('co/lists/' . $this->entity . '/view');

        for ($i = 0; $i < count($table); $i++) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $rec = $table[$i];

            $cny = $table[$i][IMSPackageDetailManager::IMSPD_CNY];
            $recordId = $table[$i]['RECORDNO'];
            $table[$i][IMSPackageDetailManager::IMSPD_CNY] = CreateHRef(
                "editor.phtml?.op=$op&.r=$recordId&.cny=$cny&.it=" . $this->entity . '&'
                . OptDone(ScriptRequest()),
                'View',
                'View'
            );
        }
    }

    /**
     *
     *
     * @param string $ts
     *
     * @return  false|string
     */
    protected function convertTimestampTZ($ts)
    {
        return $ts;
    }

    /**
     *
     * @return bool
     */
    protected function filterExactMatch(): bool
    {
        return true;
    }

    /**
     * @return bool
     */
    protected function isCSToolsPage() : bool
    {
        return true;
    }
}
