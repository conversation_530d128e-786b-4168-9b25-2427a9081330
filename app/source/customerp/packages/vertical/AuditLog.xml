<?xml version="1.0" encoding="UTF-8"?>
<customErpPackage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://www.intacct.com/ia/customerp/CustomERPPackage.xsd">
	<packageDescription>
		<name>IA.AUDIT_LOG</name>
		<description>This package enables audit trail on various Intacct objects by using smart event logging mechanism.</description>
		<author>Intacct</author>
		<intacctPackageId>IA_PKG_AUDITTLOG</intacctPackageId>
	</packageDescription>
	<featureKeys>
		<featureKey>Audit Log</featureKey>
	</featureKeys>	
	<smartLinks>
        <!-- BEGIN Master Objects-->
        <smartLink>
            <smartLinkId>AU_DEPARTMENT</smartLinkId>
            <type>workflow</type>
            <ownerObject>department</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_LOCATION</smartLinkId>
            <type>workflow</type>
            <ownerObject>location</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_LOCATIONENTITY</smartLinkId>
            <type>workflow</type>
            <ownerObject>locationentity</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_TERRITORY</smartLinkId>
            <type>workflow</type>
            <ownerObject>territory</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_GLACCOUNT</smartLinkId>
            <type>workflow</type>
            <ownerObject>glaccount</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_STATACCOUNT</smartLinkId>
            <type>workflow</type>
            <ownerObject>stataccount</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_EEACCOUNTLABEL</smartLinkId>
            <type>workflow</type>
            <ownerObject>eeaccountlabel</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_GLACCTGRP</smartLinkId>
            <type>workflow</type>
            <ownerObject>glacctgrp</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_JOURNAL</smartLinkId>
            <type>workflow</type>
            <ownerObject>journal</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_STATJOURNAL</smartLinkId>
            <type>workflow</type>
            <ownerObject>statjournal</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_TAXADJJRNL</smartLinkId>
            <type>workflow</type>
            <ownerObject>taxadjjrnl</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_GAAPADJJRNL</smartLinkId>
            <type>workflow</type>
            <ownerObject>gaapadjjrnl</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_CLASS</smartLinkId>
            <type>workflow</type>
            <ownerObject>class</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_PROJECT</smartLinkId>
            <type>workflow</type>
            <ownerObject>project</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_PROJECTRESOURCES</smartLinkId>
            <type>workflow</type>
            <ownerObject>projectresources</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_PROJECTSTATUS</smartLinkId>
            <type>workflow</type>
            <ownerObject>projectstatus</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_PROJECTTYPE</smartLinkId>
            <type>workflow</type>
            <ownerObject>projecttype</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_TRANSACTIONRULE</smartLinkId>
            <type>workflow</type>
            <ownerObject>transactionrule</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_TASK</smartLinkId>
            <type>workflow</type>
            <ownerObject>task</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_TASKRESOURCES</smartLinkId>
            <type>workflow</type>
            <ownerObject>taskresources</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_VENDOR</smartLinkId>
            <type>workflow</type>
            <ownerObject>vendor</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_VENDTYPE</smartLinkId>
            <type>workflow</type>
            <ownerObject>vendtype</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_CUSTOMER</smartLinkId>
            <type>workflow</type>
            <ownerObject>customer</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_CUSTTYPE</smartLinkId>
            <type>workflow</type>
            <ownerObject>custtype</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_EMPLOYEE</smartLinkId>
            <type>workflow</type>
            <ownerObject>employee</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_EMPLOYEERATE</smartLinkId>
            <type>workflow</type>
            <ownerObject>employeerate</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_OUTOFOFFICE</smartLinkId>
            <type>workflow</type>
            <ownerObject>outofoffice</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_EMPLOYEEOUTOFOFFICE</smartLinkId>
            <type>workflow</type>
            <ownerObject>employeeoutofoffice</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_POSITIONSKILL</smartLinkId>
            <type>workflow</type>
            <ownerObject>positionskill</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_EMPLOYEEPOSITIONSKILL</smartLinkId>
            <type>workflow</type>
            <ownerObject>employeepositionskill</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_EARNINGTYPE</smartLinkId>
            <type>workflow</type>
            <ownerObject>earningtype</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_ITEM</smartLinkId>
            <type>workflow</type>
            <ownerObject>item</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_UOM</smartLinkId>
            <type>workflow</type>
            <ownerObject>uom</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_WAREHOUSE</smartLinkId>
            <type>workflow</type>
            <ownerObject>warehouse</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_USERINFO</smartLinkId>
            <type>workflow</type>
            <ownerObject>userinfo</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <!-- END Master Objects-->
        <!-- BEGIN Transaction Objects-->
		<smartLink>
			<smartLinkId>AU_APADJUSTMENT</smartLinkId>
			<type>workflow</type>
			<ownerObject>apadjustment</ownerObject>
			<events>
				<event>add</event>
				<event>set</event>
				<event>delete</event>
			</events>
			<renderDetails>
				<workflow>
					<action>
						<logAction/>
					</action>
				</workflow>
			</renderDetails>
		</smartLink>
        <smartLink>
            <smartLinkId>AU_ARADJUSTMENT</smartLinkId>
            <type>workflow</type>
            <ownerObject>aradjustment</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
		<smartLink>
			<smartLinkId>AU_APBILL</smartLinkId>
			<type>workflow</type>
			<ownerObject>apbill</ownerObject>
			<events>
				<event>add</event>
				<event>set</event>
				<event>delete</event>
			</events>
			<renderDetails>
				<workflow>
					<action>
						<logAction/>
					</action>
				</workflow>
			</renderDetails>
		</smartLink>
        <smartLink>
            <smartLinkId>AU_ARINVOICE</smartLinkId>
            <type>workflow</type>
            <ownerObject>arinvoice</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_APRECURBILL</smartLinkId>
            <type>workflow</type>
            <ownerObject>aprecurbill</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_ARRECURINVOICE</smartLinkId>
            <type>workflow</type>
            <ownerObject>arrecurinvoice</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_EXPENSEPAYMENTTYPE</smartLinkId>
            <type>workflow</type>
            <ownerObject>expensepaymenttype</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
		<smartLink>
			<smartLinkId>AU_EEXPENSES</smartLinkId>
			<type>workflow</type>
			<ownerObject>eexpenses</ownerObject>
			<events>
				<event>add</event>
				<event>set</event>
				<event>delete</event>
			</events>
			<renderDetails>
				<workflow>
					<action>
						<logAction/>
					</action>
				</workflow>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>AU_GLBATCH</smartLinkId>
			<type>workflow</type>
			<ownerObject>glbatch</ownerObject>
			<events>
				<event>add</event>
				<event>set</event>
				<event>delete</event>
			</events>
			<renderDetails>
				<workflow>
					<action>
						<logAction/>
					</action>
				</workflow>
			</renderDetails>
		</smartLink>
        <smartLink>
            <smartLinkId>AU_BUDGETHEADER</smartLinkId>
            <type>workflow</type>
            <ownerObject>budgetheader</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
		<smartLink>
			<smartLinkId>AU_INVDOCUMENT</smartLinkId>
			<type>workflow</type>
			<ownerObject>invdocument</ownerObject>
			<documentType>ALL</documentType>
			<events>
				<event>add</event>
				<event>set</event>
				<event>delete</event>
			</events>
			<renderDetails>
				<workflow>
					<action>
						<logAction/>
					</action>
				</workflow>
			</renderDetails>
		</smartLink>
        <smartLink>
            <smartLinkId>AU_SODOCUMENT</smartLinkId>
            <type>workflow</type>
            <ownerObject>sodocument</ownerObject>
            <documentType>ALL</documentType>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_PODOCUMENT</smartLinkId>
            <type>workflow</type>
            <ownerObject>podocument</ownerObject>
            <documentType>ALL</documentType>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
		<smartLink>
			<smartLinkId>AU_INVRECURDOCUMENT</smartLinkId>
			<type>workflow</type>
			<ownerObject>invrecurdocument</ownerObject>
			<documentType>ALL</documentType>
			<events>
				<event>add</event>
				<event>set</event>
				<event>delete</event>
			</events>
			<renderDetails>
				<workflow>
					<action>
						<logAction/>
					</action>
				</workflow>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>AU_SORECURDOCUMENT</smartLinkId>
			<type>workflow</type>
			<ownerObject>sorecurdocument</ownerObject>
			<documentType>ALL</documentType>
			<events>
				<event>add</event>
				<event>set</event>
				<event>delete</event>
			</events>
			<renderDetails>
				<workflow>
					<action>
						<logAction/>
					</action>
				</workflow>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>AU_PORECURDOCUMENT</smartLinkId>
			<type>workflow</type>
			<ownerObject>porecurdocument</ownerObject>
			<documentType>ALL</documentType>
			<events>
				<event>add</event>
				<event>set</event>
				<event>delete</event>
			</events>
			<renderDetails>
				<workflow>
					<action>
						<logAction/>
					</action>
				</workflow>
			</renderDetails>
		</smartLink>
        <smartLink>
            <smartLinkId>AU_TIMESHEET</smartLinkId>
            <type>workflow</type>
            <ownerObject>timesheet</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_TIMETYPE</smartLinkId>
            <type>workflow</type>
            <ownerObject>timetype</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
		<smartLink>
			<smartLinkId>AU_TIMESHEETENTRY</smartLinkId>
			<type>workflow</type>
			<ownerObject>timesheetentry</ownerObject>
			<events>
				<event>add</event>
				<event>set</event>
				<event>delete</event>
			</events>
			<renderDetails>
				<workflow>
					<action>
						<logAction/>
					</action>
				</workflow>
			</renderDetails>
		</smartLink>
		<smartLink>
			<smartLinkId>AU_REVRECTEMPLATE</smartLinkId>
			<type>workflow</type>
			<ownerObject>revrectemplate</ownerObject>
			<events>
				<event>add</event>
				<event>set</event>
				<event>delete</event>
			</events>
			<renderDetails>
				<workflow>
					<action>
						<logAction/>
					</action>
				</workflow>
			</renderDetails>
		</smartLink>
        <smartLink>
            <smartLinkId>AU_APTERM</smartLinkId>
            <type>workflow</type>
            <ownerObject>apterm</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_ARTERM</smartLinkId>
            <type>workflow</type>
            <ownerObject>arterm</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_APPAYMENTREQUEST</smartLinkId>
            <type>workflow</type>
            <ownerObject>appaymentrequest</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_ARPAYMENT</smartLinkId>
            <type>workflow</type>
            <ownerObject>arpayment</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_BANKFEE</smartLinkId>
            <type>workflow</type>
            <ownerObject>bankfee</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_CCTRANSACTION</smartLinkId>
            <type>workflow</type>
            <ownerObject>cctransaction</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_CHARGEPAYOFF</smartLinkId>
            <type>workflow</type>
            <ownerObject>chargepayoff</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_OTHERRECEIPT</smartLinkId>
            <type>workflow</type>
            <ownerObject>otherreceipts</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_CREDITCARDFEE</smartLinkId>
            <type>workflow</type>
            <ownerObject>creditcardfee</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>

        <smartLink>
            <smartLinkId>AU_DEPOSIT</smartLinkId>
            <type>workflow</type>
            <ownerObject>deposit</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_EPPAYMENTREQUEST</smartLinkId>
            <type>workflow</type>
            <ownerObject>eppaymentrequest</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_EXPENSEADJUSTMENTS</smartLinkId>
            <type>workflow</type>
            <ownerObject>expenseadjustments</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_INVPRICELIST</smartLinkId>
            <type>workflow</type>
            <ownerObject>invpricelist</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_POPRICELIST</smartLinkId>
            <type>workflow</type>
            <ownerObject>popricelist</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <smartLink>
            <smartLinkId>AU_SOPRICELIST</smartLinkId>
            <type>workflow</type>
            <ownerObject>sopricelist</ownerObject>
            <events>
                <event>add</event>
                <event>set</event>
                <event>delete</event>
            </events>
            <renderDetails>
                <workflow>
                    <action>
                        <logAction/>
                    </action>
                </workflow>
            </renderDetails>
        </smartLink>
        <!-- END Transaction Objects-->
	</smartLinks>
<signature>EwWEtseIa9MCeMwlF3u0jqDRcL4RhvGe5qS9ZXA+fC1pPu/DJuYd+ni0Lem/+vmVLBPRBI1ibDHca/Oxo9B1Nc6bLWIT6r47evATNKnF3eT7DS5Z/RC2kOQ//4WhisrJdMiEbwvFF+AswQM2fA9wdsWhfps9ELasOU2laGNg+lA=</signature>
</customErpPackage>
