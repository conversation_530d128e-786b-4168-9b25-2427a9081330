<?php
/**
 *   FILE: CustomReport.cls
 *   AUTHOR: NaveenS
 *   DESCRIPTION:
 *
 *   (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *   This document contains trade secret data that belongs to Intacct
 *   Corporation and is protected by the copyright laws.  Information
 *   herein may not be used, copied or disclosed in whole or in part
 *   without prior written consent from Intacct Corporation.
 */

import('GroupReporter');
import('NexusInfo');
import('NexusDBInfo');
import('NexusUIInfo');
include_once( 'CustomERP.inc' );
include_once( 'customreport_util.inc' );
require_once 'nexusobjectinfo.inc';

/**
 * Class CustomReport
 */
class CustomReport extends GroupReporter
{

    /** @var array $rtparams */
    var $rtparams;
    /** @var string $reportType */
    var $reportType;
    /** @var NexusDBInfo $nexusDB */
    var $nexusDB;
    /** @var NexusUIInfo $nexusUI */
    var $nexusUI;
    /** @var string[][] $rawdata */
    var $rawdata;
    /** @var bool $export */
    var $export;
    /** @var bool $exportArray */
    var $exportArray;
    /** @var bool $multiRegion */
    var $multiRegion;
    /** @var bool $isMatrixReport */
    var $isMatrixReport;
    /** @var string[] $userCalculatedColumns */
    var $userCalculatedColumns;
    /** @var array $runtimeColumns */
    var $runtimeColumns;
    /** @var string[] $standardFilterNames */
    var $standardFilterNames;
    /** @var bool $libraryReport */
    var $libraryReport = false;

    /**
     * Flag to indicate whether we have added an extra column in the report to display the summary labels properly
     *
     * @var bool $hasExtraColumn
     */
    private $hasExtraColumn;

    /**
     * @var array $postProcessFields
     */
    private $postProcessFields;
    
    /**
     * @var array $textTokens I18N all tokens
     */
    protected $textTokens = [
        ['id' => 'IA.NO_DELIVERIES_REQUESTED_YET'],
        ['id' => 'IA.SUCCESSFUL'],
        ['id' => 'IA.DELIVERY_ERROR'],
        ['id' => 'IA.UNSUCCESSFUL']
    ];
    
    /**
     * @var array I18N mappings
     */
    protected $textMap = [];
    
    /**
     * @param array $params params global report parameters
     *
     */
    function __construct($params)
    {
        $repparams = [
            'report'      => 'custom',
            '2stage'      => true,
            'reportslide' => true,
        ];

        //Set libraryReport if we are reading IA CUSTOM REPORT
        if ( $params['libraryReport'] ) {
            $this->libraryReport = true;
        }

        parent::__construct(INTACCTarray_merge($params, $repparams));

        // Nexus 2 DB mapping handler class
        $this->nexusDB = new NexusDBInfo(null, isset($this->params['FROM_GATEWAY']) && $this->params['FROM_GATEWAY']);

        // Nexus 2 UI mapping handler class
        $this->nexusUI = new NexusUIInfo(null, isset($this->params['FROM_GATEWAY']) && $this->params['FROM_GATEWAY']);
        $this->GetQueryDef($queryDef, $rtparams);

        //  If there is no given modulekey (e.g. from readReport API), use report's default modulekey.
        if ( empty($this->modulekey) && isset($queryDef['defaultmodule']) ) {
            $this->modulekey = $queryDef['defaultmodule'];
        }
        $this->nexusUI->MergeRunTimeParamValues($queryDef);
        $this->params['NOREPORTLOCCHECK'] = ! $this->nexusUI->getRequiresLocInIGC($queryDef);




        if ( IsMCMESubscribed() ) {
            global $gManagerFactory;
            $name = Request::$r->_cr;

            //  If this is from the gateway, the global _cr may not be set, so get it from the params.
            if ( empty($name) && $this->params['FROM_GATEWAY'] == 1 && ! empty($this->params['_cr']) ) {
                $name = $this->params['_cr'];
                Request::$r->_cr = $name;    //  In case it's used later.
            }

            /** @var CustomReportManager $crMgr */
            $crMgr = $gManagerFactory->getManager('customreport');
            $cReport = $crMgr->Get($name);
            $root = $cReport['ROOT'];
            $promptForLocation = false;
            foreach ( $this->nexusUI->kStandardFilterFields[$root] ?? [] as $filter ) {
                if ( ! empty($filter['promptonrun']) && ! empty($filter['promptonrun']['LOCATION']) ) {
                    $promptForLocation = true;
                }
            }
            if ( $root && ! GetContextLocation() && ! $promptForLocation ) {
                $this->params['NOREPORTLOCCHECK'] = true;
            }
        }
    
        $this->textMap = I18N::getTokensForArray($this->textTokens);
    }

    /**
     * On-line report params
     *
     * @param bool $showmemorized
     *
     * @return bool
     */
    function &Reporter_Instantiate($showmemorized = false)
    {
        //TODO bug remove return false from base classes
        $showstored = Request::$r->showstored;
        $fromdashb = Request::$r->_db;

        $this->params['fromdash'] = ( $fromdashb == '1' );

        // Get Query Definition from DB
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ok = $this->GetQueryDef($queryDef, $rtparams);

        if ( $showstored || $this->params['PREFER_INPUT_PARAMS'] == true ) {
            //  If PREFER_INPUT_PARAMS, do the merge of the input and stored params,
            //   but prefer the inputs over the stored.
            if ( $this->params['PREFER_INPUT_PARAMS'] == true ) {
                $this->params = INTACCTarray_merge($rtparams, $this->params);

                //  If the input params for start/end/asof are preferred, make sure the PERIOD is
                //   also set else the internals won't work.
                if ( ( isset($this->params['START_DATE']) || isset($this->params['END_DATE'])
                       || isset($this->params['ASOFDATE']) )
                     && ! isset($this->params['PERIOD']) ) {
                    $this->params['PERIOD'] = 'Current Month';    // Set to some value, not used anyway.
                }
            } else {
                $this->params = INTACCTarray_merge($this->params, $rtparams);
            }

            $this->title = ( $this->params['title'] ?: '' );
            $this->title2 = ( $this->params['title2'] ?: '' );
            $this->titlecomment = ( $this->params['titlecomment'] ?: '' );
        }

        parent::Reporter_Instantiate($showmemorized);

        //reportingBook filter is mandatory to run report in IGC company. If custom report doesnot have reportingBook, then assign here
        if ( empty($this->params['reportingBook']) && array_key_exists('BOOKID', $this->params) ) {
            if ( empty($this->params['BOOKID']) ) {
                $this->params['BOOKID'] = GetDefaultBook();
            }
            $this->params['reportingBook'] = $this->params['BOOKID'];
        }
        $return = true;
        return $return;
    }

    /**
     *  Converts stored memorized parameters for display
     */
    public function convertStoredParamatersForDisplay() : void
    {
        $this->params = FormatDateAndTimestampPromptsForDisplay($this->params);
    }


    /**
     * Shows report output in online mode.
     *
     * @param string $type
     * @param string $offline
     *
     * @return bool
     */
    function unusedShowReport($type = '', $offline = 'false')
    {
        //bypass for ICRW reports
        if ($this->params['op'] != GetOperationId('cerp/lists/crw')) {
            $nexusInfo = new NexusInfo();
            // add an extra check to verify if logged in user has access to run this report
            if (!$nexusInfo->canRunReport($this->params['_cr'])) {
                $gErr = Globals::$g->gErr;
                $gErr->addError('CRW-0006', __FILE__ . ":" . __LINE__, "You are not authorized to run this report");
                return false;
            }
        }
        return parent::ShowReport($type, $offline);
    }

    /**
     * Determines if report is invoked from UI or not.
     *
     * @return bool
     */
    function invokedFromUI()
    {
        if (isset($this->params['FROM_GATEWAY']) && $this->params['FROM_GATEWAY']) {
            return false;
        }
        return true;
    }

    /**
     * Transform time parameters
     *
     * @return string[]
     */
    function GetXSLTParams()
    {

        $params = parent::GetXSLTParams();
        $params['showprint'] = $this->params['fromdash'] ? 'N' : 'Y';

        $numberFormat = new CurrencyNumberFormat();
        $alignment = $numberFormat->getCurrencyAlignament();
        $symbol = $numberFormat->getCurrencySymbol();

        // get all curency stuff according to selection
        $params['decimal'] = $numberFormat->getDecimalSeparator(); //$separators['decimal_separator'];
        $params['thousands'] = $numberFormat->getThousandsSeparator(); //$separators['thousand_separator'];

        // for count we should not show prefix or suffix
        if ( isl_strstr(isl_strtolower($this->params['YAXIS']), 'count') ) {
            $params['prefix'] = '';
            $params['suffix'] = '';
        } else {
            $params['prefix'] = ( $alignment != CurrencyNumberFormat::CURRENCY_ALLIGNAMENT_INDIA && $this->nexusDB->value_type == 'currency' ) ? $symbol : '';
            $params['suffix'] = ( $alignment == CurrencyNumberFormat::CURRENCY_ALLIGNAMENT_INDIA && $this->nexusDB->value_type == 'currency' ) ? $symbol : '';
        }
        return $params;
    }

    /**
     * Let DoQuery and DoGraphQuery do date validation
     *
     * @return bool
     */
    function DoPreQueryProcessing()
    {
        return true;
    }

    /**
     * @param array  $dashFilter
     * @param string $dashFilterPath
     * @param array  $queryDef
     */
    public function createDashboardFilter($dashFilter, $dashFilterPath, &$queryDef) {
        $gManagerFactory = Globals::$g->gManagerFactory;
        unset($this->params[$dashFilterPath]);
        if ($dashFilter['DIMENSIONID']) {
            if ($dashFilter['DIMENSIONGROUP'] === '1') {
                $value = $this->getDimensionGroupFilterExpression($dashFilter);
                $queryDef['filters']['expressions'][] = [
                    'path'     => $dashFilterPath,
                    'operator' => 'in',
                    'value'    => $value,
                ];
            } else {
                $value = $dashFilter['DIMENSIONID'] ?? null;
                $queryDef['filters']['expressions'][] = [
                    'path'     => $dashFilterPath,
                    'operator' => 'eq',
                    'value'    => $value,
                ];
            }
            $pathArrayFormat = explode('.', $dashFilterPath);
            try {
                $entityMgr = $gManagerFactory->getManager(strtolower($pathArrayFormat[count($pathArrayFormat) - 2]));
            } catch (Exception $e) {
                $entMgr = $gManagerFactory->getManager(strtolower($pathArrayFormat[0]));

                if ($entMgr != null) {
                    if (! $entMgr->isCustomObject()) {
                        return;
                    }
                    $objDefId = $entMgr->getObjectDefinitionId();
                    $objDef = Pt_DataObjectDefManager::getById($objDefId);

                    for ($i = 1; $i < count($pathArrayFormat) - 1; $i++) {
                        $rel = Pt_RelationshipDefManager::getByName($pathArrayFormat[$i]);
                        $objDef = $rel->getOtherObjectDef($objDef->getId());
                    }
                    $entityMgr = $objDef->getEntityManager();
                } else {
                    throw new Exception("Invalid entity");
                }
            }
            $fieldInfo = $entityMgr->GetFieldInfo(strtoupper($dashFilter['DIMENSIONENTITY']) . "ID");
            if (!isset($fieldInfo)) {
                $this->params[$dashFilterPath] = str_replace(",", "#~#", $value);
            }
        }
    }

    /**
     * Forms dynamic query
     *
     * @return bool
     */
    function DoQuery()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $source = __FILE__ . " : " . __LINE__;
        XACT_BEGIN($source);

        $ok = true;
        if (empty($this->params['DATEFORMATTED'])) {
            $ok = $ok && $this->ProcessDateFilter($this->params);
        }

        // Get Query Definition from DB
        $ok = $ok && $this->GetQueryDef($queryDef, $rtparams);

        // if dashboard filters exist for dimensions, then override them
        $dashKey = Request::$r->_dashboardKey ?? Request::$r->_dashkey;
        $qryDashFilter = "select dashboardfilter from userbeans WHERE record# = :1 AND dashboardkey = :2 AND cny# = :3";
        $beanRecordNo = Request::$r->_beanKey ?? Request::$r->_dashBeanId;
        $userBeanDashFilterEnable = QueryResult([$qryDashFilter, $beanRecordNo, $dashKey, GetMyCompany()])[0];
        if (isset($queryDef) && $dashKey && $userBeanDashFilterEnable['DASHBOARDFILTER'] !== 'F') {
            $this->params['ASOFDATE'] = DashboardBaseEditor::getAsOfDateForStorage(Request::$r->_dashboardKey, null) ?? $this->params['ASOFDATE'];
            $dashFilters = DashboardBaseEditor::getFilters($dashKey);
            if (!empty($dashFilters)) {
                foreach ($dashFilters as $dashFilter) {
                    if (in_array($dashFilter['DIMENSIONENTITY'], ['location', 'department'])) {
                        unset($this->params[strtoupper($dashFilter['DIMENSIONENTITY'])]);
                    }
                    // check if the report has prompts for the current dashboard filter dimension, if it has then we create filters for those
                    foreach ($this->params['reportparams'] as $key => $paramValue) {
                        if (strpos($key, $queryDef['root'] . ".") === 0
                            && substr($key, strrpos($key, '.') + 1) === strtoupper($dashFilter['DIMENSIONENTITY']) . "ID") {
                            $this->createDashboardFilter($dashFilter, $key, $queryDef);
                            break;
                        }
                    }
                    // check if we have added to the report columns that contains the current dashboard filter dimension, if so, then we have to add it to the report filters
                    foreach ($queryDef['columns'] as $reportColumn => $colValue) {
                        if (strpos($reportColumn, strtoupper($dashFilter['DIMENSIONENTITY']) . "ID") !== false) {
                            $this->createDashboardFilter($dashFilter, $reportColumn, $queryDef);
                        }
                    }
                }
                // getting uncaught exceptions for PHP Version 8.1 if $queryDef['filters']['expressions'] is null
                // updated the code to check $queryDef['filters']['expressions'] is null if it is returning empty array
                $queryDef['filters']['expressions'] = array_values($queryDef['filters']['expressions'] ?? []);
            }
        }

        /** @noinspection PhpUndefinedVariableInspection */
        if ( in_array($queryDef['root'], $this->nexusDB->kSpecialObjectMap) ) {
            if ( isset($this->nexusDB->kSpecialObjectAsOffDateFunctionMap[$queryDef['root']]['Manager']) ) {
                $specialMgr = $this->nexusDB->kSpecialObjectAsOffDateFunctionMap[$queryDef['root']]['Manager'];
                $specialFunc = $this->nexusDB->kSpecialObjectAsOffDateFunctionMap[$queryDef['root']]['callfunction'];
                $ok = $ok && $specialMgr::$specialFunc($queryDef['root'], $this->params['ASOFDATE']);
            }

            if ( isset($this->nexusDB->kSpecialObjectRenderCalcFormulaFuncMap[$queryDef['root']]['Manager']) ) {
                $specialMgr = $this->nexusDB->kSpecialObjectRenderCalcFormulaFuncMap[$queryDef['root']]['Manager'];
                $specialFunc =
                    $this->nexusDB->kSpecialObjectRenderCalcFormulaFuncMap[$queryDef['root']]['callfunction'];
                /** @noinspection PhpUnusedLocalVariableInspection */
                $ok = $ok && $specialMgr::$specialFunc($queryDef, $this->nexusDB->kSpecialSelectColumns);
            }

            if ( isset($this->nexusDB->kSpecialObjectSetFormulaFuncMap[$queryDef['root']]['Manager']) ) {
                $specialMgr = $this->nexusDB->kSpecialObjectSetFormulaFuncMap[$queryDef['root']]['Manager'];
                $specialFunc = $this->nexusDB->kSpecialObjectSetFormulaFuncMap[$queryDef['root']]['callfunction'];
                $specialField = $this->nexusDB->kSpecialObjectSetFormulaFuncMap[$queryDef['root']]['field'];

                $specialMgr::$specialFunc($this->params[$specialField]);
            }

            if ( isset($this->nexusDB->kSpecialSetMCPFieldsContext[$queryDef['root']]['Manager']) ) {
                $specialMgr = $this->nexusDB->kSpecialSetMCPFieldsContext[$queryDef['root']]['Manager'];
                $specialFunc = $this->nexusDB->kSpecialSetMCPFieldsContext[$queryDef['root']]['callfunction'];

                if ( ! $specialMgr::$specialFunc($queryDef, $this->params) ) {
                    return false;
                }
            }
        }

        // if the user choose somehow not to show details, but we don't have summaries and groups,
        // we revert to details mode
        if ( $this->params['SHOW_DETAILS'] == 'N' && ! NexusInfo::CanShowGraphs($queryDef) ) {
            $this->params['SHOW_DETAILS'] = 'Y';
        }

        $pos = isl_strpos($queryDef['doctype'], "#~#");

        $root = $queryDef['root'];

        if ( $pos !== false && IsDocTypeOwnerObject(isl_strtolower($root)) ) {
            $ok = $this->FormMultiDocTypeCustomQuery($queryDef, $qry);
        } else {
            $ok = $this->FormCustomQuery($queryDef, $qry, false, $sort, $select, $subtotals, $summary);
        }

        if ( ! $ok ) {
            XACT_ABORT($source);
            return false;
        }

        //  Query may be empty (e.g. for XSD).
        if (!$qry) {
            XACT_COMMIT($source);
            $this->rawdata = null;
            return true;
        }

        //making SCM reports compatible with AP/AR reports by starting line no's with 1
        if ( isl_strpos($qry, "DOCUMENTENTRY") ) {
            $qry = str_replace("DOCUMENTENTRY.lineno", "DOCUMENTENTRY.lineno + 1", $qry);
        }

        // If we include primarydoclines in the SCM reports ensure that the line numbers start with 1 similar to the above
        if ( isl_strpos($qry, "PRIMARYDOCLINEKEY") ) {
            $qry = str_replace("PRIMARYDOCLINEKEY.lineno", "PRIMARYDOCLINEKEY.lineno + 1", $qry);
        }

        $ent = strtolower($root);
        $entityMgr = $gManagerFactory->getManager($ent);
        $maxNum = $entityMgr->getReportLimit($this);
        if ($maxNum > 0) {
            $qry .= " FETCH FIRST $maxNum ROWS ONLY";
        }
        $readAudHistFromSnowFlake = false;
        if ( ($root === 'AUDITHISTORY' || $root === 'ADVAUDITHISTORY') &&
             FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('AUDIT_HISTORY_REPORT_READ_FROM_SNOWFLAKE_ENABLED')) {
            $readAudHistFromSnowFlake = true;
        }
        
        if ( is_array($qry) ) {
            foreach ( $qry as $qryItem ) {
                //Get data
                if ($readAudHistFromSnowFlake) {
                    
                    $qargs = [$qryItem, GetMyCompany()];
                    $qargs[0] = str_replace('cny#', 'cny_', $qargs[0]);
                    $sqlData =
                        SnowflakeQueryResult($qargs, 0);
                }
                else {
                    $sqlData =
                        QueryResult([ $qryItem, GetMyCompany() ], 0, '', null, true,
                            true, false, 'Please check the report definition.');
                }
                
                if (is_array($sqlData)) {
                    $this->rawdata = array_merge( (array)$this->rawdata, (array)$sqlData );
                }
            }
        } else {
            //Get data
            if ($readAudHistFromSnowFlake) {
                
                $qargs = [$qry, GetMyCompany()];
                $qargs[0] = str_replace('cny#', 'cny_', $qargs[0]);
                $sqlData =
                    SnowflakeQueryResult($qargs, 0);
            }
            else {
                $sqlData =  QueryResult([ $qry, GetMyCompany() ], 0, '', null, true, true, false, 'Please check the report definition.');
                
            }
            if (is_array($sqlData)) {
                $this->rawdata = $sqlData;
            }
        }

        if ($root === 'USERRIGHTS') {
            // for user rights we need to translate keys if we have terminology redefined
            $colId = 0;
            $policyColId = '';
            foreach ($queryDef['columns'] as $colKey => $colData) {
                // the translation takes place only for policy
                if ($colKey === 'USERRIGHTS.POLICY') {
                    $policyColId = 'C'.$colId;
                    break;
                }
                $colId++;
            }
            if ($policyColId !== '') {
                $dict = Dictionary::getInstance();
                foreach ($this->rawdata as $rawkey => $dataline) {
                    $this->rawdata[$rawkey][$policyColId] = $dict->GetRenamedText($dataline[$policyColId]);
                }
            }
        }

        if (explode('-', I18N::getLocale())[0] != 'en') {
            foreach ($this->rawdata as $rawKey => $rawValues) {
                foreach ($rawValues as $key => $value) {
                    if (!is_numeric($value) && !is_null($value)) {
                        $dbValue = DBTokensHandler::getInstance()->getExternalLabel($value);
                        if ($dbValue == $value) {
                            $dbValueLower = DBTokensHandler::getInstance()->getExternalLabel(ucwords(strtolower($value)));
                            if ($dbValueLower != ucwords(strtolower($value))) {
                                $dbValue = $dbValueLower;
                            }
                        }
                        $this->rawdata[$rawKey][$key] = $dbValue;
                    }
                }
            }
        }

        // Justes in cases
        $this->nexusDB->unaliasLongFieldNamesInQueryResults($this->rawdata);

        //eppp_p($this->rawdata);
        //Check for Manipulation of raw data
        $mfun = $entityMgr->_schemas[$ent]['postprocess'];
        if ( isset($mfun) && $mfun != '' ) {
            $mObj = $entityMgr->_schemas[$ent]['postProcessObj'];
            if ($mObj && is_object($mObj)) {
                $mObj->$mfun($this, $root, $this->rawdata);
            } else {
                $this->$mfun($root, $this->rawdata);
            }
        }

        //  Decrypt any encrypted fields.
        $this->decryptRecs();

        /*
                if(count($this->rawdata) > MAX_REPORT_RECORDS) {
                    global $gErr;
                    $gErr->AddError('BL03002069', __FILE__.":".__LINE__, "");
                    eppp("Records returned are more than ".MAX_REPORT_RECORDS);
                    XACT_ABORT($source);
                    return false;
                }
        */
        // preprocess the data after we got them from the DB
        // this can be used to decrypt encoded data, reformat data, etc..
        if ( is_array($this->postProcessFields) && count($this->postProcessFields) ) {
            $ok = $ok && $this->postProcessData();
        }

        if ( $ok ) {
            XACT_COMMIT($source);
        } else {
            XACT_ABORT($source);
        }

        return $ok;
    }

    /**
     * Decrypt any encrypted fields for the results.
     */
    private function decryptRecs()
    {
        $aliases = $this->nexusDB->GetAllFieldAlias();
        foreach ($this->nexusDB->nexustree as $name => $node) {
            $pieces = explode('.', strtolower($name));
            $entityName = $pieces[count($pieces)-1];
            try {
                $entityMgr = Globals::$g->gManagerFactory->getManager($entityName);
                $entityMgr->decryptRecs($this->rawdata, $aliases);
            } catch (Exception $e) {
                LogToFile("Could not find nexus object type $entityName for custom report field decryption");
            }
        }
    }

    /**
     * @return bool
     */
    private function postProcessData()
    {
        $ok = $this->transformData();

        return $ok;
    }

    /**
     * @return bool
     */
    private function transformData()
    {
        // Let's loop through all the lines in resultset and transform the data
        foreach ( $this->rawdata as &$line ) {
            foreach ( $this->postProcessFields as $alias => $fieldInfo ) {
                // Transform
                $func = $fieldInfo['transformFunction'];
                $line[$alias] = $func($line[$alias]);
            }
        }

        return true;
    }

    /** @noinspection PhpUnusedPrivateMethodInspection - called indirectly through 'postprocess' configuration
     *
     * @param string     $root
     * @param string[][] &$rawdata
     */
    private function manipulateRawData($root, &$rawdata)
    {
        $fieldAliases = $this->nexusDB->GetAllFieldAlias();
        if ( $root == 'VENDORVISIBILITY' ) {
            $ID = 'VENDORVISIBILITY.VENDORID';
        } else if ( $root == 'CUSTOMERVISIBILITY' ) {
            $ID = 'CUSTOMERVISIBILITY.CUSTOMERID';
        } else if ( $root == 'SAASCUSTOMERCOUNT' ) {
            $ID = 'SAASCUSTOMERCOUNT.MONTHCUST';
        }
        /** @noinspection PhpUnusedLocalVariableInspection */
        $vids = [];
        $resArr = [];
        foreach ( $rawdata as $line ) {
            foreach ( $line as $c => $keyval ) {
                /** @noinspection PhpUndefinedVariableInspection */
                $vid = $line[$fieldAliases[$ID]];
                if ( ! isset($resArr[$vid][$c]) || ! in_array($keyval, $resArr[$vid][$c]) ) {
                    $resArr[$vid][$c][] = $keyval;
                }
            }
        }
        $rawdata = [];
        $i = 0;
        foreach ( $resArr as $value ) {
            foreach ( $value as $k => $raw ) {
                if ( count($raw) == 1 ) {
                    $rawdata[$i][$k] = $raw[0];
                } else {
                    $raw = array_diff($raw, [ '' ]);
                    $rawdata[$i][$k] = implode(' ; ', $raw);
                    /** @noinspection PhpUndefinedVariableInspection */
                    if ( ! in_array($keyval, $resArr[$vid][$c]) ) {
                        $resArr[$vid][$c][] = $keyval;
                    }
                }
            }
            $i++;
        }
    }


    /** @noinspection PhpUnusedPrivateMethodInspection - called indirectly through 'postprocess' configuration
     *
     *      This version of 'manupulate raw data' calls the entity to do the manip of raw data.
     *      The entity must be in the root name.  The entity function is 'postProcessCustomReportResults'
     *
     * @param string     $root
     * @param string[][] &$rawdata
     */
    private function entitymanipulaterawdata($root, &$rawdata)
    {
        $theManager = Globals::$g->gManagerFactory->getManager(strtolower($root));
        if ($theManager) {
            $theManager->postProcessCustomReportResults($this, $rawdata);
        }
    }


    /** @noinspection PhpUnusedPrivateMethodInspection
     *
     * @param bool $buildSubtotals  unused function
     *
     */
    private function buildMatrixData($buildSubtotals)
    {
        //TODO obsolete - seems like an unused function
        // $this->rawdata contains the data in vertical format as read from the DB
        // we need to convert that data into a matrix format.
        // We assume that db data is sorted by dimensions in the correct order.
        //
        // DB data:
        //
        // Customer      Project   Time      Value
        // =======================================
        // a1            b1        c1        1
        // a1            b1        c2        2
        // a1            b2        c2        3
        // a1            b2        c3        4
        // a2            b1        c1        5
        // a2            b1        c2        6
        // a2            b2        c1        7
        // a2            b2        c2        8
        // a3            b1        c1        9
        // a3            b1        c2        10
        // a3            b2        c1        11
        // a3            b2        c2        12
        //
        // Matrix data (subtotals are not included):
        //
        // Customer Project\Time  |     c1       c2
        // ========================================
        // a1       b1            |      1        2
        //          b2            |      3        4
        // a2       b1            |      5        6
        //          b2            |      7        8
        // a3       b1            |      9       10
        //          b2            |     11       12

    }

    /**
     * Form map for the query
     *
     * @return array
     */
    function DoMap()
    {
        $_op = Request::$r->_op;
        $_sess = Session::getKey();

        $lines = [];
        $lines['report']['0']['sess'] = $_sess;
        $lines['report']['0']['op'] = $_op;
        if ( $this->_isindividualloc || $this->_isindividualdept || $this->_isindividualterr ) {
            $lines['report']['0']['co'] = Profile::getCompanyCacheProperty('company', 'NAME');
        } else {
            $lines['report']['0']['co'] = GetMyCompanyName();
        }
        $lines['report']['0']['title'] = $this->title;
        $lines['report']['0']['title2'] = $this->title2;
        $lines['report']['0']['titlecomment'] = $this->titlecomment;
        $orientation = 'Portrait';
        if ( isset($this->params['ORIENTATION']) && $this->params['ORIENTATION'] != '' ) {
            $orientation = $this->params['ORIENTATION'];
        }
        $lines['report']['0']['orientation'] = $orientation;

        $lines['report']['0']['ismegl'] = ( IsMultiEntityCompany() ) ? 'Y' : 'N';
        $lines['report']['0']['companyid'] = GetMyCompany();
        $lines['report']['0']['today'] = GetCurrentDate(IADATE_USRFORMAT);

        $lines['report']['0']['reportdate'] = GetCurrentDate(IADATE_USRFORMAT);
        if ( Profile::getUserCacheProperty('USERPREF', 'TIMEFORMAT') == 'HH24:MI:SS' ) {
            $lines['report']['0']['reporttime'] = date("G:i T");
        } else {
            $lines['report']['0']['reporttime'] = date("g:i A T");
        }
        $lines['report']['0']['hasSubtotals'] = ! $this->export && $this->HasSubtotals() ? '1' : '0';

        $lines['report']['0']['customObject'] = false;
        $root = $this->queryDef['root'];
        if ( startsWith($root, 'PT_') ) {
            $objDefName = isl_substr($root, 3);
            $objDef = Pt_DataObjectDefManager::getByName($objDefName);
            if ( $objDef != null ) {
                $page = Pt_WebPageManager::getBaseVersion($objDef->getId(), TYPE_VIEW);
                if ( $page != null ) {
                    $lines['report']['0']['customObject'] = true;
                    $lines['report']['0']['pageId'] = $page->getId();
                }
            }
        }

        // No data message
        //eppp_p($this->rawdata);
        if (empty($this->rawdata)) {
            $reportdata = [];
            $reportdata['NODATA']['0'] = [ 'NODATA' => '1' ];
            $lines['report']['0']['ENTITY'] = $reportdata;

            return $lines;
        }

        //adding location context
        $lines['report']['0']['locationcontext'] = (GetContextLocation())?: '';

        // For group reporter
        if ( $this->_isindividualloc || $this->_isindividualdept || $this->_isindividualterr ) {
            $lines['report']['0']['location'] = $this->_currloc;
            $lines['report']['0']['department'] = $this->_currdept;
            $lines['report']['0']['territory'] = $this->_currterr;
        }

        // Obtain Column Labels
        $headers = $this->nexusUI->GetColumnLabels($this->isMatrixReport);
        //eppp_p($headers);
        //dieFL();

        $this->nexusDB->GetNonStandardVIDColumnInfo($headers);
        //eppp_p($headers);
        //dieFL();

        $this->nexusDB->GetNonStandardDrilldownInfo($headers);
        //eppp_p($headers);
        //dieFL();

        // Obtain index of MEGA column (location)
        if ( IsMultiEntityCompany() ) {
            $this->nexusDB->GetMEGAColumnInfo($headers);
            //eppp_p($headers);
            //dieFL();
        }
        //eppp_p($headers);
        //dieFL();

        // Do not show subtotal and grandtotal if export format is selected
        // Collect grand totals
        $grandtotals = [];
        if (( ! $this->export ) && ($this->GetOutputType() != kShowCSV)) {
            $grandtotals = $this->nexusUI->GetGrandTotals($this->rawdata);
            //eppp_p($grandtotals);
            //dieFL();
        }

        //  Figure any runtime column values.
        if (!empty($this->runtimeColumns)) {
            $this->nexusUI->figureRuntimeColumnValues($this->rawdata, $this->runtimeColumns);
        }

        //eppp_p($this->export);
        //eppp_p($this->reportType);
        //dieFL();

        // Form Data XML Structure
        if ( ! $this->export && $this->HasSubtotals() ) {
            // Nexus Path => Field Name in result set
            $fieldAliases = $this->nexusDB->GetAllFieldAlias();
            //eppp_p($this->userCalculatedColumns);
            if ( isset($this->userCalculatedColumns) ) {
                foreach ( $this->userCalculatedColumns as $id => $detail ) {
                    $fieldAliases[$id] = $detail;
                }
            }
            //eppp_p($fieldAliases);
            //dieFL();

            //eppp_p($this->rawdata);
            //dieFL();

            // Reorganize map by subtotals
            $newdata = $this->nexusUI->ProcessDataForSummaryReport($this->rawdata, $fieldAliases);
            //eppp_p('newdata');
            //eppp_p($newdata);
            //dieFL();

            $reportdata = [
                'CS' => [
                    [
                        'C' => $headers,
                    ],
                ],
            ];

            if ( count($newdata) > 0 ) {
                $reportdata['STS'] = [
                    [
                        'ST' => $newdata,
                    ],
                ];
            }
        } else {
            $reportdata = [
                'CS' => [
                    [
                        'C' => $headers,
                    ],
                ],
                'RS' => [
                    [
                        'R' => $this->rawdata,
                    ],
                ],
            ];
        }

        // Optional
        /** @noinspection PhpUndefinedVariableInspection */
        if ( count($grandtotals) > 0 ) {
            $reportdata['GTS'] = [
                [
                    'R' => $grandtotals,
                ],
            ];
        }

        $lines['report']['0']['ENTITY'] = $reportdata;
        if ( count($grandtotals) > 0 || ( ! $this->export && $this->HasSubtotals() )) {
            $this->hasExtraColumn = true;
            $shift = false;
            if ($this->GetOutputType() !== kShowCSV) {
                $shift = true;
            } else if ($this->params['SHOW_DETAILS'] == 'N') {
                $shift = true;
            } else {
                $this->hasExtraColumn = false;
            }
            if ($shift) {
               array_unshift($lines['report']['0']['ENTITY']['CS'][0]['C'],
                              [ 'L' => '', 'DS' => 15, 'HS' => '', 'DD' => 'N', 'MOD' => '', 'VOP' => '' ]);
                foreach ( $lines['report']['0']['ENTITY']['CS'][0]['C'] as $key => $col ) {
                    if ( $col['LOCIDX'] ?? null ) {
                        $col['LOCIDX']++;
                    }
                    if ( $col['VIDIDX'] ?? null ) {
                        $col['VIDIDX']++;
                    }
                    $lines['report']['0']['ENTITY']['CS'][0]['C'][$key] = $col;
                }

                if ( isset($lines['report']['0']['ENTITY']['RS']) && is_array($lines['report']['0']['ENTITY']['RS']) ) {
                    foreach ( $lines['report']['0']['ENTITY']['RS'] as $key => $dt ) {
                        foreach ( $dt['R'] as $rowKey => $row ) {
                            $newRow = array_merge([ 'RL' => '' ], $row);
                            $dt['R'][$rowKey] = $newRow;
                        }
                        $lines['report']['0']['ENTITY']['RS'][$key] = $dt;
                    }
                }

                if ( isset($lines['report']['0']['ENTITY']['STS'][0]['ST'])
                     && is_array($lines['report']['0']['ENTITY']['STS'][0]['ST']) ) {
                    foreach ( $lines['report']['0']['ENTITY']['STS'][0]['ST'] as $key => $st ) {
                        foreach ( $st['R'] as $rowKey => $row ) {
                            $newRow = array_merge([ 'RL' => '' ], $row);
                            $st['R'][$rowKey] = $newRow;
                        }
                        $lines['report']['0']['ENTITY']['STS'][0]['ST'][$key] = $st;
                    }
                }
            }

            //  If we need the grouping info at a higher level, save it (e.g. readReport).
            if ( isset($this->nexusUI->params['GROUP_INFO']) ) {
                $lines['report']['0']['ENTITY']['GROUP_INFO'] = $this->nexusUI->params['GROUP_INFO'];
            }
            //eppp_p($lines); dieFL();
        }
        $id = '';
        if (isset($this->params['title'])) {
            $id = $this->params['title'];
        } else if (isset($this->params['title2'])) {
            $id = $this->params['title2'];
        } else if (isset($this->params['_cr'])) {
            $id = $this->params['_cr'];
        }
        $this->metricCrwReport->setId($id);
        $this->metricCrwReport->setResult($this->GetOutputType());
        $this->metricCrwReport->setObject($root);
        if ( isset($lines['report']['0']['ENTITY']['RS']['0']['R'])) {
            $this->metricCrwReport->setLineCount(count($lines['report']['0']['ENTITY']['RS']['0']['R']));
        } else {
            $this->metricCrwReport->setLineCount(0);
        }
        if ( isset($lines['report']['0']['ENTITY']['CS']['0']['C'])) {
            $this->metricCrwReport->setCount(count($lines['report']['0']['ENTITY']['CS']['0']['C']));
        } else {
            $this->metricCrwReport->setCount(0);
        }
        
        if ( $this->metricCrwReport->getCount() > 0
            && isset($lines['report'][0]['ENTITY']['CS'][0]['C'][2]['L'])
            && $lines['report'][0]['ENTITY']['CS'][0]['C'][2]['L'] === 'IA.LAST_DELIVERY'
            && $this->metricCrwReport->getLineCount() > 0
        ) {
            foreach ($lines['report'][0]['ENTITY']['RS'][0]['R'] as $k => $v) {
                if (!empty($v['C4']) && is_string($v['C4'])) {
                    $pos = stripos($v['C4'], 'Unsuccessful');
                    if ($pos !== false) {
                        $extLabel = DBTokensHandler::getInstance()->getExternalLabel('Unsuccessful');
                        $extSupId = DBTokensHandler::getInstance()->getExternalLabel('Support ID');
                        $lines['report'][0]['ENTITY']['RS'][0]['R'][$k]['C4'] = str_ireplace(['Unsuccessful', 'Support ID'],
                                [$extLabel, $extSupId], $v['C4']);
                    } elseif (str_contains($v['C4'], 'IA.')) {
                        $lines['report'][0]['ENTITY']['RS'][0]['R'][$k]['C4'] = $this->textMap[$v['C4']] ?? $v['C4'];
                    } else {
                        $extLabel = DBTokensHandler::getInstance()->getExternalLabel($v['C4']);
                        $lines['report'][0]['ENTITY']['RS'][0]['R'][$k]['C4'] = $extLabel;
                    }
                }
            }
        }

        $this->metricCrwReport->setType($this->params['offline_mode'] ? 'offline' : 'online');
        return $lines;
    }

    /**
     * Create map which defines property of each column
     *
     * @return array
     */
    function DoXSDMap()
    {
        //eppp_p($this->nexusDB->queryDef['columns']);
        //dieFL();

        $columns = $this->nexusDB->GetReportDef();
        //eppp_p($columns);
        //dieFL();

        $map = [
            'COLUMN' => $columns,
        ];

        return $map;
    }

    /**
     * Handles query requirement for graph creation
     *
     * @return bool
     */
    function DoGraphQuery()
    {
        //eppp_p("DoGraphQuery");
        //dieFL();

        $source = __FILE__ . " : " . __LINE__;
        XACT_BEGIN($source);

        $ok = true;
        //eppp_p($this->params);
        //dieFL();
        //impp(pp($this->params));

        $allFieldInfo = [];
        $detailFieldInfo = $this->nexusUI->GetDetailFieldInfo();

        $ok = $ok && $this->ProcessDateFilter($this->params);
        //eppp_p("ProcessDateFilter ok:$ok");
        //dieFL();

        // Get Query Definition from DB
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ok = $ok && $this->GetQueryDef($queryDef, $rtparams);
        //eppp_p($rtparams);
        //eppp_p($queryDef);
        //dieFL();

        // Remove graph irrelevant selection before initiation
        $queryDef['columns'] = [];
        $queryDef['sortby'] = [];

        // Common Validation and Initialization
        $ok = $this->Validate($queryDef, $allFieldInfo);
        //eppp_p($queryDef);
        //dieFL();

        // Do *not* proceed if validations have failed
        if ( ! $ok ) {
            XACT_ABORT($source);

            return false;
        }

        $this->multiRegion = $this->nexusUI->IsMultiRegionGraph();
        //eppp_p($this->multiRegion);
        //dieFL();

        $this->nexusUI->SetMultiRegion($this->multiRegion);
        $this->nexusDB->SetMultiRegion($this->multiRegion);

        // Form 'select' clause
        $select = [];
        //eppp_p($allFieldInfo);
        //dieFL();

        //eppp_p($this->params);
        //dieFL();

        //eppp_p($this->nexusDB->nexustree);
        //dieFL();

        //-----------------------------------------//
        // Sequence of select calls is important   //
        //-----------------------------------------//
        $ok = $ok && $this->nexusDB->GetGraphSelectClause($select, $allFieldInfo, $detailFieldInfo);
        //eppp_p($select);
        //dieFL();

        // Form 'from' clause
        $from = [];
        $ok = $ok && $this->nexusDB->GetFromClause($from);
        //eppp_p($from);
        //dieFL();

        // Form 'where' clause
        $where = [];
        $ok = $ok && $this->nexusDB->GetWhereClause($where);
        //eppp_p($where);
        //dieFL();

        // Form advance filters
        $filter = [];
        $ok = $ok && $this->nexusDB->GetAdvancedFilterClause($filter, $allFieldInfo, $detailFieldInfo);
        //eppp_p($filter);
        //dieFL();
        $where = INTACCTarray_merge($where, $filter);

        // Form user input filters
        $userFilter = [];
        $ok = $ok && $this->nexusDB->GetUserInputFilterClause($userFilter, $allFieldInfo);
        //eppp_p($userFilter);
        //dieFL();
        $where = INTACCTarray_merge($where, $userFilter);

        // Form group by
        $groupby = [];
        $ok = $ok && $this->nexusDB->GetGroupbyClause($groupby);
        //eppp_p($groupby);
        //dieFL();

        // Form 'order by' clause
        $sort = [];
        $ok = $ok && $this->nexusDB->GetGraphOrderByClause($sort);
        //eppp_p($sort);
        //dieFL();

        $ok = $ok && $this->nexusDB->postProcessUserInputFilterClause($where);

        if ( ! $ok ) {
            XACT_ABORT($source);

            return false;
        }
        //eppp_p($select);
        //eppp_p($from);
        //eppp_p($where);
        //eppp_p($groupby);
        //eppp_p($sort);
        //dieFL();

        // Form complete query
        $qry = 'select ' . join(', ', $select) .
               ' from ' . join(', ', $from) .
               ' where ' . join(' and ', $where) .
               ' group by ' . join(', ', $groupby);

        if ( !empty($sort) ) {
            $qry .= ' order by ' . join(', ', $sort);
        }
        //eppp_p($qry);
        //eppp_p(GetMyCompany());
        //dieFL();

        /** @noinspection PhpUnusedLocalVariableInspection */
        $qrysize = isl_strlen($qry);
        //eppp_p($qrysize);

        // Get data
        $this->rawdata = QueryResult([ $qry, GetMyCompany() ]);
        //eppp_p($this->rawdata);
        //dieFL();

        if ( countArray($this->rawdata) > MAX_GRAPH_RECORDS ) {
            global $gErr;
            $gErr->addError('CRW-0018', __FILE__ . ":" . __LINE__, "");
            eppp("Records returned are more than " . MAX_REPORT_RECORDS);
            XACT_ABORT($source);

            return false;
        }

        if ( $ok ) {
            XACT_COMMIT($source);
        } else {
            XACT_ABORT($source);
        }

        return $ok;
    }

    /**
     * Handles map requirement for graph creation
     *
     * @return array|true
     */
    function DoGraphMap()
    {
        //eppp_p("DoGraphMap");
        //eppp_p($this->multiRegion);
        //dieFL();

        //eppp_p($this->params);
        /** @noinspection PhpUnusedLocalVariableInspection */
        $type = $this->params['type'];
        //eppp_p($type);
        //dieFL();

        $rawdata = &$this->rawdata;
        //eppp_p($rawdata);
        //dieFL();

        if (empty($rawdata)) {
            $this->noData = true;

            return true;
        }

        // Common attributes
        $graphparams = $this->nexusUI->GetGraphParams();
        //eppp_p($graphparams);
        //dieFL();

        // Start map with graph params
        /* @var array $map */
        $map = $graphparams;

        if ( $this->multiRegion ) {
            // Multi region graph
            $this->DoMultiRegionMap($map);
        } else {
            // Single region graph
            $this->DoSingleRegionMap($map);
        }
        //eppp_p($map);
        //dieFL();

        return $map;
    }

    /**
     * Single region map
     *
     * @param array &$map
     *
     * @return bool
     */
    function DoSingleRegionMap(&$map)
    {
        //eppp_p('DoSingleRegionMap');
        //dieFL();

        $rawdata = &$this->rawdata;
        //eppp_p($rawdata);

        //eppp_p($map);
        //dieFL();

        $this->nexusUI->GetSingleRegionGraphData($rawdata, $map, $this->nexusDB->value_type);
        //eppp_p($map);
        //dieFL();

        $map['SINGLEREGION'][0]['POINT'] = $rawdata;

        return true;
    }

    /**
     * Multi region map
     *
     * @param array &$map
     *
     * @return bool
     */
    function DoMultiRegionMap(&$map)
    {
        //eppp_p("DoMultiRegionMap");
        //eppp_p($map);
        //dieFL();

        $rawdata = &$this->rawdata;
        //eppp_p($rawdata);

        $this->nexusUI->GetMultiRegionGraphData($rawdata, $map);
        //eppp_p($map);
        //dieFL();

        return true;
    }

    /**
     * This function returns the names of the standard filters for the current root.  These filter names
     *    can be used by external calling functions (e.g. the readReport API) as additional 'fixed' runtime parameters.
     *
     * @param string $useRoot
     *
     * @return string[]
     */
    function GetStandardFilterNames($useRoot = '')
    {
        $out = [];
        if ( $this->nexusUI ) {
            if ( $useRoot == '' ) {
                $useRoot = $this->queryDef['root'];
            }

            //  Get the standard fields.  Interpret PERIOD and MEGAFILTERS, and return others as is.
            $names = $this->nexusUI->GetStandardFilterFieldsOnly($useRoot);
            foreach ( $names as $nextName ) {
                if ( $nextName == 'PERIOD' ) {
                    $out[] = 'REPORTINGPERIOD';
                } else if ( $nextName == 'MEGAFILTERS' ) {
                    $out[] = 'LOCATION';
                    $out[] = 'DEPARTMENT';
                } else {
                    $out[] = $nextName;
                }
            }
        }

        return $out;
    }

    /**
     * This function allows for the resetting of the list delimiter used when parsing values for enums,
     *   radios, multienums, webcombo, and multipick data types.  By default, the delimiter is a special
     *   character sequence (currently '#~#'), but for user-facing functions (e.g. APIs) it can be reset
     *   to something simpler (e.g. a comma).
     *
     * @param string $newDelimiter
     */
    function SetListDelimiter($newDelimiter)
    {
        if ( $this->nexusUI ) {
            $this->nexusUI->SetListDelimiter($newDelimiter);
        }
    }

    /**
     * Validate a given report path parameter.
     *
     * @param string $path
     *
     * @return bool
     */
    function ValidateParamPath($path)
    {
        //  See if it is one of the paths defined for this report.
        $ok = $this->nexusDB->ValidateParamPath($path);
        if ( $ok ) {
            return true;
        }

        //  Allow the variants of PERIOD - START_DATE, END_DATE, ASOFDATE.  These are processed by
        //   GroomRequestParams.
        if ( in_array($path, [ 'START_DATE', 'END_DATE', 'ASOFDATE' ]) ) {
            return true;
        }

        //  See if the path is one of the standard filters.
        $stdFields = $this->GetStandardFilterNames();
        if ( in_array($path, $stdFields) ) {
            return true;
        }

        //  If not, see if it is one of the 'special' paths.
        $specialFields = $this->nexusUI->GetSpecialParamPaths();

        return empty($specialFields[$path]) ? false : true;
    }

    /**
     * Validates and Initializes
     *
     * @param array &$queryDef
     * @param array &$allFieldInfo
     *
     * @return bool
     */
    function Validate(&$queryDef, &$allFieldInfo)
    {
        $ok = true;
        //impp("CustomReport::Validate");
        //impp("queryDef ".pp($queryDef));
        global $_dump;
        if ( $_dump ) {
            eppp_p($queryDef);
        }
        //dieFL();

        $outputtype = $this->GetOutputType();
        //eppp_p($outputtype);

        // Add document type join as user input value
        $ok = $ok && $this->nexusUI->ProcessDocType($queryDef, $this->params);
        //impp("ProcessDocType ok:$ok");
        //eppp_p($this->params);
        //eppp_p($queryDef);
        //dieFL();

        // If user input can not be directly mapped to one of the fields or root object
        $ok = $ok && $this->nexusUI->HandleSpecialCaseUserInputs($queryDef, $this->params);
        //impp("HandleSpecialCaseUserInputs ok:$ok");
        //eppp_p($this->params);
        //eppp_p($queryDef);
        //dieFL();

        $this->reportType = $queryDef['reporttype'];
        $this->isMatrixReport = $this->reportType == 'matrix';

        // Set Query Def in context
        $this->nexusUI->SetQueryDef($queryDef);

        // Validate basic Nexus compliance
        $ok = $ok && $this->nexusUI->Validate();
        //impp("nexusUI->Validate ok:$ok");
        // Parameters names are required for UI prcessing
        $this->nexusUI->SetParams($this->params);
        $this->nexusUI->SetDocType($queryDef['doctype']);

        // Resolve field info
        $ok = $ok && $this->nexusUI->ProcessQueryDef();

        // Validate Query Def for UI info
        $ok = $ok && $this->nexusUI->ValidateQueryDef();

        // Validate Query Def for UI info
        $ok = $ok && $this->nexusUI->ValidateRequestParams($this->params);

        // Pre-process filters
        $ok = $ok && $this->nexusUI->PreprocessFilters($queryDef);
        //eppp("after PreprocessFilters");
        //eppp($queryDef);

        // Process parameter values based on the type
        $ok = $ok && $this->nexusUI->GroomRequestParams($this->params);
        //eppp_p($this->params);
        //dieFL();

        // If a request parameter needs to be processed based on root object
        $ok = $ok && $this->nexusUI->HandleSpecialCaseRequestParams($this->params);
        //eppp_p($this->params);
        //dieFL();

        // Set updated parameter values
        $this->nexusUI->SetParams($this->params);

        $allFieldInfo = $this->nexusUI->GetAllFieldInfo();
        $detailFieldInfo = $this->nexusUI->GetDetailFieldInfo();

        $this->nexusDB->SetQueryDef($queryDef);
        $this->nexusDB->SetDocType($queryDef['doctype']);
        $this->nexusDB->SetParams($this->params);
        $this->nexusDB->SetXMLExport(( $outputtype == kXMLExport || $outputtype == kXMLExportArray ));

        // Don't use short aliases for XML export, as full alias names become XML attributes
        $useShortAliases =
            ! ( $outputtype == kXMLExport || $outputtype == kXSDExport || $outputtype == kXMLExportArray );
        $this->nexusDB->SetShortFieldAlias($useShortAliases, true);

        // Forms tree
        $ok = $ok && $this->nexusDB->ProcessQueryDef($allFieldInfo, $detailFieldInfo);

        // Validate Query Def for DB info
        $ok = $ok && $this->nexusDB->ValidateQueryDef();
        //eppp_p($this->nexusDB->nexustree);
        //dieFL();

        // Validate Query Def for DB info
        $ok = $ok && $this->nexusDB->ValidateRequestParams($this->params);
        //dieFL();

        //  If requested, validate these given parameter paths (used by API to make sure given paths are
        //   valid, since the existing params structure doesn't lend itself well to such validation).

        //  If we haven't fetched the standard filter names, do so now.
        if ( ! is_array($this->standardFilterNames) ) {
            $this->standardFilterNames = $this->nexusDB->GetStandardFilterNames();
        }

        if ( ! empty($this->params['validate_paths']) ) {
            foreach ( $this->params['validate_paths'] as $nextPath ) {
                if ( $this->ValidateParamPath($nextPath) !== true ) {
                    global $gErr;
                    $gErr->addIAError('CRW-0019', __FILE__ . ":" . __LINE__, "Invalid report argument '" . $nextPath . "'", ['NEXT_PATH' => $nextPath] );
                    $ok = false;
                }
            }
        }

        return $ok;
    }

    /**
     * Standard routine doesn't generate efficient data xml for multiregion graphs
     *
     * @param array &$_params
     *
     * @return string
     */
    function GenerateXML(&$_params)
    {
        //eppp_p("GenerateXML");
        //eppp_p($this->multiRegion);
        $localParams = $_params;
        if ( $this->multiRegion ) {
            return $this->GenerateMultiRegionXML($localParams);
        }

        return parent::GenerateXML($localParams);
    }

    /**
     * Efficient dataxml for mutli region graphs
     *
     * @param array $_params
     *
     * @return string
     */
    function GenerateMultiRegionXML($_params)
    {
        $graphtype = $_params['GRAPHTYPE'];
        $xorient = $_params['XORIENT'];
        $fontsize = $_params['FONTSIZE'];
        $category = $_params['CATEGORY'];
        $regions = $_params['REGION'];
        $legend = $_params['LEGEND'];
        $slantlabels = $_params['SLANTLABELS'];
        $showlegend = $_params['SHOWLEGEND'];
        $linethickness = $_params['LINETHICKNESS'];
        $divlineisdashed = $_params['DIVLINEISDASHED'];
        $numvdivlines = $_params['NUMVDIVLINES'];

        $xml = "<?xml version='1.0' encoding='UTF-8'?>";
        $xml .= "<reportdata GRAPHTYPE='$graphtype'";
        $xml .= " XORIENT='$xorient'";
        $xml .= " FONTSIZE='$fontsize'";
        $xml .= " LEGEND='$legend'";
        $xml .= " SLANTLABELS='$slantlabels'";
        $xml .= " SHOWLEGEND='$showlegend'";
        if ( $graphtype == 'Line' || $graphtype == 'Area' || $graphtype == 'Stacked area' ) {
            $xml .= " LINETHICKNESS='$linethickness'";
            $xml .= " DIVLINEISDASHED='$divlineisdashed'";
            $xml .= " NUMVDIVLINES='$numvdivlines'";
        }
        $xml .= ">";

        $xml .= "<MULTIPLEREGION>";
        foreach ( $category as $cat ) {
            $xml .= "<CATEGORY>$cat</CATEGORY>";
        }
        foreach ( $regions as $region => $series ) {
            $xml .= "<REGION NAME='$region'>";
            foreach ( $series as $number ) {
                $xml .= "<VALUE>";
                $xml .= "<NUMBER>$number</NUMBER>";
                $xml .= "<NUMBERTEXT>" . Currency($number) . "</NUMBERTEXT>";
                $xml .= "</VALUE>";
            }
            $xml .= "</REGION>";
        }
        $xml .= "</MULTIPLEREGION>";
        $xml .= "</reportdata>";

        //dieFL($xml);
        return $xml;
    }

    /**
     * Resolve custom report type xsl. Returns the file name.
     *
     * @return string
     */
    function GetFirstStageXSLFile()
    {
        if ( $this->export || $this->exportArray || ! $this->HasSubtotals() ) {
            //eppp_p('tabular');
            return 'custom_tabular_stdxml.xsl';
        }

        //eppp_p('summary');
        return 'custom_' . $this->reportType . '_stdxml.xsl';
    }

    /**
     * First stage XSLT transformation parameters
     *
     * @return string[]|int[]
     */
    function GetFirstStageXSLTParams()
    {
        $params = parent::GetFirstStageXSLTParams();
        $colCount = count($this->nexusUI->queryDef['columns'] ?? []);
        if ( $this->hasExtraColumn ) {
            $colCount++;
        }
        //eppp_p($colCount);

        // calculate how many columns will be used for subtotal label
        $params['colCount'] = $colCount;
        $summColCount = ceil($colCount / 2);
        //eppp_p($summColCount);

        $summValColCount = $colCount - $summColCount - 1;
        //eppp_p($summValColCount);

        $params['colCountSummaryLabel'] = $summColCount;
        $params['colCountSummaryValue'] = $summValColCount;
        $params['supportsPHPCode'] =
            "customreport_inc_ProcessRS_R_Nodes"; // function below that will take nodes and generate XML

        return $params;
    }

    /**
     * XML Export transformation parameters
     *
     * @return int[]
     */
    function GetXMLExportXSLTParams()
    {
        $params = parent::GetXMLExportXSLTParams();
        $colCount = count($this->nexusUI->queryDef['columns']);
        if (( $this->isMatrixReport || ($this->params['type'] == kShowCSV && $this->rtparams['SHOW_DETAILS'] == 'N'))) {
            $colCount += count($this->nexusUI->queryDef['groupby'][0]['summary']);
        }
        $params['colCount'] = $colCount;

        return $params;
    }

    /**
     * Determine if the given report can be run.
     *
     * @param string $reportName
     *
     * @return bool
     */
    function canRunReport($reportName)
    {
        $nexusInfo = new NexusInfo();

        return $nexusInfo->canRunReport($reportName, true, $this->libraryReport);
    }

    /**
     * Return appropriate query def i.e. from DB or request
     *
     * @param array &$queryDef
     * @param array &$rtparams
     *
     * @return bool
     */
    function GetQueryDef(&$queryDef, &$rtparams)
    {
        //impp("In GetQueryDef ".pp($this->params));
        //eppp_p($this->params);
        //dieFL();

        //eppp_p($this->rtparams);
        //eppp_p($this->queryDef);

        // Optimization:For efficiency
        if ( $this->rtparams != '' && $this->queryDef != '' ) {
            //eppp_p('isset');
            $queryDef = $this->queryDef;
            $rtparams = $this->rtparams;

            return true;
        }

        $ok = NexusInfo::GetQueryDef($this->params['_cr'], $queryDef, $rtparams, true, $this->libraryReport);
        $this->queryDef = &$queryDef;
        $this->rtparams = &$rtparams;

        return $ok;
    }

    /**
     * Finds out if subtotals are requested
     *
     * @return bool
     */
    function HasSubtotals()
    {
        if ( ( $this->reportType == 'summary' || $this->reportType == 'matrix' )
             && ( count($this->nexusUI->queryDef['groupby']) > 0 ) ) {
            return true;
        }

        return false;
    }

    /**
     * Returns output format
     *
     * @return string
     */
    function GetOutputType()
    {
        if ( $this->params['offline_mode'] ) {
            $outputtype = $this->params['offreporttype'];
        } else {
            $outputtype = $this->params['type'];
        }

        return $outputtype;
    }

    /**
     * form the custom query based on report params
     *
     * @param array    $queryDef
     * @param string   &$qry
     * @param bool     $multiDocType
     * @param string[] &$sort
     * @param string[] &$selectList
     * @param string[] &$subtotalList
     * @param string[] &$summaryList
     *
     * @return bool
     */
    function FormCustomQuery($queryDef, &$qry, $multiDocType, &$sort, &$selectList, &$subtotalList,
                             &$summaryList)
    {
        //eppp_p("inside FormCustomQuery");

        //  Don't do AAT tracking during query formation.
        /** @noinspection PhpUnusedLocalVariableInspection */
        $restorer = AdvAuditTracking::setManagedTracking(false);

        $allFieldInfo = [];
        $detailFieldInfo = $this->nexusUI->GetDetailFieldInfo();

        // Common Validation and Initialization
        $ok = $this->Validate($queryDef, $allFieldInfo);

        // Do *not* proceed if validations have failed
        if ( ! $ok ) {
            return false;
        }

        /** @noinspection PhpUnusedLocalVariableInspection */
        $root = $queryDef['root'];

        $outputtype = $this->GetOutputType();

        // Don't fire query for XSD export
        if ( $outputtype == kXSDExport ) {
            return $ok;
        }

        // Find out if it is export
        $this->export = ($outputtype == kXMLExport);
        $this->exportArray = $outputtype == kXMLExportArray;

        // Form 'select' clause
        $select = [];

        //-----------------------------------------//
        // Sequence of select calls is important   //
        //-----------------------------------------//
        $userCalcColumns = &$this->userCalculatedColumns;
        $runtimeColumns = &$this->runtimeColumns;
        $ok = $ok
              && $this->nexusDB->GetSelectClause($select, $allFieldInfo, $postProcessFields, $userCalcColumns,
                                                 $runtimeColumns, $detailFieldInfo, $additionalSelects);
        // This will be set to true if any of the column need to have post processing after the select statement
        /** @noinspection PhpUndefinedVariableInspection */
        $this->postProcessFields = $postProcessFields;

        $groupByFlag = false;
        if ( in_array($queryDef['root'], [ 'JOURNAL', 'GAAPADJJRNL', 'TAXADJJRNL', 'USERADJJRNL' ]) ) {
            $groupBy = array($queryDef['root']. '.CNY#');
            $groupByFlag = true;
        }

        $summary = [];
        if ( ! $this->export || $this->isMatrixReport ) {
            $ok = $ok
                  && $this->nexusDB->GetSummaryClause($summary, $multiDocType, $this->isMatrixReport, $allFieldInfo,
                                                      $detailFieldInfo);
            if ( $this->isMatrixReport ) {
                // in the case of matrix report the summary list is actually part of the details so we want to include that in the results
                // therefore we add those expressions right after the list of user-selected columns (which are the dimensions in the case
                // of matrix report)
                $select = INTACCTarray_merge($select, $summary);
            }
        }

        $nsVIDSelect = [];
        $ok = $ok && $this->nexusDB->GetNonStandardVIDSelectClause($nsVIDSelect);
        $select = INTACCTarray_merge($select, $nsVIDSelect);
        $nsDrilldownSelect = [];
        $ok = $ok && $this->nexusDB->GetNonStandardDrilldownSelectClause($nsDrilldownSelect);
        $select = INTACCTarray_merge($select, $nsDrilldownSelect);
        // Get mega columns (location)
        if ( IsMultiEntityCompany() ) {
            $megaSelect = [];
            $ok = $ok && $this->nexusDB->GetMEGASelectClause($megaSelect);
            //eppp_p($megaSelect);
            //dieFL();
            $select = INTACCTarray_merge($select, $megaSelect);
        }

        // Now that all the other selects have been added, Add in additional selects from GetSelectClause
        /** @noinspection PhpUndefinedVariableInspection */
        $select = INTACCTarray_merge($select, $additionalSelects);

        if ( (!$multiDocType && $this->isMatrixReport) || $groupByFlag ) {
            foreach ($select as $selectValue) {
                $groupBy[] = preg_split('/ as /i', $selectValue)[0];
            }
        }

        $kSpecialSelectMCPFields = $this->nexusDB->kSpecialSelectMCPFields[$queryDef['root']];

        if ( ! empty($kSpecialSelectMCPFields) ) {
            $callmanager = $kSpecialSelectMCPFields['Manager'];
            $callfunc = $kSpecialSelectMCPFields['callfunction'];
            $ok = $ok && $callmanager::$callfunc($select, $queryDef['root']);
        }

        // Form 'from' clause
        $from = [];
        $ok = $ok && $this->nexusDB->GetFromClause($from);

        $ok && $this->renameAddReportSelectsAlias($select, $additionalSelects, $root);

        // Form 'where' clause
        $where = [];
        $ok = $ok && $this->nexusDB->GetWhereClause($where);

        $whereClauseForAgingBucket = $this->nexusDB->kwhereClauseForAgingBucket[$queryDef['root']];
        if ( ! empty($whereClauseForAgingBucket) ) {
            $callmanager = $whereClauseForAgingBucket['Manager'];
            $callfunc = $whereClauseForAgingBucket['callfunction'];

            // we are relaying on certain columns for filtering but maybe the customer don't select them
            // to be displayed in the report so need to push them into the queryDef
            foreach ($this->nexusDB->kSpecialSelectColumns[$queryDef['root']] as $column => $func) {
                if (!in_array($column, $queryDef['columns'])) {
                    $queryDef['columns'][$queryDef['root'] . "." . $column] = [];
                }
            }

            $ok = $ok && $callmanager::$callfunc($where, $queryDef, $this->params['ASOFDATE']);
        }

        // Form advance filters
        $filter = [];
        $ok = $ok && $this->nexusDB->GetAdvancedFilterClause($filter, $allFieldInfo, $detailFieldInfo);
        $where = INTACCTarray_merge($where, $filter);

        // From user input filters
        $userFilter = [];
        $ok = $ok && $this->nexusDB->GetUserInputFilterClause($userFilter, $allFieldInfo);

        $ok = $ok && $this->nexusDB->postProcessUserInputFilterClause($userFilter);

        $where = INTACCTarray_merge($where, $userFilter);
        $root = $queryDef['root'];
        if ( in_array($root, [ 'CUSTOMER', 'VENDOR' ]) && IsMultiVisibilitySubscribed(isl_strtolower($root)) ) {
            $multivisibility = [];
            $ok = $ok && $this->nexusDB->MultivisibilityFilterClause($queryDef, $multivisibility);
            $where = INTACCTarray_merge($where, $multivisibility);
        }

        // Form 'order by' clause
        $sort = [];
        $ok = $ok && $this->nexusDB->GetOrderByClause($sort, $allFieldInfo, $multiDocType);
        // Form subtotal summary
        $subtotalSummary = [];
        if ( ! $this->export ) {
            // Collect 'selects' for subtotal summary, if they are not one of columns
            $subtotalSelect = [];
            $subtotalRecursive = [];
            $ok = $ok && $this->nexusDB->GetSubtotalSelectClause($subtotalSelect, $subtotalRecursive, $allFieldInfo, $detailFieldInfo);
            // TODO : The `array_union()` here is just a quick fix for filtering the select columns duplicates
            //  Cases like `TABLE.column` and `TABLE.COLUMN` will not be filtered using `array_union()` or `array_unique()`
            //  These cases will need a different implementation
            $select = array_union($select, $subtotalSelect);

            if ( ! $this->isMatrixReport ) {
                $ok = $ok && $this->nexusDB->GetSubtotalSummaryClause($subtotalSummary, $multiDocType, false, $allFieldInfo, $detailFieldInfo);
                $select = INTACCTarray_merge($select, $subtotalSummary);
                // Form summary - the list was computed above
                /** @noinspection PhpUndefinedVariableInspection */
                $select = INTACCTarray_merge($select, $summary);
                $select = INTACCTarray_merge($select, $subtotalRecursive);
            } else {
                foreach ( $subtotalSelect as $selectvalue ) {
                    $groupBy[] = preg_split('/ as /i', $selectvalue)[0];;
                }
            }

            // Form subtotal 'order by' clause
            $subtotalSort = [];
            $ok = $ok && $this->nexusDB->GetSubtotalOrderByClause($subtotalSort, $allFieldInfo);

            // subtotal sorts *must* happen first
            // rendering relies on order of records

            $sort = INTACCTarray_merge($subtotalSort, $sort);
        }

        if ( ! $ok ) {
            return false;
        }
        //eppp_p($select); eppp_p($groupBy);
        $selectList = $select;
        $subtotalList = $subtotalSummary;
        $summaryList = $summary;

        $hint = '';
        if ( $root == 'GLACCOUNTBALANCE' ) {
            $hint = " /*+ opt_param('_optimizer_use_feedback','false') */ ";
        }

        // [F2906] Disable this hint - we are transitioning from mat views to table for Project Summary
        // // use "rule" hint to have a better plan for a report query that references PROJECTSUMMARY
        // if (preg_grep("/PROJECTSUMMARY/i", $from)) {
        //     $hint = " /*+ rule */ ";
        // }

        // Form complete query
        $qry = [];
        if ( isset($where['Locations']) ) {
            foreach ( $where['Locations'] as $key => $value ) {
                $newWhere = $where;
                unset($newWhere['Locations']);
                $newWhere[] = $value;
                $qry[$key] =  'select ' . $hint . join(', ', $select) .
                              ' from ' . join(', ', $from) .
                              ' where ' . join(' and ', $newWhere);

                /** @noinspection PhpUndefinedVariableInspection */
                if ( is_array($groupBy) && count($groupBy) > 0 ) {
                    $qry[$key] .= ' group by ' . join(', ', $groupBy);
                }

                if ( count($sort) > 0 && $multiDocType == false ) {
                    $qry[$key] .= ' order by ' . join(', ', $sort);
                }
            }
        } else {
            $qry = 'select ' . $hint . join(', ', $select) .
                   ' from ' . join(', ', $from) .
                   ' where ' . join(' and ', $where);

            /** @noinspection PhpUndefinedVariableInspection */
            if ( is_array($groupBy) && count($groupBy) > 0 ) {
                $qry .= ' group by ' . join(', ', $groupBy);
            }

            if ( count($sort) > 0 && $multiDocType == false ) {
                $qry .= ' order by ' . join(', ', $sort);
            }
        }

        return true;
    }

    /**
     * forms query for multi doctypes
     *
     * @param array  $queryDef
     * @param string &$qry
     *
     * @return bool
     */
    function FormMultiDocTypeCustomQuery($queryDef, &$qry)
    {
        //  Don't do AAT tracking during query formation.
        /** @noinspection PhpUnusedLocalVariableInspection */
        $restorer = AdvAuditTracking::setManagedTracking(false);

        $ok = true;
        //Handle multi-doc type query as UNION
        $doctypes = explode("#~#", $queryDef['doctype']);

        $params = $this->params;

        $ok = $ok && $this->Validate($queryDef, $localFix); //insert second param; fix for PHPDoc
        if ( ! $ok ) {
            return false;
        }

        // we search for any custom compoennt (field or table) that is being referenced
        // by this report. If no custom compoents are defined, or if the report is only
        // on one doc type, we won't build the query using unions of subqueries for each
        // doc type, we will go thorugh the regular query builder
        $hasCustomComponents = false;
        if ( count($doctypes) > 1 ) {
            $allFieldInfo = $this->nexusUI->GetAllFieldInfo();
            foreach ( $allFieldInfo as $field ) {
                if ( arrayExtractValue($field, 'iscustom') ) {
                    $hasCustomComponents = true;
                    break;
                }
            }

            if ( ! $hasCustomComponents ) {
                foreach ( $this->nexusDB->nexustree as $node ) {
                    if ( strpos($node['table'], 'PT_') === 0 ) {
                        $hasCustomComponents = true;
                        break;
                    }
                }
            }
        }

        if ( $hasCustomComponents ) {
            // we have custom components referecned by the report
            // so we need to go through the UNION queries since the
            // custom components are stored in columns specific for each doc type
            foreach ( $doctypes as $key => $doctype ) {
                $queryDef['doctype'] = $doctype;
                $this->nexusDB->nexustree = [];
                $this->nexusDB->fieldAliasCount = 0;
                $this->nexusDB->fieldaliasStruct = [];

                $this->params = $params;

                $select = [];
                $ok = $ok && $this->FormCustomQuery($queryDef, $tempqry, true, $sort, $select, $subtotals, $summary);

                //eppp_p($tempqry); dieFL();
                if ( $key == 0 ) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $qry = '(' . $tempqry . ')';
                } else {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $qry .= ' UNION ALL' . '(' . $tempqry . ')';
                }
            }

            //eppp_p($this);
            //handle sort by here for multi doctype
            /** @noinspection PhpUndefinedVariableInspection */
            $select = $this->processAliasesforMultiDocCustomQuery($select, $subtotals, $summary, $groupBy);
            //eppp_p($select);
            $qry = 'select ' . join(', ', $select) . ' from (' . $qry . ') ';
            if ( count($groupBy) > 0 ) {
                $qry = $qry . ' group by ' . join(', ', $groupBy);
            }
            /** @noinspection PhpUndefinedVariableInspection */
            if ( $sort ) {
                $qry = $qry . ' order by ' . join(', ', $sort);
            }
        } else {
            // there are no custom components referecned by thsi report
            // then we can build one query with in (docTypeList) filter
            $this->nexusDB->nexustree = [];
            $this->nexusDB->fieldAliasCount = 0;
            $this->nexusDB->fieldaliasStruct = [];
            $this->params = $params;
            $ok = $ok && $this->FormCustomQuery($queryDef, $qry, false, $sort, $select, $subtotals, $summary);
        }

        //eppp_p($qry);dieFL();
        return $ok;
    }

    /**
     * @param string[] $selects
     * @param string[] $subtotals
     * @param string[] $summary
     * @param string[] &$groupBy
     *
     * @return string[]
     */
    function processAliasesforMultiDocCustomQuery($selects, $subtotals, $summary, &$groupBy)
    {
        $outSelect = [];
        $groupBy = [];
        foreach ( $subtotals as &$val ) {
            $tmp = isl_strrpos($val, " as ");
            if ($tmp === false) {
                $tmp = isl_strrpos($val, " AS ");
            }
            $val = isl_substr($val, $tmp + 4);
        }
        foreach ( $summary as &$val_1 ) {
            $tmp = isl_strrpos($val_1, " as ");
            if ($tmp === false) {
                $tmp = isl_strrpos($val_1, " AS ");
            }
            $val_1 = isl_substr($val_1, $tmp + 4);
        }

        $st_idx = 1;
        $gt_idx = 1;
        foreach ( $selects as $value ) {
            $tmp = isl_strrpos($value, " as ");
            if ($tmp === false) {
                $tmp = isl_strrpos($value, " AS ");
            }
            $value = isl_substr($value, $tmp + 4);
            if ( isl_strstr($value, "ST") ) {
                $st_count = isl_substr($value, 2);
                $out = $this->nexusDB->GetNthSubTotalClauseForMultiDocCustomQuery($value, $st_count);
            } else if ( isl_strstr($value, "GT") ) {
                $gt_count = isl_substr($value, 2);
                $out = $this->nexusDB->GetNthSummaryClauseForMultiDocCustomQuery($value, $gt_count);
            } else if ( in_array($value, $subtotals) ) {
                // this is the case for matrix reports when the subtotal column aliases are not in the STxxx form
                $out = $this->nexusDB->GetNthSubTotalClauseForMultiDocCustomQuery($value, $st_idx);
                $st_idx++;
            } else if ( in_array($value, $summary) ) {
                // this is the case for matrix reports when the subtotal column aliases are not in the STxxx form
                $out =
                    $this->nexusDB->GetNthSummaryClauseForMultiDocCustomQuery($value, $gt_idx, $this->isMatrixReport);
                $gt_idx++;
            } else {
                $out = $value;
            }
            $outSelect[] = $out;
            if ( $out == $value && $this->reportType == 'matrix' ) {
                $groupBy[] = $value;
            }
        }

        return $outSelect;
    }

    /**
     * @return string
     */
    function GetStylesheetPath()
    {
        return '../resources/css/customreport-alt.css';
    }

    /**
     * @param string $type
     *
     * @return string
     */
    public function UseThisXML($type)
    {
        // determine which xsl type to use for this report
        switch ($type) {
            case kShowExcel:
                $usethisxsl = 'xls';
                break;
            default:
                $usethisxsl = parent::UseThisXML($type);
        }

        return $usethisxsl;
    }

    /** Retrieve dashboard filter expression for dimension group members.
     *
     * @param array  $dashFilter
     *
     * @return array|null
     */
    public function getDimensionGroupFilterExpression($dashFilter) {
        /** @var DimensionGroupManager $dimGroupMgr */
        $dimGroupMgr = Globals::$g->gManagerFactory->getManager($dashFilter['DIMENSIONENTITY']. "group");
        $members = $dimGroupMgr->getGroupMembersById($dashFilter['DIMENSIONID']);
        foreach ($members['MEMBERIDS'] as $member) {
            if (!isset($membersIDs)) {
                $membersIDs = explode("--", $member)[0];
            } else {
                $membersIDs .= "," . explode("--", $member)[0];
            }
        }

        return $membersIDs ?? null;
    }

    /**
     * Get filters for custom report
     *
     * @return array
     */
    public static function getCustomReportFilters() {
        $dbFilters = [];
        $crFilters = [];
        if (NexusInfo::GetQueryDef(Request::$r->_cr, $queryDef, $rtparams)) {
            $dbFilters = QueryResult(["select advancedfilter, filters from customreport where cny# = :1 and name = :2 ",
                             GetMyCompany(), $queryDef['reportname']]);
        }
        $kOverrideNexusObjectInfo = array();
        InitNexusObjectInfoOverride($kOverrideNexusObjectInfo);
        $nexusUI= new NexusUIInfo();
        $filters = self::extractFilters($dbFilters);
        foreach ($filters as $dbFilter) {
            $filterPath = explode('.', $dbFilter['path']);
            $countPath = count($filterPath);
            if ($countPath > 2) {
                $field = $filterPath[$countPath - 1];
                $filterName = "";
                $fieldInfo = [];
                $objectPath = substr($dbFilter['path'], 0, strrpos($dbFilter['path'], "."));
                while ($objectPath) {
                    $nexusParentObj = [];
                    $printAsObj = substr($objectPath, strrpos($objectPath, ".") + 1);
                    $object = $nexusUI->GetObject($objectPath);
                    $objectPath = substr($objectPath, 0, strrpos($objectPath, "."));
                    if ($objectPath) {
                        $parentObject = $nexusUI->GetObject($objectPath);
                        $parentMgr = Globals::$g->gManagerFactory->getManager(strtolower($parentObject));
                        $nexusParentObj = $parentMgr->_schemas[strtolower($parentObject)]['nexus'][strtolower($printAsObj)];
                    }
                    $manager = Globals::$g->gManagerFactory->getManager(strtolower($object));

                    $printAs = $nexusParentObj['printas'] ?? $kOverrideNexusObjectInfo[$object]['printas'];
                    if (isset($printAs)) {
                        $filterName = $printAs ." > " . $filterName;
                    }
                    if (empty($fieldInfo)) {
                        $fieldInfo = $manager->GetFieldInfo($field);
                        $filterName .= $fieldInfo['fullname'];
                    }
                }
            } else {
                $entity = strtolower($filterPath[0]);
                $manager = Globals::$g->gManagerFactory->getManager($entity);
                $fieldInfo = $manager->GetFieldInfo(explode('.', $dbFilter['path'])[1]);
                $filterName = $fieldInfo['fullname'];
            }

            $filterOperator = $nexusUI->GetOperatorLabel($dbFilter['operator']);
            if (isset($filterName) and isset($filterOperator) and isset($dbFilter['value'])) {
                $crFilters[] = ['filterName'     => $filterName,
                              'filterOperator' => $filterOperator,
                              'filterValue'    => $dbFilter['value']];
            }
        }

        if ( ! empty($dbFilters[0]['ADVANCEDFILTER'])) {
            $crFilters[] = [ 'filterName'     => I18N::getSingleToken('IA.FILTER_EXPRESSION_COLON_VALUE',
                                                                      [ [ 'name'  => 'VALUE',
                                                                          'value' => $dbFilters[0]['ADVANCEDFILTER'] ] ]),
                             'filterOperator' => '',
                             'filterValue'    => '' ];
        }
        self::translateCRFilters($crFilters);
        return $crFilters;
    }

    /**
     * @param $crFilters
     * translate CR filter name and values
     * @return void
     * @throws I18NException
     */
    private static function translateCRFilters(array &$crFilters): void
    {
        foreach ($crFilters as &$crFilter) {
            self::translateComposedObject($crFilter['filterName']);
            // //operators are already translated at the .inc
            //if values are tokens
            self::translateComposedObject($crFilter['filterValue']);
        }
    }

    /**
     * Translates composed string from token to text
     *
     * @param string &$flatlabel might look something like 'IA.OBJECT1>IA.OBJECT2>IA.OBJECT3'
     *
     */
    public static function translateComposedObject(&$labelText): void
    {
        if ( !is_string($labelText) || trim($labelText) == '' ) {
            return;
        }
        $separator = " > ";
        if (str_contains($labelText, "-")) {
            $separator = " - ";
        }
        $translateTokensForTheComposed = function($labelText) use ($separator) {
            $tokens = [];
            $filterNameParts = explode($separator, $labelText);
            foreach ($filterNameParts as $part) {
                if (str_starts_with($part, "IA.")) {
                    $tokens[] = $part;
                }
            }
            I18N::addTokens(I18N::tokenArrayToObjectArray(array_unique($tokens)));
            I18N::getText();
        } ;
        $loadTranslationForTheComposed = function (&$labelText) use ($separator) {
            $translatedFilterParts = [];
            $filterNameParts = explode($separator, $labelText);
            foreach($filterNameParts as $part){
                if(str_starts_with($part,"IA.")){
                    $translatedFilterParts[] = I18N::getSingleToken($part);
                }else{
                    $translatedFilterParts[]=$part;
                }
            }
            $labelText = count($translatedFilterParts)>1  ? implode($separator,$translatedFilterParts) : $translatedFilterParts[0];
        };

        $translateTokensForTheComposed($labelText);
        $loadTranslationForTheComposed($labelText);
    }

    /**
     * Determines if the "Show filters in report output" property is checked or not
     *
     * @return bool
     */
    public static function showFiltersInReportOutputEnabled()
    {
        if (isset(Request::$r->_cr) && Request::$r->_cr != '') {
            $customReportMgr = Globals::$g->gManagerFactory->getManager('customreport');
            $params = [ 'filters' => [ [ [ 'NAME', '=', Request::$r->_cr ], [ 'SHOWFILTERSINREPORTOUTPUT', '=', 'true' ] ] ] ];
            if ( ! empty($customReportMgr->GetList($params)) ) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    /**
     * @param array $dbFilters
     * @return array filters
     */
    private static function extractFilters($dbFilters)
    {
        $filters = !empty($dbFilters) ? $dbFilters[0]['FILTERS'] : null;
        if ($filters) {
            $filters = databaseStringUncompress($filters);
            if ($filters) {
                $filters = unserialize($filters);
            }
        }
        return $filters ?: array();
    }

    /**
     * @param array $select
     * @param array $additionalSelects
     * @param string $root
     */
    private function renameAddReportSelectsAlias(&$select, $additionalSelects, $root){
        global $gManagerFactory;
        $root = isNullOrBlank($root) ? "" : $root;
        $additionalSelects = $additionalSelects ?? [];
        $nexusDB = $this->nexusDB;
        if($additionalSelects && ($root == "SODOCUMENTENTRY" || $root == "PODOCUMENTENTRY")){
            foreach($additionalSelects as $addSelect){
                if(in_array($addSelect, $select)){
                    $key = array_search($addSelect, $select);
                    if($key !== false){
                        $arr =  explode('.', $select[$key]);
                        if(count($arr) > 1){
                            $ent = strtolower($arr[0]);
                            $manager = class_lookup(isl_strtolower($ent) . 'manager');
                            if(isNullOrBlank($manager)){
                                continue;
                            }
                            $entityMgr = $gManagerFactory->getManager($ent);
                            $table = $entityMgr->_schemas[$ent]['table'];
                            foreach($nexusDB->nexustree as $val){
                                $usePtRelationship = $val['use_pt_relationship'] ?? false;
                                if($table == $val['table'] && !$usePtRelationship){
                                    $tableAlias = $val['alias'];
                                    $select[$key] = str_replace($arr[0],$tableAlias,$select[$key]);
                                    continue(2);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    function ProcessDateFilter(&$params)
    {
        // NOTE: THE FOLLOWING CALLS A FUNCTION OF THE SAME NAME INSIDE THE FilterEditor.cls FILE
        $ok = ProcessDateFilter($params, $startDate, $endDate);

        // If a Custom Report is from Dashboard, then don't treat it as OFFLINEREPORTS
        $fromDashboard = Request::$r->_dashboardKey ?? Request::$r->_dashkey;
        $offlineReport = $params['OFFLINEREPORTS'] ?? $params['offline_mode'];
        if ( $offlineReport && empty($fromDashboard) ) {
            $params = FormatDateAndTimestampPromptsForDisplay($params);
        } else {
            $params = AutocompleteTimestampPrompts($params);
        }
        if ( $ok ) {
            $this->startdate = ( $startDate ? : $params['START_DATE'] );
            $this->enddate = ( $endDate ? : $params['END_DATE'] );
        }

        return $ok;
    }

}

//  **********************************************************************************************
//  Support for 'customreport_inc.xsl':
//
//      THIS CODE MIMICS THE XSL INSIDE customreport_inc.xsl, SO IF YOU CHANGE ONE, CHANGE BOTH!!
//      (this code in XSL form took 14 minutes in one escalation, but the PHP version takes 12 seconds, plus
//      4 seconds for the remaining XSL)
//
//  The 'column meta' data looks like this, and directs the formatting of the columns:
//
//                   <C L="Exchange Rate" HS="" DS="11" DD="N" LINK="N" MOD="" VOP="" P="00########"/>
//                   <C L="Record Number" HS="" DS="10" DD="Y" LINK="N" MOD="gl" VOP="129" CDD="Y" JEDRILL="Y" SCRIPT="editor.phtml"/>
//                   <C L="Vendor ID" HS="" DS="10" DD="Y" LINK="N" MOD="ap" VOP="163" LOCIDX="28"/>
//
//  And a typical row that we are converting looks like this:
//
//      <R C16="04/06/2017" C4="2127" C19="US" C20="Code 42-US" C11="APJ" C0="650220"
//          C1="Consulting" C2="7155" C14="Debit" C3="USD" C12="0" C13="7155" C6="USD" C17="1" C18="04/06/2017" C22="8"
//          C5="jbohmbac" C8="54786" C15="" C7="" C10="54786" C9="Pat Connelly Project" C21="37223622" C23="" C25="V00002184"
//          C24="Steven Douglas Interim FInancial Services" DRILLBATCHKEY="2727993" C25_LOC=""/>
//
//  Output will look like this:
//  <row s="10">
//         ... <col s="11" precision="00########">1</col> ... <col s="10" href="javascript:DoShowJE('2727993','','129', 'editor.phtml');">37223622</col> ...
//  </row>
//
//
//		This gets called by xsl_interface.cls
//
//  **********************************************************************************************

/**
 * @param mixed    $xslpath       can't determine the type; unused in the function...
 * @param DOMXPath $xmlpath
 * @param string[] &$replaceList
 */
function customreport_inc_ProcessRS_R_Nodes(/** @noinspection PhpUnusedParameterInspection */ $xslpath, $xmlpath, &$replaceList)
{
    $customreport_inc_metaData = [];

    $colmeta = $xmlpath->query("//reportdata/report/CS/C"); // a list of directions per column

    // One-time init:
    //  I'm ass-u-me-ing that a PHP array is faster than DOMNode access
    foreach ( $colmeta as $meta ) {
        $oneMeta = [];
        /** @var DOMElement $meta */
        $attributes = $meta->attributes;
        $attributeCount = $attributes->length;
        for ( $col = 0; $col < $attributeCount; $col++ ) {
            $one = $attributes->item($col);
            $oneMeta[$one->nodeName] = $one->nodeValue;
        }
        $customreport_inc_metaData[] = $oneMeta;   // Accessed by index
    }

    $rtn = "";
    // The 'R' nodes.  These (in the RS section) use default parameters and are relatively easy to process
    // However they comprise the bulk of the data (the non-header/non-footer rows)
    $nodes = $xmlpath->query("//reportdata/report/RS/R");
    foreach ( $nodes as $node ) {
        $rtn .= customreport_inc_ProcessOneNode($customreport_inc_metaData, $node);
    }
    $replaceList['REPLACE_customreport_inc_ProcessRS_R_Nodes_HERE'] =
        $rtn;     // This text gets replaced in the finished XML with the contents of $rtn
}

/**
 * This routine mimics the 'R' template in customreport_inc.xsl, and processes one row, or 'R' node.
 *  The row has 25+ columns, and each has a description of what-to-do inside the 'meta data' passed in.....
 *
 * @param string[][] $customreport_inc_metaData
 * @param DOMElement $node
 *
 * @return string
 */
function customreport_inc_ProcessOneNode($customreport_inc_metaData, $node)
{
    static $rowStyle = 10; // default

    $rtn = "<row s='$rowStyle'>\n  ";

    // This follows the logic for <xsl:template match="R"> with DEFAULT paranmeters....
    $attributes = ( $node ) ? $node->attributes : [];
    $attributeCount = min(count($customreport_inc_metaData), $attributes->length);

    for ( $col = 0; $col < $attributeCount; $col++ ) {
        $meta = $customreport_inc_metaData[$col];
        $attribute = $attributes->item($col);
        $attrValue = $attribute->nodeValue;
        cleanXML($attrValue);

        $rtn .= "<col ";

        // column style
        $style = $meta['DS'];
        $rtn .= "s='$style'";

        // number precision
        $precision = $meta['P'] ?? false;
        if ( $precision !== false ) {
            $rtn .= " precision='$precision'";
        }

        // links; there are several of them, all become 'href' parameters.
        // I'm guessing that in XSL, the LAST ONE WINS....
        $href = "";
        if ( isset($meta['DD']) && ( $meta['DD'] == 'Y' ) ) {
            $vididx = isset($meta['VIDIDX']) ? ( $meta['VIDIDX'] - 1 )
                : -1;   // Ticket : 67684 - vididx is 1-based, this code is zero-based.
            $vidvalue =
                ( ( $vididx >= 0 ) && ( $vididx < $attributes->length ) ) ? $attributes->item($vididx)->nodeValue
                    : $attrValue;
            if ( $vidvalue != '' ) {
                $locidx = isset($meta['LOCIDX']) ? ( $meta['LOCIDX'] - 1 )
                    : -1;   // Ticket : 67684 - locidx is 1-based, this code is zero-based.
                $locidxValue =
                    ( ( $locidx >= 0 ) && ( $locidx < $attributes->length ) ) ? $attributes->item($locidx)->nodeValue
                        : "";
                $script = $meta['SCRIPT'] ?? "";
                $mod = $meta['MOD'] ?? "";
                if ( strpos($mod, 'PT_') === 0 ) {
                    $vop = OP_RUNTIME;
                } else {
                    $vop = $meta['VOP'] ?? "";
                }

                $href = " href=\"javascript:DoShow('$vidvalue','$locidxValue','$vop','$script','$mod');\"";
            }
        }

        // another kind of link
        if ( isset($meta['LINK']) && ( $meta['LINK'] == 'Y' ) ) {
            $foundat = strpos($attrValue, '|*|*|*|');
            if ( $foundat !== false ) {
                $firstPart = substr($attrValue, 0, $foundat);
                $attrValue = substr($attrValue, $foundat + 7); // 7 is the length of '|*|*|*|'

                $href = " href=\"javascript:WinPop('$firstPart','', null);\"";
            } else {
                $url = strval($attrValue);
                if (strpos($url, "://") == false) {   // No protocol in URL
                    $url = "https://".$url;
                }
                $popUp = util_encode($url);
                $href = " href=\"javascript:WinPop('$popUp','', null);\"";
            }
        }

        // Yet ANOTHER kind of link!
        if ( isset($meta['ARPAYDD']) && ( $meta['ARPAYDD'] == 'Y' ) ) {
            $recordNo = $node->getAttribute("RECORDNO");
            $batchKey = $node->getAttribute("PRBATCHKEY");
            if ( ( $recordNo != '' ) && ( $batchKey != '' ) ) {
                $locidx = isset($meta['LOCIDX']) ? ( $meta['LOCIDX'] - 1 )
                    : -1;   // Ticket : 67684 - locidx is 1-based, this code is zero-based.
                $locidxValue =
                    ( ( $locidx >= 0 ) && ( $locidx < $attributes->length ) ) ? $attributes->item($locidx)->nodeValue
                        : "";
                $vop = $meta['VOP'] ?? "";

                $href = " href=\"javascript:DoShowARPayment('$recordNo','$batchKey', '$locidxValue','$vop','');\"";
            }
        }

        if ( isset($meta['ARPYMTDD']) && $meta['ARPYMTDD'] == 'Y' ) {
            $recordKey = $node->getAttribute("RECORDNO");
            $multiEnityPymtKey = $node->getAttribute("MULTIENTITYPYMTKEY");
            if (isSpecified($multiEnityPymtKey)) {
                $recordKey = $multiEnityPymtKey;
            }
            $locidx = isset($meta['LOCIDX']) ? $meta['LOCIDX'] - 1 : -1;
            $locidxValue =
                $locidx >= 0 && $locidx < $attributes->length ? $attributes->item($locidx)->nodeValue
                    : "";
            $script = $meta['SCRIPT'] ?? "";
            $mod = $meta['MOD'] ?? "";
            if ( strpos($mod, 'PT_') === 0 ) {
                $vop = OP_RUNTIME;
            } else {
                $vop = $meta['VOP'] ?? "";
            }

            $href = " href=\"javascript:DoShow('$recordKey','$locidxValue','$vop','$script','$mod');\"";
        }
        // Yet ANOTHER kind of link!
        if ( isset($meta['JEDRILL']) && ( $meta['JEDRILL'] == 'Y' ) ) {
            $drillBatchKey = $node->getAttribute("DRILLBATCHKEY");
            if ( $drillBatchKey != "" ) {
                $ownershipKey = $node->getAttribute("OWNERSHIPKEY");
                $vop = $meta['VOP'] ?? "";
                $script = $meta['SCRIPT'] ?? "";

                $href = " href=\"javascript:DoShowJE('$drillBatchKey','$ownershipKey', '$vop','$script');\"";
            }
        }

        //The drill-down on DOCID for all rows in SO/PO/INV documents will navigate through RECORDNO instead of DOCID.
        // If there is no DOCID, we will display a 'view' label to navigate to that document.
        if (!empty($meta['DOCIDDRILL']) && $meta['DOCIDDRILL'] == 'Y' ) {
            $recordKey = $node->getAttribute("DRILLRECORDNO");
            if(!empty($recordKey)){
                $locidx = ($meta['LOCIDX'] ?? 0) - 1;
                $locidxValue = ($locidx >= 0 && $locidx < $attributes->length) ? $attributes->item($locidx)->nodeValue : "";
                $vop = $meta['VOP'] ?? "";
                $script = $meta['SCRIPT'] ?? "";
                $mod = $meta['MOD'] ?? "";
                $href = " href=\"javascript:DoShow('$recordKey','$locidxValue','$vop','$script','$mod');\"";

                $type = Request::$r->_type ?? '';
                // If there is no DOCID, we need to display a 'view' label and allow the user to navigate to the SO/PO/INV document.
                // The 'view' label should only be displayed for the _html export format. It should not appear for any other export formats.
                if (empty($attrValue ?? '') && $type == kShowHTML) {
                    $attrValue = 'IA.VIEW';
                }
            }
        }

        if ( $href != "" ) {
            $rtn .= $href;  // pick the most recent one that succeeds.
        }

        if ( $attrValue == '' ) {
            $rtn .= "/>";   // no node contents
        } else {
            $rtn .= ">$attrValue</col>";
        }
    }
    $rtn .= "\n</row>\n";

    return $rtn;
}
