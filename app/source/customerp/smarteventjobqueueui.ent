<?php

/**
 * File description
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

require 'fifodispatcherqueueui.ent';
require 'smarteventjobqueue.ent';

$kSchemas['smarteventjobqueueui'] = $kSchemas['fifodispatcherqueueui'];
unset($kSchemas['smarteventjobqueueui']['ownedobjects']);
$kSchemas['smarteventjobqueueui'] = EntityManager::inheritEnts($kSchemas['smarteventjobqueue'], $kSchemas['smarteventjobqueueui']);
$kSchemas['smarteventjobqueueui']['printas'] = 'IA.SMART_EVENT_JOBS';
$kSchemas['smarteventjobqueueui']['pluralprintas'] = 'IA.SMART_EVENT_JOBS';
