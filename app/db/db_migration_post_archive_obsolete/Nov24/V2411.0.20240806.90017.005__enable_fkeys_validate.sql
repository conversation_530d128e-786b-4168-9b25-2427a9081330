-- Turning OFF triggers
BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
END;
/

ALTER TABLE CUSTOMER MODIFY CONSTRAINT FK_CUSTOMER_CUSTMESSAGE ENABLE VALIDATE
/
ALTER TABLE PRRECORD MODIFY CONSTRAINT FK_PRRECORD_CUSTMESSAGE ENABLE VALIDATE
/
ALTER TABLE RECURPRRECORD MODIFY CONSTRAINT FK_RECURPRRECORD_CUSTMESSAGE ENABLE VALIDATE
/

-- Turning ON triggers
BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/


