--liquibase formatted sql
--changeset Bipin.Mandoti:2405.0.20240202.129318.001 runOnChange:false logicalFilePath:V2405.0.20240202.129318.001__pcb_details_data_insert_constraints.sql
/*

== DELTA ONLY FOR DEV SCHEMAS ==

Repair action: EXECUTE
Repair action: IGNORE

*/

DECLARE
    CURSOR row_data IS (
        select CNY#, DOCHDRKEY from PCB_INVOICE_SUMMARY
    );
    recordid number := 1;
    sql_stmt VARCHAR2(2000);
BEGIN
    FOR rd IN row_data LOOP
            sql_stmt :='update PCB_INVOICE_SUMMARY set RECORD# ='|| recordid ||' where cny# ='|| rd.CNY#||' and DOCHDRKEY ='|| rd.DOCHDRKEY||'';
            EXECUTE IMMEDIATE sql_stmt;
            recordid := recordid +1;
        END LOOP;
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END;
/

ALTER TABLE PCB_INVOICE_SUMMARY ADD CONSTRAINT NN_PCB_INV_SUMMARY_RECORD CHECK(RECORD# IS NOT NULL)
/
ALTER TABLE PCB_INVOICE_SUMMARY ADD CONSTRAINT PK_PCB_INVOICE_SUMMARY PRIMARY KEY (CNY#,RECORD#) USING INDEX TABLESPACE ACCTINDX
/
ALTER TABLE PCB_INVOICE_SUMMARY ADD CONSTRAINT FK_PCB_INV_SUMMARY_DOCHDRKEY FOREIGN KEY (CNY#, DOCHDRKEY) REFERENCES DOCHDR (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
/
ALTER TABLE PCB_INVOICE_SUMMARY ADD CONSTRAINT FK_PCB_INV_SUMMARY_PRRECORDKEY FOREIGN KEY (CNY#, PRRECORDKEY) REFERENCES PRRECORD (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
/
CREATE INDEX IX_PCB_INV_SUMMARY_PRRECORDKEY ON PCB_INVOICE_SUMMARY (CNY#, PRRECORDKEY) TABLESPACE ACCTINDX INVISIBLE ONLINE
/
CREATE INDEX IX_PCB_INV_SUMMARY_DOCHDRKEY ON PCB_INVOICE_SUMMARY (CNY#, DOCHDRKEY) TABLESPACE ACCTINDX INVISIBLE ONLINE
/
