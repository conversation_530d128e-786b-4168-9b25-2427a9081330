--OWNER_CONTACT:reuben<PERSON><EMAIL>
-- ------------------------------------------------------------------------------------------------------------------------
-- -- V2411.0.20241230.52827.003__ocr_add_index_oda_ola_table
-- -- JIRA : IA-52827: ADD INDEX TO MODA, MOLA TABLES
-- ------------------------------------------------------------------------------------------------------------------------

--Create index on objecttype column

--drop index if exist
DECLARE
index_exists number := 0;
BEGIN
select count(*) into index_exists
from all_indexes where table_owner=SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA') and table_name = 'MODA' and index_name = 'IX_MODA_OBJECTTYPE';
    if (index_exists = 0) then
        execute immediate 'CREATE INDEX IX_ODA_OBJECTTYPE ON ODA (CNY#, OBJECTTYPE) TABLESPACE ACCTINDX ONLINE';
        execute immediate 'CREATE INDEX IX_OLA_OBJECTTYPE ON OLA (CNY#, OBJECTTYPE) TABLESPACE ACCTINDX ONLINE';
        execute immediate 'CREATE INDEX IX_MODA_OBJECTTYPE ON MODA (CNY#, OBJECTTYPE) TABLESPACE ACCTINDX ONLINE';
        execute immediate 'CREATE INDEX IX_MOLA_OBJECTTYPE ON MOLA (CNY#, OBJECTTYPE) TABLESPACE ACCTINDX ONLINE';
    end if;
end;
/