--liquibase formatted sql
--changeset nithin.chemmayamgottu:20220222.46610.001 runOnChange:false logicalFilePath:U20220608.46610.001__revert_add_copy_pay_in_advance_permission.sql

------------------------------------------------------------------------------------------------------------------------
------------------ ticket  : Revert --- Permission for AP Advance -----------------
------------------------------------------------------------------------------------------------------------------------
BEGIN
DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
END;
/
---rename to Ap advances
UPDATE iapolicy SET name = 'Pay in Advance' WHERE name = 'AP Advances' and module = 'ap';
/

BEGIN
DECLARE
advancepolicyid         iapolicy.record#%TYPE;
postedadvancepolicyid   iapolicy.record#%TYPE;
BEGIN
    --- select the receive advance record#
SELECT record# INTO advancepolicyid FROM iapolicy WHERE name = 'Pay in Advance' AND module = 'ap';
--- select the posted advance record#
SELECT record# INTO postedadvancepolicyid FROM iapolicy WHERE name = 'Posted Advances' AND module = 'ap';
---- update to add list to permission  where add exists
UPDATE policyassignment p1 set p1.policyval = 'list' WHERE p1.policykey = advancepolicyid and p1.policyval LIKE '%add'
/
---delete if there is no add at all
DELETE policyassignment p1 WHERE p1.policykey = advancepolicyid and p1.policyval NOT LIKE '%add'
/
---- update to add list to permission  where add exists
UPDATE rolepolicyassignment p1 set p1.policyval = 'list' WHERE p1.policykey = advancepolicyid and p1.policyval LIKE '%add'
/
---delete if there is no add at all
DELETE rolepolicyassignment p1 WHERE p1.policykey = advancepolicyid and p1.policyval NOT LIKE '%add'
/

--- update the perm_cache_valid to F for all the user that have the pay in advances permission
UPDATE userinfo u SET u.perm_cache_valid = 'F'
WHERE
EXISTS (
        SELECT 1
        FROM policyassignment pa
        WHERE pa.cny# = u.cny#
          AND pa.user_role_key = u.record#
          AND pa.policykey = advancepolicyid
    );
--- update the perm_cache_valid to F for all the role based that have the pay in advances permission
UPDATE userinfo u SET u.perm_cache_valid = 'F'
WHERE
EXISTS (
        SELECT 1
        FROM rolepolicyassignment rpa, ugroles ugr
        WHERE rpa.cny# = u.cny#
          AND ugr.cny# = u.cny#
          AND rpa.rolekey = ugr.rolekey
          AND ugr.type = 'U'
          AND ugr.u_o_gkey = u.record#
          AND rpa.policykey = advancepolicyid
    );
END;
/

BEGIN
DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/

