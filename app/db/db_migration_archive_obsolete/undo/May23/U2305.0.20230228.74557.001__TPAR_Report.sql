-- ------------------------------------------------------------------------------------------------------------------------
-- ----------------  JIRA Epic IA-74557 : TPAR report -----------------
-- ------------------------------------------------------------------------------------------------------------------------
--
--
ALTER TABLE IATAXFORMTYPE RENAME TO IAFORM1099TYPE
/

ALTER TABLE IAFORM1099TYPE DROP COLUMN TYPE
/

ALTER TABLE IAFORM1099TYPE ADD CREATEDBY NUMBER(15,0)
/

ALTER TABLE IAFORM1099TYPE ADD MODIFIEDBY NUMBER(15,0)
/

ALTER TABLE IATAXFORMBOX RENAME TO IAFORM1099BOX
/

ALTER TABLE IAFORM1099BOX DROP COLUMN TAXFORMTYPE
/

ALTER TABLE IAFORM1099PARTNERBOXFIELDMAP DROP CONSTRAINT FK_IAFORM1099PARTNERBOXFIELDMAP_FORMTYPEKEY
/

ALTER TABLE IAFORM1099PARTNERBOXFIELDMAP DROP CONSTRAINT FK_IAFORM1099PARTNERBOXFIELDMAP_FORMBOXKEY
/

ALTER TABLE FILE1099SUBMISSIONLOG DROP CONSTRAINT FK_FILE1099SUBMISSIONLOG_FORMTYPEKEY
/

ALTER TABLE IAFORM1099BOX DROP CONSTRAINT FK_IATAXFORMBOX_FORMTYPEKEY
/

ALTER TABLE IATAXFORMTYPE DROP CONSTRAINT PK_IATAXFORMTYPE
/

ALTER TABLE IATAXFORMBOX DROP CONSTRAINT PK_IATAXFORMBOX
/

DROP INDEX IX_IATAXFORMBOX_FORMTYPEKEY
/

ALTER TABLE IAFORM1099TYPE ADD CONSTRAINT PK_IAFORM1099TYPE PRIMARY KEY (RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE
/

ALTER TABLE IAFORM1099BOX ADD CONSTRAINT PK_IAFORM1099BOX PRIMARY KEY (RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE
/

ALTER TABLE IAFORM1099BOX ADD CONSTRAINT  FK_IAFORM1099BOX_FORMTYPEKEY FOREIGN KEY (FORMTYPEKEY) REFERENCES IATAXFORMTYPE (RECORD#) DEFERRABLE ENABLE
/

ALTER TABLE IAFORM1099PARTNERBOXFIELDMAP ADD CONSTRAINT  FK_IAFORM1099PARTNERBOXFIELDMAP_FORMTYPEKEY FOREIGN KEY (FORMTYPEKEY) REFERENCES IAFORM1099TYPE (RECORD#) DEFERRABLE ENABLE
/

ALTER TABLE IAFORM1099PARTNERBOXFIELDMAP ADD CONSTRAINT  FK_IAFORM1099PARTNERBOXFIELDMAP_FORMBOXKEY FOREIGN KEY (FORMBOXKEY) REFERENCES IAFORM1099BOX (RECORD#) DEFERRABLE ENABLE
/

ALTER TABLE IAFORM1099PARTNERBOXFIELDMAP ADD CONSTRAINT  FK_IAFORM1099PARTNERBOXFIELDMAP_FORMBOXKEY FOREIGN KEY (FORMBOXKEY) REFERENCES IAFORM1099BOX (RECORD#) DEFERRABLE ENABLE
/

ALTER TABLE FILE1099SUBMISSIONLOG ADD CONSTRAINT  FK_FILE1099SUBMISSIONLOG_FORMTYPEKEY FOREIGN KEY (FORMTYPEKEY) REFERENCES IAFORM1099TYPE (RECORD#) DEFERRABLE ENABLE
/

CREATE INDEX IX_IAFORM1099BOX_FORMTYPEKEY ON IAFORM1099BOX (FORMTYPEKEY) TABLESPACE ACCTINDX
/


DELETE FROM IAPOLICY WHERE MODULE = 'ap' and NAME = 'Taxable payments annual report (TPAR)'
/

ALTER TABLE LOCATION DROP COLUMN LEGALCONTACTNAME
/

ALTER TABLE LOCATION DROP COLUMN LEGALCONTACTPHONE
/

ALTER TABLE LOCATION DROP COLUMN LEGALCONTACTFAX
/

ALTER TABLE LOCATION DROP COLUMN LEGALCONTACTEMAIL
/

ALTER TABLE LOCATION DROP COLUMN LEGALBRANCHNUMBER
/

ALTER TABLE LOCATION DROP COLUMN ENABLELEGALCONTACTTPAR
/

DROP TABLE TAXFORMINITIALBALANCE
/

ALTER TABLE VENDOR DROP COLUMN ISTPARENABLED
/

ALTER TABLE VENDOR DROP COLUMN NAMETPAR
/

DROP INDEX IX_VENDOR_ISTPARENABLED
/

DELETE FROM IAFORM1099TYPE WHERE RECORD# = 10 AND ABBREVATION = 'TPAR'
/

DELETE FROM IAFORM1099BOX WHERE RECORD# in (285, 286, 287,288, 289, 290, 291) AND FORMTYPEKEY = 10
/