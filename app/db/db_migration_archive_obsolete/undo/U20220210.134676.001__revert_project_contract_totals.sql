
CREATE OR REPLACE EDITIONABLE TRIGGER BRIU_PROJECTCONTRACT BEFORE UPDATE OR INSERT
                                                                  ON PROJECTCONTRACT
                                                                      FOR EACH ROW DECLARE

BEGIN

    DBMS_APPLICATION_INFO.read_client_info (acct_utils.trigger_enabled);

    IF acct_utils.trigger_enabled <> 'F'
    THEN

        :new.totalbillednetretainage := :new.billedprice - :new.totalretainageheld + :new.totalretainagereleased;

        IF :new.totalprice > 0
        THEN
            :new.percentagebilled := (:new.billedprice * 100) / :new.totalprice;
            :new.percentagebillednetretainage := (:new.totalbillednetretainage * 100) / :new.totalprice;
END IF;

        :new.retainagebalance := :new.totalretainageheld - :new.totalretainagereleased;
        :new.balancetobill := :new.totalprice - :new.billedprice;
        :new.nettotalbilled := :new.billedtaxsubtotal - :new.billeddiscountsubtotal + :new.billedchargesubtotal + :new.billedprice;

END IF;
END BRIU_PROJECTCONTRACT;
/

CREATE OR REPLACE EDITIONABLE TRIGGER BRIU_PROJECTCONTRACTLINE BEFORE UPDATE OR INSERT
                                                                      ON PROJECTCONTRACTLINE
                                                                          FOR EACH ROW DECLARE

BEGIN

    DBMS_APPLICATION_INFO.read_client_info (acct_utils.trigger_enabled);

    IF acct_utils.trigger_enabled <> 'F'
    THEN

        :new.billednetretainage := :new.billedprice - :new.retainageheld + :new.retainagereleased;

        IF :new.totalprice > 0
        THEN
            :new.percentagebilled := (:new.billedprice * 100) / :new.totalprice;
            :new.percentagebillednetretainage := (:new.billednetretainage * 100) / :new.totalprice;
END IF;

        :new.retainagebalance := :new.retainageheld - :new.retainagereleased;
        :new.balancetobill := :new.totalprice - :new.billedprice;

END IF;
END BRIU_PROJECTCONTRACTLINE;
/

CREATE OR REPLACE FORCE VIEW v_projectcontractlinepick (cny#,projectcontractlinepick,record#,status,projectcontractkey,billable,billingtype) AS
SELECT A.CNY#,
       (A.PROJECTCONTRACTLINEID || '--' || A.DESCRIPTION) PROJECTCONTRACTLINEPICK,
       A.RECORD#,
       A.STATUS,
       A.PROJECTCONTRACTKEY,
       A.BILLABLE,
       A.BILLINGTYPE
FROM PROJECTCONTRACTLINE A;
/

CREATE OR REPLACE FORCE VIEW v_projectcontractpick (cny#,projectcontractpick,record#,contractdate) AS
SELECT A.CNY#,
       (A.PROJECTCONTRACTID || '--' || A.DESCRIPTION) PROJECTCONTRACTPICK,
       A.RECORD#,
       A.CONTRACTDATE
FROM PROJECTCONTRACT A;
