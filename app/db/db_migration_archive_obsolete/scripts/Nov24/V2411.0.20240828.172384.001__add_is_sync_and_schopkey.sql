-- DB Migration Change to add <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> to R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

ALTER TABLE RENE<PERSON>LM<PERSON><PERSON> ADD (
    IS_<PERSON>YNC CHAR(1 CHAR) DEFAULT NULL,
    SCHOPKEY NUMBER(15, 0) DEFAULT NULL,
      CONS<PERSON>AINT FK_SCHOPKEY FOREIGN KEY (CNY#, SCHOPKEY) REFERENCES SCHEDULEDOPERATION (CNY#, RECORD#) DEFERRABLE ENABLE
)
/

CREATE INDEX IX_RENEWALMACRO_SCHOPKEY ON R<PERSON>E<PERSON>LMAC<PERSON> (CNY#, SCHOPKEY) INVI<PERSON><PERSON><PERSON> TABLESPACE ACCTINDX
/
