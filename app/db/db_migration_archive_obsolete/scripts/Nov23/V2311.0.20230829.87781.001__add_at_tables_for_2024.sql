

-- 2024 January
CREATE TABLE AT_01_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_01_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_01_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_01_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_01_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_01_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WOR<PERSON><PERSON>OWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MI<PERSON>AT<PERSON>        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_01_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_01_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_01_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_01_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_01_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('01/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('02/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_01_2024 ON AT_01_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_01_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_01_2024_ID TO PUBLIC
/

CREATE TABLE AT_01_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_01_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_01_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_01_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_01_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_01_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_01_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_01_2024_FIELDS ON AT_01_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/


-- 2024 February
CREATE TABLE AT_02_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_02_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_02_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_02_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_02_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_02_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WORKFLOWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MIGRATED        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_02_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_02_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_02_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_02_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_02_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('02/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('03/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_02_2024 ON AT_02_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_02_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_02_2024_ID TO PUBLIC
/

CREATE TABLE AT_02_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_02_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_02_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_02_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_02_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_02_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_02_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_02_2024_FIELDS ON AT_02_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/


-- 2024 March
CREATE TABLE AT_03_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_03_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_03_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_03_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_03_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_03_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WORKFLOWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MIGRATED        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_03_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_03_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_03_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_03_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_03_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('03/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('04/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_03_2024 ON AT_03_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_03_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_03_2024_ID TO PUBLIC
/

CREATE TABLE AT_03_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_03_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_03_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_03_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_03_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_03_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_03_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_03_2024_FIELDS ON AT_03_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/


-- 2024 April
CREATE TABLE AT_04_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_04_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_04_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_04_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_04_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_04_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WORKFLOWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MIGRATED        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_04_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_04_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_04_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_04_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_04_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('04/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('05/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_04_2024 ON AT_04_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_04_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_04_2024_ID TO PUBLIC
/

CREATE TABLE AT_04_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_04_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_04_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_04_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_04_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_04_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_04_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_04_2024_FIELDS ON AT_04_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/


-- 2024 May
CREATE TABLE AT_05_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_05_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_05_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_05_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_05_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_05_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WORKFLOWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MIGRATED        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_05_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_05_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_05_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_05_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_05_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('05/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('06/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_05_2024 ON AT_05_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_05_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_05_2024_ID TO PUBLIC
/

CREATE TABLE AT_05_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_05_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_05_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_05_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_05_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_05_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_05_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_05_2024_FIELDS ON AT_05_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/


-- 2024 June
CREATE TABLE AT_06_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_06_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_06_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_06_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_06_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_06_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WORKFLOWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MIGRATED        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_06_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_06_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_06_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_06_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_06_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('06/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('07/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_06_2024 ON AT_06_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_06_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_06_2024_ID TO PUBLIC
/

CREATE TABLE AT_06_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_06_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_06_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_06_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_06_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_06_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_06_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_06_2024_FIELDS ON AT_06_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/


-- 2024 July
CREATE TABLE AT_07_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_07_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_07_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_07_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_07_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_07_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WORKFLOWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MIGRATED        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_07_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_07_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_07_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_07_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_07_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('07/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('08/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_07_2024 ON AT_07_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_07_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_07_2024_ID TO PUBLIC
/

CREATE TABLE AT_07_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_07_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_07_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_07_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_07_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_07_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_07_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_07_2024_FIELDS ON AT_07_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/


-- 2024 August
CREATE TABLE AT_08_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_08_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_08_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_08_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_08_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_08_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WORKFLOWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MIGRATED        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_08_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_08_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_08_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_08_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_08_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('08/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('09/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_08_2024 ON AT_08_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_08_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_08_2024_ID TO PUBLIC
/

CREATE TABLE AT_08_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_08_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_08_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_08_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_08_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_08_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_08_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_08_2024_FIELDS ON AT_08_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/


-- 2024 September
CREATE TABLE AT_09_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_09_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_09_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_09_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_09_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_09_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WORKFLOWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MIGRATED        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_09_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_09_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_09_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_09_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_09_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('09/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('10/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_09_2024 ON AT_09_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_09_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_09_2024_ID TO PUBLIC
/

CREATE TABLE AT_09_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_09_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_09_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_09_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_09_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_09_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_09_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_09_2024_FIELDS ON AT_09_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/


-- 2024 October
CREATE TABLE AT_10_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_10_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_10_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_10_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_10_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_10_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WORKFLOWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MIGRATED        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_10_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_10_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_10_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_10_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_10_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('10/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('11/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_10_2024 ON AT_10_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_10_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_10_2024_ID TO PUBLIC
/

CREATE TABLE AT_10_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_10_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_10_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_10_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_10_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_10_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_10_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_10_2024_FIELDS ON AT_10_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/


-- 2024 November
CREATE TABLE AT_11_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_11_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_11_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_11_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_11_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_11_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WORKFLOWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MIGRATED        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_11_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_11_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_11_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_11_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_11_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('11/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('12/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_11_2024 ON AT_11_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_11_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_11_2024_ID TO PUBLIC
/

CREATE TABLE AT_11_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_11_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_11_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_11_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_11_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_11_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_11_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_11_2024_FIELDS ON AT_11_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/


-- 2024 December
CREATE TABLE AT_12_2024
(
    CNY#            NUMBER(15) CONSTRAINT NN_AT_12_2024_CNY NOT NULL ENABLE,
    RECORDID        VARCHAR2(25 CHAR) CONSTRAINT NN_AT_12_2024_RECORDID NOT NULL ENABLE,
    OBJECTTYPE      VARCHAR2(100 CHAR) CONSTRAINT NN_AT_12_2024_OBJECTTYPE NOT NULL ENABLE,
    OBJECTKEY       VARCHAR2(200 CHAR) CONSTRAINT NN_AT_12_2024_OBJECTKEY NOT NULL ENABLE,
    OBJECTDESC      VARCHAR2(500 CHAR),
    USERID          VARCHAR2(200 CHAR) CONSTRAINT NN_AT_12_2024_USERID NOT NULL ENABLE,
    ACCESSTIME      DATE,
    ACCESSMODE      CHAR(1 CHAR),
    IPADDRESS       VARCHAR2(100 CHAR),
    SOURCE          VARCHAR2(6 CHAR),
    WORKFLOWACTION  VARCHAR2(50 CHAR),
    OBJ_BLOB        BLOB,
    MIGRATED        CHAR(1 CHAR),
    VERSION         NUMBER(2) DEFAULT 1,
    CONSTRAINT FK_AT_12_2024_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE,
    CONSTRAINT PK_AT_12_2024 PRIMARY KEY (CNY#,RECORDID) USING INDEX TABLESPACE IAAUDITINDX,
    CONSTRAINT CK_AT_12_2024_ACCESSMODE CHECK (ACCESSMODE IN ('C','A','M','D','W','T','U','S')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_12_2024_SOURCE CHECK (SOURCE IN ('ui','api','csv','system','sev')) ENABLE VALIDATE,
    CONSTRAINT CK_AT_12_2024_ACCESSTIME CHECK (ACCESSTIME >= TO_DATE('12/01/2024 00:00:00', 'MM/DD/YYYY HH24:MI:SS')
     and ACCESSTIME < TO_DATE('01/01/2025 00:00:00', 'MM/DD/YYYY HH24:MI:SS')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
LOB (OBJ_BLOB) STORE AS ( TABLESPACE IAAUDITLOB DISABLE STORAGE IN ROW)
/
CREATE INDEX IX_AT_12_2024 ON AT_12_2024 (CNY#, OBJECTTYPE, USERID, RECORDID) TABLESPACE IAAUDITINDX
/
CREATE SEQUENCE SEQ_AT_12_2024_ID START WITH 1 INCREMENT BY 1 NOCYCLE
/
GRANT SELECT ON SEQ_AT_12_2024_ID TO PUBLIC
/

CREATE TABLE AT_12_2024_FIELDS
(
    OBJECTFK    VARCHAR2(25 CHAR) CONSTRAINT NN_AT_12_2024_FIELDS_OBJECTFK NOT NULL ENABLE,
    FIELDNAME   VARCHAR2(200 CHAR) CONSTRAINT NN_AT_12_2024_FIELDS_FIELDNAME NOT NULL ENABLE,
    FIELDTYPE   CHAR(1 CHAR) CONSTRAINT NN_AT_12_2024_FIELDS_FIELDTYPE NOT NULL ENABLE,
    OLDSTRVAL   VARCHAR2(4000 CHAR),
    NEWSTRVAL   VARCHAR2(4000 CHAR),
    OLDINTVAL   NUMBER(15),
    NEWINTVAL   NUMBER(15),
    OLDNUMVAL   NUMBER,
    NEWNUMVAL   NUMBER,
    OLDDATEVAL  DATE,
    NEWDATEVAL  DATE,
    CNY#        NUMBER(15),
    CONSTRAINT FK_AT_12_2024 FOREIGN KEY (CNY#,OBJECTFK) REFERENCES AT_12_2024 (CNY#,RECORDID) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT CK_AT_12_2024_FIELDTYPE CHECK (FIELDTYPE IN ('T','I','N','D','B')) ENABLE VALIDATE
) TABLESPACE IAAUDITDATA
/
CREATE INDEX IX_AT_12_2024_FIELDS ON AT_12_2024_FIELDS (CNY#, OBJECTFK, FIELDNAME) TABLESPACE IAAUDITINDX
/

