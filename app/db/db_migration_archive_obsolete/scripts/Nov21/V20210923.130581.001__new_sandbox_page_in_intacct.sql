--liquibase formatted sql
--changeset andrei.ganera:V20210923.130581.001 runOnChange:false logicalFilePath:V20210923.130581.001__new_sandbox_page_in_intacct.sql

CREATE TABLE SANDBOX (
    cny# NUMBER(15,0) CONSTRAINT NN_SANDBOX_CNY NOT NULL ENABLE,
    record# NUMBER(15,0) CONSTRAINT NN_SANDBOX_RECORD NOT NULL ENABLE,
    name VARCHAR2(40 CHAR),
    description VARCHAR2(4000 CHAR),
    sandboxcny# NUMBER(15,0), -- column intentionally not set as FK to company, it can be in a different schema
    state VARCHAR2(50 CHAR) DEFAULT 'Pending' CHECK( STATE IN ('Ready', 'In Progress', 'Pending', 'Failed')),
    whenexpires DATE,
    whenrefreshed DATE,
    whencreated DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    whenmodified DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    createdby NUMBER(15,0),
    modifiedby NUMBER(15,0),
    CONSTRAINT pk_sandbox PRIMARY KEY (cny#, record#) USING INDEX TABLESPACE acctindx,
    CONSTRAINT fk_sandbox_cny FOREIGN KEY (cny#) REFERENCES company (record#) ON DELETE CASCADE DEFERRABLE ENABLE
) TABLESPACE acctdata
/


ALTER TABLE company
ADD allowed_sandboxes NUMBER(3,0) DEFAULT '0' CONSTRAINT NN_COMPANY_ALLOWED_SANDBOXES NOT NULL ENABLE
/

INSERT INTO iapolicy(record#, module, name, type, vertical)
SELECT MAX(record#) + 1, 'co', 'Sandbox', 'U', 'F'
FROM iapolicy
/
