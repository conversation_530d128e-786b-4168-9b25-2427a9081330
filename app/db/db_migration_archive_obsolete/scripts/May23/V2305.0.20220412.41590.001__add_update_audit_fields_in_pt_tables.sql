--liquibase formatted sql
--changeset jean-francois.ngo:20220412.41590.001 runOnChange:false logicalFilePath:V2305.0.20220412.41590.001__add_update_audit_fields_in_pt_tables.sql

/*

== DELTA ONLY FOR DEV SCHEMAS ==

Description:

Repair action: EXECUTE

*/

ALTER TABLE PT_WORKFLOW ADD
(
    CREATED_BY NUMBER(15,0),
	CREATED_AT DATE,
	UPDATED_BY NUMBER(15,0),
	UPDATED_AT DATE
)
/

ALTER TABLE PT_USER_FLAGS ADD
(
    CREATED_BY NUMBER(15,0),
	CREATED_AT DATE,
	UPDATED_BY NUMBER(15,0),
	UPDATED_AT DATE
)
/

ALTER TABLE PT_TRIGGER_LOGS ADD
(
	UPDATED_BY NUMBER(15,0),
	UPDATED_AT DATE
)
/

ALTER TABLE PT_SYS_RELS ADD
(
    CREATED_BY NUMBER(15,0),
	CREATED_AT DATE,
	UPDATED_BY NUMBER(15,0),
	UPDATED_AT DATE
)
/

ALTER TABLE PT_PAGE_SECTION ADD
(
    CREATED_BY NUMBER(15,0),
	CREATED_AT DATE,
	UPDATED_BY NUMBER(15,0),
	UPDATED_AT DATE
)
/

ALTER TABLE PT_PAGE_CELL ADD
(
    CREATED_BY NUMBER(15,0),
	CREATED_AT DATE,
	UPDATED_BY NUMBER(15,0),
	UPDATED_AT DATE
)
/

ALTER TABLE PT_LIST_ITEM ADD
(
    CREATED_BY NUMBER(15,0),
	CREATED_AT DATE,
	UPDATED_BY NUMBER(15,0),
	UPDATED_AT DATE
)
/

ALTER TABLE PT_CSP_VIOLATION ADD
(
    CREATED_BY NUMBER(15,0),
	CREATED_AT DATE,
	UPDATED_BY NUMBER(15,0),
	UPDATED_AT DATE
)
/

ALTER TABLE PT_APP_ISSUE ADD
(
    CREATED_BY NUMBER(15,0),
	CREATED_AT DATE,
	UPDATED_BY NUMBER(15,0),
	UPDATED_AT DATE
)
/

ALTER TABLE PT_ACT_TRAIL ADD
(
	UPDATED_BY NUMBER(15,0),
	UPDATED_AT DATE
)
/