--liquibase formatted sql
--changeset jean-francois.ngo:20220412.41589.001 runOnChange:false logicalFilePath:V2305.0.20220412.41589.001__add_mising_record#_in_pt_tables.sql

/*

== DELTA ONLY FOR DEV SCHEMAS ==

Description:

Repair action: IGNORE

*/

ALTER TABLE PT_WORKFLOW ADD RECORD# NUMBER(15,0)
/

DECLARE
BEGIN
   FOR rec IN (SELECT distinct cny# FROM PT_WORKFLOW)
   LOOP
        UPDATE PT_WORKFLOW SET record# = 10000 + ROWNUM WHERE cny# = rec.cny#;
   END LOOP;
END;
/

ALTER TABLE PT_WORKFLOW MODIFY RECORD# NUMBER(15,0) CONSTRAINT NN_PT_WORKFLOW_RECORD# NOT NULL ENABLE
/

ALTER TABLE PT_USER_FLAGS ADD RECORD# NUMBER(15,0) 
/

DECLARE
BEGIN
   FOR rec IN (SELECT distinct cny# FROM PT_USER_FLAGS)
   LOOP
        UPDATE PT_USER_FLAGS SET record# = 10000 + ROWNUM WHERE cny# = rec.cny#;
   END LOOP;
END;
/

ALTER TABLE PT_USER_FLAGS MODIFY RECORD# NUMBER(15,0) CONSTRAINT NN_PT_USER_FLAGS_RECORD# NOT NULL ENABLE
/

ALTER TABLE PT_UNIQUE_DATA ADD RECORD# NUMBER(15,0)
/

DECLARE
BEGIN
FOR rec IN (SELECT distinct cny# FROM PT_UNIQUE_DATA)
   LOOP
UPDATE PT_UNIQUE_DATA SET record# = 10000 + ROWNUM WHERE cny# = rec.cny#;
END LOOP;
END;
/

ALTER TABLE PT_UNIQUE_DATA MODIFY RECORD# NUMBER(15,0) CONSTRAINT NN_PT_UNIQUE_DATA_RECORD# NOT NULL ENABLE
/

ALTER TABLE PT_SYS_RELS ADD RECORD# NUMBER(15,0)
/

DECLARE
BEGIN
   FOR rec IN (SELECT distinct cny# FROM PT_SYS_RELS)
   LOOP
        UPDATE PT_SYS_RELS SET record# = 10000 + ROWNUM WHERE cny# = rec.cny#;
   END LOOP;
END;
/

ALTER TABLE PT_SYS_RELS MODIFY RECORD# NUMBER(15,0) CONSTRAINT NN_PT_SYS_RELS_RECORD# NOT NULL ENABLE
/

ALTER TABLE PT_RELATIONSHIP ADD RECORD# NUMBER(15,0)
/

ALTER TABLE PT_APP_LINK ADD RECORD# NUMBER(15,0)
/

DECLARE
BEGIN
   FOR rec IN (SELECT distinct cny# FROM PT_APP_LINK)
   LOOP
        UPDATE PT_APP_LINK SET record# = 10000 + ROWNUM WHERE cny# = rec.cny#;
   END LOOP;
END;
/

ALTER TABLE PT_APP_LINK MODIFY RECORD# NUMBER(15,0) CONSTRAINT NN_PT_APP_LINK_RECORD# NOT NULL ENABLE
/
