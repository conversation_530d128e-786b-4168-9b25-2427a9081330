-- Create temp table
CREATE TABLE TEMP_PATHPOL_19AUG23 AS
    SELECT * FROM PATHPOL AS OF TIMESTAMP TO_TIMESTAMP('19-Aug-2023 02:30:00','DD-MON-YYYY HH24: MI: SS') FLASHBACK
/

-- Recover Pathpol
DECLARE
    CURSOR CNYS IS SELECT RECORD# FROM COMPANY;
BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
    FOR CNY IN CNYS
        LOOP
            INSERT INTO PATHPOL
            SELECT * FROM TEMP_PATHPOL_19AUG23 FLASHBACK
            WHERE FLASHBACK.CNY# = CNY.RECORD#
              AND NOT EXISTS(
                SELECT 1 FROM PATHPOL PROD
                WHERE PROD.CNY# = CNY.RECORD#
                  AND PROD.DIRKEY = FLASHBACK.DIRKEY
                  AND PROD.U_O_GKEY = FLASHBACK.U_O_GKEY
                  AND PROD.A_O_D = FLASHBACK.A_O_D
                  AND PROD.POLICYVAL = FLASHBACK.POLICYVAL
                  AND PROD.TYPE = FLASHBACK.TYPE
                  AND PROD.POLICYKEY = FLASHBACK.POLICYKEY
            );
            COMMIT;
        END LOOP;
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/