-- Add the CCTXNTEMPLATEKEY in BANKTXNR<PERSON>LE table
ALTER TABLE BANKTXNRULE ADD CCTXNTEMPLATEKEY  NUMBER(15,0)
/
ALTER TABLE BANKTXNRULE ADD CONSTRAINT FK_BANKTXNRULE_CCTXNTMPLKEY FOREIGN KEY (CNY#, CCTXNTEMPLATEKEY)
    REFERENCES RECURPRRECORD (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
/
CREATE INDEX IX_BANKTXNRULE_CCTXNTMPLKEY ON BANKTXNRULE (CNY#, CCTXNTEMPLATEKEY) TABLESPACE ACCTINDX INVISIBLE
/
-- Add rule related columns to RECURPRRECORD
ALTER TABLE RECURPRRECORD ADD (
    TRANSACTIONSOURCE       VARCHAR2(20 CHAR) DEFAULT 'RECURPR',
    TEMPLATEID              VARCHAR2(100 CHAR),
    <PERSON>L<PERSON>                   CHAR(1 CHAR)
    )
/
ALTER TABLE RECURPRRECORD ADD CONSTRAINT CK_RECURPRRECORD_SPLIT CHECK (SPLIT IN ('P', 'A')) ENABLE
/
CREATE INDEX IX_RECURPRRECORD_TXNSOURCE ON RECURPRRECORD (CNY#, TRANSACTIONSOURCE) TABLESPACE ACCTINDX
/
CREATE UNIQUE INDEX IX_RECURPRRECORD_TMPLID ON RECURPRRECORD
(
    CASE WHEN TEMPLATEID IS NOT NULL THEN CNY# END,
    CASE WHEN TEMPLATEID IS NOT NULL THEN TEMPLATEID END
) TABLESPACE ACCTINDX
/
-- Add TEMPLATEID in RECURGLBATCH
ALTER TABLE RECURGLBATCH ADD TEMPLATEID  VARCHAR2(100 CHAR)
/
CREATE UNIQUE INDEX IX_RECURGLBATCH_TMPLID ON RECURGLBATCH
(
    CASE WHEN TEMPLATEID IS NOT NULL THEN CNY# END,
    CASE WHEN TEMPLATEID IS NOT NULL THEN TEMPLATEID END
) TABLESPACE ACCTINDX
/
-- Disable triggers
begin dbms_application_info.set_client_info('F'); end;
/
-- Default the template id with record#
UPDATE RECURGLBATCH SET TEMPLATEID = RECORD# WHERE TRANSACTIONSOURCE = 'BANK' AND TEMPLATEID IS NULL
/
-- Enable triggers
begin dbms_application_info.set_client_info('T'); end;
/