INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.ACCOUNTNO', 'BANKACCOUNT, VENDOR', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.ROUTINGNO', 'BANKACCOUNT', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.CARDNUM', 'CREDITCARD', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.TAXID', 'COMPANY', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.FEDERALID', 'COMPANY', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.USERID', 'DELIVERYSERVICE', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.OAUTHTOKEN', 'DELIVERYSERVICE', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.OAUTHSECRET', 'DELIVERYSERVICE', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.SS', 'EMPLOYEE', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.BIRTHDATE', 'EMPLOYEE', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.CERTIFICATE', 'SSO_APP', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.CSQANSWER', 'USERINFO', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.MFA_INFO', 'USERINFO', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.FILEDATA', 'BANKFILE', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.NEWPASSWORD', 'IMSPARTNERINFO', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.OLDPASSWORD', 'IMSPARTNERINFO', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.OTP', 'IMSPARTNERINFO', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.SHAREDSECRET', 'IMSPARTNERINFO', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.AUTHCODE', 'IMSPARTNERINFO, PASSWORDRESETAUTHCODE', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.REQUEST_CACHE', 'PROVIDERBANKACCOUNT, PROVIDERVENDOR', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.PROPERTIES', 'SUBSYSTEM', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.SSO_FEDERATED_ID', 'USERPREF', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.SFORCEUSERKEY', 'USERPREF', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.ACH_AUTH_NET_ID', 'MODULEPREF: 40.CCP (Payment Services)', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.ACH_AUTH_NET_TRAN_KEY', 'MODULEPREF: 40.CCP (Payment Services)', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.AVA_DASH_PASSWORD', 'MODULEPREF: 43.AVA (Avalara Tax)', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.AVA_KEY', 'MODULEPREF: 43.AVA (Avalara Tax)', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.MERCHANT_AUTH_NET_TRAN_KEY', 'MODULEPREF: 40.CCP (Payment Services)', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.MERCHANT_AUTH_NET_ID', 'MODULEPREF: 40.CCP (Payment Services)', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.MERCHANT_PASSWORD', 'MODULEPREF: 40.CCP (Payment Services)', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.LICENSEKEY', 'MODULEPREF: 53.CHAT (Intacct Collaborate)', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.PASSWORD', 'USERINFO, MODULEPREF: 50.ZUORA (Intacct-Zuora Integration), 49.CLARIZEN (Intacct-Clarizen Integration), 44.QARROW (Intacct-QuickArrow Integration)', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.SFORCEPWD', 'MODULEPREF: 38.SFDC (Intacct-Salesforce Integration)', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.SFORCEADMIN', 'MODULEPREF: 58.SFDC2, 38.SFDC (Intacct-Salesforce Integration), 61.SFDC2 (Advanced CRM Integration)', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.ACHACCOUNTNUMBER', '', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.ACHBANKROUTINGNUMBER', '', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_data.ADMINPASSWORD', '', 'intacct')
/
INSERT INTO ia_recording_params(param, notes, createdbyuser) VALUES ('_passwd', '', 'intacct')
/
