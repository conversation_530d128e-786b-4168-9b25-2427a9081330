--OWNER_CONTACT:<EMAIL>
/*

== DELTA ONLY FOR DEV SCHEMAS ==

Description:

Repair action: IGNORE

*/
--create payroll report pto accrual schedule table
CREATE TABLE PAYROLLREPORTPTOACCRUALSCHEDULE(
   CNY# NUMBER(15) CONSTRAINT NN_PYLRTPTOACUSCH_CNY NOT NULL ENABLE,
   RECORD# NUMBER(15) CONSTRAINT NN_PYLRTPTOACUSCH_RECORD# NOT NULL ENABLE,
   PTOACCRUALSCHEDULEID VARCHAR2(100 CHAR) NOT NULL,
   EFFECTIVEDATE DATE NOT NULL,
   EXPIRATIONDATE DATE,
   PTOTYPEID VARCHAR2(100 CHAR),
   REVISIONNUMBER NUMBER(30, 10),
   LOCATIONKEY NUMBER(15, 0) DEFAULT sys_context('TMCtx', 'LOCATIONKEY'),
   WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
   WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
   CREATEDBY NUMBER(15, 0),
   MODIFIEDBY NUMBER(15, 0),
   SI_UUID VARCHAR2(36 CHAR),
   CONSTRAINT PK_PYLRTPTOACUSCH PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
   CONSTRAINT FK_PYLRTPTOACUSCH_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
   CONSTRAINT FK_PYLRTPTOACUSCH_LOCATIONKEY FOREIGN KEY (CNY#, LOCATIONKEY) REFERENCES LOCATION (CNY#, RECORD#) DEFERRABLE ENABLE,
   CONSTRAINT FK_PYLRTPTOACUSCH_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
   CONSTRAINT FK_PYLRTPTOACUSCH_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
   CONSTRAINT UQ_PYLRTPTOACUSCH_PTOID_EFDATE UNIQUE (CNY#, PTOACCRUALSCHEDULEID, EFFECTIVEDATE) USING INDEX TABLESPACE ACCTINDX ENABLE
)TABLESPACE ACCTDATA
/
CREATE INDEX IX_PYLRTPTOACUSCH_CNY ON PAYROLLREPORTPTOACCRUALSCHEDULE (CNY#) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPTOACUSCH_LOCATIONKEY ON PAYROLLREPORTPTOACCRUALSCHEDULE (CNY#, LOCATIONKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPTOACUSCH_CREATEDBY ON PAYROLLREPORTPTOACCRUALSCHEDULE ( CNY#, CREATEDBY ) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPTOACUSCH_MODIFIEDBY ON PAYROLLREPORTPTOACCRUALSCHEDULE ( CNY#, MODIFIEDBY ) TABLESPACE ACCTINDX INVISIBLE
/


--create payroll report pto accrual schedule line table
CREATE TABLE PAYROLLREPORTPTOACCRUALSCHEDULELINE(
    CNY# NUMBER(15) CONSTRAINT NN_PYLRTPTOACLINE_CNY NOT NULL ENABLE,
    RECORD# NUMBER(15) CONSTRAINT NN_PYLRTPTOACLINE_RECORD# NOT NULL ENABLE,
    PTOACCRUALSCHEDULEKEY NUMBER(15, 0) NOT NULL,
    MINIMUMYEARSOFSERVICE NUMBER(30, 10) NOT NULL,
    MAXIMUMYEARSOFSERVICE NUMBER(30, 10),
    MAXIMUMHOURSACCRUEDPERYEAR NUMBER(30, 10),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
    CREATEDBY NUMBER(15, 0),
    MODIFIEDBY NUMBER(15, 0),
    SI_UUID VARCHAR2(36 CHAR),
    CONSTRAINT PK_PYLRTPTOACLINE PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT FK_PYLRTPTOACLINE_PTOKEY FOREIGN KEY (CNY#, PTOACCRUALSCHEDULEKEY) REFERENCES PAYROLLREPORTPTOACCRUALSCHEDULE (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_PYLRTPTOACLINE_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_PYLRTPTOACLINE_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PYLRTPTOACLINE_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT UQ_PYLRTPTOACLINE_PTO_MINIMUM UNIQUE (CNY#, PTOACCRUALSCHEDULEKEY, MINIMUMYEARSOFSERVICE) USING INDEX TABLESPACE ACCTINDX ENABLE
)TABLESPACE ACCTDATA
/
CREATE INDEX IX_PYLRTPTOACLINE_CNY ON PAYROLLREPORTPTOACCRUALSCHEDULELINE (CNY#) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPTOACLINE_PTOKEY ON PAYROLLREPORTPTOACCRUALSCHEDULELINE (CNY#, PTOACCRUALSCHEDULEKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPTOACLINE_CREATEDBY ON PAYROLLREPORTPTOACCRUALSCHEDULELINE ( CNY#, CREATEDBY ) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPTOACLINE_MODIFIEDBY ON PAYROLLREPORTPTOACCRUALSCHEDULELINE ( CNY#, MODIFIEDBY ) TABLESPACE ACCTINDX INVISIBLE
/

---Not started
--create payroll report pto accrual schedule rate table
CREATE TABLE PAYROLLREPORTPTOACCRUALSCHEDULERATE(
        CNY# NUMBER(15) CONSTRAINT NN_PYLRTPTOACRATE_CNY NOT NULL ENABLE,
        RECORD# NUMBER(15) CONSTRAINT NN_PYLRTPTOACRATE_RECORD# NOT NULL ENABLE,
        PTOACCRUALSCHEDULELINEKEY NUMBER(15, 0) NOT NULL,
        WORKEDHOURSID VARCHAR2(100 CHAR) NOT NULL,
        HOURSACCRUEDPERHOURWORKED NUMBER(30, 10),
        WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
        WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
        CREATEDBY NUMBER(15, 0),
        MODIFIEDBY NUMBER(15, 0),
        SI_UUID VARCHAR2(36 CHAR),
        CONSTRAINT PK_PYLRTPTOACRATE PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
        CONSTRAINT FK_PYLRTPTOACRATE_PTOLINEKEY FOREIGN KEY (CNY#, PTOACCRUALSCHEDULELINEKEY) REFERENCES PAYROLLREPORTPTOACCRUALSCHEDULELINE (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
        CONSTRAINT FK_PYLRTPTOACRATE_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
        CONSTRAINT FK_PYLRTPTOACRATE_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
        CONSTRAINT FK_PYLRTPTOACRATE_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
        CONSTRAINT UQ_PYLRTPTOACRATE_TRADEID_LEVEL UNIQUE (CNY#, PTOACCRUALSCHEDULELINEKEY, WORKEDHOURSID) USING INDEX TABLESPACE ACCTINDX ENABLE
)TABLESPACE ACCTDATA
/
CREATE INDEX IX_PYLRTPTOACRATE_CNY ON PAYROLLREPORTPTOACCRUALSCHEDULERATE (CNY#) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPTOACRATE_PTOLINEKEY ON PAYROLLREPORTPTOACCRUALSCHEDULERATE (CNY#, PTOACCRUALSCHEDULELINEKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPTOACRATE_CREATEDBY ON PAYROLLREPORTPTOACCRUALSCHEDULERATE ( CNY#, CREATEDBY ) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPTOACRATE_MODIFIEDBY ON PAYROLLREPORTPTOACCRUALSCHEDULERATE ( CNY#, MODIFIEDBY ) TABLESPACE ACCTINDX INVISIBLE
/