--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_ARD_SICOLLABORATIONSETUP.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER ARD_SICOL<PERSON><PERSON>RATIONSETUP AFTER DELETE ON SICOLLABORATIONSETUP FOR EACH ROW
BEGIN
  dbms_application_info.read_client_info(acct_utils.trigger_enabled);
  IF acct_utils.trigger_enabled <> 'F' THEN
    -- add the billing event
    INSERT INTO ia_message_queue (message_queue_id, cny#, type, action, payload)
    SELECT message_queue_seq.nextval, :old.cny#, 'MODULEPREF', 'DELETE',
      json_object(
        'company_record' VALUE :old.cny#,
        'module_record' VALUE :old.modulekey,
        'entity_record' VALUE null,
        'properties' VALUE (
          json_array(
            json_object(
              'property' VALUE 'ENABLECOLLAB'
            )
          )
        )
      ) AS json_value
    FROM company c
    WHERE c.record# = :old.cny#
      AND c.ia_billing > 2;
  END IF;
END ARD_SICOLLABORATIONSETUP;
/
