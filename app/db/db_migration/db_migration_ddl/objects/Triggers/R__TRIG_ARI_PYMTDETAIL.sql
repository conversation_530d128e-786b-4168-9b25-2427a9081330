--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_ARI_PYMTDETAIL.sql
--OWNER_CONTACT: <EMAIL>,raj<PERSON>.<EMAIL>,<EMAIL>,ravi.shan<PERSON><PERSON><PERSON><PERSON><PERSON>@sage.com

CREATE OR REPLACE TRIGGER ARI_PYMTDETAIL
AFTER INSERT ON PYMTDETAIL
FOR EACH ROW DECLARE
    -- Record Types for holding data to minimize variable clutter
    payment_details         SL_PYMT_UTILS.payment_record;
    record_details          SL_PYMT_UTILS.txn_record;
    var_parentpymt          PRENTRYPYMTRECS.PARENTPYMT%TYPE;
    var_currency            VARCHAR2(3);
    var_baseBillForeignBank VARCHAR2(1) := 'F';
BEGIN
    -- Check if triggers are enabled
    dbms_application_info.read_client_info(acct_Utils.Trigger_Enabled);
    -- If triggers are disabled, return
    IF acct_Utils.Trigger_Enabled = 'F' THEN RETURN; END IF;

    -- Retrieve record and set up for different payment cases
    IF (:NEW.RECORDKEY IS NOT NULL OR :NEW.POSADJKEY IS NOT NULL) THEN
        IF (:NEW.POSADJKEY IS NOT NULL) THEN
            record_details.recordkey := :NEW.POSADJKEY;
            record_details.paiditemkey := :NEW.POSADJENTRYKEY;
        ELSE
            record_details.recordkey := :NEW.RECORDKEY;
            record_details.paiditemkey := :NEW.ENTRYKEY;
        END IF;

        -- Set the currency and foreign bank flag
        SL_PYMT_UTILS.setcurrencyandforeignbankflag
        (
            :NEW.CNY#,
            record_details.recordkey,
            :NEW.CURRENCY,
            var_currency,
            var_baseBillForeignBank
        );

        FOR i IN 1..8 LOOP
            payment_details.paymentkey := null;
            payment_details.payitemkey := null;
            CASE i
                WHEN 1 THEN
                    IF :NEW.TRX_PAYMENTAMOUNT > 0 THEN
                        payment_details := SL_PYMT_UTILS.payment_record(:NEW.PAYMENTKEY, :NEW.PAYMENTENTRYKEY, :NEW.PAYMENTAMOUNT, :NEW.TRX_PAYMENTAMOUNT);
                    END IF;
                WHEN 2 THEN
                    payment_details := SL_PYMT_UTILS.payment_record(:NEW.DISCOUNTKEY, :NEW.DISCOUNTENTRYKEY, -1 * :NEW.DISCOUNTAMOUNT, -1 * :NEW.TRX_DISCOUNTAMOUNT);
                WHEN 3 THEN
                    payment_details := SL_PYMT_UTILS.payment_record(:NEW.POSTEDADVANCEKEY, :NEW.POSTEDADVANCEENTRYKEY, :NEW.POSTEDADVANCEAMOUNT, :NEW.TRX_POSTEDADVANCEAMOUNT);
                WHEN 4 THEN
                    payment_details := SL_PYMT_UTILS.payment_record(:NEW.INLINEKEY, :NEW.INLINEENTRYKEY, :NEW.INLINEAMOUNT, :NEW.TRX_INLINEAMOUNT);
                WHEN 5 THEN
                    payment_details := SL_PYMT_UTILS.payment_record(:NEW.ADJUSTMENTKEY, :NEW.ADJUSTMENTENTRYKEY, :NEW.ADJUSTMENTAMOUNT, :NEW.TRX_ADJUSTMENTAMOUNT);
                WHEN 6 THEN
                    payment_details := SL_PYMT_UTILS.payment_record(:NEW.POSTEDOVERPAYMENTKEY, :NEW.POSTEDOVERPAYMENTENTRYKEY, :NEW.POSTEDOVERPAYMENTAMOUNT, :NEW.TRX_POSTEDOVERPAYMENTAMOUNT);
                WHEN 7 THEN
                    payment_details := SL_PYMT_UTILS.payment_record(:NEW.NEGBILLINVKEY, :NEW.NEGBILLINVENTRYKEY, :NEW.NEGBILLINVAMOUNT, :NEW.TRX_NEGBILLINVAMOUNT);
                WHEN 8 THEN
                    payment_details := SL_PYMT_UTILS.payment_record(:NEW.EXCHGAINLOSSKEY, :NEW.EXCHGAINLOSSENTRYKEY, :NEW.EXCHGAINLOSSAMOUNT, NULL);
                    var_parentpymt := :NEW.EXCHGAINLOSSPARENTPYMTKEY;
            END CASE;

            IF(payment_details.paymentkey IS NULL) THEN CONTINUE; END IF;

            IF (:NEW.PAYMENTKEY IS NOT NULL) THEN
                var_parentpymt := :NEW.PAYMENTKEY;
            ELSIF (var_parentpymt IS NULL) THEN
                var_parentpymt := payment_details.paymentkey;
            END IF;

            SL_PYMT_UTILS.processpayment(
                payment_details,
                record_details,
                var_currency,
                :NEW.CNY#,
                :NEW.PARENTPAYMENTKEY,
                var_parentpymt,
                :NEW.STATE,
                :NEW.PAYMENTDATE,
                :NEW.WHENCREATED,
                :NEW.CREATEDBY,
                var_baseBillForeignBank,
                'F' -- isrefund
            );
        END LOOP;

    ELSIF (:NEW.REFUNDKEY IS NOT NULL AND :NEW.REFUNDENTRYKEY IS NOT NULL) THEN
        -- populate the paymentkey and payitemkey
        IF (:NEW.ADJUSTMENTKEY IS NOT NULL) THEN
            payment_details.paymentkey := :NEW.ADJUSTMENTKEY;
            payment_details.payitemkey := :NEW.ADJUSTMENTENTRYKEY;
        ELSIF (:NEW.ADVANCEKEY IS NOT NULL) THEN
            payment_details.paymentkey := :NEW.ADVANCEKEY;
            payment_details.payitemkey := :NEW.ADVANCEENTRYKEY;
        ELSIF (:NEW.NEGBILLINVKEY IS NOT NULL) THEN
            payment_details.paymentkey := :NEW.NEGBILLINVKEY;
            payment_details.payitemkey := :NEW.NEGBILLINVENTRYKEY;
        ELSIF (:NEW.OVERPAYMENTKEY IS NOT NULL) THEN
            payment_details.paymentkey := :NEW.OVERPAYMENTKEY;
            payment_details.payitemkey := :NEW.OVERPAYMENTENTRYKEY;
        END IF;
        -- populate the recordkey and paiditemkey
        record_details := SL_PYMT_UTILS.txn_record(:NEW.REFUNDKEY, :NEW.REFUNDENTRYKEY, :NEW.REFUNDAMOUNT, :NEW.TRX_REFUNDAMOUNT);

        var_parentpymt := :NEW.REFUNDKEY;
        -- set the payment trx amount & amount
        payment_details.amount := :NEW.REFUNDAMOUNT;
        payment_details.trx_amount := :NEW.TRX_REFUNDAMOUNT;

        SL_PYMT_UTILS.setcurrencyandforeignbankflag
        (
            :NEW.CNY#,
            payment_details.paymentkey, -- in case of refund payment key is credits, so pass as recordkey here
            :NEW.CURRENCY,
            var_currency,
            var_baseBillForeignBank
        );

        SL_PYMT_UTILS.processpayment
        (
            payment_details,
            record_details,
            var_currency,
            :NEW.CNY#,
            :NEW.PARENTPAYMENTKEY,
            var_parentpymt,
            :NEW.STATE,
            :NEW.PAYMENTDATE,
            :NEW.WHENCREATED,
            :NEW.CREATEDBY,
            var_baseBillForeignBank,
            'T' -- isrefund
        );

    ELSE
        -- populate the recordkey and paiditemkey
        IF (:NEW.INLINEKEY IS NOT NULL) THEN
            record_details.recordkey := :NEW.INLINEKEY;
            record_details.paiditemkey := :NEW.INLINEENTRYKEY;
        ELSIF (:NEW.ADJUSTMENTKEY IS NOT NULL) THEN
            record_details.recordkey := :NEW.ADJUSTMENTKEY;
            record_details.paiditemkey := :NEW.ADJUSTMENTENTRYKEY;
        ELSIF (:NEW.ADVANCEKEY IS NOT NULL) THEN
            record_details.recordkey := :NEW.ADVANCEKEY;
            record_details.paiditemkey := :NEW.ADVANCEENTRYKEY;
        ELSIF (:NEW.NEGBILLINVKEY IS NOT NULL) THEN
            record_details.recordkey := :NEW.NEGBILLINVKEY;
            record_details.paiditemkey := :NEW.NEGBILLINVENTRYKEY;
        ELSIF (:NEW.OVERPAYMENTKEY IS NOT NULL) THEN
            record_details.recordkey := :NEW.OVERPAYMENTKEY;
            record_details.paiditemkey := :NEW.OVERPAYMENTENTRYKEY;
        END IF;

        -- set the payment details, trx_amount is NULL
        payment_details := SL_PYMT_UTILS.payment_record(:NEW.EXCHGAINLOSSKEY, :NEW.EXCHGAINLOSSENTRYKEY, :NEW.EXCHGAINLOSSAMOUNT, NULL);

        IF (payment_details.paymentkey IS NOT NULL) THEN
            var_parentpymt := :NEW.EXCHGAINLOSSPARENTPYMTKEY;
            SL_PYMT_UTILS.setcurrencyandforeignbankflag
            (
                :NEW.CNY#,
                record_details.recordkey,
                :NEW.CURRENCY,
                var_currency,
                var_baseBillForeignBank
            );

            SL_PYMT_UTILS.processpayment
            (
                payment_details,
                record_details,
                var_currency,
                :NEW.CNY#,
                :NEW.PARENTPAYMENTKEY,
                var_parentpymt,
                :NEW.STATE,
                :NEW.PAYMENTDATE,
                :NEW.WHENCREATED,
                :NEW.CREATEDBY,
                var_baseBillForeignBank,
                'F' -- isrefund
            );
        END IF; -- end payment_details.paymentkey IS NOT NULL
    END IF;

EXCEPTION
  WHEN OTHERS THEN
    -- Log error and raise application error
    RAISE_APPLICATION_ERROR(-20001, 'Error while insert into pymtdetails: ' || SQLERRM);
END;
/
