--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__PKGBODY_IA_UTILS.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE PACKAGE BODY IA_UTILS AS
  PROCEDURE DELIMSTRING
    ( p_delimstring IN VARCHAR2
    , p_table OUT varchar2_table
    , p_delim IN VARCHAR2 DEFAULT '|'
    )
  IS
    v_string VARCHAR2(32767) := p_delimstring;
    v_nfields INTEGER := 1;
    v_table varchar2_table := varchar2_table();
    v_delimpos INTEGER := INSTR(p_delimstring, p_delim);
    v_delimlen INTEGER := LENGTH(p_delim);
  BEGIN
    WHILE v_delimpos > 0
    LOOP
      v_table.EXTEND;
      v_table(v_nfields) := SUBSTR(v_string,1,v_delimpos-1);
      v_string := SUBSTR(v_string,v_delimpos+v_delimlen);
      v_nfields := v_nfields+1;
      v_delimpos := INSTR(v_string, p_delim);
    END LOOP;
    v_table.EXTEND;
    v_table(v_nfields) := v_string;
    p_table := v_table;
  END DELIMSTRING;

  PROCEDURE CONCATSTRING
    ( p_table IN varchar2_table
    , p_concatstring OUT VARCHAR2
    , p_delim IN VARCHAR2 DEFAULT '|'
    )
  IS
    v_concatstring varchar2(32767) := '';
  BEGIN
    for idx in 1 .. p_table.COUNT loop
      if(idx <> 1) then
        v_concatstring := v_concatstring || p_delim || p_table(idx);
      else
        v_concatstring := v_concatstring || p_table(idx);
      end if;
    end loop;
    p_concatstring := v_concatstring;
  END CONCATSTRING;

END IA_UTILS;
/
