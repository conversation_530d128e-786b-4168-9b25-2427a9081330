--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__PKG_INV_UTILS.sql
--OWNER_CONTACT: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

CREATE OR REPLACE PACKAGE inv_utils
IS
    IsDocReposting CHAR(1 CHAR) default 'T';
     type src_det is record(
          cny#		          docentry.cny#%type,
          docentrykey	      docentry.record#%type,
          dochdrkey	        docentry.dochdrkey%type,
          itemkey           docentry.itemkey%type,
          source_dockey	     docentry.source_dockey%type,
          source_doclinekey  docentry.source_doclinekey%type,
          source_quantity	docentry.quantity%type,
          source_value	    docentry.value%type,
          totalname	     ictotal.name%type,
          totalkey	     ictotal.record#%type
       );

       TYPE t_source_details IS
        TABLE OF src_det;

      g_source_details t_source_details := t_source_details ();

    type row_det is record(
          cny#		            docentry.cny#%type,
          record#	            docentry.record#%type,
          dochdrkey	            docentry.dochdrkey%type,
          itemkey               docentry.itemkey%type,
          warehousekey          docentry.warehousekey%type,
          cost                  docentry.cost%type,
          quantity              docentry.quantity%type,
          value                 docentry.value%type,
          whencreated           docentry.whencreated%type,
          locationkey           docentry.locationkey%type,
          deptkey               docentry.deptkey%type,
          source_dockey         docentry.source_dockey%type,
          source_doclinekey     docentry.source_doclinekey%type,
          item_type             icitem.itemtype%TYPE,
          backorder             dochdr.backorder%TYPE,
          old_cny#              docentry.cny#%type,
          old_record#           docentry.record#%type,
          old_dochdrkey         docentry.dochdrkey%type,
          relateddockey         docentry.relateddockey%type,
          relateddoclinekey     docentry.relateddoclinekey%type
       );

       TYPE t_row_details IS
        TABLE OF row_det;

      g_row_details t_row_details := t_row_details ();

   PROCEDURE updateicitemtotalsinsert (
      a_cny#           IN   icitemactivity.cny#%TYPE,
      a_itemkey        IN   icitemactivity.itemkey%TYPE,
      a_warehousekey   IN   icitemactivity.warehousekey%TYPE,
      a_totalkey       IN   icitemactivity.totalkey%TYPE,
      a_quantity       IN   icitemactivity.quantity%TYPE,
      a_value          IN   icitemactivity.VALUE%TYPE,
      a_whencreated    IN   icitemactivity.whencreated%TYPE,
      a_locationkey    IN   icitemactivity.locationkey%TYPE,
      a_deptkey        IN   icitemactivity.deptkey%TYPE
   );
   PROCEDURE updateicitemtotalsdelete (
      a_cny#           IN   icitemactivity.cny#%TYPE,
      a_itemkey        IN   icitemactivity.itemkey%TYPE,
      a_warehousekey   IN   icitemactivity.warehousekey%TYPE,
      a_totalkey       IN   icitemactivity.totalkey%TYPE,
      a_quantity       IN   icitemactivity.quantity%TYPE,
      a_value          IN   icitemactivity.VALUE%TYPE,
      a_whencreated    IN   icitemactivity.whencreated%TYPE,
      a_locationkey    IN   icitemactivity.locationkey%TYPE,
      a_deptkey        IN   icitemactivity.deptkey%TYPE
   );
   PROCEDURE inserticitemactivityondocentry (
      a_cny#           IN   docentry.cny#%TYPE,
      a_docentrykey    IN   docentry.record#%TYPE,
      a_dochdrkey      IN   docentry.dochdrkey%TYPE,
      a_itemkey        IN   docentry.itemkey%TYPE,
      a_warehousekey   IN   docentry.warehousekey%TYPE,
      a_cost           IN   docentry.COST%TYPE,
      a_quantity       IN   docentry.quantity%TYPE,
      a_value          IN   docentry.VALUE%TYPE,
      a_whencreated    IN   docentry.whencreated%TYPE,
      a_locationkey    IN   docentry.locationkey%TYPE,
      a_deptkey        IN   docentry.deptkey%TYPE,
      a_source_dockey  IN   docentry.source_dockey%TYPE,
      a_backorder      IN   dochdr.backorder%TYPE,
      a_autodeduct_desired IN OUT BOOLEAN,
      a_relateddockey       IN   docentry.relateddockey%TYPE,
      a_relateddoclinekey   IN   docentry.relateddoclinekey%TYPE
   );
   PROCEDURE processitemondocentry (
      a_cny#              IN   docentry.cny#%TYPE,
      a_docentrykey       IN   docentry.record#%TYPE,
      a_dochdrkey         IN   docentry.dochdrkey%TYPE,
      a_itemkey           IN   docentry.itemkey%TYPE,
      a_warehousekey      IN   docentry.warehousekey%TYPE,
      a_cost              IN   docentry.COST%TYPE,
      a_quantity          IN   docentry.quantity%TYPE,
      a_value             IN   docentry.VALUE%TYPE,
      a_whencreated       IN   docentry.whencreated%TYPE,
      a_locationkey       IN   docentry.locationkey%TYPE,
      a_deptkey           IN   docentry.deptkey%TYPE,
      a_source_dockey     IN   docentry.source_dockey%TYPE,
      a_source_doclinekey IN   docentry.source_doclinekey%TYPE,
      a_old_cny#          IN  docentry.cny#%TYPE,
      a_old_docentrykey   IN  docentry.record#%TYPE,
      a_old_dochdrkey     IN docentry.dochdrkey%TYPE,
      a_item_type          IN   icitem.itemtype%TYPE,
      a_backorder         IN   dochdr.backorder%TYPE,
      a_relateddockey     IN   docentry.relateddockey%TYPE,
      a_relateddoclinekey IN   docentry.relateddoclinekey%TYPE
   );
   PROCEDURE updatedochdrondochdrsubtotals (
      a_cny#         IN   dochdrsubtotals.cny#%TYPE,
      a_dochdrkey    IN   dochdrsubtotals.dochdrkey%TYPE,
      a_absval       IN   dochdrsubtotals.absval%TYPE,
      a_percentval   IN   dochdrsubtotals.percentval%TYPE,
      a_total        IN   dochdrsubtotals.total%TYPE,
      a_trx_total    IN   dochdrsubtotals.trx_total%TYPE
   );
   PROCEDURE inserticitemactondocentrycost (
      a_cny#           IN   docentrycost.cny#%TYPE,
      a_docentrykey    IN   docentrycost.docentrykey%TYPE,
      a_dochdrkey      IN   docentrycost.cny#%TYPE,
      a_itemkey        IN   docentrycost.itemkey%TYPE,
      a_warehousekey   IN   docentrycost.whsekey%TYPE,
      a_cost           IN   docentrycost.COST%TYPE,
      a_quantity       IN   docentrycost.quantity%TYPE,
      a_value          IN   docentrycost.VALUE%TYPE,
      a_whencreated    IN   docentrycost.datein%TYPE,
      a_locationkey    IN   docentrycost.cny#%TYPE,
      a_deptkey        IN   docentrycost.cny#%TYPE
   );
   PROCEDURE upsertdetotals (
  de_cny IN docentry.cny#%TYPE,
  de_dochdrkey IN docentry.dochdrkey%TYPE,
  de_docclass IN docpar.docclass%TYPE,
  de_spi IN docpar.sale_pur_trans%TYPE,
  de_entity IN dochdr.entity%TYPE
   );
   PROCEDURE deletedetotals (
  de_cny IN docentry.cny#%TYPE,
  de_dochdrkey IN docentry.dochdrkey%TYPE,
  de_docclass IN docpar.docclass%TYPE,
  de_spi IN docpar.sale_pur_trans%TYPE,
  de_entity IN dochdr.entity%TYPE
   );
   PROCEDURE BuildDETotals(cny in NUMBER);

   FUNCTION eval(val in VARCHAR2) return NUMBER;

    v_dochdrkey NUMBER;
    v_cny NUMBER;
	procedure dochdrentitytotalupdate (
		p_cnykey	IN DOCHDRENTITYTOTAL.CNY#%TYPE,
		p_dochdrkey	IN DOCHDR.RECORD#%TYPE,
		p_locationkey	IN DOCHDRENTITYTOTAL.locationkey%TYPE,
		p_trx_totalentered	IN DOCHDRENTITYTOTAL.trx_totalentered%TYPE,
		p_totalentered	IN DOCHDRENTITYTOTAL.totalentered%TYPE,
		p_trx_subtotal	IN DOCHDRENTITYTOTAL.trx_subtotal%TYPE,
		p_subtotal	IN DOCHDRENTITYTOTAL.subtotal%TYPE
    );
	procedure dochdrentitytotaldelete (
		p_cnykey	IN DOCHDRENTITYTOTAL.CNY#%TYPE,
		p_dochdrkey	IN DOCHDR.RECORD#%TYPE,
		p_locationkey	IN DOCHDRENTITYTOTAL.locationkey%TYPE,
		p_trx_totalentered	IN DOCHDRENTITYTOTAL.trx_totalentered%TYPE,
		p_totalentered	IN DOCHDRENTITYTOTAL.totalentered%TYPE,
		p_trx_subtotal	IN DOCHDRENTITYTOTAL.trx_subtotal%TYPE,
		p_subtotal	IN DOCHDRENTITYTOTAL.subtotal%TYPE
    );
    procedure builddochdrentitytotal (
		p_cnykey	IN DOCHDRENTITYTOTAL.CNY#%TYPE
    );
END inv_utils;
/
