--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__PKG_MC.sql
--OWNER_CONTACT: <EMAIL>,<EMAIL>

create or replace PACKA<PERSON> MC IS
  TYPE KeyArrayType IS TABLE OF NUMBER INDEX BY BINARY_INTEGER;

  deptBelow KeyArrayType;
  deptBoth KeyArrayType;
  dept KeyArrayType;

  locBelow KeyArrayType;
  locBoth KeyArrayType;
  loc KeyArrayType;

  cny COMPANY.RECORD#%TYPE;

  procedure initAll(ctx CHAR);
  procedure initCommonContext;
  procedure initViewContext(ctx CHAR);
  procedure reset;

  function allowCny(aCny number) RETURN NUMBER;

  function allowDeptBelow(rec# number) RETURN NUMBER;
  function allowDeptBoth(rec# number) RETURN NUMBER;
  function allowDept(rec# number) RETURN NUMBER;

  function allowLocBelow(rec# number) RETURN NUMBER;
  function allowLocBoth(rec# number) RETURN NUMBER;
  function allowLoc(rec# number) RETURN NUMBER;
  procedure populateHybridContextTable(sessKey USERPROF.SESSION#%TYPE, ctx CHAR);

END MC;
/
