--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__PROC_UPDATETASKRESOURCESPROGRESS.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE procedure updateTaskResourcesProgress(
    a_cny company.record#%type,
    a_taskkey task.record#%type,
    a_employeekey employee.record#%type )
is
  cursor taskresources_cursor is
    select record#, pbegindate, penddate
    from taskresources
    where cny# = a_cny
      and taskkey = a_taskkey
      and employeekey = a_employeekey
      and pbegindate is not null
    order by pbegindate;
  type resultType is table of taskresources_cursor%ROWTYPE;
  results resultType;

  ts_begindate task.pbegindate%type;
  ts_enddate task.penddate%type;
  max_enddate task.penddate%type;
  recordCount number;
  config_hoursinday number;
  config_uom char(2);
  indx integer;

begin
  if a_taskkey is null or a_employeekey is null then
    return;
  end if;
  -- fetch configuration information regarding hours in a day and units of measurement
  begin
    select to_number(value)
    into config_hoursinday
    from modulepref
    where cny#       = a_cny
    and modulekey    = '48.PROJACCT'
    and property     = 'TSHOURSINDAY'
    and (locationkey = sys_context('TMCtx', 'LOCATIONKEY')
    or (locationkey is null
    and not exists
      (select 1
      from modulepref
      where cny#      = a_cny
      and modulekey   = '48.PROJACCT'
      and property    = 'TSHOURSINDAY'
      and locationkey = sys_context('TMCtx', 'LOCATIONKEY')
      )));
    select value
    into config_uom
    from modulepref
    where cny#       = a_cny
    and modulekey    = '48.PROJACCT'
    and property     = 'TSUOM'
    and (locationkey = sys_context('TMCtx', 'LOCATIONKEY')
    or (locationkey is null
    and not exists
      (select 1
      from modulepref
      where cny#      = a_cny
      and modulekey   = '48.PROJACCT'
      and property    = 'TSUOM'
      and locationkey = sys_context('TMCtx', 'LOCATIONKEY')
      )));
  exception
  when no_data_found then
    return;
  end;

  -- find out how many records we are dealing with
  select count(1) into recordCount from taskresources where cny# = a_cny and taskkey = a_taskkey and employeekey = a_employeekey;

  if recordCount = 0 then
    return;
  end if;

  if recordCount = 1 then
    -- only one task resource assignment - assign all relevant timesheetentries to it - no need to fetch record# and dates
    updateSingleTaskResource(a_cny, a_taskkey, a_employeekey, null, null, null, config_hoursinday, config_uom);
  else

    -- read all of the task resources associated with this task and employee with a valid begindate (we know there's at least one)
    open taskresources_cursor;
    fetch taskresources_cursor bulk collect into results;
    close taskresources_cursor;

    if results.count = 1 then
      -- only one valid target - assign all relevant timesheetentries to this specific record
      updateSingleTaskResource(a_cny, a_taskkey, a_employeekey, results(1).record#, null, null, config_hoursinday, config_uom);
    else
      -- first fix missing end dates and overlaps
      for indx in 1..results.count loop
        -- if end date empty, set end date = begin date
        if (results(indx).penddate is null) then
          results(indx).penddate := results(indx).pbegindate;
        end if;
        -- look for overlaps
        if (indx = 1) then
          max_enddate := results(indx).penddate;
        else
          -- if it is a complete overlap, then delete this entry
          if (results(indx).penddate <= max_enddate) then
            results.delete(indx);
            continue;
          end if;
          -- if a partial overlap, move the begin date
          if (results(indx).pbegindate <= max_enddate) then
            results(indx).pbegindate := max_enddate + 1;
          end if;
          -- save largest end date for overlap detection
          if (results(indx).penddate > max_enddate) then
            max_enddate := results(indx).penddate;
          end if;
        end if;
      end loop;

      -- now we can issue updates for all entries
      indx := results.first;
      while indx is not null loop
        ts_begindate := null;
        ts_enddate := null;
        -- if not first entry, limit the timesheetentry start date
        if (indx != results.first) then
          ts_begindate := results(indx).pbegindate;
        end if;
        -- if not last entry, limit the timesheetentry end date
        if (indx != results.last) then
          ts_enddate := results(results.next(indx)).pbegindate;
        end if;
        -- assign all timesheetentries in this range to this specific task resource record
        updateSingleTaskResource(a_cny, a_taskkey, a_employeekey, results(indx).record#, ts_begindate, ts_enddate, config_hoursinday, config_uom);
        indx := results.next(indx);
      end loop;
    end if;
  end if;

end updateTaskResourcesProgress;
/
