--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__PROC_UPDATETASKPROGRESS.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE procedure updateTaskProgress(
  a_cny company.record#%TYPE,
  a_taskkey task.record#%TYPE
) IS
  actualstartdate task.abegindate%type;
  actualenddate task.aenddate%type;
  workedhours task.actualqty%type;
  approvedhours task.approvedqty%type;
  config_hoursinday number;
  config_uom char(2);
BEGIN

  IF a_taskkey is null THEN
    RETURN;
  END IF;

  BEGIN
    select to_number(value) into config_hoursinday
    from modulepref
    where cny# = a_cny and modulekey = '48.PROJACCT' and property = 'TSHOURSINDAY'
          and (locationkey = sys_context('TMCtx', 'LOCATIONKEY')  or (locationkey is null and not exists (select 1 from modulepref
          where cny# = a_cny and modulekey = '48.PROJACCT' and property = 'TSHOURSINDAY' and locationkey = sys_context('TMCtx', 'LOCATIONKEY'))));

    select value into config_uom
    from modulepref
    where cny# = a_cny and modulekey = '48.PROJACCT' and property = 'TSUOM'
          and (locationkey = sys_context('TMCtx', 'LOCATIONKEY')  or (locationkey is null and not exists (select 1 from modulepref
          where cny# = a_cny and modulekey = '48.PROJACCT' and property = 'TSUOM' and locationkey = sys_context('TMCtx', 'LOCATIONKEY'))));
  EXCEPTION
      WHEN NO_DATA_FOUND THEN
        RETURN;
  END;

  select /*+ LEADING(E) INDEX(E IX_TIMESHEETENTRY_TASKKEY) */
    nvl(sum( decode(t.uom,
        'D', e.qty * nvl(t.hoursinday, config_hoursinday),
        e.qty
    )),0),
    nvl(sum( decode( e.state,
        'A', decode(t.uom,
          'D', e.qty * nvl(t.hoursinday, config_hoursinday),
          e.qty),
        0
    )),0),
    min(e.entrydate),
    max(e.entrydate)
  into workedhours, approvedhours, actualstartdate, actualenddate
  from timesheetentry e, timesheet t
  where e.cny# = a_cny and e.taskkey = a_taskkey and e.state != 'R' and t.cny# = e.cny# and t.record# = e.timesheetkey ;

  update task
  set
    abegindate = actualstartdate
    , aenddate = decode(taskstatus, 'C', actualenddate, null)
    , actualqty = decode(config_uom, 'D', workedhours / config_hoursinday, workedhours)
    , approvedqty = decode(config_uom, 'D', approvedhours / config_hoursinday, approvedhours)
    , remainingqty = greatest(nvl(nvl(estqty, budgetqty),0) - decode(config_uom, 'D', workedhours / config_hoursinday, workedhours), 0)
    , percentcomplete = least(case when nvl(nvl(estqty, budgetqty),0) != 0 then decode(config_uom, 'D', approvedhours / config_hoursinday, approvedhours) / nvl(nvl(estqty, budgetqty),0) else null end, 1)
  where cny# = a_cny and record# = a_taskkey;

END updateTaskProgress;
/
