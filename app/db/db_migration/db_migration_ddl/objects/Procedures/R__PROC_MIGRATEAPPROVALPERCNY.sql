--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__PROC_MIGRATEAPPROVALPERCNY.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE PROCEDURE migrateApprovalperCNY (strict_validate IN NUMBER, debug_log IN NUMBER)
IS
  success_counter NUMBER;
  failure_counter NUMBER;
  skip_counter NUMBER;
  total_count NUMBER;
  migrate_result NUMBER;

  BEGIN
    dbms_output.enable(buffer_size => NULL);

    dbms_output.put_line('--------------------------------------------------------------------------------------------');
    dbms_output.put_line('Starting to migrate approval setup for all companies in this schema ...');
    dbms_output.put_line('-------------------------------------------------------------------------------------------');

    success_counter := 0;
    failure_counter := 0;
    skip_counter := 0;

    FOR ROW IN (
      SELECT record#, title, migrated
      FROM temp_migrateapprovals
    )
    LOOP
      IF row.migrated = 'F' THEN
        migrateapprovalsetup(row.record#, row.title, strict_validate, debug_log, migrate_result);
        IF migrate_result = 1 THEN
          success_counter := success_counter + 1;
        ELSIF migrate_result < 0 THEN
          failure_counter := failure_counter + 1;
        ELSE
          skip_counter := skip_counter + 1;
        END IF;
      ELSE
        -- skip migration if company is already migrated
        dbms_output.put_line('Skipping migration for already migrated company --> ' || TO_CHAR(row.title) || ' CNY# --> ' || row.record#);
        skip_counter := skip_counter + 1;
      END IF;
    END LOOP;

    SELECT count(1) INTO total_count FROM temp_migrateapprovals;

    dbms_output.put_line('---------------------------------------------------------------------------------------------');
    dbms_output.put_line('Completed Approvals Migration run on ' || total_count || ' total companies ' );
    dbms_output.put_line('Succesfull migration for ' || success_counter || ' companies ');
    dbms_output.put_line('Skipped migration for ' || skip_counter || ' companies ');
    dbms_output.put_line('Failed migration for ' || failure_counter || ' companies ');
    dbms_output.put_line('---------------------------------------------------------------------------------------------');

END migrateapprovalperCNY;
/
