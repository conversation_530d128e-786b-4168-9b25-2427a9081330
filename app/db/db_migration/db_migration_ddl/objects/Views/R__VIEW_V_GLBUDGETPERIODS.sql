--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_GLBUDGETPERIODS.sql

CREATE OR REPLACE FORCE VIEW v_glbudgetperiods (record#,cny#,budgetrec,"NAME",start_date,end_date,amount,basedon,growby,perperiod) AS
select
        GLBUDGETTYPE.RECORD#,
        GLBudget.CNY#,
        GLBudget.account# || '@' || GLBudget.dept# || '@' ||
GLBudget.location# || '@' || GLBudget.budgetkey as BUDGETREC,
        GLBUDGETTYPE.NAME,
        GLBUDGETTYPE.START_DATE,
        GLBUDGETTYPE.END_DATE,
        trim(to_char((GLBUDGET.AMOUNT *
NVL(baseaccount.normal_balance,1)),'*************.99')) as AMOUNT,
        decode(GLBUDGET.BASEDON,'B','budget','A','amount','E','employee','')
as BASEDON,
        GLBUDGET.GROWBY,
        decode(GLBUDGET.PERPERIOD,'A','actual','P','percent','') as
PERPERIOD
    from GLBudget,GLBUDGETTYPE,baseaccount
    where
        GLBudget.cny# = GLBUDGETTYPE.cny# and
        GLBUDGET.BUD_TYPE# = GLBUDGETTYPE.RECORD# and
        GLBUDGETTYPE.BUDGETING = 'T' and
        baseaccount.record# = glbudget.account# and
        baseaccount.cny# = glbudget.cny#



 
 
 
 
 ;