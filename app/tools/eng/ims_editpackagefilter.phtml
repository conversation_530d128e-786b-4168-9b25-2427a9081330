<?php

require_once 'imsPackageFilterManager.cls';
require_once 'ims_include.inc';

ims_queue::createAppSessionNoCredentials();

$_r      = Request::$r->_r;
$_sess   = Request::$r->_sess;
$_op     = Request::$r->_op;
$_do     = Request::$r->_do;
$_status = Request::$r->_status;
$_done   = Request::$r->_done;
$id      = Request::$r->id;
$_ret    = Request::$r->_ret;
$hlpfile = &Request::$r->hlpfile;
$_save   = Request::$r->_save;

$_cny    = Request::$r->_cny;
$_recipient    = Request::$r->_recipient;
$_loginid    = Request::$r->_loginid;
$_topic    = Request::$r->_topic;
$_package_action    = Request::$r->_package_action;
$_package_details    = Request::$r->_package_details;
$_starttime    = Request::$r->_starttime;
$_endtime    = Request::$r->_endtime;
$_reason    = Request::$r->_reason;
$_action = Request::$r->_action;

$gErr = Globals::$g->gErr;

$splitResult = explode(".F_DB=", Request::$r->_done);
$schemaId = explode('&',$splitResult[1])[0];
SetDBSchemaVars($schemaId);

$imspackagefiltermanager = new imsPackageFilterManager();

/**
 * @param int $cny
 *
 * @return bool
 */
function setCny($cny){
    Backend_Init::Init();

    $query1 = "SELECT TITLE
               FROM COMPANY WHERE RECORD# = :1";
    $sqlstate = array($query1, $cny);
    if(QueryResult($sqlstate)) {
        Backend_Init::SetEnvironment(isl_trim($cny));
        return true;
    }
    return false;
}

if($_r) {
    $packagefilter = $imspackagefiltermanager->get($_r);
    if (!$packagefilter) {
        $gErr->AddDBError("GetPackageFilter", __FILE__ . ':' . __LINE__, $packagefilter);
    }
    if(!setCny($packagefilter['CNY#'])) {
        $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__, "The company is not in this schema");
    }
}

if($_save) {
    if ( ! $_r ) {
        if ( ! isl_trim($_cny) ) {
            $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__, "The cny# cannot be empty");
        }
        if ( ! isl_trim($_endtime) ) {
            $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__, "The end time cannot be empty");
        }
        if ( ! $imspackagefiltermanager->validateDate(isl_trim($_endtime)) ) {
            $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__, "The format for end time is wrong. It should be: m-d-Y H:i:s");
        }
        else {
            $auxEndTime = DateTime::createFromFormat('m-d-Y H:i:s', isl_trim($_endtime));
        }
        if ( ! isl_trim($_reason) ) {
            $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__, "The reason cannot be empty");
        }
        if(isl_trim($_starttime)) {
            if ( ! $imspackagefiltermanager->validateDate(isl_trim($_starttime)) ) {
                $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__,
                                "The format for start time is wrong. It should be: m-d-Y H:i:s");
            } else {
                $auxStartTime = DateTime::createFromFormat('m-d-Y H:i:s', isl_trim($_starttime));
            }
            if ( $auxStartTime && strtotime($auxStartTime->format('d-m-Y H:i:s')) < 0 ) {
                $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__,
                                "The date should be after 1st of January 1970");
            }
        }
        if($auxEndTime && $auxStartTime && strtotime($auxStartTime->format('d-m-Y H:i:s')) > strtotime($auxEndTime->format('d-m-Y H:i:s'))){
            $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__, "The endtime should be after starttime");
        }

        if (!HasErrors()) {
            $packagefilter = [
                'CNY#'            => isl_trim($_cny),
                'RECIPIENT'       => isl_trim($_recipient),
                'LOGINID'         => isl_trim($_loginid),
                'TOPIC'           => isl_trim($_topic),
                'PACKAGE_ACTION'  => isl_trim($_package_action),
                'PACKAGE_DETAILS' => isl_trim($_package_details),
                'ENDTIME'         => isl_trim($_endtime),
                'REASON'          => isl_trim($_reason),
                'ACTION'          => isl_trim($_action),
                'STATE'          => 'Active'
            ];

            $packagefilter['STARTTIME'] = isl_trim($_starttime) ?: date('m-d-Y H:i:s');

            if(!setCny($_cny)){
                $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__, "The company is not in this schema");
            }
            if (!HasErrors()) {
                $MM = $gManagerFactory->getManager('cosetup');
                $aux = $MM->GetNextSequence('IMSPACKAGEFILTER');
                while(QueryResult(array("SELECT RECORD# FROM IMSPACKAGEFILTER WHERE RECORD# = :1",$aux))){
                    $aux = $MM->GetNextSequence('IMSPACKAGEFILTER');
                }
                $packagefilter['RECORDNO'] = $aux;

                if (!$imspackagefiltermanager->add($packagefilter)) {
                    $gErr->AddDBError("AddPackageFilter", __FILE__ . ':' . __LINE__, $packagefilter);
                }
            }
        }
    } else if ( isset($_do) && $_do == 'edit' ) {
        $starttime = DateTime::createFromFormat('m-d-Y H:i:s', $packagefilter['STARTTIME']);
        if($imspackagefiltermanager->validateDate(isl_trim($_endtime))) {
            $auxEndTime = DateTime::createFromFormat('m-d-Y H:i:s', isl_trim($_endtime));
            $packagefilter['ENDTIME'] = isl_trim($_endtime);
            if(strtotime($starttime->format('d-m-Y H:i:s')) > strtotime($auxEndTime->format('d-m-Y H:i:s'))) {
                $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__, "The endtime should be after starttime");
            }
        }
        else{
            $gErr->addError("PL03000004", __FILE__ . ':' . __LINE__, "The format for end time is wrong. It should be: m-d-Y H:i:s");
        }

        if(!HasErrors()) {
            $editLock = new Lock();
            $editLockName = 'imspackagefilteractions' . $_r;
            if ( $editLock->lockHeld($editLockName) || ! $editLock->setLock($editLockName, 900, false) ) {
                $msg = "Another user is currently working on this transaction. Wait a few minutes, and then try again";
                $msg2 = "Go back to the table and press Edit again.";
                $gErr->addError('BL01001973', __FILE__ . '.' . __LINE__, $msg, $msg2);
            }
        }
        if (!HasErrors()) {
            if ( ! $imspackagefiltermanager->set($packagefilter) ) {
                $gErr->AddDBError("UpdatePackageFilter", __FILE__ . ':' . __LINE__, $packagefilter);
            }
            $editLock->releaseLock();
        }
    }
} elseif (isset($_do) && $_do == 'del') {
    $deleteLock = new Lock();
    $deleteLockName = 'imspackagefilteractions' . $_r;
    if ( $deleteLock->lockHeld($deleteLockName) || ! $deleteLock->setLock($deleteLockName, 900, false) ) {
        $msg = "Another user is currently working on this transaction. Wait a few minutes, and then try again";
        $gErr->addError('BL01001973', __FILE__ . '.' . __LINE__, $msg);
    }
    if(!HasErrors()) {
        if ( ! $imspackagefiltermanager->Delete($packagefilter) ) {
            $gErr->AddDBError("DeletePackageFilter", __FILE__ . ':' . __LINE__, $packagefilter);
        }
        $deleteLock->releaseLock();
    }
}

// If we're done (Save/Save Field/Add/Delete), redirect to _done.
if ( $gErr->hasErrors() ) {
    CaptureErrors();
} elseif ($_ret) {
    Ret();
}

$title = 'IMS Package filter: Edit Package Filter Information';
$hlpfile = 'Department_Information';
$focus = '';?>
<?
Head('#FFFFFF', '#000000', '#006699', '#990000', '#000000', $focus, 'ac');
?>
<form name = ac action="<? echo GoUrl("ims_editpackagefilter.phtml"); ?>" method=post>
    <input type=hidden name='.sess' value="<? echo util_encode($_sess); ?>">
    <input type=hidden name=".r" value="<?echo util_encode($_r); ?>">
    <input type=hidden name=".ret" value="1">
    <input type=hidden name=".op" value="<? echo util_encode($_op); ?>">
    <input type=hidden name=".do" value="<? echo util_encode($_do); ?>">
    <?
    if ($_do == 'view') {
        $buttons = array( 'cancel');
        $optsbuttons = '';
        $gray_msg_text = '';
    } else {
        $buttons = '';
        $optsbuttons = [
            '0' => array('save'),
            '1' => array('cancel',$_doclose)
        ];
        $gray_msg_text = "Click Save to save changes";
    }

    htmlHeaderBar($title, $gray_msg_text, $buttons, $optsbuttons, $_quickadd);
    ?>
    <p style="margin:40px 0 0 0"></p>
    <table border=0 cellpadding=2 cellspacing=0 width="100%" bgcolor="#DEDEDE">
        <tr>
            <td align=right valign=middle>&nbsp;</td>
            <td align=right valign=middle>&nbsp;</td>
        </tr>
        <? if($_r) { ?>
            <tr>
                <td align=right valign=top width="50%">
                    <font  color="#FF0000" face="verdana, arial"  >
                        *Company id
                    </font>
                </td>
                <td valign=top width="50%">
                    <font face="Verdana, Arial, Helvetica"></font>
                    <input type=text name=".cny" disabled value="<? echo isl_htmlspecialchars($packagefilter['CNY#']) ?>" size=15 maxlength=15 >
                </td>
            </tr>
            <?
        } else  {?>
            <tr>
                <td align=right valign=top>
                    <font color="#FF0000" face="verdana, arial"  >
                        *Company Id
                    </font>
                </td>
                <td valign=top>
                    <input type=text name=".cny" size=15 maxlength=15 >
                    <font face="Verdana, Arial, Helvetica"></font>
                </td>
            </tr>
            <?
        } ?>
        <? if($_r) { ?>
            <tr>
                <td align=right valign=top width="50%">
                    <font face="verdana, arial">
                        Topic
                    </font>
                </td>
                <td valign=top width="50%">
                    <font face="Verdana, Arial, Helvetica"></font>
                    <input type=text name=".topic" disabled value="<? echo isl_htmlspecialchars($packagefilter['TOPIC']) ?>" size="40" maxlength=80 >
                </td>
            </tr>
            <?
        } else  {?>
            <tr>
                <td align=right valign=top>
                    <font face="verdana, arial">
                        Topic
                    </font>
                </td>
                <td valign=top>
                    <input type=text name=".topic" size=40 maxlength=80>
                    <font face="Verdana, Arial, Helvetica"></font>
                </td>
            </tr>
            <?
        } ?>
        <? if($_r) { ?>
            <tr>
                <td align=right valign=top width="50%">
                    <font face="verdana, arial"  >
                        Recipient
                    </font>
                </td>
                <td valign=top width="50%">
                    <font face="Verdana, Arial, Helvetica"></font>
                    <input type=text name=".topic" disabled value="<? echo isl_htmlspecialchars($packagefilter['RECIPIENT']) ?>" size="40" maxlength=80 >
                </td>
            </tr>
            <?
        } else  {?>
        <tr>
            <td align=right valign=top>
                <font face="verdana, arial"  >
                    Recipient
                </font>
            </td>
            <td valign=top>
                <input type=text name=".recipient" size=40 maxlength=80>
                <font face="Verdana, Arial, Helvetica"></font>
            </td>
        </tr>
        <?
        } ?>
        <? if($_r) { ?>
            <tr>
                <td align=right valign=top width="50%">
                    <font face="verdana, arial"  >
                        Login Id
                    </font>
                </td>
                <td valign=top width="50%">
                    <font face="Verdana, Arial, Helvetica"></font>
                    <input type=text name=".loginid" disabled value="<? echo isl_htmlspecialchars($packagefilter['LOGINID']) ?>" size="40" maxlength=80 >
                </td>
            </tr>
            <?
        } else  {?>
            <tr>
                <td align=right valign=top>
                    <font face="verdana, arial"  >
                        Login Id
                    </font>
                </td>
                <td valign=top>
                    <input type=text name=".loginid" size=40 maxlength=80>
                    <font face="Verdana, Arial, Helvetica"></font>
                </td>
            </tr>
            <?
        } ?>
        <? if($_r) { ?>
            <tr>
                <td align=right valign=top width="50%">
                    <font face="verdana, arial"  >
                        Package action
                    </font>
                </td>
                <td valign=top width="50%">
                    <font face="Verdana, Arial, Helvetica"></font>
                    <input type=text name=".package_action" disabled value="<? echo isl_htmlspecialchars($packagefilter['PACKAGE_ACTION']) ?>" size="40" maxlength=100 >
                </td>
            </tr>
            <?
        } else  {?>
            <tr>
                <td align=right valign=top>
                    <font face="verdana, arial"  >
                        Package action
                    </font>
                </td>
                <td valign=top>
                    <input type=text name=".package_action" placeholder="Here should be a regular expression" size=40 maxlength=100 >
                    <font face="Verdana, Arial, Helvetica"></font>
                </td>
            </tr>
            <?
        } ?>
        <? if($_r) { ?>
            <tr>
                <td align=right valign=top width="50%">
                    <font face="verdana, arial"  >
                        Package details
                    </font>
                </td>
                <td valign=top width="50%">
                    <font face="Verdana, Arial, Helvetica"></font>
                    <input type=text name=".package_details" disabled value="<? echo isl_htmlspecialchars($packagefilter['PACKAGE_DETAILS']) ?>" size="40" maxlength=1000 >
                </td>
            </tr>
            <?
        } else  {?>
            <tr>
                <td align=right valign=top>
                    <font face="verdana, arial"  >
                        Package details
                    </font>
                </td>
                <td valign=top>
                    <input type=text name=".package_details" placeholder="Here should be a regular expression" size=40 maxlength=1000 >
                    <font face="Verdana, Arial, Helvetica"></font>
                </td>
            </tr>
            <?
        } ?>
        <? if($_r) { ?>
            <tr>
                <td align=right valign=top width="50%">
                    <font face="verdana, arial"  >
                        Start time
                    </font>
                </td>
                <td valign=top width="50%">
                    <font face="Verdana, Arial, Helvetica"></font>
                    <input type=text name=".starttime" disabled value="<? echo $packagefilter['STARTTIME'] ?>" size="40" maxlength=1000 >
                </td>
            </tr>
        <? } else { ?>
            <tr>
                <td align=right valign=top>
                    <font face="verdana, arial"  >
                        Start time
                    </font>
                </td>
                <td valign=top>
                    <input type=text name=".starttime" placeholder="m-d-Y H:i:s" value="<? echo date('m-d-Y H:i:s') ?>">
                    <font face="Verdana, Arial, Helvetica"></font>
                </td>
            </tr>
        <? } ?>
        <? if($_r) { ?>
            <tr>
                <td align=right valign=top>
                    <font color="#FF0000" face="verdana, arial"  >
                        *End time
                    </font>
                </td>
                <td valign=top>
                    <input type=text placeholder="m-d-Y H:i:s" name=".endtime" value="<? echo $packagefilter['ENDTIME'] ?>">
                    <font face="Verdana, Arial, Helvetica"></font>
                </td>
            </tr>
        <? } else { ?>
            <tr>
                <td align=right valign=top>
                    <font color="#FF0000" face="verdana, arial"  >
                        *End time
                    </font>
                </td>
                <td valign=top>
                    <input type=text placeholder="m-d-Y H:i:s" name=".endtime">
                    <font face="Verdana, Arial, Helvetica"></font>
                </td>
            </tr>
        <? } ?>
        <? if($_r) { ?>
            <tr>
                <td align=right valign=top width="50%">
                    <font color="#FF0000" face="verdana, arial"  >
                        *Reason
                    </font>
                </td>
                <td valign=top width="50%">
                    <font face="Verdana, Arial, Helvetica"></font>
                    <input type=text name=".package_details" disabled value="<? echo isl_htmlspecialchars($packagefilter['REASON']) ?>" size="40" maxlength=1000 >
                </td>
            </tr>
            <?
        } else  {?>
        <tr>
            <td align=right valign=top>
                <font color="#FF0000" face="verdana, arial"  >
                    *Reason
                </font>
            </td>
            <td valign=top>
                <input type=text name=".reason" size=40 maxlength=1000 >
                <font face="Verdana, Arial, Helvetica"></font>
            </td>
        </tr>
    <?
    } ?>
        <? if($_r) { ?>
        <tr>
            <td align=right valign=top>
                <font color="#FF0000" face="verdana, arial">
                    *Action
                </font>
            </td>
            <td valign=top>
                <input type=text disabled name=".action" size=40 maxlength=80 value="<? echo isl_htmlspecialchars($packagefilter['ACTION']) ?>" >
                <font face="Verdana, Arial, Helvetica"></font>
            </td>
        </tr>
            <? }
            else { ?>
        <tr>
            <td align=right valign=top>
                <font color="#FF0000" face="verdana, arial">
                    *Action
                </font>
            </td>
            <td valign=top>
                <?php
                echo '<input type="radio" name=".action" value="Park" checked>';
                echo '<label for="parkAction">Park</label><br>';

                echo '<input type="radio" name=".action" value="Discard">';
                echo '<label for="discardAction">Discard</label>';
                ?>
            </td>
        </tr>
        <? } ?>
    </table>
    <?
    htmlButtonFooter($gray_msg_text, $buttons, $optsbuttons);
    ?>
</form>
