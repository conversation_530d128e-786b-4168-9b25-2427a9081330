
var OrderTypeLookup = {
    'I': 'Initial',
    'A': 'Add-on',
    'R': 'Reduction',
    'C': 'Cancelation'
};

var OrderStatusLookup = {
    'T': 'To Do',
    'P': 'Placed',
    'D': 'Draft',
    'R': 'Received',
    'E': 'Error',
    'C': 'Complete'
};

var OrgTypeDisp = { 'E': 'Enterprise Edition',
    'U': 'Unlimited Edition',
    'D': 'Developer Edition' };

var LicenseStatusLookup = {
    'U': 'Unintialized',
    'L': 'Live',
    'C': 'To-cancel',
    'X': 'Canceled'
};



function showSummaryTable(licenses, orders, prodCount) {

    var numLicenses = 0;
    var numIAManaged = 0;
    var numUserManaged = 0;
    var numUsers = 0;
    var numPlatform = 0;
    var numAdmin = 0;
    var numOrders = 0;
    var numTodo = 0;
    var numPlaced = 0;
    var numErrors = 0;
    var numDraft = 0;
    var numReceived = 0;
    var numComplete = 0;

    for ( var license of licenses ) {

        if ( license.STATUS == 'C' || license.STATUS == 'X' ) {
            continue;
        }

        numLicenses++;

        if ( license.OWNORG == 'T' ) {
            numUserManaged++;
        } else {
            numIAManaged++;
        }

        numUsers += license.NUM_USERS;
        numPlatform += license.NUM_USERS;
        numUsers += license.NUM_ADMINS;
        numAdmin += license.NUM_ADMINS;

        numOrders += license.ORDERS_TOTAL;
        numTodo += license.ORDERS_TODO;
        numPlaced += license.ORDERS_PLACED;
        numErrors += license.ORDERS_ERROR;
        numDraft += license.ORDERS_DRAFT;
        numReceived += license.ORDERS_RECEIVED;
        numComplete += license.ORDERS_COMPLETE;
    }


    var adoption = numLicenses / prodCount;

    $('#subscriptions_total').text(numLicenses);
    $('#subscriptions_iamanaged').text(numIAManaged.toLocaleString());
    $('#subscriptions_usermanaged').text(numUserManaged.toLocaleString());
    $('#subscriptions_adoption').text(adoption.toLocaleString(undefined, { style: 'percent' }));
    $('#subscriptions_adoption').attr('title', 'out of ' + prodCount.toLocaleString() + ' production tenants');

    setDashboardValueWithError($('#orders_error'), numErrors);
    setDashboardValueWithError($('#orders_todo'), numTodo);
    setDashboardValueWithError($('#orders_submitted'), numPlaced);
    setDashboardValueWithError($('#orders_draft'), numDraft);
    $('#orders_received').text(numReceived.toLocaleString());
    $('#orders_complete').text(numComplete.toLocaleString());


    $('#licenses_total').text(numUsers.toLocaleString());
    $('#licenses_total').attr('title', numPlatform.toLocaleString() + ' user and ' + numAdmin.toLocaleString() + ' admin licenses');
//    $('#licenses_platform').text(numPlatform.toLocaleString());
//    $('#licenses_admin').text(numAdmin.toLocaleString());
}

function setDashboardValueWithError($elem, value) {
    $elem.text(value.toLocaleString());
    if ( value > 0 ) {
        $elem.addClass('red');
    } else {
        $elem.removeClass('red');
    }
}


function getLicenseTable(licenses, options) {


    var $table = $('<table>').addClass('table').addClass('table-condensed')
        .append($('<tr>')
            .append($('<th>').text('CNY#'))
            .append($('<th>').text('Company ID'))
            .append($('<th>').text('Company Name'))
            .append($('<th>').text('Order Status'))
            .append($('<th>').text('Type'))
            .append($('<th>').addClass('center').text('Licenses'))
            .append($('<th>').text('Date Subscribed'))
            .append($('<th>').text(''))
    );

    $.each(licenses, function(index, license) {

        if ( options.OWNORG && options.OWNORG != license.OWNORG ) {
            return;
        }

        if ( options.ORDERS_TODO && license.ORDERS_TODO == 0 ) {
            return;
        }

        if ( options.ORDERS_PLACED && license.ORDERS_PLACED == 0 ) {
            return;
        }

        if ( options.ORDERS_DRAFT && license.ORDERS_DRAFT == 0 ) {
            return;
        }

        if ( options.ORDERS_RECEIVED && license.ORDERS_RECEIVED == 0 ) {
            return;
        }

        if ( options.ORDERS_ERROR && license.ORDERS_ERROR == 0 ) {
            return;
        }

        if ( options.ORDERS_COMPLETE && license.ORDERS_COMPLETE == 0 ) {
            return;
        }

        var searchExp = new RegExp(options.SEARCH, 'i');

        if ( options.SEARCH && license.CNY != options.SEARCH && ! searchExp.test(license.CO_TITLE) && ! searchExp.test(license.CO_NAME) ) {
            return;
        }

        var errorClass = '';
        var errorState = "OK";
        if ( license.ERROR_STATE == 'E' ) {
            errorClass = 'red';
            errorState = 'Error';
        }

        var numLicenses = license.NUM_USERS + license.NUM_ADMINS;
        var coTitle = ! license.CO_TITLE ? '- deleted -' : license.CO_TITLE;
        var coName = ! license.CO_NAME ? '' : license.CO_NAME;

        var title = 'record: ' + license.RECORDNO
                  + ', sfdc org id: ' + license.SFDC_ORG_ID
                  + ', org type: ' + OrgTypeDisp[license.ORGTYPE]
                  + ', status: ' + LicenseStatusLookup[license.STATUS];
                  + ', service agreement accepted '+ license.SA_ACCEPT_DATE;
        if ( license.CANCEL_ORDER_DATE ) {
            title = title + ', cancel date: ' + license.CANCEL_ORDER_DATE;
        }
        title = title + ', users: ' + license.NUM_USERS
                      + ', admins: ' + license.NUM_ADMINS;

        $table.append($('<tr>').attr('title', title)
            .append($('<td>').text(license.CNY))
            .append($('<td>').text(coTitle))
            .append($('<td>').text(coName))
            .append($('<td>').addClass(errorClass).text(errorState))
            .append($('<td>').text(license.OWNORG == 'T' ? "User Managed" : "IA Managed"))
            .append($('<td>').addClass('right').text(numLicenses.toLocaleString()))
            .append($('<td>').text(license.INITIAL_ORDER_DATE ? license.INITIAL_ORDER_DATE : ''))
            .append($('<td>').append($('<button>').addClass('view_orders_button').attr('license_key', license.RECORDNO).attr('cny', license.CNY).text('Orders')))
        );

    });

    return $table;
}

function filterOrders(orders, filters) {

    var ret = [];

    $.each(orders, function(index, order) {

        if ( filters.LICENSE_KEY && filters.LICENSE_KEY != order.LICENSE_KEY ) {
            return;
        }

        if ( filters.CNY && filters.CNY != order.CNY ) {
            return;
        }

        if ( filters.ORDERS_TODO && order.STATUS != 'T' ) {
            return;
        }

        if ( filters.ORDERS_PLACED && order.STATUS != 'P' ) {
            return;
        }

        if ( filters.ORDERS_DRAFT && order.STATUS != 'D' ) {
            return;
        }

        if ( filters.ORDERS_RECEIVED && order.STATUS != 'R' ) {
            return;
        }

        if ( filters.ORDERS_ERROR && order.STATUS != 'E' ) {
            return;
        }

        if ( filters.ORDERS_COMPLETE && order.STATUS != 'C' ) {
            return;
        }

        ret.push(order);

    });

    return ret;


}

function createPopupOrdersDisplay(license, orders) {

    var $record = $('<div>').attr('id', 'record');

    $('<div>')
        .css({ 'padding-bottom': '20px', 'text-align': 'right', 'cursor': 'pointer'})
        .addClass('close-record')
        .append(
            $('<i>').addClass('fa').addClass('fa-times')
        )
        .click(closeRecord)
        .appendTo($record);

    if ( license ) {

        var coTitle = ! license.CO_TITLE ? license.CNY : license.CO_TITLE;
        var coName = ! license.CO_NAME ? '- deleted -' : license.CO_NAME;

        $('<div>').addClass('row')
            .append($('<div>').addClass('col-md-3').addClass('bold').text('Company ID'))
            .append($('<div>').addClass('col-md-3').text(coTitle))
            .append($('<div>').addClass('col-md-3').addClass('bold').text('Org'))
            .append($('<div>').addClass('col-md-3').text(license.SFDC_ORG_ID))
            .appendTo($record);

        $('<div>').addClass('row')
            .append($('<div>').addClass('col-md-3').addClass('bold').text('Company Name'))
            .append($('<div>').addClass('col-md-3').text(coName))
            .append($('<div>').addClass('col-md-3').addClass('bold').text('SFDC Edition'))
            .append($('<div>').addClass('col-md-3').text(OrgTypeDisp[license.ORGTYPE]))
            .appendTo($record);

        $('<div>').addClass('row')
            .append($('<div>').addClass('col-md-3').addClass('bold').text('Tenant Type'))
            .append($('<div>').addClass('col-md-3').css('text-transform', 'capitalize').text(license.CO_TYPE ? license.CO_TYPE : ''))
            .append($('<div>').addClass('col-md-3').addClass('bold').text('Subscription Created'))
            .append($('<div>').addClass('col-md-3').text(license.INITIAL_ORDER_DATE ? license.INITIAL_ORDER_DATE : ''))
            .appendTo($record);

        var licStatusDisplay = { 'U': 'New',
                                 'L': 'Active',
                                 'C': 'To Cancel',
                                 'X': 'Canceled' ,
                                 'P': 'Pending' };


        var usersTitle = 'Licenses - Platform: ' + license.NUM_USERS + ', Admin: ' + license.NUM_ADMINS;
        $('<div>').addClass('row')
            .append($('<div>').addClass('col-md-3').addClass('bold').text('Total Users'))
            .append($('<div>').addClass('col-md-3').attr('title', usersTitle).attr('id', 'num_users'))
            .append($('<div>').addClass('col-md-3').addClass('bold').text('Subscription Status'))
            .append($('<div>').addClass('col-md-3').text(licStatusDisplay[license.STATUS]))
            .appendTo($record);

        $.ajax('collaborateinfo.phtml?.getusercount=' + license.CNY)
            .done(function(data) {
                if ( data.numUsers != undefined ) {
                    $('#num_users').text(data.numUsers);
                } else {
                    $('#num_users').text('-');
                }
            })
            .fail(function() {
                $('#num_users').text('-');
            })

    }

    var $table = $('<table>').addClass('table').addClass('table-condensed').css('margin-top', '30px')
        .append($('<tr>')
            .append($('<th>').text('Order ID'))
            .append($('<th>').text('Type'))
            .append($('<th>').text('Date Submitted'))
            .append($('<th>').text('Status'))
            .append($('<th>').addClass('center').html('User<br>Licenses'))
            .append($('<th>').addClass('center').html('Admin<br>Licenses'))
            .append($('<th>').text(''))
        )
        .appendTo($record);

    $.each(orders, function(index, order) {

        var sfdcOrderId;
        var $button;

        if ( order.SFDC_ORDER_ID ) {
            sfdcOrderId = order.SFDC_ORDER_ID;
            $button = $('<button>').addClass('view_order_button').attr('order_sfdcid', order.SFDC_ORDER_ID).html('View in<br>Salesforce');
        } else {
            sfdcOrderId = '- not submitted -';
            $button = $('<span>').text('');
        }

        var title = 'license record#: ' + order.LICENSE_KEY;

        $table.append($('<tr>').attr('title', title)
            .append($('<th>').attr('scope', 'row').html(order.RECORDNO + '<br>' + sfdcOrderId))
            .append($('<td>').text(OrderTypeLookup[order.ORDER_TYPE]))
            .append($('<td>').text(order.ORDER_DATE))
            .append($('<td>').text(OrderStatusLookup[order.STATUS]))
            .append($('<td>').addClass('right').text(order.NUM_USERS.toLocaleString()))
            .append($('<td>').addClass('right').text(order.NUM_ADMINS.toLocaleString()))
            .append($('<td>').append($button))
        );

    });

    return $record;

}


function createInlineOrdersDisplay(licenses, orders, filters) {

    var $table = $('<table>').addClass('table').addClass('table-condensed')
        .append($('<tr>')
            .append($('<th>').text('Order ID'))
            .append($('<th>').text('Tenant Info'))
            .append($('<th>').text('Type'))
            .append($('<th>').text('Date Submitted'))
            .append($('<th>').text('Status'))
            .append($('<th>').addClass('center').html('User<br>Licenses'))
            .append($('<th>').addClass('center').html('Admin<br>Licenses'))
            .append($('<th>').text('Error Message'))
            .append($('<th>').text(''))
            .append($('<th>').text(''))
        );

    $.each(orders, function(index, order) {

        var license = licenses.find(lic => lic.RECORDNO === order.LICENSE_KEY && lic.CNY === order.CNY);

        var entityInfo = order.CNY;
        if ( license ) {
            if ( license.CO_TITLE ) {
                entityInfo += '<br>' + license.CO_TITLE;
            } else {
                entityInfo += '<br>- deleted -';
            }
            if ( license.CO_NAME ) {
                entityInfo += '<br>' + license.CO_NAME;
            }
        }

        var sfdcOrderId;
        var $button;
        var $delbutton = $('<span>').text('');

        if ( order.SFDC_ORDER_ID ) {
            sfdcOrderId = order.SFDC_ORDER_ID;
            $button = $('<button>').addClass('view_order_button').attr('order_sfdcid', order.SFDC_ORDER_ID).html('View in<br>Salesforce');
            if(filters.ORDERS_ERROR) {
                $delbutton = $('<button>').addClass('del_order_button').attr({del_order_sfdcid: order.SFDC_ORDER_ID, del_order_cny: order.CNY}).html('Delete From<br>Intacct');
            }
        } else {
            sfdcOrderId = '- not submitted-';
            $button = $('<span>').text('');
        }

        var title = 'license record#: ' + order.LICENSE_KEY;

        $table.append($('<tr>').attr('title', title)
                .append($('<th>').attr('scope', 'row').html(order.RECORDNO + '<br>' + sfdcOrderId))
                .append($('<td>').html(entityInfo))
                .append($('<td>').text(OrderTypeLookup[order.ORDER_TYPE]))
                .append($('<td>').text(order.ORDER_DATE))
                .append($('<td>').text(OrderStatusLookup[order.STATUS]))
                .append($('<td>').addClass('right').text(order.NUM_USERS.toLocaleString()))
                .append($('<td>').addClass('right').text(order.NUM_ADMINS.toLocaleString()))
                .append($('<td>').text(order.SFDC_ERROR_MSG ? order.SFDC_ERROR_MSG : ''))
                .append($('<td>').append($button))
                .append($('<td>').append($delbutton))
        );

    });

    return $table;

}

function init() {

    $('#loading').fadeOut(100, function() { $('#page').fadeIn(100); });
    showSummaryTable(licenses, orders, prodCount);

}

function showLicenses(options) {

    $('#search_text').val(options.SEARCH);

    var $results = $('#results');

    unbindAll();
    $results.fadeOut(100, function () {
        $results.html('<div id="loading" class="loading"><i class="fa fa-gear fa-spin"></i></div>');
        $results.fadeIn(100, function() {
            var $licenseTable = getLicenseTable(licenses, options);
            $('#results .loading').fadeOut(100, function () {
                $results.children().remove();
                $results.append($licenseTable);
                $results.fadeIn(100);
                $('.view_orders_button').click(viewOrdersButtonClicked);
            })
        })
    });

}

function showOrders(options) {

    $('#search_text').val('');

    var filteredOrders = filterOrders(orders, options);

    var $results = $('#results');

    unbindAll();
    $results.fadeOut(100, function () {
        $results.html('<div id="loading" class="loading"><i class="fa fa-gear fa-spin"></i></div>');
        $results.fadeIn(100, function() {
            var $content = createInlineOrdersDisplay(licenses, filteredOrders, options);
            $('#results .loading').fadeOut(100, function () {
                $results.children().remove();
                $results.append($content);
                $results.fadeIn(100);
                $('.view_order_button').click(viewOrderButtonClicked);
                $('.del_order_button').click(deletOrderButtonClicked);
            })
        })
    });

}


function showOrdersPopup(licenseKey, cny) {

    var $host = $('#window');

    var filteredOrders = filterOrders(orders, { 'LICENSE_KEY': licenseKey, 'CNY': cny });
    var license = licenses.find(lic => lic.RECORDNO === licenseKey && lic.CNY === cny);

    $host.fadeOut(100, function () {
        $host.html('<div id="loading" class="loading"><i class="fa fa-gear fa-spin"></i></div>');
        $host.fadeIn(100, function() {
            var $licenseTable = createPopupOrdersDisplay(license, filteredOrders);
            $host.children().remove();
            $host.append($licenseTable);
            $('#overlay,#window').fadeIn(100);
            $('.view_order_button').click(viewOrderButtonClicked);
        })
    });

}

function doSearch() {

    var searchText = $('#search_text').val();
    if ( ! searchText || searchText == '' ) {
        alert("Please enter something for which to search.");
        return;
    }

    showLicenses({ SEARCH: searchText });

}


function closeRecord() {
    $('#overlay,#window').fadeOut(100);
    $('.view_order_button').off('click');
}

function unbindAll() {
    $('.view_orders_button').off('click');
    $('.view_order_button').off('click');
    $('.del_order_button').off('click');
}

function viewOrdersButtonClicked(e) {

    var $this = $(this);
    var licenseKey = $this.attr('license_key');
    var cny = $this.attr('cny');
    if ( ! licenseKey ) {
        alert("I don't know on what you clicked.");
        return;
    }

    showOrdersPopup(licenseKey, cny);

}

function viewOrderButtonClicked(e) {

    var $this = $(this);
    var sfdcId = $this.attr('order_sfdcid');
    if ( ! sfdcId ) {
        alert("I don't know on what you clicked.");
        return;
    }

    window.open(coaLink + sfdcId, '_blank', '');
}


function encodeQuery() {

    function encodeProxyData(data) {

        // Ensure that the encoded string is UTF-8
        // http://ecmanaut.blogspot.co.uk/2006/07/encoding-decoding-utf8-in-javascript.html
        // http://monsur.hossa.in/2012/07/20/utf-8-in-javascript.html
        data = unescape(encodeURIComponent(data));

        var encArray = [];
        var len = data.length;
        for ( var i = 0; i < len; i++ ) {
            var code = data.charCodeAt(i);
            var hex = code.toString(16);
            if ( code < 16 ) {
                hex = '0' + hex;
            }
            encArray[i] = hex;
        }

        var ret = encArray.join('');
        return ret;

    }

    var queryElement = document.getElementsByName('sfdc_query')[0];

    queryElement.value = encodeProxyData(queryElement.value);



}

function clearToDo(companyCny)
{
    action = 'TODO';
    var companyCny = $('#todo_companyid_text').val();
    var pods = $('#pods_text').val();
    var $results = $('#todoresults');
    $results.fadeOut(100, function () {
        $results.html('<div id="loading" class="loading"><i class="fa fa-gear fa-spin"></i></div>');
        $results.fadeIn(100, function() {
            $.ajax({
                url:  'collaborateinfo.phtml?.action=' + action + '&.company_Cny=' + companyCny + '&.pods=' + pods,
                type: 'POST',
                headers: { 'X-XSRF_TOKEN': window.csrfQRequestToken },
                dataType: 'json',
                data: {_action: action, _company_Cny: companyCny, _goForRefresh: true},
                success: function(response){
                    showClearTODOResult(response);
                },
                error: function(response){
                    alert("Error occured, Couldn\'t commit");
                }
            });
        })
    });
}

function showClearTODOResult(response) {

    var $results = $('#todoresults');

    var $content = createInlineMessageDisplay(response);
    $('#todoresults .loading').fadeOut(100, function () {
        $results.children().remove();
        $results.append($content);
        $results.fadeIn(100);
    });
}

function createInlineMessageDisplay(response) {

    var $table = $('<table>').addClass('table').addClass('table-condensed')
        .append($('<tr>')
            .append($('<th>').text('TODO Order deleted'))
            .append($('<th>').text('License count updated'))
        );

    $table.append($('<tr>')
        .append($('<td>').text(response.todoCount))
        .append($('<td>').text(response.licensesCount))
    );

    return $table;

}

function deletOrderButtonClicked(e) {

    var $this = $(this);
    var sfdcId = $this.attr('del_order_sfdcid');
    if ( ! sfdcId ) {
        alert("I don't know on what you clicked.");
        return;
    }
    var companyCny = $this.attr('del_order_cny');

    var conf = confirm("Do you want to delete the order '" + sfdcId + "' ?");
    if (conf == true) {
        var $results = $('#results');
        //$results.fadeTo(100, function () {
        $results.fadeTo("slow", 0.3);
            var action = 'DELORDER';
            $.ajax({
                url: 'collaborateinfo.phtml?.action=' + action + '&.company_Cny=' + companyCny + '&.sfdc_Id=' + sfdcId,
                type: 'POST',
                headers: {'X-XSRF_TOKEN': window.csrfQRequestToken},
                dataType: 'json',
                data: {_action: action, _company_Cny: companyCny, _sfdc_Id: sfdcId, _goForRefresh: true},
                success: function (response) {
                    if (response.delCount != undefined && response.delCount == 1) {
                        alert("Order '" + sfdcId + "' deleted.");
                        //location.reload();
                        //showOrders({ORDERS_ERROR: ">0"})
                    } else {
                        alert("No record deleted.")
                    }
                    $results.fadeTo("slow", 1);
                },
                error: function (response) {
                    alert("Error occured, Couldn\'t commit.");
                    $results.fadeTo("slow", 1);
                }
            });
        //});

    }
}