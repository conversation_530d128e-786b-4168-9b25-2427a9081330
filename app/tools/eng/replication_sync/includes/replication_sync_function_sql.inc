<?php
    //=============================================================================
    //
    //	FILE:        replication_sync_functions_sql.inc
    //	AUTHOR:      <PERSON><PERSON> <<EMAIL>>
    //	DESCRIPTION: general purpose functions with sql
    //
    //	(C)2021, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    /**
     * Get info from global DB
     *
     * @param string $p_table       the table from which you want to extract the info
     * @param array  $p_columns     db columns name you want to see
     * @param array  $p_primary_key primary key from table
     * @param array  $p_run_on      PODs where the function should run
     *
     * @return array
     * @throws IAException if the query fails
     */
    function get_data($p_table, $p_columns, $p_primary_key, $p_run_on) {
        $pk = implode(" || '-' || ", $p_primary_key);
        $columns = implode (",", $p_columns);

        $sql = "SELECT " . $columns . ", " . $pk . " as primary_key FROM " . $p_table;
        $query = array($sql);
        $result = DBRunner::runOnPODGroups("QueryResult", array($query), $p_run_on, null, false, false);

        if ($result === false) {
            addLog("Unable to retrieve information. DBRunner call failed.", LogManager::ERROR);
            throw new IAException("Unable to retrieve information.");
        }

        foreach ($result as $group_id => $res) {
            if ($res === false) {
                addLog("Unable to retrieve information. DBRunner call failed from pod group id " . $group_id, LogManager::ERROR);
                throw new IAException("Unable to retrieve information from pod group id " . $group_id . ".");
            }
        }

        return $result;
    }

    /**
     *
     *
     * @param array $p_data
     * @param string $p_see
     *
     * @return array
     */
    function prepare_data($p_data, $p_see) {
        $result = array();

        foreach ($p_data as $pod_group_id => $pod_data) {
            $result["pods"][] = $pod_group_id;
            foreach ($pod_data as $data) {
                $result["data"][$data["PRIMARY_KEY"]][$pod_group_id] = $data;

                unset($result["data"][$data["PRIMARY_KEY"]][$pod_group_id]["PRIMARY_KEY"]);
            }

        }

        // compare data
        if ($p_see === "diff") {
            $count_pods = count($result["pods"]);
            foreach ($result["data"] as $primary_key => $info) {
                $i = 1;
                while ($i < $count_pods) {
                    if ($info[$result["pods"][0]] != $info[$result["pods"][$i]]) {
                        continue 2;
                    }
                    $i++;
                }
                unset($result["data"][$primary_key]);
            }
        }

        return $result;
    }

    /**
     * Get the last replication date
     *
     * @return array
     */
    function get_last_replication_date() {
        $sql = "SELECT id, TO_CHAR(whenreplicated, 'YYYY-MM-DD HH24:MI:SS') whenreplicated FROM podgroup WHERE table_type = :1";
        $query = array($sql, "functional");
        $result = DBRunner::runOnPODGroups("QueryResult", array($query), true, null, false, false);

        if ($result === false) {
            addLog("Unable to retrieve information about the last replication date. DBRunner call failed.", LogManager::ERROR);
            throw new IAException("Unable to retrieve information about the last replication date.");
        }

        foreach ($result as $group_id => $res) {
            if ($res === false) {
                addLog("Unable to retrieve information about the last replication date. DBRunner call failed from pod group id " . $group_id, LogManager::ERROR);
                throw new IAException("Unable to retrieve information about the last replication date from pod group id " . $group_id . ".");
            }
        }

        return $result;
    }
