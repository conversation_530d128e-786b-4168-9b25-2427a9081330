<?php
/**
 * Class CRWUtil
 *
 * Utility class containing supporting methods for Interativce Custom Report Writer Administration dashboard.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Sage Intacct Corporation
 *
 * This document contains trade secret data that belongs to Sage Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Sage Intacct Corporation.
 */

require_once 'CRWUtilStatus.cls';

class CRWUtil
{
    const FEATURE_NAME_CRW_INTERACTIVE = 'CRW_INTERACTIVE';
    const FEATURE_NAME_DV_INTERACTIVE = 'CRW_VA';
    const FEATURE_NAME_SL_INTERACTIVE = 'SL_SUBJECTAREA';
    const FEATURE_NAME_CRW_MASTER_CONSUMER_ROLE = 'CRW_MASTER_CONSUMER_ROLE';

    const DESIGNER_MODEL = 'Designer model';
    const RUN_ONLY_MODEL = 'Run only model';

    /* @var string[][]|false[] $allSubscribedCompanies */
    private static $allSubscribedCompanies = null;

    /* @var string[][]|false[] $allDVSubscribedCompanies */
    private static $allDVSubscribedCompanies = null;

    /**
     * @param string $companyTitle
     * @param string $module
     *
     * @return mixed
     */
    public static function enableStateToReady($companyTitle, $module)
    {
        $gErr = Globals::$g->gErr;

        if (isl_strtolower($module) == 'dv') {
            $moduleKey = Globals::$g->kDVid;
        } else {
            $moduleKey = Globals::$g->kCRWid;
        }

        $cny = self::getCompanyId($companyTitle);

        if (!isset($cny) || $cny === '') {
            $gErr->addError(
                '', __FILE__ . ":" . __LINE__,
                "Invalid Company.", "Company not found", "Enter valid Company Title or Cny#"
            );
            $ok = false;
        } elseif (isset($moduleKey)){
            try {
                $ok = self::doEnableStateToReady($cny, $moduleKey);
            } catch (Exception $exp) {
                $ok = false;
                $gErr->addError('', __FILE__ . ":" . __LINE__, "Error while changing ready: " . $exp->getMessage());
            }
        } else {
            LogToFile('Error module is not set');
            $ok = false;
        }
        if (!$ok || $gErr->hasErrors()) {
            // if there are items in our errors array, return those errors
            $data['success'] = 'false';
            $data['errors'] = $gErr->myToString(true);
        } else {
            if (isl_strtolower($module) == 'dv') {
                $data['success'] = 'trueDv';
            } else {
                $data['success'] = 'trueCrw';
            }

            $data['message'] = "Successfully enabled state to ready for ". $module;
        }
        return $data;
    }

    /**
     * @param string $cny
     * @param string $moduleKey
     *
     * @return bool
     */
    private static function doEnableStateToReady($cny, $moduleKey)
    {
        self::initEnvironment($cny);
        $ok = true;
        $stmt = [
            'update modulepref set value =:1 where modulekey=:2 and property=:3 and value=:4 and cny#=:5',
            'R',
            $moduleKey,
            'STATE',
            'P',
            $cny,
        ];
        $ok = $ok && ExecStmt($stmt);
        return $ok;
    }

    /**
     * Returns the list of companies subscribed to Interactive CRW and whether they have customizations or not
     *
     * @param string $moduleId
     *
     * @return array|null
     */
    public static function getAllSubscribedCompanies($moduleId)
    {
        $stmt = [
            "SELECT a.cny, a.title, a.name, a.type, a.contactemail, NVL(b.author, 'N') AS author_subscribed,
               DECODE(c.state, 'R', 'ready', 'P', 'pending', c.state) AS state, (select case when ( select (SELECT COUNT(*)
           FROM company c, module m, customfield cf, customcomponent cc
           WHERE c.status = 'T'
             AND m.cny# = c.record#
             AND m.moduleid = :1 
             AND cf.cny# = c.record#
             AND cf.cny# = cc.cny#
             AND cf.cny# = a.cny
             AND cf.record# = cc.customfieldkey) + (SELECT COUNT(*)
           FROM module m, company c 
             LEFT JOIN featureconfig f ON f.cny# = c.record# AND featureid = 'RELATIONSHIP_REDESIGN_READ_NEW_PLACE'
           WHERE m.moduleid = :1 
             AND c.record# = m.cny#
             AND m.cny# = a.cny
             AND c.status = 'T'
             AND c.record# IN (SELECT cny# FROM pt_obj_def WHERE properties LIKE '%isGLDimension=1%')) FROM DUAL ) > 0 then 'yes' else 'no' end from dual) AS Customizations
             FROM
              (SELECT c.record# AS cny, c.title, c.name, NVL(c.type, 'test') type, c.contactemail
               FROM company c
               WHERE status = 'T'
                 AND EXISTS (SELECT cny# FROM module WHERE cny# = c.record# AND moduleid = :1)) a
               LEFT OUTER JOIN
                (SELECT cny# AS cny, value AS author
                 FROM modulepref 
                 WHERE modulekey = :1 AND property = 'AUTHOR') b ON b.cny = a.cny
               LEFT OUTER JOIN
                (SELECT cny# AS cny, value AS state
                 FROM modulepref
                 WHERE modulekey = :1 AND property = 'STATE') c ON c.cny = a.cny
             ORDER BY a.title",
            $moduleId,
        ];

        if ($moduleId === Globals::$g->kDVid) {
            if (self::$allDVSubscribedCompanies === null) {
                $results = DBRunner::runOnAllDBsInCurrentPOD('QueryResult', [$stmt]);
                self::$allDVSubscribedCompanies = DBRunner::mergeRunOnSomeResults($results);
            }

            return self::$allDVSubscribedCompanies;
        } else {
            if (self::$allSubscribedCompanies === null) {
                $results = DBRunner::runOnAllDBsInCurrentPOD('QueryResult', [$stmt]);
                self::$allSubscribedCompanies = DBRunner::mergeRunOnSomeResults($results);
            }

            return self::$allSubscribedCompanies;
        }
    }

    /**
     * Get ICRW / IVE module pref for a company
     *
     * @param string $cny
     *
     * @return array|bool
     */
    public static function getCrwModulePrefs($cny)
    {
        $stmt = array("select CNY# as CNY, MODULEKEY, PROPERTY, VALUE, LOCATIONKEY, WHENMODIFIED from modulepref where cny# = :1 and modulekey in (:2, :3)",
            $cny, Globals::$g->kCRWid, Globals::$g->kDVid);
        return DBRunner::runOnCnyDB('QueryResult', [ $stmt ], $cny);
    }

    /**
     * Returns the company Id (cny#) by title.
     *
     * @param string $companyTitle
     *
     * @return string
     */
    private static function getCompanyId($companyTitle)
    {
        $stmt = [
            "SELECT cny# FROM schemamap WHERE title = :1",
            $companyTitle
        ];
        $resultSet = QueryResult($stmt, 0, '', GetGlobalConnection()); //Call with global connection
        $cnyId = $resultSet[0]['CNY#'];

        return $cnyId;
    }

    /**
     * Public API for disabling a company with the given title.
     *
     * @param string $companyTitle
     * @param string $module
     * @return mixed
     */
    public static function disableCompany($companyTitle, $module = 'crw')
    {
        $gErr = Globals::$g->gErr;
        if (isl_strtolower($module) == 'dv') {
            $moduleKey = Globals::$g->kDVid;
        } else {
            $moduleKey = Globals::$g->kCRWid;
        }

        $cny = self::getCompanyId($companyTitle);

        if (!isset($cny) || $cny === '') {
            $gErr->addError(
                '', __FILE__ . ":" . __LINE__,
                "Invalid Company.", "Company not found", "Enter valid Company Title or Cny#"
            );
            $ok = false;
        } else {
            try {
                $ok = self::doDisableCompany($cny, $moduleKey);

            } catch (Exception $exp) {
                $ok = false;
                $gErr->addError('', __FILE__ . ":" . __LINE__,
                    "Error while disabling Interactive CRW for tenant: " . $exp->getMessage());
            }
        }

        // if there are any errors in our errors array, return false
        if (!$ok || $gErr->hasErrors()) {
            // if there are items in our errors array, return those errors
            $data['success'] = 'false';
            $data['errors'] = $gErr->myToString(true);
        } else {
            // show a message of success and provide a true success variable
            $moduleLabel = isl_strtolower($module) == 'dv' ? 'IVE' : 'ICRW';
            $data['success'] = 'true';
            $data['message'] = "Successfully disabled subscription to $moduleLabel in this company: " . $companyTitle;
        }

        return $data;
    }

    /**
     * @param int $cny
     * @param string $moduleKey
     *
     * @return bool
     */
    private static function doDisableCompany($cny, $moduleKey)
    {
        $moduleLabel = 'ICRW';
        self::initEnvironment($cny);
        $featureName = self::FEATURE_NAME_CRW_INTERACTIVE;
        $removeFolders = true;
        if ($moduleKey == Globals::$g->kDVid) {
            $featureName = self::FEATURE_NAME_DV_INTERACTIVE;
            if (Reporting::isCRWIntEnabled()) {
                $removeFolders = false;
            }
            $moduleLabel = 'IVE';
        } else if (Reporting::isCRWVAEnabled()) {
            $removeFolders = false;
        }
        $featureConfigManager = FeatureConfigManagerFactory::getInstance();
        $isEnabled = $featureConfigManager->isFeatureEnabled($featureName);

        $ok = true;
        if ($isEnabled) {
            if ($featureConfigManager->isFeatureSetByCompany($featureName)) {
                $featureConfigManager->disableFeatureForCompany($featureName);
            }
        } else {
            // Error condition. The company IS NOT enabled and hence, CANNOT be disabled
            $ok = false;
            Globals::$g->gErr->addError(
                '', __FILE__ . ":" . __LINE__,
                "The company is not enabled for $moduleLabel");
        }

        if ($ok) {
            $moduleIdList = GetModuleIdList(Globals::$g->_userid);
            $isInstalled = in_array($moduleKey, $moduleIdList);

            if ($isInstalled) {
                $ok = $ok && CRWSetupManager::forceFullBackup($cny);
                if ($ok &&  $removeFolders) {
                    CRWSetupManager::removeCompanyFolders($cny);
                }
                $ok = $ok && !DeinstallModule($moduleKey);
                InitModules();
            } else {
                $ok = false;
                Globals::$g->gErr->addError(
                    '', __FILE__ . ":" . __LINE__,
                    "The company is not enabled for $moduleLabel");
            }
        }

        if ($moduleKey === Globals::$g->kDVid) {
            $stmt = [
                'delete from modulepref where property = :1 and cny# = :2 and modulekey = :3',
                'NROFBUILDERS',
                $cny,
                $moduleKey
            ];
            $ok = $ok && ExecStmt($stmt);
        }
        $ok = $ok && self::resetPermissionCache($cny);

        return $ok;
    }

    /**
     * Public API for enabling a company with the given title.
     *
     * @param string $companyTitle
     * @param bool   $designerMode
     * @param string $module
     *
     * @return mixed
     */
    public static function enableCompany($companyTitle, $designerMode, $module = 'crw')
    {
        $gErr = Globals::$g->gErr;
        if (isl_strtolower($module) == 'dv') {
            $moduleKey = Globals::$g->kDVid;
        } else {
            $moduleKey = Globals::$g->kCRWid;
        }

        $cny = self::getCompanyId($companyTitle);

        if (!isset($cny) || $cny === '') {
            $gErr->addError(
                '', __FILE__ . ":" . __LINE__,
                "Invalid Company.", "Company not found", "Enter valid Company Title or Cny#"
            );
            $ok = false;
        } else {
            try {
                $ok = self::doEnableCompany($cny, $designerMode, $moduleKey);
            } catch (Exception $exp) {
                $ok = false;
                $gErr->addError('', __FILE__ . ":" . __LINE__, "Error while enabling tenant: " . $exp->getMessage());
            }
        }

        // if there are any errors in our errors array, return false
        if (!$ok || $gErr->hasErrors()) {
            // if there are items in our errors array, return those errors
            $data['success'] = 'false';
            $data['errors'] = $gErr->myToString(true);
        } else {
            // show a message of success and provide a true success variable
            $data['success'] = 'true';
            $modelText = $designerMode ? 'Designer' : 'Run-only';
            $data['message'] = 'Successfully enabled Interactive ' . isl_strtoupper($module). ' ' . $modelText . ' model in this company: '
                . $companyTitle;
            // Update BI_TRANSLATIONS
            ObieeTranslations::getInstance()->updateForCny($cny, true);
        }

        return $data;
    }

    /**
     * Reset the permissions cache for all users of the company
     *
     * @param int $cny
     *
     * @return bool success or failure
     */
    private static function resetPermissionCache($cny)
    {
        $ok = (new ProfileHelper())->invalidateSessionCache(
            ProfileHandler::USER_CACHE_FLAG + ProfileHandler::PERM_CACHE_FLAG, $cny
        );

        $qry = "update userinfo set perm_cache_valid = 'F' where cny# = :1 ";
        $stmt_desc = [$qry, $cny];

        $ok = $ok && ExecStmt($stmt_desc);

        return $ok;
    }

    /**
     * @param int    $cny
     * @param bool   $designerMode
     * @param string $moduleKey
     *
     * @return bool
     */
    private static function doEnableCompany($cny, $designerMode, $moduleKey)
    {
        self::initEnvironment($cny);
        if ($moduleKey === Globals::$g->kDVid) {
            $feature = self::FEATURE_NAME_DV_INTERACTIVE;
        } else {
            $feature = self::FEATURE_NAME_CRW_INTERACTIVE;
        }

        InitModules();

        $moduleIdList = GetModuleIdList(Globals::$g->_userid);
        $isInstalled = in_array($moduleKey, $moduleIdList);

        $featureConfigManager = FeatureConfigManagerFactory::getInstance();
        $isEnabled = $featureConfigManager->isFeatureEnabled($feature);
        if (!$isEnabled) {
            $featureConfigManager->enableFeatureForCompany($feature);
        }

        $source = __FILE__ . ':' . __METHOD__;
        XACT_BEGIN($source);
        $ok = true;
        if (!$isInstalled) {
            $ok = InstallModule(['MODULEID' => $moduleKey]);
            InitModules();
        }
        if ($ok) {
            if ($moduleKey === Globals::$g->kDVid) {
                $mgr = new DVSetupManager();
            } else {
                $mgr = new CRWSetupManager();
            }

            $authorValue = $designerMode ? self::DESIGNER_MODEL : self::RUN_ONLY_MODEL;
            $values = $mgr->get('');
            if (!isset($values['STATE'])) {
                // there are no module prefs
                $values = array("AUTHOR" => $authorValue);
                if (isset(Request::$r->_nrOfBuilders) and $authorValue === self::RUN_ONLY_MODEL and $moduleKey === Globals::$g->kDVid) {
                    $values['NROFBUILDERS'] = Request::$r->_nrOfBuilders;
                }
                $ok = $mgr->add($values);
            } else if ($values['AUTHOR'] != $authorValue) {
                $values['AUTHOR'] = $authorValue;
                $ok = $mgr->set($values);
                $value = $mgr->TransformValue('AUTHOR', $authorValue, 0);
                $stmt = [
                    'update modulepref set value = :1 where property = :2 and cny# = :3 and modulekey = :4',
                    $value,
                    'AUTHOR',
                    $cny,
                    $moduleKey
                ];
                $ok = $ok && ExecStmt($stmt);
            }
            if ($moduleKey === Globals::$g->kDVid and isset(Request::$r->_nrOfBuilders)) {
                $stmt = [
                    'update modulepref set value = :1 where property = :2 and cny# = :3 and modulekey = :4',
                    Request::$r->_nrOfBuilders,
                    'NROFBUILDERS',
                    $cny,
                    $moduleKey
                ];
                $ok = $ok && ExecStmt($stmt);
            }

            if($authorValue == self::RUN_ONLY_MODEL) {
                // in case the company is already subscribed to 'ICRW Designer' with full ICRW permissions granted and switches to 'ICRW Viewer Only' all the permissions
                // except list & view should be cleared; otherwise the users may be left with add, delete and modify rights that won't appear in the permission tab but are
                // actually there and these users can perform illegal opeartions for a 'ICRW Viewer Only' subscription type
                self::initPoliciesForICRWViewerOnly(Request::$r->_company_title);
            }

            $ok = $ok && self::resetPermissionCache($cny);
        }

        $ok = $ok && XACT_COMMIT($source);
        if (!$ok) {
            XACT_ABORT($source);
        }

        return $ok;
    }

    /**
     * Basic method calls for initializing environment for a specific company.
     *
     * @param int $cnyId
     */
    private static function initEnvironment($cnyId)
    {
        SetDBSchema($cnyId, 'cny#');

        Backend_Init::SetEnvironment($cnyId, 1);
        Backend_Init::Init();
        SetCompanyContext($cnyId);
    }

    /**
     * This method should be invoked through DBRunner
     *
     * @return array the roles information, array of ID, NAME and GRP
     */
    public static function fetchOBIEERolesInfoFromOneSchema()
    {
        return QueryResult("select 'r' || cny# id, 'r' || cny# name, 'c' || cny# grp"
            . " from module where moduleid = '59.CRW' and cny# in (select record# from company where status = 'T')"
            . " union all"
            . " select id, name, id grp from (select 'k' || cny# || '_' || record# id, name from ugroup"
            . " where cny# in (select cny# C from module where moduleid = '59.CRW' and"
            . " cny# in (select record# from company where status = 'T')))");
    }

    /**
     * Create the roles specific to tenants in OBIEE. These roles are:
     * - main tenant role
     * - user groups roles
     * - UDD roles
     *
     * @param int|null      $instanceId the instance (and the group it's in) for which the roles are created
     * @param CRWUtilStatus $status
     *
     * @param int           $bsize      number of roles sent per http call to obiee app
     *
     * @return bool success or failure
     */
    public static function createTenantRoles($instanceId = null, $status = null, $bsize = 5000)
    {
        if ($status !== null) {
            $status->updateJobStatus(null, 'Creating tenant roles on OBIEE');
        }

        return RPDJobs::createAllTenantRoles($instanceId, $bsize);
    }

    /**
     * Perform an OBIEE server initialization
     *
     * @param string $serverId          the OBIEE instance id
     * @param string $jobId             the job id
     * @param bool   $openForNewTenants true if the server should be set to accept new tenants after successful
     *                                  initialization
     * @param bool   $skipRPD           true to skip deploying the RPD
     */
    public static function initServer($serverId, $jobId, $openForNewTenants, $skipRPD)
    {
        $instanceId = OBIEEInstance::getInstance($serverId)->getInstanceId();

        $status = new CRWUtilStatus(__CLASS__ . '_' . $jobId, 'Starting server init');
        $status->updateJobStatus('Creating object roles');

        $ok = RPDJobs::createObjectRolesAndGroups($instanceId);

        // check status of step 1, update status and contine to step 2 if OK
        $status->updateJobStatus(
            $ok ? 'Create tenant roles' : 'Job failed',
            $ok ? 'Object roles created' : 'Object roles creation failed',
            $ok,
            $ok ? CRWUtilStatus::STATE_RUN : CRWUtilStatus::STATE_DONE
        );

        if ( ! $ok ) {
            return;
        }

        // step 2 is to generate the tenant roles
        $ok = self::createTenantRoles($instanceId, $status);

        // check status of step 2, update status and contine to step 3 if OK
        $status->updateJobStatus(
            $ok ? 'Deploy RPD' : 'Job failed',
            $ok ? 'Tenant roles created' : 'Tenant roles creation failed',
            $ok,
            $ok ? CRWUtilStatus::STATE_RUN : CRWUtilStatus::STATE_DONE
        );

        if ( ! $ok ) {
            return;
        }

        // until this point we dealt with all instances and the code might have setup the cny# context to the OBI admin
        // company. Reset all that so we can operate on the desired instance
        Globals::$g->_userid = null;

        $log = [];
        // step 3 is to deploy the RPD
        if ( ! $skipRPD ) {
            $rpdJobs = new RPDJobs(OBIEEInstance::getInstance($instanceId)->getGroup());
            $ok = $rpdJobs->redeployRPD($log, $instanceId);
        }

        $status->updateJobStatus(
            $ok ? 'Deploy Intacct Shared Library' : 'Deploy RPD failed',
            $log,
            $ok,
            $ok ? CRWUtilStatus::STATE_RUN : CRWUtilStatus::STATE_DONE
        );

        if ( ! $ok ) {
            return;
        }

        // step 4 is to deploy the Intacct Shared Library
        $reporting = new Reporting(OBIEEInstance::getInstance($instanceId)->getAdminUser());
        $archivePath = $reporting->getIntacctSharedLibraryFromDb();
        if (file_exists($archivePath)) {
            $ok = $reporting->importLibraryFolder($archivePath, false, $log);
        } else {
            $ok = false;
        }

        // check status of step 3, update status and contine to next step if OK
        $status->updateJobStatus(
            $ok ? 'Open up server' : 'Job failed',
            $ok ? 'Intacct Shared Library ready' : 'Intacct Shared Library deploy failed',
            $ok,
            $ok ? CRWUtilStatus::STATE_RUN : CRWUtilStatus::STATE_DONE
        );

        if ( ! $ok ) {
            return;
        }

        $logMsg = null;

        if ( $openForNewTenants ) {
            $logMsg = "Set accept new tenant flag on server";
            $serverRecordNo = OBIEEInstance::getInstance($instanceId)->getRecordNo();
            $mgr = Globals::$g->gManagerFactory->getManager('subsystem');
            $instanceRecord = $mgr->get($serverRecordNo);
            if (is_array($instanceRecord)) {
                $instanceRecord['ACCEPT_NEW'] = 'yes';
                $ok = $mgr->set($instanceRecord);
            } else {
                $ok = false;
            }
        }
        $status->updateJobStatus(
            $ok ? 'Done' : 'Updating the server configuration failed',
            $ok ? $logMsg : 'Updating the server configuration failed',
            $ok,
            CRWUtilStatus::STATE_DONE
        );
    }

    /**
     * Fetch the job status
     *
     * @param string $jobId    the job id
     *
     * @return CRWUtilStatus the job status details
     */
    public static function fetchStatus($jobId)
    {
        return CRWUtilStatus::initFromKey(__CLASS__ . '_' . $jobId);
    }

    /**
     * Check if a company is subscribed to CRW
     *
     * @param string $cny
     *
     * @return bool
     */
    public static function isCnySubcribed($cny) {
        global $kCRWid, $kDVid;
        $stmt = [
            "select * from modulepref where cny# = :1 and modulekey in (:2, :3) and property = 'STATE' and value = 'R'",
            $cny, $kCRWid, $kDVid,
        ];
        return count(QueryResult($stmt)) > 0;
    }

    /**
     * Check if a company is on pending on IVE
     *
     * @param string $cny
     *
     * @return bool
     */
    public static function isPending($cny, $moduleKey):bool
    {
        $stmt = array("select cny# from modulepref where cny# = :1 and modulekey=:2 and property = :3 and value = :4",
                      $cny, $moduleKey, 'STATE','P');
        $res = DBRunner::runOnCnyDB('QueryResult', [ $stmt ], $cny);
        return !empty($res);
    }

    /**
     * @param string $cnyTitle title of the company to update policies
     *
     * @return bool returns true on success, false otherwise
     */
    public static function initPoliciesForICRWViewerOnly($cnyTitle) {

        $cnyQuery = "select record# from company where title = :1";
        $cnyRes = QueryResult(array($cnyQuery, $cnyTitle));
        $cny = $cnyRes[0]['RECORD#'];

        $gManagerFactory = Globals::$g->gManagerFactory;
        $entityMgr = $gManagerFactory->getManager("roles");
        $policyKey = $entityMgr->_QM->DoQuery('QRY_IAPOLICY_GETBYNAME', array("cerp", "Interactive Custom Reports"));
        $policyKey = $policyKey[0]['RECORD#'];

        $viewerOnlyDefaultPolicies = "list|readonly";
        if (IsRolesEnabled($cny)) {
            $qry = "update rolepolicyassignment set policyval = :1 where cny# = :2 and module = :3 and policykey = :4";
            $ok = ExecStmt([ $qry, $viewerOnlyDefaultPolicies, $cny, "cerp", $policyKey ]);
        } else {
            $qry = "update policyassignment set policyval = :1 where cny# = :2 and policykey = :3";
            $ok = ExecStmt([ $qry, $viewerOnlyDefaultPolicies, $cny, $policyKey ]);
        }

        return $ok;
    }

    /**
     * Filters policyval to allow only list and readonly and remove everything else
     *
     * @param string $policyval
     */
    public static function filterICRWViewerOnlyPermissions(&$policyval){

        $newPolicyVal = "";
        if ( strpos($policyval, 'list') !== false ) {
            $newPolicyVal = 'list';
        }
        if ( strpos($policyval, 'readonly') !== false ) {
            if ( ! empty($newPolicyVal) ) {
                $newPolicyVal .= '|';
            }
            $newPolicyVal .= 'readonly';
        }
        $policyval = $newPolicyVal;
    }

    /**
     * @param string|int $instance
     * @param string|null $tenant
     * @param string|null $exportPath
     * @param bool $includeReports
     * @param bool $includeOwner
     * @return mixed
     */
    static function readACLPermissionsForTenants($instance, $tenant = null, $exportPath = null, $includeReports = false, $includeOwner = false)
    {
        $permissions = [];

        $reporting = new Reporting(null, $instance);
        $tenants = [];
        if ($exportPath != null) {
            $path = Reporting::CRW_PATH . $exportPath;
            $files = array_values(array_diff(scandir($path), array('..', '.')));

            foreach ($files as $fileName) {
                $details = explode('__', pathinfo($fileName, PATHINFO_FILENAME));
                $companyName = end($details);
                $tenants[] = $companyName;
            }
        } elseif ($tenant != null) {
            $tenants = !is_array($tenant) ? [$tenant] : $tenant;
        } else {
            $instanceTenants = $reporting->getTenantsForInstance($instance);
            foreach ($instanceTenants as $company) {
                $tenants[] = $company['N'];
            }
        }

        $reportType = [];
        foreach ($tenants as $companyName) {
            $report['PATH'] = "/shared/$companyName";
            $itemInfo = $reporting->fetchReport($report);
            $tenantPerm = $itemInfo->return->acl->accessControlTokens;
            $tenantFolderPerms = [];
            foreach ($tenantPerm as $key => $permItem) {
                $tenantFolderPerms[$permItem->account->name] = ["mask" => $permItem->permissionMask, "accountType" => $permItem->account->accountType];
            }
            $permissions[$companyName]['TENANT_FOLDER'] = $tenantFolderPerms;

            if ($includeReports) {
                $permissions[$companyName]['TENANT_REPORTS'] = [];

                $reports = $reporting->fetchFolderItems("/shared/$companyName");
                $reportsInfo = Reporting::objectToArray($reports->itemInfo);
                if (!isset($reportsInfo[0])) {
                    $reportsInfo = [$reportsInfo];
                }
                foreach ($reportsInfo as $report) {
                    $reportType[$report['path']] = $report['type'];
                    $rptDetail = $reporting->fetchReport(["PATH" => $report['path']]);
                    $accessControlTokens = $rptDetail->return->acl->accessControlTokens;
                    $owner = $rptDetail->return->owner;

                    $permissionMap = [];
                    foreach ($accessControlTokens as $key => $permItem) {
                        $permissionMap[$permItem->account->name] = ["mask" => $permItem->permissionMask, "accountType" => $permItem->account->accountType];
                    }

                    if ($includeOwner) {
                        $permissionMap['owner'] = $owner;
                    }

                    $permissions[$companyName]['TENANT_REPORTS'][$report['path']] = $permissionMap;
                }
            }
        }

        return [$permissions, $reportType];
    }

    /**
     * @param int|string $sourceInstance
     * @param int|string $destinationInstance
     * @param array      $sourceInstancePermissions
     * @param string     $tenantFolderPath
     * @param array      $reportType
     */
    static function changeFolderPermissions($sourceInstance, $destinationInstance, $sourceInstancePermissions, $tenantFolderPath, $reportType)
    {
        $destinationReporting = new Reporting(null, $destinationInstance);
        $obiAdminSource = OBIEEInstance::getInstance($sourceInstance)->getAdminUser();
        // apply tenant permissions
        $tenantFolderAccessList = [];
        foreach ($sourceInstancePermissions['TENANT_FOLDER'] as $name => $maskAndAccountType) {
            $accountType = ($maskAndAccountType['accountType'] == 2) ? 4 : $maskAndAccountType['accountType'];
            $tenantFolderAccessList[] = ['name' => $name, 'type' => $accountType, 'permissions' => $maskAndAccountType['mask']];
        }
        $destinationReporting->replacePermissions($tenantFolderPath, $tenantFolderAccessList);

        // apply report permissions
        foreach ($sourceInstancePermissions['TENANT_REPORTS'] as $reportPath => $reportRolePerm) {
            // set obiadmin as owner so he is able to change permissions
            $destinationReporting->setOwnership($reportPath, ['name' => $obiAdminSource, 'accountType' => 0, 'guid' => $obiAdminSource]);
            $reportAccessList = [];
            $reportSubFoldersAccessList = [];
            foreach ($reportRolePerm as $name => $maskAndAccountType) {
                if ($name == "owner") {
                    continue;
                }
                $accountType = ($maskAndAccountType['accountType'] == 2) ? 4 : $maskAndAccountType['accountType'];
                if (substr($name, 0, 1) != 'c') {
                    $reportSubFoldersAccessList[] = ['name' => $name, 'type' => $accountType, 'permissions' => $maskAndAccountType['mask']];
                }
                $reportAccessList[] = ['name' => $name, 'type' => $accountType, 'permissions' => $maskAndAccountType['mask']];
            }

            //$destinationInstance->replacePermissions($reportPath, $reportAccessList, true, 1);
            // user roles are not supposed to be part of subfolders permissions
            if ($reportType[$reportPath] == "Folder") {
                $destinationReporting->replacePermissions($reportPath, $reportSubFoldersAccessList, true, 1);
            }
            $destinationReporting->replacePermissions($reportPath, $reportAccessList, false, 1);

            // set the owner from source instance
            $owner = $sourceInstancePermissions['TENANT_REPORTS'][$reportPath]['owner'];
            if (isset($owner)) {
                $destinationReporting->setOwnership($reportPath, ['name' => $owner->name, 'accountType' => $owner->accountType, 'guid' => $owner->name]);
            }
        }
    }
}
