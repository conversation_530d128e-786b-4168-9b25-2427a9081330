<?php
    /**
     * Subject Area Admin Tool
     *  entities usage page
     *
     * <AUTHOR> <<EMAIL>>
     * @copyright 2020 Intacct Corporation, All Rights Reserved
     */

    //
    // init
    //

    $cache = $cache ?? null;
    $oMemCache = $oMemCache ?? null;

    // workaround to register_globals without extract()
    if (isset(Request::$r->btn_validate_fieldinfo)) {
        $r_btn_validate_fieldinfo = Request::$r->btn_validate_fieldinfo;
    }
    if (isset(Request::$r->btn_validate_description)) {
        $r_btn_validate_description = Request::$r->btn_validate_description;
    }
    if (isset(Request::$r->entity)) {
        $r_entity = Request::$r->entity;
    }

    $errors = array();

    if (!isset($r_file_key)) {
        header("Location: obi_sa_manager.phtml");

        exit;
    }

    $oFile = new RPDSAFile($r_file_key);
    $sas = unserialize($oFile->data);

    $validation = false;

    // get a list of entities used in SAs
    $entities = array();
    if (isset($cache["entities_".SA_SID])) {
        $entities = $cache["entities_".SA_SID];
    } else {
        // set the environment to retreive all entites correctly (including intacct dimensions)
        IADimensions::setForceAllStdDimensions(true);
        // we need a cny context in order to deal with dimensions properly
        $cny = arrayExtractValue(GetValueForIACFGProperty("REPORTING"), "RPD_CNY");
        if ($cny !== null) {
            Backend_Init::SetEnvironment($cny);
            SetDBSchema($cny, "");
        }
        if ($cny === null or GetMyCompanyTitle() === null) {
            die("The given company, ".$cny.", was not found in ia_init.cfg: REPORTING\\RPD_CNY.");
        }

        foreach ($sas as $sa) {
            foreach ($sa["objects"] as $object) {
                $mgr = Globals::$g->gManagerFactory->getManager($object["object"]);
                $entities[$mgr->getEntity()] = $mgr->getEntity();
            }
        }
        ksort($entities);
        $cache["entities_".SA_SID] = $entities;
        $oMemCache->set("sa_tool_cache", $cache);
    }

    //
    // ops
    //

    // check entities for valid fieldinfo for published fields
    // (all fields in "object"/"publish" should have an entry in "fieldinfo" with "path" and "fullname" properties)
    if (isset($r_btn_validate_fieldinfo)) {
        if (isset($r_entity)) {
            $entity_list = array($r_entity.".ent" => $r_entity.".ent");
        } else {
            $entity_list = $entities;
        }
        $b_errors = false;
        foreach ($entity_list as $entFile) {
            $entName = strtolower(str_replace(".ent", "", $entFile));
            $entErrors[$entName] = array();

            $schema = EntityManager::justIncludeEntity($entName);
            if ($schema === false) {
                $entErrors[$entName][] = array("type" => "no_entity");
                $b_errors = true;

                continue;
//                throw new Exception("Cannot include entity ".$entFile);
            }

            // check that "object" exists
            if (!isset($schema["object"])) {
                $entErrors[$entName][] = array("type" => "no_object");
                $b_errors = true;

                continue;
            }

            // check that "fieldinfo" exists
            if (!isset($schema["fieldinfo"])) {
                $entErrors[$entName][] = array("type" => "no_fieldinfo");
                $b_errors = true;

                continue;
            }

            // 1. check if all published fields from "schema" are in "fieldinfo"
            // 2. check if all published fields in "fieldinfo" have a "fullname" property
            $publish = array_flip($schema["publish"] ?? $schema["object"]);
            $exclude = array_flip($schema["exclude"] ?? array());
            $fields = array();
//            foreach ($schema["object"] as $field) {
            foreach ($publish as $field => $key) {
//                if (isset($publish[$field]) and !isset($exclude[$field])) {
                if (!isset($exclude[$field])) {
                    $fields[$field] = $field;
                }
            }

            // all fields are invalid, unless they are valid :)
            $invalidPaths = $invalidNames = $invalidTypes = $fields;
            foreach ($schema["fieldinfo"] as $fieldinfo) {
                if (!isset($fieldinfo["path"])) {

                    continue;
                }
                if (isset($invalidPaths[$fieldinfo["path"]])) {
                    unset($invalidPaths[$fieldinfo["path"]]);
                }
                if (isset($fieldinfo["fullname"]) and $fieldinfo["fullname"] !== "" and isset($invalidNames[$fieldinfo["path"]])) {
                    unset($invalidNames[$fieldinfo["path"]]);
                }
                if (isset($fieldinfo["type"]) and isset($fieldinfo["type"]["type"]) and isset($fieldinfo["type"]["ptype"]) and isset($invalidTypes[$fieldinfo["path"]])) {
                    unset($invalidTypes[$fieldinfo["path"]]);
                }
            }

            // remove from invalid names/types if it doesn't have fieldinfo (path) at all, to avoid duplicate errors
            foreach ($invalidPaths as $field) {
                if (isset($invalidNames[$field])) {
                    unset($invalidNames[$field]);
                }
                if (isset($invalidTypes[$field])) {
                    unset($invalidTypes[$field]);
                }
            }

            if (count($invalidPaths) > 0) {
                $entErrors[$entName][] = array("type" => "missing_fieldinfo", "data" => $invalidPaths);
                $b_errors = true;
            }
            if (count($invalidNames) > 0) {
                $entErrors[$entName][] = array("type" => "missing_fullname", "data" => $invalidNames);
                $b_errors = true;
            }
            if (count($invalidTypes) > 0) {
                $entErrors[$entName][] = array("type" => "invalid_type", "data" => $invalidTypes);
                $b_errors = true;
            }

            // add module to find entity easier
            if (count($entErrors[$entName]) > 0) {
                $entErrors[$entName]["module"] = $schema["module"];
            }
        }

        // display the result
        // TODO move to a template
        echo "<pre>";
        if (!$b_errors) {
            echo "no errors";

            exit;
        }

        foreach ($entErrors as $entity => $errors) {
            if (count($errors) > 0) {
                echo $entity."\t".$errors["module"];
                unset($errors["module"]);
                $indent = "\t";
                foreach ($errors as $error) {
                    echo $indent.$error["type"];
                    if (isset($error["data"])) {
                        $first = true;
                        foreach ($error["data"] as $field => $data) {
                            echo ($first ? "\t" : "\t\t\t").$field."\n";
                            $first = false;
                        }
                    }
                    $indent = "\t\t";
                }
                echo "\n";
            }
        }

        exit;
    }

    if (isset($r_btn_validate_description)) {
        // initialize validator
        EntityValidator::init();
        EntityValidator::buildRuleMap(EntityValidator::DICT_FILE_NAME);
        EntityValidator::setRequiredRule(".description", true);

        if (isset($r_entity)) {
            // validate one entity
            $arguments = array("validateent", "entfile=".$r_entity, "skiplist='schema'");
        } else {
            // validate all entities
            $arguments = array("validateentlist", "entlist=".implode(",", $entities), "skiplist='schema'");
        }

        // run validation
        ob_start();
        EntityValidator::run($arguments);
        $validation = ob_get_clean();
    }

    //
    // data
    //

    //
    // template
    //

//    $t["errors"] = $errors;
//    $t["req"] = html_prepare($req);
    $t["main"] = "obi_ents_usage.tpl.inc";
    $t["file_name"] = html_prepare($oFile->name);
    $t["file_key"] = $r_file_key;
    $t["entities"] = $entities;
    $t["validation"] = $validation;

    render("obi_frame.tpl.inc", $t);
