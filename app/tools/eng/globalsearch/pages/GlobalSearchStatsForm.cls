<?php
/**
 * Global Search Stats Base From - add extra inputs to this form using the decorator GlobalSearchStatsInputForm.cls
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Intacct Corporation All, Rights Reserved
 */

class GlobalSearchStatsForm extends GlobalSearchRenderView
{
    /** @var string $statsToDisplay The page(stats) to be rendered*/
    private $statsToDisplay;

    // To avoid having to create a new class for each Operation we create a label map:
    const SUBMIT_LABEL_MAP = [
        ES_BUILDS => 'Get ES Builds',
        ES_INFO => 'Get ES Info',
        INDEX_STATS => 'Get index stats',
        INDEX_MAPPING => 'Get index mapping',
        CLUSTER_STATS => 'Get cluster stats',
        ES_QUERY_RUNNER => 'Run ES Query',
    ];

    /**
     * GlobalSearchStatsForm constructor.
     *
     * @param string $statsToDisplay
     */
    public function __construct(string $statsToDisplay)
    {
        $this->statsToDisplay = $statsToDisplay;
    }

    /**
     * Returns the page to be displayed
     *
     * @return string
     */
    public function getStatsToDisplay(): string
    {
        return $this->statsToDisplay;
    }

    /**
     * Display form
     */
    public function getForm()
    {
        $this->render(
            'globalSearchStatsFormView.inc',
            [
                'form' => $this,
                'label' => $this->getSubmitLabel(),
            ]
        );
    }

    /**
     * Add inputs to the form
     */
    public function addInput()
    {
        $this->render(
            'globalSearchStatsInputView.inc',
            [
                'statsToDisplay' => $this->statsToDisplay,
            ]
        );
    }

    /**
     * @return string
     */
    protected function getSubmitLabel() : string
    {
        return self::SUBMIT_LABEL_MAP[$this->statsToDisplay] ?? 'Submit';
    }
}