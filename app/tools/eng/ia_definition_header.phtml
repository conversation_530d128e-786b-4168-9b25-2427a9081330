<?
//=============================================================================
//
//	FILE:			ia_definition_header.phtml
//	AUTHOR:			Senthil
//	DESCRIPTION:
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

$title = $title ?? '';
$i = $i ?? 0;
$appurl = "HTTPS://" . $_SERVER['HTTP_HOST'] . RootPath() . "/acct/";
if (Globals::$g->islive) {
    $linkmeta = [
        [
            'title'    => 'Industry',
            'golink'    => "ia_definition.phtml?page=industry_list",
        ],
        [
            'title' => 'ICRW',
            'golink' => 'ia_definition_csindustrycodecrw.phtml?reportType=crw',
        ],
        [
            'title' => 'IVE',
            'golink' => 'ia_definition_csindustrycodecrw.phtml?reportType=dv',
        ],
    ];
} else {
    $linkmeta = [
        [
            'title'    => 'Industry',
            'golink'    => "ia_definition.phtml?page=industry_list",
        ],
        [
            'title'    => 'Category',
            'golink'    => $appurl."lister.phtml?.op=" . GetOperationId('gl/lists/iacoacategory'),
        ],
        [
            'title'    => 'Account Group',
            'golink'    => $appurl."lister.phtml?.op=" . GetOperationId('gl/lists/iaglacctgrp'),
        ],
        [
            'title'    => 'Financial Report',
            'golink'    => $appurl."lister.phtml?.op=" . GetOperationId('gl/reports/iafinancialtool'),
        ],
        [
            'title'    => 'Graph',
            'golink'    => $appurl."lister.phtml?.op=" . GetOperationId('gl/reports/iagraphstool'),
        ],
        [
            'title'    => 'Customization Packages',
            'golink'    => $appurl."lister.phtml?.op=" . GetOperationId('co/setup/iapackagetool'),
        ],
        [
            'title' => 'Dashboard',
            'golink' => $appurl . "lister.phtml?.op=" . GetOperationId('co/lists/iadashboardproperties'),
        ],
        [
            'title' => 'Dimension Structure',
            'golink' => 'ia_definition_dimstructure.phtml',
        ],
        [
            'title' => 'Dimension Group',
            'golink' => 'ia_definition_dimgroup.phtml',
        ],
        [
            'title' => 'Synchronize',
            'golink' => "ia_sync.phtml",
        ],
        [
            'title' => 'Custom Reports',
            'golink' => $appurl . "lister.phtml?.op=" . GetOperationId('co/setup/iacustomreporttool'),
        ],
        [
            'title' => 'ICRW',
            'golink' => 'ia_definition_csindustrycodecrw.phtml?reportType=crw',
        ],
        [
            'title' => 'IVE',
            'golink' => 'ia_definition_csindustrycodecrw.phtml?reportType=dv',
        ],
    ];
}
?>
<html>
<head>
<title>Intacct</title>
</head>

<script>
	function RefreshCurFrame(app, curId){
		//To change the image
		imgObjs = document.getElementsByTagName('img');
		for (var i=0; i<imgObjs.length ;i++ )
		{
			if (imgObjs[i].id.indexOf('img')>=0)
			{
				if (imgObjs[i].id == 'img'+curId) {
					imgObjs[i].src = "../resources/images/ia-app/buttons/financialarrowdown.gif";
				} else {
					imgObjs[i].src = "../resources/images/ia-app/buttons/financialarrowright.gif";
				}
			}
		}
		
		window.parent.frames[1].location.replace(app);
	 }
	function RefreshTop(app){
		window.parent.location.replace(app);
	 }

</script>
<body bgcolor="#FFFFFF" text="#000000" link="#006699" alink="#990000" vlink="#000000" topmargin="0" leftmargin="0" marginwidth="0" marginheight="0">
<FORM>

<table border="0" cellpadding="3" cellspacing="0" width="100%" bgcolor="#FFFFFF">
	<tr>
		<td valign="middle" align="left"><img src="../resources/images/ia-app/logos/intacct_logo.gif" alt="" width="168" height="41" border="0"></td>
		<td colspan="2" valign="middle" align="center" rowspan="2" width="100%"><font face="arial" size=4 color="#003366"><b>Manage Definition </b></font>
		</td>
		<td colspan="2" valign="bottom" align="right" rowspan="2" width="100%"  nowrap><font face="arial" size="0" color="#003366"><b><?  echo (date('F j, Y H:i:s')); ?> </b></font>
		</td>
	</tr>
	<tr>
	</tr>
</table>
<!--   menu begins here -->
<table border="0" cellpadding="0" cellspacing="0" width="100%" bgcolor="#DEDEDE">
	<tr><td bgcolor="#000000" colspan="2"><img src="../resources/images/ia-app/backgrounds/trans.gif" width="1" height="1"></td></tr>
	<tr><td bgcolor="#DEDEDE" colspan="2"><img src="../resources/images/ia-app/backgrounds/trans.gif" width="1" height="2"></td></tr>
	<tr align="left" colspan="2">
		<td valign="center" style="white-space:nowrap">&nbsp;&nbsp;
    <? 
    foreach ($linkmeta as $link) {
        extract($link);
        ?>
     <a href="#" onmouseover="window.status=''; return true;"  onclick="javascript:RefreshCurFrame('<? /** @noinspection PhpUndefinedVariableInspection */
     echo ($golink); ?>', <?=++$i?>);">
				<img src="../resources/images/ia-app/buttons/financialarrowright.gif" id='<?="img".$i?>' alt="" border="0">
				<font face=arial size=2 color="#003366"><b><? echo ($title); ?></b></font></a>
				&nbsp;&nbsp;
				<? 
    } 
    ?>
		</td>
		<td valign="center" align="right" nowrap>
			<a href="#" onclick="javascript:RefreshTop('ia_definition_manager.phtml');"><font face=arial size="2" color="#003366"><b> Home</b></font></a>&nbsp;&nbsp;
            <a href="#" onclick="javascript:RefreshCurFrame('<? echo util_encode($appurl.'frameset.phtml'); ?>')" ><font face=arial size="2" color="#003366"><b>Application</b></font></a>&nbsp;&nbsp;
			<a href="#" onclick="javascript:RefreshCurFrame('index.phtml')" ><font face=arial size="2" color="#003366"><b>CS Tool</b></font></a>&nbsp;&nbsp;
			<a href="#" onclick="RefreshTop('index.phtml?.logout=1')"><font face=arial size="2" color="#003366"><b>logout</b></font></a>
		</td>		
	</tr>
	<tr><td bgcolor="#DEDEDE" colspan="2"><img src="../resources/images/ia-app/backgrounds/trans.gif" width="1" height="2"></td></tr>
	<tr><td bgcolor="#000000" colspan="2"><img src="../resources/images/ia-app/backgrounds/trans.gif" width="1" height="1"></td></tr>
</table>
<!--   menu ends here -->
</FORM>
</body>
</html>
