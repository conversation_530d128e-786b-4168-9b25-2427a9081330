<?php
    //=============================================================================
    //
    //	FILE:        copy_cny_copy.tpl.inc
    //	AUTHOR:      <PERSON> <<EMAIL>>
    //	DESCRIPTION: Copy company tool template
    //
    //	(C)2023, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    // just to keep inspections happy
    $t_header_buttons = $t_header_buttons ?? array();
    $t_alerts = $t_alerts ?? array();
    $t_company_types = $t_company_types ?? array();
    $t_admins = $t_admins ?? array();
    $t_instances = $t_instances ?? array();
    $t_users = $t_users ?? array();
    $t_company = $t_company ?? array();
    $t_pod_labels = $t_pod_labels ?? array();
    $t_schema_labels = $t_schema_labels ?? array();
    // end inspections
?>

<div class="alerts">
    <? if (isset($t_alerts[ALERT_TYPE_CONFIRM]) and count($t_alerts[ALERT_TYPE_CONFIRM]) > 0) { ?>
    <div id="confirm_group" class="alert alert-confirm">
        <? foreach ($t_alerts[ALERT_TYPE_CONFIRM] as $msg) { ?>
        <div class="alert-message">
            <?= $msg; ?>
        </div>
        <? } ?>
    </div>
    <? } ?>

    <? if (isset($t_alerts[ALERT_TYPE_WARNING]) and count($t_alerts[ALERT_TYPE_WARNING]) > 0) { ?>
    <div id="warning_group" class="alert alert-warn">
        <? foreach ($t_alerts[ALERT_TYPE_WARNING] as $msg) { ?>
        <span class="alert-message">
            <?= $msg; ?>
        </span>
        <? } ?>
    </div>
    <? } ?>

    <? if (isset($t_alerts[ALERT_TYPE_ERROR]) and count($t_alerts[ALERT_TYPE_ERROR]) > 0) { ?>
    <div id="error_group" class="alert alert-error">
        <? foreach ($t_alerts[ALERT_TYPE_ERROR] as $msg) { ?>
        <div class="alert-message">
            <?= $msg; ?>
        </div>
        <? } ?>
    </div>
    <? } ?>
</div>

<div class="page-row">
    <div class="page-col2">
        <form id="frm_copycny" name="copycny" method="POST" class="box" onsubmit="showLoader();">
            <input name=".oldcnytitle" type="hidden" value="<?= $t_company["cnytitle"]; ?>">
            <input name=".validateTicket" type="hidden" value="<?= $t_company["validate_ticket"]; ?>">
            <input name=".srcschemaid" type="hidden" value="<?= $t_company["srcschemaid"]; ?>">
            <input name=".srccnytype" type="hidden" value="<?= $t_company["srccnytype"]; ?>">
            <input name=".fetchschema" id="fetchschema" type="hidden" value="">

            <div class="form-row">
                <label for="cnytitle">Company to be copied</label>
                <input name=".cnytitle" id="cnytitle" type="text" value="<?= $t_company["cnytitle"]; ?>" size="40" maxlength="40" autofocus onchange="autoLoadSourceSchema('cnytitle', 'frm_copycny', 'fetchschema', 'loadSrcSchema');">
            </div>
            <div class="form-row">
                <label for="newcnytitle">New company title</label>
                <input name=".newcnytitle" id="newcnytitle" type="text" value="<?= $t_company["newcnytitle"]; ?>" size="40" maxlength="40">
            </div>
            <div class="form-row">
                <label>Source company type</label>
                <div class="form-row-data-text"><?= $t_company["src_company_type"] ?: "-"; ?></div>
            </div>
            <div class="form-row">
                <label>Destination company type</label>
                <div id="select_type" class="select-group">
                    <button id="type_button" type="button" class="select-btn" onclick="showDropdown(event, 'type_selector');">
                        <span id="type_selector_label" class="select-label"><?= ($t_company["type"] === "") ? "" : $t_company_types[$t_company["type"]]; ?></span>
                        <span class="select-arrow-box"><span class="select-arrow"></span></span>
                    </button>
                    <div id="type_selector" class="select-dropdown">
                        <? foreach ($t_company_types as $type_key => $type) { ?>
                        <input id="type_<?= $type_key; ?>" name=".type" type="radio" class="select-option"
                               value="<?= $type_key; ?>" <?= $type_key == $t_company["type"] ? "checked" : ""; ?>
                               onclick="setSelector('type_selector_label', '<?= $type; ?>');" onchange="autoLoadSourceSchema('cnytitle', 'frm_copycny', 'fetchschema', 'loadSrcSchema');">
                        <label for="type_<?= $type_key; ?>" class="select-item" tabindex="-1"><?= $type; ?></label>
                        <? } ?>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <label>Source schema</label>
                <div class="form-row-data-text"><?= $t_company["src_schema_name"] ?: "-"; ?></div>
            </div>

            <div class="form-row">
                <label for="pod_title">Destination pod</label>
                <div class="select-group" id="pod_select">
                    <div class="select-box">
                        <input id="pod_title" type="text" autocomplete="off" placeholder="Select destination pod..." class="select-input select-input-dropdown" value="<?= ($t_company["dest_pod_id"] === "") ? "" : $t_pod_labels[$t_company["dest_pod_id"]];?>"
                               onkeyup="filterSelect(event, 'pod_title', 'pod_label', 'dest_pod_id', 'pod_selector', 'pod_select', 'pod_title');"
                               onfocus="this.select();">
                        <span class="select-arrow-box" onclick="showAllValuesSelect(event, 'pod_label'); showDropdown(event, 'pod_selector', 'pod_select', 'pod_title'); autoScroll('pod_selector', 'dest_pod_id');">
                            <span class="select-arrow"></span>
                        </span>
                    </div>
                    <div id="pod_selector" class="select-dropdown">
                    <? foreach ($t_pod_labels as $pod_id => $pod_label) { ?>
                        <input id="pod_<?=$pod_id;?>" name="dest_pod_id" type="radio" class="select-option" value="<?=$pod_id;?>"
                            <?=$pod_id == $t_company["dest_pod_id"] ? " checked" : ""; ?> onclick="setSelectorInput('pod_title', '<?=$pod_label;?>'); change_schemas('<?=$pod_id?>')" onchange="autoLoadSourceSchema('cnytitle', 'frm_copycny', 'fetchschema', 'loadSrcSchema');">
                        <label for="pod_<?=$pod_id;?>" id="pod_<?=$pod_id;?>_label" name="pod_label"  class="select-item" tabindex="-1"><?=$pod_label;?></label>
                    <? } ?>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <label for="schema_title">Destination schema</label>
                <div class="select-group" id="schema_select">
                    <div class="select-box">
                        <input id="schema_title" type="text" autocomplete="off" placeholder="Select destination schema..." value="<?= ($t_company["dest_schema_id"] === "") ? "" : $t_schema_labels[$t_company["dest_pod_id"]][$t_company["dest_schema_id"]]["label"];?>"
                               class="select-input select-input-dropdown"
                               onkeyup="filterSelect(event, 'schema_title', 'schema_label', 'dstschemaid', 'schema_selector', 'schema_select', 'schema_title');"
                               onfocus="this.select();">
                        <span class="select-arrow-box"
                              onclick="showAllValuesSelect(event, 'schema_label');
                              showDropdown(event, 'schema_selector', 'schema_select', 'schema_title');
                              autoScroll('schema_selector', 'dstschemaid');">
                            <span class="select-arrow"></span>
                        </span>
                    </div>
                    <div id="schema_selector" class="select-dropdown">
                        <? foreach ($t_schema_labels[$t_company["dest_pod_id"]] as $schema_key => $schema) { ?>
                        <input id="schema_<?=$schema_key;?>" name=".dstschemaid" type="radio" class="select-option" value="<?=$schema_key;?>" <?=$schema_key == $t_company["dest_schema_id"] ? " checked" : "";?> onclick="setSelectorInput('schema_title', '<?=$schema["label"];?>');">
                        <label for="schema_<?=$schema_key;?>" name="schema_label" class="select-item" tabindex="-1"><?=$schema["label"];?></label>
                        <? } ?>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <label>Administrative user</label>
                <div class="select-group" id="select_admin">
                    <button id="admin_button" type="button" class="select-btn" onclick="showDropdown(event, 'admin_selector');">
                        <span id="admin_selector_label" class="select-label"><?= ($t_company["adminuserkey"] === "") ? "" : $t_admins[$t_company["adminuserkey"]]; ?></span>
                        <span class="select-arrow-box"><span class="select-arrow"></span></span>
                    </button>
                    <div id="admin_selector" class="select-dropdown">
                        <? foreach ($t_admins as $admin_key => $admin) { ?>
                        <input id="admin_<?= $admin_key; ?>" name=".adminuserkey" type="radio" class="select-option"
                               value="<?= $admin_key; ?>" <?= $admin_key == $t_company["adminuserkey"] ? "checked" : ""; ?>
                               onclick="setSelector('admin_selector_label', '<?= $admin; ?>');" onchange="contactData(this.value, cs.data.company_info);">
                        <label for="admin_<?= $admin_key; ?>" class="select-item" tabindex="-1"><?= $admin; ?></label>
                        <? } ?>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <label for="contactemail">Email</label>
                <input type="text" name=".contactemail" id="contactemail" value="<?= $t_company["contactemail"]; ?>" size="40" maxlength="100">
                <i class="form-row-data-span">(override admin contact email)</i>
            </div>
            <div class="form-row">
                <label for="contactname">Name</label>
                <input type="text" name=".contactname" id="contactname" value="<?= $t_company["contactname"]; ?>" size="40" maxlength="100">
            </div>
            <div class="form-row">
                <label for="contactphone">Phone</label>
                <input type="text" name=".contactphone" id="contactphone" value="<?= $t_company["contactphone"]; ?>" size="40" maxlength="40">
            </div>
            <div class="form-row">
                <label for="ticketid">Ticket ID</label>
                <input type="text" name=".ticketid" id="ticketid" value="<?= $t_company["ticketid"]; ?>" size="40" maxlength="40">
            </div>
            <div class="form-row">
                <label for="comments">Comments</label>
                <textarea name=".comments" id="comments" rows="5" cols="34" maxlength="400"><?= $t_company["comments"]; ?></textarea>
            </div>
            <div class="form-row">
                <label for="timestamp">Timestamp</label>
                <input type="text" name=".timestamp" id="timestamp" value="<?= $t_company["timestamp"]; ?>" size=19 maxlength="19">
                <i class="form-row-data-span">e.g. 2017-05-31_13:10:00</i>
            </div>
            <div class="form-row">
                <label>Options:</label>
            </div>
            <div class="form-row form-row-props">
                <label class="checkbox clickable">
                    <input type="checkbox" name=".ignoreschemadiff" <?= ($t_company["ignore_schema_diff"]) ? "checked" : ""; ?>>
                    <span class="fal"></span>
                    <div class="form-row-text">Ignore schema difference</div>
                </label>
            </div>
            <div class="form-row form-row-props">
                <label class="checkbox clickable">
                    <input type="checkbox" name=".ignoretype" <?= ($t_company["ignore_type"]) ? "checked" : ""; ?>>
                    <span class="fal"></span>
                    <div class="form-row-text">Ignore database type restrictions</div>
                </label>
            </div>
            <div class="form-row form-row-props">
                <label class="checkbox clickable">
                    <input type="checkbox" name=".mongoNoCopy" <?= ($t_company["mongo_no_copy"]) ? "checked" : ""; ?>>
                    <span class="fal"></span>
                    <div class="form-row-text">Do not copy Mongo DB config</div>
                </label>
            </div>
            <div class="form-row form-row-props">
                <label class="checkbox clickable">
                    <input type="checkbox" name=".replace" <?= ($t_company["replace"]) ? "checked" : ""; ?>>
                    <span class="fal"></span>
                    <div class="form-row-text">Copy to Existing Company, Overwrite Data</div>
                </label>
            </div>
            <div class="form-row form-row-props">
                <label class="checkbox clickable">
                    <input type="checkbox" name=".maskSensitiveData" <?= ($t_company["mask_sensitive_data"]) ? "checked" : ""; ?>>
                    <span class="fal"></span>
                    <div class="form-row-text">Mask Sensitive Data</div>
                </label>
            </div>
            <div class="form-row form-row-props">
                <label class="checkbox clickable">
                    <input type="checkbox" name="full_copy" <?= $t_company["full_copy"] ? "checked" : ""; ?>>
                    <span class="fal"></span>
                    <div class="form-row-text">Full copy (copy attachment content & audit data)</div>
                </label>
            </div>

            <!-- Ability to bypass Global Search related failures on copy company. -->
            <div class="form-row form-row-props">
                <label class="checkbox clickable">
                    <input type="checkbox" name="disableGlobalSearch" <?= $t_company["skip_global_search_init"] ? "checked" : ""; ?>>
                    <span class="fal"></span>
                    <div class="form-row-text">Skip Global Search initialization</div>
                </label>
            </div>

            <!--Check this if you need to copy the ICRW and IVE reports to the new company. If so, OBIEE's data have to be copied also.-->
            <div class="form-row form-row-props">
                <label class="checkbox clickable">
                    <input type="checkbox" name=".restoreInteractiveReports" id="interactive_reports" <?= $t_company["restore_interactive_reports"] ? "checked" : ""; ?> onchange="displayBIID();">
                    <span class="fal"></span>
                    <div class="form-row-text">Copy interactive reports</div>
                </label>
            </div>
           
            <div class="form-row" id="instance_id" style="display: <?= $t_company["restore_interactive_reports"] ? "block" : "none"; ?>;">
                <label>BI instance id</label>
                <div class="select-group" id="select_instance">
                    <button id="instance_button" type="button" class="select-btn" onclick="showDropdown(event, 'instance_selector');">
                        <span id="instance_selector_label" class="select-label"><?= ($t_company["bi_instance_id"] === "") ? "Please select BI instance id" : $t_company["bi_instance_id"]; ?></span>
                        <span class="select-arrow-box"><span class="select-arrow"></span></span>
                    </button>
                    <div id="instance_selector" class="select-dropdown">
                        <input id="instance_no_filter" name=".BIInstanceId" type="radio" class="select-option"
                               value="" <?= $t_company["bi_instance_id"] === "" ? "checked" : ""; ?>
                               onclick="setSelector('instance_selector_label', 'Please select BI instance id');">
                        <label for="instance_no_filter" title="No filter" class="select-item" tabindex="-1">Please select BI instance id</label>
                        <? foreach ($t_instances as $instance) { ?>
                        <input id="instance_<?= $instance; ?>" name=".BIInstanceId" type="radio" class="select-option"
                               value="<?= $instance; ?>" <?= $instance == $t_company["bi_instance_id"] ? "checked" : ""; ?>
                               onclick="setSelector('instance_selector_label', '<?= $instance; ?>');">
                        <label for="instance_<?= $instance; ?>" class="select-item" tabindex="-1"><?= $instance; ?></label>
                        <? } ?>
                    </div>
                </div>
            </div>

            <!-- Save and Cancel buttons -->
            <div class="form-buttons">
                <? foreach ($t_header_buttons as $button) { ?>
                <? if ($button["type"] === "submit") { ?>
                <input name="<?= $button["name"]; ?>" type="submit" form="<?= $button["form"]; ?>" value="<?= $button["label"]; ?>"/>
                <? } else if ($button["type"] === "link") { ?>
                <a href="<?= $button["url"]; ?>" class="button-link" onclick="showLoader();"><?= $button["label"]; ?></a>
                <? } ?>
                <? } ?>
            </div>
        </form>
    </div>
</div>

<template id="schema_template">
    <input id="BLOCK_KEY" name=".dstschemaid" type="radio" value="KEY" class="select-option" onclick="setSelectorInput('schema_title', 'VALUE');"/>
    <label for="BLOCK_KEY" name="schema_label" title="VALUE" class="select-item" tabindex="-1">VALUE</label>
</template>

<script>
    cs.data = cs.data || {};
    cs.data.user_list = <?= json_encode($t_users); ?>;
    cs.data.company_info = {
        set: true,
        contact_email: "",
        contact_name: "",
        contact_phone: "",
    };
    cs.data.schema_labels = <?=json_encode($t_schema_labels);?>;
    window.addEventListener("load", function () {
        const adminuserkey = document.querySelector('input[name=".adminuserkey"]:checked');

        if (adminuserkey != null) {
            contactData(adminuserkey.value, cs.data.company_info);
        }
    });
</script>
