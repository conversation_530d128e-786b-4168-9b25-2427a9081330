<?php
//=============================================================================
//
//      FILE:                   audit_view_storage.phtml
//      AUTHOR:                 <PERSON>
//      DESCRIPTION:
//      (C)2000, Intacct Corporation, All Rights Reserved
//
//      Intacct Corporation Proprietary Information.
//      This document contains trade secret data that belongs to Intacct
//      corporation and is protected by the copyright laws. Information herein
//      may not be used, copied or disclosed in whole or part without prior
//      written consent from Intacct Corporation.
//=============================================================================
//

require_once 'show_list_noperm.inc';
require_once 'common_cs.js';

$_srcschema = &Request::$r->_srcschema;

$_gotopage = 'audit_tool.phtml';
Head('#FFFFFF', '#000000', '#006699', '#990000', '#000000');
if (isset(Request::$r->_cny) && Request::$r->_cny != '' && Globals::$g->gPODManager->arePODGroupsEnabled()) {
    $podInfo = Globals::$g->gPODManager->getPODInfoForCny(
        Request::$r->_cny,
        (Request::$r->_cnySearchType == 'Title')?'title':'cny#'
    );
    if ($podInfo != false) {
        $destPODId = $podInfo[PODInfo::POD_ID];
        if ($destPODId != Globals::$g->gPODManager->getCurrentPOD()->getId()) {
            $url = "audit_view.phtml?" .
                ".cnySearchType=" . urlencode(Request::$r->_cnySearchType) .
                "&.cny=" . util_urlEncode(Request::$r->_cny) .
                "&.showInp=" . Request::$r->_showInp;
            Globals::$g->gPODManager->redirectCSTools($url, $destPODId);
        }
    }
}

if (Request::$r->_aj == 'getrows') {
    getRows(Request::$r->cny, Request::$r->chosenMode, Request::$r->objectType, Request::$r->objectKey,
     Request::$r->accessMode, Request::$r->startDate, Request::$r->endDate, Request::$r->whichUser,
     Request::$r->maxRows, Request::$r->showDups == "1" ? true : false);
    exit(0);
} else if (Request::$r->_aj == 'fields') {
    getFields(Request::$r->cny, Request::$r->record, Request::$r->accesstime);
    exit(0);
} else if (Request::$r->_aj == 'aat') {
    getAAT(Request::$r->cny, Request::$r->record, Request::$r->accesstime);
    exit(0);
}

?>

<html>
<style>
#Title {
    font-size:      28px;
}
.setup_left {
    width:           600px;
    float:           left;
    text-align:      left;
    line-height:     30px;
}
.setup_right {
    margin-left:     400px;
    text-align:      left;
}
.setup_left span, .setup_right span, .setup_right select, .setup_right input
{
    margin:          5px;
    font-size:       16px;
}
.data_area {
    clear:           both;
}
.datatable {
    border:          solid 1px black;
    border-collapse: collapse;
}
.datatable td, .datatable th {
    border:          solid 1px black;
    padding:         3px;
    text-align:      center;
    vertical-align:  middle;
}
.fieldData {
    border:          solid 1px black;
    border-collapse: collapse;
}
.fieldData td, .fieldData th {
    border:          solid 1px black;
    padding:         3px;
    text-align:      center;
    vertical-align:  middle;
}
#error_div {
    margin:          5px;
    font-size:       12px;
    color:           red;
}
.waiting {
    display:    none;
    position:   fixed;
    z-index:    1000;
    top:        0;
    left:       0;
    height:     100%;
    width:      100%;
    background: rgba( 128, 128, 128, .8 )
                url('../resources/images/ia-app/misc/querytool_spinner.gif')
                50% 50%
                no-repeat;
}
body.loading .waiting {
    overflow: hidden;
}
body.loading .waiting {
    display: block;
}

</style>

<title>Audit Table Viewer</title>

<body>
<script src="../resources/thirdparty/jquery/jquery-3.5.1.min.js"></script>
<script src="../resources/thirdparty/jquery/jquery-migrate-3.3.0.custom.min.js"></script>
<script src="../resources/thirdparty/jquery-ui/jquery-ui.min.js" language="javascript"></script>
<form name="t" method="POST" action="audit_view_storage.phtml">
<?php PrintCommonComponents("", $_gotopage); ?>
<input type="hidden" id="objecttypes" name="objecttypes" value="<?=util_encode(getObjectTypes());?>"/>
<input type="hidden" id="modeoptions" name="modeoptions" value="<?=util_encode(getAccessModes());?>"/>

    <table>
        <tr>
            <td>*
                <select name=".cnySearchType">
                    <option <? if (Request::$r->_cnySearchType == 'Title') {
                        echo 'selected="selected"';
                    } ?> >Title
                    </option>
                    <option <? if (Request::$r->_cnySearchType == 'CNY#') {
                        echo 'selected="selected"';
                    } ?> >CNY#
                    </option>
                </select>
            </td>
            <td><input type="text" name=".cny" id=".cny" size="15" value="<?php echo util_encode(Request::$r->_cny); ?>">
<?php
if (Request::$r->_cnySearchType == 'Title') {
    $cnyId = SetDBSchema(Request::$r->_cny);
} else {
    $cnyId = SetDBSchema(Request::$r->_cny, 'cny#');
}
?>
            </td>
            <td><input type="submit" name=".showInp" value="SUBMIT"></td>
        </tr>
    </table>
<input type="hidden" id="_cnyid" name="_cnyid" value="<?=$cnyId;?>"/>
<?php
if (Request::$r->_showInp && !(Request::$r->_showRes || Request::$r->_showTog)) {
    // Check all required values
    if (empty(Request::$r->_cny)) {
        showInputError("CNY#/Title is a required field (enter a number or title).");
        return false;
    }
    if (getCurrentDBSchemaName() == '') {
        showInputError("Enter a Valid ".Request::$r->_cnySearchType);
    } else {
?>

<div id="error_div"></div>
<p>This tool is for viewing the audit trail as of the Audit Storage project</p>

<?
if (!empty($_REQUEST['_cnyid'])) {
    $cny = $_REQUEST['_cnyid'];
} else {
    $cny = is_numeric($_REQUEST['_cny']) ? $_REQUEST['_cny'] : GetCNYbyTitle($_REQUEST['_cny']);
}
Globals::$g->_userid ='@' . $cny;
if ( FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('AUDIT_STORAGE_WRITE_TO_SNOWFLAKE_ENABLED') ) {
?>
 <input type="radio" id="_audit_type_both" name="_audit_type" value="both"
  <?php echo (Request::$r->_chosenMode == "both" || Request::$r->_chosenMode == '')? "checked" : ""; ?>/>
 <label for="audit_local">Retrieve both local and Snowflake audit data</label>
 <input type="radio" id="_audit_type_local" name="_audit_type" value="local"
  <?php echo (Request::$r->_chosenMode == "local") ? "checked" : ""; ?>/>
 <label for="audit_local">Retrieve local audit data</label>
 <input type="radio" id="_audit_type_snowflake" name="_audit_type" value="snowflake"
  <?php echo Request::$r->_chosenMode == "snowflake" ? "checked" : ""; ?>/>
 <label for="audit_snowflake">Retrieve Snowflake audit data</label>
 <input type="checkbox" id="showDups" name="showDups">
 <label for="showDups">Show duplicate entries</label>
<?
} else {
?>
 <label for="audit_snowlake">Retrieves data from local database and, if enabled, Snowflake</label>
<?
}
?>

<div id="audit_results"></div>
<div class="data_area">
 <div id="field_dialog"><table id="field_table"></table></div>
</div>

<input type="hidden" id="_chosenObjType" name="_chosenObjType" value="<? echo Request::$r->_chosenObjType;?>"/>
<input type="hidden" id="_chosenCompany" name="_chosenCompany" value="<? echo Request::$r->_chosenCompany;?>"/>
<input type="hidden" id="_chosenMode" name="_chosenMode" value="<? echo Request::$r->_chosenMode;?>"/>
<input type="hidden" id="showDev" name="showDev" value="1"/>

<script type="text/javascript">
    var jq=jQuery.noConflict();
    jq(document).on({
        ajaxStart: function() { showWaiting();    },
        ajaxStop: function() { hideWaiting(); }
    });

    jq(document).ready(function() {
        renderMain();
    });

    function showWaiting()
    {
        jq('body').addClass('loading');
    }

    function hideWaiting()
    {
        jq('body').removeClass('loading');
    }

    jq( "#curobjtype" ).change(function() {
        var objType = jq("#curobjtype").val();
        jq("#_chosenObjType").val(objType);
    });

    function renderMain() {
        renderHeader();
    }

    function renderHeader() {
        var table = jq('<table id="audit_table"/>').appendTo('#audit_results');
        jq('#audit_table').html("<th>Object Type</th><th>Object Key</th><th>Access Mode</th>" +
         "<th>Access Time (&gt;)</th><th>Access Time (&lt;)</th><th>User</th><th>Max Rows</th><th></th>");

        tRow = jq('<tr>').appendTo(table);
        tObject = jq('<td>').appendTo(tRow);
        var objectOptions = jq('<select id="objectTypes"/>').appendTo(tObject);
        var objectTypes = JSON.parse(jq('#objecttypes').val());
        for (var atype in objectTypes) {
            jq('<option value="' + objectTypes[atype] + '">' + objectTypes[atype] +
             '</option>').appendTo(objectOptions);
        }

        tObject = jq('<td>').appendTo(tRow);
        var objectOptions = jq('<input type="text" id="objectKey"/>').appendTo(tObject);

        tObject = jq('<td>').appendTo(tRow);
        var modeOptions = jq('<select id="accessModes"/>').appendTo(tObject);
        jq('<option value="ALL">All Modes</option>').appendTo(modeOptions);
        var modes = JSON.parse(jq('#modeoptions').val());
        for (var amode in modes) {
            jq('<option value="' + amode + '">' + modes[amode] + '</option>').appendTo(modeOptions);
        }

        tObject = jq('<td>').appendTo(tRow);
        jq('<input type="text" id="startDate"/>').appendTo(tObject);

        tObject = jq('<td>').appendTo(tRow);
        jq('<input type="text" id="endDate"/>').appendTo(tObject);

        tObject = jq('<td>').appendTo(tRow);
        jq('<input type="text" id="whichUser"/>').appendTo(tObject);

        tObject = jq('<td>').appendTo(tRow);
        jq('<input type="text" id="maxRows"/>').appendTo(tObject);

        tObject = jq('<td>').appendTo(tRow);
        jq('<input type="button" name="_getrows" id="_getrows" value="Search Audit Table"/>').appendTo(tObject);

        var resultsTable = jq('<table id="audit_results_table"/>').appendTo('#audit_results');
        resultsTable.addClass('datatable');

        jq( "#_getrows" ).click(function() {
            var cny = jq("#_cnyid").val();
            if (jq("#_cnyid").val() == '') {
                alert("You must first specify a valid company title or id");
                return false;
            }
            var chosenMode = jq('input[name="_audit_type"]:checked').val();
            if (typeof chosenMode == 'undefined') {
                chosenMode = "local";
            }
            var objectType = jq('#objectTypes').children("option:selected").val();
            var objectKey = jq('#objectKey').val();
            var accessMode = jq('#accessModes').children("option:selected").val();
            var startDate = jq('#startDate').val();
            var endDate = jq('#endDate').val();
            var whichUser = jq('#whichUser').val();
            var maxRows = jq('#maxRows').val();
            var showDev = jq('#showDev').val();
            var showDups = jq('#showDups').is(":checked") ? 1 : 0;
            jq.ajax(document.location.href, {
                data: { _aj: "getrows", cny, chosenMode, objectType, objectKey, accessMode, startDate, endDate,
                 whichUser, maxRows, showDups },
                dataType: "json",
                success: function(data) {
                    jq('#error_div').empty();
                    var resultsTable = jq('#audit_results_table');
                    resultsTable.empty();
                    var headers = "<th>Object Type</th><th>Object Key</th><th>Time</th><th>Mode</th>" +
                     "<th>User</th><th>Source</th><th>Action</th><th>IP</th><th>Recordid</th>" +
                     "<th>Description</th>";
                    if (showDev) {
                        headers = headers + "<th>From Snowflake?</th>";
                    }
                    resultsTable.html(headers);
                    var msgText = 'Number audit records matched:' + (data.length-1);
                    jq.each(data, function(i) {
                        if (i == 0) {
                            if (data[0]['ERROR'].length > 0) {
                                msgText = msgText + "<br>" + "Snowflake Error: " + data[0]['ERROR'];
                            }
                        } else {
                            tRow = jq('<tr>');
                            tCell = jq('<td>').html(data[i]['OBJECTTYPE']);
                            resultsTable.append(tRow.append(tCell));
                            tCell = jq('<td>').html(data[i]['OBJECTKEY']);
                            resultsTable.append(tRow.append(tCell));
                            tCell = jq('<td>').html(data[i]['ACCESSTIME']);
                            resultsTable.append(tRow.append(tCell));
                            tCell = jq('<td>').html(data[i]['ACCESSMODE']);
                            resultsTable.append(tRow.append(tCell));
                            tCell = jq('<td>').html(data[i]['USERID']);
                            resultsTable.append(tRow.append(tCell));
                            tCell = jq('<td>').html(data[i]['SOURCE']);
                            resultsTable.append(tRow.append(tCell));
                            tCell = jq('<td>').html(data[i]['WORKFLOWACTION']);
                            resultsTable.append(tRow.append(tCell));
                            tCell = jq('<td>').html(data[i]['IPADDRESS']);
                            resultsTable.append(tRow.append(tCell));
                            tCell = jq('<td>').html(data[i]['RECORDID']);
                            resultsTable.append(tRow.append(tCell));
                            var isSnowflake = (data[i]['SNOWFLAKE']) ? true : false;
                            tCell = jq('<td>').html(data[i]['OBJECTDESC']);
                            resultsTable.append(tRow.append(tCell));
                            if (showDev == 1) {
                                tCell = jq('<td>').html(isSnowflake?'true':'false');
                                resultsTable.append(tRow.append(tCell));
                            }
                            renderFieldsButton(data[i], tRow, isSnowflake);
                        }
                    });
                    jq('#error_div').html(msgText);
                },
                error: function(jqXHR, textStatus, errorThrown ) {
                    var resultsTable = jq('#audit_results_table');
                    resultsTable.empty();
                    jq('#error_div').text(jqXHR.responseJSON);
                }
            })
        });
    }

    function renderFieldsButton(rec, tRow, isSnowflake) {
         tCell = jq('<td>');
         if (rec['ACCESSMODE'] == 'Modify') {
             var button = jq('<input type="button" value="Fields">').appendTo(tCell);
             button.click(function() {
                 showFieldScreen(rec['CNY#'], rec['RECORDID'], rec['ACCESSTIME'], isSnowflake);
             });
         }
         tRow.append(tCell);
    }

    function showAATScreen(cny, record, accesstime) {
        jq.ajax(document.location.href, {
            data: { _aj: "aat", cny, record, accesstime },
            dataType: "json",
            success: function(data) {
                jq('#field_table').html("<th>CNY#</th><th>RECORDID</th><th>OBJECTTYPE</th><th>OBJECTKEY</th>" +
                 "<th>USERID</th><th>ACCESSTIME</th><th>ACCESSMODE</th><th>FIELDNAME</th><th>FIELDVALUE</th>");
                jq('#field_table').addClass('fieldData');
                jq.each(data, function(i) {
                  tRow = jq('<tr>');
                  tCell = jq('<td>').html(data[i]['CNY#']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['RECORDID']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['OBJECTTYPE']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['OBJECTKEY']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['USERID']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['ACCESSTIME']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['ACCESSMODE']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['FIELDNAME']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['FIELDVALUE']);
                  jq('#field_table').append(tRow.append(tCell));
                });
                jq("#field_dialog").dialog({
                    width: 900, height: 500, autoOpen : false, modal : true, show : "blind", hide : "blind"
                });
                jq("#field_dialog").dialog("open");
            }
        });
    }

    function showFieldScreen(cny, record, accesstime, isSnowflake) {
        var dbsource = isSnowflake ? 'Snowflake' : 'local';
        jq.ajax(document.location.href, {
            data: { _aj: "fields", _chosenMode: dbsource, cny, record, accesstime },
            dataType: "json",
            success: function(data) {
                jq('#field_table').html("<th>CNY#</th><th>RECORDID</th><th>OBJECT TYPE</th><th>OBJECT KEY</th>" +
                 "<th>USER ID</th><th>ACCESS TIME</th><th>ACCESS MODE</th>" +
                 "<th>FIELD NAME</th><th>FIELD TYPE</th><th>OLD VALUE</th><th>NEW VALUE</th>" +
                 "<th>SOURCE</th><th>IPADDRESS</th><th>From Snowflake?</th>");
                jq('#field_table').addClass('fieldData');
                jq.each(data, function(i) {
                  tRow = jq('<tr>');
                  tCell = jq('<td>').html(data[i]['CNY#']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['RECORDID']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['OBJECTTYPE']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['OBJECTKEY']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['USERID']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['ACCESSTIME']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['ACCESSMODE']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['FIELDNAME']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['FIELDTYPE']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['OLDVAL']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['NEWVAL']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['SOURCE']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html(data[i]['IPADDRESS']);
                  jq('#field_table').append(tRow.append(tCell));
                  tCell = jq('<td>').html((data[i]['SNOWFLAKE']) ? 'true' : 'false');
                  jq('#field_table').append(tRow.append(tCell));
                });
                jq("#field_dialog").dialog({
                    width: 900, height: 500, autoOpen : false, modal : true, show : "blind", hide : "blind"
                });
                jq("#field_dialog").dialog("open");
            }
        });
    }

</script>

<?php
}

/**
 * @param array $data
 *
 * @return string
 */
function array2string($data)
{
    $log_a = "";
    foreach ($data as $key => $value) {
        if (is_array($value)) {
            $log_a .= "<b>" . $key . "</b> = [" . array2string($value) . "], \n";
        } else {
            $log_a .= "<b>" . $key . "</b> = " . $value . ", \n";
        }
    }
    return $log_a;
}
}

/**
 * @return string List of valid auditable object types.
 */
function getObjectTypes()
{
    $types = AuditTrail::getAuditedObjectTypes();
    return json_encode($types);
}

/**
 * @param string $errMsg
 */
function showInputError($errMsg)
{
    ?>
    <br>
    <font color="red">
        <b>ERROR:<i><big>&nbsp;<?php echo $errMsg; ?></big></i></b>
        <br>
    </font>

    <?php
}

/**
 * @return string List of valid access modes.
 */
function getAccessModes()
{
    $types = AuditTrail::getAccessModes(true, false);
    return json_encode(array_flip($types));
}

/**
 * @param array  $row
 *
 * @return string
 */
function showFieldsButton($row)
{
     $out = [ '<td>' ];
     if ($row['ACCESSMODE'] == 'Create' || $row['ACCESSMODE'] == 'Modify') {
         $out[] = '<input type="button" value="Fields" onclick="showFieldScreen(' .
          $row['CNY#'] . ',\'' . $row['RECORDID'] . '\',\'' . $row['ACCESSTIME'] . '\');"/>';
     } else if (!$row['SNOWFLAKE'] && $row['ACCESSMODE'] == 'Personal data access') {
         $out[] = '<input type="button" value="AAT Refs" onclick="showAATScreen(' .
          $row['CNY#'] . ',\'' . $row['RECORDID'] . '\',\'' . $row['ACCESSTIME'] . '\');"/>';
     }
     $out[] = '</td>';
     return implode("\n", $out);
}

/**
 * @param int $cny Optional company id, if not given, uses global _chosenCompany.
 */
function setCompany($cny=0)
{
    if (!$cny) {
        $cnyTitle = Request::$r->_chosenCompany;
        if (!$cnyTitle) {
            echo "<div><span>You must enter a company title</span></div>";
            return;
        }
        $cnyInfo = AuditStorageManager::getCompanyInfo($cnyTitle);
        if ($cnyInfo === null) {
            echo "<div><span>Company $cnyTitle does not exist</span></div>";
            return;
        }
        SetDBSchemaVars($cnyInfo['DATABASEID'], $cnyInfo['SCHEMAID']);
        $cny = $cnyInfo['CNY#'];
    } else {
        SetDBSchema($cny, 'cny');
    }

    $userId = &Globals::$g->_userid;
    $userId = "@" . $cny;
}

/**
 * Get the requested audit rows from the data source (OLTP and/or Snowflake).
 *
 * @param int    $cny        Company id.
 * @param string $chosenMode Which database mode ('snowflake', 'local', or 'both')
 * @param string $objType    Which type of object.
 * @param string $objectKey  Key of object
 * @param string $accessMode Access mode, or 'ALL' for all modes.
 * @param string $startDate  Optional start access time.
 * @param string $endDate    Optional end access time.
 * @param string $whichUser  Optional user login id.
 * @param int    $maxRows    Maximum rows to return - default=0, range=0-1000.
 * @param bool   $showDups   Should we return duplicate rows?
 */
function getRows($cny, $chosenMode, $objType, $objectKey, $accessMode, $startDate, $endDate, $whichUser,
 $maxRows, $showDups)
{
    try {
        setCompany($cny);
        $reader = new AuditStorageReader();
        
        $readSnowflake = false;
        if ( FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('AUDIT_STORAGE_WRITE_TO_SNOWFLAKE_ENABLED') ) {
            $readSnowflake = ($chosenMode == 'snowflake') || ($chosenMode == 'both') || empty($chosenMode);
        }
        $readOLTP = ($chosenMode == 'local') || ($chosenMode == 'both') || empty($chosenMode);

        $accessMode = $accessMode == 'ALL' ? '' : $accessMode;
        $maxToRead = is_numeric($maxRows) ? (int)$maxRows : 0;
        $maxToRead = ($maxToRead <= 0) ? 1000 : (($maxToRead > 1000) ? 1000 : $maxToRead);
        $res = $reader->readAll($readOLTP, $readSnowflake, $cny, $objType, $whichUser, $objectKey, $accessMode,
         '', $startDate, $endDate, '', 0, '', true, false, true, !$showDups);

        //  Cut to the max here, not in readAll via FETCH FIRST, because of duplicates.
        $cnt = count($res);
        if ($maxToRead > 0 && $cnt > $maxToRead) {
            $res = array_slice($res, 0, $maxToRead);
        }

        $resHeader = [ "TOTALCOUNT" => $cnt, "MAXREAD" => $maxToRead, "ERROR" => Globals::$g->gErr->__toString()];

        $res = $reader->externalizeRecords($res, false, false);
        array_unshift($res, $resHeader);
        ob_end_clean();
        header('Content-type: application/json');
        echo json_encode($res, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_INVALID_UTF8_IGNORE);

    } catch (Exception $e) {
        ob_end_clean();
        header('HTTP/1.0 500 Internal Server Error');
        die(json_encode($e->getMessage(),
         JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT));
    }
}

/**
 * @param int                $cny
 * @param string             $record
 * @param string             $accessTime
 */
function getAAT($cny, $record, $accessTime)
{
    setCompany($cny);
    $reader = new AuditStorageReader();

    $res = $reader->read($cny, '', '', '', '', '', '', '', '', '', $record, $accessTime, false, false, false);
    ob_end_clean();
    header('Content-type: application/json');
    $str = json_encode($res, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT);
    echo $str;
}

/**
 * @param int                $cny
 * @param string             $record
 * @param string             $accessTime
 */
function getFields($cny, $record, $accessTime)
{
    setCompany($cny);
    $reader = new AuditStorageReader();

    $mode = Request::$r->_chosenMode;
    $readSnowflake = ($mode == 'Snowflake') || ($mode == 'both');
    $readOLTP = ($mode == 'local') || ($mode == 'both');

    $res = $reader->readAll($readOLTP, $readSnowflake, $cny, '', '', '', '', '', '', '', '', $record, $accessTime,
     true, true);

    ob_end_clean();
    header('Content-type: application/json');
    $str = json_encode($res, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT);
    echo $str;
}

?>
</form>
<div class="waiting"></div>
</body>
</html>
