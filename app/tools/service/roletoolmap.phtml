<?php

require_once 'common_cs.js';
echo "<h1>CS Tools Role and Tool Mapping</h1>";
$rkey = Request::$r->rkey;
if ( Request::$r->_assign && $rkey && !empty(Request::$r->tlist)) {
    CSRoleToolModel::AssignTools($rkey, Request::$r->tlist);
}
CaptureErrors();
echo '<script type="text/javascript" language="javascript" src="../resources/js/base_lib.js"></script>';
echo "<html>";
echo "<body>";
echo '<form name="rlmap" method="post">';
echo "<table border='0'>";
echo "<tr>";
echo "<td>Roles</td>";
echo "<td>";
echo "<select name='rkey' onchange='this.form.submit()'>";
$roleManager = Globals::$g->gManagerFactory->getManager('csroles');
$roleList = $roleManager->GetList(['orders'  => [['NAME', 'ASC']]]);
$roleList[] = [ 'NAME' => 'Select a role', 'RECORDNO' => ''];
foreach ($roleList as $roleInfo) {
    $roleName = $roleInfo['NAME'];
    $roleKey = $roleInfo['RECORDNO'];
    if ( $roleKey == $rkey ) {
        echo "<option value='$roleKey' selected>$roleName</option>";
    } else {
        echo "<option value='$roleKey'>$roleName</option>";
    }
}
echo "</select>";
echo "<input type=submit name=\".assign\" value=\"Assign\" >";
echo "</td>";
echo "</tr>";
if ( !empty($rkey) ) {
    $toolList = CSRoleToolModel::ListByRoles(array($rkey), true);
    //print_r($toolList);
    $toolKeyList = [];
    foreach ($toolList as $toolMappingInfo) {
        $toolKeyList[] = $toolMappingInfo['TOOL_ID'];
    }
    foreach (CS_TOOLS as $id => $tInfo) {
        if (in_array($id, $toolKeyList)) {
            echo "<tr>";
            echo "<td></td>";
            echo "<td>{$tInfo['NAME']}</td>";
            echo "</tr>";
        } else {
            echo "<tr>";
            echo "<td><input type='checkbox' name='tlist[]' value='{$id}'></td>";
            echo "<td>{$tInfo['NAME']}</td>";
            echo "</tr>";
        }
    }
}
echo "</table>";
echo "</form>";
echo "</body>";
echo "</html>";