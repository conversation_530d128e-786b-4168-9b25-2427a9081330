<?php
/**
 * @package cconsole
 */

require_once "cconsole_data_details_chart.cls";

/**
 * Generates the GLBATCH TRANSACTIONS CHART chart to be used in the data statistics page.
 *
 * @see cconsole_page_details_data_stats
 */
class cconsole_data_details_stats_glbatch_by_modified extends cconsole_data_details_chart {
    /**
     * Query the database and store results in $this->results.
     */
    function load() {
        SetDBSchema($this->company, "cny#");

        $sql = "SELECT TO_CHAR(modified, 'MON-YY') month, COUNT(record#) count
                FROM glbatch
                WHERE cny# = :1
                GROUP BY TO_CHAR(modified, 'MON-YY'), TO_CHAR(modified, 'YY-MM')
                ORDER BY TO_CHAR(modified, 'YY-MM') ASC";
        $gl_stats = QueryResult(array($sql, $this->company));
        if ($gl_stats === false) {
            throw new IAException("Can not get GL batch stats.", "BL01000004");
        }

        foreach ($gl_stats as $row) {
            $this->results["chart_data"][0][] = $row["MONTH"];
            $this->results["chart_data"][1][] = $row["COUNT"];
        }

        parent::count_ticks();
    }
}
