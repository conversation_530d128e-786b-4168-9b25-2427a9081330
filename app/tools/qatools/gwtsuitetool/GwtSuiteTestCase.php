<?php

class GwtTestCase
{

    /** @var TestCaseRequest $request */
    private $request;
    /** @var TestCaseResponse $response */
    private $response;
    /** @var string $description */
    private $description;
    /** @var string $expectedStatus */
    private $expectedStatus;
    /** @var string $testTarget */
    private $testTarget;
    /** @var string $ticketFailure */
    private $ticketFailure;

    /**
     * GwtTestCase constructor.
     *
     * @param TestCaseRequest  $request
     * @param TestCaseResponse $response
     * @param string[]         $specialFieldsMap
     */
    public function __construct($request, $response, $specialFieldsMap)
    {
        $this->request = $request;
        $this->response = $response;
        $this->description = $specialFieldsMap[GstConstants::CUSTOM_FIELD_SYMBOL . 'DESCRIPTION'] ?? "description";
        $this->expectedStatus = $specialFieldsMap[GstConstants::CUSTOM_FIELD_SYMBOL . 'STATUS'] ?? "pass";
        $this->testTarget = $specialFieldsMap[GstConstants::CUSTOM_FIELD_SYMBOL . 'TARGET'] ?? "std_company";
        $this->ticketFailure = $specialFieldsMap[GstConstants::CUSTOM_FIELD_SYMBOL . 'TICKET'] ?? "FALSE";
    }

    /**
     * @return TestCaseRequest
     */
    public function getRequest()
    {
        return $this->request;
    }

    /**
     * @return TestCaseResponse
     */
    public function getResponse()
    {
        return $this->response;
    }

    /**
     * Builds the GWT suite csv file line for this test case
     *
     * @param string $suitePath
     *
     * @return string
     */
    public function toSuiteLine($suitePath)
    {
        return join(GstConstants::CSV_SEPARATOR,
                    [ $suitePath . $this->request->getName(), $suitePath . $this->response->getName(),
                      $this->description,
                      $this->expectedStatus, $this->testTarget, $this->ticketFailure ]);
    }

}

class TestCaseRequest
{

    /** @var string $body */
    private $body;
    /** @var string $name */
    private $name;

    /**
     * TestCaseRequest constructor.
     *
     * @param OperationRequest        $operationRequest
     * @param string                  $name
     * @param GenXml                  $genXml
     * @param array[]                 $testContext
     * @param ObjectDefinitionManager $objectDefinitionManager
     */
    public function __construct($operationRequest, $name, $genXml, $testContext, $objectDefinitionManager = null)
    {
        $reqStructure = [
            'control'   => $testContext['control'],
            'operation' => [
                [
                    'authentication' => $testContext['auth'],
                    'content'        => [
                        [
                            'function' => [
                                [
                                    'controlid'                                   => 'testControlId',
                                    strtolower($operationRequest->getOperation()) => $operationRequest->getArrayStructure($objectDefinitionManager),
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
        $this->body = $genXml->build("request", $reqStructure);
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getBody()
    {
        return $this->body;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

}

class TestCaseResponse
{

    /** @var string $body */
    private $body;
    /** @var string $name */
    private $name;

    /**
     * TestCaseResponse constructor.
     *
     * @param string $type
     * @param string $object
     * @param string $name
     * @param GenXml $genXml
     */
    public function __construct($type, $object, $name, $genXml)
    {
        $respStructure = [
            'control'   => [
                [
                    'status'     => [ [ 'cdata' => 'success', ], ],
                    'senderid'   => [ [ 'cdata' => 'intacct_dev', ], ],
                    'controlid'  => [ [ 'cdata' => 'ControlIdHere', ], ],
                    'uniqueid'   => [ [ 'cdata' => 'false', ], ],
                    'dtdversion' => [ [ 'cdata' => '3.0', ], ],
                ],
            ],
            'operation' => [
                [
                    'authentication' => [
                        [
                            'status'           => [ [ 'cdata' => 'success', ], ],
                            'userid'           => [ [ 'cdata' => 'Admin', ], ],
                            'companyid'        => [ [ 'cdata' => 'XMLGatewayTest466230', ], ],
                            'locationid'       => [ [ 'cdata' => '', ], ],
                            'sessiontimestamp' => [ [ 'cdata' => '2020-10-13T01:59:34+00:00', ], ],
                            'sessiontimeout'   => [ [ 'cdata' => '2020-10-13T04:13:58+00:00', ], ],
                        ],
                    ],
                    'result'         => [
                        [
                            'status'    => [ [ 'cdata' => 'success', ], ],
                            'function'  => [ [ 'cdata' => $type, ], ],
                            'controlid' => [ [ 'cdata' => 'testControlId', ], ],
                            'data'      => [
                                [
                                    'listtype' => "objects", 'count' => "1",
                                    $object    => [ [ 'RECORDNO' => [ [ '49' ] ] ] ],
                                ] ],
                        ],
                    ],
                ],
            ],
        ];

        $this->body = $genXml->build("response", $respStructure);
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getBody()
    {
        return $this->body;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }
}

abstract class TestContext
{

    const GWT = [
        'control' =>
            [
                [
                    'senderid'   => [ [ 'cdata' => '%%gwt-partnerid%%' ] ],
                    'password'   => [ [ 'cdata' => '%%gwt-partnerpassword%%' ] ],
                    'controlid'  => [ [ 'cdata' => 'ControlIdHere' ] ],
                    'uniqueid'   => [ [ 'cdata' => 'false' ] ],
                    'dtdversion' => [ [ 'cdata' => '3.0' ] ],
                ],
            ],
        'auth'    => [
            [
                'login' => [
                    [
                        'senderid'   => [ [ 'cdata' => '%%gwt-userid%%' ] ],
                        'companyid'  => [ [ 'cdata' => '%%gwt-companyid%%' ] ],
                        'password'   => [ [ 'cdata' => '%%gwt-userpassword%%' ] ],
                        'locationid' => [ [ 'cdata' => '' ] ],
                    ],
                ],
            ],
        ],
    ];
    const POSTMAN = [
        'control' =>
            [
                [
                    'senderid'   => [ [ 'cdata' => '{{sender_id}}' ] ],
                    'password'   => [ [ 'cdata' => '{{sender_password}}' ] ],
                    'controlid'  => [ [ 'cdata' => '{{$timestamp}}' ] ],
                    'uniqueid'   => [ [ 'cdata' => 'false' ] ],
                    'dtdversion' => [ [ 'cdata' => '3.0' ] ],
                ],
            ],
        'auth'    => [
            [
                'sessionid' => [ [ 'cdata' => '{{temp_session_id}}' ] ],
            ],
        ],
    ];
}