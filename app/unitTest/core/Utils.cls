<?php

/**
 * File description
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 Intacct Corporation, All Rights Reserved
 */

namespace unitTest\core;

/**
 * Collection of utility functions for unit test
 */
class Utils
{

    /**
     * @var string|null $currentContext
     */
    private static $currentContext = null;

    /**
     * Sets the context/session to the given company and login id
     *
     * @param string $companyTitle  the title of the company
     * @param string $loginId       the user login id
     *
     * @throws \Exception for any login, session or initialization errors
     */
    public static function setContext($companyTitle, $loginId)
    {
        if ( \Globals::$g->gErr != null ) {
            \Globals::$g->gErr->Clear();
        }
        \Request::$r->_oid = 'unitTest';
        InitGlobalObjectVars();

        $contextString = $companyTitle . '@' . $loginId;

        if ( $contextString == self::$currentContext ) {
            return;
        }

        self::$currentContext = $contextString;

        SetDBSchema($companyTitle, 'loginid');
        \Request::$r->_sess = null;
        \Profile::cleanHandler();
        $res = QueryResult(array(
            "select cny# c, record# r from userinfo where cny# in (select record# from company "
            . "where title = :1) and loginid = :2",
            $companyTitle, $loginId
        ));

        if ( ! $res ) {
            throw new \Exception("Incorrect company/loginId specified for context: $companyTitle/$loginId");
        }

        \Globals::$g->_userid = $res[0]['R'] . '@' . $res[0]['C'];
        $session = \IASessionHandler::setupSession('LOGIN', \Globals::$g->_userid);
        if ( ! $session ) {
            throw new \Exception("Cannot create session for context: $companyTitle/$loginId");
        }

        \Request::$r->_op = PHP_UNITTEST_ID;
        $initparams = array('skipFilterRequest' => true, 'redirectTimeout' => false);
        $errURL = Init($initparams);

        if ( $errURL != '' ) {
            throw new \Exception("Cannot run Init() for context: $companyTitle/$loginId");
        }

        self::purgeInstances();
    }

    public static function resetContext()
    {
        self::purgeInstances();
        self::$currentContext = null;
        \Globals::$g->_userid = null;
    }

    /**
     * Utility function, helps in accessing protected or private method of a class
     * using reflection
     *
     * @param string $className    name of class having the private/protected method
     * @param string $functioName  name of private or protected method
     *
     * @return \ReflectionMethod $method  which could be invoked
     *
     */
    public static function getAccessibleReflectionMethod($className, $functioName){
        $reflection = new \ReflectionClass($className);
        $method = $reflection->getMethod($functioName);
        /** @noinspection PhpExpressionResultUnusedInspection */
        $method->setAccessible(true);
        return $method;
    }

    /**
     * Utility function, helps in setting up private method of a class
     * using reflection
     *
     * @param object $mockClassInstance the mocked Class instance for which the private or protected value should be set
     * @param string $originalClassName  The originl class name from which the mocked class has been obtained
     * @param string $propertyName       the private or protected property/member variable of the class
     * @param mixed  $propertyValue      the value which should be set for $propertyName
     *
     */
    public static function setReflectionPrivateProperty(&$mockClassInstance, $originalClassName, $propertyName, $propertyValue){
        $reflection = new \ReflectionClass($originalClassName);
        $reflectionProperty = $reflection->getProperty($propertyName);
        /** @noinspection PhpExpressionResultUnusedInspection */
        $reflectionProperty->setAccessible(true);
        $reflectionProperty->setValue($mockClassInstance, $propertyValue);
    }

    /**
     * creates a string of random printable characters that can be safely placed into a single-quoted string
     * if needed
     *
     * @param int $length
     *
     * @return string
     */
    public static function randomString($length)
    {
        // Leave off single-quote and backslash so that result can be easily embedded into a single-quoted string
        $source = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789) !@#$%^&*(-_=+`~[]{}|;:",<.>/?';
        $sourceLen = strlen($source) - 1;

        $ret = '';
        for ($i = 0; $i < $length; $i++) {
            $ret .= $source[rand(0, $sourceLen)];
        }

        return $ret;
    }

    /**
     * Invokes a private method on a partially mocked class
     *
     * This method is very specific for the needs of tests in GLSetupManagerTest.php:
     * - the param $partialMock is a partial mock of the SUT, which means means that for the method to be tested,
     *      the real method is called, but some of the other methods are mocked to faciliate testing
     *
     * - $params is in the format that works for these tests
     * - 8/7/2020 changed $params to be passed by reference. This allows unit tests that verify that correct changes
     *   are made by the function being tested
     *
     * This method should be made more general, including verifying that for the first param, a class name could
     *     be passed in, and to make the $params param more general.
     * Ideally, GLSetupManagerTest would use already-existing methods in this Utils class, or in UnitTestBase,
     *     and this method could go away.
     *
     * @param $partialMock
     * @param $privateMethodName
     * @param &$params
     *
     * @return bool return value from invoked method
     */
    public static function partialMockInvokePrivateMethod($partialMock, $privateMethodName, &$params)
    {
        // use reflection so that you can call private method
        $reflector = new \ReflectionClass($partialMock);
        $method = $reflector->getMethod($privateMethodName);
        /** @noinspection PhpExpressionResultUnusedInspection */
        $method->setAccessible(true);

        return $method->invokeArgs($partialMock, array(&$params));
    }

    private static function purgeInstances() : void
    {
        \IADimensions::resetAllDimensions();
        \Globals::$g->gManagerFactory->PurgeManagerInstances();
        \ManagerFactory::PurgeObjectInstances();
        \Pt_DataObjectDefManager::reset();
        \RegistryLoader::reset();
        // keep company cache in sync with DB and ready for the later testing
        $companyCacheHandler = \CompanyCacheHandler::getInstance();
        if ($companyCacheHandler !== null) {
            $companyCacheHandler->reload();
        }
    }
}
