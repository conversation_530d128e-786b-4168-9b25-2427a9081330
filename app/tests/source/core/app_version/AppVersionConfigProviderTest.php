<?php
namespace tests\source\core\app_version;

use AppVersionConfigProvider;
use LogManager;
use PHPUnit\Framework\MockObject\MockObject;

class AppVersionConfigProviderTest extends \PHPUnit\Framework\TestCase
{
    /**
     * @return array
     */
    public function gestGetCurrentReleaseWhenGetValueForIACFGPropertyIsInvalidReturnEmptyString()
    {
        return [
            'test 1' => [null],
            'test 2' => [[]],
            'test 3' => [['foo' => 'bar']],
            'test 4' => [['RELEASE_VERSIO' => 'foo']] ,
            'test 5' => [''],
            'test 6' => ['2024 R1']
        ];
    }
    
    /**
     * @dataProvider gestGetCurrentReleaseWhenGetValueForIACFGPropertyIsInvalidReturnEmptyString
     * @param $iaCfgResponse
     *
     * @return void
     */
    public function testGetCurrentReleaseWhenGetValueForIACFGPropertyIsInvalidReturnEmptyString($iaCfgResponse) {
        /** @var MockObject|AppVersionConfigProvider $appVersionMock */
        $appVersionConfigProviderMock = $this->getMockBuilder(AppVersionConfigProvider::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getValueForIACFGProperty'])
            ->getMock();
        $appVersionConfigProviderMock
            ->expects(self::once())
            ->method('getValueForIACFGProperty')
            ->willReturn($iaCfgResponse);
        
        self::assertSame("", $appVersionConfigProviderMock->getCurrentRelease());
    }
    
    public function testGetCurrentReleaseWhenGetValueForIACFGPropertyIsValidReturnValidAppVersion() {
        /** @var MockObject|AppVersionConfigProvider $appVersionConfigProviderMock */
        $appVersionConfigProviderMock = $this->getMockBuilder(AppVersionConfigProvider::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getValueForIACFGProperty'])
            ->getMock();
        $appVersionConfigProviderMock
            ->expects(self::once())
            ->method('getValueForIACFGProperty')
            ->willReturn(['CURRENT_VERSION' => '2024 R1']);
        
        self::assertSame("2024 R1", $appVersionConfigProviderMock->getCurrentRelease());
    }
    
    /**
     * @return array
     */
    public function validateReleaseVersionWhenReleaseVersionIsInvalidReturnFalse()
    {
        return [
            'test 1' => [null],
            'test 2' => [""],
            'test 3' => [2023],
            'test 4' => ["2023"],
            'test 5' => ["2023 "],
            'test 6' => ["2023 R"],
            'test 7' => ["2023 R5"],
            'test 8' => ["2023 X"],
            'test 9' => ["2023 X1"],
            'test 10' => ["2023 R22"],
            'test 11' => ["R1 2024"],
            'test 12' => ["2024  R1"],
            'test 13' => ["2024 R1 "],
            'test 15' => [" 2024 R1"],
            'test 16' => ["-2024 R1"],
            'test 17' => ["20245 R1"],
            'test 18' => ["202 R1"],
            'test 19' => [true],
            'test 20' => [false],
            'test 21' => ["true"],
            'test 22' => ["false"],
            'test 23' => ["foo"],
            'test 24' => ["..."],
            'test 25' => ["2024R1"],
            'test 26' => ["20R124"],
            'test 27' => ["20 R1 24"],
            'test 28' => [" "]
        ];
    }
    
    /**
     * @dataProvider validateReleaseVersionWhenReleaseVersionIsInvalidReturnFalse
     * @param $releaseVersion
     *
     * @return void
     */
    public function testValidateReleaseVersionWhenReleaseVersionIsInvalidReturnFalse($releaseVersion)
    {
        /** @var MockObject|AppVersionConfigProvider $appVersionMock */
        $appVersionConfigProviderMock = $this->getMockBuilder(AppVersionConfigProvider::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['logMessage'])
            ->getMock();
        $appVersionConfigProviderMock
            ->expects(self::once())
            ->method('logMessage')
            ->with("Aplication version $releaseVersion is not valid. Contact release team to update release version with a supported value. \n", LogManager::ERROR);
        
        self::assertFalse($appVersionConfigProviderMock->validateReleaseVersion($releaseVersion));
    }
}
