<?php

namespace tests\source\core;

use EditorUrls;
use FeatureConfigManager;
use PHPUnit\Framework\MockObject\MockObject;
use unitTest\core\UnitTestBase;
use URLEncryption;

/**
 * Class ArrayUtilTest
 *
 * @property FeatureConfigManager|MockObject $featureConfigManager
 * @coversDefaultClass EditorUrls
 *
 */
class EditorUrlsTest extends UnitTestBase
{
    /**
     * @return array
     */
    public function dataProviderForGetAuditTrailJsFunction(): array
    {
        return [
            ['entity1', 'entityKey1', 'session1', false, 'Launch(\'token\', \'Audit Trail\', 900, 600);'],
            ['entity2', 'entityKey2', 'session2', true, 'LaunchInWindow(\'token\', \'Audit Trail\', 900, 600);'],
            ['entity3', 'entityKey3', '', true, 'LaunchInWindow(\'token\', \'Audit Trail\', 900, 600);'],
            ['entity4', '', 'session4', true, 'LaunchInWindow(\'token\', \'Audit Trail\', 900, 600);'],
            ['', 'entityKey5', 'session5', false, 'Launch(\'token\', \'Audit Trail\', 900, 600);'],
        ];
    }
    
    /**
     * @param string $entity
     * @param string $entityKey
     * @param string $session
     * @param bool   $isQuixote
     * @param string $expected
     *
     * @dataProvider dataProviderForGetAuditTrailJsFunction
     */
    public function testGetAuditTrailJsFunction(string $entity, string $entityKey, string $session, bool $isQuixote, string $expected)
    {
        $testInstance = $this->createPartialMock(EditorUrls::class, ['buildUrl']);
        $featureMock = $this->createConfiguredMock(FeatureConfigManager::class, ['isFeatureEnabled' => true]);
        $assertPropertyClosure = function () use ($featureMock) {
            $this->featureConfigManager = $featureMock;
        };
        $assertPropertyClosure->bindTo($testInstance, EditorUrls::class)();
        
        $testInstance->expects(self::once())->method('buildUrl')->with('editor.phtml',
            [
                '.popup' => 3,
                '.ydialog' => 1,
                '.sess' => $session,
                '.op' => 5074,
                '.showgrid' => 1,
                '.auditEntity' => $entity,
                '.auditEntityKey' => $entityKey,
                '.dcl' => 1,
            ])->willReturn('token');
        $actual = $testInstance->getAuditTrailJsFunction($entity, $entityKey, $session, $isQuixote);
        self::assertEquals($expected, $actual);
    }
    
    /**
     * @return array
     */
    public function dataProviderForTemplateQueryFragmentsForJs(): array
    {
        return [
            [
                [
                    'actions' => ['list', 'edit'],
                    'entities' => [
                        'vendor' => ['abc', null],
                        'contact' => [null, 'xyz'],
                        'other' => [null, null],
                    ],
                ],
                2,
                '{"vendor":["token",""],' . "\n" . '"contact":["","token"],' . "\n" . '"other":["",""]}',
            ],
            [
                [
                    'actions' => ['add', 'view'],
                    'entities' => [
                        'vendor' => ['abc', 'def'],
                        'contact' => [null, 'xyz'],
                    ],
                ],
                3,
                '{"vendor":["token","token"],' . "\n" . '"contact":["","token"]}',
            ],
            [[], 0, '{}'],
        ];
    }
    
    /**
     * @param array $input
     * @param int   $urlCount
     * @param mixed $expected
     *
     * @dataProvider dataProviderForTemplateQueryFragmentsForJs
     */
    public function testTemplateQueryFragmentsForJs(array $input, int $urlCount, string $expected)
    {
        $testInstance = $this->createPartialMock(EditorUrls::class, ['buildQueryFragment']);
        $testInstance->expects(self::exactly($urlCount))->method('buildQueryFragment')->willReturn('token');
        $actual = $testInstance->templateQueryFragmentsForJs($input);
        self::assertEquals($expected, $actual);
        
        $jsonObject = json_decode($actual, true);
        self::assertNotNull($jsonObject);
    }
    
    /**
     * @return array
     */
    public function dataProviderForBuildQueryFragment(): array
    {
        return [
            [
                [
                    '.op' => '1',
                    '.it' => 'testIt',
                    '.r' => 'testR',
                    '.do' => 'testDo',
                    '.sess' => 'testSess',
                ],
                URLEncryption::TOKEN . '=',
            ],
            [[], URLEncryption::TOKEN . '='],
        ];
    }
    
    /**
     * @param mixed $input
     * @param mixed $expected
     *
     * @dataProvider dataProviderForBuildQueryFragment
     */
    public function testBuildQueryFragmentFeatureOn(array $input, string $expected)
    {
        $featureMock = $this->createConfiguredMock(FeatureConfigManager::class, ['isFeatureEnabled' => true]);
        $testInstance = new EditorUrls();
        $this->setPrivateProperty($testInstance, 'featureConfigManager', $featureMock);
        
        $result = $testInstance->buildQueryFragment($input);
        self::assertStringStartsWith($expected, $result);
    }
    
    public function testBuildQueryFragmentFeatureOff()
    {
        $featureMock = $this->createConfiguredMock(FeatureConfigManager::class, ['isFeatureEnabled' => false]);
        $testInstance = new EditorUrls();
        $this->setPrivateProperty($testInstance, 'featureConfigManager', $featureMock);
        $queries = [
            '.op' => '1',
            '.it' => 'testIt',
            '.r' => 'testR',
            '.do' => 'testDo',
            '.sess' => 'testSess',
        ];
        $expected = '.op=1&.it=testIt&.r=testR&.do=testDo&.sess=testSess';
        $actual = $testInstance->buildQueryFragment($queries);
        self::assertEquals($expected, $actual);
    }
}
