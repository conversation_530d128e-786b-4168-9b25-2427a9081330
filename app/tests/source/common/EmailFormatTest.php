<?php

use unitTest\core\UnitTestBase;

/**
 * Class EmailFormatTest
 *
 * @coversDefaultClass IAEmail
 *
 */
class EmailFormatTest extends UnitTestBase
{
    /**
     * @test validate email format
     * @covers IAEmail::isValidEmailExtendedFormat
     */
    function isValidEmailExtendedFormatTest()
    {
        $testSuite = [
            '<EMAIL>' => true,
            '<EMAIL>' => true,
            '<EMAIL>' => true,
            '<EMAIL>' => true,
            '<EMAIL>'=> true,
            '<EMAIL>' => true,    // (may <NAME_EMAIL> inbox depending on mail server)
            '<EMAIL>'=> true,     //  (one-letter local-part)
            '<EMAIL>'=> true,
            // 'admin@mailserver1'=> true,       // (local domain name with no TLD, although ICANN highly discourages dotless email addresses)  => also invalid with simple Globals::$g->gEmailFormat
            '<EMAIL>'=> true,     // (see the List of Internet top-level domains)
            // '" "@example.org'=> true,     // (space between the quotes) => also invalid with simple Globals::$g->gEmailFormat
            '"john..doe"@example.org'=> true,     // (quoted double dot)
            'mailhost!<EMAIL>'=> true, // (bangified host route used for uucp mailers)
            '<EMAIL>'=> true,  //  (% escaped mail <NAME_EMAIL> via example.org)
            'Abc.example.com'=> false,  // (no @ character)
            'A@b@<EMAIL>'=> false,    // (only one @ is allowed outside quotation marks)
            'a"b(c)d,e:f;g<h>i[j\k]<EMAIL>'=> false, // (none of the special characters in this local-part are allowed outside quotation marks)
            'just"not"<EMAIL>'=> false,   // (quoted strings must be dot separated or the only element making up the local-part)
            'this is"not\<EMAIL>'=> false,  // (spaces, quotes, and backslashes may only exist when within quoted strings and preceded by a backslash)
            'this\ still\"not\\<EMAIL>'=> false,    // (even if escaped (preceded by a backslash), spaces, quotes, and backslashes must still be contained by quotes)
            'i_like_underscore@but_its_not_allow_in_this_part.example.com'=> false,  // (Underscore is not allowed in domain part)
            '"bla bla"@example.org'=> false,     // (space between the quotes)
            '"  "@example.org'=> false,     // (bigger space between the quotes)
            '"test-14088873-\"-escaped-double-quotes"@example.com'=> true, // (known issue: a single escaped quote \" )
            'test"-14088873-double-quotes"@example.com'=> false, // (double quotes should be allow only as first and last character before @; when in the middle, it must be \" )
            '"<EMAIL>'=> false, // (open double quote, no pair with an ending unescaped double quote)
            'test-14088873-XSS<a href="#" onclick="alert(1)">XSS</a>@example.com'=> false, // (xss double quote )
            'test-14088873-XSS<a href=\'#\' onclick=\'alert(1)\'>XSS</a>@example.com'=> false, // (xss single quote )
            'test-14088873-XSS<script>alert(String.fromCharCode(88,83,83))</script>@example.com'=> false, // (xss try <script>alert('xss')</script>)
            '"test-14088873-XSS<img src=../resources/images/ia-app/logos/intacct_logo_banner.png>"@example.com'=> false, // (xss try image reflection)
        ];

        foreach ($testSuite as $email => $expected) {
            $actual = IAEmail::isValidEmailExtendedFormat($email);
            $failureMessage = "Failed to " . ($expected ? "validate" : "invalidate") . " email address " . $email;
            self::assertEquals($expected, $actual, $failureMessage);
        }

        // allow to not check the length
        $specialSuite = [
            '"aa162bee-e926-4d47-8be2-920a14d47f41:290:1kNKNe:D4nQT4jLcUDKSX4EmgNAkbXysYs"@intacct-invoice-pdf.mailhooks.sage.com' => true,  // (local-part is longer than 64 characters BUT we ignore the standard and allow it)
            '<EMAIL>'=> true,   // (local-part is longer than 64 characters BUT we ignore the standard and allow it)
        ];

        foreach ($specialSuite as $email => $expected) {
            $actual = IAEmail::isValidEmailExtendedFormat($email);
            $failureMessage = "Failed to validate special case for email address $email";
            self::assertEquals($expected, $actual, $failureMessage);
        }
    }

    /**
     * @test validate email format
     * @covers IAEmail::isValidEmailExtendedFormat
     */
    function isValidEmailExtendedFormatPerformanceTest()
    {
        $commonEmail = '<EMAIL>';
        $iterationCount = 1000;
        $start = microtime(true);
        for ($idx = 0; $idx < $iterationCount; $idx++) {
            IAEmail::isValidEmailExtendedFormat($commonEmail);
        }
        $durationExtended = microtime(true) - $start;

        $startOld = microtime(true);
        for ($idx = 0; $idx < $iterationCount; $idx++) {
            IAEmail::isValidEmailFormat($commonEmail);
        }
        $durationStandard = microtime(true) - $startOld;

        self::assertTrue($durationExtended - $durationStandard < 0.002);
    }
}
