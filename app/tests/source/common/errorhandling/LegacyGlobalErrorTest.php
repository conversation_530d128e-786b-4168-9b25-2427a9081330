<?php

namespace tests\source\common\errorhandling;

use IAIssueCategory;
use IAIssueDef;
use unitTest\core\UnitTestBaseContext;

require_once 'backend_locale.inc';

/**
 * test usages relating to gErr
 */
class LegacyGlobalErrorTest extends UnitTestBaseContext
{
    /**
     * Setup data common to all invocations of test routines
     */
    public static function setUpBeforeClass() : void
    {
        // Set the global userid to a dummy value so locale() functions work
        global $_userid;
        $_userid = '1@1';
    }

    /**
     * @return void
     */
    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
    }

    public function testIssueCategory()
    {
        $category = IAIssueCategory::INFORMATION_MISSING;
        var_dump($category);
        self::assertNotNull($category);
        self::assertTrue(strcmp('IA.INFORMATION_MISSING', $category->value) === 0);
        print_r('localized IAIssueCategory::INFORMATION_MISSING: ' . $category->toString());
    }

    /**
     * sanity test IAError through Global Error addError function
     *
     * @return \IAError[]
     */
    public function testAddError() : array
    {
        $description1 = 'Required information missing. ';
        $accountKey = "account key input";
        $dimFieldText = "dim field text";

        \Globals::$g->gErr->addError(
            'BL03000011', __FILE__.'.'.__LINE__, $description1,
            "Account " .  $accountKey . " requires ".$dimFieldText/*, 'Enter '.$dimFieldText.' for this entry.'*/
        );
        self::assertNotNull($i18nError = \Globals::$g->gErr->popIAError());
        self::assertTrue($i18nError instanceof \IAError);
        if ($i18nError instanceof \IAError) {
            self::assertTrue(strcmp($description1, $i18nError->getDescription1LogString()) === 0); // todo - overload getDescription1String to return both localized and log version
            self::assertTrue(strcmp($i18nError->getId(), $i18nError->getLegacyErrorNo()) === 0);
            if (\IAIssueUtil::reserveLegacyMisbehvior()) {
                self::assertEmpty($i18nError->getCorrectionString());
                echo "I18N_ISSUE_CFG_RESERVE_LEGACY_MISBEHAVIOR setting is true\n";
            } else {
                self::assertNotEmpty(
                    $correction = $i18nError->getCorrectionString()); // expecting "[>>> i18n tokenization error]"
                self::assertTrue(strcontains($correction, \IAMessage::LEGACY_MESSAGE_ERRO_HINT));
                echo "I18N_ISSUE_CFG_RESERVE_LEGACY_MISBEHAVIOR setting is false\n";
                echo $correction;
            }
            return [ $i18nError ];
        }
        self::assertTrue(false, "invalid IAError instance");
        return [];
    }

    /**
     * sanity test IAError with unknown error code
     */
    public function testAddErrorWithUnknownErrorNo()
    {
        $description1 = 'Required information missing.';

        \Globals::$g->gErr->addError(
            'UNKNOWN-0001', __FILE__.'.'.__LINE__, $description1);
        self::assertNotNull($i18nError = \Globals::$g->gErr->popIAError());
        self::assertTrue($i18nError instanceof \IAError);
        self::assertTrue(strcmp($i18nError->getId(), $i18nError->getLegacyErrorNo()) === 0);
    }

    /**
     * verify that IAError, without description 1, can be converted into details object properly in REST error response
     *
     * @throws \IAException
     */
    public function testNoDesc1ErrorToAdditionalErrorInfo()
    {
        $lineNo = 1;
        $msg = "testNoDesc1ErrorToAdditionalErrorInfo: Duplicate effective start date in line $lineNo";
        \Globals::$g->gErr->addIAError("CO-1042", GetFL(), "", [], $msg,
                                       [ 'LINE_NO' => $lineNo ]);
        self::assertNotNull($i18nError = \Globals::$g->gErr->popIAError());
        self::assertTrue($i18nError instanceof \IAError);
        if ( $i18nError instanceof \IAError ) {
            $additionalErrorInfo = \APIError::getAdditionalErrorInfoFromIAIssue($i18nError);
            $this->assertNotNull($additionalErrorInfo);
        } else {
            self::fail("test need a valid IAError instance");
        }
    }

    /**
     * validate that description 2 included in additional information when description 1 is missing
     *
     * @return void
     * @throws \APIInternalException
     * @throws \IAException
     */
    public function testNoDesc1ErrorInAPIResponse()
    {
        $keyValidation = "key validation";
        $msg = "testNoDesc1ExceptionInAPIResponse: Duplicate effective start date in $keyValidation";
        $detailError = \IAError::getInstance(
            "CO-1042",
            \IAIssueData::initByLegacyParams(
            // build fake i18n error input given legacy error messages
                "",
                [],
                $msg,
                ['LINE_NO' => $keyValidation],
                "",
                [])
        );
        $apiError = \APIError::getInstance(
            \APIErrorMessages::UNPROCESSABLE_ENTITY_OPERATION_FAILED_0001, [
            "OPERATION" => \APIConstants::getOperation("READ"),
            "RESOURCE_NAME" => "company"
        ], true);
        $apiError->addIAError($detailError);
        $apiResponse = $apiError->getErrorResponsePayload();
        self::assertNotEmpty($apiResponse);
        $apiResponseJson = json_encode($apiResponse);
        self::assertTrue(str_contains($apiResponseJson, $keyValidation));
    }

    /**
     * validate that description 2 included in additional information when description 1 is missing
     *
     * @return void
     * @throws \APIInternalException
     * @throws \IAException
     */
    public function testConcatenateLegacyDescriptionsInAPIResponse()
    {
        $description1 = 'The form was not processed';
        $description2 = 'Module of document type and schedule do not match';
        $correction = 'Enter suitable values in the field';
        $detailError = \IAError::getInstance(
            "TAX-0119",
            \IAIssueData::initByLegacyParams(
                "description1 log",
                [],
                "description2 log",
                [],
                "correction log",
                []
            )
        );
        $apiError = \APIError::getInstance(
            \APIErrorMessages::UNPROCESSABLE_ENTITY_OPERATION_FAILED_0001,
            [
                "OPERATION" => \APIConstants::getOperation("READ"),
                "RESOURCE_NAME" => "company"
            ],
            true
        );
        $apiError->addIAError($detailError);
        $apiResponse = $apiError->getErrorResponsePayload();
        self::assertNotEmpty($apiResponse);
        $apiResponseJson = json_encode($apiResponse);
        if (!str_contains($apiResponseJson, $description1)
            || !str_contains($apiResponseJson, $description2)
            || !str_contains($apiResponseJson, $correction)) {
               echo 'investigate: ' . $apiResponseJson;
        }
        // comparing string literal does not sustain in different environments
        // but keep them for demo and debug purpose
        //self::assertTrue(str_contains($apiResponseJson, $description1), $apiResponseJson);
        //self::assertTrue(str_contains($apiResponseJson, $description2), $apiResponseJson);
        //self::assertTrue(str_contains($apiResponseJson, $correction), $apiResponseJson);
        //self::assertTrue(str_contains($apiResponseJson, "correction"), $apiResponseJson);
    }

    /**
     * validate that description 2 included in additional information when description 1 is missing
     *
     * @return void
     * @throws \APIInternalException
     * @throws \IAException
     */
    public function testNoCorrectionInAPIResponse()
    {
        $description1 = 'The entities do not share a base currency.';
        $detailError = \IAError::getInstance(
            "TAX-0285",
            \IAIssueData::initByLegacyParams(
                "description1 log",
                [],
                "",
                [],
                "redundant correction log",
                []
            )
        );
        $apiError = \APIError::getInstance(
            \APIErrorMessages::UNPROCESSABLE_ENTITY_OPERATION_FAILED_0001,
            [
                "OPERATION" => \APIConstants::getOperation("READ"),
                "RESOURCE_NAME" => "company"
            ],
            true
        );
        $apiError->addIAError($detailError);
        $apiResponse = $apiError->getErrorResponsePayload();
        self::assertNotEmpty($apiResponse);
        $apiResponseJson = json_encode($apiResponse);
        if (!str_contains($apiResponseJson, $description1)) {
            echo 'investigate: ' . $apiResponseJson;
        }
        // comparing string literal does not sustain in different environments
        // but keep them for demo and debug purpose
        //self::assertTrue(str_contains($apiResponseJson, $description1), $apiResponseJson);
        //self::assertFalse(str_contains($apiResponseJson, "correction"), $apiResponseJson);
    }

    /**
     * sanity test IAError through Global Error addError function
     *
     * @return \IAError[]
     */
    public function testAddError2() : array
    {
        $description1 = 'Your Session is corrupted. Please contact Customer Support';
        $description2 = 'description2 log';

        \Globals::$g->gErr->addError(
            'BL02000012', __FILE__.'.'.__LINE__, $description1, $description2);
        self::assertNotNull($i18nError = \Globals::$g->gErr->popIAError());
        self::assertTrue($i18nError instanceof \IAError);
        if ($i18nError instanceof \IAError) {
            self::assertTrue(strcmp($description1, $i18nError->getDescription1LogString()) === 0); // todo - overload getDescription1String to return both localized and log version
            self::assertTrue(strcmp($i18nError->getId(), $i18nError->getLegacyErrorNo()) === 0);
            if (\IAIssueUtil::reserveLegacyMisbehvior()) {
                self::assertEmpty($i18nError->getCorrectionString());
                echo "I18N_ISSUE_CFG_RESERVE_LEGACY_MISBEHAVIOR setting is true\n";
            } else {
                echo "I18N_ISSUE_CFG_RESERVE_LEGACY_MISBEHAVIOR setting is false\n";
                self::assertNotEmpty(
                    $correction = $i18nError->getCorrectionString()); // expecting "[>>> i18n tokenization error]"
                self::assertTrue(strcontains($correction, \IAMessage::LEGACY_MESSAGE_ERRO_HINT));
                echo $correction;
            }
            return [ $i18nError ];
        }
        self::assertTrue(false, "invalid IAError instance");
        return [];
    }

    /**
     * sanity test IAError through Global Error addError function
     *
     * @return \IAError[]
     */
    public function testAddErrorNewDef() : array
    {
        $errorCode = 'ERR-0001';
        $legacyErrorNo = 'BL03002188';
        $log1 = 'some weird descripition1';
        $log2 = 'more internal description2';
        \Globals::$g->gErr->addError(
            $errorCode, __FILE__.'.'.__LINE__, $log1, $log2
        );
        self::assertNotNull($i18nError = \Globals::$g->gErr->popIAError());
        self::assertTrue($i18nError instanceof \IAError);
        if ($i18nError instanceof \IAError) {
            // todo - overload getDescription1String to return both localized and log version
            self::assertTrue(strcmp($log1, $i18nError->getDescription1LogString()) === 0);
            self::assertTrue(strcmp($log2, $i18nError->getDescription2LogString()) === 0);
            self::assertTrue(strcmp($errorCode, $i18nError->getId()) === 0);
            self::assertTrue(strcmp($legacyErrorNo, $i18nError->getLegacyErrorNo()) === 0);
            self::assertNotNull($i18nError->getDescription1()->__toString());
            self::assertNotNull($i18nError->getDescription2()->__toString());
            self::assertNotNull($i18nError->getCorrection()->__toString());
            self::assertNotNull($i18nError->getCategoryString());
            return [ $i18nError ];
        }
        self::assertTrue(false, "invalid IAError instance");
        return [];
    }

    /**
     * @return \IAError
     * @throws \IAException
     */
    public function testAddErrorWithPlaceholders() : \IAError
    {
        $errorCode = 'ERR-0002';
        $log1 = 'random des1 log';
        $correct = 'arbitrary correction log';
        \Globals::$g->gErr->addIAError($errorCode, GetFL(),
                                       $log1, ['FIELD_VALUE'=>'-1'],
                                       '', [],
                                       $correct, ['MAX_VALUE'=>10,'MIN_VALUE'=>1]);
        self::assertNotNull($i18nError = \Globals::$g->gErr->popIAError());
        self::assertTrue($i18nError instanceof \IAError);
        if ($i18nError instanceof \IAError) {
            // todo - overload getDescription1String to return both localized and log version
            self::assertTrue(strcmp($log1, $i18nError->getDescription1LogString()) === 0);
            self::assertTrue(strcmp($correct, $i18nError->getCorrectionLogString()) === 0);
            self::assertTrue(strcmp($errorCode, $i18nError->getId()) === 0);
            self::assertNull($i18nError->getLegacyErrorNo());
            self::assertNotNull($i18nError->getDescription1()->__toString());
            self::assertNull($i18nError->getDescription2());
            self::assertNotNull($correctionText = $i18nError->getCorrection()->__toString());
            var_dump($correctionText);
            self::assertNotNull($i18nError->getCategoryString());
            return $i18nError;
        }
        self::assertTrue(false, "invalid IAError instance");
        return $i18nError;
    }

    /**
     * sanity test IAError through Global Error addError function
     *
     * @return \IAError[]
     */
    public function testAddErrorWithoutLogStrings() : array
    {
        $errorCode = 'ERR-0001';
        $source = 'test ui';
        \Globals::$g->gErr->addIAError($errorCode, $source,
                                       '', [],
                                       '', [],
                                       '', []);
        self::assertNotNull($i18nError = \Globals::$g->gErr->popIAError());
        self::assertTrue($i18nError instanceof \IAError);
        if ($i18nError instanceof \IAError) {
            // todo - overload getDescription1String to return both localized and log version
            self::assertNotNull($i18nError->getDescription1LogString());
            self::assertNotNull($i18nError->getCorrectionLogString());
            self::assertNotNull($i18nError->getId());
            self::assertNotNull($i18nError->getDescription1()->__toString());
            self::assertNotNull($i18nError->getDescription2()->__toString());
            self::assertNotNull($i18nError->getCorrection()->__toString());
            self::assertNotNull($i18nError->getCategoryString());
            return [ $i18nError ];
        }
        self::assertTrue(false, "invalid IAError instance");
        return [];
    }

    /**
     * sanity test IAError through Global Error addIAError function
     *
     * @return \IAError[]
     */
    public function testAddIAError() : array
    {
        \Globals::$g->gErr->addIAErrorInstance(
            \IAError::getInstance('ERR-0001'),
            GetFL()
        );
        self::assertNotNull($i18nError = \Globals::$g->gErr->popIAError());
        if ($i18nError instanceof \IAError) {
            return [ $i18nError ];
        }
        self::assertTrue(false, "invalid IAError instance");
        return [];
    }

    /**
     * verify message and message id consistency in error details
     *
     * @throws \APIInternalException
     */
    public function testIGCError()
    {
        $action = "Remove the reporting books from the following and try again.";
        $messageId = "IA.WE_CAN_T_DELETE_THE_BOOK_BECAUSE_IT";
        $description1 = "We can't delete the book because it's being used. " . $action;
        $description2 = 'Financial Reports - CTAissue, CTAissue_2';
        $financialReports = 'CTAissue, CTAissue_2';

        \Globals::$g->gErr->addIAError('IGC-0257', __FILE__ . ':' . __LINE__, $description1, [],
            $description2, ['FINANCIAL_REPORTS' => $financialReports]
        );
        $i18nError = \Globals::$g->gErr->getIAError(0);
        self::assertTrue($i18nError instanceof \IAError);
        $iaErrorString = $i18nError->__toString();
        self::assertNotEmpty($iaErrorString);
        $restException = (new \APIInternalException())->setAPIError(\APIError::getInstance(\APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0006,[],true));
        $response = \HTTPAPIResponse::getErrorResponseInstance($restException->getAPIError()->freeze());
        self::assertNotNull($response);
        $restString = $response->__toString();
        self::assertNotEmpty($restString);

        // verify the consistency
        self::assertTrue(!str_contains(strtolower($restString), "financial reports"), $restString);
        self::assertTrue(str_contains(strtolower($restString), strtolower($messageId)), $restString);
    }

    /**
     * sanity test adding IAError through \IAError::addToGErr
     *
     * @return \IAError|mixed|null
     * @throws \IAException
     */
    public function testAddErrorToGerr()
    {
        $errorCode = 'ERR-0001';
        \IAError::getInstance($errorCode)->addToGErr();
        self::assertNotNull($i18nError = \Globals::$g->gErr->popIAError());
        if ($i18nError instanceof \IAError) {
            self::assertTrue($errorCode === $i18nError->getId());
        } else {
            self::assertTrue(false, "invalid IAError instance");
        }
        return $i18nError;
    }

    /**
     * @return \IAError
     * @throws \IAException
     */
    public function testAddErrorWithPlaceholdersNoLogs() : \IAError
    {
        $errorCode = 'ERR-0002';
        \Globals::$g->gErr->addIAError($errorCode, GetFL(),
                                       null, ['FIELD_VALUE'=>'-1'],
                                       null, [],
                                       null, ['MAX_VALUE'=>10,'MIN_VALUE'=>1]);
        self::assertNotNull($i18nError = \Globals::$g->gErr->popIAError());
        self::assertTrue($i18nError instanceof \IAError);
        self::assertNull($i18nError->getLegacyErrorNo());
        self::assertEquals($i18nError->getId(), $i18nError->getLegacyErrorNo() ?? $i18nError->getId());
        return $i18nError;
    }

    /**
     * @depends testAddErrorWithPlaceholdersNoLogs
     * @param \IAError $i18nError
     *
     * @return void
     * @throws \Exception
     */
    public function testAddErrorNoRuntimeLogProvided(\IAError $i18nError)
    {
        self::assertNotEmpty($i18nError->getDescription1LogString(),
                             'ERR-0002 description-1 log string should not be empty');
        self::assertNull($i18nError->getDescription2LogString(),
                         'ERR-0002 description-2 log string should be null');
        self::assertNotEmpty($i18nError->getCorrectionLogString(),
                             'ERR-0002 correction log string should not be empty');
        self::assertNotEmpty($i18nError->getDescription1()->getLogString(),
                             'ERR-0002 description-1 log string should not be empty');
        self::assertNull($i18nError->getDescription2(),
                         'ERR-0002 description-2 should be null');
        self::assertNotEmpty($i18nError->getCorrection()->getLogString(),
                             'ERR-0002 correction log string should not be empty');
    }

    /**
     * sanity test log string through Global Error addIAErrorInstance function
     *
     * @throws \IAException
     */
    public function testAddErrorInstanceNoLog()
    {
        $accountKey = '12345';
        $dimentionFieldInfoPrefix = 'dimentions field text in';
        \Globals::$g->gErr->addIAErrorInstance(
            \IAError::getInstance(
                'ERR-0001',
                \IAIssueData::initByPlaceholders(
                    [],
                    [
                        'ACCOUNT_KEY'          => $accountKey,
                        'DIMENSION_FIELD_TEXT' => $dimentionFieldInfoPrefix . ' description2'
                    ],
                    [
                        'DIMENSION_FIELD_TEXT' => $dimentionFieldInfoPrefix . ' correction'
                    ]
                )),
            GetFL()
        );
        print_r('IA.TYPE value is ' . \I18N::getSingleToken("IA.TYPE"));
        self::assertNotNull($i18nError = \Globals::$g->gErr->popIAError());
        self::assertTrue($i18nError instanceof \IAError);
        self::assertNotEmpty($i18nError->getDescription1LogString(),
                             'ERR-0001 description-1 log string should not be empty');
        self::assertNotEmpty($i18nError->getDescription2LogString(),
                             'ERR-0001 description-2 log string should not be empty');
        self::assertNotEmpty($i18nError->getCorrectionLogString(),
                             'ERR-0001 correction log string should not be empty. MessageMap content: ' .
                             print_r($i18nError->getDescription2()->getLocaleHandler()->getMessageMap(), true));
        self::assertNotEmpty($i18nError->getDescription1()->getLogString(),
                             'ERR-0001 description-1 log string should not be empty');
        self::assertNotEmpty($i18nError->getDescription2()->getLogString(),
                             'ERR-0001 description-2 log string should not be empty');
        self::assertNotEmpty($i18nError->getCorrection()->getLogString(),
                             'ERR-0001 correction log string should not be empty');
        if (strcasecmp(\I18N::getSingleToken("IA.TYPE"), 'Type') === 0) {
            // I18N is returning values
            echo ">>> I18N is resolving token and returning value.\n";
            self::assertStringContainsString(
                $accountKey,
                $i18nError->getDescription2LogString(),
                'placholder value ' . $accountKey . ' is missing. MessageMap content: ' .
                print_r($i18nError->getDescription2()
                                  ->getLocaleHandler()
                                  ->getMessageMap(), true));
            self::assertStringContainsString(
                $dimentionFieldInfoPrefix,
                $i18nError->getDescription2LogString(),
                'placholder value ' . $dimentionFieldInfoPrefix . ' is missing');
            self::assertStringContainsString(
                $dimentionFieldInfoPrefix,
                $i18nError->getCorrectionLogString(),
                'placholder value ' . $dimentionFieldInfoPrefix . ' is missing');
        } else {
            // I18N is not returning values
            echo ">>> I18N is not resolving token and returning value.\n";
        }
    }

    /**
     * sanity test IAError through abusive Global Error usage
     *
     * @return void
     */
    public function testAbusiveGErrUsage()
    {
        // this is an abusive usage found in existing code
        // we want to tolerate it, so app does not fail due to this type of legacy usages
        $errorId = 'BL03002154';
        \Globals::$g->gErr->errors[] = [
            'NUMBER' => $errorId,
            'SOURCE' => GetFL(),
            'DESCRIPTION' => 'Invlaid vendor name.',
            'CDESCRIPTION' => '',
            'CORRECTION' => 'Enter a valid vendor and try again.'
        ];
        $i18nError = \Globals::$g->gErr->popIAError();
        //self::assertNotNull($i18nError->getCategory());
        self::assertTrue(strcmp($i18nError->getId(), $errorId) === 0);
    }

    /**
     * Test locale message concatenated with meta data
     *
     * @depends testAddIAError
     *
     * @param \IAError[] $error
     *
     * @return void
     */
    public function testPrintFormat(array $error)
    {
        $moduleName = "subledger";
        $objectName = "IA.SampleObject";
        $requestId = "Yv8l1mOtE9FHVFjJatuv_gAAAAw";
        self::assertNotNull(($i18nError = $error[0]) ?? null);
        $description1 = $i18nError->getDescription1();
        $description1
            ->/*setDelimiter(' : ')->*/setTargetObjectInfo($objectName, true)
            ->setTargetDomainInfo($moduleName)
            ->setRequestInfo($requestId)
            ->setTargetFieldInfo("IA.STATUS", true);
        $messageEnriched = $i18nError->getDescription1String(\IAMessageFormatStyles::GENERIC);
        self::assertNotNull($messageEnriched);
        self::assertTrue(str_starts_with($messageEnriched, $moduleName) === true);
        self::assertTrue(str_contains($messageEnriched, $objectName));
        self::assertTrue(4 == substr_count($messageEnriched, $i18nError->getDescription1()->getDelimiter()));
        var_dump($messageEnriched);
        $messageOnly = $i18nError->getDescription1String(\IAMessageFormatStyles::MESSAGE_ONLY);
        self::assertNotNull($messageOnly);
        self::assertTrue(0 == substr_count($messageOnly, $i18nError->getDescription1()->getDelimiter()));
        var_dump($messageOnly);
        $message = $i18nError->getDescription1String();
        self::assertNotNull($message);
        self::assertTrue(0 == substr_count($message, $i18nError->getDescription1()->getDelimiter()));
        self::assertTrue(0 === strcmp($message, $messageOnly));
        var_dump($message);
    }

    /**
     * sanity test IAWarning through Global Error addWarning function
     *
     * @return \IAWarning[]
     */
    public function testAddWarning() : array
    {
        $log1 = "The payment posting date occurs outside the current period.";
        \Globals::$g->gErr->AddWarning($log1, __FILE__ . ":" . __LINE__);
        self::assertNotNull($iaWarning = \Globals::$g->gErr->popIAWarning());
        self::assertTrue($iaWarning instanceof \IAWarning);
        if ($iaWarning instanceof \IAWarning) {
            return [ $iaWarning ];
        }
        self::assertTrue(false, "invalid IAWarning instance");
        return [];
    }

    /**
     * sanity test adding IAError through \IAError::addToGErr
     *
     * @return void
     * @throws \IAException
     */
    public function testAddWarningToGerr()
    {
        $errorCode = 'ERR-0001';
        \IAWarning::getInstance($errorCode)->addToGErr();
        self::assertNotNull($iaWarning = \Globals::$g->gErr->popIAWarning());
        if ($iaWarning instanceof \IAWarning) {
            self::assertTrue($errorCode === $iaWarning->getId());
        } else {
            self::assertTrue(false, "invalid IAError instance");
        }
    }

    /**
     * sanity test IAError through Global Error addIAError function
     *
     * @return \IAWarning[]
     */
    public function testAddIAWarning() : array
    {
        $log1 = 'Failed to onbaord the company for Smart transaction services';
        $warningCode = 'ERR-0001';
        \Globals::$g->gErr->AddDetailWarning(
            'ERR-0001',
            GetFL(),
            $log1);
        self::assertNotNull($iaWarning = \Globals::$g->gErr->popIAWarning());
        if ($iaWarning instanceof \IAWarning) {
            self::assertTrue(strcmp($warningCode, $iaWarning->getId()) === 0);
            self::assertTrue(strcmp($log1, $iaWarning->getDescription1LogString()) === 0);
            return [ $iaWarning ];
        }
        self::assertTrue(false, "invalid IAWarning instance");
        return [];
    }

    public function testErrorCode()
    {
        $errorCode = "BLAH-0001";
        if (preg_match(IAIssueDef::ERROR_ID_FORMAT, $errorCode, $matches) !== 1 || count($matches) != 5) {
            echo "invalid error code " . $errorCode;
        }
        echo "success";
        self::assertTrue(true);
    }

    /**
     * verify that there are no regression in existing legacy functions
     *
     * @return void
     * @throws \IAException
     */
    public function testLegacyGerrUtil()
    {
        $errorOne = 'ERR-0001';
        $errorTwo = 'ERR-0002';
        $toStringResult = 'to string result: ';
        \IAError::getInstance($errorOne, \IAIssueData::initByPlaceholders(
            [],
            ['ACCOUNT_KEY'=>'accountKey', 'DIMENSION_FIELD_TEXT'=>'dimensionField'],
            ['DIMENSION_FIELD_TEXT'=>'dimensionFieldText2']))->addToGErr();
        \IAError::getInstance($errorTwo, \IAIssueData::initByPlaceholders(
            ['FIELD_VALUE'=>'field-value'],
            [],
            ['MIN_VALUE'=>'1', 'MAX_VALUE'=>'10'],))->addToGErr();
        self::assertTrue(\Globals::$g->gErr->FindError($errorOne));
        self::assertTrue(\Globals::$g->gErr->FindError($errorTwo));
        self::assertNotNull($errorOneIndex = \Globals::$g->gErr->FindErrorIdx($errorOne));
        self::assertNotNull($errorTwoIndex = \Globals::$g->gErr->FindErrorIdx($errorTwo));
        self::assertTrue($errorOneIndex !== $errorTwoIndex);

        // IA-75831 - make sure toString does not error out
        $errorTwoToString = $toStringResult . \Globals::$g->gErr->popIAError();
        self::assertTrue(strlen($errorTwoToString) > strlen($toStringResult));
        self::assertTrue(strcontains($errorTwoToString, $errorTwo));
        $errorOneToString = $toStringResult . \Globals::$g->gErr->popIAError();
        self::assertTrue(strlen($errorOneToString) > strlen($toStringResult));
        self::assertTrue(strcontains($errorOneToString, $errorOne));
    }

    public function testBatchWithPlaceHolders()
    {
        \I18N::addToken('IA.TEST_TOKEN');
        \I18N::addToken('IA.TEST_TOKEN',[
            ['name' => 'PLACEHOLDER1', 'value' => 'VALUE1'],
            ['name'=> 'PLACEHOLDER2', 'value' => 'VALUE2']]);

        \I18N::addToken('IA.TEST_TOKEN2',[
            ['name' => 'PLACEHOLDER1', 'value' => 'VALUE1']]);
        \I18N::addToken('IA.TEST_TOKEN2:CONTEXT1',[
            ['name'=>'PLACEHOLDER2', 'value' => 'VALUE0'],]);
        \I18N::addToken('IA.TEST_TOKEN2:CONTEXT2',[
            ['name'=>'PLACEHOLDER2', 'value' => 'VALUE1']]);
        \I18N::addToken('IA.SDS');
        $existingPlaceholderReturnSetting = \I18N::isPlaceholderReturn();
        \I18N::setPlaceholderReturn(true);
        $result = \I18N::getText();
        self::assertNotNull($result);
        $result2 = \I18N::getText();
        self::assertNotNull($result2);
        \I18N::setPlaceholderReturn($existingPlaceholderReturnSetting);
    }

    public function testConfigProperty()
    {
        self::assertNotNull($forceLegacyToFail = \IAIssueUtil::forceLegacyErrorToFail());
        print_r(\IAIssueUtil::I18N_ISSUE_CFG_FORCE_LEGACY_TO_FAIL . ' setting is ' . ($forceLegacyToFail ? 'TRUE' : 'FALSE') . PHP_EOL);
        self::assertNotNull($showPlaceholders = \IAIssueUtil::showPlaceholders());
        print_r(\IAIssueUtil::I18N_ISSUE_CFG__SHOW_PLACEHOLDER . ' setting is ' . ($showPlaceholders ? 'TRUE' : 'FALSE') . PHP_EOL);
    }

    /**
     * @return void
     */
    public function testSpellCheck()
    {
        $pspell_link = pspell_new("en");
        foreach([
                    'testt'=>false,
                    'test.result'=>false,
                    'attempts'=>true,
                    'Roy'=>true,
                    'duplicated'=>true
                ] as $word=>$expectedResult)
        {
            $result = pspell_check($pspell_link, $word);
            echo $word . ' is ' . ($result ? 'correctly' : 'wrongly') . ' spelled.' . PHP_EOL;
            self::assertTrue($result === $expectedResult, $word . " spell check failed. Expected " . $expectedResult . ", but got " . $result);
        }
    }

    /**
     * Since PHP allows duplicate keys in array, there could potentially a risk that engineer created
     * duplicate definition in same or different issuedefs files.
     *
     * This unit test is to load all error messages definition, in "inc" file format, files without evaluate it
     * and then look up if any duplicates detected
     *
     * @return void
     */
    public function testDuplicateIssueDefKeys()
    {
        // Get an array of all issuedefs.*.inc files in the inc folder
        $inc_files = glob(INCDIR . "issuedefs.*.inc");

        // instead of evaluate the inc files
        // look up error ids by pattern
        $moduleScanned = [];
        $issueDefKeys = [];
        foreach ($inc_files as $inc_file) {
            // Load the .inc file
            include $inc_file;

            $incFileContents = file_get_contents($inc_file);
            $moduleName = explode('.', explode('/', $inc_file)[4] ?? '')[1]?? '';
            $moduleScanned[] = $moduleName;
            if (preg_match_all('/([A-Z]{2,5})-([0-9]{4})/', $incFileContents, $matches)) {
                foreach ($matches[0] as $match) {
                    $issueDefKeys[] = $match;
                }
            }
        }
        $this->assertNotEmpty($issueDefKeys, 'no issue definitions found');

        $counted = array_count_values($issueDefKeys);

        // Loop through the counted array and find elements with count > 1
        $duplicates = array();
        foreach ($counted as $key => $value) {
            if ($value > 1) {
                // if any error id has shown more than once, we detected a duplicate
                $duplicates[] = $key;
            }
        }

        // Print the duplicates
        if (count($duplicates) > 0) {
            $this->fail("Duplicate issue defnition detected: " . implode(", ", $duplicates) . "\n");
        } else {
            echo "No duplicates issue definitions found in " . implode(", ", $moduleScanned) . "\n";
        }
    }

    /**
     * @return void
     */
    public function testZipIssueDef() {
        $base64Encoded = \IAIssueDef::getCompressedIssueDefinitions(['rest']);
        $this->assertNotNull($base64Encoded);
    }

    /**
     * return null when no domain provided
     *
     * @return void
     */
    public function testEmptyZipIssueDef() {
        $base64Encoded = \IAIssueDef::getCompressedIssueDefinitions([]);
        $this->assertNull($base64Encoded);
    }

    /**
     * throw APIException when domain/module name not found
     *
     * @return void
     */
    public function testUnkownZipIssueDef() {
        $this->expectException(\APIException::class);
        \IAIssueDef::getCompressedIssueDefinitions(['abc']);
    }

    /**
     * Test IA REST content negotiation and verify it supports RFC7231 & RFC4647
     * When no value is provided, "*" is provided, or no supported locale is provided,
     * make sure content negotiation pick en-US
     *
     * @return void
     */
    public function testAcceptLanguageValue()
    {
        $sampleAcceptedLanguageValues = [
            ["value"=>"en-US,en;q=0.9,fr-CA;q=0.8","answer"=>"en-US"],
            ["value"=>"es-ES,es;q=0.8,en-US;q=0.5,en;q=0.3","answer"=>"es-ES"],
            ["value"=>"zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7","answer"=>"en-US"],
            ["value"=>"fr-FR;q=0.8,de;q=0.7,en-US;q=0.5,en;q=0.3","answer"=>"fr-FR"],
            ["value"=>"fr-CA;q=0.8,de;q=0.7,en-US;q=0.5,en;q=0.3","answer"=>"fr-CA"],
            ["value"=>"de-DE;q=0.8,de;q=0.7,en-US;q=0.5,en;q=0.3","answer"=>"de-DE"],
            ["value"=>"ja-JP","answer"=>"en-US"],
            ["value"=>"en;q=0.5,*;q=0.1","answer"=>"en-US"],
            ["value"=>"en-US,en;q=0.9","answer"=>"en-US"],
            ["value"=>"en-US;q=0.8, en-GB;q=0.5, en;q=0.3","answer"=>"en-US"],
            ["value"=>"fr-CA-fran-CA","answer"=>"en-US"],
            ["value"=>"ja,en-US;q=0.7,en;q=0.3","answer"=>"en-US"],
            ["value"=>"de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7","answer"=>"de-DE"],
            ["value"=>"en-US,en;q=0.9,de-DE;q=0.8","answer"=>"en-US"],
            ["value"=>"en-US,en,fr,fr-FR","answer"=>"en-US"],
            ["value"=>"ja-JP,zH,fr,fr-FR,en-US","answer"=>"fr-FR"],
            ["value"=>"fr,fr-FR;q=0.9,en-US;q=0.8,en;q=0.7","answer"=>"fr-FR"],
            ["value"=>"fr,fr-CA;q=0.9,en-US;q=0.8,en;q=0.7","answer"=>"fr-FR"],
            ["value"=>"es-ES,es;q=0.9,en-US;q=0.8,en;q=0.7","answer"=>"es-ES"],
            ["value"=>"en-GB;q=0.5, en-US;q=0.8, en;q=0.3","answer"=>"en-US"],
            ["value"=>"ja-JP;q=0.5, en;q=0.3, en-US;q=0.8","answer"=>"en-US"],
            ["value"=>"*","answer"=>"en-US"],
            ["value"=>"","answer"=>"en-US"]
        ];
        foreach($sampleAcceptedLanguageValues as $sampleAcceptedLanguageValue) {
            $clientAcceptedLanguageArray = parseAcceptLanguageFieldValue($sampleAcceptedLanguageValue["value"]);
            $chosenLanguage = selectSupportedLanguage($clientAcceptedLanguageArray);
            self::assertNotNull($chosenLanguage);
            echo 'Content-Language: ' . $chosenLanguage . PHP_EOL;
            $this->assertEquals($sampleAcceptedLanguageValue["answer"], $chosenLanguage, $sampleAcceptedLanguageValue . ' is not parsed as expected.');
        }
    }
}
