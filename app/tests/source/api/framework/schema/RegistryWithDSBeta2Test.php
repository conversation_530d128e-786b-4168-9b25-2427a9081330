<?php

namespace tests\source\api\framework\schema;

require_once ( __DIR__ . '/../APITestHelper.cls');
class RegistryWithDSBeta2Test extends \unitTest\core\UnitTestBaseContext
{
    /**
     * @var string $owner the base name of the owner for created sandbox
     */
    private static string $owner_base = "php_unit";

    /**
     * @var string $owner the name of the owner for created sandbox
     */
    private static string $owner;

    /**
     * @var string $owner2 the name of an additional owner for created sandbox
     */
    private static string $owner2;

    /**
     * @var string $sandbox the name of sandbox to use in tests
     */
    private static string $sandbox;

    private static string $dummyObjectName = 'dummy/dummyclass';

    private static string $dsOverrideSandbox;

    /**
     * @var \RawStore[] $collections - all collection that were created
     */
    private static array $collections = [];

    public static function setUpBeforeClass() : void
    {
        parent::setUpBeforeClass();
        print_r("\n" . '===================== [' . __CLASS__ . ' START] =====================' . "\n\n");
        self::$sandbox = uniqid(self::$owner_base, true);
        self::$dsOverrideSandbox = 'ds_' . self::$sandbox;
        self::$owner = "ia-ds-" . self::$owner_base;
        self::$owner2 = "ia-ds-x-" . self::$owner_base;
        print "==== using sandbox " . self::$sandbox . " =====\n";
        self::createAllDS();
    }

    public static function tearDownAfterClass(): void
    {
        self::deleteDS();

        print_r("\n" . '===================== [' . __CLASS__ . ' END] =====================' . "\n\n");
        parent::tearDownAfterClass();
    }

    /**
     * @param string $collectionName
     * @param array $records
     *
     * @return void
     * @throws \StorageException
     */
    private static function insertMongoRecords(string $collectionName, array $records) : void
    {
        $options = self::getStorageOptions();
        $store = \RSFactory::get($collectionName, $options);
        foreach ( $records as $name => $data ) {
            $record = json_decode($data, true);
            $record[\MongoDBRawStoreHandler::MONGODB_ID] = $name;
            $store->insertOne($record);
        }
        self::$collections[$collectionName] = $store;
    }

    protected function setUp() : void
    {
        \RegistryLoader::reset();
        parent::setUp();
    }
    /**
     * This method is called after each test.
     */
    protected function tearDown() : void
    {
        \RegistryLoader::reset();
        parent::tearDown();
    }

    private static function createAllDS()
    {
        $domains = [
            self::$owner                  => '{"enabled" : true}',
            self::$owner2                 => '{"enabled" : true}',
        ];

        // domains
        $domainsCollectionName = self::$sandbox . \RegistryMongoStorage::DOMAINS_COLLECTION_NAME;
        self::insertMongoRecords($domainsCollectionName, $domains);

        self::createDS();
        self::createDSForOtherOwners();
        self::createDSOverride();
    }

    /**
     * @throws \StorageException
     */
    private static function createDS()
    {
        $registries = [
            "v999-test-beta2" => '{
               "dummy" : {
                  "objects" : {
                    "dummyclass" : {
                      "revision" : "s4",
                      "hash" : "0",
                      "type" : "rootObject",
                      "uiMetadataHash" : "0"
                    }
                  }
               }
            }',
            "v5-test-beta2i" => '
            {
              "x": {
                "objects": {
                  "xclass": {
                    "revision": "s4",
                    "hash": "0",
                    "type": "rootObject",
                    "uiMetadataHash": "0",
                    "runtimeOwner": "' . self::$owner . '"
                  }
                },
                "services": {
                  "x-service": {
                    "x-a": {
                      "revision": "s1",
                      "hash": "0",
                      "type": "functionService",
                      "runtimeOwner": "' . self::$owner . '"
                    },
                    "x-b": {
                      "revision": "s1",
                      "hash": "0",
                      "type": "functionService",
                      "runtimeOwner": "' . self::$owner . '"
                    }
                  }
                },
                "systemViews": {
                  "xclass": {
                    "getAll": {
                      "revision": "s1",
                      "hash": "0"
                    }
                  }
                }
              }
            }',
            "v5-test-beta2" => '
            {
              "dummy" : {
                "objects" : {
                    "dummyclass" : {
                      "revision" : "s4",
                      "hash" : "0",
                      "type" : "rootObject",
                      "uiMetadataHash" : "0",
                      "runtimeOwner" : "' . self::$owner . '"
                    }
                },
                "workflows" : {
                    "dummyclass" : {
                      "revision" : "s1",
                      "hash" : "0",
                      "runtimeOwner" : "' . self::$owner . '"
                    }
                },
                "services" : {
                    "dummy-service" : {
                        "dummy-a" : {
                          "revision" : "s1",
                          "hash" : "0",
                          "type" : "functionService",
                          "runtimeOwner" : "' . self::$owner . '"
                        },
                        "dummy-b" : {
                          "revision" : "s1",
                          "hash" : "0",
                          "type" : "functionService",
                          "runtimeOwner" : "' . self::$owner . '"
                        }
                    }
                },
                "systemViews": {
                    "dummyclass" : {
                        "getAll": {
                            "revision": "s1",
                            "hash": "0"
                        }
                    },
                    "vendor" : {
                        "badEntry": {
                            "revision": "s1",
                            "hash": "0"
                        }
                    }
                }
              }
            }'];

        $schemas = [
            "dummy.dummyclass.s4" => '{
              "fields" : {
                "description" : {
                  "mappedTo" : "description",
                  "type" : "string"
                },
                "id" : {
                  "mappedTo" : "id",
                  "mutable" : false,
                  "required" : true,
                  "type" : "string"
                },
                "key" : {
                  "mappedTo" : "key",
                  "readOnly" : true,
                  "type" : "string"
                },
                "name" : {
                  "mappedTo" : "name",
                  "type" : "string"
                }
              },
              "httpMethods" : "OPTIONS,GET,POST,DELETE,PATCH",
              "mappedTo" : "class"
            }',
            "dummy.dummy-service.dummy-a.s1" => '{
              "httpMethods" : "OPTIONS,POST",
              "request" : {
                "fields" : {
                  "description" : {
                    "mappedTo" : "inputDtoField2",
                    "type" : "string"
                  },
                  "externalId" : {
                    "mappedTo" : "inputDtoField1",
                    "type" : "string"
                  }
                },
                "mappedTo" : "__custom__"
              },
              "response" : {
                "fields" : {
                  "resultDescription" : {
                    "mappedTo" : "outputDtoField2",
                    "type" : "string"
                  },
                  "resultId" : {
                    "mappedTo" : "outputDtoField1",
                    "type" : "string"
                  }
                },
                "mappedTo" : "__custom__"
              }
            }',
            "dummy.dummy-service.dummy-b.s1" => '{
              "httpMethods" : "OPTIONS,POST",
              "request" : {},
              "response" : {}
            }',
        ];

        $histories = [
            "dummy.dummyclass" => '{
                  "ia::definition" : {
                    "methodPermissions" : {
                      "DELETE" : [ "co/lists/class/delete" ],
                      "GET" : [ "co/lists/class/view" ],
                      "OPTIONS" : [ "ALL" ],
                      "PATCH" : [ "co/lists/class/edit" ],
                      "POST" : [ "co/lists/class/create" ]
                    }
                  },
                  "s4" : {
                    "hash" : "0",
                    "runtimeOwner" : "' . self::$owner . '",
                    "type" : "rootObject",
                    "uiMetadataHash" : "0",
                    "workflows" : { 
                        "hash": "0", 
                        "revision": "s1" 
                    }
                  }
                }',
            "dummy.dummy-service.dummy-a" => '{
                  "ia::definition" : {
                    "id" : "dummy.sample-service.function-b",
                    "methodPermissions" : {
                      "POST" : [ "co/lists/class/create" ]
                    }
                  },
                  "s1" : {
                    "hash" : "0",
                    "id" : "",
                    "runtimeOwner" : "' . self::$owner . '",
                    "type" : "functionService"
                  }
                }',
            "dummy.dummy-service.dummy-b" => '{
                  "ia::definition" : {
                    "id" : "dummy.sample-service.function-b",
                    "methodPermissions" : {
                      "POST" : [ "co/lists/class/create" ]
                    }
                  },
                  "s1" : {
                    "hash" : "0",
                    "id" : "",
                    "runtimeOwner" : "' . self::$owner . '",
                    "type" : "functionService"
                  }
                }',
        ];

        $uimetas = [
            "dummy.dummyclass.s4" => '{
                "fields": {
                    "key": {
                        "uiType": "integer",
                        "uiLabel": "Record number"
                    },
                    "id": {
                        "uiType": "text",
                        "uiLabel": "Class ID"
                    },
                    "name": {
                        "uiType": "text",
                        "uiLabel": "Name"
                    },
                    "description": {
                        "uiType": "text",
                        "uiLabel": "Description"
                    }
                }
            }',
        ];

        $systemViews = [
            "dummy.dummyclass.getAll.s1" => '{
              "contexts" : [ "__default" ],
              "description" : "get all",
              "id" : "getAll",
              "key" : "dummy/dummyclass::getAll",
              "name" : "All",
              "object" : "dummy/dummyclass",
              "query" : {
                 "object" : "dummy/dummyclass",
                 "fields" : [ "id", "name" ],
                 "orderBy" : [ {"id" : "asc"} ]
              }  
            }',
        ];

        $workflows = [
            "workflows.dummy.dummyclass.s1" => '{
              "actions" : {
                "activate" : {
                  "request" : {
                    "fields" : {
                      "key" : {
                        "mappedTo" : "key",
                        "required" : true,
                        "type" : "string"
                      },
                      "notes" : {
                        "mappedTo" : "description",
                        "type" : "string"
                      }
                    },
                    "mappedTo" : "class"
                  },
                  "response" : {
                    "fields" : {
                      "id" : {
                        "mappedTo" : "id",
                        "readOnly" : true,
                        "type" : "string"
                      },
                      "key" : {
                        "mappedTo" : "key",
                        "readOnly" : true,
                        "type" : "string"
                      },
                      "state" : {
                        "enum" : [ "activated" ],
                        "mappedTo" : "status",
                        "mappedToValues" : [ "active" ],
                        "readOnly" : true,
                        "type" : "string"
                      }
                    },
                    "mappedTo" : "class"
                  }
                },
                "deactivate" : {
                  "request" : {
                    "fields" : {
                      "key" : {
                        "mappedTo" : "key",
                        "required" : true,
                        "type" : "string"
                      },
                      "notes" : {
                        "mappedTo" : "description",
                        "type" : "string"
                      }
                    },
                    "mappedTo" : "class"
                  },
                  "response" : {
                    "fields" : {
                      "id" : {
                        "mappedTo" : "id",
                        "readOnly" : true,
                        "type" : "string"
                      },
                      "key" : {
                        "mappedTo" : "key",
                        "readOnly" : true,
                        "type" : "string"
                      },
                      "state" : {
                        "enum" : [ "deactivated" ],
                        "mappedTo" : "status",
                        "mappedToValues" : [ "inactive" ],
                        "readOnly" : true,
                        "type" : "string"
                      }
                    },
                    "mappedTo" : "class"
                  }
                }
              }
            }',
        ];

        $workflowsMetadata = [
            "dummy.dummyclass.s1" => '{
                "transitions" : {
                    "activate" : {
                      "from" : [ "deactivated" ],
                      "to" : [ "activated" ],
                      "type" : "stateTransition"
                    },
                    "deactivate" : {
                      "from" : [ "activated" ],
                      "to" : [ "deactivated" ],
                      "type" : "stateTransition"
                    }
                }
            }',
        ];

        // registries
        $registryCollectionName = self::$sandbox . \RegistryMongoStorage::REGISTRY_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($registryCollectionName, $registries);

        // schemas
        $schemasCollectionName = self::$sandbox . \RegistryMongoStorage::SCHEMA_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($schemasCollectionName, $schemas);

        // histories
        $historiesCollectionName = self::$sandbox . \RegistryMongoStorage::HISTORY_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($historiesCollectionName, $histories);

        // uimeta
        $uimetasCollectionName = self::$sandbox . \RegistryMongoStorage::UIMETA_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($uimetasCollectionName, $uimetas);

        // system view
        $systemViewsCollectionName = self::$sandbox . \RegistryMongoStorage::SYSTEM_VIEWS_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($systemViewsCollectionName, $systemViews);

        // workflows
        $workflowsCollectionName = self::$sandbox . \RegistryMongoStorage::WORKFLOWS_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($workflowsCollectionName, $workflows);

        // workflows metadata
        $workflowsMetadataCollectionName = self::$sandbox . \RegistryMongoStorage::WORKFLOWS_METADATA_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($workflowsMetadataCollectionName, $workflowsMetadata);
    }

    private static function createDSForOtherOwners()
    {
        $owner2Registry = [
            "v5-test-beta2" => '{
                "owner" : {
                  "objects" : {
                    "owner2-object": {
                        "revision": "s1",
                        "hash": null,
                        "type": "rootObject",
                        "runtimeOwner" : "' . self::$owner2 . '"
                    }
                  },
                  "services" : {
                    "owner2-service": {
                        "revision": "s1",
                        "hash": null,
                        "type": "coreService",
                        "runtimeOwner" : "' . self::$owner2 . '"
                    }
                  }
                }
           }',
        ];

        $registryCollectionName = self::$sandbox . \RegistryMongoStorage::REGISTRY_COLLECTION_NAME . self::$owner2;
        self::insertMongoRecords($registryCollectionName, $owner2Registry);

        $owner2Histories = [
            "owner.owner2-object" => '{
                  "ia::definition" : {
                    "methodPermissions" : {
                      "DELETE" : [ "co/lists/class/delete" ],
                      "GET" : [ "co/lists/class/view" ],
                      "OPTIONS" : [ "ALL" ],
                      "PATCH" : [ "co/lists/class/edit" ],
                      "POST" : [ "co/lists/class/create" ]
                    }
                  },
                  "s1" : {
                    "hash" : "0",
                    "runtimeOwner" : "' . self::$owner2 . '",
                    "type" : "rootObject",
                    "uiMetadataHash" : "0"
                  }
                }',
            "owner.owner2-service" => '{
                  "ia::definition" : {
                    "methodPermissions" : {
                      "POST" : [ "rest_api" ]
                    }
                  },
                  "s1" : {
                    "hash" : "0",
                    "id" : "",
                    "runtimeOwner" : "' . self::$owner2 . '",
                    "type" : "coreService"
                  }
                }',
        ];

        $historiesCollectionName = self::$sandbox . \RegistryMongoStorage::HISTORY_COLLECTION_NAME . self::$owner2;
        self::insertMongoRecords($historiesCollectionName, $owner2Histories);

        $owner2Schemas = [
            "owner.owner2-object.s1" => '{
              "fields" : {
                "description" : {
                  "mappedTo" : "description",
                  "type" : "string"
                },
                "id" : {
                  "mappedTo" : "id",
                  "mutable" : false,
                  "required" : true,
                  "type" : "string"
                },
                "key" : {
                  "mappedTo" : "key",
                  "readOnly" : true,
                  "type" : "string"
                },
                "name" : {
                  "mappedTo" : "name",
                  "type" : "string"
                }
              },
              "httpMethods" : "OPTIONS,GET,POST,DELETE,PATCH",
              "mappedTo" : "class"
            }',
            "owner.owner2-service.s1" => '{
              "httpMethods" : "OPTIONS,GET",
              "request" : {},
              "response" : {
                "fields" : {
                  "status" : {
                    "mappedTo" : "status",
                    "type" : "string"
                  }
                },
                "mappedTo" : "__custom__"
              }
            }',
        ];

        $schemasCollectionName = self::$sandbox . \RegistryMongoStorage::SCHEMA_COLLECTION_NAME . self::$owner2;
        self::insertMongoRecords($schemasCollectionName, $owner2Schemas);
    }

    private static function createDSOverride()
    {
        $overrideRegistry = [
            "v5-test-beta2" => '{
              "dummy" : {
                  "objects" : {
                    "dummyclass" : {
                      "revision" : "s4",
                      "hash" : "0",
                      "type" : "rootObject",
                      "uiMetadataHash" : "0",
                      "runtimeOwner" : "' . self::$owner . '"
                    },
                    "anotherclass" : {
                      "revision" : "s1",
                      "hash" : "0",
                      "type" : "rootObject",
                      "uiMetadataHash" : "0",
                      "runtimeOwner" : "' . self::$owner . '"
                    }
                  },
                "services" : {
                    "dummy-service" : {
                        "dummy-a" : {
                          "revision" : "s1",
                          "hash" : "0",
                          "type" : "functionService",
                          "runtimeOwner" : "' . self::$owner . '"
                        },
                        "dummy-b" : {
                          "revision" : "s1",
                          "hash" : "0",
                          "type" : "functionService",
                          "runtimeOwner" : "' . self::$owner . '"
                        },
                        "dummy-c" : {
                          "revision" : "s1",
                          "hash" : "0",
                          "type" : "functionService",
                          "runtimeOwner" : "' . self::$owner . '"
                        }
                    }
                },
                "systemViews": {
                    "dummyclass" : {
                        "getAll": {
                            "revision": "s1",
                            "hash": "0"
                        }
                    },
                    "anotherclass" : {
                        "getEntry": {
                            "revision": "s1",
                            "hash": "0"
                        }
                    }
                }
              }
            }'];

        $registryCollectionName = self::$dsOverrideSandbox . \RegistryMongoStorage::REGISTRY_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($registryCollectionName, $overrideRegistry);

        $historyOverrides = [
            "dummy.dummyclass" => '{
                  "ia::definition" : {
                    "methodPermissions" : {
                      "DELETE" : [ "co/lists/class/delete" ],
                      "GET" : [ "co/lists/class/view" ],
                      "OPTIONS" : [ "ALL" ],
                      "PATCH" : [ "co/lists/class/edit" ],
                      "POST" : [ "co/lists/class/create" ]
                    }
                  },
                  "s4" : {
                    "hash" : "0",
                    "runtimeOwner" : "' . self::$owner . '",
                    "type" : "rootObject",
                    "uiMetadataHash" : "0"
                  }
                }',
            "dummy.anotherclass" => '{
                  "ia::definition" : {
                    "methodPermissions" : {
                      "DELETE" : [ "co/lists/class/delete" ],
                      "GET" : [ "co/lists/class/view" ],
                      "OPTIONS" : [ "ALL" ],
                      "PATCH" : [ "co/lists/class/edit" ],
                      "POST" : [ "co/lists/class/create" ]
                    }
                  },
                  "s1" : {
                    "hash" : "0",
                    "runtimeOwner" : "' . self::$owner . '",
                    "type" : "rootObject",
                    "uiMetadataHash" : "0"
                  }
                }',
            "dummy.dummy-service.dummy-a" => '{
                  "ia::definition" : {
                    "id" : "dummy.sample-service.function-b",
                    "methodPermissions" : {
                      "POST" : [ "co/lists/class/create" ]
                    }
                  },
                  "s1" : {
                    "hash" : "0",
                    "id" : "",
                    "runtimeOwner" : "' . self::$owner . '",
                    "type" : "functionService"
                  }
                }',
            "dummy.dummy-service.dummy-b" => '{
                  "ia::definition" : {
                    "id" : "dummy.sample-service.function-b",
                    "methodPermissions" : {
                      "POST" : [ "co/lists/class/create" ]
                    }
                  },
                  "s1" : {
                    "hash" : "0",
                    "id" : "",
                    "runtimeOwner" : "' . self::$owner . '",
                    "type" : "functionService"
                  }
                }',
            "dummy.dummy-service.dummy-c" => '{
                  "ia::definition" : {
                    "id" : "dummy.sample-service.function-c",
                    "methodPermissions" : {
                      "POST" : [ "co/lists/class/create" ]
                    }
                  },
                  "s1" : {
                    "hash" : "0",
                    "id" : "",
                    "runtimeOwner" : "' . self::$owner . '",
                    "type" : "functionService"
                  }
                }',
        ];

        $historiesCollectionName = self::$dsOverrideSandbox . \RegistryMongoStorage::HISTORY_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($historiesCollectionName, $historyOverrides);

        $schemaOverrides = [
            "dummy.dummyclass.s4" => '{
              "fields" : {
                "description" : {
                  "mappedTo" : "description",
                  "type" : "string"
                },
                "id" : {
                  "mappedTo" : "id",
                  "mutable" : false,
                  "required" : true,
                  "type" : "string"
                },
                "key" : {
                  "mappedTo" : "key",
                  "readOnly" : true,
                  "type" : "string"
                },
                "name" : {
                  "mappedTo" : "name",
                  "type" : "string"
                },
                "status" : {
                  "mappedTo" : "name",
                  "type" : "string"
                }
              },
              "httpMethods" : "OPTIONS,GET,POST,DELETE,PATCH",
              "mappedTo" : "class"
            }',
            "dummy.anotherclass.s1" => '{
              "fields" : {
                "description" : {
                  "mappedTo" : "description",
                  "type" : "string"
                },
                "id" : {
                  "mappedTo" : "id",
                  "mutable" : false,
                  "required" : true,
                  "type" : "string"
                },
                "key" : {
                  "mappedTo" : "key",
                  "readOnly" : true,
                  "type" : "string"
                },
                "name" : {
                  "mappedTo" : "name",
                  "type" : "string"
                }
              },
              "httpMethods" : "OPTIONS,GET,POST,DELETE,PATCH",
              "mappedTo" : "class"
            }',
            "dummy.dummy-service.dummy-a.s1" => '{
              "httpMethods" : "OPTIONS,POST",
              "request" : {
                "fields" : {
                  "description" : {
                    "mappedTo" : "inputDtoField2",
                    "type" : "string"
                  },
                  "externalId" : {
                    "mappedTo" : "inputDtoField1",
                    "type" : "string"
                  }
                },
                "mappedTo" : "__custom__"
              },
              "response" : {
                "fields" : {
                  "resultDescription" : {
                    "mappedTo" : "outputDtoField2",
                    "type" : "string"
                  },
                  "resultId" : {
                    "mappedTo" : "outputDtoField1",
                    "type" : "string"
                  }
                },
                "mappedTo" : "__custom__"
              }
            }',
            "dummy.dummy-service.dummy-b.s1" => '{
              "httpMethods" : "OPTIONS,POST",
              "request" : {},
              "response" : {}
            }',
            "dummy.dummy-service.dummy-c.s1" => '{
              "httpMethods" : "OPTIONS,POST",
              "request" : {},
              "response" : {}
            }',
        ];

        $schemasCollectionName = self::$dsOverrideSandbox . \RegistryMongoStorage::SCHEMA_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($schemasCollectionName, $schemaOverrides);

        $uimetaOverrides = [
            "dummy.dummyclass.s4" => '{
                "fields": {
                    "key": {
                        "uiType": "integer",
                        "uiLabel": "Record number"
                    },
                    "id": {
                        "uiType": "text",
                        "uiLabel": "Class ID"
                    },
                    "name": {
                        "uiType": "text",
                        "uiLabel": "Name"
                    },
                    "status": {
                        "uiType": "text",
                        "uiLabel": "Description"
                    }
                }
            }',
            "dummy.anotherclass.s1" => '{
                "fields": {
                    "key": {
                        "uiType": "integer",
                        "uiLabel": "Record number"
                    },
                    "id": {
                        "uiType": "text",
                        "uiLabel": "Class ID"
                    },
                    "name": {
                        "uiType": "text",
                        "uiLabel": "Name"
                    }
                }
            }',
        ];

        $uimetasCollectionName = self::$dsOverrideSandbox . \RegistryMongoStorage::UIMETA_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($uimetasCollectionName, $uimetaOverrides);

        $systemViewsOverrides = [
            "dummy.dummyclass.getAll.s1" => '{
              "contexts" : [ "__default" ],
              "description" : "get all",
              "id" : "getAll",
              "key" : "dummy/dummyclass::getAll",
              "name" : "All",
              "object" : "dummy/dummyclass",
              "query" : {
                 "object" : "dummy/dummyclass",
                 "fields" : [ "id", "name", "status" ],
                 "orderBy" : [ {"id" : "asc"} ]
              }  
            }',
            "dummy.anotherclass.getEntry.s1" => '{
              "contexts" : [ "__default" ],
              "description" : "get some",
              "id" : "getEntry",
              "key" : "dummy/anotherclass::getEntry",
              "name" : "All",
              "object" : "dummy/anotherclass",
              "query" : {
                 "object" : "dummy/anotherclass",
                 "fields" : [ "id", "name" ],
                 "filters" : [{"$eq" : {"key" : 5}}],
                 "orderBy" : [ {"name" : "asc"} ]
              }  
            }',
        ];


        $systemViewsCollectionName = self::$dsOverrideSandbox . \RegistryMongoStorage::SYSTEM_VIEWS_COLLECTION_NAME . self::$owner;
        self::insertMongoRecords($systemViewsCollectionName, $systemViewsOverrides);
    }

    private static function deleteDS()
    {
        foreach (self::$collections as $store) {
            $store->deleteAll();
        }
    }

    /**
     * Only execute explicitly to cleanup previous results
     * @return void
     */
    public function testDeleteCollections()
    {
        if (! \APITestHelper::isInSuite('::' . __FUNCTION__) ) {
            $options = self::getStorageOptions();
            // replace the numbers with what to delete and add as many as needed
            $suffixes = [
                "65d7ebea5ea283.52552499",
            ];
            foreach ($suffixes as $suffix) {

                $prefix = self::$owner_base . $suffix;
                $prefixForOverride = 'ds_' . $prefix;
                print "cleaning collections with $prefix and $prefixForOverride\n";
                $collections = [
                    $prefix . \RegistryMongoStorage::DOMAINS_COLLECTION_NAME,
                    $prefix . \RegistryMongoStorage::REGISTRY_COLLECTION_NAME . self::$owner,
                    $prefix . \RegistryMongoStorage::SCHEMA_COLLECTION_NAME . self::$owner,
                    $prefix . \RegistryMongoStorage::HISTORY_COLLECTION_NAME . self::$owner,
                    $prefix . \RegistryMongoStorage::UIMETA_COLLECTION_NAME . self::$owner,
                    $prefix . \RegistryMongoStorage::SYSTEM_VIEWS_COLLECTION_NAME . self::$owner,
                    $prefix . \RegistryMongoStorage::WORKFLOWS_COLLECTION_NAME . self::$owner,
                    $prefix . \RegistryMongoStorage::WORKFLOWS_METADATA_COLLECTION_NAME . self::$owner,
                    $prefix . \RegistryMongoStorage::REGISTRY_COLLECTION_NAME . self::$owner2,
                    $prefix . \RegistryMongoStorage::SCHEMA_COLLECTION_NAME . self::$owner2,
                    $prefix . \RegistryMongoStorage::HISTORY_COLLECTION_NAME . self::$owner2,
                    $prefixForOverride . \RegistryMongoStorage::REGISTRY_COLLECTION_NAME . self::$owner,
                    $prefixForOverride . \RegistryMongoStorage::SCHEMA_COLLECTION_NAME . self::$owner,
                    $prefixForOverride . \RegistryMongoStorage::HISTORY_COLLECTION_NAME . self::$owner,
                    $prefixForOverride . \RegistryMongoStorage::UIMETA_COLLECTION_NAME . self::$owner,
                    $prefixForOverride . \RegistryMongoStorage::SYSTEM_VIEWS_COLLECTION_NAME . self::$owner,
                    $prefixForOverride . \RegistryMongoStorage::WORKFLOWS_COLLECTION_NAME . self::$owner,
                    $prefixForOverride . \RegistryMongoStorage::WORKFLOWS_METADATA_COLLECTION_NAME . self::$owner,
                ];
                foreach ($collections as $collection) {
                    // print "cleaning collection $collection\n";
                    $store = \RSFactory::get($collection, $options);
                    $store->deleteAll();
                }
            }
        }
        $this->assertNull(null);
    }

    /**
     * @return void
     */
    public function testMongoDBRegistries()
    {
        print "==== " . __METHOD__ . " START =====\n";
        $registryStore = new \RegistryMongoStorage(self::$sandbox);
        $domains = $registryStore->getDomains();
        $this->assertNotEmpty($domains);
        foreach ($domains as $domain) {
            $coll = self::$sandbox . \RegistryMongoStorage::REGISTRY_COLLECTION_NAME . $domain;
            $res = $registryStore->getCollection($coll);
            $this->assertNotEmpty($res, "$domain <> $coll");
            $ids = array_column($res, '_id');
            $this->assertNotEmpty($ids);
            $id = $ids[0]; // there is one
            $registries = $registryStore->getRegistriesForVersion($id);
            // print_r($reg);
            $this->assertArrayHasKey($domain, $registries);
        }
        print "==== " . __METHOD__ . " END =====\n";
    }

    /**
     * @return void
     * @throws \APISchemaParsingException
     */
    public function testAvailableVersions()
    {
        print "==== " . __METHOD__ . " START =====\n";
        $res1 = \RegistryLoader::getAvailableVersions();

        $this->includeDS();
        $res2 = \RegistryLoader::getAvailableVersions();
        // should be exactly the same number
        // v999-test is hidden because there is no such PHP version
        $this->assertEquals(count($res1), count($res2));
        print "==== " . __METHOD__ . " END =====\n";
    }

    /**
     * @return array
     * @throws \APIException
     */
    public function testRegistry() : array
    {
        print "==== " . __METHOD__ . " START =====\n";
        $regDiff = $this->validateRegistryVersion('5-test-beta2', self::$dummyObjectName, 5,
                                                  // dummy added from owner and onwer2-xxxx are from owner2
                                                  [self::$dummyObjectName, "owner/owner2-object", "owner/owner2-service"]);
        print "==== " . __METHOD__ . " END =====\n";
        return $regDiff ?? [];
    }

    /**
     * @dataProvider _testNegativeOverride
     *
     * @param int    $recordCount
     * @param string $errorMsg
     *
     * @return void
     * @throws \APIException
     * @throws \APIInternalException
     * @throws \StorageException
     */
    public function testNegativeOverride(int $recordCount, string $errorMsg)
    {
        print "==== " . __METHOD__ . " For $errorMsg ===== \n";
        $version = '5-test-beta2';
        \RegistryLoader::reset();
        $this->includeDS();
        // invalid sandbox
        $sandboxOverride = self::$dsOverrideSandbox . "---xxxxx";
        $store = null;
        try {
            if ($recordCount !== -1) {
                $registryCollectionName = $sandboxOverride . \RegistryMongoStorage::REGISTRY_COLLECTION_NAME . self::$owner;
                $options = self::getStorageOptions();
                $store = \RSFactory::get($registryCollectionName, $options);
                $id = "x";
                $record = [
                    \MongoDBRawStoreHandler::MONGODB_ID => $id,
                    []
                ];
                $store->insertOne($record);
                if ($recordCount === 0) {
                    $store->deleteById($id);
                    $this->assertEquals(0, $store->count());
                } else {
                    $this->assertGreaterThanOrEqual(1, $store->count());
                }
            } // else test that sandbox does not exist

            \RegistryLoader::setDSOverrides($sandboxOverride, self::$owner);
            try {
                \RegistryLoader::getInstance($version);
                $this->fail("Should not reach here");
            } catch (\APISchemaParsingException $e) {
                $this->assertStringContainsString($errorMsg, $e->getMessage());
            }
            // and as model
            $url = "v0-beta2/services/core/model?version=$version&domainOverride=" . self::$owner . "&sandboxOverride=$sandboxOverride";
            try {
                $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url, \APIConstants::API_HTTP_METHOD_GET,
                                                                       null, [], false);
                // if exception is caught
                $this->assertStringContainsString($errorMsg, json_encode($res));
            } catch (\APIException $e) {
                $this->assertStringContainsString($errorMsg, $e->getMessage());
            }
        } finally {
            if ($store) {
                // will drop the collection
                $store->deleteAll();
            }
        }
    }

    protected function _testNegativeOverride() : array
    {
        return [
            [0, "is empty"],
            [-1, "does not have registry for domain"],
            [1, "does not have registry for domain"],
        ];
    }

    /**
     * @throws \APIException
     */
    public function testRegistryWithOverride()
    {
        print "==== " . __METHOD__ . " START =====\n";
        $version = '5-test-beta2';
        $this->includeDS();
        $regLoader = \RegistryLoader::getInstance($version);
        $regWithDS = $regLoader->getRegistry();
        // without override
        $this->assertArrayNotHasKey("dummy/anotherclass", $regWithDS);
        $this->assertArrayNotHasKey("dummy/dummy-service/dummy-c", $regWithDS);
        $this->assertArrayNotHasKey("dummy/anotherclass", $regWithDS[\RegistryLoader::SYSTEM_VIEWS_KEY]);
        $handler = $regLoader->getHandler("dummy/dummyclass");
        $schema = $handler->getSchemaDefinition();
        $this->assertArrayNotHasKey("status", $schema[\APIConstants::API_FIELDS_KEY]);
        $view = $regLoader->getSystemViewsForKey("dummy/dummyclass::getAll");
        $this->assertEquals(["id", "name"], $view[\APIConstants::API_QUERY][\APIConstants::API_FIELDS_KEY] ?? []);
        $view = $regLoader->getSystemViewsForKey("dummy/anotherclass::getEntry");
        $this->assertEmpty($view);
        $uimeta = $regLoader->getUIMetadata("dummy/dummyclass");
        $this->assertArrayNotHasKey("status", $uimeta[\APIConstants::API_FIELDS_KEY]);
        $uimeta = $regLoader->getUIMetadata("dummy/anotherclass");
        $this->assertEmpty($uimeta);

        $this->includeDS();
        \RegistryLoader::setDSOverrides(self::$dsOverrideSandbox, self::$owner);
        $regLoader = \RegistryLoader::getInstance($version);
        $regWithDS = $regLoader->getRegistry();
        // with override - see createDSOverride()
        $this->assertArrayHasKey("objects/dummy/anotherclass", $regWithDS);
        $this->assertArrayHasKey("services/dummy/dummy-service/dummy-c", $regWithDS);
        $this->assertArrayHasKey("objects/dummy/anotherclass", $regWithDS[\RegistryLoader::SYSTEM_VIEWS_KEY]);
        $handler = $regLoader->getHandler("dummy/dummyclass");
        $schema = $handler->getSchemaDefinition();
        $this->assertArrayHasKey("status", $schema[\APIConstants::API_FIELDS_KEY]);
        $view = $regLoader->getSystemViewsForKey("dummy/dummyclass::getAll");
        $this->assertEquals(["id", "name", "status"], $view[\APIConstants::API_QUERY][\APIConstants::API_FIELDS_KEY] ?? []);
        $view = $regLoader->getSystemViewsForKey("dummy/anotherclass::getEntry");
        $this->assertNotEmpty($view);
        $uimeta = $regLoader->getUIMetadata("dummy/dummyclass");
        $this->assertArrayHasKey("status", $uimeta[\APIConstants::API_FIELDS_KEY]);
        $uimeta = $regLoader->getUIMetadata("dummy/anotherclass");
        $this->assertNotEmpty($uimeta);

        foreach ( ["dummy/anotherclass", "dummy/dummy-service/dummy-c"] as $name) {
            print("\n$name ");
            $handler = $regLoader->getHandler($name);
            $this->assertNotNull($handler);
            // print(json_encode($handler->getSchemaDefinition(), 128));
        }
    }

    /**
     * @depends testRegistry
     * @return array
     * @throws \APIException
     */
    public function testModel(array $dsNames) : array
    {
        print "==== " . __METHOD__ . " START =====\n";
        // no DS in default 5-test-beta2
        $url = 'v0-beta2/services/core/model?version=5-test-beta2';
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET,
                                                               null, [], false);
        $names = array_column($res[\APIConstants::IA_RESULT_KEY]??[], \APIConstants::API_OBJECT_REF_KEY);
        foreach ($dsNames as $dsname) {
            $this->assertNotContains($dsname, $names, json_encode($res));
        }

        // add test DS
        $this->includeDS();
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET,
                                                               null, [], false)[\APIConstants::IA_RESULT_KEY]??[];
        foreach ($res as $item) {
            $this->assertStringNotContainsString('domainOverride=', $item[\APIConstants::API_HREF_FIELD_NAME] ?? null);
            $this->assertStringNotContainsString('sandboxOverride=', $item[\APIConstants::API_HREF_FIELD_NAME] ?? null);
        }

        $names = array_column($res, \APIConstants::API_OBJECT_REF_KEY);
        foreach ($dsNames as $dsname) {
            $this->assertContains($dsname, $names, json_encode($res));
        }

        print "==== " . __METHOD__ . " END =====\n";
        return $dsNames;
    }

    /**
     * @depends testRegistry
     * @throws \APIException
     */
    public function testModelRuntimeOwners(array $dsNames)
    {
        print "==== " . __METHOD__ . " START =====\n";
        // no DS in default 5-test-beta2
        $url = 'v0-beta2/services/core/model?version=5-test-beta2&listDSOwners=true';
        \APITestHelper::executeAndValidateAPIDispatcher(0, 0, $url, \APIConstants::API_HTTP_METHOD_GET);

        // test that DS is in the list of runtime owners
        $url .= "&domainOverride=" . self::$owner . "&sandboxOverride=" . self::$sandbox;
        foreach ($dsNames as $i=>$dsname) {
            if (strpos($dsname, '/dummy/') === false) {
                unset($dsNames[$i]); // remove dummy
            }
        }
        $res = \APITestHelper::executeAndValidateAPIDispatcher(count($dsNames), 0, $url, \APIConstants::API_HTTP_METHOD_GET,
                                                               null, [], false)[\APIConstants::IA_RESULT_KEY]??[];

        print_r($res);
        foreach ($dsNames as $dsname) {
            $this->assertArrayHasKey("/v5-test-beta2/$dsname", $res, json_encode($res, 64));
        }

        $url = str_replace('listDSOwners=true', 'type=workflow', $url);
        $res0 = \APITestHelper::executeAndValidateAPIDispatcher(count($dsNames) + 1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);
        $workflows = array_column($res0[\APIConstants::IA_RESULT_KEY]??[], \APIConstants::API_OBJECT_REF_KEY);
        foreach ($workflows as $workflow) {
            if (strpos($workflow, '/dummy/') === false) {
                $this->assertArrayNotHasKey("/v5-test-beta2/$workflow", $res, json_encode($res, 64));
            } else {
                $this->assertArrayHasKey("/v5-test-beta2/$workflow", $res, json_encode($res, 64));
            }
        }
        print "==== " . __METHOD__ . " END =====\n";
    }

    /**
     * @depends testModel
     *
     * @param array $dsnames
     *
     * @return void
     * @throws \APIException
     */
    public function testModelForName(array $dsnames)
    {
        print "==== " . __METHOD__ . " START =====\n";

        $name = self::$dummyObjectName;
        $this->_testModelForName($name);

        // let's check a function service if what we picked was a simple name
        if (strpos($name, '/') === false) {
            foreach ($dsnames as $dsname) {
                if (strpos($dsname, '/') !== false) {
                    \RegistryLoader::reset(); // reset to correctly fetch non-DS version
                    $this->_testModelForName($dsname);
                    break;
                }
            }
        } // else ok if it was already an FS
        print "==== " . __METHOD__ . " END =====\n";
    }

    /**
     * @depends testModel
     *
     * @param array $dsnames
     *
     * @return void
     * @throws \APIException
     */
    public function testUiMetaIn(array $dsnames) {
        print "==== " . __METHOD__ . " START =====\n";
        // no DS by default in 5-test
        $url = 'v0-beta2/services/core/uimodel?version=5-test-beta2';
        $resNoDS = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET,
                                                               null, [], false);
        $names = array_column($resNoDS[\APIConstants::IA_RESULT_KEY]??[], \APIConstants::API_OBJECT_REF_KEY);
        $dsname = self::$dummyObjectName;
        $this->assertNotContains($dsname, $names, json_encode($resNoDS));
        $countNoDS = $resNoDS[\APIConstants::IA_META_KEY][\APIConstants::API_TOTAL_COUNT_KEY]?? 0;

        print "Testing UIModel with test DS for v0-beta2...\n";
        // there should be exactly 1 extra
        $this->includeDS();
        $resWithDS = \APITestHelper::executeAndValidateAPIDispatcher($countNoDS + 1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);
        $url .= "&name=$dsname";
        print "fetching uimodel for $url with test DS\n";
        $this->includeDS();
        $res1 = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);
        // print (json_encode($res1) . "\n");

        print "==== " . __METHOD__ . " END =====\n";
    }

    /**
     * @return void
     */
    public function testSystemViews()
    {
        print "==== " . __METHOD__ . " START =====\n";
        // Not in regular request
        $viewKey = self::$dummyObjectName . '::getAll';
        $url = 'v5-test-beta2/objects/core/' . \APIConstants::SYSTEM_VIEW_OBJ_NAME . '?name=' . $viewKey;
        // it's only in our sandbox, so SystemViewOrchestrator won't find it even on retry
        \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url, \APIConstants::API_HTTP_METHOD_GET);

        // it's there in the registry with DS and our sandbox
        $this->includeDS();
        $regWithDS = \RegistryLoader::getInstance('5-test-beta2');
        $view = $regWithDS->getSystemViewsForKey($viewKey);
        $this->assertNotEmpty($view);
        print "$viewKey system view: " . json_encode($view) . "\n";

        $viewKeyBad = "vendor::badEntry";
        $view = $regWithDS->getSystemViewsForKey($viewKeyBad);
        $this->assertEmpty($view);

        $this->includeDS();
        // run with Dispatcher
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);
        print "$viewKey system view GET: " . json_encode($res) . "\n";

        // test view service - it should fail with 500 trying to invoke DS (like query test) not with any other error
        $this->includeDS();
        $viewServiceUrl = 'v5-test-beta2/services/core/' . \APIConstants::API_VIEW;
        $payload = json_encode([
                                   "key"      => $viewKey,
                                   "viewType" => "system",
                                   "size"     => 1,
                               ]);
        \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $viewServiceUrl, \APIConstants::API_HTTP_METHOD_POST,
                                                               $payload, [], true, 500);

        print "==== " . __METHOD__ . " END =====\n";
    }

    /**
     * @return void
     */
    public function testWorkflows()
    {
        print "==== " . __METHOD__ . " START =====\n";
        $baseUrl = "v0-beta2/services/core/model?version=5-test-beta2";
        $url = "$baseUrl&type=workflow";
        $obj = 'dummy/dummyclass';
        $action1 = 'activate';
        $action2 = 'deactivate';
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET,
                                                               null, [], false);
        $names = array_column($res[\APIConstants::IA_RESULT_KEY]??[], \APIConstants::API_OBJECT_REF_KEY);
        $this->assertNotContains("workflows/$obj/$action1", $names, json_encode($res));
        $this->assertNotContains("workflows/$obj/$action2", $names, json_encode($res));

        // it's there in the registry with DS and our sandbox
        $this->includeDS();
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET,
                                                               null, [], false);
        $names = array_column($res[\APIConstants::IA_RESULT_KEY]??[], \APIConstants::API_OBJECT_REF_KEY);
        $this->assertContains("workflows/$obj/$action1", $names, json_encode($res));
        $this->assertContains("workflows/$obj/$action2", $names, json_encode($res));

        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, "$url&schema=true", \APIConstants::API_HTTP_METHOD_GET,
                                                               null, [], false);
        $result = $res[\APIConstants::IA_RESULT_KEY]??[];
        foreach ([$action1, $action2] as $action) {
            $this->assertArrayHasKey("workflows/$obj/$action", $result, json_encode($res));
            $this->assertArrayHasKey(\APIConstants::API_FIELDS_KEY, $result["workflows/$obj/$action"][\APIConstants::API_REQUEST_KEY] ?? [], json_encode($result, JSON_UNESCAPED_SLASHES));
            $this->assertArrayHasKey(\APIConstants::API_FIELDS_KEY, $result["workflows/$obj/$action"][\APIConstants::API_RESPONSE_KEY] ?? [], json_encode($result, JSON_UNESCAPED_SLASHES));
        }

        $queryParam = \ModelServiceOrchestrator::WORKFLOWS_METADATA_PARAM . "=$obj";
        print "\n==$baseUrl&$queryParam ==\n";
        $result = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, "$baseUrl&$queryParam", \APIConstants::API_HTTP_METHOD_GET);
        $this->assertArrayHasKey(\APIConstants::WORKFLOW_TRANSITIONS, $result[\APIConstants::IA_RESULT_KEY], json_encode($result, JSON_UNESCAPED_SLASHES));
        // print json_encode($result, JSON_UNESCAPED_SLASHES);
        print "==== " . __METHOD__ . " END =====\n";
    }

    /**
     * @throws \APIException
     */
    public function testModelOverrides()
    {
        print "==== " . __METHOD__ . " START =====\n";
        // positive test
        $url = 'v0-beta2/services/core/model?version=5-test-beta2&domainOverride=' . self::$owner . '&sandboxOverride=' . self::$dsOverrideSandbox;
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET,
                                                               null, [], false);
        $names = array_column($res[\APIConstants::IA_RESULT_KEY]??[], \APIConstants::API_OBJECT_REF_KEY);
        foreach (["objects/dummy/anotherclass", "services/dummy/dummy-service/dummy-c"] as $dsname) {
            $this->assertContains($dsname, $names, json_encode($res, JSON_UNESCAPED_SLASHES));
        }
        $hrefOverrideDomainPart = 'domainOverride=' . self::$owner;
        $hrefOverrideSandboxPart = 'sandboxOverride=' . self::$dsOverrideSandbox;
        foreach ($res[\APIConstants::IA_RESULT_KEY] ?? [] as $item) {
            $this->assertStringContainsString($hrefOverrideDomainPart, $item[\APIConstants::API_HREF_FIELD_NAME] ?? null);
            $this->assertStringContainsString($hrefOverrideSandboxPart, $item[\APIConstants::API_HREF_FIELD_NAME] ?? null);
        }

        // fetch details
        $url .= '&name=dummy/dummyclass';
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);
        $this->assertArrayHasKey("status", $res[\APIConstants::IA_RESULT_KEY][\APIConstants::API_FIELDS_KEY] ?? [], json_encode($res));

        // negative
        $url = 'v0-beta2/services/core/model?version=5-test-beta2&domainOverride=' . self::$owner;
        \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url, \APIConstants::API_HTTP_METHOD_GET);
        $url = 'v0-beta2/services/core/model?version=5-test-beta2&sandboxOverride=' . self::$dsOverrideSandbox;
        \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url, \APIConstants::API_HTTP_METHOD_GET);
        print "==== " . __METHOD__ . " END =====\n";
    }

    public function testOverrideWithDefaultSandbox()
    {
        $sandbox = \DomainServiceUtil::getSandboxName();
        echo "Fetching domains for sandbox $sandbox\n";
        $mongoStorage = new \RegistryMongoStorage($sandbox);
        $domains = $mongoStorage->getDomains();
        if (empty($domains)) {
            print "Skip self-override test: domains are empty for $sandbox\n";
            $this->assertNull(null);
            return;
        }

        // find one with v0-beta2
        $version = '0-beta2';
        $found = false;
        foreach ($domains as $domain) {
            if (!empty($mongoStorage->getRegistryForDomainAndVersion($domain, 'v' . $version))) {
                print "using $sandbox for $domain to test self-override\n";
                $url = 'v0-beta2/services/core/model?domainOverride=' . $domain . '&sandboxOverride=' . $sandbox;
                \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET,
                                                                null, [], false);
                $found = true;
                break;
            }
        }
        if (!$found) {
            print "Skip self-override test: DS v$version no domain found in $sandbox\n";
            $this->assertNull(null);
        }

    }

    /**
     * @throws \APIException
     */
    public function testUIModelOverrides()
    {
        print "==== " . __METHOD__ . " START =====\n";
        // positive test
        $url = 'v0-beta2/services/core/uimodel?version=5-test-beta2&domainOverride=' . self::$owner . '&sandboxOverride=' . self::$dsOverrideSandbox;
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET,
                                                               null, [], false);
        $names = array_column($res[\APIConstants::IA_RESULT_KEY]??[], \APIConstants::API_OBJECT_REF_KEY);
        $this->assertContains("objects/dummy/anotherclass", $names, json_encode($res));
        $hrefOverrideDomainPart = 'domainOverride=' . self::$owner;
        $hrefOverrideSandboxPart = 'sandboxOverride=' . self::$dsOverrideSandbox;
        foreach ($res[\APIConstants::IA_RESULT_KEY] ?? [] as $item) {
            $this->assertStringContainsString($hrefOverrideDomainPart, $item[\APIConstants::API_HREF_FIELD_NAME] ?? null);
            $this->assertStringContainsString($hrefOverrideSandboxPart, $item[\APIConstants::API_HREF_FIELD_NAME] ?? null);
        }

        // fetch details
        $url .= '&name=dummy/dummyclass';
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);
        $this->assertArrayHasKey("status", $res[\APIConstants::IA_RESULT_KEY][\APIConstants::API_FIELDS_KEY] ?? [], json_encode($res));

        // negative
        $url = 'v0-beta2/services/core/uimodel?version=5-test-beta2&domainOverride=' . self::$owner;
        \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url, \APIConstants::API_HTTP_METHOD_GET);
        $url = 'v0-beta2/services/core/uimodel?version=5-test-beta2&sandboxOverride=' . self::$dsOverrideSandbox;
        \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url, \APIConstants::API_HTTP_METHOD_GET);
        print "==== " . __METHOD__ . " END =====\n";
    }

    public function testDSResourceAccessNegative()
    {
        print "==== " . __METHOD__ . " START =====\n";
        $this->includeDS();
        // check model = should be there
        \APITestHelper::executeAndValidateAPIDispatcher(1, 0, "v0-beta2/services/core/model?version=5-test-beta2&name=" . self::$dummyObjectName,
                                                        \APIConstants::API_HTTP_METHOD_GET);
        // but CRUD should fail in PHP because is owned by DS
        $url = 'v5-test-beta2/objects/' . self::$dummyObjectName;
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url, \APIConstants::API_HTTP_METHOD_GET);
        $this->assertStringContainsStringIgnoringCase(
            \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0010, [
                "RESOURCE_NAME" => 'objects/' . self::$dummyObjectName,
                "VERSION" => '5-test-beta2',
            ]), json_encode($res, JSON_UNESCAPED_SLASHES));

        // same for the key-ed element
        $url .= '/1';
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url, \APIConstants::API_HTTP_METHOD_GET);
        $this->assertStringContainsStringIgnoringCase(
            \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0010, [
                "RESOURCE_NAME" => 'objects/' . self::$dummyObjectName,
                "VERSION" => '5-test-beta2',
            ]), json_encode($res, JSON_UNESCAPED_SLASHES));

        // now a service
        $service = 'dummy/dummy-service/dummy-a';
        // check model = should be there
        \APITestHelper::executeAndValidateAPIDispatcher(1, 0, "v0-beta2/services/core/model?version=5-test-beta2&name=$service" ,
                                                        \APIConstants::API_HTTP_METHOD_GET);
        // but fail if invoked in PHP
        $url = "v5-test-beta2/services/$service";
        $payload = '{"description" : "abc"}';
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url, \APIConstants::API_HTTP_METHOD_POST, $payload);
        $this->assertStringContainsStringIgnoringCase(
            \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0008, [
                "RESOURCE" => "services/$service",
                "VERSION" => '5-test-beta2',
            ]), json_encode($res, JSON_UNESCAPED_SLASHES));

        print "==== " . __METHOD__ . " END =====\n";
    }

    public function testDSQuery()
    {
        print "==== " . __METHOD__ . " START =====\n";
        // only a negative test but it should show that it attempted to invoke DS
        $payload = '
        {
            "object": "' . self::$dummyObjectName . '",
            "fields": [
                "xxxx"
            ]
        }';


        $this->includeDS();
        \APITestHelper::executeAndValidateAPIDispatcher(1, 1, 'v5-test-beta2/services/core/query',
                                                               \APIConstants::API_HTTP_METHOD_POST, $payload,
                                                               [],
                                                               true, 500);
        // $this->assertStringContainsStringIgnoringCase(\APIErrorMessages::buildMessage(
        //     \APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0004,
        //     [\APIConstants::API_QUERY, "0", \APIConstants::API_QUERY]), json_encode($res));
        print "==== " . __METHOD__ . " END =====\n";
    }

    public function testUserViewWithDS()
    {
        print "==== " . __METHOD__ . " START =====\n";

        $payload = '{
            "name": "' . self::$dummyObjectName . '-user-view-1",
            "description": "Testing user-view",
            "query": {
                    "object": "' . self::$dummyObjectName . '",
                    "fields": [
                        "xxxx"
                    ]
            }
        }';
        $url = 'v5-test-beta2/objects/core/' . \APIConstants::USER_VIEW_OBJECT_NAME;
        // should not find object without a test sandbox
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url,
                                                               \APIConstants::API_HTTP_METHOD_POST, $payload, []);
        $this->assertStringContainsStringIgnoringCase(\APIErrorMessages::buildMessage(
            \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0010,
            ["RESOURCE_NAME" => self::$dummyObjectName, "VERSION" => '5-test-beta2']), json_encode($res, JSON_UNESCAPED_SLASHES));

        // with test DS should detect wrong field
        $this->includeDS();
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url,
                                                               \APIConstants::API_HTTP_METHOD_POST, $payload);
        $this->assertStringContainsStringIgnoringCase(\APIErrorMessages::buildMessage(
            \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0107,
            ["FIELD" => 'xxxx', "OBJECT" => self::$dummyObjectName, "VERSION" => '5-test-beta2']), json_encode($res, JSON_UNESCAPED_SLASHES));

        $payload = '{
            "name": "' . self::$dummyObjectName . '-user-view-1",
            "description": "Testing user-view for ' . self::$dummyObjectName . '",
            "query": {
                    "object": "' . self::$dummyObjectName . '",
                    "fields": [
                        "key",
                        "description"
                    ]
            }
        }';
        \RegistryLoader::reset();
        // should still fail with default sandbox even with the right fields
        \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url,
                                                               \APIConstants::API_HTTP_METHOD_POST, $payload);
        // should succeed with the test sandbox
        $this->includeDS();
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url,
                                                               \APIConstants::API_HTTP_METHOD_POST, $payload);
        $key = $res[\APIConstants::IA_RESULT_KEY][\APIConstants::API_OBJECT_KEY_FIELD_NAME];

        // reset for a clean test
        $this->includeDS();
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url . "/$key",
                                                               \APIConstants::API_HTTP_METHOD_GET);
        $payloadAsArray = json_decode($payload, true);
        $res = $res[\APIConstants::IA_RESULT_KEY];
        $this->assertEquals($payloadAsArray["name"], $res["name"]);
        $this->assertEquals($payloadAsArray["description"], $res["description"]);
        $this->assertEquals($payloadAsArray["query"], $res["query"]);

        // test view service - it should fail with 500 trying to invoke DS (like query test) not with any other error
        $this->includeDS();
        $viewServiceUrl = 'v5-test-beta2/services/core/' . \APIConstants::API_VIEW;
        $payload = json_encode([
                                   "key"      => $key,
                                   "viewType" => "user",
                                   "size"     => 1,
                               ]);
        \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $viewServiceUrl, \APIConstants::API_HTTP_METHOD_POST,
                                                        $payload, [], true, 500);

        // reset for a clean test
        $this->includeDS();
        \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url . "/$key",
                                                        \APIConstants::API_HTTP_METHOD_DELETE);
        print "==== " . __METHOD__ . " END =====\n";
    }

    /**
     * v5-test-beta2i should be available in PHP to test DS merge
     *
     * @return void
     * @throws \APIException
     */
    public function testRegistryVNi()
    {
        print "==== " . __METHOD__ . " START =====\n";
        $this->validateRegistryVersion('5-test-beta2i', 'x/xclass', 3, []);
        print "==== " . __METHOD__ . " END =====\n";
    }

    public function testDSRegistryFile()
    {
        print "==== " . __METHOD__ . " START =====\n";

        // key is version, value if it should be found
        $versions = [
            "0" => true,
            "1-beta" => true,
            "5-test-beta2i" => false,
            "5-test-beta2" => false,
        ];
        foreach ($versions as $version => $toFind) {
            $this->validateDSFileRead($version, $toFind);
        }
        // add in case file was missing or empty
        $this->assertTrue(true);
        print "==== " . __METHOD__ . " END =====\n";
    }

    /**
     * @param $name
     *
     * @return void
     * @throws \APIException
     */
    private function _testModelForName($name) : void
    {
        print "==== " . __METHOD__ . " for " . $name . " START =====\n";

        $url = 'v0-beta2/services/core/model?version=5-test-beta2&name=' . $name;
        $this->includeDS();
        \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);

        print "==== " . __METHOD__ . " END =====\n";
    }

    /**
     * @param string $version
     * @param bool   $toFind
     *
     * @return void
     */
    private function validateDSFileRead(string $version, bool $toFind) : void
    {
        print "==== " . __METHOD__ . " START =====\n";
        $iaRegistries = [];
        if ( include \RegistryLoader::DS_REGISTRY_FILE_CACHE ) {
            if ( ! empty($iaRegistries) ) {
                $this->assertEquals($toFind, array_key_exists($version, $iaRegistries), "version: $version, toFind: " . ($toFind? "true" : "false"));
            }
        }
        print "==== " . __METHOD__ . " END =====\n";
    }

    /**
     * @param string $version
     * @param string $dsObjectNameWithSysView
     * @param int    $diffCount
     * @param array  $dsSchemasToCheck
     *
     * @return int[]|string[]
     * @throws \APIException
     */
    private function validateRegistryVersion(string $version, string $dsObjectNameWithSysView, int $diffCount, array $dsSchemasToCheck = []) : array
    {
        $regBase = \RegistryLoader::getInstance($version)
                                ->getRegistry();
        $this->assertArrayNotHasKey($dsObjectNameWithSysView, $regBase);
        foreach ( $dsSchemasToCheck as $name) {
            $this->assertArrayNotHasKey($name, $regBase);
        }
        $this->assertArrayNotHasKey($dsObjectNameWithSysView, $regBase[\RegistryLoader::SYSTEM_VIEWS_KEY]);

        $this->includeDS();
        try {
            $regLoader = \RegistryLoader::getInstance($version);
            $regWithDS = $regLoader->getRegistry();
            // print("WITH DS: " . json_encode($regWithDS, 128));
            $regDiff = array_keys(array_diff_assoc($regWithDS, $regBase));
            $this->assertEquals($diffCount, count($regDiff), json_encode($regDiff, 128));
            $this->assertArrayHasKey("objects/$dsObjectNameWithSysView", $regWithDS);
            $this->assertArrayHasKey("objects/$dsObjectNameWithSysView", $regWithDS[\RegistryLoader::SYSTEM_VIEWS_KEY]);
            foreach ( $dsSchemasToCheck as $name) {
                $handler = $regLoader->getHandler($name);
                print("\n$name");
                // print(": " . json_encode($handler->getSchemaDefinition(), 128));
                $this->assertNotNull($handler);
            }
        } finally {
            \RegistryLoader::reset();
        }

        return $regDiff;
    }

    /**
     * @return array|\string[][]
     */
    private static function getStorageOptions() : array
    {
        $options = \RegistryMongoStorage::OPTIONS;
        $options['STORAGE']['SHARD'] = \MongoDBEnvManager::getInstance()
                                                         ->getHostsManager()
                                                         ->getShardHostGlobal();

        return $options;
    }

    /**
     * @return void
     */
    private function includeDS() : void
    {
        \RegistryLoader::reset();
        \RegistryLoader::includeDS(self::$sandbox);
    }
}
