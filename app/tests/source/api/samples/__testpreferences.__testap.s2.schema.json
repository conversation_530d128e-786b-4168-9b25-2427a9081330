{"mappedTo": "apsetup", "fields": {"defaultPaymethod": {"mappedTo": "CREDITLIMITCHECK", "type": "string", "enum": "WARN,DISALLOW,NOCHECK"}}, "groups": {"aging": {"fields": {"period1": {"mappedTo": "AGP01", "type": "string"}, "period2": {"mappedTo": "AGP02", "type": "string"}, "period3": {"mappedTo": "AGP03", "type": "string"}}}, "1099Overrides": {"fields": {"allow": {"mappedTo": "ALLOW_1099_OVERRIDE", "type": "boolean"}, "type": {"mappedTo": "ALLOW_1099_FORM_TYPE_OVERRIDE", "type": "boolean"}}}}, "httpMethods": "GET,PATCH,OPTIONS"}