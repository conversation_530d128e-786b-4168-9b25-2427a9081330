{"mappedTo": "sodocumententry", "fields": {"key": {"mappedTo": "RECORDNO", "type": "string", "readOnly": true}, "id": {"mappedTo": "RECORDNO", "type": "string", "readOnly": true}, "quantity": {"mappedTo": "UIQTY", "type": "string"}, "documentTemplateName": {"mappedTo": "DOCPARID", "type": "string", "mutable": false, "useForDocType": true}, "status": {"mappedTo": "STATUS", "type": "string"}, "unit": {"mappedTo": "UNIT", "type": "string"}, "price": {"mappedTo": "PRICE", "type": "number"}}, "groups": {"audit": {"fields": {"whenCreated": {"mappedTo": "WHENCREATED", "type": "string", "format": "date-time", "description": "Time of the submission", "readOnly": true}, "whenModified": {"mappedTo": "WHENMODIFIED", "type": "string", "format": "date-time", "description": "Time of the modification", "readOnly": true}, "createdBy": {"mappedTo": "CREATEDBY", "type": "string", "description": "User who created this", "readOnly": true}, "modifiedBy": {"mappedTo": "MODIFIEDBY", "type": "string", "description": "User who modified this", "readOnly": true}}}}, "refs": {"item": {"mappedTo": "item", "apiObject": "inv/__testitem", "fields": {"key": {"mappedTo": "ITEMKEY", "type": "string"}, "id": {"mappedTo": "ITEMID", "type": "string"}}}, "documentHeader": {"mappedTo": "sodocument", "apiObject": "so/__testsodocument", "fields": {"key": {"mappedTo": "DOCHDRNO", "type": "string"}, "id": {"mappedTo": "DOCHDRID", "type": "string"}}, "isOwner": true}}, "httpMethods": "GET,POST,PATCH,DELETE,OPTIONS"}