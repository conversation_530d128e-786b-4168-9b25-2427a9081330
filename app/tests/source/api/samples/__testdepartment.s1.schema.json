{"mappedTo": "department", "fields": {"id": {"mappedTo": "DEPARTMENTID", "mutable": false, "nullable": false, "type": "string"}, "key": {"mappedTo": "RECORDNO", "readOnly": true, "type": "string"}, "name": {"mappedTo": "TITLE", "nullable": false, "type": "string"}, "reportTitle": {"mappedTo": "CUSTTITLE", "nullable": true, "type": "string"}, "status": {"enum": ["active", "activeNonPosting", "inactive"], "mappedTo": "STATUS", "mappedToValues": ["active", "active non-posting", "inactive"], "type": "string"}}, "httpMethods": "GET,POST,PATCH,DELETE,OPTIONS"}