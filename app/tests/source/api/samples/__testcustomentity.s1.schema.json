{"request": {"mappedTo": "__custom__"}, "response": {"mappedTo": "__custom__", "groups": {"db": {"mappedTo": "db", "groups": {"global": {"fields": {"host": {"type": "string"}, "user": {"type": "string"}, "pwd": {"type": "string"}}}}}, "mongoDB": {"mappedTo": "mongoDB", "fields": {"acceptHosts": {"type": "object"}}, "groups": {"shard": {"fields": {"defaultHost": {"type": "string"}, "globalHost": {"type": "string"}, "baseDB": {"type": "string"}}}, "opt": {"fields": {"connectTimeoutMS": {"type": "integer"}, "socketTimeoutMS": {"type": "integer"}, "username": {"type": "string"}, "password": {"type": "string"}, "db": {"type": "string"}}}}, "lists": {"hosts": {"fields": {"id": {"type": "string"}, "host": {"type": "array", "items": {"type": "string"}}, "acceptNew": {"type": "boolean"}, "connString": {"type": "string"}, "mongoDBClient": {"type": "string"}, "baseDB": {"type": "string"}}, "groups": {"options": {"fields": {"connectTimeoutMS": {"type": "integer"}, "socketTimeoutMS": {"type": "integer"}, "username": {"type": "string"}, "password": {"type": "string"}, "db": {"type": "string"}, "replicaSet": {"type": "string"}}}}}}}}}, "httpMethods": "OPTIONS,POST"}