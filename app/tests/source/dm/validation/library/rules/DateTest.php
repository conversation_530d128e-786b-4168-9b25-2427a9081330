<?php

namespace tests\source\validation\library\rules;

use PHPUnit\Framework\TestCase;
use DateRule;

class DateTest extends TestCase
{

    public function testValids()
    {
        $this->assertTrue((new DateRule())->check("10/25/2020")); // default format

        $args = ['/mdY'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("12/9/2021"));
        $args = ['/mdy'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("12/09/21"));
        $args = ['.mdy'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("12.09.21"));
        $args = ['.mdY'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("12.09.2021"));
        $args = ['-mdy'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("12-09-21"));
        $args = ['-mdY'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("12-09-2021"));
        $args = [' mdy'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("120950"));
        $args = [' mdy'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("12 09 50"));
        $args = [' mdY'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("12 09 2050"));

        $args = ['/dmy'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("09/12/50"));
        $args = ['/dmY'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("09/12/2050"));
        $args = ['.dmy'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("09.12.50"));
        $args = ['.dmY'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("09.12.2050"));
        $args = ['-dmy'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("09-12-50"));
        $args = ['-dmY'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("09-12-2050"));
        $args = [' dmy'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("091250"));
        $args = [' dmy'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("09 12 50"));
        $args = [' dmY'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("09 12 2050"));

        $args = ['/ymd'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("50/12/25"));
        $args = ['/Ymd'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("2050/12/25"));
        $args = ['.ymd'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("50.12.25"));
        $args = ['.Ymd'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("2050.12.25"));
        $args = ['-ymd'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("50-12-25"));
        $args = ['-Ymd'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("2050-12-25"));
        $args = [' ymd'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("501225"));
        $args = [' ymd'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("50 12 25"));
        $args = [' Ymd'];
        $this->assertTrue((new DateRule())->fillParameters($args)->check("2050 12 25"));
    }

    public function testInvalids()
    {
        // not a valid date
        $args = ['/mdY'];
        $this->assertFalse((new DateRule())->fillParameters($args)->check("30/02/2021"));

        // default format is different with -mdY
        $this->assertFalse((new DateRule())->check("10-25-20"));
        $this->assertFalse((new DateRule())->check("10/25/2020 10:10"));

        // this format is not accepted
        $args = ['format' => 'Y-m-d'];
        $this->assertFalse((new DateRule())->fillParameters($args)->check("2010-10-10"));

        // selected format is not respected
        $args = ['format' => '-mdY'];
        $this->assertFalse((new DateRule())->fillParameters($args)->check("10-25-20"));

        // before 1990
        $args = ['format' =>'/mdY', 'limitdaterange' => true];
        $this->assertFalse((new DateRule())->fillParameters($args)->check("12/9/1989"));
    }
}
