<?php

namespace tests\source\validation\library\rules\custom\vendor;

use PHPUnit\Framework\TestCase;
use unitTest\core\UnitTestTool;
use Validator;
use ValidatorResourcesBase;
use CoordinatesRule;

/**
 * @covers Vendors
 * @group  unit
 */
class CoordinatesRuleTest extends UnitTestTool
{

    public $rulesReader;
    public $validator;

    public function setUp() : void
    {
        $this->validator = new Validator('OBJECT_TYPE', 'OBJECT_ALIAS');

        $this->stub = $this->createMyStub();
        $args = [ 'latitude', 'longitude' ];
        $this->rule = (new CoordinatesRule($this->stub))->fillParameters($args);

        $this->rules = [
            'latitude'  => [ $this->rule ],
            'longitude' => [],
        ];
    }

    public function createMyStub()
    {
        $stub = $this->createStub(ValidatorResourcesBase::class);

        $stub->method('isModuleIdInstalled')
             ->willReturn(true);

        $stub->method('getModulePreferences')
             ->willReturn([ 'AVA_ENABLE_COORDINATES' => 'T' ]);

        return $stub;
    }

    public function testPasses()
    {
        $rowarray = [ 'latitude' => '', 'longitude' => '' ];

        $this->validation = $this->validator->make($rowarray, $this->rules);
        $this->validation->validate();

        $this->assertFalse($this->validation->fails());

        $rowarray = [ 'latitude' => '10', 'longitude' => '20' ];

        $this->validation = $this->validator->make($rowarray, $this->rules);
        $this->validation->validate();

        $this->assertFalse($this->validation->fails());
    }

    public function testFails()
    {
        $rowarray = [ 'latitude' => '10', 'longitude' => '' ];

        $this->validation = $this->validator->make($rowarray, $this->rules);
        $this->validation->validate();

        $this->assertTrue($this->validation->fails());

        $rowarray = [ 'latitude' => '', 'longitude' => '20' ];

        $this->validation = $this->validator->make($rowarray, $this->rules);
        $this->validation->validate();

        $this->assertTrue($this->validation->fails());
    }

    public function testKavaModuleNotInstalled()
    {
        $stub = $this->createStub(ValidatorResourcesBase::class);

        $stub->method('isModuleIdInstalled')
             ->willReturn(false);

        $rowarray = [ 'latitude' => '10', 'longitude' => '20' ];

        $args = [ 'latitude', 'longitude' ];
        $rule = (new CoordinatesRule($stub))->fillParameters($args);

        $rules = [
            'latitude'  => [ $rule ],
            'longitude' => [],
        ];

        $this->validation = $this->validator->make($rowarray, $rules);
        $this->validation->validate();

        $this->assertTrue($this->validation->fails());
    }

    public function test_NOT_AVA_ENABLE_COORDINATES()
    {
        $stub = $this->createStub(ValidatorResourcesBase::class);

        $stub->method('getModulePreferences')
             ->willReturn([ 'AVA_ENABLE_COORDINATES' => 'F' ]);

        $rowarray = [ 'latitude' => '10', 'longitude' => '20' ];

        $args = [ 'latitude', 'longitude' ];
        $rule = (new CoordinatesRule($stub))->fillParameters($args);

        $rules = [
            'latitude'  => [ $rule ],
            'longitude' => [],
        ];

        $this->validation = $this->validator->make($rowarray, $rules);
        $this->validation->validate();

        $this->assertTrue($this->validation->fails());
    }
}
