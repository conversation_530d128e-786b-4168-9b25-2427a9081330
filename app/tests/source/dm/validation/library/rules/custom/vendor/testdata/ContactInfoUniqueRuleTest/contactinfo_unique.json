{"stub": {"getEntityObject": {"vendor_1": {"VENDOR_ID": "vendor_1", "NAME": "vendor 1", "DISPLAYCONTACT.CONTACT_NAME": "contact_1"}, "vendor_2": {"VENDOR_ID": "vendor_2", "NAME": "vendor 2", "DISPLAYCONTACT.CONTACT_NAME": "contact_2"}, "contact_1": {"CONTACT_NAME": "contact_1", "PRINT_AS": "Contact 1", "MAILADDRESS.ADDRESS1": "Contact 1 address"}, "contact_2": {"CONTACT_NAME": "contact_2", "PRINT_AS": "Contact 2", "MAILADDRESS.ADDRESS1": "Contact 2 address"}, "contact_3": {}}}, "testPasses": {"testcases": [{"ID": 10, "_comment": "the contact exists, is owned by vendor", "request": {"VENDOR_ID": "vendor_1", "CONTACTINFO.CONTACT_NAME": "contact_1", "CONTACTINFO.PRINT_AS": "Other name", "CONTACTINFO.MAILADDRESS.ADDRESS1": "100 Big Way"}}, {"ID": 12, "_comment": "the contact is new", "request": {"VENDOR_ID": "vendor_1", "CONTACTINFO.CONTACT_NAME": "contact_3", "CONTACTINFO.PRINT_AS": "Contact 3", "CONTACTINFO.MAILADDRESS.ADDRESS1": "an address"}}]}, "testFails": {"testcases": [{"ID": 11, "_comment": "the contact exists, but it is owned by other vendor", "request": {"VENDOR_ID": "vendor_1", "CONTACTINFO.CONTACT_NAME": "contact_2", "CONTACTINFO.PRINT_AS": "Contact 2", "CONTACTINFO.MAILADDRESS.ADDRESS1": "Contact 2 address"}}]}}