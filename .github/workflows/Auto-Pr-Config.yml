name: Auto PR Creation
on:
   pull_request:
    types: [closed]
    branches:
      - 'main'
      - 'Feb[2-9][0-9]'
      - 'May[2-9][0-9]'
      - 'Aug[2-9][0-9]'
      - 'Nov[2-9][0-9]'
      - 'Aug[2-9][0-9]-[0-9][0-9]'
      - 'Aug[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'Feb[2-9][0-9]-[0-9][0-9]'
      - 'Feb[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'May[2-9][0-9]-[0-9][0-9]'
      - 'May[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'Nov[2-9][0-9]-[0-9][0-9]'
      - 'Nov[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
  
    
jobs:
 Conf_Changes: 
    if: github.event.pull_request.merged == true
    runs-on: self-hosted  
    outputs:
      conf: ${{ steps.filter.outputs.conf }}
      live: ${{ steps.live.outputs.live }}

    steps:              
      - name: Check out ghaction-paths-filter
        uses: actions/checkout@v4
        with:
          repository: intacct/ghaction-paths-filter
          ref: main
          token: ${{ secrets.GH_PAT_IADODEPLOY_ACTION }}
          path: ./actions/ghaction-paths-filter

     
      - uses: dorny/paths-filter@v3.0.2
        id: filter
        with:
          filters: |
            conf:
             - "app/source/common/ia_init.cfg"
       
      - uses: dorny/paths-filter@v3.0.2
        id: live
        with:
          filters: |
            live:
             - "app/source/common/ia_init.live.cfg"    
              
 Create_ia_init:
   needs: Conf_Changes
   if: ${{ needs.Conf_Changes.outputs.conf == 'true' }}
   runs-on: self-hosted  
   steps:  
      - name: Setup GitHub CLI
        uses: sersoft-gmbh/setup-gh-cli-action@v2.0.1  
        
      - name: Cherrypik and Auto-PR Creation
        id: branch_create
        env:
             GH_TOKEN: ${{ secrets.GITHUBS_TOKEN }}
        run: |    
               ##################################################
                # Collect Commits
                echo "${{ github.base_ref}}"
                 # Retrieve the pull request number from the pull request merged event
                  PR_NUMBER=$(cat $GITHUB_EVENT_PATH | jq -r '.pull_request.number')
                  
                  gh api repos/intacct/ia-app/pulls/$PR_NUMBER/commits --paginate \
                  | jq -r '.[]|select(.parents | length == 1) | .sha'> commits.out
                  echo "PR Commits -----> "
                  echo >commits_list.out
                  for i in $(cat commits.out)
                  do
                     echo "checking --> $i"
                     change=$(gh api repos/intacct/ia-app/commits/$i| jq '.files[] | select(.filename == "app/source/common/ia_init.cfg" )  | length > 0')
                     if [ "$change" ]; then
                     echo $i >>commits_list.out
                     fi
                     
                   done
                   echo "Commits with ia_init.cfg Changes--->"
                   cat commits_list.out
                   
            
            
                echo "#########################################"   
                
                  [ -d "ia-config" ] && rm -rf ia-config
                  [ -d "ia-app" ] && rm -rf ia-app
                  
                  git clone https://jenkins-sageintacct:${{ secrets.GITHUBS_TOKEN }}@github.com/intacct/ia-config.git --branch $GITHUB_BASE_REF ia-config
                  git clone https://jenkins-sageintacct:${{ secrets.GITHUBS_TOKEN }}@github.com/intacct/ia-app.git --branch $GITHUB_BASE_REF ia-app
                  cd ia-config
                  git config  user.email "<EMAIL>"
                  git config  user.name "jenkins-sageintacct"
                  git checkout -b ${{ github.head_ref }}
                  cd ../ia-app
                  cp ../ia-config/ia_init.cfg app/source/common/              
                  git add app/source/common/ia_init.cfg
                  
                  git config  user.email "<EMAIL>"
                  git config  user.name "jenkins-sageintacct"
                  
                  git commit -m "copy original ia_init.cfg file to ia-app"
                  for i in $(cat ../commits_list.out);
                  do
                    echo "Cherry picking commit ----->$i"              
                    author=$(git show $i | grep Author |awk -F 'Author: ' '{print $2}')
                    message=$(git log --format=%B -n 1 $i)
                    
                    git config  user.email "<EMAIL>"
                    git config  user.name "jenkins-sageintacct"
                    
                    if  git cherry-pick --no-merges $i -n -n --strategy-option=theirs; then
                     echo "######################################################"
                     echo "Cherrypick is Complete for ---> $i "
                     echo "######################################################"
                   else
                     echo "######################################################"
                     echo "Found Merge Commit Skipping..."
                     echo "######################################################"
                   fi
                
                   if git status | grep -q "nothing to commit, working tree clean"; then
                       echo "######################################################"
                       echo "Commit $i has already been applied to  ${{github.head_ref}}"
                       echo "######################################################"
                   else
                
                       git commit -m "$message" --author="$author"

                    fi                
                  done 
                    echo "Copy final config file -------------> "
                    cp app/source/common/ia_init.cfg ../ia-config
                    cd ../ia-config
                    
                    git config  user.email "<EMAIL>"
                    git config  user.name "jenkins-sageintacct"
                    
                    if git status | grep -q "nothing to commit, working tree clean"; then
                       echo "######################################################"
                       echo "Changes are already been applied to  ${{github.head_ref}}"
                       echo "######################################################"
                   else
                                    
                      git add ia_init.cfg
                      git commit -m "Cherrypicked ia_init.cfg changes from ia-app"
                      git push https://jenkins-sageintacct:${{ secrets.GITHUBS_TOKEN }}@github.com/intacct/ia-config.git 
                    fi
                   
                   ###########################################
                    echo "Create a Auto-PR in ia-config"
                   ###########################################
                   #Collect title and body
                   echo "Collect title and body"
                   PR_TITLE=$(jq -r '.pull_request.title' "$GITHUB_EVENT_PATH")
                   echo $PR_TITLE
                   PR_body=$(jq -r '.pull_request.body' "$GITHUB_EVENT_PATH" )
                   PR_BODY=$(echo "$PR_body" |sed 's/Auto-PR:.*//; s/Co-authored-by:.*//')
                   
                   #Create Auto-PR
                   echo "Create Auto-PR"
                   pr_output=$(gh pr create --title "Auto-PR:$PR_TITLE" --body "$PR_BODY" --base ${{github.base_ref}} --head ${{ github.head_ref }} --repo intacct/ia-config)

                   echo "$pr_output"
                   pr_number=$(echo "$pr_output" | awk -F'/' '{ print $(NF) }')
                   echo $pr_number
                   #Comment Original PR number in config PR

                   gh api repos/intacct/ia-config/issues/$pr_number/comments --field "body=Auto PR is Created Sucessfully from https://github.com/intacct/ia-app/pull/$PR_NUMBER"
                   
      - name: Create Jira Issue
        env:
               JIRA_API_TOKEN: ${{ secrets.JIRA_API_TOKEN }}
               JIRA_BASE_URL: https://jira.sage.com
               GH_TOKEN: ${{ secrets.GITHUBS_TOKEN }}
        run: |
                 gh api repos/intacct/ia-app/pulls/${{ github.event.pull_request.number}}/commits --paginate \
                  | jq -r '.[]|select(.parents | length == 1) | .sha'> commits.out
                  echo "PR Commits -----> "
                  for i in $(cat commits.out)
                  do
                     echo "checking --> $i"
                     change=$(gh api repos/intacct/ia-app/commits/$i| jq '.files[] | select(.filename == "app/source/common/ia_init.cfg" )  | length > 0')
                     if [ "$change" ]; then
                     echo $i >>commits_list.out
                     fi
                     
                   done
                   echo "Commits with ia_init.cfg Changes--->"
                   cat commits_list.out
                   commits=$(cat commits_lists.out | awk 'NR>1{printf "%s\\n",prev} {prev=$0} END{printf "%s",prev}')
                   echo "Commits in PR"
                   commits_PR=$(cat commits.out | awk 'NR>1{printf "%s\\n",prev} {prev=$0} END{printf "%s",prev}')
                  
                  #get the owner
                   owner=$(gh api repos/intacct/ia-app/pulls/${{ github.event.pull_request.number}} | jq -r .user.login)
                   echo "owner -->$owner"

                  #get the file_changes
                  gh api repos/intacct/ia-app/pulls/${{ github.event.pull_request.number}}/files | jq -r '.[].filename' >file_changes.out
                  file_changes=$(cat file_changes.out | awk 'NR>1{printf "%s\\n",prev} {prev=$0} END{printf "%s",prev}')
                  echo "$file_changes"

                
                   # Create the description with PR number, files changed, commits
                     description="ia-app Pull-Request ${{github.server_url}}/${{github.repository}}/pull/${{ github.event.pull_request.number }} \n\n"
                     description+="Owner of the pull request: "
                     description+=$(echo "$owner" )
                     description+="\n\n"
                     description+="The following files were changed in the above pull request:\n\n"
                     description+=$(echo "$file_changes" | sed 's/^/- /')
                     description+="\n\n"
                     description+="config file Commits:\n\n"
                     description+=$(echo "$commits" | sed 's/^/- /')
                     description+="\n\n"
                     description+="Commits in PR:\n\n"
                     description+=$(echo "$commits_PR" | sed 's/^/- /')
              

                   curl -v -u -X POST  \
                     -H "Authorization: Bearer ${{ secrets.JIRA_API_TOKEN  }}" \
                     -H "Content-Type: application/json" \
                     -d '{
                       "fields": {
                         "project": {
                           "key": "RM"
                         },
                         "summary": "ia_init Config file Change in ia-app PR-${{ github.event.pull_request.number }}",
                        "issuetype": {
                           "name": "Task"
                         },
                         "assignee": {
                          "name": "intacctRelease"
                         }, 
                         
                          "description": "'"$description"'"

                       }
                     }' \
                     https://jira.sage.com/rest/api/2/issue

                    





 Create_ia_init_live:
   needs: Conf_Changes
   if: ${{ needs.Conf_Changes.outputs.live == 'true' }}
   runs-on: self-hosted  
   steps:  
      - name: Setup GitHub CLI
        uses: sersoft-gmbh/setup-gh-cli-action@v2.0.1   
        
      - name: Cherrypik and Auto-PR Creation
        id: branch_create
        env:
             GH_TOKEN: ${{ secrets.GITHUBS_TOKEN }}
        run: |    
               ##################################################
                # Collect Commits
                echo "${{ github.base_ref}}"
                 # Retrieve the pull request number from the pull request merged event
                  PR_NUMBER=$(cat $GITHUB_EVENT_PATH | jq -r '.pull_request.number')
                  
                  gh api repos/intacct/ia-app/pulls/$PR_NUMBER/commits --paginate \
                  | jq -r '.[]|select(.parents | length == 1) | .sha'> commits.out
                  echo "PR Commits -----> "
                  echo >commits_list.out
                  for i in $(cat commits.out)
                  do
                     echo "checking --> $i"
                     change=$(gh api repos/intacct/ia-app/commits/$i| jq '.files[] | select(.filename =="app/source/common/ia_init.live.cfg")  | length > 0')
                     if [ "$change" ]; then
                     echo $i >>commits_list.out
                     fi
                     
                   done
                   echo "Commits with ia_init.live.cfg Changes--->"
                   cat commits_list.out
            
            
                echo "#########################################"   
                
                  [ -d "ia-config" ] && rm -rf ia-config
                  [ -d "ia-app" ] && rm -rf ia-app
                  
                  git clone https://jenkins-sageintacct:${{ secrets.GITHUBS_TOKEN }}@github.com/intacct/ia-config.git --branch $GITHUB_BASE_REF ia-config
                  git clone https://jenkins-sageintacct:${{ secrets.GITHUBS_TOKEN }}@github.com/intacct/ia-app.git --branch $GITHUB_BASE_REF ia-app
                  cd ia-config
                  git config  user.email "<EMAIL>"
                  git config  user.name "jenkins-sageintacct"
                  git checkout -b ${{ github.head_ref }}
                  cd ../ia-app
                  cp ../ia-config/ia_init.live.cfg app/source/common/              
                  git add app/source/common/ia_init.live.cfg
                  
                  git config  user.email "<EMAIL>"
                  git config  user.name "jenkins-sageintacct"
                  
                  git commit -m "copy original ia_init.live.cfg file to ia-app"
                  for i in $(cat ../commits_list.out);
                  do
                    echo "Cherry picking commit ----->$i"              
                    author=$(git show $i | grep Author |awk -F 'Author: ' '{print $2}')
                    message=$(git log --format=%B -n 1 $i)
                    
                    git config  user.email "<EMAIL>"
                    git config  user.name "jenkins-sageintacct"
                    
                    if  git cherry-pick --no-merges $i -n -n --strategy-option=theirs; then
                     echo "######################################################"
                     echo "Cherrypick is Complete for ---> $i "
                     echo "######################################################"
                   else
                     echo "######################################################"
                     echo "Found Merge Commit Skipping..."
                     echo "######################################################"
                   fi
                
                   if git status | grep -q "nothing to commit, working tree clean"; then
                       echo "######################################################"
                       echo "Commit $i has already been applied to  ${{github.head_ref}}"
                       echo "######################################################"
                   else
                
                       git commit -m "$message" --author="$author"

                    fi                
                  done 
                    echo "Copy final ia_init.live.cfg file -------------> "
                    cp app/source/common/ia_init.live.cfg ../ia-config
                    cd ../ia-config
                    
                    git config  user.email "<EMAIL>"
                    git config  user.name "jenkins-sageintacct"
                    
                    if git status | grep -q "nothing to commit, working tree clean"; then
                       echo "######################################################"
                       echo "Changes are already been applied to  ${{github.head_ref}}"
                       echo "######################################################"
                   else
                                    
                      git add ia_init.live.cfg
                      git commit -m "Cherrypicked Config changes from ia-app"
                      git push https://jenkins-sageintacct:${{ secrets.GITHUBS_TOKEN }}@github.com/intacct/ia-config.git 
                    fi
                   
                   ###########################################
                    echo "Create a Auto-PR in ia-config"
                   ###########################################
                   #Collect title and body
                   echo "Collect title and body"
                   PR_TITLE=$(jq -r '.pull_request.title' "$GITHUB_EVENT_PATH")
                   echo $PR_TITLE
                   PR_body=$(jq -r '.pull_request.body' "$GITHUB_EVENT_PATH" )
                   PR_BODY=$(echo "$PR_body" |sed 's/Auto-PR:.*//; s/Co-authored-by:.*//')
                   
                   #Create Auto-PR
                   echo "Create Auto-PR"
                   pr_output=$(gh pr create --title "Auto-PR:$PR_TITLE" --body "$PR_BODY" --base ${{github.base_ref}} --head ${{ github.head_ref }} --repo intacct/ia-config)

                   echo "$pr_output"
                   pr_number=$(echo "$pr_output" | awk -F'/' '{ print $(NF) }')
                   echo $pr_number
                   #Comment Original PR number in config PR

                   gh api repos/intacct/ia-config/issues/$pr_number/comments --field "body=Auto PR is Created Sucessfully from https://github.com/intacct/ia-app/pull/$PR_NUMBER"
                    
      - name: Create Jira Issue
        env:
                     JIRA_API_TOKEN: ${{ secrets.JIRA_API_TOKEN }}
                     JIRA_BASE_URL: https://jira.sage.com
                     GH_TOKEN: ${{ secrets.GITHUBS_TOKEN }}
        run: |
                       gh api repos/intacct/ia-app/pulls/${{ github.event.pull_request.number}}/commits --paginate \
                        | jq -r '.[]|select(.parents | length == 1) | .sha'> commits.out
                        echo "PR Commits -----> "
                        for i in $(cat commits.out)
                        do
                           echo "checking --> $i"
                           change=$(gh api repos/intacct/ia-app/commits/$i| jq '.files[] | select(.filename == "app/source/common/ia_init.live.cfg" )  | length > 0')
                           if [ "$change" ]; then
                           echo $i >>commits_list.out
                           fi

                         done
                         echo "Commits with ia_init.live.cfg Changes--->"
                         cat commits_list.out
                         commits=$(cat commits_lists.out | awk 'NR>1{printf "%s\\n",prev} {prev=$0} END{printf "%s",prev}')
                         echo "Commits in PR"
                         commits_PR=$(cat commits.out | awk 'NR>1{printf "%s\\n",prev} {prev=$0} END{printf "%s",prev}')

                        #get the owner
                         owner=$(gh api repos/intacct/ia-app/pulls/${{ github.event.pull_request.number}} | jq -r .user.login)
                         echo "owner -->$owner"

                        #get the file_changes
                        gh api repos/intacct/ia-app/pulls/${{ github.event.pull_request.number}}/files | jq -r '.[].filename' >file_changes.out
                        file_changes=$(cat file_changes.out | awk 'NR>1{printf "%s\\n",prev} {prev=$0} END{printf "%s",prev}')
                        echo "$file_changes"

                         # Create the description with PR number, files changed, commits
                           description="ia-app Pull-Request ${{github.server_url}}/${{github.repository}}/pull/${{ github.event.pull_request.number }} \n\n"
                           description+="Owner of the pull request: "
                           description+=$(echo "$owner" )
                           description+="\n\n"
                           description+="The following files were changed in the above pull request:\n\n"
                           description+=$(echo "$file_changes" | sed 's/^/- /')
                           description+="\n\n"
                           description+="config file Commits:\n\n"
                           description+=$(echo "$commits" | sed 's/^/- /')
                           description+="\n\n"
                           description+="Commits in PR:\n\n"
                           description+=$(echo "$commits_PR" | sed 's/^/- /')
              

                         curl -v -u -X POST  \
                           -H "Authorization: Bearer ${{ secrets.JIRA_API_TOKEN  }}" \
                           -H "Content-Type: application/json" \
                           -d '{
                             "fields": {
                               "project": {
                                 "key": "RM"
                               },
                               "summary": "ia_init.live.cfg Config file Change in ia-app PR-${{ github.event.pull_request.number }}",
                              "issuetype": {
                                 "name": "Task"
                               },
                               "assignee": {
                                "name": "intacctRelease"
                               }, 

                                "description": "'"$description"'"

                             }
                           }' \
                           https://jira.sage.com/rest/api/2/issue                    
                    
                               
 Alert_ia_init:
  needs: Create_ia_init
  runs-on: self-hosted 
  steps:  
        # Slack Notification
            - name: Checkout intacct/ghaction-slack-post
              uses: actions/checkout@v4
              with:
                repository: intacct/ghaction-slack-post
                ref: main
                token: ${{ secrets.GH_PAT_IADODEPLOY_ACTION  }}
                path: ./actions/ghaction-slack-post

            - name: Post Slack message
              uses: ./actions/ghaction-slack-post
              with:
                args: '{\"channel\":\"C0509TH4221\",\"text\":\" "Auto-PR Alert: ia_init.cfg file Changed \n A PR has been raised and merged with ia_init.cfg file changes\n \n "Pull-Request::${{ github.server_url }}/intacct/ia-config/pulls" \n\n \"}'
              env:
                SLACK_BOT_TOKEN: ${{ secrets.IA_APP_SLACK_BOT_TOKEN }}
 
 Alert_ia_init_live:
  needs: Create_ia_init_live
  runs-on: self-hosted 
  steps:  
        # Slack Notification
            - name: Checkout intacct/ghaction-slack-post
              uses: actions/checkout@v4
              with:
                repository: intacct/ghaction-slack-post
                ref: main
                token: ${{ secrets.GH_PAT_IADODEPLOY_ACTION  }}
                path: ./actions/ghaction-slack-post

            - name: Post Slack message
              uses: ./actions/ghaction-slack-post
              with:
                args: '{\"channel\":\"C0509TH4221\",\"text\":\" "Auto-PR Alert: ia_init.live.cfg file Changed \n A PR has been raised and merged with ia_init.live.cfg file changes\n \n "Pull-Request::${{ github.server_url }}/intacct/ia-config/pulls" \n\n \"}'
              env:
                SLACK_BOT_TOKEN: ${{ secrets.IA_APP_SLACK_BOT_TOKEN }}

          
